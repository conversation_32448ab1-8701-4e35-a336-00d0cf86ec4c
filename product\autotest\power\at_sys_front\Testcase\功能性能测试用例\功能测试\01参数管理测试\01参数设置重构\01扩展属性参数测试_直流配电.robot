*** Settings ***
Suite Setup        run keywords     备份参数文件
...                AND     导入直流配电条件下参数文件
Suite Teardown      恢复备份参数文件
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
直流配电单元下电时间设置测试
    [Tags]    PMSA-NTest    Restr
    [Setup]    run keywords    判断web参数是否存在    下电模式
    ...    AND    设置web参数量    下电模式    停电时间
    ...    AND    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    直流配电单元下电使能
    ...    AND    设置web序列参数    直流配电单元下电使能    允许
    ...    AND    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    直流配电单元下电时间
    ${下电时间数量}    获取web参数的数量    直流配电单元下电时间
    ${缺省值}    获取web参数上下限范围    直流配电单元下电时间
    ${可设置范围}    获取web参数可设置范围    直流配电单元下电时间_1
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    FOR    ${i}    IN RANGE    ${下电时间数量}    0    -1
        ${原参数}    Wait Until Keyword Succeeds    30    2    获取web参数量    直流配电单元下电时间_${i}
    #(1)超范围设置不成功
    #超下限
        ${设置结果}    run keyword and return status    设置web参数量    直流配电单元下电时间_${i}    ${超下限}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    直流配电单元下电时间_${i}
        should be true    ${参数获取}==${原参数}
    #超上限
        ${设置结果}    run keyword and return status    设置web参数量    直流配电单元下电时间_${i}    ${超上限}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    直流配电单元下电时间_${i}
        should be true    ${参数获取}==${原参数}
    #范围内设置成功
        Wait Until Keyword Succeeds    10    2    设置web参数量    直流配电单元下电时间_${i}    ${可设置范围}[0]
        sleep    1
        ${参数获取}    获取web参数量    直流配电单元下电时间_${i}
        should be true    ${参数获取}==${可设置范围}[0]
        Wait Until Keyword Succeeds    10    2    设置web参数量    直流配电单元下电时间_${i}    ${可设置范围}[1]
        sleep    1
        ${参数获取}    获取web参数量    直流配电单元下电时间_${i}
        should be true    ${参数获取}==${可设置范围}[1]
        Wait Until Keyword Succeeds    10    2    设置web参数量    直流配电单元下电时间_${i}    ${缺省值}[0]
        sleep    1
        ${参数获取}    获取web参数量    直流配电单元下电时间_${i}
        should be true    ${参数获取}==${缺省值}[0]
    END
    恢复默认值并重新登录
    sleep    2m
    系统复位
    sleep    1m
    [Teardown]

直流配电单元下电SOC阈值设置测试
    [Tags]    PMSA-NTest    Restr
    [Setup]    Run keywords    判断web参数是否存在    下电模式
    ...    AND    设置web参数量    下电模式    电池剩余容量
    ...    AND    判断web参数是否存在    直流配电单元下电使能
    ...    AND    设置web序列参数    直流配电单元下电使能    允许
    ...    AND    判断web参数是否存在    直流配电单元下电SOC阈值
    ${下电SOC数量}    获取web参数的数量    直流配电单元下电SOC阈值
    ${缺省值}    获取web参数上下限范围    直流配电单元下电SOC阈值
    ${可设置范围}    获取web参数可设置范围    直流配电单元下电SOC阈值_1
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    FOR    ${i}    IN RANGE    ${下电SOC数量}    0    -1
        ${原参数}    Wait Until Keyword Succeeds    30    2    获取web参数量    直流配电单元下电SOC阈值_${i}
    #(1)超范围设置不成功
    #超下限
        ${设置结果}    run keyword and return status    设置web参数量    直流配电单元下电SOC阈值_${i}    ${超下限}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    直流配电单元下电SOC阈值_${i}
        should be true    ${参数获取}==${原参数}
    #超上限
        ${设置结果}    run keyword and return status    设置web参数量    直流配电单元下电SOC阈值_${i}    ${超上限}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    直流配电单元下电SOC阈值_${i}
        should be true    ${参数获取}==${原参数}
    #范围内设置成功
        Wait Until Keyword Succeeds    10    2    设置web参数量    直流配电单元下电SOC阈值_${i}    ${可设置范围}[0]
        sleep    1
        ${参数获取}    获取web参数量    直流配电单元下电SOC阈值_${i}
        should be true    ${参数获取}==${可设置范围}[0]
        Wait Until Keyword Succeeds    10    2    设置web参数量    直流配电单元下电SOC阈值_${i}    ${可设置范围}[1]
        sleep    1
        ${参数获取}    获取web参数量    直流配电单元下电SOC阈值_${i}
        should be true    ${参数获取}==${可设置范围}[1]
        Wait Until Keyword Succeeds    10    2    设置web参数量    直流配电单元下电SOC阈值_${i}    ${缺省值}[0]
        sleep    1
        ${参数获取}    获取web参数量    直流配电单元下电SOC阈值_${i}
        should be true    ${参数获取}==${缺省值}[0]
    END
    恢复默认值并重新登录
    sleep    2m
    系统复位
    sleep    1m
    [Teardown]