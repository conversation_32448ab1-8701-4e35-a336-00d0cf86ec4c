*** Settings ***
Suite Setup       测试用例前置条件
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
手动模式启动油机测试
    仅电池模拟器供电
    连接CSU
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流输入场景    纯油机
    设置子工具值    oileng    all    只读    灯亮为    4
    sleep    20s
    ${获取值1}    获取web实时数据    油机控制器工作模式
    should be true    '${获取值1}'=='手动'
    Wait Until Keyword Succeeds    5m    1    设置失败的web控制量    油机开启
    [Teardown]    设置子工具值    oileng    all    只读    灯亮为    8

手动模式关闭油机测试
    设置电池模拟器模式    铅酸电池    24    5    50
    电池模拟器接触器分合    ON
    打开电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    连接CSU
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流输入场景    纯油机
    设置子工具值    oileng    all    只读    灯亮为    4
    sleep    20s
    ${获取值1}    获取web实时数据    油机控制器工作模式
    should be true    '${获取值1}'=='手动'
    Wait Until Keyword Succeeds    5m    1    设置失败的web控制量    油机关闭
    [Teardown]    设置子工具值    oileng    all    只读    灯亮为    8

自动模式手动启停油机测试
    [Setup]
    仅电池模拟器供电
    连接CSU
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流输入场景    纯油机
    ${获取值1}    获取web实时数据    油机控制器工作模式
    run keyword if    '${获取值1}' != '自动'    Wait Until Keyword Succeeds    5m    1    设置web控制量    油机进入自动模式
    设置子工具值    oileng    all    只读    灯亮为    8
    sleep    20s
    ${获取值2}    获取web实时数据    油机控制器工作模式
    should be true    '${获取值2}'=='自动'
    Wait Until Keyword Succeeds    5m    1    设置web控制量    油机开启
    同时设置三相电压频率    220    50
    打开交流源输出
    设置子工具值    oileng    all    只读    发电 A 相电压    299
    设置子工具值    oileng    all    只读    发电 B 相电压    300
    设置子工具值    oileng    all    只读    发电 C 相电压    298
    设置子工具值    oileng    all    只读    转速    1500
    sleep    30s
    ${获取值1}    获取web实时数据    油机相电压_1
    ${获取值2}    获取web实时数据    油机相电压_2
    ${获取值3}    获取web实时数据    油机相电压_3
    ${获取值4}    获取web实时数据    油机转速
    should be true    ${获取值1}==298
    should be true    ${获取值2}==300
    should be true    ${获取值3}==299
    should be true    ${获取值4}==1500
    ${干接点实际是否动作状态1}    获取干接点状态    ${油机干接点状态}
    Comment    should be true    ${干接点实际是否动作状态1}==1    #0为恢复，没动作
    Wait Until Keyword Succeeds    3m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    3m    1    信号量数据值为    油机状态    运行
    Wait Until Keyword Succeeds    5m    1    设置web控制量    油机关闭
    关闭交流源输出
    设置子工具值    oileng    all    只读    发电 A 相电压    0
    设置子工具值    oileng    all    只读    发电 B 相电压    0
    设置子工具值    oileng    all    只读    发电 C 相电压    0
    设置子工具值    oileng    all    只读    转速    0
    sleep    30s
    ${获取值1}    获取web实时数据    油机相电压_1
    ${获取值2}    获取web实时数据    油机相电压_2
    ${获取值3}    获取web实时数据    油机相电压_3
    ${获取值4}    获取web实时数据    油机转速
    should be true    ${获取值1}==0
    should be true    ${获取值2}==0
    should be true    ${获取值3}==0
    should be true    ${获取值4}==0
    Wait Until Keyword Succeeds    3m    1    信号量数据值为    油机状态    停止
    [Teardown]    Run keywords    同时设置三相电压频率    220    50
    ...    AND    打开交流源输出

WEB设置油机控制屏工作模式测试
    [Documentation]    通过WEB设置油机控制屏模式后获取油机控制屏模式正确即可。
    连接CSU
    Wait Until Keyword Succeeds    5m    1    设置web控制量    油机进入手动模式
    设置子工具值    oileng    all    只读    灯亮为    4
    sleep    20s
    ${获取值1}    获取web实时数据    油机控制器工作模式
    should be true    '${获取值1}'=='手动'
    Wait Until Keyword Succeeds    5m    1    设置web控制量    油机进入自动模式
    设置子工具值    oileng    all    只读    灯亮为    8
    sleep    20
    ${获取值1}    获取web实时数据    油机控制器工作模式
    should be true    '${获取值1}'=='自动'

油机控制屏复位测试
    连接CSU
    Wait Until Keyword Succeeds    5m    1    设置web控制量    油机控制屏复位
    sleep    20
    ${子工具参数获取}    获取子工具值    oileng    1    读写    操作
    @{子工具参数获取}    split string    @{子工具参数获取}    ,
    should be true    '@{子工具参数获取}'=='复位'
