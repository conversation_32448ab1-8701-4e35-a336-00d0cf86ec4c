*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
禁止下电模式电池下电测试(参数设置)
    设置web参数量    下电模式    禁止
    ${直流配电单元数量}    获取指定量的调测信息    res_int2
    FOR    ${val}    IN RANGE    1    ${直流配电单元数量}+1
        ${配电单元下电使能}    Catenate    SEPARATOR=    直流配电单元下电使能_    ${val}
        ${下电电压}    Catenate    SEPARATOR=    直流配电单元下电电压_    ${val}
        ${下电时间}    Catenate    SEPARATOR=    直流配电单元下电时间_    ${val}
        ${下电SOC}    Catenate    SEPARATOR=    直流配电单元下电SOC阈值_    ${val}
        ${直流配电单元下电使能}    获取web参数量    ${配电单元下电使能}
        ${获取值}    Convert To Boolean    ${直流配电单元下电使能}
        should not be true    ${获取值}
        ${直流配电单元下电电压}    获取web参数量    ${下电电压}
        ${获取值}    Convert To Boolean    ${直流配电单元下电电压}
        should not be true    ${获取值}
        ${直流配电单元下电时间}    获取web参数量    ${下电时间}
        ${获取值}    Convert To Boolean    ${直流配电单元下电时间}
        should not be true    ${获取值}
        ${直流配电单元下电SOC}    获取web参数量    ${下电SOC}
        ${获取值}    Convert To Boolean    ${直流配电单元下电SOC}
        should not be true    ${获取值}
    END
    [Teardown]    设置web设备参数量为默认值    下电模式

禁止下电模式电池下电测试(定时下电)
    [Documentation]    无“直流配电单元下电预置条件”关键字
    [Tags]    notest
    [Setup]    直流配电单元下电预置条件
    设置web参数量    下电模式    禁止
    ${定时下电使能}    获取web参数量    <<配电单元定时下电使能_1~0xa001050170001>>
    设置web参数量    <<配电单元定时下电使能_1~0xa001050170001>>    允许
    FOR    ${val}    IN RANGE    1    6
        ${参数}    Catenate    SEPARATOR=    <<配电单元定时下电起始时刻1_    ${val}    ~0xa001050180001>>
        设置web参数量    ${参数}    08:00
        ${参数}    Catenate    SEPARATOR=    <<配电单元定时下电终止时刻1_    ${val}    ~0xa001050190001>>
        设置web参数量    ${参数}    08:00
    END
    ${系统时间}    获取系统时间
    ${起始时间}    add time to date    ${系统时间}    00:02:00    result_format=%H:%M    exclude_millis=yes
    ${结束时间}    add time to date    ${系统时间}    00:05:00    result_format=%H:%M    exclude_millis=yes
    设置web参数量    <<配电单元定时下电起始时刻1_1~0xa001050180001>>    ${起始时间}
    设置web参数量    <<配电单元定时下电终止时刻1_1~0xa001050190001>>    ${结束时间}
    ${开始时间}    获取系统时间
    sleep    130
    判断告警存在    直流配电:直流配电单元下电告警[1]
    @{实时量名称}    Create List    租户负载电流_1    配电单元电流_1    租户一次下电状态_1    租户负载分路断状态_1
    @{下电实时值}    Create List    0.00    0.00    动作    断开
    @{上电实时值}    Create List    1    1    恢复    闭合
    FOR    ${val}    IN RANGE    2    3
        ${名称}    Get From List    ${实时量名称}    ${val}
        ${预期值}    Get From List    ${下电实时值}    ${val}
        ${获取值}    获取web实时数据    ${名称}
        should be equal    ${预期值}    ${获取值}
    END
    sleep    200
    判断告警不存在    直流配电:直流配电单元下电告警[1]
    FOR    ${val}    IN RANGE    2    3
        ${名称}    Get From List    ${实时量名称}    ${val}
        ${预期值}    Get From List    ${上电实时值}    ${val}
        ${获取值}    获取web实时数据    ${名称}
        Run keyword if    '电流' in $名称    should be true    ${获取值}
        Run Keyword Unless    '电流' in $名称    should be equal    ${预期值}    ${获取值}
    END
    ${结束时间}    获取系统时间
    ${告警内容}    获取web历史告警内容    ${开始时间}    ${结束时间}    1    10    1
    ${value}    Evaluate    False
    FOR    ${val}    IN    @{告警内容}
        ${array}    Split String    ${val}    ,
        ${value}=    set variable if    '直流配电:直流配电单元下电告警[1]' in '${array}[5]'    True    False
        Run keyword if    ${value}    Exit For Loop
    END
    should be true    ${value}
    设置web参数量    <<配电单元定时下电使能_1~0xa001050170001>>    ${定时下电使能}
    [Teardown]    设置web设备参数量为默认值    下电模式

禁止下电模式电池下电测试(免责下电)（未完成）
    [Documentation]    无“直流配电单元下电预置条件”关键字
    [Tags]    notest
    [Setup]    直流配电单元下电预置条件
    设置web参数量    下电模式    禁止
    [Teardown]    设置web设备参数量为默认值    下电模式

禁止下电模式电池下电测试(远程下电)
    [Documentation]    无“直流配电单元下电预置条件”关键字
    [Tags]    notest
    [Setup]    直流配电单元下电预置条件
    设置web参数量    下电模式    禁止
    设置web参数量    <<配电单元定时下电使能_1~0xa001050170001>>    禁止
    设置web参数量    <<配电单元免责下电使能_1~0xa0010502e0001>>    禁止
    设置web参数量    直流配电单元远程下电使能_1    允许
    ${开始时间}    获取系统时间
    设置web参数量    直流配电单元远程下电_1    下电
    sleep    10
    判断告警存在    直流配电:直流配电单元下电告警[1]
    直流配电下电状态    配电单元电流_1    1
    设置web参数量    直流配电单元远程下电_1    上电
    sleep    10
    判断告警不存在    直流配电:直流配电单元下电告警[1]
    直流配电上电状态    配电单元电流_1    1
    ${结束时间}    获取系统时间
    ${告警内容}    获取web历史告警内容    ${开始时间}    ${结束时间}    1    10    1
    ${value}    查询特定历史告警    ${告警内容}    直流配电:直流配电单元下电告警[1]
    should be true    ${value}
    [Teardown]    设置web设备参数量为默认值    直流配电单元远程下电使能_1    下电模式

禁止下电模式电池下电测试(配电单元和租户不同组合的远程下电)
    [Documentation]    无“直流配电单元下电预置条件”关键字
    [Tags]    notest
    设置web参数量    下电模式    禁止
    设置web参数量    配电单元名称编辑方式    铁塔定制
    ${直流配电单元数量}    获取指定量的调测信息    res_int2
    FOR    ${val}    IN RANGE    1    ${直流配电单元数量}+1
        远程下电测试    ${val}    ${直流配电单元数量}
    END
    [Teardown]    设置web设备参数量为默认值    下电模式

配电单元下电配置测试(电池电压)
    设置web参数量    下电模式    电池电压
    ${直流配电单元数量}    获取指定量的调测信息    res_int2
    FOR    ${val}    IN RANGE    1    ${直流配电单元数量}+1
        ${配电单元下电使能}    Catenate    SEPARATOR=    直流配电单元下电使能_    ${val}
        ${取值范围}    获取web参数的取值约定    ${配电单元下电使能}
        should be equal    ${取值范围}[0]    禁止
        should be equal    ${取值范围}[1]    允许
        设置web参数量    ${配电单元下电使能}    禁止
        ${下电电压}    Catenate    SEPARATOR=    直流配电单元下电电压_    ${val}
        ${下电时间}    Catenate    SEPARATOR=    直流配电单元下电时间_    ${val}
        ${下电SOC}    Catenate    SEPARATOR=    直流配电单元下电SOC阈值_    ${val}
        ${直流配电单元下电电压}    获取web参数量    ${下电电压}
        ${获取值}    Convert To Boolean    ${直流配电单元下电电压}
        should not be true    ${获取值}
        ${直流配电单元下电时间}    获取web参数量    ${下电时间}
        ${获取值}    Convert To Boolean    ${直流配电单元下电时间}
        should not be true    ${获取值}
        ${直流配电单元下电SOC}    获取web参数量    ${下电SOC}
        ${获取值}    Convert To Boolean    ${直流配电单元下电SOC}
        should not be true    ${获取值}
        设置web参数量    ${配电单元下电使能}    允许
        ${直流配电单元下电电压}    获取web参数量    ${下电电压}
        ${获取值}    Convert To Boolean    ${直流配电单元下电电压}
        should be true    ${获取值}
        ${直流配电单元下电时间}    获取web参数量    ${下电时间}
        ${获取值}    Convert To Boolean    ${直流配电单元下电时间}
        should not be true    ${获取值}
        ${直流配电单元下电SOC}    获取web参数量    ${下电SOC}
        ${获取值}    Convert To Boolean    ${直流配电单元下电SOC}
        should not be true    ${获取值}
        设置web设备参数量为默认值    ${配电单元下电使能}
    END
    [Teardown]    设置web设备参数量为默认值    下电模式

配电单元下电配置测试(停电时间)
    设置web参数量    下电模式    停电时间
    ${直流配电单元数量}    获取指定量的调测信息    res_int2
    FOR    ${val}    IN RANGE    1    ${直流配电单元数量}+1
        ${配电单元下电使能}    Catenate    SEPARATOR=    直流配电单元下电使能_    ${val}
        ${取值范围}    获取web参数的取值约定    ${配电单元下电使能}
        should be equal    ${取值范围}[0]    禁止
        should be equal    ${取值范围}[1]    允许
        设置web参数量    ${配电单元下电使能}    禁止
        ${下电电压}    Catenate    SEPARATOR=    直流配电单元下电电压_    ${val}
        ${下电时间}    Catenate    SEPARATOR=    直流配电单元下电时间_    ${val}
        ${下电SOC}    Catenate    SEPARATOR=    直流配电单元下电SOC阈值_    ${val}
        ${直流配电单元下电电压}    获取web参数量    ${下电电压}
        ${获取值}    Convert To Boolean    ${直流配电单元下电电压}
        should not be true    ${获取值}
        ${直流配电单元下电时间}    获取web参数量    ${下电时间}
        ${获取值}    Convert To Boolean    ${直流配电单元下电时间}
        should not be true    ${获取值}
        ${直流配电单元下电SOC}    获取web参数量    ${下电SOC}
        ${获取值}    Convert To Boolean    ${直流配电单元下电SOC}
        should not be true    ${获取值}
        设置web参数量    ${配电单元下电使能}    允许
        ${直流配电单元下电电压}    获取web参数量    ${下电电压}
        ${获取值}    Convert To Boolean    ${直流配电单元下电电压}
        should be true    ${获取值}
        ${直流配电单元下电时间}    获取web参数量    ${下电时间}
        ${获取值}    Convert To Boolean    ${直流配电单元下电时间}
        should be true    ${获取值}
        ${直流配电单元下电SOC}    获取web参数量    ${下电SOC}
        ${获取值}    Convert To Boolean    ${直流配电单元下电SOC}
        should not be true    ${获取值}
        设置web设备参数量为默认值    ${配电单元下电使能}
    END
    [Teardown]    设置web设备参数量为默认值    下电模式

配电单元下电配置测试(剩余电量)
    设置web参数量    下电模式    电池剩余容量
    ${直流配电单元数量}    获取指定量的调测信息    res_int2
    FOR    ${val}    IN RANGE    1    ${直流配电单元数量}+1
        ${配电单元下电使能}    Catenate    SEPARATOR=    直流配电单元下电使能_    ${val}
        ${取值范围}    获取web参数的取值约定    ${配电单元下电使能}
        should be equal    ${取值范围}[0]    禁止
        should be equal    ${取值范围}[1]    允许
        设置web参数量    ${配电单元下电使能}    禁止
        ${下电电压}    Catenate    SEPARATOR=    直流配电单元下电电压_    ${val}
        ${下电时间}    Catenate    SEPARATOR=    直流配电单元下电时间_    ${val}
        ${下电SOC}    Catenate    SEPARATOR=    直流配电单元下电SOC阈值_    ${val}
        ${直流配电单元下电电压}    获取web参数量    ${下电电压}
        ${获取值}    Convert To Boolean    ${直流配电单元下电电压}
        should not be true    ${获取值}
        ${直流配电单元下电时间}    获取web参数量    ${下电时间}
        ${获取值}    Convert To Boolean    ${直流配电单元下电时间}
        should not be true    ${获取值}
        ${直流配电单元下电SOC}    获取web参数量    ${下电SOC}
        ${获取值}    Convert To Boolean    ${直流配电单元下电SOC}
        should not be true    ${获取值}
        设置web参数量    ${配电单元下电使能}    允许
        ${直流配电单元下电电压}    获取web参数量    ${下电电压}
        ${获取值}    Convert To Boolean    ${直流配电单元下电电压}
        should be true    ${获取值}
        ${直流配电单元下电时间}    获取web参数量    ${下电时间}
        ${获取值}    Convert To Boolean    ${直流配电单元下电时间}
        should not be true    ${获取值}
        ${直流配电单元下电SOC}    获取web参数量    ${下电SOC}
        ${获取值}    Convert To Boolean    ${直流配电单元下电SOC}
        should be true    ${获取值}
        设置web设备参数量为默认值    ${配电单元下电使能}
    END
    [Teardown]    设置web设备参数量为默认值    下电模式

*** Keywords ***
远程下电测试
    [Arguments]    ${配电单元序号}    ${配电单元数量}
    ${定时下电使能}    Catenate    SEPARATOR=    <<配电单元定时下电使能_    ${配电单元序号}    ~0xa001050170001>>
    ${免责下电使能}    Catenate    SEPARATOR=    <<配电单元免责下电使能_    ${配电单元序号}    ~0xa0010502e0001>>
    ${远程下电使能}    Catenate    SEPARATOR=    直流配电单元远程下电使能_    ${配电单元序号}
    ${远程下电}    Catenate    SEPARATOR=    直流配电单元远程下电_    ${配电单元序号}
    ${下电告警}    Catenate    SEPARATOR=    直流配电:直流配电单元下电告警[    ${配电单元序号}    ]
    ${电流}    Catenate    SEPARATOR=    配电单元电流_    ${配电单元序号}
    设置web参数量    ${定时下电使能}    禁止
    设置web参数量    ${免责下电使能}    禁止
    设置web参数量    ${远程下电使能}    允许
    FOR    ${val}    IN RANGE    1    7
        直流配电单元下电预置条件    ${val}    ${配电单元序号}    ${配电单元数量}
        ${开始时间}    获取系统时间
        设置web参数量    ${远程下电}    下电
        sleep    10
        判断告警存在    ${下电告警}
        直流配电下电状态    ${电流}    ${val}
        设置web参数量    ${远程下电}    上电
        sleep    10
        判断告警不存在    ${下电告警}
        直流配电上电状态    ${电流}    ${val}
        ${结束时间}    获取系统时间
        ${告警内容}    获取web历史告警内容    ${开始时间}    ${结束时间}    1    10    1
        ${value}    查询特定历史告警    ${告警内容}    ${下电告警}
        should be true    ${value}
    END
    [Teardown]    设置web设备参数量为默认值    ${远程下电使能}    ${免责下电使能}    ${定时下电使能}
