*** Settings ***
Suite Setup       #生成1104/1363文件夹
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
1104_0002_V_3.0_1104_交流段_模拟量
    ${协议数据}    1104获取数据    ${g_prtcl_1104}    <<获取交流段模拟量~CID1=40H>>    <<获取交流段模拟量~CID2=42H>>    FF    ${g_ver_1104}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${对比结果}    批量对比数据_1104_WEB    ${协议数据}    ${web数据}    1104_交流段_模拟量
    should be true    ${对比结果}

1104_0004_V_3.0_1104_交流段_数字量
    ${协议数据}    1104获取数据    ${g_prtcl_1104}    <<获取交流段数字量~CID1=40H>>    <<获取交流段数字量~CID2=43H>>    FF    ${g_ver_1104}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${对比结果}    批量对比数据_1104_WEB    ${协议数据}    ${web数据}    1104_交流段_数字量
    should be true    ${对比结果}

1104_0006_V_3.0_1104_交流段_参数量
    ${协议数据}    1104获取数据    ${g_prtcl_1104}    <<获取交流段参数量~CID1=40H>>    <<获取交流段参数量~CID2=47H>>    FF    ${g_ver_1104}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${对比结果}    批量对比数据_1104_WEB    ${协议数据}    ${web数据}    1104_交流段_参数量
    should be true    ${对比结果}

1104_0008_V_3.0_1104_交流段_参数设置
    [Tags]    notest
    [Documentation]    <<市电降额系数~0x6001050020001>>
    ...    <<交流输入限功率预警阈值~0x6001050030001>>
    ...    <<交流输入限功率告警阈值~0x6001050040001>>
    ...    以上三个需要"市电额定有功功率"的值>0
    ${1104数据}    1104批量参数设置测试    ${g_prtcl_1104}    <<交设置交流段参数~CID1=40H>>    <<设置交流段参数~CID2=49H>>    ${g_ver_1104}
    ${对比结果}    批量对比参数设置_1104    ${1104数据}    1104_交流段_参数设置
    should be true    ${对比结果}
    [Teardown]    run keywords    设置web设备参数量为默认值    市电额定有功功率
    ...    AND    sleep    10
