*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
风扇数量配置测试
    连接CSU
    ${风扇配置1}    获取web参数量    风扇配置_1
    ${风扇配置2}    获取web参数量    风扇配置_2
    设置web参数量    风扇配置_1    有
    设置web参数量    风扇配置_1    无
    设置web参数量    风扇配置_2    有
    设置web参数量    风扇配置_2    无
    ${加热器开启温度}    获取web参数量    加热器开启温度
    ${加热器关闭温度}    获取web参数量    加热器关闭温度
    should not be equal    ${加热器开启温度}    ${加热器关闭温度}
    设置web参数量    风扇配置_1    ${风扇配置1}
    设置web参数量    风扇配置_2    ${风扇配置2}

风扇控制功能使能测试
    连接CSU
    ${风扇配置1}    获取web参数量    风扇配置_1
    ${风扇配置2}    获取web参数量    风扇配置_2
    设置web参数量    风扇配置_1    有
    设置web参数量    风扇配置_2    有
    ${参数}    Create List    风扇停转使能    风扇最大转速    风扇故障阈值    风扇停转温度    风扇控制曲线温度_1    风扇控制曲线温度_2    风扇控制曲线温度_3    风扇控制曲线温度_4    风扇控制曲线温度_5    风扇控制转速比率_1    风扇控制转速比率_2    风扇控制转速比率_3    风扇控制转速比率_4    风扇控制转速比率_5
    FOR    ${val}    IN    @{参数}
        ${参数值}    获取web参数量    ${val}
        ${转换值}    Convert To Boolean    ${参数值}
        Should Be True    ${转换值}
    END
    设置web参数量    风扇配置_1    无
    设置web参数量    风扇配置_2    无
    FOR    ${val}    IN    @{参数}
        ${参数值}    获取web参数量    ${val}
        ${转换值}    Convert To Boolean    ${参数值}
        Should Not Be True    ${转换值}
    END
    设置web参数量    风扇配置_1    无
    设置web参数量    风扇配置_2    有
    FOR    ${val}    IN    @{参数}
        ${参数值}    获取web参数量    ${val}
        ${转换值}    Convert To Boolean    ${参数值}
        Should Be True    ${转换值}
    END
    设置web参数量    风扇配置_1    有
    设置web参数量    风扇配置_2    无
    FOR    ${val}    IN    @{参数}
        ${参数值}    获取web参数量    ${val}
        ${转换值}    Convert To Boolean    ${参数值}
        Should Be True    ${转换值}
    END
    设置web参数量    风扇配置_1    ${风扇配置1}
    设置web参数量    风扇配置_2    ${风扇配置2}
