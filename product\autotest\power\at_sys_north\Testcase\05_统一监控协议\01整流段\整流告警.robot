*** Settings ***
Suite Setup       主动告警测试前置条件    ${CSU_role}
Suite Teardown    设置web设备参数量为默认值    CSU主动告警使能
Test Setup        Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
sm_批量获取状态量和SMR告警测试
    [Setup]    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    写入CSV文档    整流器数字量和告警量获取测试    信号名称    信号值    结果
    连接CSU
    ${级别设置值}    获取web参数量    整流器告警
    run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    整流器告警    严重
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=整流器
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除告警量信号}    ${排除列表}    1    ${模拟整流器地址}    1
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        @{序号}    evaluate    re.split('[-~]','${信号名称}')    re
        ${sm告警名}    set variable    整流器告警
        ${sm告警名}    Catenate    SEPARATOR=    ${sm告警名}    -    ${序号}[1]
        ${实时告警中的设备名称}    主动告警_组合实时告警中的设备名称    整流器    ${信号名称}
        Run Keyword And Continue On Failure    整流器或PU告警量产生封装判断结果    sm    SMR    ${信号名称}    数字量    ${告警产生}    ${模拟整流器地址}    整流器告警    整流器数字量和告警量获取测试    ${实时告警中的设备名称}    null    null    整流告警量    null    ${sm告警名}
        Run Keyword And Continue On Failure    整流器或PU告警量恢复封装判断结果    sm    SMR    ${信号名称}    数字量    ${告警恢复}    ${模拟整流器地址}    整流器告警    整流器数字量和告警量获取测试    null    null    null    整流告警量    null    ${sm告警名}
    END
    [Teardown]

sm_批量获取状态量和SMR故障测试
    [Setup]    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    写入CSV文档    整流器数字量和故障量获取测试    信号名称    信号值    结果
    连接CSU
    ${级别设置值}    获取web参数量    整流器故障
    run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    整流器故障    严重
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=整流器
    ${排除列表}    create list
    P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除故障量信号}    ${排除列表}    1    ${模拟整流器地址}    1
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        @{序号}    evaluate    re.split('[-~]','${信号名称}')    re
        ${sm告警名}    set variable    整流器故障
        ${sm告警名}    Catenate    SEPARATOR=    ${sm告警名}    -    ${序号}[1]
        ${实时告警中的设备名称}    主动告警_组合实时告警中的设备名称    整流器    ${信号名称}
        Run Keyword And Continue On Failure    整流器或PU故障量产生封装判断结果    sm    SMR    ${信号名称}    数字量    ${告警产生}    ${模拟整流器地址}    整流器故障    整流器数字量和故障量获取测试    ${实时告警中的设备名称}    null    null    整流告警量    null    ${sm告警名}
        ...    null
        Run Keyword And Continue On Failure    整流器或PU故障量恢复封装判断结果    sm    SMR    ${信号名称}    数字量    ${告警恢复}    ${模拟整流器地址}    整流器故障    整流器数字量和故障量获取测试    null    null    null    整流告警量    null    ${sm告警名}
        ...    null
    END

uniform_0026_通讯中断告警
    [Setup]    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    连接CSU
    ${实际工作整流器地址}    获取工作整流器地址
    #子设备工具模拟
    ${级别设置值}    获取web参数量    整流器通讯中断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    整流器通讯中断    严重
    wait until keyword succeeds    2m    2    查询指定告警信息不为    整流器通讯中断
    ${SMR中断}    evaluate    str(${模拟整流器开始地址}+1)
    设置子工具个数    SMR    1
    FOR    ${SMR序号}    IN RANGE    ${SMR中断}    ${北向协议SMR最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    整流器通讯状态-${SMR序号}    异常
        wait until keyword succeeds    5m    2    判断告警存在    整流器通讯中断-${SMR序号}
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    整流告警量    整流器通讯中断-${SMR序号}
        should be true    ${统一监控协议告警结果}
    END
    wait until keyword succeeds    5m    1    判断告警不存在    整流器通讯中断-${模拟整流器开始地址}
    设置子工具个数    SMR    48
    FOR    ${SMR序号}    IN RANGE    ${SMR中断}    ${北向协议SMR最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    整流器通讯状态-${SMR序号}    正常
        wait until keyword succeeds    5m    1    判断告警不存在    整流器通讯中断-${SMR序号}
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    整流告警量    整流器通讯中断-${SMR序号}
        should not be true    ${统一监控协议告警结果}
    END
    [Teardown]    Run Keywords    设置子工具个数    SMR    48
    ...    AND    sleep    3min

uniform_0028_多整流器模块告警
    [Tags]    T1-1
    # [Setup]    整流器测试结束条件
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    10    2    设置web参数量    多个整流器模块告警    严重
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    多个整流器模块告警
    #使整流器输出过压来制造多个整流器告警
    ${均充电压可设置范围}    获取web参数可设置范围    均充电压
    设置web参数量    均充电压    ${均充电压可设置范围}[0]
    ${输出高停机电压可设置范围}    获取web参数可设置范围    整流器输出高停机电压
    设置web参数量    整流器输出高停机电压    ${输出高停机电压可设置范围}[0]
    ${整流器输出高电压}    获取web参数量    整流器输出高停机电压
    向上调节电池电压    ${整流器输出高电压} + 0.5
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    工作整流器数量    0
    Wait Until Keyword Succeeds    5m    2    设置web参数量    多个整流器模块告警    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    多个整流器模块告警
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    多个整流器模块告警    ${告警级别设置}
        sleep    2
        wait until keyword succeeds    1m    1    查询指定告警信息    多个整流器模块告警
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    整流告警量    多整流器同时告警
        should be true    ${统一监控协议告警结果}
        ${多个整流器模块告警级别}    获取web告警属性    多个整流器模块告警    告警级别
        should be equal    '${告警级别设置}'    '${多个整流器模块告警级别}'
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    多个整流器模块告警    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    #整流器过压恢复，5min应能恢复
    设置web设备参数量为默认值    整流器输出高停机电压
    设置web设备参数量为默认值    均充电压
    向下调节电池电压    53.5
    ${在线整流器数量}    获取web实时数据    在线整流器数量
    Wait Until Keyword Succeeds    5m20s    10    信号量数据值为    工作整流器数量    ${在线整流器数量}
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    多个整流器模块告警
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    整流告警量    多整流器同时告警
    should not be true    ${统一监控协议告警结果}
    [Teardown]    重置电池模拟器输出

整流器风扇故障SMR故障测试
    [Setup]    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    ${指定数据}    Create Dictionary    signal_name    <<整流器风扇故障-4~0x7001030020001>>    convention    True    device_name    整流器    data_type    int    unit    ''
    @{整流器故障告警列表}    Create List    ${指定数据}
    FOR    ${i}    IN RANGE    5    49
        ${指定数据1}    Create Dictionary    signal_name    <<整流器风扇故障-${i}~0x7001030020001>>    convention    True    device_name    整流器    data_type    int    unit    ''
        Append To List    ${整流器故障告警列表}    ${指定数据1}
    END
    ${级别设置值}    获取web参数量    整流器故障
    ${子设备名称}    Set Variable    SMR
    ${通用告警}    Set Variable    整流器故障
    run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    整流器故障    严重
    Run Keyword And Continue On Failure    wait until keyword succeeds    5m    5    判断告警不存在    整流器故障
    设置子工具值    ${子设备名称}    all    数字量    整流器风扇故障状态    1
    FOR    ${i}    IN    @{整流器故障告警列表}
    ${信号名称}    Get From Dictionary    ${i}    signal_name
    @{序号}    evaluate    re.split('[-~]','${信号名称}')    re
    ${sm告警名}    set variable    整流器故障
    ${sm告警名}    Catenate    SEPARATOR=    ${sm告警名}    -    ${序号}[1]
    ${实时告警中的设备名称}    主动告警_组合实时告警中的设备名称    整流器    ${信号名称}
    ${信号量}    拆分信号量名称和SID    ${信号名称}
    @{信号名称及下标}    split string    ${信号量}[0]    -
    ${获取子工具信号名称}    获取子工具信号名称    ${子设备名称}    ${信号量}[2]
    @{信号名称及关联SID}    split string    ${获取子工具信号名称}    ,
    ${故障名称}    set variable    ${通用告警}-${信号名称及下标}[1]
    ${列表}    获取数据字典指定查询的信号量列表_通过SID    ${信号名称及关联SID}[1]    ${信号量}[0]
    FOR    ${i}    IN    @{列表}
        ${关联信号名称}    Get From Dictionary    ${i}    signal_name
        ${关联信号量}    拆分信号量名称和SID    ${关联信号名称}
        ${取值约定dict}    获取web参数的取值约定    ${关联信号名称}
        ${参数值对应的取值约定}    Get From Dictionary    ${取值约定dict}    1
        Run Keyword And Continue On Failure    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    ${关联信号名称}    ${参数值对应的取值约定}
        Run Keyword And Continue On Failure    wait until keyword succeeds    5m    5    判断告警存在    ${信号量}[0]
    END
    ${sm告警}    获取统一监控协议一条告警值并判断告警存在    整流告警量    ${sm告警名}
    should be true    ${sm告警}
    ${整流器风扇故障告警}    获取统一监控协议一条告警值并判断告警存在    整流告警量    ${信号量}[0]
    should be true    ${整流器风扇故障告警}
    END
    设置子工具值    ${子设备名称}    all    数字量    整流器风扇故障状态    0
    FOR    ${i}    IN    @{整流器故障告警列表}
    ${信号名称}    Get From Dictionary    ${i}    signal_name
    @{序号}    evaluate    re.split('[-~]','${信号名称}')    re
    ${sm告警名}    set variable    整流器故障
    ${sm告警名}    Catenate    SEPARATOR=    ${sm告警名}    -    ${序号}[1]
    ${实时告警中的设备名称}    主动告警_组合实时告警中的设备名称    整流器    ${信号名称}
    ${信号量}    拆分信号量名称和SID    ${信号名称}
    @{信号名称及下标}    split string    ${信号量}[0]    -
    ${获取子工具信号名称}    获取子工具信号名称    ${子设备名称}    ${信号量}[2]
    @{信号名称及关联SID}    split string    ${获取子工具信号名称}    ,
    ${故障名称}    set variable    ${通用告警}-${信号名称及下标}[1]
    ${列表}    获取数据字典指定查询的信号量列表_通过SID    ${信号名称及关联SID}[1]    ${信号量}[0]
    FOR    ${i}    IN    @{列表}
        ${关联信号名称}    Get From Dictionary    ${i}    signal_name
        ${关联信号量}    拆分信号量名称和SID    ${关联信号名称}
        ${取值约定dict}    获取web参数的取值约定    ${关联信号名称}
        ${参数值对应的取值约定}    Get From Dictionary    ${取值约定dict}    0
        Run Keyword And Continue On Failure    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    ${关联信号名称}    ${参数值对应的取值约定}
        Run Keyword And Continue On Failure    wait until keyword succeeds    5m    5    判断告警不存在    ${信号量}[0]
    END
    ${sm告警}    获取统一监控协议一条告警值并判断告警存在    整流告警量    ${sm告警名}
    Should Not Be True    ${sm告警}
    ${整流器风扇故障告警}    获取统一监控协议一条告警值并判断告警存在    整流告警量    ${信号量}[0]
    Should Not Be True    ${整流器风扇故障告警}
    END
