*** Settings ***
Suite Setup
Suite Teardown
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
1363_0004_V_3.0_1363_VRLA段_参数量
    [Tags]    1
    ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取VRLA段参数量~CID1=46H>>    <<获取VRLA段参数量~CID2=47H>>    None    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${对比结果}    批量对比数据_1104_WEB    ${协议数据}    ${web数据}    1363_VRLA段_参数量
    should be true    ${对比结果}

1363_0006_V_3.0_1363_VRLA段_参数设置
    [Tags]    1
    ${1363数据}    1104批量参数设置测试    ${g_prtcl_1363}    <<设置VRLA段参数量~CID1=46H>>    <<设设置VRLA段参数量~CID2=49H>>    ${g_ver_1363}
    ${对比结果}    批量对比参数设置_1104    ${1363数据}    1363_VRLA段_参数设置
    should be true    ${对比结果}

1363_0008_V_3.0_1363_VRLA段_模拟量
    [Tags]    1
    [Setup]    Run keywords    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池组容量_2    100
    ...    AND    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池组容量_3    100
    ...    AND    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池组容量_4    100
    ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取VRLA段模拟量~CID1=46H>>    <<获取VRLA段模拟量~CID2=42H>>    None    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${对比结果}    批量对比数据_1104_WEB    ${协议数据}    ${web数据}    1363_VRLA段_模拟量
    Comment    should be true    ${对比结果}
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池组容量_2    0
    ...    AND    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池组容量_3    0
    ...    AND    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池组容量_4    0
