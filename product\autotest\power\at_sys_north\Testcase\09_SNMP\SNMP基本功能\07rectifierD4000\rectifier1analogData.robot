*** Settings ***
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp_test关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/Web关键字_V30.robot
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
snmp批量获取SMR模拟量
    [Documentation]    9min
    写入CSV文档    整流器模拟量获取测试    信号名称    信号值    结果    备注
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=整流器
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除模拟量信号}    ${排除列表}    3    ${模拟整流器开始地址}
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    rectifier    analogData
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    1    snmp
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    snmp    SMR4000    模拟量    ${缺省值列表}    整流器模拟量获取测试    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    2    snmp
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    snmp    SMR4000    模拟量    ${缺省值列表}    整流器模拟量获取测试    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    0    snmp
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    snmp    SMR4000    模拟量    ${缺省值列表}    整流器模拟量获取测试    null
    断开连接SNMP

