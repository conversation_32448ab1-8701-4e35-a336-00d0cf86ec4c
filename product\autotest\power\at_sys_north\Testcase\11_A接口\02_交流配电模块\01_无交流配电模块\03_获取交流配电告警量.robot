*** Settings ***
Resource          ../../../../../../ztepwrlibrary/Resource/协议平台关键字.robot
Resource          ../../A接口关键字_V30.robot

*** Test Cases ***
参数有效，获取交流配电告警量
    ${返回值}    ${A接口获取数据}    协议平台获取北向数据    获取交流段告警量_40H_44H_FFH
    should be true    ${返回值}
    log    ${A接口获取数据}
    ${web获取数据}    web获取实时告警sid
    ${数据对比结果}    批量对比告警_A接口_WEB    ${A接口获取数据}    ${web获取数据}    A_交流段_告警量1
    should be true    ${数据对比结果}
    ${返回值}    ${A接口获取数据}    协议平台获取北向数据    获取交流段告警量_40H_44H_01H
    should be true    ${返回值}
    log    ${A接口获取数据}
    ${web获取数据}    web获取实时告警sid
    ${数据对比结果}    批量对比告警_A接口_WEB    ${A接口获取数据}    ${web获取数据}    A_交流段_告警量1
    should be true    ${数据对比结果}

参数无效，获取交流配电告警量
    ${返回结果}    A接口生成自定义数据帧    40    44
    ${返回值}    Get From Dictionary    ${返回结果}    result
    ${错误信息}    Get From Dictionary    ${返回结果}    err
    should not be true    ${返回值}
    should be equal as strings    ${错误信息}    命令格式错
    ${返回结果}    A接口生成自定义数据帧    40    44    00
    ${返回值}    Get From Dictionary    ${返回结果}    result
    ${错误信息}    Get From Dictionary    ${返回结果}    err
    should not be true    ${返回值}
    should be equal as strings    ${错误信息}    命令格式错
    ${返回结果}    A接口生成自定义数据帧    40    44    02
    ${返回值}    Get From Dictionary    ${返回结果}    result
    ${错误信息}    Get From Dictionary    ${返回结果}    err
    should not be true    ${返回值}
    should be equal as strings    ${错误信息}    命令格式错
