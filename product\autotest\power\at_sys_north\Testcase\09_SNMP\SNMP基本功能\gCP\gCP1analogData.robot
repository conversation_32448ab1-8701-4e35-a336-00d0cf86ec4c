*** Settings ***
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp_test关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/Web关键字_V30.robot
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
# snmp批量获取gcp模拟量
#     [Documentation]    9min
#     写入CSV文档    gcp模拟量获取测试    信号名称    信号值    结果    备注
#     连接CSU
#     进行SNMP_V2/V3连接    ${snmp连接方式}
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=油机控制屏
#     ${排除列表}    create list
#     append to list    ${油机控制屏排除模拟量信号}    油机油压~0x1a001010120001
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${油机控制屏排除模拟量信号}    ${排除列表}
#     ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    gCP    analogData
#     Comment    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    FBBMS模块    analog data    ${PU排除模拟量信号}
#     Comment    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
#     FOR    ${i}    IN    @{snmp待测}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${节点名称}    Get From Dictionary    ${i}    snmp_name
#         ${信号序号}    Get From Dictionary    ${i}    Vindex
#         ${缺省值}    获取web参数上下限范围    ${信号名称}
#         Run Keyword And Continue On Failure    南向子设备模拟量获取值封装判断结果    snmp    oileng    ${信号名称}    只读    ${缺省值}[1]    gcp模拟量获取测试    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    南向子设备模拟量获取值封装判断结果    snmp    oileng    ${信号名称}    只读    ${缺省值}[2]    gcp模拟量获取测试    null    ${节点名称}    ${信号序号}
#         Run keyword IF    ${缺省值}[1]!=${缺省值}[0] and ${缺省值}[2]!=${缺省值}[0]    Run Keyword And Continue On Failure    南向子设备模拟量获取值封装判断结果    snmp    oileng    ${信号名称}    只读    ${缺省值}[0]    gcp模拟量获取测试    null    ${节点名称}    ${信号序号}
#     END
#     断开连接SNMP

snmp批量获取gcp模拟量
    [Documentation]    9min
    写入CSV文档    gcp模拟量获取测试    信号名称    信号值    结果    备注
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=油机控制屏
    ${排除列表}    create list
    append to list    ${油机控制屏排除模拟量信号}    油机油压~0x1a001010120001
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${油机控制屏排除模拟量信号}    ${排除列表}
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    gCP    analogData
    Comment    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    FBBMS模块    analog data    ${PU排除模拟量信号}
    Comment    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    1    snmp
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    snmp    oileng    只读    ${缺省值列表}    gcp模拟量获取测试    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    2    snmp
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    snmp    oileng    只读    ${缺省值列表}    gcp模拟量获取测试    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    0    snmp
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    snmp    oileng    只读    ${缺省值列表}    gcp模拟量获取测试    null
    断开连接SNMP

snmp_0002_油机相电压√
    连接CSU
    设置子工具值    oileng    all    只读    发电 A 相电压    0
    设置子工具值    oileng    all    只读    发电 B 相电压    0
    设置子工具值    oileng    all    只读    发电 C 相电压    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机相电压_1    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机相电压_2    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机相电压_3    0
    进行SNMP_V2/V3连接    ${snmp连接方式}
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    0    gCPDGPhaseVoltage1value    1
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    0    gCPDGPhaseVoltage1value    2
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    0    gCPDGPhaseVoltage1value    3
    设置子工具值    oileng    all    只读    发电 A 相电压    299
    设置子工具值    oileng    all    只读    发电 B 相电压    300
    设置子工具值    oileng    all    只读    发电 C 相电压    298
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机相电压_1    298
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机相电压_2    300
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机相电压_3    299
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    298    gCPDGPhaseVoltage1value    1
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    300    gCPDGPhaseVoltage1value    2
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    299    gCPDGPhaseVoltage1value    3
    设置子工具值    oileng    all    只读    发电 A 相电压    220
    设置子工具值    oileng    all    只读    发电 B 相电压    221
    设置子工具值    oileng    all    只读    发电 C 相电压    222
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机相电压_1    222
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机相电压_2    221
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机相电压_3    220
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    222    gCPDGPhaseVoltage1value    1
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    221    gCPDGPhaseVoltage1value    2
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    220    gCPDGPhaseVoltage1value    3
    断开连接SNMP

snmp_0004_油机线电压√
    连接CSU
    设置子工具值    oileng    all    只读    发电 AC 线电压    0
    设置子工具值    oileng    all    只读    发电 BC 线电压    0
    设置子工具值    oileng    all    只读    发电 AB 线电压    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机线电压_1    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机线电压_2    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机线电压_3    0
    进行SNMP_V2/V3连接    ${snmp连接方式}
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    0    gCPDGLineVoltage1value    1
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    0    gCPDGLineVoltage1value    2
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    0    gCPDGLineVoltage1value    3
    设置子工具值    oileng    all    只读    发电 AC 线电压    520
    设置子工具值    oileng    all    只读    发电 BC 线电压    519
    设置子工具值    oileng    all    只读    发电 AB 线电压    518
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机线电压_1    520
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机线电压_2    519
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机线电压_3    518
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    520   gCPDGLineVoltage1value    1
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    519   gCPDGLineVoltage1value    2
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    518   gCPDGLineVoltage1value    3
    设置子工具值    oileng    all    只读    发电 AC 线电压    380
    设置子工具值    oileng    all    只读    发电 BC 线电压    381
    设置子工具值    oileng    all    只读    发电 AB 线电压    379
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机线电压_1    380
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机线电压_2    381
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机线电压_3    379
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    380   gCPDGLineVoltage1value    1
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    381   gCPDGLineVoltage1value    2
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    379   gCPDGLineVoltage1value    3
    断开连接SNMP

snmp_0006_油机相电流√
    连接CSU
    设置子工具值    oileng    all    只读    发电 A 相电流    601
    设置子工具值    oileng    all    只读    发电 B 相电流    602
    设置子工具值    oileng    all    只读    发电 C 相电流    603
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机相电流_1    603
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机相电流_2    602
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机相电流_3   601
    进行SNMP_V2/V3连接    ${snmp连接方式}
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    603    gCPDGPhaseCurrent1value    1
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    602    gCPDGPhaseCurrent1value    2
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    601    gCPDGPhaseCurrent1value    3
    设置子工具值    oileng    all    只读    发电 A 相电流    221
    设置子工具值    oileng    all    只读    发电 B 相电流    220
    设置子工具值    oileng    all    只读    发电 C 相电流    219
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机相电流_1    219
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机相电流_2    220
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机相电流_3    221
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    219    gCPDGPhaseCurrent1value    1
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    220    gCPDGPhaseCurrent1value    2
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    221    gCPDGPhaseCurrent1value    3
    设置子工具值    oileng    all    只读    发电 A 相电流    1
    设置子工具值    oileng    all    只读    发电 B 相电流    0
    设置子工具值    oileng    all    只读    发电 C 相电流    2
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机相电流_1    2
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机相电流_2    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机相电流_3    1
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    2    gCPDGPhaseCurrent1value    1
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    0    gCPDGPhaseCurrent1value    2
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    1    gCPDGPhaseCurrent1value    3
    断开连接SNMP
    
snmp_0008_油机功率因数√
    连接CSU
    设置子工具值    oileng    all    只读    发电 A 相功率因数    0.98
    设置子工具值    oileng    all    只读    发电 B 相功率因数    0.99
    设置子工具值    oileng    all    只读    发电 C 相功率因数    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机功率因数_1    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机功率因数_2    0.99
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机功率因数_3    0.98
    进行SNMP_V2/V3连接    ${snmp连接方式}
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    1       gCPDGPowerFactor1value    1
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    0.99    gCPDGPowerFactor1value    2
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    0.98    gCPDGPowerFactor1value    3
    设置子工具值    oileng    all    只读    发电 A 相功率因数    99
    设置子工具值    oileng    all    只读    发电 B 相功率因数    98
    设置子工具值    oileng    all    只读    发电 C 相功率因数    97
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机功率因数_1    97
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机功率因数_2    98
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机功率因数_3    99
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    97     gCPDGPowerFactor1value    1
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    98     gCPDGPowerFactor1value    2
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    99     gCPDGPowerFactor1value    3
    设置子工具值    oileng    all    只读    发电 A 相功率因数    0.95
    设置子工具值    oileng    all    只读    发电 B 相功率因数    0.96
    设置子工具值    oileng    all    只读    发电 C 相功率因数    0.97
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机功率因数_1    0.97
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机功率因数_2    0.96
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机功率因数_3    0.95
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    0.97     gCPDGPowerFactor1value    1
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    0.96     gCPDGPowerFactor1value    2
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    0.95     gCPDGPowerFactor1value    3
    断开连接SNMP
    

snmp_0010_油机输出频率√
    [Documentation]    def,min,max:50,40,75
    连接CSU
    设置子工具值    oileng    all    只读    发电频率    75
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机输出频率    75
    进行SNMP_V2/V3连接    ${snmp连接方式}
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    75     gCPDGOutputFrequency1value    0
    设置子工具值    oileng    all    只读    发电频率    40
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机输出频率    40
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    40     gCPDGOutputFrequency1value    0
    设置子工具值    oileng    all    只读    发电频率    50
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    油机输出频率    50
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    50     gCPDGOutputFrequency1value    0
    断开连接SNMP
    

snmp_0012_油机视在功率√
    [Documentation]    def,min,max:50,40,75
    连接CSU
    设置子工具值    oileng    all    只读    发电 A 相视在功率    1001
    设置子工具值    oileng    all    只读    发电 B 相视在功率    2002
    设置子工具值    oileng    all    只读    发电 C 相视在功率    3003
    sleep    1m
    ${获取值1}    获取web实时数据    油机视在功率_1
    ${获取值2}    获取web实时数据    油机视在功率_2
    ${获取值3}    获取web实时数据    油机视在功率_3
    #
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${snmp获取值1}    获取SNMP数据_单个    gCPDGApparentPower1value    序号=1
    ${snmp获取值2}    获取SNMP数据_单个    gCPDGApparentPower1value    序号=2
    ${snmp获取值3}    获取SNMP数据_单个    gCPDGApparentPower1value    序号=3
    should be true    ${获取值1}==3003
    should be true    ${获取值2}==2002
    should be true    ${获取值3}==1001
    should be true    ${snmp获取值1}==3003
    should be true    ${snmp获取值2}==2002
    should be true    ${snmp获取值3}==1001
    设置子工具值    oileng    all    只读    发电 A 相视在功率    3010
    设置子工具值    oileng    all    只读    发电 B 相视在功率    3120
    设置子工具值    oileng    all    只读    发电 C 相视在功率    3478
    sleep    1m
    ${获取值1}    获取web实时数据    油机视在功率_1
    ${获取值2}    获取web实时数据    油机视在功率_2
    ${获取值3}    获取web实时数据    油机视在功率_3
    ${snmp获取值1}    获取SNMP数据_单个    gCPDGApparentPower1value    序号=1
    ${snmp获取值2}    获取SNMP数据_单个    gCPDGApparentPower1value    序号=2
    ${snmp获取值3}    获取SNMP数据_单个    gCPDGApparentPower1value    序号=3
    should be true    ${获取值1}==3478
    should be true    ${获取值2}==3120
    should be true    ${获取值3}==3010
    should be true    ${snmp获取值1}==3478
    should be true    ${snmp获取值2}==3120
    should be true    ${snmp获取值3}==3010
    设置子工具值    oileng    all    只读    发电 A 相视在功率    4300
    设置子工具值    oileng    all    只读    发电 B 相视在功率    4334
    设置子工具值    oileng    all    只读    发电 C 相视在功率    4367
    sleep    1m
    ${获取值1}    获取web实时数据    油机视在功率_1
    ${获取值2}    获取web实时数据    油机视在功率_2
    ${获取值3}    获取web实时数据    油机视在功率_3
    ${snmp获取值1}    获取SNMP数据_单个    gCPDGApparentPower1value    序号=1
    ${snmp获取值2}    获取SNMP数据_单个    gCPDGApparentPower1value    序号=2
    ${snmp获取值3}    获取SNMP数据_单个    gCPDGApparentPower1value    序号=3
    #
    断开连接SNMP
    should be true    ${获取值1}==4367
    should be true    ${获取值2}==4334
    should be true    ${获取值3}==4300
    should be true    ${snmp获取值1}==4367
    should be true    ${snmp获取值2}==4334
    should be true    ${snmp获取值3}==4300

snmp_0014_油机有功功率√
    [Documentation]    def,min,max:50,40,75
    连接CSU
    设置子工具值    oileng    all    只读    发电 A 相有功功率    4000
    设置子工具值    oileng    all    只读    发电 B 相有功功率    5000
    设置子工具值    oileng    all    只读    发电 C 相有功功率    6000
    sleep    1m
    ${获取值1}    获取web实时数据    油机有功功率_1
    ${获取值2}    获取web实时数据    油机有功功率_2
    ${获取值3}    获取web实时数据    油机有功功率_3
    #
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${snmp获取值1}    获取SNMP数据_单个    gCPDGActivePower1value    序号=1
    ${snmp获取值2}    获取SNMP数据_单个    gCPDGActivePower1value    序号=2
    ${snmp获取值3}    获取SNMP数据_单个    gCPDGActivePower1value    序号=3
    should be true    ${获取值1}==6000
    should be true    ${获取值2}==5000
    should be true    ${获取值3}==4000
    should be true    ${snmp获取值1}==6000
    should be true    ${snmp获取值2}==5000
    should be true    ${snmp获取值3}==4000
    设置子工具值    oileng    all    只读    发电 A 相有功功率    2000
    设置子工具值    oileng    all    只读    发电 B 相有功功率    3000
    设置子工具值    oileng    all    只读    发电 C 相有功功率    3568
    sleep    1m
    ${获取值1}    获取web实时数据    油机有功功率_1
    ${获取值2}    获取web实时数据    油机有功功率_2
    ${获取值3}    获取web实时数据    油机有功功率_3
    ${snmp获取值1}    获取SNMP数据_单个    gCPDGActivePower1value    序号=1
    ${snmp获取值2}    获取SNMP数据_单个    gCPDGActivePower1value    序号=2
    ${snmp获取值3}    获取SNMP数据_单个    gCPDGActivePower1value    序号=3
    should be true    ${获取值1}==3568
    should be true    ${获取值2}==3000
    should be true    ${获取值3}==2000
    should be true    ${snmp获取值1}==3568
    should be true    ${snmp获取值2}==3000
    should be true    ${snmp获取值3}==2000
    设置子工具值    oileng    all    只读    发电 A 相有功功率    2200
    设置子工具值    oileng    all    只读    发电 B 相有功功率    2234
    设置子工具值    oileng    all    只读    发电 C 相有功功率    2267
    sleep    1m
    ${获取值1}    获取web实时数据    油机有功功率_1
    ${获取值2}    获取web实时数据    油机有功功率_2
    ${获取值3}    获取web实时数据    油机有功功率_3
    ${snmp获取值1}    获取SNMP数据_单个    gCPDGActivePower1value    序号=1
    ${snmp获取值2}    获取SNMP数据_单个    gCPDGActivePower1value    序号=2
    ${snmp获取值3}    获取SNMP数据_单个    gCPDGActivePower1value    序号=3
    #
    断开连接SNMP
    should be true    ${获取值1}==2267
    should be true    ${获取值2}==2234
    should be true    ${获取值3}==2200
    should be true    ${snmp获取值1}==2267
    should be true    ${snmp获取值2}==2234
    should be true    ${snmp获取值3}==2200

snmp_0016_油机无功功率√
    [Documentation]    def,min,max:50,40,75
    连接CSU
    设置子工具值    oileng    all    只读    发电 A 相无功功率    98
    设置子工具值    oileng    all    只读    发电 B 相无功功率    88
    设置子工具值    oileng    all    只读    发电 C 相无功功率    68
    sleep    1m
    ${获取值1}    获取web实时数据    油机无功功率_1
    ${获取值2}    获取web实时数据    油机无功功率_2
    ${获取值3}    获取web实时数据    油机无功功率_3
    #
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${snmp获取值1}    获取SNMP数据_单个    gCPDGReactivePower1value    序号=1
    ${snmp获取值2}    获取SNMP数据_单个    gCPDGReactivePower1value    序号=2
    ${snmp获取值3}    获取SNMP数据_单个    gCPDGReactivePower1value    序号=3
    should be true    ${获取值1}==68
    should be true    ${获取值2}==88
    should be true    ${获取值3}==98
    should be true    ${snmp获取值1}==68
    should be true    ${snmp获取值2}==88
    should be true    ${snmp获取值3}==98
    设置子工具值    oileng    all    只读    发电 A 相无功功率    1000
    设置子工具值    oileng    all    只读    发电 B 相无功功率    1100
    设置子工具值    oileng    all    只读    发电 C 相无功功率    1468
    sleep    1m
    ${获取值1}    获取web实时数据    油机无功功率_1
    ${获取值2}    获取web实时数据    油机无功功率_2
    ${获取值3}    获取web实时数据    油机无功功率_3
    ${snmp获取值1}    获取SNMP数据_单个    gCPDGReactivePower1value    序号=1
    ${snmp获取值2}    获取SNMP数据_单个    gCPDGReactivePower1value    序号=2
    ${snmp获取值3}    获取SNMP数据_单个    gCPDGReactivePower1value    序号=3
    should be true    ${获取值1}==1468
    should be true    ${获取值2}==1100
    should be true    ${获取值3}==1000
    should be true    ${snmp获取值1}==1468
    should be true    ${snmp获取值2}==1100
    should be true    ${snmp获取值3}==1000
    设置子工具值    oileng    all    只读    发电 A 相无功功率    200
    设置子工具值    oileng    all    只读    发电 B 相无功功率    234
    设置子工具值    oileng    all    只读    发电 C 相无功功率    267
    sleep    1m
    ${获取值1}    获取web实时数据    油机无功功率_1
    ${获取值2}    获取web实时数据    油机无功功率_2
    ${获取值3}    获取web实时数据    油机无功功率_3
    ${snmp获取值1}    获取SNMP数据_单个    gCPDGReactivePower1value    序号=1
    ${snmp获取值2}    获取SNMP数据_单个    gCPDGReactivePower1value    序号=2
    ${snmp获取值3}    获取SNMP数据_单个    gCPDGReactivePower1value    序号=3
    #
    断开连接SNMP
    should be true    ${获取值1}==267
    should be true    ${获取值2}==234
    should be true    ${获取值3}==200
    should be true    ${snmp获取值1}==267
    should be true    ${snmp获取值2}==234
    should be true    ${snmp获取值3}==200

snmp_0018_油机转速√
    [Documentation]    def,min,max:0,0,5000
    连接CSU
    设置子工具值    oileng    all    只读    转速    5000
    sleep    1m
    ${获取值1}    获取web实时数据    油机转速
    #
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${snmp获取值1}    获取SNMP数据_单个    gCPDGSpeed1value
    should be true    ${获取值1}==5000
    should be true    ${snmp获取值1}==5000
    设置子工具值    oileng    all    只读    转速    0
    sleep    1m
    ${获取值1}    获取web实时数据    油机转速
    ${snmp获取值1}    获取SNMP数据_单个    gCPDGSpeed1value
    should be true    ${获取值1}==0
    should be true    ${snmp获取值1}==0
    设置子工具值    oileng    all    只读    转速    180
    sleep    1m
    ${获取值1}    获取web实时数据    油机转速
    ${snmp获取值1}    获取SNMP数据_单个    gCPDGSpeed1value
    断开连接SNMP
    should be true    ${获取值1}==180
    should be true    ${snmp获取值1}==180

snmp_0020_油机油压√
    [Documentation]    def,min,max:0,0,5000
    连接CSU
    设置子工具值    oileng    all    只读    油压    5600
    sleep    1m
    ${获取值1}    获取web实时数据    油机油压
    #
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${snmp获取值1}    获取SNMP数据_单个    gCPDGOilPressure1value
    should be true    ${获取值1}==5600
    should be true    ${snmp获取值1}==5600
    设置子工具值    oileng    all    只读    油压    1800
    sleep    1m
    ${获取值1}    获取web实时数据    油机油压
    ${snmp获取值1}    获取SNMP数据_单个    gCPDGOilPressure1value
    should be true    ${获取值1}==1800
    should be true    ${snmp获取值1}==1800
    设置子工具值    oileng    all    只读    油压    67
    sleep    1m
    ${获取值1}    获取web实时数据    油机油压
    ${snmp获取值1}    获取SNMP数据_单个    gCPDGOilPressure1value
    断开连接SNMP
    should be true    ${获取值1}==67
    should be true    ${snmp获取值1}==67

snmp_0022_油机水温√
    [Documentation]    def,min,max:0,0,100
    连接CSU
    设置子工具值    oileng    all    只读    水温    100
    sleep    1m
    ${获取值1}    获取web实时数据    油机水温
    #
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${snmp获取值1}    获取SNMP数据_单个    gCPDGCoolantTemperature1value
    should be true    ${获取值1}==100
    should be true    ${snmp获取值1}==100
    设置子工具值    oileng    all    只读    水温    0
    sleep    1m
    ${获取值1}    获取web实时数据    油机水温
    ${snmp获取值1}    获取SNMP数据_单个    gCPDGCoolantTemperature1value
    should be true    ${获取值1}==0
    should be true    ${snmp获取值1}==0
    设置子工具值    oileng    all    只读    水温    28
    sleep    1m
    ${获取值1}    获取web实时数据    油机水温
    ${snmp获取值1}    获取SNMP数据_单个    gCPDGCoolantTemperature1value
    断开连接SNMP
    should be true    ${获取值1}==28
    should be true    ${snmp获取值1}==28

snmp_0024_油机电池电压√
    [Documentation]    def,min,max:12,0,60
    连接CSU
    设置子工具值    oileng    all    只读    电池电压    60
    sleep    1m
    ${获取值1}    获取web实时数据    油机电池电压
    #
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${snmp获取值1}    获取SNMP数据_单个    gCPDGBatteryVoltage1value
    should be true    ${获取值1}==60
    should be true    ${snmp获取值1}==60
    设置子工具值    oileng    all    只读    电池电压    0
    sleep    1m
    ${获取值1}    获取web实时数据    油机电池电压
    ${snmp获取值1}    获取SNMP数据_单个    gCPDGBatteryVoltage1value
    should be true    ${获取值1}==0
    should be true    ${snmp获取值1}==0
    设置子工具值    oileng    all    只读    电池电压    12
    sleep    1m
    ${获取值1}    获取web实时数据    油机电池电压
    ${snmp获取值1}    获取SNMP数据_单个    gCPDGBatteryVoltage1value
    断开连接SNMP
    should be true    ${获取值1}==12
    should be true    ${snmp获取值1}==12

snmp_0026_油机带载率_无
    [Tags]    3
    Log    子工具无，暂时无法自动化
    Log    后续若有需补充用例

snmp_0028_油机油温_无
    [Tags]    3
    Log    子工具无，暂时无法自动化
    Log    后续若有需补充用例

snmp_0030_油机油位_无
    [Tags]    3
    Log    子工具无，暂时无法自动化
    Log    后续若有需补充用例

snmp_0032_油机持续运行时间_无
    [Tags]    3
    Log    子工具无，暂时无法自动化
    Log    后续若有需补充用例
