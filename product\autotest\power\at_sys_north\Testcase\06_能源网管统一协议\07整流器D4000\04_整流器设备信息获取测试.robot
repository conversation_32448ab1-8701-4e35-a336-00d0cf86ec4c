*** Settings ***
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_批量获取整流器设备信息
    写入CSV文档    整流器设备信息获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string32    INDEX_DEVICE_CH=整流器
    ${排除列表}    create list
    @{信号名称列表1}    create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除设备版本信息}    ${排除列表}    1    ${模拟整流器地址}
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    整流器    device info    null
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    FOR    ${i}    IN    @{power_sm待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
		Append To List       ${信号名称列表1}    ${dict}
    END
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    SMR4000   ${信号名称列表1}    版本    V99.23    整流器设备信息获取测试    字符    获取整流器设备信息    
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    SMR4000   ${信号名称列表1}   版本    V10.10    整流器设备信息获取测试    字符    获取整流器设备信息    
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    SMR4000   ${信号名称列表1}   版本    V1.81    整流器设备信息获取测试    字符    获取整流器设备信息   
    

    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string32    INDEX_DEVICE_CH=整流器
    ${排除列表}    create list
    @{信号名称列表2}    create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除设备名称信息}    ${排除列表}    1    ${模拟整流器地址}
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    整流器    device info    null
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    FOR    ${i}    IN    @{power_sm待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
		Append To List       ${信号名称列表2}    ${dict}
    END
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    SMR4000    ${信号名称列表2}    版本    V99.23    整流器设备信息获取测试    字符    获取整流器设备信息    
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    SMR4000    ${信号名称列表2}    版本    V10.10    整流器设备信息获取测试    字符    获取整流器设备信息   
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    SMR4000    ${信号名称列表2}    版本    V1.81    整流器设备信息获取测试    字符    获取整流器设备信息    
    
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=float    INDEX_DEVICE_CH=整流器
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    1    ${模拟整流器地址}
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    整流器    device info    null
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    1    power_sm
    Run Keyword And Continue On Failure    南向子设备厂家信息列表封装判断结果    power_sm    SMR4000    ${缺省值列表}    版本     整流器设备信息获取测试    数值    获取整流器设备信息   
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    2    power_sm
    Run Keyword And Continue On Failure    南向子设备厂家信息列表封装判断结果    power_sm    SMR4000    ${缺省值列表}    版本     整流器设备信息获取测试    数值    获取整流器设备信息    
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    0    power_sm
    Run Keyword And Continue On Failure    南向子设备厂家信息列表封装判断结果    power_sm    SMR4000    ${缺省值列表}    版本      整流器设备信息获取测试    数值    获取整流器设备信息    

    
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=date    INDEX_DEVICE_CH=整流器
    ${排除列表}    create list
    @{信号名称列表3}    create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除设备日期信息}    ${排除列表}    1    ${模拟整流器地址}
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    整流器    device info    ${整流器排除设备日期信息}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    FOR    ${i}    IN    @{power_sm待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
		Append To List       ${信号名称列表3}    ${dict}
    END
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    SMR4000    ${信号名称列表3}     版本    2018-11-15    整流器设备信息获取测试    字符    获取整流器设备信息    
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    SMR4000    ${信号名称列表3}     版本    2021-08-23    整流器设备信息获取测试    字符    获取整流器设备信息    
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    SMR4000    ${信号名称列表3}     版本    2018-07-28    整流器设备信息获取测试    字符    获取整流器设备信息    
    
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=int    INDEX_DEVICE_CH=整流器
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    1    ${模拟整流器地址}
    ${排除列表}    create list
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    整流器    device info    null
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    1    power_sm
    Run Keyword And Continue On Failure    南向子设备厂家信息列表封装判断结果    power_sm    SMR4000    ${缺省值列表}    版本       整流器设备信息获取测试    数值    获取整流器设备信息    
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    2    power_sm
    Run Keyword And Continue On Failure    南向子设备厂家信息列表封装判断结果    power_sm    SMR4000    ${缺省值列表}    版本       整流器设备信息获取测试    数值    获取整流器设备信息    
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    0    power_sm
    Run Keyword And Continue On Failure    南向子设备厂家信息列表封装判断结果    power_sm    SMR4000    ${缺省值列表}    版本       整流器设备信息获取测试    数值    获取整流器设备信息    
    

power_sm_0002_整流器条码
    连接CSU
    设置子工具值    SMR4000    all    资产管理信息    条码3    12522
    设置子工具值    SMR4000    all    资产管理信息    条码4    50416
    设置子工具值    SMR4000    all    资产管理信息    条码5g    178
    @{SMR随机list}    从1-15个数中随机选n个不重复的单体    ${北向协议SMR最大数}    10    ${模拟整流器地址}
    FOR    ${SMR序号}    IN    @{SMR随机list}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<整流器条码-${SMR序号}~0x70010800b0001>>    210097205426
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取整流器设备信息    整流器条码-${SMR序号}    ${SSH}
        should be equal as strings    ${power_sm获取值1}    210097205426
    END

power_sm_0003_生产日期
    连接CSU
    #子设备工具模拟
    设置子工具值    SMR4000    all    资产管理信息    生产日期年5d6g    2020
    设置子工具值    SMR4000    all    资产管理信息    生产日期月6d    11
    设置子工具值    SMR4000    all    资产管理信息    生产日期日7g    19
    Comment    sleep    2m
    @{SMR随机list}    从1-15个数中随机选n个不重复的单体    ${北向协议SMR最大数}    10    ${模拟整流器地址}
    FOR    ${SMR序号}    IN    @{SMR随机list}
        Comment    ${web1}    获取web实时数据    <<生产日期-${SMR序号}~0x70010800c0001>>
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<生产日期-${SMR序号}~0x70010800c0001>>    2020-11-19
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取整流器设备信息    生产日期-${SMR序号}    ${SSH}
        Comment    should be equal as strings    ${web1}    2020-11-19
        should be equal as strings    ${power_sm获取值1}    2020-11-19
    END
    设置子工具值    SMR4000    all    资产管理信息    生产日期年5d6g    2021
    设置子工具值    SMR4000    all    资产管理信息    生产日期月6d    8
    设置子工具值    SMR4000    all    资产管理信息    生产日期日7g    8
    Comment    sleep    2m
    FOR    ${SMR序号}    IN    @{SMR随机list}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<生产日期-${SMR序号}~0x70010800c0001>>    2021-08-08
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取整流器设备信息    生产日期-${SMR序号}    ${SSH}
        Comment    should be equal as strings    ${web1}    2021-08-08
        should be equal as strings    ${power_sm获取值1}    2021-08-08
    END

pm_sm_0004_整流器ID
    连接CSU
    ${工作整流器地址}    获取工作整流器地址
    @{SMR随机list}    从1-15个数中随机选n个不重复的单体    ${北向协议SMR最大数}    3    ${模拟整流器地址}
    FOR    ${SMR序号}    IN    @{SMR随机list}
        ${SMR地址}    Evaluate    ${SMR序号}-${模拟整流器地址}+1
        ${SMR地址}    Convert To String    ${SMR地址}
    #序列号4294967295
        ${SMR序列号}    Set Variable    4294967295
        ${SMR序列号}    Convert To String    ${SMR序列号}
        设置子工具值    SMR4000    ${SMR地址}    版本    SMR序列号    ${SMR序列号}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<整流器ID-${SMR序号}~0x7001080010001>>    ${SMR序列号}
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取整流器设备信息    整流器ID-${SMR序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    ${SMR序列号}
    #序列号2147483646
        ${SMR序列号}    Set Variable    2147483646
        ${SMR序列号}    Convert To String    ${SMR序列号}
        设置子工具值    SMR4000    ${SMR地址}    版本    SMR序列号    ${SMR序列号}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<整流器ID-${SMR序号}~0x7001080010001>>    ${SMR序列号}
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取整流器设备信息    整流器ID-${SMR序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    ${SMR序列号}
    #序列号2415919102
        ${SMR序列号}    Set Variable    2415919102
        ${SMR序列号}    Convert To String    ${SMR序列号}
        设置子工具值    SMR4000    ${SMR地址}    版本    SMR序列号    ${SMR序列号}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<整流器ID-${SMR序号}~0x7001080010001>>    ${SMR序列号}
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取整流器设备信息    整流器ID-${SMR序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    ${SMR序列号}
    #序列号-0
        ${SMR序列号}    Set Variable    0
        ${SMR序列号}    Convert To String    ${SMR序列号}
        设置子工具值    SMR4000    ${SMR地址}    版本    SMR序列号    ${SMR序列号}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<整流器ID-${SMR序号}~0x7001080010001>>    ${SMR序列号}
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取整流器设备信息    整流器ID-${SMR序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    ${SMR序列号}
    #序列号3803448745
        ${SMR序列号}    Set Variable    3803448745
        ${SMR序列号}    Convert To String    ${SMR序列号}
        设置子工具值    SMR4000    ${SMR地址}    版本    SMR序列号    ${SMR序列号}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<整流器ID-${SMR序号}~0x7001080010001>>    ${SMR序列号}
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取整流器设备信息    整流器ID-${SMR序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    ${SMR序列号}
    #序列号3803448752
        ${SMR序列号}    Set Variable    3803448752
        ${SMR序列号}    Convert To String    ${SMR序列号}
        设置子工具值    SMR4000    ${SMR地址}    版本    SMR序列号    ${SMR序列号}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<整流器ID-${SMR序号}~0x7001080010001>>    ${SMR序列号}
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取整流器设备信息    整流器ID-${SMR序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    ${SMR序列号}
        ${SMR序号}    Convert To String    ${SMR序号}
        设置子工具值    SMR4000    ${SMR地址}    版本    SMR序列号    ${SMR序号}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<整流器ID-${SMR序号}~0x7001080010001>>    ${SMR序号}
    END
