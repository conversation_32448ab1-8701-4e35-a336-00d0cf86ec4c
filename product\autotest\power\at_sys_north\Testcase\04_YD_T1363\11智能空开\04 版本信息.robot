*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
智能空开系统名称
    [Documentation]    VZXDU48 FB100B3
    ...    ZTE-smartli
    ...    zte
    连接CSU
    设置子工具值    DMU_IntelAirSwit    all    建链    空开系统名称    VZXDU48 FB100B3
    FOR    ${智能空开序号}    IN RANGE    1    21
        Wait Until Keyword Succeeds    10m    5    查询web信息直到值为    <<智能空开系统名称-${智能空开序号}~0x2c001080010001>>    VZXDU48 FB100B3
        1104/1363_南向子设备字符型/数值型厂家信息    字符    VZXDU48 FB100B3    1363    <<获取智能空开版本信息 (1-20)~CID1=D3H>>    <<获取智能空开版本信息(1-20)~CID2=A3H>>    智能空开系统名称-${智能空开序号}
    END
    FOR    ${智能空开序号}    IN RANGE    21    41
        Wait Until Keyword Succeeds    10m    5    查询web信息直到值为    <<智能空开系统名称-${智能空开序号}~0x2c001080010001>>    VZXDU48 FB100B3
        1104/1363_南向子设备字符型/数值型厂家信息    字符    VZXDU48 FB100B3    1363    <<获取智能空开版本信息 (21-40)~CID1=D3H>>    <<获取智能空开版本信息(21-40)~CID2=A4H>>    智能空开系统名称-${智能空开序号}
    END
    设置子工具值    DMU_IntelAirSwit    all    建链    空开系统名称    zte
    FOR    ${智能空开序号}    IN RANGE    1    21
        Wait Until Keyword Succeeds    10m    5    查询web信息直到值为    <<智能空开系统名称-${智能空开序号}~0x2c001080010001>>    zte
        1104/1363_南向子设备字符型/数值型厂家信息    字符    zte    1363    <<获取智能空开版本信息 (1-20)~CID1=D3H>>    <<获取智能空开版本信息(1-20)~CID2=A3H>>    智能空开系统名称-${智能空开序号}
    END
    FOR    ${智能空开序号}    IN RANGE    21    41
        Wait Until Keyword Succeeds    10m    5    查询web信息直到值为    <<智能空开系统名称-${智能空开序号}~0x2c001080010001>>    zte
        1104/1363_南向子设备字符型/数值型厂家信息    字符    zte    1363    <<获取智能空开版本信息 (21-40)~CID1=D3H>>    <<获取智能空开版本信息(21-40)~CID2=A4H>>    智能空开系统名称-${智能空开序号}
    END

智能空开软件版本
    连接CSU
    设置子工具值    DMU_IntelAirSwit    all    建链    空开软件版本    V99.23
    FOR    ${智能空开序号}    IN RANGE    1    21
        Wait Until Keyword Succeeds    10m    5    查询web信息直到值为    <<智能空开软件版本-${智能空开序号}~0x2c001080020001>>    V99.23
        1104/1363_南向子设备字符型/数值型厂家信息    字符    V99.23    1363    <<获取智能空开版本信息 (1-20)~CID1=D3H>>    <<获取智能空开版本信息(1-20)~CID2=A3H>>    智能空开软件版本-${智能空开序号}
    END
    FOR    ${智能空开序号}    IN RANGE    21    41
        Wait Until Keyword Succeeds    10m    5    查询web信息直到值为    <<智能空开软件版本-${智能空开序号}~0x2c001080020001>>    V99.23
        1104/1363_南向子设备字符型/数值型厂家信息    字符    V99.23    1363    <<获取智能空开版本信息 (21-40)~CID1=D3H>>    <<获取智能空开版本信息(21-40)~CID2=A4H>>    智能空开软件版本-${智能空开序号}
    END
    设置子工具值    DMU_IntelAirSwit    all    建链    空开软件版本    V10.10
    FOR    ${智能空开序号}    IN RANGE    1    21
        Wait Until Keyword Succeeds    10m    5    查询web信息直到值为    <<智能空开软件版本-${智能空开序号}~0x2c001080020001>>    V10.10
        1104/1363_南向子设备字符型/数值型厂家信息    字符    V10.10    1363    <<获取智能空开版本信息 (1-20)~CID1=D3H>>    <<获取智能空开版本信息(1-20)~CID2=A3H>>    智能空开软件版本-${智能空开序号}
    END
    FOR    ${智能空开序号}    IN RANGE    21    41
        Wait Until Keyword Succeeds    10m    5    查询web信息直到值为    <<智能空开软件版本-${智能空开序号}~0x2c001080020001>>    V10.10
        1104/1363_南向子设备字符型/数值型厂家信息    字符    V10.10    1363    <<获取智能空开版本信息 (21-40)~CID1=D3H>>    <<获取智能空开版本信息(21-40)~CID2=A4H>>    智能空开软件版本-${智能空开序号}
    END
    设置子工具值    DMU_IntelAirSwit    all    建链    空开软件版本    V12345
    FOR    ${智能空开序号}    IN RANGE    1    21
        Wait Until Keyword Succeeds    10m    5    查询web信息直到值为    <<智能空开软件版本-${智能空开序号}~0x2c001080020001>>    V12345
        1104/1363_南向子设备字符型/数值型厂家信息    字符    V12345    1363    <<获取智能空开版本信息 (1-20)~CID1=D3H>>    <<获取智能空开版本信息(1-20)~CID2=A3H>>    智能空开软件版本-${智能空开序号}
    END
    FOR    ${智能空开序号}    IN RANGE    21    41
        Wait Until Keyword Succeeds    10m    5    查询web信息直到值为    <<智能空开软件版本-${智能空开序号}~0x2c001080020001>>    V12345
        1104/1363_南向子设备字符型/数值型厂家信息    字符    V12345    1363    <<获取智能空开版本信息 (21-40)~CID1=D3H>>    <<获取智能空开版本信息(21-40)~CID2=A4H>>    智能空开软件版本-${智能空开序号}
    END

智能空开软件发布日期
    [Documentation]    VZXDU48 FB100B3
    ...    ZTE-smartli
    ...    zte
    连接CSU
    设置子工具值    DMU_IntelAirSwit    all    建链    空开软件发布日期    2018-11-15
    FOR    ${智能空开序号}    IN RANGE    1    21
        Wait Until Keyword Succeeds    10m    5    查询web信息直到值为    <<智能空开软件发布日期-${智能空开序号}~0x2c001080030001>>    2018-11-15
        1104/1363_南向子设备字符型/数值型厂家信息    字符    2018-11-15    1363    <<获取智能空开版本信息 (1-20)~CID1=D3H>>    <<获取智能空开版本信息(1-20)~CID2=A3H>>    智能空开软件发布日期-${智能空开序号}
    END
    FOR    ${智能空开序号}    IN RANGE    21    41
        Wait Until Keyword Succeeds    10m    5    查询web信息直到值为    <<智能空开软件发布日期-${智能空开序号}~0x2c001080030001>>    2018-11-15
        1104/1363_南向子设备字符型/数值型厂家信息    字符    2018-11-15    1363    <<获取智能空开版本信息 (21-40)~CID1=D3H>>    <<获取智能空开版本信息(21-40)~CID2=A4H>>    智能空开软件发布日期-${智能空开序号}
    END
    设置子工具值    DMU_IntelAirSwit    all    建链    空开软件发布日期    2021-08-23
    FOR    ${智能空开序号}    IN RANGE    1    21
        Wait Until Keyword Succeeds    10m    5    查询web信息直到值为    <<智能空开软件发布日期-${智能空开序号}~0x2c001080030001>>    2021-08-23
        1104/1363_南向子设备字符型/数值型厂家信息    字符    2021-08-23    1363    <<获取智能空开版本信息 (1-20)~CID1=D3H>>    <<获取智能空开版本信息(1-20)~CID2=A3H>>    智能空开软件发布日期-${智能空开序号}
    END
    FOR    ${智能空开序号}    IN RANGE    21    41
        Wait Until Keyword Succeeds    10m    5    查询web信息直到值为    <<智能空开软件发布日期-${智能空开序号}~0x2c001080030001>>    2021-08-23
        1104/1363_南向子设备字符型/数值型厂家信息    字符    2021-08-23    1363    <<获取智能空开版本信息 (21-40)~CID1=D3H>>    <<获取智能空开版本信息(21-40)~CID2=A4H>>    智能空开软件发布日期-${智能空开序号}
    END
    设置子工具值    DMU_IntelAirSwit    all    建链    空开软件发布日期    2018-10-31
    FOR    ${智能空开序号}    IN RANGE    1    21
        Wait Until Keyword Succeeds    10m    5    查询web信息直到值为    <<智能空开软件发布日期-${智能空开序号}~0x2c001080030001>>    2018-10-31
        1104/1363_南向子设备字符型/数值型厂家信息    字符    2018-10-31    1363    <<获取智能空开版本信息 (1-20)~CID1=D3H>>    <<获取智能空开版本信息(1-20)~CID2=A3H>>    智能空开软件发布日期-${智能空开序号}
    END
    FOR    ${智能空开序号}    IN RANGE    21    41
        Wait Until Keyword Succeeds    10m    5    查询web信息直到值为    <<智能空开软件发布日期-${智能空开序号}~0x2c001080030001>>    2018-10-31
        1104/1363_南向子设备字符型/数值型厂家信息    字符    2018-10-31    1363    <<获取智能空开版本信息 (21-40)~CID1=D3H>>    <<获取智能空开版本信息(21-40)~CID2=A4H>>    智能空开软件发布日期-${智能空开序号}
    END

智能空开序列号X
    [Documentation]    VZXDU48 FB100B3
    ...    ZTE-smartli
    ...    zte
    连接CSU
    
    ${智能空开初始值}    Create Dictionary
    FOR    ${智能空开序号}    IN RANGE    1    41
        ${智能空开地址}    Convert To String    ${智能空开序号}
        FOR    ${次数}    IN RANGE    1    30
            ${初始值}    获取web实时数据    <<智能空开序列号-${智能空开序号}~0x2c001080040001>>
            exit for loop if    '${初始值}'!=''
            sleep    10
        END
        Set To Dictionary    ${智能空开初始值}    ${智能空开地址}    ${初始值}
        ${设置智能空开序号}    Evaluate    int(${智能空开序号})-1
        设置子工具值    DMU_IntelAirSwit    ${智能空开地址}    建链    空开序列号    ${设置智能空开序号}
        设置子工具值    DMU_IntelAirSwit    ${智能空开地址}    建链    空开序列号1    ${设置智能空开序号}
    END
    FOR    ${智能空开序号}    IN RANGE    1    21
        ${智能空开地址}    Convert To String    ${智能空开序号}
        ${设置智能空开序号}    Evaluate    int(${智能空开序号})-1
        ${智能空开序号当前值}    生成智能空开序列号    ${设置智能空开序号}    ${设置智能空开序号}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<智能空开序列号-${智能空开序号}~0x2c001080040001>>    ${智能空开序号当前值}
        1104/1363_南向子设备字符型/数值型厂家信息    字符    ${智能空开序号当前值}    1363    <<获取智能空开版本信息 (1-20)~CID1=D3H>>    <<获取智能空开版本信息(1-20)~CID2=A3H>>    智能空开序列号-${智能空开序号}
        设置子工具值    DMU_IntelAirSwit    ${智能空开地址}    建链    空开序列号    ${智能空开序号}
        设置子工具值    DMU_IntelAirSwit    ${智能空开地址}    建链    空开序列号1    666
    END
    FOR    ${智能空开序号}    IN RANGE    21    41
        ${智能空开地址}    Convert To String    ${智能空开序号}
        ${设置智能空开序号}    Evaluate    int(${智能空开序号})-1
        ${智能空开序号当前值}    生成智能空开序列号    ${设置智能空开序号}    ${设置智能空开序号}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<智能空开序列号-${智能空开序号}~0x2c001080040001>>    ${智能空开序号当前值}
        1104/1363_南向子设备字符型/数值型厂家信息    字符    ${智能空开序号当前值}    1363    <<获取智能空开版本信息 (1-20)~CID1=D3H>>    <<获取智能空开版本信息(1-20)~CID2=A4H>>    智能空开序列号-${智能空开序号}
        设置子工具值    DMU_IntelAirSwit    ${智能空开地址}    建链    空开序列号    ${智能空开序号}
        设置子工具值    DMU_IntelAirSwit    ${智能空开地址}    建链    空开序列号1    666
    END
    FOR    ${智能空开序号}    IN RANGE    1    21
        ${智能空开地址}    Convert To String    ${智能空开序号}
        ${初始值}    Get From Dictionary    ${智能空开初始值}    ${智能空开地址}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<智能空开序列号-${智能空开序号}~0x2c001080040001>>    ${初始值}
        1104/1363_南向子设备字符型/数值型厂家信息    字符    ${初始值}    1363    <<获取智能空开版本信息 (1-20)~CID1=D3H>>    <<获取智能空开版本信息(1-20)~CID2=A3H>>    智能空开序列号-${智能空开序号}
    END
    FOR    ${智能空开序号}    IN RANGE    21    41
        ${智能空开地址}    Convert To String    ${智能空开序号}
        ${初始值}    Get From Dictionary    ${智能空开初始值}    ${智能空开地址}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<智能空开序列号-${智能空开序号}~0x2c001080040001>>    ${初始值}
        1104/1363_南向子设备字符型/数值型厂家信息    字符    ${初始值}    1363    <<获取智能空开版本信息 (1-20)~CID1=D3H>>    <<获取智能空开版本信息(1-20)~CID2=A4H>>    智能空开序列号-${智能空开序号}
    END

智能空开额定电流
    连接CSU
    ${缺省值}    获取web参数上下限范围    <<智能空开额定电流-1~0x2c001080050001>>
    设置子工具值    DMU_IntelAirSwit    all    建链    空开额定电流    ${缺省值}[1]
    FOR    ${智能空开序号}    IN RANGE    1    21
        Wait Until Keyword Succeeds    10m    5    查询web信息直到值为    <<智能空开额定电流-${智能空开序号}~0x2c001080050001>>    ${缺省值}[1]
        1104/1363_南向子设备字符型/数值型厂家信息    数值    ${缺省值}[1]    1363    <<获取智能空开版本信息 (1-20)~CID1=D3H>>    <<获取智能空开版本信息(1-20)~CID2=A3H>>    智能空开额定电流-${智能空开序号}
    END
    FOR    ${智能空开序号}    IN RANGE    21    41
        Wait Until Keyword Succeeds    10m    5    查询web信息直到值为    <<智能空开额定电流-${智能空开序号}~0x2c001080050001>>    ${缺省值}[1]
        1104/1363_南向子设备字符型/数值型厂家信息    数值    ${缺省值}[1]    1363    <<获取智能空开版本信息 (21-40)~CID1=D3H>>    <<获取智能空开版本信息(21-40)~CID2=A4H>>    智能空开额定电流-${智能空开序号}
    END
    设置子工具值    DMU_IntelAirSwit    all    建链    空开额定电流    ${缺省值}[2]
    FOR    ${智能空开序号}    IN RANGE    1    21
        Wait Until Keyword Succeeds    10m    5    查询web信息直到值为    <<智能空开额定电流-${智能空开序号}~0x2c001080050001>>    ${缺省值}[2]
        1104/1363_南向子设备字符型/数值型厂家信息    数值    ${缺省值}[2]    1363    <<获取智能空开版本信息 (1-20)~CID1=D3H>>    <<获取智能空开版本信息(1-20)~CID2=A3H>>    智能空开额定电流-${智能空开序号}
    END
    FOR    ${智能空开序号}    IN RANGE    21    41
        Wait Until Keyword Succeeds    10m    5    查询web信息直到值为    <<智能空开额定电流-${智能空开序号}~0x2c001080050001>>    ${缺省值}[2]
        1104/1363_南向子设备字符型/数值型厂家信息    数值    ${缺省值}[2]    1363    <<获取智能空开版本信息 (21-40)~CID1=D3H>>    <<获取智能空开版本信息(21-40)~CID2=A4H>>    智能空开额定电流-${智能空开序号}
    END
    设置子工具值    DMU_IntelAirSwit    all    建链    空开额定电流    ${缺省值}[0]
    FOR    ${智能空开序号}    IN RANGE    1    21
        Wait Until Keyword Succeeds    10m    5    查询web信息直到值为    <<智能空开额定电流-${智能空开序号}~0x2c001080050001>>    ${缺省值}[0]
        1104/1363_南向子设备字符型/数值型厂家信息    数值    ${缺省值}[0]    1363    <<获取智能空开版本信息 (1-20)~CID1=D3H>>    <<获取智能空开版本信息(1-20)~CID2=A3H>>    智能空开额定电流-${智能空开序号}
    END
    FOR    ${智能空开序号}    IN RANGE    21    41
        Wait Until Keyword Succeeds    10m    5    查询web信息直到值为    <<智能空开额定电流-${智能空开序号}~0x2c001080050001>>    ${缺省值}[0]
        1104/1363_南向子设备字符型/数值型厂家信息    数值    ${缺省值}[0]    1363    <<获取智能空开版本信息 (21-40)~CID1=D3H>>    <<获取智能空开版本信息(21-40)~CID2=A4H>>    智能空开额定电流-${智能空开序号}
    END
    [Teardown]    设置子工具值    DMU_IntelAirSwit    all    建链    空开额定电流    125
