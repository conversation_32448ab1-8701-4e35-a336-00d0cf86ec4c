*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
永久非节能启动浮充
    
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池充电电流系数    0.6
    Wait Until Keyword Succeeds    1m    1    设置web控制量    永久非节能控制
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    永久非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    浮充
    ${设置值}    获取web参数量    浮充电压
    Wait Until Keyword Succeeds    5m    2    直流电压及电池电压在指定范围内    ${设置值}
    # ${上限}    evaluate    ${设置值}+0.3
    # ${下限}    evaluate    ${设置值}-0.3
    # sleep    5m
    # ${获取数据}    获取web实时数据    直流电压
    # ${获取数据_bat}    获取web实时数据    电池电压-1
    # should be true    ${下限}<=${获取数据}<=${上限}
    # should be true    -0.5<${获取数据}-${获取数据_bat}<0.5
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    永久非节能
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池充电电流系数    0.15
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    [Teardown]    设置web设备参数量为默认值    整流器最小开机数量    交流节能模式    电池充电电流系数

永久非节能启动均充
    
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池充电电流系数    0.6
    Wait Until Keyword Succeeds    1m    1    设置web控制量    永久非节能控制
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    永久非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动均充
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    均充
    ${设置值}    获取web参数量    均充电压
    # ${上限}    evaluate    ${设置值}+0.5
    # ${下限}    evaluate    ${设置值}-0.5
    设置电池模拟器电压模式    铅酸电池    1    1    ${设置值}
    Wait Until Keyword Succeeds    5m    2    直流电压及电池电压在指定范围内    ${设置值}
    # sleep    5m
    # ${获取数据}    获取web实时数据    直流电压
    # ${获取数据_bat}    获取web实时数据    电池电压-1
    # should be true    ${下限}<=${获取数据}<=${上限}
    # should be true    -0.5<${获取数据}-${获取数据_bat}<0.5
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    永久非节能
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池充电电流系数    0.15
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    浮充
    ${设置值}    获取web参数量    浮充电压
    设置电池模拟器电压模式    铅酸电池    1    1    ${设置值}
    sleep    5m
    [Teardown]    Run keywords    设置web设备参数量为默认值    整流器最小开机数量    交流节能模式    电池充电电流系数
    ...    AND    重置电池模拟器输出

永久非节能启动测试（测试正常退出）
    
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    10    1    设置web参数量    测试最长时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    Wait Until Keyword Succeeds    1m    1    设置web控制量    永久非节能控制
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    永久非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动测试
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    测试
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    永久非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    4m    1    信号量数据值不为    电池管理状态    测试
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    永久非节能
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    均充
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3
    ...    AND    设置web设备参数量为默认值    测试最长时间

永久非节能启动测试（测试异常退出）
    
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    Wait Until Keyword Succeeds    1m    1    设置web控制量    永久非节能控制
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    永久非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动测试
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    测试
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    永久非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    关闭交流源输出
    Wait Until Keyword Succeeds    4m    1    信号量数据值不为    电池管理状态    测试
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    永久非节能
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    工作整流器数量    0
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    系统停电
    打开交流源输出
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    永久非节能
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    [Teardown]    Run keywords    设置web设备参数量为默认值    整流器最小开机数量    交流节能模式
    ...    AND    打开交流源输出

永久非节能启动电池检测
    
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    Wait Until Keyword Succeeds    1m    1    设置web控制量    永久非节能控制
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    永久非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    永久非节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    [Teardown]    设置web设备参数量为默认值    整流器最小开机数量    交流节能模式

永久非节能开启所有整流器
    
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    Wait Until Keyword Succeeds    1m    1    设置web控制量    永久非节能控制
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    永久非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    [Teardown]    设置web设备参数量为默认值    整流器最小开机数量    交流节能模式

永久非节能不轮换整流器测试
    
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    Wait Until Keyword Succeeds    1m    1    设置web控制量    永久非节能控制
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    永久非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    ${下次轮换时间}    获取web实时数据    下次轮换时间
    ${起始时间}    获取系统时间
    ${设置轮换时间}    Subtract Time From Date    ${起始时间}    50s    #比16:00:00差5s
    设置系统时间    ${设置轮换时间}
    sleep    5m
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    ${下次轮换时间1}    获取web实时数据    下次轮换时间
    should be equal    ${下次轮换时间}    ${下次轮换时间1}
    [Teardown]    设置web设备参数量为默认值    整流器最小开机数量    交流节能模式
