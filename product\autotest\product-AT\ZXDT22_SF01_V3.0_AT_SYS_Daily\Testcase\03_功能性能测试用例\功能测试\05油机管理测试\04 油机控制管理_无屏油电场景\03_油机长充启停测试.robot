*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
油机长充启动条件测试（无市电长充时刻到达）
    [Setup]    重置电池模拟器输出
    油机管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流输入场景    油电
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池应用场景    循环场景
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动电压使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动SOC使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动时间使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动系统异常使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机定时启动使能    禁止
    ${设置值}    获取web参数量    油机启动电压
    Comment    ${缺省值}    获取web参数上下限范围    长充启动周期
    Comment    Wait Until Keyword Succeeds    10    1    设置web参数量    长充启动周期    @{缺省值}[0]
    Comment    ${缺省值}    获取web参数上下限范围    长充最大时长
    Comment    Wait Until Keyword Succeeds    10    1    设置web参数量    长充最大时长    @{缺省值}[1]
    ${长充时长}    获取web参数量    长充最大时长
    ${长充周期}    获取web参数量    长充启动周期
    ${下次长充时间}    获取web实时数据    下次长充时间
    ${下次长充标志}    获取指定量的调测信息    is_full_Chg
    should be true    ${下次长充标志} == 0
    设置负载电压电流    53.5    20
    打开负载输出
    ${电池电流}    获取web实时数据    电池电流-1
    sleep    17m
    ${长充持续时间}    获取web实时数据    长充持续时间
    should be true    ${长充持续时间}== 0
    ${下次长充时间0}    Subtract Time From Date    ${下次长充时间}    5s    #比1:00:00差5s
    Wait Until Keyword Succeeds    10    1    设置系统时间    ${下次长充时间0}
    sleep    10
    ${下次长充标志}    获取指定量的调测信息    is_full_Chg
    should be true    ${下次长充标志} == 1
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    长充状态    否
    Wait Until Keyword Succeeds    4m    1    获取干接点状态直到变化为    ${油机干接点状态}    1    #干接点动作为1（断开），恢复为0（闭合）
    向下调节电池电压    ${设置值}-0.5
    Wait Until Keyword Succeeds    4m    1    获取干接点状态直到变化为    ${油机干接点状态}    0    #干接点动作为1（断开），恢复为0（闭合）
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    运行
    ${长充时间}    evaluate    ${长充时长}+2
    ${状态}    Run Keyword And Return Status    Wait Until Keyword Succeeds    ${长充时间}m    2    信号量数据值为    长充状态    否
    should not be true    ${状态}
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    长充状态    否
    sleep    10
    ${下次长充标志}    获取指定量的调测信息    is_full_Chg
    should be true    ${下次长充标志} == 0
    ${下次长充时间_cal}    add time to date    ${下次长充时间}    ${长充周期}d    exclude_millis=yes
    sleep    5
    ${下次长充时间_new}    获取web实时数据    下次长充时间
    should be equal    ${下次长充时间_new}    ${下次长充时间_cal}
    向上调节电池电压    ${设置值}+1
    Wait Until Keyword Succeeds    2m    1    设置web控制量    油机关闭
    sleep    100
    ${干接点实际是否动作状态1}    获取干接点状态    ${油机干接点状态}
    should be true    ${干接点实际是否动作状态1}==1    #干接点动作为1（断开），恢复为0（闭合）
    关闭交流源输出
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    油机启动电压使能    油机启动时间使能    油机启动SOC使能    油机定时启动使能    长充启动周期    长充最大时长    电池应用场景
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动系统异常使能    允许
    ...    AND    重置电池模拟器输出
    ...    AND    关闭交流源输出

油机长充启动条件测试（下电后长充）
    [Setup]    重置电池模拟器输出
    油机管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流输入场景    油电
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池应用场景    循环场景
    设置web参数量    下电模式    电池电压
    设置web参数量    下电控制延时    0
    设置web参数量    负载一次下电使能    允许
    设置web参数量    负载二次下电使能    允许
    设置web参数量    电池下电使能    允许
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    @{可设置范围}[1]
    ${电压低可设置范围}    获取web参数可设置范围    电池电压低阈值
    设置web参数量    电池电压低阈值    @{电压低可设置范围}[1]
    ${恢复时间可设置范围}    获取web参数可设置范围    一次下电恢复时间
    run keyword and ignore error    设置web参数量    一次下电恢复时间    @{恢复时间可设置范围}[0]
    ${一次下电电压可设置范围}    获取web参数可设置范围    负载一次下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载一次下电电压    @{一次下电电压可设置范围}[1]
    ${二次下电电压可设置范围}    获取web参数可设置范围    负载二次下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电电压    @{二次下电电压可设置范围}[1]
    ${电池下电电压可设置范围}    获取web参数可设置范围    电池下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池下电电压    @{电池下电电压可设置范围}[1]
    ${一次下电电压}    获取web参数量    负载一次下电电压
    ${二次下电电压}    获取web参数量    负载二次下电电压
    ${电池下电电压}    获取web参数量    电池下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动电压使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动SOC使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动时间使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动系统异常使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机定时启动使能    禁止
    ${设置值}    获取web参数量    油机启动电压
    ${缺省值}    获取web参数上下限范围    长充启动周期
    Wait Until Keyword Succeeds    10    1    设置web参数量    长充启动周期    @{缺省值}[0]
    ${缺省值}    获取web参数上下限范围    长充最大时长
    Wait Until Keyword Succeeds    10    1    设置web参数量    长充最大时长    @{缺省值}[0]
    ${长充时长}    获取web参数量    长充最大时长
    ${长充周期}    获取web参数量    长充启动周期
    ${下次长充时间}    获取web实时数据    下次长充时间
    关闭交流源输出
    向下调节电池电压    ${电池下电电压}-0.5
    Wait Until Keyword Succeeds    5m    1    判断告警存在    一次下电告警
    Wait Until Keyword Succeeds    5m    1    判断告警存在    二次下电告警
    Wait Until Keyword Succeeds    5m    1    判断告警存在    电池下电告警
    sleep    10
    ${下次长充标志}    获取指定量的调测信息    is_full_Chg
    should be true    ${下次长充标志} == 1
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    长充状态    否
    ${电池电压}    获取web实时数据    电池电压-1
    run keyword if    ${电池电压} > ${设置值}    向下调节电池电压    ${设置值}-0.5
    sleep    180
    ${干接点实际是否动作状态1}    获取干接点状态    ${油机干接点状态}
    should be true    ${干接点实际是否动作状态1}==0    #干接点动作为1（断开），恢复为0（闭合）
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    油机状态    运行
    ${长充时间}    evaluate    ${长充时长}+2
    ${状态}    Run Keyword And Return Status    Wait Until Keyword Succeeds    ${长充时间}m    2    信号量数据值为    长充状态    否
    should not be true    ${状态}
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    长充状态    否
    sleep    10
    ${下次长充标志}    获取指定量的调测信息    is_full_Chg
    should be true    ${下次长充标志} == 0
    ${下次长充时间_cal}    add time to date    ${下次长充时间}    ${长充周期}d    exclude_millis=yes
    sleep    5
    ${下次长充时间_new}    获取web实时数据    下次长充时间
    should be equal    ${下次长充时间_new}    ${下次长充时间_cal}
    向上调节电池电压    ${设置值}+1
    Wait Until Keyword Succeeds    2m    1    设置web控制量    油机关闭
    sleep    100
    ${干接点实际是否动作状态1}    获取干接点状态    ${油机干接点状态}
    should be true    ${干接点实际是否动作状态1}==1    #干接点动作为1（断开），恢复为0（闭合）
    关闭交流源输出
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    油机启动电压使能    油机启动时间使能    油机启动SOC使能    油机定时启动使能    长充启动周期    长充最大时长    电池应用场景
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动系统异常使能    允许
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    负载一次下电电压    负载二次下电电压    电池下电电压    测试终止电压    电池电压低阈值    一次下电恢复时间
    ...    AND    重置电池模拟器输出
    ...    AND    关闭交流源输出

油机长充启动条件测试（油机启动异常后触发长充）
    [Setup]    重置电池模拟器输出
    油机管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流输入场景    油电
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池应用场景    循环场景
    Wait Until Keyword Succeeds    5m    1    设置web控制量    油机开启
    sleep    20
    ${干接点实际是否动作状态1}    获取干接点状态    ${油机干接点状态}
    should be true    ${干接点实际是否动作状态1}==0    #干接点动作为1（断开），恢复为0（闭合）
    Comment    同时设置三相电压频率    220    50
    Comment    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    5m    1    判断告警存在    油机异常告警
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动电压使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动SOC使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动时间使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动系统异常使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机定时启动使能    禁止
    ${设置值}    获取web参数量    油机启动电压
    ${缺省值}    获取web参数上下限范围    长充启动周期
    Wait Until Keyword Succeeds    10    1    设置web参数量    长充启动周期    @{缺省值}[0]
    ${缺省值}    获取web参数上下限范围    长充最大时长
    Wait Until Keyword Succeeds    10    1    设置web参数量    长充最大时长    @{缺省值}[1]
    ${长充时长}    获取web参数量    长充最大时长
    ${长充周期}    获取web参数量    长充启动周期
    ${下次长充时间}    获取web实时数据    下次长充时间
    sleep    10
    ${下次长充标志}    获取指定量的调测信息    is_full_Chg
    should be true    ${下次长充标志} == 1
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    长充状态    否
    向下调节电池电压    ${设置值}-0.5
    sleep    100
    ${干接点实际是否动作状态1}    获取干接点状态    ${油机干接点状态}
    should be true    ${干接点实际是否动作状态1}==0    #干接点动作为1（断开），恢复为0（闭合）
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    运行
    ${长充时间}    evaluate    ${长充时长}+2
    ${状态}    Run Keyword And Return Status    Wait Until Keyword Succeeds    ${长充时间}m    2    信号量数据值为    长充状态    否
    should not be true    ${状态}
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    长充状态    否
    sleep    10
    ${下次长充标志}    获取指定量的调测信息    is_full_Chg
    should be true    ${下次长充标志} == 0
    ${下次长充时间_cal}    add time to date    ${下次长充时间}    ${长充周期}d    exclude_millis=yes
    sleep    5
    ${下次长充时间_new}    获取web实时数据    下次长充时间
    should be equal    ${下次长充时间_new}    ${下次长充时间_cal}
    向上调节电池电压    ${设置值}+1
    Wait Until Keyword Succeeds    2m    1    设置web控制量    油机关闭
    sleep    100
    ${干接点实际是否动作状态1}    获取干接点状态    ${油机干接点状态}
    should be true    ${干接点实际是否动作状态1}==1    #干接点动作为1（断开），恢复为0（闭合）
    关闭交流源输出
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    油机启动电压使能    油机启动时间使能    油机启动SOC使能    油机定时启动使能    长充启动周期    长充最大时长    电池应用场景
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动系统异常使能    允许
    ...    AND    重置电池模拟器输出
    ...    AND    关闭交流源输出

油机长充退出测试（有市电场景）

油机长充退出测试（无市电场景）
