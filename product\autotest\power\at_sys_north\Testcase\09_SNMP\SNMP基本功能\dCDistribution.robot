*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
snmp_0002_dCDistribution1analogDataX
    ${比较结果}    对比数据_V2C    dCDistribution1analogData
    should be true    ${比较结果}
    Comment    ${比较结果}    对比数据_V3    cSU1analogData
    Comment    should be true    ${比较结果}

snmp_0004_dCDistribution1digitalData√
    [Documentation]    dCDistribution1digitalData
    ${比较结果}    对比数据_V2C    dCDistribution1digitalData
    should be true    ${比较结果}

snmp_0006_dCDistribution1alarmX
    [Documentation]    该用例运行时，需要修改配置文件中的，4个参数
    ...
    ...
    ...    <para name="LLVD1 Config">0</para>
    ...    <para name="LLVD2 Config">0</para>
    ...    <para name="BLVD Config">0</para>
    ...    <para name="Subrack Res Integer1">0</para>
    [Tags]    4
    Comment    ${比较结果}    对比告警_V2C    dCDistribution1alarm
    Comment    should be true    ${比较结果}
    Log    dCDistribution1alarm此项在trap中测

snmp_0008_dCDistribution1alarm_level√
    ${比较结果}    对比告警级别_V2C    dCDistribution1alarm
    should be true    ${比较结果}

snmp_0010_dCDistribution1alarm_level_write√
    Comment    ${比较结果}    批量修改告警级别_V2C    battery1alarm
    Comment    should be true    ${比较结果}
    ${比较结果}    批量设置四种/五种告警级别    dCDistribution1alarm
    should be true    ${比较结果}

snmp_0012_dCDistribution1alarm_relay√
    ${比较结果}    对比告警干接点_V2C    dCDistribution1alarm
    should be true    ${比较结果}

snmp_0014_dCDistribution1alarm_relay_write√
    ${比较结果}    批量修改告警干接点_V2C    dCDistribution1alarm
    should be true    ${比较结果}

snmp_0016_dCDistribution1parameterX
    ${比较结果}    对比数据_V2C    dCDistribution1parameter
    should be true    ${比较结果}

snmp_0018_dCDistribution1parameter_writeX
    Comment    ${比较结果}    批量修改参数_V2C    dCDistribution1parameter
    Comment    should be true    ${比较结果}
    ${比较结果}    SNMP批量参数设置    dCDistribution1parameter
    should be true    ${比较结果}
