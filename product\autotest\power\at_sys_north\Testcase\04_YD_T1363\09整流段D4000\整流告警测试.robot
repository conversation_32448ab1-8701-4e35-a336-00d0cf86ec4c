*** Settings ***
Suite Setup
Suite Teardown
Test Setup
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
1363批量获取状态量和SMR故障测试
    [Setup]
    写入CSV文档    整流器数字量和故障量获取测试    信号名称    信号值    结果    备注
    连接CSU
    ${级别设置值}    获取web参数量    整流器故障
    run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    整流器故障    严重
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=整流器
    ${排除列表}    create list
    P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除故障量信号}    ${排除列表}    1    ${模拟整流器地址}    1
    ${列表1}    处理1104/1363SMR或PU设备序号大于40的部分    ${列表1}    ${模拟整流器地址}
    #1363协议中只显示SMR告警/SMR故障/SMR通讯中断
    #所以此处无需从1363表中查找数据，以data_dict得到的为主
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        @{序号}    evaluate    re.split('[-~]','${信号名称}')    re
        ${1363告警名}    set variable    整流器故障
        ${1363告警名}    Catenate    SEPARATOR=    ${1363告警名}    -    ${序号}[1]
        ${实时告警中的设备名称}    主动告警_组合实时告警中的设备名称    整流器    ${信号名称}
        Run Keyword And Continue On Failure    整流器或PU故障量产生封装判断结果    1363    SMR4000    ${信号名称}    数字量    ${告警产生}    ${模拟整流器地址}    整流器故障    整流器数字量和告警量获取测试    ${实时告警中的设备名称}    null    null    <<获取整流段告警量~CID1=41H>>    <<获取整流段告警量~CID2=44H>>    ${1363告警名}
        Run Keyword And Continue On Failure    整流器或PU故障量恢复封装判断结果    1363    SMR4000    ${信号名称}    数字量    ${告警恢复}    ${模拟整流器地址}    整流器故障    整流器数字量和告警量获取测试    null    null    null    <<获取整流段告警量~CID1=41H>>    <<获取整流段告警量~CID2=44H>>    ${1363告警名}
    END

1363批量获取状态量和SMR告警测试
    写入CSV文档    整流器数字量和告警量获取测试    信号名称    信号值    结果    备注
    连接CSU
    ${级别设置值}    获取web参数量    整流器告警
    run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    整流器告警    严重
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=整流器
    ${排除列表}    create list
    P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除告警量信号}    ${排除列表}    1    ${模拟整流器地址}    1
    ${列表1}    处理1104/1363SMR或PU设备序号大于40的部分    ${列表1}    ${模拟整流器地址}
    #1363协议中只显示SMR告警/SMR故障/SMR通讯中断
    #所以此处无需从1363表中查找数据，以data_dict得到的为主
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        @{序号}    evaluate    re.split('[-~]','${信号名称}')    re
        ${1363告警名}    set variable    整流器告警
        ${1363告警名}    Catenate    SEPARATOR=    ${1363告警名}    -    ${序号}[1]
        ${实时告警中的设备名称}    主动告警_组合实时告警中的设备名称    整流器    ${信号名称}
        Run Keyword And Continue On Failure    整流器或PU告警量产生封装判断结果    1363    SMR4000    ${信号名称}    数字量    ${告警产生}    ${模拟整流器地址}    整流器告警    整流器数字量和告警量获取测试    ${实时告警中的设备名称}    null    null    <<获取整流段告警量~CID1=41H>>    <<获取整流段告警量~CID2=44H>>    ${1363告警名}
        Run Keyword And Continue On Failure    整流器或PU告警量恢复封装判断结果    1363    SMR4000    ${信号名称}    数字量    ${告警恢复}    ${模拟整流器地址}    整流器告警    整流器数字量和告警量获取测试    null    null    null    <<获取整流段告警量~CID1=41H>>    <<获取整流段告警量~CID2=44H>>    ${1363告警名}
    END
    [Teardown]

1363_0006_整流器通讯断
    [Setup]    #测试用例前置条件
    连接CSU
    设置子工具个数    SMR4000    0
    ${级别设置值}    获取web参数量    整流器通讯中断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    整流器通讯中断    严重
    ${告警级别取值约定dict}    获取web参数的取值约定    整流器通讯中断
    Log    ${测试环境真实整流个数}
    ${模拟一个时的起始号}    evaluate    ${模拟整流器开始地址}+1
    ${整流中断个数}    evaluate    40-${测试环境真实整流个数}
    @{SMR序号随机list}    从1-15个数中随机选n个不重复的单体    40    3    ${模拟整流器开始地址}
    FOR    ${SMR序号}    IN    @{SMR序号随机list}
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    整流器通讯状态-${SMR序号}    异常
        wait until keyword succeeds    2m    1    判断告警存在    整流器通讯中断-${SMR序号}
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取整流段告警量~CID1=41H>>    <<获取整流段告警量~CID2=44H>>    整流器通讯断-${SMR序号}    1    None    ${g_ver_1363}
        should be true    ${1104告警结果}
    END
    wait until keyword succeeds    5m    2    查询指定告警信息    整流器通讯中断
    wait until keyword succeeds    30m    60    获取指定告警数量大于    整流器通讯中断    ${整流中断个数}
    ${SMR通信恢复}    evaluate    str(${北向协议SMR最大数}-${测试环境真实整流个数})
    设置子工具个数    SMR4000    ${SMR通信恢复}
    FOR    ${SMR序号}    IN    @{SMR序号随机list}
        Wait Until Keyword Succeeds    5m    1    信号量数据值为    整流器通讯状态-${SMR序号}    正常
        wait until keyword succeeds    2m    1    判断告警不存在    整流器通讯中断-${SMR序号}
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取整流段告警量~CID1=41H>>    <<获取整流段告警量~CID2=44H>>    整流器通讯断-${SMR序号}    1    None    ${g_ver_1363}
        should not be true    ${1104告警结果}
    END
    wait until keyword succeeds    30m    2    查询指定告警信息不为    整流器通讯中断

1363_整流器风扇转速异常
    连接CSU
    @{SMR序号随机list}    从1-15个数中随机选n个不重复的单体    40    3    ${模拟整流器地址}
    FOR    ${SMR序号}    IN RANGE    @{SMR序号随机list}
        ${整流器地址}    Convert To String    ${SMR序号}
        设置子工具值    SMR    ${整流器地址}    数字量    整流器风扇转速异常    1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    整流器风扇转速异常状态-${SMR序号}    异常
        wait until keyword succeeds    5m    1    判断内部告警存在    整流器风扇转速异常-${SMR序号}
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取整流段告警量~CID1=41H>>    <<获取整流段告警量~CID2=44H>>    整流器风扇转速异常-${SMR序号}    1    None    ${g_ver_1363}
        should be true    ${1104告警结果}
    END
    FOR    ${SMR序号}    IN    @{SMR序号随机list}
        ${整流器地址}    Convert To String    ${SMR序号}
        设置子工具值    SMR    ${整流器地址}    数字量    整流器风扇转速异常    0
        Wait Until Keyword Succeeds    5m    1    信号量数据值为    整流器风扇转速异常状态-${SMR序号}    正常
        wait until keyword succeeds    5m    1    判断内部告警不存在    整流器风扇转速异常-${SMR序号}
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取整流段告警量~CID1=41H>>    <<获取整流段告警量~CID2=44H>>    整流器风扇转速异常-${SMR序号}    1    None    ${g_ver_1363}
        should not be true    ${1104告警结果}
    END

1363_整流器连接器温度高
    连接CSU
    @{SMR序号随机list}    从1-15个数中随机选n个不重复的单体    40    3    ${模拟整流器地址}
    FOR    ${SMR序号}    IN RANGE    @{SMR序号随机list}
        ${整流器地址}    Convert To String    ${SMR序号}
        设置子工具值    SMR    ${整流器地址}    数字量    整流器连接器温度高    1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    整流器连接器温度高状态-${SMR序号}    异常
        wait until keyword succeeds    5m    1    判断内部告警存在    整流器连接器温度高-${SMR序号}
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取整流段告警量~CID1=41H>>    <<获取整流段告警量~CID2=44H>>    整流器连接器温度高-${SMR序号}    1    None    ${g_ver_1363}
        should be true    ${1104告警结果}
    END
    FOR    ${SMR序号}    IN    @{SMR序号随机list}
        ${整流器地址}    Convert To String    ${SMR序号}
        设置子工具值    SMR    ${整流器地址}    数字量    整流器连接器温度高    0
        Wait Until Keyword Succeeds    5m    1    信号量数据值为    整流器连接器温度高状态-${SMR序号}    正常
        wait until keyword succeeds    5m    1    判断内部告警不存在    整流器连接器温度高-${SMR序号}
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取整流段告警量~CID1=41H>>    <<获取整流段告警量~CID2=44H>>    整流器连接器温度高-${SMR序号}    1    None    ${g_ver_1363}
        should not be true    ${1104告警结果}
    END
