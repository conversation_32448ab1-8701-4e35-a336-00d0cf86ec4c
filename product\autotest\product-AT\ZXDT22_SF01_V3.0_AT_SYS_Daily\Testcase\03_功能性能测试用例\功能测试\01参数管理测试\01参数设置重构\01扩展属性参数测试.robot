*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
负载一次下电时间设置测试
    [Documentation]    默认45V；小于等于电池电压低阈值-1、测试终止电压-1
    [Setup]    run keywords    判断web参数是否存在    下电模式
    ...    AND    设置web参数量    下电模式    停电时间
    ...    AND    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    负载一次下电使能
    ...    AND    设置web参数量    负载一次下电使能    允许
    ...    AND    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    负载一次下电时间
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    纯铅酸
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    铅酸类型
    ${缺省值}    获取web参数上下限范围    铅酸类型
    ${取值约定dict}    获取web参数的取值约定    铅酸类型
    ${取值约定values}    get dictionary values    ${取值约定dict}
    ${val}    Get From Dictionary    ${取值约定dict}    @{缺省值}[0]
    FOR    ${参数设置}    IN    @{取值约定values}
    Wait Until Keyword Succeeds    10    2    设置web参数量    铅酸类型    ${参数设置}
    sleep    1
    ${参数获取}    获取web参数量    铅酸类型
    should be equal    ${参数获取}    ${参数设置}
    数值类参数设置最大值最小值默认值    负载一次下电时间
    [Teardown]    设置web设备参数量为默认值    负载一次下电时间    负载一次下电使能    下电模式    铅酸类型    电池配置

负载一次下电SOC阈值设置测试
    [Documentation]    铅酸：10~80，默认20；铁锂：10~90，默认15；负载一次下电SOC阈值<=测试终止SOC阈值-1；测试终止SOC阈值<=测试失败SOC阈值；
    [Setup]    Run keywords    判断web参数是否存在    下电模式
    ...    AND    设置web参数量    下电模式    电池剩余容量
    ...    AND    判断web参数是否存在    负载一次下电使能
    ...    AND    设置web参数量    负载一次下电使能    允许
    ...    AND    判断web参数是否存在    负载一次下电SOC阈值
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    纯铅酸
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    铅酸类型
    ${缺省值}    获取web参数上下限范围    铅酸类型
    ${取值约定dict}    获取web参数的取值约定    铅酸类型
    ${取值约定values}    get dictionary values    ${取值约定dict}
    ${val}    Get From Dictionary    ${取值约定dict}    @{缺省值}[0]
    FOR    ${参数设置}    IN    @{取值约定values}    ${val}
    Wait Until Keyword Succeeds    10    2    设置web参数量    铅酸类型    ${参数设置}
    sleep    1
    ${参数获取}    获取web参数量    铅酸类型
    should be equal    ${参数获取}    ${参数设置}
    数值类参数设置最大值最小值默认值    负载一次下电SOC阈值
    [Teardown]    设置web设备参数量为默认值    负载一次下电SOC阈值    负载二次下电SOC阈值    电池下电SOC阈值    测试终止SOC阈值    测试失败SOC阈值    铅酸类型    电池配置

负载二次下电SOC阈值设置测试
    [Documentation]    普通铅酸：10~80，默认10；
    ...    铁锂：5~90，默认5；
    ...    负载二次下电SOC阈值<=测试终止SOC阈值-1；测试终止SOC阈值<=测试失败SOC阈值；
    [Setup]    Run keywords    判断web参数是否存在    下电模式
    ...    AND    设置web参数量    下电模式    电池剩余容量
    ...    AND    判断web参数是否存在    负载二次下电使能
    ...    AND    设置web参数量    负载二次下电使能    允许
    ...    AND    判断web参数是否存在    负载二次下电SOC阈值
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    纯铅酸
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    铅酸类型
    ${缺省值}    获取web参数上下限范围    铅酸类型
    ${取值约定dict}    获取web参数的取值约定    铅酸类型
    ${取值约定values}    get dictionary values    ${取值约定dict}
    ${val}    Get From Dictionary    ${取值约定dict}    @{缺省值}[0]
    FOR    ${参数设置}    IN    @{取值约定values}    ${val}
    Wait Until Keyword Succeeds    10    2    设置web参数量    铅酸类型    ${参数设置}
    sleep    1
    ${参数获取}    获取web参数量    铅酸类型
    should be equal    ${参数获取}    ${参数设置}
    数值类参数设置最大值最小值默认值    负载二次下电SOC阈值
    [Teardown]    设置web设备参数量为默认值    负载一次下电SOC阈值    负载二次下电SOC阈值    电池下电SOC阈值    测试终止SOC阈值    测试失败SOC阈值    铅酸类型    电池配置

负载二次下电时间设置测试
    [Documentation]    普通铅酸：38~49V，默认44V；
    ...    铅酸25: 38~51V，默认45.8V
    ...    铁锂：42~58V，默认46V；
    ...    小于等于电池电压低阈值-1、测试终止电压-1
    [Setup]    run keywords    判断web参数是否存在    下电模式
    ...    AND    设置web参数量    下电模式    停电时间
    ...    AND    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    负载二次下电使能
    ...    AND    设置web参数量    负载二次下电使能    允许
    ...    AND    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    负载二次下电时间
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    纯铅酸
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    铅酸类型
    ${缺省值}    获取web参数上下限范围    铅酸类型
    ${取值约定dict}    获取web参数的取值约定    铅酸类型
    ${取值约定values}    get dictionary values    ${取值约定dict}
    ${val}    Get From Dictionary    ${取值约定dict}    @{缺省值}[0]
    FOR    ${参数设置}    IN    @{取值约定values}
    Wait Until Keyword Succeeds    10    2    设置web参数量    铅酸类型    ${参数设置}
    sleep    1
    ${参数获取}    获取web参数量    铅酸类型
    should be equal    ${参数获取}    ${参数设置}
    数值类参数设置最大值最小值默认值    负载二次下电时间
    [Teardown]    设置web设备参数量为默认值    负载二次下电时间    负载二次下电使能    下电模式    铅酸类型    电池配置

电池下电SOC阈值设置测试
    [Documentation]    10~80，默认10；
    ...    小于测试终止SOC阈值-1
    [Tags]
    [Setup]    Run keywords    判断web参数是否存在    下电模式
    ...    AND    设置web参数量    下电模式    电池剩余容量
    ...    AND    判断web参数是否存在    电池下电使能
    ...    AND    设置web参数量    电池下电使能    允许
    ...    AND    判断web参数是否存在    电池下电SOC阈值
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    纯铅酸
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    铅酸类型
    ${缺省值}    获取web参数上下限范围    铅酸类型
    ${取值约定dict}    获取web参数的取值约定    铅酸类型
    ${取值约定values}    get dictionary values    ${取值约定dict}
    ${val}    Get From Dictionary    ${取值约定dict}    @{缺省值}[0]
    FOR    ${参数设置}    IN    @{取值约定values}    ${val}
    Wait Until Keyword Succeeds    10    2    设置web参数量    铅酸类型    ${参数设置}
    sleep    1
    ${参数获取}    获取web参数量    铅酸类型
    should be equal    ${参数获取}    ${参数设置}
    数值类参数设置最大值最小值默认值    电池下电SOC阈值
    [Teardown]    设置web设备参数量为默认值    负载一次下电SOC阈值    负载二次下电SOC阈值    电池下电SOC阈值    测试终止SOC阈值    测试失败SOC阈值    铅酸类型    电池配置

电池下电时间设置测试
    [Tags]
    [Setup]    run keywords    判断web参数是否存在    下电模式
    ...    AND    设置web参数量    下电模式    停电时间
    ...    AND    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    电池下电使能
    ...    AND    设置web参数量    电池下电使能    允许
    ...    AND    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    电池下电时间
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    纯铅酸
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    铅酸类型
    ${缺省值}    获取web参数上下限范围    铅酸类型
    ${取值约定dict}    获取web参数的取值约定    铅酸类型
    ${取值约定values}    get dictionary values    ${取值约定dict}
    ${val}    Get From Dictionary    ${取值约定dict}    @{缺省值}[0]
    FOR    ${参数设置}    IN    @{取值约定values}
    Wait Until Keyword Succeeds    10    2    设置web参数量    铅酸类型    ${参数设置}
    sleep    1
    ${参数获取}    获取web参数量    铅酸类型
    should be equal    ${参数获取}    ${参数设置}
    数值类参数设置最大值最小值默认值    电池下电时间
    [Teardown]    设置web设备参数量为默认值    电池下电时间    电池下电使能    下电模式    铅酸类型    电池配置

整流器轮换周期设置测试
    [Setup]    run keywords    判断web参数是否存在    交流节能模式
    ...    AND    Wait Until Keyword Succeeds    10    2    设置web参数量    交流节能模式    节能
    ...    AND    判断web参数是否存在    整流器轮换周期
    数值类参数设置最大值最小值默认值    整流器轮换周期
    [Teardown]    设置web设备参数量为默认值    整流器轮换周期    交流节能模式

暂时非节能延时时间设置测试
    [Setup]    run keywords    判断web参数是否存在    交流节能模式
    ...    AND    Wait Until Keyword Succeeds    10    2    设置web参数量    交流节能模式    节能
    ...    AND    判断web参数是否存在    暂时非节能延时时间
    数值类参数设置最大值最小值默认值    暂时非节能延时时间
    [Teardown]    设置web设备参数量为默认值    暂时非节能延时时间    交流节能模式

充电电压设置测试
    [Setup]    Run keywords    判断web参数是否存在    电池配置
    ...    AND    设置web参数量    电池配置    智能锂电
    ...    AND    判断web参数是否存在    充电电压
    数值类参数设置最大值最小值默认值    充电电压
    [Teardown]    设置web设备参数量为默认值    充电电压    电池配置

直流配电单元下电时间设置测试
    [Setup]    run keywords    判断web参数是否存在    下电模式
    ...    AND    设置web参数量    下电模式    停电时间
    ...    AND    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    直流配电单元下电使能
    ...    AND    设置web序列参数    直流配电单元下电使能    允许
    ...    AND    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    直流配电单元下电时间
    ${下电时间数量}    获取web参数的数量    直流配电单元下电时间
    ${缺省值}    获取web参数上下限范围    直流配电单元下电时间
    ${可设置范围}    获取web参数可设置范围    直流配电单元下电时间_1
    ${超下限}    evaluate    @{可设置范围}[0]-@{缺省值}[4]
    ${超上限}    evaluate    @{可设置范围}[1]+@{缺省值}[4]
    FOR    ${i}    IN RANGE    ${下电时间数量}    0    -1
    ${原参数}    Wait Until Keyword Succeeds    30    2    获取web参数量    直流配电单元下电时间_${i}
    #(1)超范围设置不成功
    #超下限
    ${设置结果}    run keyword and return status    设置web参数量    直流配电单元下电时间_${i}    ${超下限}
    should not be true    ${设置结果}
    ${参数获取}    获取web参数量    直流配电单元下电时间_${i}
    should be true    ${参数获取}==${原参数}
    #超上限
    ${设置结果}    run keyword and return status    设置web参数量    直流配电单元下电时间_${i}    ${超上限}
    should not be true    ${设置结果}
    ${参数获取}    获取web参数量    直流配电单元下电时间_${i}
    should be true    ${参数获取}==${原参数}
    #范围内设置成功
    Wait Until Keyword Succeeds    10    2    设置web参数量    直流配电单元下电时间_${i}    @{可设置范围}[0]
    sleep    1
    ${参数获取}    获取web参数量    直流配电单元下电时间_${i}
    should be true    ${参数获取}==@{可设置范围}[0]
    Wait Until Keyword Succeeds    10    2    设置web参数量    直流配电单元下电时间_${i}    @{可设置范围}[1]
    sleep    1
    ${参数获取}    获取web参数量    直流配电单元下电时间_${i}
    should be true    ${参数获取}==@{可设置范围}[1]
    Wait Until Keyword Succeeds    10    2    设置web参数量    直流配电单元下电时间_${i}    @{缺省值}[0]
    sleep    1
    ${参数获取}    获取web参数量    直流配电单元下电时间_${i}
    should be true    ${参数获取}==@{缺省值}[0]
    [Teardown]    设置web设备参数量为默认值    直流配电单元下电时间_1    直流配电单元下电时间_2    直流配电单元下电时间_3    直流配电单元下电使能_1    直流配电单元下电使能_2    直流配电单元下电使能_3    下电模式

直流配电单元下电SOC阈值设置测试
    [Setup]    Run keywords    判断web参数是否存在    下电模式
    ...    AND    设置web参数量    下电模式    电池剩余容量
    ...    AND    判断web参数是否存在    直流配电单元下电使能
    ...    AND    设置web序列参数    直流配电单元下电使能    允许
    ...    AND    判断web参数是否存在    直流配电单元下电SOC阈值
    ${下电SOC数量}    获取web参数的数量    直流配电单元下电SOC阈值
    ${缺省值}    获取web参数上下限范围    直流配电单元下电SOC阈值
    ${可设置范围}    获取web参数可设置范围    直流配电单元下电SOC阈值_1
    ${超下限}    evaluate    @{可设置范围}[0]-@{缺省值}[4]
    ${超上限}    evaluate    @{可设置范围}[1]+@{缺省值}[4]
    FOR    ${i}    IN RANGE    ${下电SOC数量}    0    -1
    ${原参数}    Wait Until Keyword Succeeds    30    2    获取web参数量    直流配电单元下电SOC阈值_${i}
    #(1)超范围设置不成功
    #超下限
    ${设置结果}    run keyword and return status    设置web参数量    直流配电单元下电SOC阈值_${i}    ${超下限}
    should not be true    ${设置结果}
    ${参数获取}    获取web参数量    直流配电单元下电SOC阈值_${i}
    should be true    ${参数获取}==${原参数}
    #超上限
    ${设置结果}    run keyword and return status    设置web参数量    直流配电单元下电SOC阈值_${i}    ${超上限}
    should not be true    ${设置结果}
    ${参数获取}    获取web参数量    直流配电单元下电SOC阈值_${i}
    should be true    ${参数获取}==${原参数}
    #范围内设置成功
    Wait Until Keyword Succeeds    10    2    设置web参数量    直流配电单元下电SOC阈值_${i}    @{可设置范围}[0]
    sleep    1
    ${参数获取}    获取web参数量    直流配电单元下电SOC阈值_${i}
    should be true    ${参数获取}==@{可设置范围}[0]
    Wait Until Keyword Succeeds    10    2    设置web参数量    直流配电单元下电SOC阈值_${i}    @{可设置范围}[1]
    sleep    1
    ${参数获取}    获取web参数量    直流配电单元下电SOC阈值_${i}
    should be true    ${参数获取}==@{可设置范围}[1]
    Wait Until Keyword Succeeds    10    2    设置web参数量    直流配电单元下电SOC阈值_${i}    @{缺省值}[0]
    sleep    1
    ${参数获取}    获取web参数量    直流配电单元下电SOC阈值_${i}
    should be true    ${参数获取}==@{缺省值}[0]
    [Teardown]    设置web设备参数量为默认值    直流配电单元下电SOC阈值_1    直流配电单元下电SOC阈值_2    直流配电单元下电SOC阈值_3    直流配电单元下电使能_1    直流配电单元下电使能_2    直流配电单元下电使能_3    下电模式
