*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
批量设置参数测试
    写入CSV文档    智能空开参数测试    参数名称    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=parameter    INDEX_DEVICE_CH=智能空开    INDEX_DATA_TYPE=int
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SSW排除参数量设置}    ${排除列表}    2
    @{取值列表1F}    create list
    @{取值列表1T}    create list
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${取值约定}    Get From Dictionary    ${i}    convention
        ${设备名称}    Get From Dictionary    ${i}    device_name
        ${dict1}          Create Dictionary
        Set To Dictionary    ${dict1}     name     ${信号名称}
        Set To Dictionary    ${dict1}     value    ${取值约定}
        Set To Dictionary    ${dict1}     devi_name    ${设备名称}
        run keyword if    '${取值约定}'=='False'  Append To List       ${取值列表1F}    ${dict1}
        run keyword if    '${取值约定}'=='True'    Append To List      ${取值列表1T}    ${dict1}
    END
    Run Keyword And Continue On Failure    南向子设备数值型列表参数设置封装判断结果         DMU_IntelAirSwit     ${取值列表1F}    批量查询私有参数    智能空开参数测试
    Run Keyword And Continue On Failure    南向子设备有取值约定型列表参数设置封装判断结果    DMU_IntelAirSwit     ${取值列表1T}    批量查询私有参数    智能空开参数测试
	
	
	@{参数列表}    create list    INDEX_SIGNAL_TYPE=parameter    INDEX_DEVICE_CH=智能空开    INDEX_DATA_TYPE=float
    ${排除列表}    create list
    ${列表2}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    2
    @{取值列表2F}    create list
    @{取值列表2T}    create list
    FOR    ${i}    IN    @{列表2}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${取值约定}    Get From Dictionary    ${i}    convention
        ${设备名称}    Get From Dictionary    ${i}    device_name
        ${dict2}          Create Dictionary
        Set To Dictionary    ${dict2}     name     ${信号名称}
        Set To Dictionary    ${dict2}     value    ${取值约定}
        Set To Dictionary    ${dict2}     devi_name    ${设备名称}
        run keyword if    '${取值约定}'=='False'  Append To List       ${取值列表2F}    ${dict2}
        run keyword if    '${取值约定}'=='True'    Append To List      ${取值列表2T}    ${dict2}
    END
    Run Keyword And Continue On Failure    南向子设备数值型列表参数设置封装判断结果         DMU_IntelAirSwit    ${取值列表2F}    批量查询私有参数    智能空开参数测试
    Run Keyword And Continue On Failure    南向子设备有取值约定型列表参数设置封装判断结果    DMU_IntelAirSwit    ${取值列表2T}    批量查询私有参数    智能空开参数测试


智能空开下电电压阈值设置测试
    连接CSU
	Wait Until Keyword Succeeds    10    1    设置web参数量    智能空开下电使能    允许
    判断web带ID的参数是否存在    智能空开下电电压阈值_1
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    智能空开下电电压阈值_1
    ${缺省值}    获取web参数上下限范围    智能空开下电电压阈值_1
    ${可设置范围}    获取web参数可设置范围    智能空开下电电压阈值_1
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    智能空开下电电压阈值_1    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    智能空开下电电压阈值_1
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        ${设置结果}    run keyword and return status    设置web参数量    智能空开下电电压阈值_1    ${参数设置}
        ${参数获取}    获取web参数量    智能空开下电电压阈值_1
        should be true    ${参数获取}==${参数设置}
    END

智能空开上电电压设置测试
    连接CSU
    判断web带ID的参数是否存在    智能空开上电电压_1
	Wait Until Keyword Succeeds    10    1    设置web参数量    智能空开下电使能    允许
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    智能空开上电电压_1
    ${缺省值}    获取web参数上下限范围    智能空开上电电压_1
    ${可设置范围}    获取web参数可设置范围    智能空开上电电压_1
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    智能空开上电电压_1    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    智能空开上电电压_1
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        ${设置结果}    run keyword and return status    设置web参数量    智能空开上电电压_1    ${参数设置}
        ${参数获取}    获取web参数量    智能空开上电电压_1
        should be true    ${参数获取}==${参数设置}
    END
