*** Settings ***
Force Tags
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp_test关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/Web关键字_V30.robot
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
# snmp批量获取SMR数字量
#     [Documentation]    21min
#     写入CSV文档    整流器数字量获取测试    信号名称    信号值    结果    备注
#     连接CSU
#     进行SNMP_V2/V3连接    ${snmp连接方式}
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=digital data    INDEX_DEVICE_CH=整流器
#     ${排除列表}    create list
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除数字量信号}    ${排除列表}    2    ${模拟整流器开始地址}
#     ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    rectifier    digitalData
#     Comment    ${指定数据1}    Create Dictionary    signal_name    <<整流器风扇控制状态-4~0x70010200a0001>>    convention    False    device_name    整流器    data_type    int    unit    None    snmp_name    sMRFanControlState4value    signal_node    sMRFanControlState4value
#     ...    Vindex    0
#     Comment    ${指定数据2}    Create Dictionary    signal_name    <<整流器一键功能状态-4~0x7001020060001>>    convention    False    device_name    整流器    data_type    int    unit    None    snmp_name    sMRP2PStatus4value    signal_node    sMRP2PStatus4
#     ...    Vindex    0
#     Comment    @{snmp待测}    Create List    ${指定数据1}    ${指定数据2}
#     FOR    ${i}    IN    @{snmp待测}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${节点名称}    Get From Dictionary    ${i}    snmp_name
#         ${信号序号}    Get From Dictionary    ${i}    Vindex
#         ${缺省值}    获取web参数上下限范围    ${信号名称}
#         Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    snmp    SMR    ${信号名称}    数字量    ${缺省值}[1]    整流器数字量获取测试    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    snmp    SMR    ${信号名称}    数字量    ${缺省值}[2]    整流器数字量获取测试    null    ${节点名称}    ${信号序号}
#         Run keyword IF    ${缺省值}[1]!=${缺省值}[0] and ${缺省值}[2]!=${缺省值}[0]    Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    snmp    SMR    ${信号名称}    数字量    ${缺省值}[0]    整流器数字量获取测试    null    ${节点名称}    ${信号序号}
#     END
#     断开连接SNMP


snmp批量获取SMR数字量
    [Documentation]    21min
    [Setup]    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    自由
    写入CSV文档    整流器数字量获取测试    信号名称    信号值    结果    备注
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=digital data    INDEX_DEVICE_CH=整流器
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除数字量信号}    ${排除列表}    2    ${模拟整流器开始地址}
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    rectifier    digitalData
    Comment    ${指定数据1}    Create Dictionary    signal_name    <<整流器风扇控制状态-4~0x70010200a0001>>    convention    False    device_name    整流器    data_type    int    unit    None    snmp_name    sMRFanControlState4value    signal_node    sMRFanControlState4value
    ...    Vindex    0
    Comment    ${指定数据2}    Create Dictionary    signal_name    <<整流器一键功能状态-4~0x7001020060001>>    convention    False    device_name    整流器    data_type    int    unit    None    snmp_name    sMRP2PStatus4value    signal_node    sMRP2PStatus4
    ...    Vindex    0
    Comment    @{snmp待测}    Create List    ${指定数据1}    ${指定数据2}
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    1    snmp
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    snmp    SMR    数字量    ${缺省值列表}    整流器数字量获取测试    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    2    snmp
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    snmp    SMR    数字量    ${缺省值列表}    整流器数字量获取测试    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    0    snmp
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    snmp    SMR    数字量    ${缺省值列表}    整流器数字量获取测试    null
    断开连接SNMP
    [Teardown]    Wait Until Keyword Succeeds    10    2    设置web参数量    交流节能模式   安全
