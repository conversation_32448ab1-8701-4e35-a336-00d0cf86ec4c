*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
IB1通道设置测试
    连接CSU
    wait until keyword succeeds    30    1    设置通道配置    ${plat.battcurr1}    0    1    直流配电    电池分流器电流_1
    sleep    20
    ${电流}    获取web实时数据    电池电流
    wait until keyword succeeds    30    1    设置通道配置    ${plat.battcurr1}    1    1    直流配电    电池分流器电流_1
    sleep    20
    ${电流new}    获取web实时数据    电池电流
    should be true    ${电流}<=${电流new}<=${电流}+1
    wait until keyword succeeds    30    1    设置通道配置    ${plat.battcurr1}    10    10    直流配电    电池分流器电流_1
    sleep    20
    ${电流new1}    获取web实时数据    电池电流
    ${电流计算值}    evaluate    ${电流}
    should be true    ${电流计算值}+9<=${电流new1}<=${电流计算值}+10
    [Teardown]    wait until keyword succeeds    30    1    设置通道配置    ${plat.battcurr1}    0    1    直流配电    电池分流器电流_1

IL1通道设置测试
    [Documentation]    要配置负载分流器
    [Tags]    notest
    连接CSU
    wait until keyword succeeds    30    1    设置通道配置    SPB_X3_IL1    0    1    直流配电    负载分流器电流_1
    sleep    20
    ${电流}    获取web实时数据    负载总电流
    wait until keyword succeeds    30    1    设置通道配置    SPB_X3_IL1    1    1    直流配电    负载分流器电流_1
    sleep    20
    ${电流new}    获取web实时数据    负载总电流
    should be true    ${电流}<=${电流new}<=${电流}+1
    wait until keyword succeeds    30    1    设置通道配置    SPB_X3_IL1    10    10    直流配电    负载分流器电流_1
    sleep    20
    ${电流new1}    获取web实时数据    负载总电流
    ${电流计算值}    evaluate    ${电流}
    should be true    ${电流计算值}+9<=${电流new1}<=${电流计算值}+10
    [Teardown]    wait until keyword succeeds    30    1    设置通道配置    SPB_X3_IL1    0    1    直流配电    负载分流器电流_1

VB1通道设置测试
    连接CSU
    wait until keyword succeeds    30    1    设置通道配置    ${plat.battvolt1}    0    1    电池_1    电池电压
    sleep    20
    ${电压}    获取web实时数据    电池电压-1
    wait until keyword succeeds    30    1    设置通道配置    ${plat.battvolt1}    1    1    电池_1    电池电压
    sleep    20
    ${电压new}    获取web实时数据    电池电压-1
    should be true    ${电压}<=${电压new}<=${电压}+2
    wait until keyword succeeds    30    1    设置通道配置    ${plat.battvolt1}    10    0.5    电池_1    电池电压
    sleep    20
    ${电压new1}    获取web实时数据    电池电压-1
    ${电压计算值}    evaluate    ${电压}*0.5
    should be true    ${电压计算值}+9<=${电压new1}<=${电压计算值}+15
    [Teardown]    wait until keyword succeeds    30    1    设置通道配置    ${plat.battvolt1}    0    1    电池_1    电池电压

VIN通道设置测试
    连接CSU
    wait until keyword succeeds    30    1    设置通道配置    ${plat.voltage}    0    1    直流配电    直流电压
    sleep    20
    ${电压}    获取web实时数据    直流电压
    wait until keyword succeeds    30    1    设置通道配置    ${plat.voltage}    1    1    直流配电    直流电压
    sleep    20
    ${电压new}    获取web实时数据    直流电压
    should be true    ${电压}<=${电压new}<=${电压}+2
    wait until keyword succeeds    30    1    设置通道配置    ${plat.voltage}    10    0.5    直流配电    直流电压
    sleep    20
    ${电压new1}    获取web实时数据    直流电压
    ${电压计算值}    evaluate    ${电压}*0.5
    should be true    ${电压计算值}+9<=${电压new1}<=${电压计算值}+15
    [Teardown]    wait until keyword succeeds    30    1    设置通道配置    ${plat.voltage}    0    1    直流配电    直流电压

T1通道设置测试
    连接CSU
    wait until keyword succeeds    30    1    设置通道配置    ${plat.batttemp1}    0    1    电池_1    电池温度
    sleep    20
    ${电池温度}    获取web实时数据    电池温度-1
    wait until keyword succeeds    30    1    设置通道配置    ${plat.batttemp1}    1    1    电池_1    电池温度
    sleep    20
    ${电池温度new}    获取web实时数据    电池温度-1
    should be true    ${电池温度}+1<=${电池温度new}<=${电池温度}+2
    wait until keyword succeeds    30    1    设置通道配置    ${plat.batttemp1}    10    0.5    电池_1    电池温度
    sleep    20
    ${电池温度new1}    获取web实时数据    电池温度-1
    ${电池温度计算值}    evaluate    ${电池温度}*0.5
    should be true    ${电池温度计算值}+8<=${电池温度new1}<=${电池温度计算值}+15
