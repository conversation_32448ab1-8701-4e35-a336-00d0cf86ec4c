*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
油机运行状态测试
    油机管理初始化
    ${最短运行时间设置值}    获取web参数量    油机最短运行时间
    ${默认值}    获取web参数上下限范围    油机最短运行时间
    run keyword if    ${最短运行时间设置值} != ${默认值}[0]    Wait Until Keyword Succeeds    10    1    设置web参数量    油机最短运行时间
    ...    ${默认值}[0]
    ${最短运行时间设置值}    获取web参数量    油机最短运行时间
    Wait Until Keyword Succeeds    5m    1    设置web控制量    <<油机开启~0x19001040010001>>
    Wait Until Keyword Succeeds    20m    1    获取干接点状态直到变化为    ${油机干接点状态}    0    #干接点动作为1（断开），恢复为0（闭合）
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    异常
    Wait Until Keyword Succeeds    15m    1    判断告警存在    油机异常告警
    Wait Until Keyword Succeeds    15m    1    设置web控制量    <<油机开启~0x19001040010001>>
    Wait Until Keyword Succeeds    20m    1    获取干接点状态直到变化为    ${油机干接点状态}    0    #干接点动作为1（断开），恢复为0（闭合）
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    运行
    Wait Until Keyword Succeeds    15m    1    判断告警不存在    油机异常告警
    Wait Until Keyword Succeeds    15m    1    设置web控制量    <<油机关闭~0x19001040020001>>
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    停止
    Wait Until Keyword Succeeds    20m    1    获取干接点状态直到变化为    ${油机干接点状态}    1    #干接点动作为1（断开），恢复为0（闭合）
    关闭交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    [Teardown]    关闭交流源输出

直流电压启动测试（电压启动手动关闭）
    [Setup]
    油机管理初始化
    ${开始数量}    获取web事件记录数量    油机启停记录
    @{记录内容old}    run keyword if    ${开始数量} != 0    获取web事件记录最新一条    油机启停记录
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动电压使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动SOC使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动时间使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动系统异常使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机定时启动使能    禁止
    ${最短运行时间设置值}    获取web参数量    油机最短运行时间
    ${默认值}    获取web参数上下限范围    油机最短运行时间
    run keyword if    ${最短运行时间设置值} != ${默认值}[0]    Wait Until Keyword Succeeds    10    1    设置web参数量    油机最短运行时间
    ...    ${默认值}[0]
    ${最短运行时间设置值}    获取web参数量    油机最短运行时间
    ${设置值}    获取web参数量    油机启动电压
    ${设置电压}    evaluate    ${设置值}-0.5
    设置电池模拟器电压模式    铅酸电池    1    1    ${设置电压}
    run keyword and ignore error    wait until keyword succeeds    20    1    判断告警不存在    油机异常告警
    Wait Until Keyword Succeeds    20m    1    获取干接点状态直到变化为    ${油机干接点状态}    0
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    运行
    ${起始时间}    获取系统时间
    ${起始发电量}    获取web实时数据    油机累计发电量
    ${起始运行时间}    获取web实时数据    油机累计运行时间
    sleep    2m
    ${设置电压}    evaluate    ${设置值}+1
    设置电池模拟器电压模式    铅酸电池    1    1    ${设置电压}
    Wait Until Keyword Succeeds    12m    1    设置web控制量    <<油机关闭~0x19001040020001>>
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    停止
    ${结束运行时间}    获取web实时数据    油机累计运行时间
    Wait Until Keyword Succeeds    20m    1    获取干接点状态直到变化为    ${油机干接点状态}    1
    ${结束时间}    获取系统时间
    ${结束发电量}    获取web实时数据    油机累计发电量
    sleep    1m
    关闭交流源输出
    sleep    5m
    ${结束数量}    获取web事件记录数量    油机启停记录
    should be true    ${结束数量} == ${开始数量}+1
    @{记录内容}    获取web事件记录最新一条    油机启停记录
    should be true    ${记录内容}[0] == ${结束数量}
    ${起始时间差}    subtract date from date    ${记录内容}[1]    ${起始时间}
    should be true    -40<=${起始时间差}<=40
    ${结束时间差}    subtract date from date    ${记录内容}[2]    ${结束时间}
    should be true    -40<=${结束时间差}<=40
    should be true    ${起始运行时间}-1<= ${记录内容}[3] <= ${起始运行时间}+1
    should be true    ${结束运行时间}-1<= ${记录内容}[4] <= ${结束运行时间}+1
    should be true    ${起始发电量}-0.4 <= ${记录内容}[5] <= ${起始发电量}+0.4
    should be true    ${结束发电量}-0.4<= ${记录内容}[6] <= ${结束发电量}+0.4
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[7] \ == ${记录内容old}[7]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[8] \ == ${记录内容old}[8]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[9] \ == ${记录内容old}[9]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[10] \ == ${记录内容old}[10]
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    油机启动时间使能
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动系统异常使能
    ...    允许
    ...    AND    关闭交流源输出

电池SOC启动测试（SOC启动手动关闭）
    油机管理初始化
    ${开始数量}    获取web事件记录数量    油机启停记录
    @{记录内容old}    run keyword if    ${开始数量} != 0    获取web事件记录最新一条    油机启停记录
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    20
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动电压使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动SOC使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动时间使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动系统异常使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机定时启动使能    禁止
    ${最短运行时间设置值}    获取web参数量    油机最短运行时间
    ${默认值}    获取web参数上下限范围    油机最短运行时间
    run keyword if    ${最短运行时间设置值} != ${默认值}[0]    Wait Until Keyword Succeeds    10    1    设置web参数量    油机最短运行时间
    ...    ${默认值}[0]
    ${最短运行时间设置值}    获取web参数量    油机最短运行时间
    ${可设置范围}    获取web参数可设置范围    油机启动SOC阈值
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动SOC阈值    ${可设置范围}[1]
    ${设置值}    获取web参数量    油机启动SOC阈值
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    40
    打开负载输出
    sleep    10
    ${当前容量}    获取web实时数据    电池组当前容量比率-1
    ${电流}    获取web实时数据    电池电流-1
    Wait Until Keyword Succeeds    32m    1    信号量数据值小于    电池组当前容量比率-1    ${设置值}
    run keyword and ignore error    wait until keyword succeeds    20    1    判断告警不存在    油机异常告警
    Wait Until Keyword Succeeds    20m    1    获取干接点状态直到变化为    ${油机干接点状态}    0
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    关闭负载输出
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    运行
    ${起始时间}    获取系统时间
    ${起始发电量}    获取web实时数据    油机累计发电量
    ${起始运行时间}    获取web实时数据    油机累计运行时间
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    浮充    #浮充
    #设置电池电流
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.battcurr1}     20    1.6
    ...    直流配电    电池分流器电流_1
    ${关闭SOC}    evaluate    ${设置值}+5
    Wait Until Keyword Succeeds    32m    1    信号量数据值大于    电池组当前容量比率-1    ${关闭SOC}
    Wait Until Keyword Succeeds    2m    1    设置web控制量    <<油机关闭~0x19001040020001>>
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    停止
    ${结束运行时间}    获取web实时数据    油机累计运行时间
    Wait Until Keyword Succeeds    20m    1    获取干接点状态直到变化为    ${油机干接点状态}    1
    ${结束时间}    获取系统时间
    ${结束发电量}    获取web实时数据    油机累计发电量
    sleep    1m
    关闭交流源输出
    sleep    4m
    ${结束数量}    获取web事件记录数量    油机启停记录
    should be true    ${结束数量} == ${开始数量}+1
    @{记录内容}    获取web事件记录最新一条    油机启停记录
    should be true    ${记录内容}[0] == ${结束数量}
    ${起始时间差}    subtract date from date    ${记录内容}[1]    ${起始时间}
    should be true    -40<=${起始时间差}<=40
    ${结束时间差}    subtract date from date    ${记录内容}[2]    ${结束时间}
    should be true    -40<=${结束时间差}<=40
    should be true    ${起始运行时间}-1<= ${记录内容}[3] <= ${起始运行时间}+1
    should be true    ${结束运行时间}-1<= ${记录内容}[4] <= ${结束运行时间}+1
    should be true    ${起始发电量}-0.4 <= ${记录内容}[5] <= ${起始发电量}+0.4
    should be true    ${结束发电量}-0.4<= ${记录内容}[6] <= ${结束发电量}+0.4
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[7] \ == ${记录内容old}[7]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[8] \ == ${记录内容old}[8]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[9] \ == ${记录内容old}[9]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[10] \ == ${记录内容old}[10]
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    油机启动电压使能
    ...    油机启动时间使能    油机启动SOC阈值    油机启动SOC使能
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动系统异常使能
    ...    允许
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1
    ...    100
    ...    AND    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.battcurr1} 
    ...    0    1    直流配电    电池分流器电流_1
    ...    AND    关闭负载输出
    ...    AND    关闭交流源输出

电池放电时间启动测试（放电时间启动手动关闭）
    油机管理初始化
    ${开始数量}    获取web事件记录数量    油机启停记录
    @{记录内容old}    run keyword if    ${开始数量} != 0    获取web事件记录最新一条    油机启停记录
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动电压使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动SOC使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动时间使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动系统异常使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机定时启动使能    禁止
    ${最短运行时间设置值}    获取web参数量    油机最短运行时间
    ${默认值}    获取web参数上下限范围    油机最短运行时间
    run keyword if    ${最短运行时间设置值} != ${默认值}[0]    Wait Until Keyword Succeeds    10    1    设置web参数量    油机最短运行时间
    ...    ${默认值}[0]
    ${最短运行时间设置值}    获取web参数量    油机最短运行时间
    ${可设置范围}    获取web参数可设置范围    油机启动放电时间阈值
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动放电时间阈值    ${可设置范围}[0]
    ${设置值}    获取web参数量    油机启动放电时间阈值
    设置负载电压电流    53.5    20
    打开负载输出
    sleep    10
    ${获取值}    获取web实时数据    电池电流-1
    ${放电时间}    evaluate    ${设置值}+1
    sleep    ${放电时间}m
    run keyword and ignore error    wait until keyword succeeds    20    1    判断告警不存在    油机异常告警
    Wait Until Keyword Succeeds    20m    1    获取干接点状态直到变化为    ${油机干接点状态}    0    #干接点动作为1（断开），恢复为0（闭合）
    同时设置三相电压频率    220    50
    打开交流源输出
    关闭负载输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    15m    1    信号量数据值为    油机状态    运行
    ${起始时间}    获取系统时间
    ${起始发电量}    获取web实时数据    油机累计发电量
    ${起始运行时间}    获取web实时数据    油机累计运行时间
    sleep    2m
    Wait Until Keyword Succeeds    15m    1    设置web控制量    <<油机关闭~0x19001040020001>>
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    停止
    ${结束运行时间}    获取web实时数据    油机累计运行时间
    Wait Until Keyword Succeeds    20m    1    获取干接点状态直到变化为    ${油机干接点状态}    1    #干接点动作为1（断开），恢复为0（闭合）
    ${结束时间}    获取系统时间
    ${结束发电量}    获取web实时数据    油机累计发电量
    sleep    1m
    关闭交流源输出
    sleep    12m
    ${结束数量}    获取web事件记录数量    油机启停记录
    should be true    ${结束数量} == ${开始数量}+1
    @{记录内容}    获取web事件记录最新一条    油机启停记录
    should be true    ${记录内容}[0] == ${结束数量}
    ${起始时间差}    subtract date from date    ${记录内容}[1]    ${起始时间}
    should be true    -40<=${起始时间差}<=40
    ${结束时间差}    subtract date from date    ${记录内容}[2]    ${结束时间}
    should be true    -40<=${结束时间差}<=40
    should be true    ${起始运行时间}-1<= ${记录内容}[3] <= ${起始运行时间}+1
    should be true    ${结束运行时间}-1<= ${记录内容}[4] <= ${结束运行时间}+1
    should be true    ${起始发电量}-0.4 <= ${记录内容}[5] <= ${起始发电量}+0.4
    should be true    ${结束发电量}-0.4<= ${记录内容}[6] <= ${结束发电量}+0.4
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[7] \ == ${记录内容old}[7]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[8] \ == ${记录内容old}[8]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[9] \ == ${记录内容old}[9]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[10] \ == ${记录内容old}[10]
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    油机启动电压使能
    ...    油机启动时间使能    油机启动放电时间阈值
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动系统异常使能
    ...    允许
    ...    AND    关闭交流源输出

油机定时启动测试
    油机管理初始化
    ${开始数量}    获取web事件记录数量    油机启停记录
    @{记录内容old}    run keyword if    ${开始数量} != 0    获取web事件记录最新一条    油机启停记录
    Wait Until Keyword Succeeds    10    1    设置系统时间    2021-01-29 8:00:00
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动电压使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动SOC使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动时间使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动系统异常使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机定时启动使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止电压使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止SOC使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止电流使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机定时启动时刻_1    10:00
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机定时关闭时刻_1    10:20
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机定时启动时刻_2    11:00
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机定时关闭时刻_2    12:20
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机定时启动时刻_3    13:00
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机定时关闭时刻_3    14:20
    ${油机启动时间}    获取web参数量    油机定时启动时刻_1
    ${当前系统时间}    获取系统时间
    ${当前系统时间}    split string    ${当前系统时间}
    ${油机启动时间}    catenate    SEPARATOR=    ${油机启动时间}    :00
    ${设置系统时间}    catenate    ${当前系统时间}[0]    ${油机启动时间}
    ${油机启动时间0}    Subtract Time From Date    ${设置系统时间}    5s    #比1:00:00差5s
    Wait Until Keyword Succeeds    10    1    设置系统时间    ${油机启动时间0}
    ${可设置范围}    获取web参数可设置范围    油机最短运行时间
    ${时间设置值}    evaluate    ${可设置范围}[0]+5
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机最短运行时间    ${时间设置值}
    ${最短运行时间设置值}    获取web参数量    油机最短运行时间
    run keyword and ignore error    wait until keyword succeeds    20    1    判断告警不存在    油机异常告警
    Wait Until Keyword Succeeds    20m    1    获取干接点状态直到变化为    ${油机干接点状态}    0    #干接点动作为1（断开），恢复为0（闭合）
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    15m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    运行
    ${起始时间}    获取系统时间
    ${起始发电量}    获取web实时数据    油机累计发电量
    ${起始运行时间}    获取web实时数据    油机累计运行时间
    sleep    2m
    ${油机启动时间}    获取web参数量    油机定时关闭时刻_1
    ${当前系统时间}    获取系统时间
    ${当前系统时间}    split string    ${当前系统时间}
    ${油机启动时间}    catenate    SEPARATOR=    ${油机启动时间}    :00
    ${设置系统时间}    catenate    ${当前系统时间}[0]    ${油机启动时间}
    ${油机启动时间0}    Subtract Time From Date    ${设置系统时间}    5s    #比1:00:00差5s
    Wait Until Keyword Succeeds    10    1    设置系统时间    ${油机启动时间0}
    Wait Until Keyword Succeeds    10m    1    信号量数据值为    油机状态    停止
    ${结束运行时间}    获取web实时数据    油机累计运行时间
    Wait Until Keyword Succeeds    20m    1    获取干接点状态直到变化为    ${油机干接点状态}    1    #干接点动作为1（断开），恢复为0（闭合）
    ${结束时间}    获取系统时间
    ${结束发电量}    获取web实时数据    油机累计发电量
    sleep    1m
    关闭交流源输出
    sleep    4m
    ${结束数量}    获取web事件记录数量    油机启停记录
    should be true    ${结束数量} == ${开始数量}+1
    @{记录内容}    获取web事件记录最新一条    油机启停记录
    should be true    ${记录内容}[0] == ${结束数量}
    ${起始时间差}    subtract date from date    ${记录内容}[1]    ${起始时间}
    should be true    -40<=${起始时间差}<=40
    ${结束时间差}    subtract date from date    ${记录内容}[2]    ${结束时间}
    should be true    -40<=${结束时间差}<=40
    should be true    ${起始运行时间}-1<= ${记录内容}[3] <= ${起始运行时间}+1
    should be true    ${结束运行时间}-1<= ${记录内容}[4] <= ${结束运行时间}+1
    should be true    ${起始发电量}-0.4 <= ${记录内容}[5] <= ${起始发电量}+0.4
    should be true    ${结束发电量}-0.4<= ${记录内容}[6] <= ${结束发电量}+0.4
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[7] \ == ${记录内容old}[7]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[8] \ == ${记录内容old}[8]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[9] \ == ${记录内容old}[9]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[10] \ == ${记录内容old}[10]
    #启动时刻2启动油机
    ${开始数量}    获取web事件记录数量    油机启停记录
    @{记录内容old}    run keyword if    ${开始数量} != 0    获取web事件记录最新一条    油机启停记录
    ${油机启动时间}    获取web参数量    油机定时启动时刻_2
    ${当前系统时间}    获取系统时间
    ${当前系统时间}    split string    ${当前系统时间}
    ${油机启动时间}    catenate    SEPARATOR=    ${油机启动时间}    :00
    ${设置系统时间}    catenate    ${当前系统时间}[0]    ${油机启动时间}
    ${油机启动时间0}    Subtract Time From Date    ${设置系统时间}    5s    #比1:00:00差5s
    Wait Until Keyword Succeeds    10    1    设置系统时间    ${油机启动时间0}
    Wait Until Keyword Succeeds    20m    1    获取干接点状态直到变化为    ${油机干接点状态}    0    #干接点动作为1（断开），恢复为0（闭合）
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    15m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    运行
    ${起始时间}    获取系统时间
    ${起始发电量}    获取web实时数据    油机累计发电量
    ${起始运行时间}    获取web实时数据    油机累计运行时间
    sleep    2m
    #启动时刻2关闭油机
    ${油机启动时间}    获取web参数量    油机定时关闭时刻_2
    ${当前系统时间}    获取系统时间
    ${当前系统时间}    split string    ${当前系统时间}
    ${油机启动时间}    catenate    SEPARATOR=    ${油机启动时间}    :00
    ${设置系统时间}    catenate    ${当前系统时间}[0]    ${油机启动时间}
    ${油机启动时间0}    Subtract Time From Date    ${设置系统时间}    5s    #比1:00:00差5s
    Wait Until Keyword Succeeds    10    1    设置系统时间    ${油机启动时间0}
    Wait Until Keyword Succeeds    10m    1    信号量数据值为    油机状态    停止
    ${结束运行时间}    获取web实时数据    油机累计运行时间
    Wait Until Keyword Succeeds    20m    1    获取干接点状态直到变化为    ${油机干接点状态}    1    #干接点动作为1（断开），恢复为0（闭合）
    ${结束时间}    获取系统时间
    ${结束发电量}    获取web实时数据    油机累计发电量
    sleep    1m
    关闭交流源输出
    sleep    4m
    ${结束数量}    获取web事件记录数量    油机启停记录
    should be true    ${结束数量} == ${开始数量}+1
    @{记录内容}    获取web事件记录最新一条    油机启停记录
    should be true    ${记录内容}[0] == ${结束数量}
    ${起始时间差}    subtract date from date    ${记录内容}[1]    ${起始时间}
    should be true    -40<=${起始时间差}<=40
    ${结束时间差}    subtract date from date    ${记录内容}[2]    ${结束时间}
    should be true    -40<=${结束时间差}<=40
    should be true    ${起始运行时间}-1<= ${记录内容}[3] <= ${起始运行时间}+1
    should be true    ${结束运行时间}-1<= ${记录内容}[4] <= ${结束运行时间}+1
    should be true    ${起始发电量}-0.4 <= ${记录内容}[5] <= ${起始发电量}+0.4
    should be true    ${结束发电量}-0.4<= ${记录内容}[6] <= ${结束发电量}+0.4
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[7] \ == ${记录内容old}[7]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[8] \ == ${记录内容old}[8]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[9] \ == ${记录内容old}[9]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[10] \ == ${记录内容old}[10]
    #启动时刻3启动油机
    ${开始数量}    获取web事件记录数量    油机启停记录
    @{记录内容old}    run keyword if    ${开始数量} != 0    获取web事件记录最新一条    油机启停记录
    ${油机启动时间}    获取web参数量    油机定时启动时刻_3
    ${当前系统时间}    获取系统时间
    ${当前系统时间}    split string    ${当前系统时间}
    ${油机启动时间}    catenate    SEPARATOR=    ${油机启动时间}    :00
    ${设置系统时间}    catenate    ${当前系统时间}[0]    ${油机启动时间}
    ${油机启动时间0}    Subtract Time From Date    ${设置系统时间}    5s    #比1:00:00差5s
    Wait Until Keyword Succeeds    10    1    设置系统时间    ${油机启动时间0}
    Wait Until Keyword Succeeds    20m    1    获取干接点状态直到变化为    ${油机干接点状态}    0    #干接点动作为1（断开），恢复为0（闭合）
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    15m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    运行
    ${起始时间}    获取系统时间
    ${起始发电量}    获取web实时数据    油机累计发电量
    ${起始运行时间}    获取web实时数据    油机累计运行时间
    sleep    2m
    #启动时刻2关闭油机
    ${油机启动时间}    获取web参数量    油机定时关闭时刻_3
    ${当前系统时间}    获取系统时间
    ${当前系统时间}    split string    ${当前系统时间}
    ${油机启动时间}    catenate    SEPARATOR=    ${油机启动时间}    :00
    ${设置系统时间}    catenate    ${当前系统时间}[0]    ${油机启动时间}
    ${油机启动时间0}    Subtract Time From Date    ${设置系统时间}    5s    #比1:00:00差5s
    Wait Until Keyword Succeeds    10    1    设置系统时间    ${油机启动时间0}
    Wait Until Keyword Succeeds    10m    1    信号量数据值为    油机状态    停止
    ${结束运行时间}    获取web实时数据    油机累计运行时间
    Wait Until Keyword Succeeds    20m    1    获取干接点状态直到变化为    ${油机干接点状态}    1    #干接点动作为1（断开），恢复为0（闭合）
    ${结束时间}    获取系统时间
    ${结束发电量}    获取web实时数据    油机累计发电量
    sleep    1m
    关闭交流源输出
    sleep    4m
    ${结束数量}    获取web事件记录数量    油机启停记录
    should be true    ${结束数量} == ${开始数量}+1
    @{记录内容}    获取web事件记录最新一条    油机启停记录
    should be true    ${记录内容}[0] == ${结束数量}
    ${起始时间差}    subtract date from date    ${记录内容}[1]    ${起始时间}
    should be true    -40<=${起始时间差}<=40
    ${结束时间差}    subtract date from date    ${记录内容}[2]    ${结束时间}
    should be true    -40<=${结束时间差}<=40
    should be true    ${起始运行时间}-1<= ${记录内容}[3] <= ${起始运行时间}+1
    should be true    ${结束运行时间}-1<= ${记录内容}[4] <= ${结束运行时间}+1
    should be true    ${起始发电量}-0.4 <= ${记录内容}[5] <= ${起始发电量}+0.4
    should be true    ${结束发电量}-0.4<= ${记录内容}[6] <= ${结束发电量}+0.4
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[7] \ == ${记录内容old}[7]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[8] \ == ${记录内容old}[8]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[9] \ == ${记录内容old}[9]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[10] \ == ${记录内容old}[10]
    [Teardown]    Run keywords    同步系统时间
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    油机定时启动时刻_1
    ...    油机定时关闭时刻_1    油机定时启动时刻_2    油机定时关闭时刻_2    油机定时启动时刻_3    油机定时关闭时刻_3
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    油机定时启动使能
    ...    油机启动时间使能    油机停止电压使能    油机停止SOC使能    油机最短运行时间
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动系统异常使能
    ...    允许
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止电流使能
    ...    禁止
    ...    AND    关闭交流源输出

直流电压启动测试（电压启动电压关闭）
    [Setup]
    油机管理初始化
    ${开始数量}    获取web事件记录数量    油机启停记录
    @{记录内容old}    run keyword if    ${开始数量} != 0    获取web事件记录最新一条    油机启停记录
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动电压使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动SOC使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动时间使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动系统异常使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机定时启动使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止电压使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止SOC使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止电流使能    禁止
    ${设置值}    获取web参数量    油机启动电压
    ${停止电压}    获取web参数量    油机停止电压
    ${可设置范围}    获取web参数可设置范围    油机最短运行时间
    ${时间设置值}    evaluate    ${可设置范围}[0]+5
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机最短运行时间    ${时间设置值}
    ${最短运行时间设置值}    获取web参数量    油机最短运行时间
    ${设置电压}    evaluate    ${设置值}-0.5
    设置电池模拟器电压模式    铅酸电池    1    1    ${设置电压}
    run keyword and ignore error    wait until keyword succeeds    20    1    判断告警不存在    油机异常告警
    Wait Until Keyword Succeeds    20m    1    获取干接点状态直到变化为    ${油机干接点状态}    0    #干接点动作为1（断开），恢复为0（闭合）
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    15m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    运行
    ${起始时间}    获取系统时间
    ${起始发电量}    获取web实时数据    油机累计发电量
    ${起始运行时间}    获取web实时数据    油机累计运行时间
    #电压不满足条件，不关闭油机
    ${设置电压}    evaluate    ${停止电压}-1
    设置电池模拟器电压模式    铅酸电池    1    1    ${设置电压}
    ${状态}    Run Keyword And Return Status    Wait Until Keyword Succeeds    2m    2    信号量数据值为    油机状态
    ...    停止
    should not be true    ${状态}
    ${设置电压}    evaluate    ${停止电压}+0.3
    设置电池模拟器电压模式    铅酸电池    1    1    ${设置电压}
    #电压满足条件，但是最短运行时间不满足条件
    ${状态}    Run Keyword And Return Status    Wait Until Keyword Succeeds    2m40s    2    信号量数据值为    油机状态
    ...    停止
    should not be true    ${状态}
    #电压满足条件且最短运行时间满足条件
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    停止
    ${结束运行时间}    获取web实时数据    油机累计运行时间
    Wait Until Keyword Succeeds    20m    1    获取干接点状态直到变化为    ${油机干接点状态}    1    #干接点动作为1（断开），恢复为0（闭合）
    ${结束时间}    获取系统时间
    ${结束发电量}    获取web实时数据    油机累计发电量
    sleep    1m
    关闭交流源输出
    sleep    4m
    ${结束数量}    获取web事件记录数量    油机启停记录
    should be true    ${结束数量} == ${开始数量}+1
    @{记录内容}    获取web事件记录最新一条    油机启停记录
    should be true    ${记录内容}[0] == ${结束数量}
    ${起始时间差}    subtract date from date    ${记录内容}[1]    ${起始时间}
    should be true    -40<=${起始时间差}<=40
    ${结束时间差}    subtract date from date    ${记录内容}[2]    ${结束时间}
    should be true    -40<=${结束时间差}<=40
    should be true    ${起始运行时间}-1<= ${记录内容}[3] <= ${起始运行时间}+1
    should be true    ${结束运行时间}-1<= ${记录内容}[4] <= ${结束运行时间}+1
    should be true    ${起始发电量}-0.4 <= ${记录内容}[5] <= ${起始发电量}+0.4
    should be true    ${结束发电量}-0.4<= ${记录内容}[6] <= ${结束发电量}+0.4
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[7] \ == ${记录内容old}[7]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[8] \ == ${记录内容old}[8]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[9] \ == ${记录内容old}[9]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[10] \ == ${记录内容old}[10]
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    油机启动时间使能
    ...    油机停止电压使能    油机停止SOC使能    油机最短运行时间
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动系统异常使能
    ...    允许
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止电流使能
    ...    禁止
    ...    AND    关闭交流源输出

电池SOC启动测试（SOC启动SOC关闭）
    油机管理初始化
    ${开始数量}    获取web事件记录数量    油机启停记录
    @{记录内容old}    run keyword if    ${开始数量} != 0    获取web事件记录最新一条    油机启停记录
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    20
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动电压使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动SOC使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动时间使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动系统异常使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机定时启动使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止电压使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止SOC使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止电流使能    禁止
    ${可设置范围}    获取web参数可设置范围    油机最短运行时间
    ${时间设置值}    evaluate    ${可设置范围}[0]+1
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机最短运行时间    ${时间设置值}
    ${最短运行时间设置值}    获取web参数量    油机最短运行时间
    ${可设置范围}    获取web参数可设置范围    油机启动SOC阈值
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动SOC阈值    ${可设置范围}[1]
    ${设置值}    获取web参数量    油机启动SOC阈值
    ${可设置范围}    获取web参数可设置范围    油机关闭SOC阈值
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机关闭SOC阈值    ${可设置范围}[0]
    ${关闭SOC设置值}    获取web参数量    油机关闭SOC阈值
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    40
    打开负载输出
    sleep    10
    ${当前容量}    获取web实时数据    电池组当前容量比率-1
    Wait Until Keyword Succeeds    32m    1    信号量数据值小于    电池组当前容量比率-1    ${设置值}
    run keyword and ignore error    wait until keyword succeeds    20    1    判断告警不存在    油机异常告警
    Wait Until Keyword Succeeds    20m    1    获取干接点状态直到变化为    ${油机干接点状态}    0    #干接点动作为1（断开），恢复为0（闭合）
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    关闭负载输出
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    运行
    ${起始时间}    获取系统时间
    ${起始发电量}    获取web实时数据    油机累计发电量
    ${起始运行时间}    获取web实时数据    油机累计运行时间
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    浮充    #浮充
    #设置电池电流
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.battcurr1}     20    1.6
    ...    直流配电    电池分流器电流_1
    ${关闭失败SOC}    evaluate    ${关闭SOC设置值}-3
    Wait Until Keyword Succeeds    32m    1    信号量数据值大于    电池组当前容量比率-1    ${关闭失败SOC}
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    <<油机关闭~0x19001040020001>>
    Wait Until Keyword Succeeds    32m    1    信号量数据值大于    电池组当前容量比率-1    ${关闭SOC设置值}
    Wait Until Keyword Succeeds    12m    1    设置web控制量    <<油机关闭~0x19001040020001>>
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    停止
    ${结束运行时间}    获取web实时数据    油机累计运行时间
    Wait Until Keyword Succeeds    20m    1    获取干接点状态直到变化为    ${油机干接点状态}    1    #干接点动作为1（断开），恢复为0（闭合）
    ${结束时间}    获取系统时间
    ${结束发电量}    获取web实时数据    油机累计发电量
    sleep    1m
    关闭交流源输出
    sleep    4m
    ${结束数量}    获取web事件记录数量    油机启停记录
    should be true    ${结束数量} == ${开始数量}+1
    @{记录内容}    获取web事件记录最新一条    油机启停记录
    should be true    ${记录内容}[0] == ${结束数量}
    ${起始时间差}    subtract date from date    ${记录内容}[1]    ${起始时间}
    should be true    -400<=${起始时间差}<=400
    ${结束时间差}    subtract date from date    ${记录内容}[2]    ${结束时间}
    should be true    -400<=${结束时间差}<=400
    should be true    ${起始运行时间}-1<= ${记录内容}[3] <= ${起始运行时间}+1
    should be true    ${结束运行时间}-1<= ${记录内容}[4] <= ${结束运行时间}+1
    should be true    ${起始发电量}-0.4 <= ${记录内容}[5] <= ${起始发电量}+0.4
    should be true    ${结束发电量}-0.4<= ${记录内容}[6] <= ${结束发电量}+0.4
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[7] \ == ${记录内容old}[7]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[8] \ == ${记录内容old}[8]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[9] \ == ${记录内容old}[9]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[10] \ == ${记录内容old}[10]
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    油机启动电压使能
    ...    油机启动时间使能    油机启动SOC阈值    油机启动SOC使能    油机停止电压使能    油机关闭SOC阈值    油机停止SOC使能
    ...    油机最短运行时间
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动系统异常使能
    ...    允许
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止电流使能
    ...    禁止
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1
    ...    100
    ...    AND    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.battcurr1} 
    ...    0    1    直流配电    电池分流器电流_1
    ...    AND    关闭负载输出
    ...    AND    关闭交流源输出

电池放电时间启动测试（放电时间启动停止电流关闭）
    [Setup]
    油机管理初始化
    ${开始数量}    获取web事件记录数量    油机启停记录
    @{记录内容old}    run keyword if    ${开始数量} != 0    获取web事件记录最新一条    油机启停记录
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    500
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动电压使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动SOC使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动时间使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动系统异常使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机定时启动使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止电压使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止SOC使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止电流使能    允许
    ${可设置范围}    获取web参数可设置范围    油机启动放电时间阈值
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动放电时间阈值    ${可设置范围}[0]
    ${设置值}    获取web参数量    油机启动放电时间阈值
    ${可设置范围}    获取web参数可设置范围    油机最短运行时间
    ${时间设置值}    evaluate    ${可设置范围}[0]+1
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机最短运行时间    ${时间设置值}
    ${最短运行时间设置值}    获取web参数量    油机最短运行时间
    ${可设置范围}    获取web参数可设置范围    油机关闭电池电流
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机关闭电池电流    ${可设置范围}[1]
    ${关闭电流设置值}    获取web参数量    油机关闭电池电流
    ${电池容量}    获取web参数量    电池组容量_1
    ${关闭电流}    evaluate    ${电池容量}*${关闭电流设置值}
    设置负载电压电流    53.5    20
    打开负载输出
    sleep    10
    ${获取值}    获取web实时数据    电池电流-1
    ${放电时间}    evaluate    ${设置值}+1
    sleep    ${放电时间}m
    run keyword and ignore error    wait until keyword succeeds    20    1    判断告警不存在    油机异常告警
    Wait Until Keyword Succeeds    20m    1    获取干接点状态直到变化为    ${油机干接点状态}    0    #干接点动作为1（断开），恢复为0（闭合）
    关闭负载输出
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    浮充    #浮充
    #设置电池电流
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.battcurr1}     20    1.6
    ...    直流配电    电池分流器电流_1
    Wait Until Keyword Succeeds    15m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    运行
    ${起始时间}    获取系统时间
    ${起始发电量}    获取web实时数据    油机累计发电量
    ${起始运行时间}    获取web实时数据    油机累计运行时间
    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    油机启动放电时间阈值
    ${系统异常}    获取指定量的调测信息    system_abnormal
    run keyword if    ${系统异常} != 0    系统复位
    #设置电池电流
    设置子工具值    iddb    all    AI数据    0xa001010030001    40
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.battcurr1}     0    1
    ...    直流配电    电池分流器电流_1
    设置子工具值    iddb    all    AI数据    0xa001010030001    40
    Wait Until Keyword Succeeds    32m    1    信号量数据值小于    电池电流-1    ${关闭电流}

    Wait Until Keyword Succeeds    20m    1    信号量数据值为    油机状态    停止
    ${结束运行时间}    获取web实时数据    油机累计运行时间
    Wait Until Keyword Succeeds    20m    1    获取干接点状态直到变化为    ${油机干接点状态}    1    #干接点动作为1（断开），恢复为0（闭合）
    ${结束时间}    获取系统时间
    ${结束发电量}    获取web实时数据    油机累计发电量
    sleep    1m
    #关闭交流源输出
    设置子工具值    smr    all    模拟量    整流器输出电压    0
    设置子工具值    smr    all    模拟量    整流器输入电压    0
    设置子工具值    smr    all    数字量    整流器关机状态    1
    sleep    4m
    ${结束数量}    获取web事件记录数量    油机启停记录
    should be true    ${结束数量} == ${开始数量}+1
    @{记录内容}    获取web事件记录最新一条    油机启停记录
    should be true    ${记录内容}[0] == ${结束数量}
    ${起始时间差}    subtract date from date    ${记录内容}[1]    ${起始时间}
    should be true    -40<=${起始时间差}<=40
    ${结束时间差}    subtract date from date    ${记录内容}[2]    ${结束时间}
    should be true    -40<=${结束时间差}<=40
    should be true    ${起始运行时间}-1<= ${记录内容}[3] <= ${起始运行时间}+1
    should be true    ${结束运行时间}-1<= ${记录内容}[4] <= ${结束运行时间}+1
    should be true    ${起始发电量}-0.4 <= ${记录内容}[5] <= ${起始发电量}+0.4
    should be true    ${结束发电量}-0.4<= ${记录内容}[6] <= ${结束发电量}+0.4
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[7] \ == ${记录内容old}[7]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[8] \ == ${记录内容old}[8]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[9] \ == ${记录内容old}[9]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[10] \ == ${记录内容old}[10]
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    油机启动电压使能
    ...    油机启动时间使能    油机启动放电时间阈值    油机停止电压使能    油机停止SOC使能    油机关闭电池电流    油机最短运行时间
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动系统异常使能
    ...    允许
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止电流使能
    ...    禁止
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1
    ...    100
    ...    AND    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.battcurr1} 
    ...    0    1    直流配电    电池分流器电流_1
    ...    AND    关闭交流源输出
