*** Settings ***
Documentation     告警级别
...               0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning; 0xFF：无效值
Suite Setup
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
1363_0006_1交流电压高
    [Documentation]    获取1104一条告警值并判断告警存在
    ...    输入
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${理应值}= | ${屏号}=None | ${g_ver}=
    ...
    ...
    ...
    ...    0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning; 0xFF：无效值
    ...
    ...    设置
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${var_name} | ${val} || ${屏号}=None | ${g_ver}=、
    ...
    ...    获取
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${屏号}=None | ${g_ver}=
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置交流段告警级别~CID1=40H>>    <<设置交流段告警级别~CID2=82H>>    交流电压高    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流告警级别~CID2=81H>>    交流电压高    FF    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    交流电压高_1    ${VAR}
        ${交流电压低告警级别}    获取web参数量    交流电压高_1
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    交流电压高_1    主要

1363_0008_2交流电压低
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置交流段告警级别~CID1=40H>>    <<设置交流段告警级别~CID2=82H>>    交流电压低    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流告警级别~CID2=81H>>    交流电压低    FF    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    交流电压低_1    ${VAR}
        ${交流电压低告警级别}    获取web参数量    交流电压低_1
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    设置web参数量    交流电压低_1    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    END
    [Teardown]    设置web参数量    交流电压低_1    主要

1363_0010_3交流缺相
    [Documentation]    获取1104一条告警值并判断告警存在
    ...    输入
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${理应值}= | ${屏号}=None | ${g_ver}=
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        Comment    ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置交流段告警级别~CID1=40H>>    <<设置交流段告警级别~CID2=82H>>    交流缺相    ${VAR}    None    ${g_ver_1363}
        Comment    1104设置单个参并判断设置结果    ${g_prtcl_1363}    <<设置交流段告警级别~CID1=40H>>    <<设置交流段告警级别~CID2=82H>>    交流缺相    ${VAR}    None    ${g_ver_1363}
        Wait Until Keyword Succeeds    10X    2    1104设置单个参数    ${g_prtcl_1363}    <<设置交流段告警级别~CID1=40H>>    <<设置交流段告警级别~CID2=82H>>    交流缺相    ${VAR}    None    ${g_ver_1363}
        Comment    should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流告警级别~CID2=81H>>    交流缺相    FF    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    交流缺相_1    ${VAR}
        ${交流电压低告警级别}    获取web参数量    交流缺相_1
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
    END
    设置web参数量    交流缺相_1    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    [Teardown]    设置web参数量    交流缺相_1    主要

1363_0012_4交流防雷器异常
    [Documentation]    注意从web上获取告警级别时，
    ...    是 交流防雷器异常 不是 交流防雷器异常_1
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置交流段告警级别~CID1=40H>>    <<设置交流段告警级别~CID2=82H>>    交流防雷器异常    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流告警级别~CID2=81H>>    交流防雷器异常    FF    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    交流防雷器异常    ${VAR}
        ${交流电压低告警级别}    获取web参数量    交流防雷器异常
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    交流防雷器异常    主要

1363_0014_5交流停电
    [Documentation]    获取1104一条告警值并判断告警存在
    ...    输入
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${理应值}= | ${屏号}=None | ${g_ver}=
    ...
    ...
    ...
    ...    0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning; 0xFF：无效值
    ...
    ...    设置
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${var_name} | ${val} || ${屏号}=None | ${g_ver}=、
    ...
    ...    获取
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${屏号}=None | ${g_ver}=
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置交流段告警级别~CID1=40H>>    <<设置交流段告警级别~CID2=82H>>    交流停电    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流告警级别~CID2=81H>>    交流停电    FF    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    交流停电    ${VAR}
        ${交流电压低告警级别}    获取web参数量    交流停电
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    交流停电    主要

1363_0016_6交流电压不平衡
    [Documentation]    获取1104一条告警值并判断告警存在
    ...    输入
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${理应值}= | ${屏号}=None | ${g_ver}=
    ...
    ...
    ...
    ...    0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning; 0xFF：无效值
    ...
    ...    设置
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${var_name} | ${val} || ${屏号}=None | ${g_ver}=、
    ...
    ...    获取
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${屏号}=None | ${g_ver}=
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置交流段告警级别~CID1=40H>>    <<设置交流段告警级别~CID2=82H>>    交流电压不平衡    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流告警级别~CID2=81H>>    交流电压不平衡    FF    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    交流电压不平衡    ${VAR}
        ${交流电压低告警级别}    获取web参数量    交流电压不平衡
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    交流电压不平衡    主要

1363_0018_9交流电流高
    [Documentation]    获取1104一条告警值并判断告警存在
    ...    输入
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${理应值}= | ${屏号}=None | ${g_ver}=
    ...
    ...
    ...
    ...    0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning; 0xFF：无效值
    ...
    ...    设置
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${var_name} | ${val} || ${屏号}=None | ${g_ver}=、
    ...
    ...    获取
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${屏号}=None | ${g_ver}=
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置交流段告警级别~CID1=40H>>    <<设置交流段告警级别~CID2=82H>>    交流电流高    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流告警级别~CID2=81H>>    交流电流高    FF    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    交流电流高_1    ${VAR}
        ${交流电压低告警级别}    获取web参数量    交流电流高_1
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    交流电流高_1    主要
