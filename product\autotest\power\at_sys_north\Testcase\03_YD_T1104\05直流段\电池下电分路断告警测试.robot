*** Settings ***
Suite Setup       Run Keywords    主动告警测试前置条件    ${CSU_role}
...               AND     连接CSU
...               AND     备份参数并导入电池下电条件下参数文件
Suite Teardown    Run Keywords    设置web设备参数量为默认值    CSU主动告警使能
...               AND     导入参数文件压缩包    ${备份参数压缩文件名称}
...               AND     删除文件    download/${备份参数压缩文件名称}
Resource          ../../../测试用例关键字.robot


*** Test Cases ***
1104_0040_电池下电扩展分路断告警测试
    实时告警刷新完成
    ${级别设置值}    获取web参数量    电池下电扩展分路断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池下电扩展分路断    主要
    wait until keyword succeeds    1m    1    判断告警不存在    电池下电扩展分路断
    设置web参数量    电池下电扩展分路断    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    电池下电扩展分路断
    ${告警}    判断告警存在_带返回值    电池下电扩展分路断
    should not be true    ${告警}
    模拟数字量告警    下电告警    ON
    sleep    10
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    电池下电扩展分路断    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池下电扩展分路断
        sleep    20
    #1363查询告警存在
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1104}    <<获取交流段告警量~CID1=42H>>    <<获取交流段告警量~CID2=44H>>    电池下电分路E断    3    None    ${g_ver_1104}
        should be true    ${1104告警结果}
    #1363查询告警级别与web端级别
        ${交流停电告警级别}    获取web告警属性    电池下电扩展分路断    告警级别
        should be equal    '${告警级别设置}'    '${交流停电告警级别}'
        ${web实时告警名}    由子工具获取web实时告警名称    电池下电扩展分路断
        Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    1104    直流配电    ${web实时告警名}
    END
    设置web参数量    电池下电扩展分路断    次要
    [Teardown]    run keywords    设置web参数量    电池下电扩展分路断    次要
    ...    AND    模拟数字量告警    下电告警    OFF


1104_0044_电池下电分路断告警测试
    实时告警刷新完成
    ${级别设置值}    获取web参数量    电池下电分路断_1
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池下电分路断_1    主要
    wait until keyword succeeds    1m    1    判断告警不存在    电池下电分路断_1
    设置web参数量    电池下电分路断_1    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    电池下电分路断_1
    ${告警}    判断告警存在_带返回值    电池下电分路断_1
    should not be true    ${告警}
    模拟数字量告警    下电告警    ON
    sleep    10
    FOR    ${告警级别设置}    IN    主要    次要    警告    严重
        Wait Until Keyword Succeeds    30    1    设置web参数量    电池下电分路断_1    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池下电分路断_1
        sleep    20
    #1363查询告警存在
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1104}    <<获取交流段告警量~CID1=42H>>    <<获取交流段告警量~CID2=44H>>    电池下电分路断_1    3    None    ${g_ver_1104}
        should be true    ${1104告警结果}
    #1363查询告警级别与web端级别
        ${交流停电告警级别}    获取web告警属性    电池下电分路断_1    告警级别
        should be equal    '${告警级别设置}'    '${交流停电告警级别}'
        Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    1104    直流配电    电池下电分路断[1]
    END
    设置web参数量    电池下电分路断_1    次要
    [Teardown]    模拟数字量告警    下电告警    OFF