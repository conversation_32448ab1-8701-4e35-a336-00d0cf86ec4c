*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
批量获取温湿度传感器模拟量
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    写入CSV文档    温湿度传感器模拟量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=温湿度传感器
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${排除列表}    ${排除列表}
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    温湿度传感器    analog data    ${排除列表}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    ${待测数据长度}    Get Length    ${power_sm待测}
    ${number}    Set Variable    1
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    1    power_sm
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    power_sm    DMU_WS312M1        只读    ${缺省值列表}     温湿度传感器模拟量获取测试    获取温湿度传感器模拟量    
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    2    power_sm
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    power_sm    DMU_WS312M1        只读    ${缺省值列表}     温湿度传感器模拟量获取测试    获取温湿度传感器模拟量    
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    0    power_sm
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    power_sm    DMU_WS312M1        只读    ${缺省值列表}      温湿度传感器模拟量获取测试    获取温湿度传感器模拟量     
    [Teardown]    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接


    


扩展温度（温湿度传感器）
    [Documentation]    1、通过通道复用关联扩展温度；
    ...    2、通过温湿度计关联扩展温度；
    [Setup]
    连接CSU
    ${缺省值}    获取web参数上下限范围    <<扩展温度~0x9001010030001>>
    ${下限}    Set Variable    -20
    ${上限}    Set Variable    80
    ${默认值}    Set Variable    24
    设置子工具值    DMU_WS312M1    all    只读    温度    ${下限}
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    温湿度传感器环境温度    ${下限}
    sleep    15
    ${power_sm获取值}    power_sm_获取一个数据的值    获取温湿度传感器模拟量    温湿度传感器环境温度    ${SSH}
    should be equal as numbers    ${power_sm获取值}    ${下限}
    FOR    ${i}    IN RANGE    1    9
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    温湿度传感器环境温度    ${下限}
        Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_${i}
        sleep    30
        ${WEB扩展温度}    获取web实时数据    扩展温度_${i}
        # sleep    15
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取系统运行环境模拟量    扩展温度_${i}    ${SSH}
        should be equal as numbers    ${WEB扩展温度}    ${下限}
        should be equal as numbers    ${power_sm获取值1}    ${WEB扩展温度}
    END
    设置子工具值    DMU_WS312M1    all    只读    温度    ${上限}
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    温湿度传感器环境温度    ${上限}
    sleep    15
    ${power_sm获取值}    power_sm_获取一个数据的值    获取温湿度传感器模拟量    温湿度传感器环境温度    ${SSH}
    should be equal as numbers    ${power_sm获取值}    ${上限}
    FOR    ${i}    IN RANGE    1    9
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    温湿度传感器环境温度    ${上限}
        Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_${i}
        sleep    30
        ${WEB扩展温度}    获取web实时数据    扩展温度_${i}
        # sleep    15
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取系统运行环境模拟量    扩展温度_${i}    ${SSH}
        should be equal as numbers    ${WEB扩展温度}    ${上限}
        should be equal as numbers    ${power_sm获取值1}    ${WEB扩展温度}
    END
    设置子工具值    DMU_WS312M1    all    只读    温度    ${默认值}
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    温湿度传感器环境温度    ${默认值}
    sleep    15
    ${power_sm获取值}    power_sm_获取一个数据的值    获取温湿度传感器模拟量    温湿度传感器环境温度    ${SSH}
    should be equal as numbers    ${power_sm获取值}    ${默认值}
    FOR    ${i}    IN RANGE    1    9
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    温湿度传感器环境温度    ${默认值}
        Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_${i}
        sleep    30
        ${WEB扩展温度}    获取web实时数据    扩展温度_${i}
        # sleep    15
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取系统运行环境模拟量    扩展温度_${i}    ${SSH}
        should be equal as numbers    ${WEB扩展温度}    ${默认值}
        should be equal as numbers    ${power_sm获取值1}    ${WEB扩展温度}
    END
    [Teardown]    Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_4

扩展湿度（温湿度传感器）
    [Documentation]    1、通过通道复用关联扩展温度；
    ...    2、通过温湿度计关联扩展温度；
    [Setup]
    连接CSU
    ${缺省值}    获取web参数上下限范围    <<扩展湿度~0x30001010020001>>
    设置子工具值    DMU_WS312M1    all    只读    湿度    ${缺省值}[1]
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    温湿度传感器环境湿度    ${缺省值}[1]
    sleep    15
    ${power_sm获取值}    power_sm_获取一个数据的值    获取温湿度传感器模拟量    温湿度传感器环境湿度    ${SSH}
    should be equal as numbers    ${power_sm获取值}    ${缺省值}[1]
    Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    湿度传感器    系统运行环境    扩展湿度
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    扩展湿度    ${缺省值}[1]
    sleep    15
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取系统运行环境模拟量    扩展湿度    ${SSH}
    should be equal as numbers    ${power_sm获取值1}    ${缺省值}[1]
    设置子工具值    DMU_WS312M1    all    只读    湿度    ${缺省值}[2]
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    温湿度传感器环境湿度    ${缺省值}[2]
    sleep    15
    ${power_sm获取值}    power_sm_获取一个数据的值    获取温湿度传感器模拟量    温湿度传感器环境湿度    ${SSH}
    should be equal as numbers    ${power_sm获取值}    ${缺省值}[2]
    Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    湿度传感器    系统运行环境    扩展湿度
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    扩展湿度    ${缺省值}[2]
    sleep    15
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取系统运行环境模拟量    扩展湿度    ${SSH}
    should be equal as numbers    ${power_sm获取值1}    ${缺省值}[2]
    设置子工具值    DMU_WS312M1    all    只读    湿度    ${缺省值}[0]
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    温湿度传感器环境湿度    ${缺省值}[0]
    sleep    15
    ${power_sm获取值}    power_sm_获取一个数据的值    获取温湿度传感器模拟量    温湿度传感器环境湿度    ${SSH}
    should be equal as numbers    ${power_sm获取值}    ${缺省值}[0]
    Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    湿度传感器    系统运行环境    扩展湿度
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    扩展湿度    ${缺省值}[0]
    sleep    15
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取系统运行环境模拟量    扩展湿度    ${SSH}
    should be equal as numbers    ${power_sm获取值1}    ${缺省值}[0]
    [Teardown]
