*** Settings ***
Force Tags
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp_test关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/Web关键字_V30.robot
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
snmp_0002_交流电表通讯状态√
    [Documentation]    0:正常/Normal;1:异常/Abnormal
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    显示属性配置    交流电表通讯状态    数字量    web_attr=On    gui_attr=On
    控制子工具运行停止    DMU_aemb    启动
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表通讯状态-1    正常
    ${snmp获取值1}    获取SNMP数据_单个    aCEMCommunicationState1value
    should be equal    ${snmp获取值1}    0
    控制子工具运行停止    DMU_aemb    关闭
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表通讯状态-1    异常
    ${snmp获取值1}    获取SNMP数据_单个    aCEMCommunicationState1value
    should be equal    ${snmp获取值1}    1
    断开连接SNMP
    显示属性配置    交流电表通讯状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    Run Keywords    控制子工具运行停止    DMU_aemb    启动
    ...    AND    sleep    3min

snmp_0004_交流电表在位状态√
    [Documentation]    0:不在位/Not Exist;1:在位/Is Exist
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    显示属性配置    交流电表在位状态    数字量    web_attr=On    gui_attr=On
    控制子工具运行停止    DMU_aemb    启动
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表在位状态-1    在位
    ${snmp获取值1}    获取SNMP数据_单个    aCEMExistState1value
    should be equal    '${snmp获取值1}'    '1'
    控制子工具运行停止    DMU_aemb    启动
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表在位状态-1    在位
    ${snmp获取值1}    获取SNMP数据_单个    aCEMExistState1value
    should be equal    '${snmp获取值1}'    '1'
    断开连接SNMP
    显示属性配置    交流电表在位状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    Run Keywords    控制子工具运行停止    DMU_aemb    启动
    ...    AND    sleep    3min

snmp_0006_交流电表工作状态X
    [Documentation]    0:无/Null;1:正常/Normal;2:告警/Alarm;3:通讯断/CommFail;
    连接CSU
    显示属性配置    交流电表工作状态    数字量    web_attr=On    gui_attr=On
    进行SNMP_V2/V3连接    ${snmp连接方式}
    控制子工具运行停止    DMU_aemb    启动
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表工作状态-1    正常
    ${snmp获取值1}    获取SNMP数据_单个    aCEMWorkState1value
    should be equal    ${snmp获取值1}    1
    控制子工具运行停止    DMU_aemb    关闭
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表工作状态-1    通讯断
    ${snmp获取值1}    获取SNMP数据_单个    aCEMWorkState1value
    should be equal    ${snmp获取值1}    3
    断开连接SNMP
    显示属性配置    交流电表工作状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    Run Keywords    控制子工具运行停止    DMU_aemb    启动
    ...    AND    sleep    3min
