*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
远程下电使能设置测试
    ${下电使能参数名列表}    Create List
    FOR    ${val}    IN RANGE    1    3
        ${下电使能名称}    Catenate    SEPARATOR=    <<配电单元远程下电使能_    ${val}    ~0xa0010500d0001>>
        Append To List    ${下电使能参数名列表}    ${下电使能名称}
        ${取值范围}    获取web参数的取值约定    ${下电使能名称}
        should be equal    ${取值范围}[0]    禁止
        should be equal    ${取值范围}[1]    允许
        ${下电使能}    获取web参数量    ${下电使能名称}
        设置web参数量    ${下电使能名称}    禁止
        ${远程下电名称}    Catenate    SEPARATOR=    <<配电单元远程下电_    ${val}    ~0xa0010500e0001>>
        ${远程下电}    获取web参数量    ${远程下电名称}
        ${转换值}    Convert To Boolean    ${远程下电}
        should not be true    ${转换值}
        设置web参数量    ${下电使能名称}    允许
        ${取值范围}    获取web参数的取值约定    ${远程下电名称}
        should be equal    ${取值范围}[0]    上电
        should be equal    ${取值范围}[1]    下电
        设置web参数量    ${下电使能名称}    ${下电使能}
    END
    [Teardown]    设置web设备参数量为默认值  @{下电使能参数名列表}

远程下电功能测试(CSU控制上电)
    [Documentation]   无“直流配电下电状态”及“直流配电上电状态”关键字
    [Tags]    notest
    设置web参数量    <<配电单元定时下电使能_1~0xa001050170001>>    禁止
    设置web参数量    直流配电单元远程下电使能_1    允许
    ${开始时间}    获取系统时间
    设置web参数量    直流配电单元远程下电_1    下电
    sleep    10
    判断告警存在    直流配电:直流配电单元下电告警[1]
    直流配电下电状态    配电单元电流_1    1
    设置web参数量    直流配电单元远程下电_1    上电
    sleep    10
    判断告警不存在    直流配电:直流配电单元下电告警[1]
    直流配电上电状态    配电单元电流_1    1
    ${结束时间}    获取系统时间
    ${告警内容}    获取web历史告警内容    ${开始时间}    ${结束时间}    1    10
    ${value}    Evaluate    False
    FOR    ${val}    IN    @{告警内容}
        ${array}    Split String    ${val}    ,
        ${value}=    set variable if    '直流配电:直流配电单元下电告警[1]' in '${array}[5]'    True    False
        Run keyword if    ${value}    Exit For Loop
    END
    should be true    ${value}
    [Teardown]    设置web设备参数量为默认值    直流配电单元远程下电_1

远程下电功能测试(CSU控制下电)
    [Documentation]   无“直流配电下电状态”及“直流配电上电状态”关键字
    [Tags]    notest
    设置web参数量    <<配电单元定时下电使能_1~0xa001050170001>>    禁止
    ${开始时间}    获取系统时间
    sleep    130
    判断告警存在    直流配电:直流配电单元下电告警[1]
    直流配电下电状态    配电单元电流_1    1
    ${远程下电使能}    获取web参数量    直流配电单元远程下电使能_1
    设置web参数量    直流配电单元远程下电使能_1    允许
    设置web参数量    直流配电单元远程下电_1    上电
    sleep    15
    判断告警存在    直流配电:直流配电单元下电告警[1]
    直流配电下电状态    配电单元电流_1    1
    设置web参数量    直流配电单元远程下电_1    下电
    sleep    15
    判断告警存在    直流配电:直流配电单元下电告警[1]
    直流配电下电状态    配电单元电流_1    1
    ${结束时间}    获取系统时间
    ${告警内容}    获取web历史告警内容    ${开始时间}    ${结束时间}    1    10    1
    ${value}    Evaluate    False
    FOR    ${val}    IN    @{告警内容}
        ${array}    Split String    ${val}    ,
        ${value}=    set variable if    '直流配电:直流配电单元下电告警[1]' in '${array}[5]'    True    False
        Run keyword if    ${value}    Exit For Loop
    END
    should not be true    ${value}
    [Teardown]    设置web设备参数量为默认值    直流配电单元远程下电_1    <<配电单元定时下电使能_1~0xa001050170001>>

远程下电功能测试-系统重启
    [Documentation]    无“直流配电下电状态”及“直流配电上电状态”关键字
    [Tags]    notest
    设置web参数量    <<配电单元定时下电使能_1~0xa001050170001>>    禁止
    设置web参数量    直流配电单元远程下电使能_1    允许
    设置web参数量    直流配电单元远程下电_1    下电
    sleep    15
    判断告警存在    直流配电:直流配电单元下电告警[1]
    直流配电下电状态    配电单元电流_1    1
    系统复位
    sleep    260
    连接CSU
    ${远程下电状态}    获取web参数量    直流配电单元远程下电_1
    should be equal    ${远程下电状态}    下电
    sleep    15
    判断告警存在    直流配电:直流配电单元下电告警[1]
    直流配电下电状态    配电单元电流_1    1
    设置web参数量    直流配电单元远程下电_1    上电
    sleep    15
    判断告警不存在    直流配电:直流配电单元下电告警[1]
    直流配电上电状态    配电单元电流_1    1
    系统复位
    sleep    260
    连接CSU
    ${远程下电状态}    获取web参数量    直流配电单元远程下电_1
    should be equal    ${远程下电状态}    上电
    判断告警不存在    直流配电:直流配电单元下电告警[1]
    直流配电上电状态    配电单元电流_1    1
    设置web参数量    直流配电单元远程下电使能_1    禁止
