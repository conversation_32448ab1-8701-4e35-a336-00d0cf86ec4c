*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
远程下电使能设置测试
    获取参数取值约定并比对取值范围    <<智能空开远程下电使能-1~0x2c001050190001>>    禁止    允许
    设置web参数量    <<智能空开远程下电使能-1~0x2c001050190001>>    禁止
    ${远程下电}    获取web参数量    <<智能空开远程下电-1~0x2c0010501a0001>>
    ${转换值}    Convert To Boolean    ${远程下电}
    should not be true    ${转换值}
    设置web参数量    <<智能空开远程下电使能-1~0x2c001050190001>>    允许
    获取参数取值约定并比对取值范围    <<智能空开远程下电-1~0x2c0010501a0001>>    上电    下电
    [Teardown]    设置web设备参数量为默认值    <<智能空开远程下电使能-1~0x2c001050190001>>

智能空开下电禁止，远程下电测试
    Wait Until Keyword Succeeds    30    1    设置web参数量    下电模式    电池电压    #默认1电池电压
    Wait Until Keyword Succeeds    30    1    设置web参数量    下电控制延时    0    #默认0
    Wait Until Keyword Succeeds    30    1    设置web参数量    <<智能空开下电使能-1~0x2c001050010001>>    禁止    #默认1允许
    设置web参数量    <<智能空开远程下电使能-1~0x2c001050190001>>    允许
    sleep    10
    ${开始时间}    获取系统时间
    设置web参数量    <<智能空开远程下电-1~0x2c0010501a0001>>    下电
    Wait Until Keyword Succeeds    5m    10    判断告警存在    智能空开下电告警-1
    判断智能空开下电状态和回路状态    下电    断开
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    <<智能空开电流-1~0x2c001010020001>>    0.0
    设置web参数量    <<智能空开远程下电-1~0x2c0010501a0001>>    上电
    Wait Until Keyword Succeeds    8m    20    查询指定告警信息不为    智能空开下电告警-1
    判断智能空开下电状态和回路状态    上电    闭合
    ${结束时间}    获取系统时间
    ${告警内容}    获取web历史告警内容    ${开始时间}    ${结束时间}    1    10
    ${value}    Evaluate    False
    FOR    ${val}    IN    @{告警内容}
        ${array}    Split String    ${val}    ,
        ${value}=    set variable if    '智能空开_1:智能空开下电告警' in '${array}[5]'    True    False
        Run keyword if    ${value}    Exit For Loop
    END
    should be true    ${value}
    [Teardown]    设置web设备参数量为默认值    <<智能空开远程下电使能-1~0x2c001050190001>>    <<智能空开下电使能-1~0x2c001050010001>>
    ...    下电模式

定时下电时进行远程上下电测试
    设置web参数量    <<智能空开定时下电使能-1~0x2c001050110001>>    允许
    sleep    10
    ${系统时间}    获取系统时间
    ${起始时间}    add time to date    ${系统时间}    00:02:00    result_format=%H:%M    exclude_millis=yes
    ${结束时间}    add time to date    ${系统时间}    00:05:00    result_format=%H:%M    exclude_millis=yes
    设置web参数量    <<智能空开定时下电起始时刻1-1~0x2c001050250001>>    ${起始时间}
    设置web参数量    <<智能空开定时下电终止时刻1-1~0x2c001050260001>>    ${结束时间}
    ${开始时间}    获取系统时间
    Wait Until Keyword Succeeds    3m    10    判断告警存在    智能空开下电告警-1
    设置web参数量    <<智能空开远程下电使能-1~0x2c001050190001>>    允许
    sleep    10
    设置web参数量    <<智能空开远程下电-1~0x2c0010501a0001>>    下电
    Wait Until Keyword Succeeds    3m    10    判断告警存在    智能空开下电告警-1
    设置web参数量    <<智能空开远程下电-1~0x2c0010501a0001>>    上电
    sleep   15
    Wait Until Keyword Succeeds    3m    10    判断告警存在    智能空开下电告警-1
    Wait Until Keyword Succeeds    5m    10    判断告警不存在    智能空开下电告警-1
    ${结束时间}    获取系统时间
    [Teardown]     设置web设备参数量为默认值    <<智能空开定时下电起始时刻1-1~0x2c001050250001>>    
    ...    <<智能空开定时下电终止时刻1-1~0x2c001050260001>>    <<智能空开远程下电使能-1~0x2c001050190001>>    
    ...    <<智能空开定时下电使能-1~0x2c001050110001>>


*** Keywords ***
判断智能空开下电状态和回路状态
    [Arguments]    ${下电状态}    ${回路状态}
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    <<智能空开回路状态-1~0x2c001020010001>>    ${回路状态}
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    <<智能空开下电状态-1~0x2c001020020001>>    ${下电状态}