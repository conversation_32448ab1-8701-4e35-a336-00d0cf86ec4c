*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_获取SDDU模拟量
    [Tags]    3
    ${能源网管数据}    能源网管协议_获取数据    获取SDDU模拟量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取SDDU模拟量
    Should Be True    ${数据对比结果}

power_sm_0002_获取SDDU数字量
    [Tags]    3
    ${能源网管数据}    能源网管协议_获取数据    获取SDDU数字量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取SDDU数字量
    Should Be True    ${数据对比结果}

power_sm_0004_获取SDDU参数量
    ${能源网管数据}    能源网管协议_获取数据    获取SDDU参数量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取SDDU参数量
    Should Be True    ${数据对比结果}

power_sm_0006_设置SDDU参数量
    [Setup]
    ${能源网管数据}    能源网管协议_设置单个参数    设置SDDU参数    ${BOARD_IP}    ${SSH}
    ${对比结果}    能源网管协议_批量对比参数设置    ${能源网管数据}    设置SDDU参数
    Should Be True    ${对比结果}

power_sm_0008_获取SDDU统计量
    ${能源网管数据}    能源网管协议_获取数据    获取SDDU统计量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取SDDU统计量
    Should Be True    ${数据对比结果}

power_sm_0010_设置SDDU控制量
    ${sheet_name}    Set Variable    设置SDDU控制量
    ${能源网管数据}    能源网管协议_设置控制量    ${sheet_name}    ${BOARD_IP}    ${SSH}
    ${对比结果}    能源网管协议_批量对比参数设置    ${能源网管数据}    设置SDDU控制量
    Should Be True    ${对比结果}
    [Teardown]     SDDU控制后置条件
