*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_获取直流模拟量
    [Documentation]    返回data数据为空，bin文件定义的所有直流模拟量均为传输标志为禁止传输，满足要求
    [Tags]    4    3
    ${能源网管数据}    能源网管协议_获取数据    获取直流配电模拟量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取直流配电模拟量
    Should Be True    ${数据对比结果}
    Comment    ${校验结果}    能源网管协议_校验数据上送完整性    ${能源网管数据}    直流配电    analog data
    Comment    Should Be True    ${校验结果}

power_sm_0002_获取直流数字量
    [Documentation]    返回data数据为空，bin文件定义的所有直流数字量都是禁止传输，满足要求
    [Tags]    4
    ${能源网管数据}    能源网管协议_获取数据    获取直流配电数字量    ${SSH}
    Should Be Equal As Strings    ${能源网管数据}    False
    Comment    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    Comment    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取直流配电数字量
    Comment    Should Be True    ${数据对比结果}

power_sm_0003_获取直流参数量
    [Tags]    4
    Wait Until Keyword Succeeds    5m   5    批量对比WEB和能源网管协议结果    获取直流配电参数量
    # ${能源网管数据}    能源网管协议_获取数据    获取直流配电参数量    ${SSH}
    # ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    # ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取直流配电参数量
    # Should Be True    ${数据对比结果}
    # Comment    ${校验结果}    能源网管协议_校验数据上送完整性    ${能源网管数据}    直流配电    parameter
    # Comment    Should Be True    ${校验结果}

power_sm_0004_设置直流参数量
    [Tags]    4
    ${sheet_name}    Set Variable    设置直流配电参数
    ${能源网管数据}    能源网管协议_设置单个参数    ${sheet_name}    ${BOARD_IP}    ${SSH}
    ${对比结果}    能源网管协议_批量对比参数设置    ${能源网管数据}    设置直流配电参数
    Should Be True    ${对比结果}

power_sm_0005_获取直流统计量
    [Tags]    3
    ${能源网管数据}    能源网管协议_获取数据    获取直流配电统计量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取直流配电统计量
    Should Be True    ${数据对比结果}
    Comment    ${校验结果}    能源网管协议_校验数据上送完整性    ${能源网管数据}    直流配电    stastic data
    Comment    Should Be True    ${校验结果}
