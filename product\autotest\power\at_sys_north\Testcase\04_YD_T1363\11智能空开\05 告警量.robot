*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
1363批量获取SSW告警量
    [Documentation]    ${协议类型} | ${子设备名称} | ${信号名称} | ${命令名称} | ${参数值} | ${文档名称} | ${设备中文名} | ${告警节点名} | ${vindex序号} | ${1104/1363表单名称} | ${1104/1363命令名称} | ${1104/1363数据名称} | ${屏号}=None
    [Setup]    主动告警测试前置条件    ${CSU_role}
    写入CSV文档    1363智能空开获取告警量测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=智能空开
    ${排除列表}    create list
    P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SSW排除告警量信号}    ${排除列表}    1
    ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取智能空开告警量~CID1=D3H>>    <<获取智能空开告警量~CID2=44H>>    None    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${1104待测}    1104_获取1104/1363指定设备指定信号量    ${协议数据}    ${web数据}    ${列表1}
    ${信号量列表}    create list
    FOR    ${i}    IN    @{1104待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${1104协议名称}    Get From Dictionary    ${i}    1104_name
        ${实时告警中的设备名称}    主动告警_组合实时告警中的设备名称    智能空开    ${1104协议名称}
	    ${dict1}          Create Dictionary 
        Set To Dictionary    ${dict1}     signal_name     ${信号名称}  
        Set To Dictionary    ${dict1}     alarm_name    ${1104协议名称} 
        Set To Dictionary    ${dict1}     device_name    ${实时告警中的设备名称} 
	    Append To List        ${信号量列表}    ${dict1}
    END
    南向RS485子设备告警量列表产生封装判断结果    1363    DMU_IntelAirSwit    ${信号量列表}    呼叫    ${告警产生}    
    ...    1363智能空开获取告警量测试    负载    智能空开    null    null    
    ...    <<获取智能空开告警量~CID1=D3H>>    <<获取智能空开告警量~CID2=44H>>    null
    南向RS485子设备告警量列表恢复封装判断结果    1363    DMU_IntelAirSwit    ${信号量列表}    呼叫    ${告警恢复}    
    ...    1363智能空开获取告警量测试    负载    智能空开    null    null    
    ...    <<获取智能空开告警量~CID1=D3H>>    <<获取智能空开告警量~CID2=44H>>    null
    [Teardown]    设置web设备参数量为默认值    CSU主动告警使能

1363获取智能空开通讯断告警&工作状态
    连接CSU
    ${级别设置值}    获取web参数量    智能空开通讯断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    智能空开通讯断    严重
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    智能空开通讯断-1
    设置子工具个数    DMU_IntelAirSwit    1
    FOR    ${智能空开序号}    IN RANGE    2    41
        Wait Until Keyword Succeeds    10m    5    信号量数据值为    智能空开工作状态-${智能空开序号}    通讯断
        wait until keyword succeeds    5m    1    判断告警存在    智能空开通讯断-${智能空开序号}
        1104/1363_南向子设备模拟量/数字量获取    3    1363    <<获取智能空开开关量~CID1=D3H>>    <<获取智能空开开关量~CID2=43H>>    智能空开工作状态-${智能空开序号}    None
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取智能空开告警量~CID1=D3H>>    <<获取智能空开告警量~CID2=44H>>    智能空开通讯断-${智能空开序号}    1    None    ${g_ver_1363}
        Should be true    ${1104告警结果}
    END
    设置子工具个数    DMU_IntelAirSwit    40
    FOR    ${智能空开序号}    IN RANGE    1    41
        Wait Until Keyword Succeeds    10m    5    信号量数据值为    智能空开工作状态-${智能空开序号}    正常
        wait until keyword succeeds    5m    1    判断告警不存在    智能空开通讯断-${智能空开序号}
        1104/1363_南向子设备模拟量/数字量获取    1    1363    <<获取智能空开开关量~CID1=D3H>>    <<获取智能空开开关量~CID2=43H>>    智能空开工作状态-${智能空开序号}    None
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取智能空开告警量~CID1=D3H>>    <<获取智能空开告警量~CID2=44H>>    智能空开通讯断-${智能空开序号}    0    None    ${g_ver_1363}
        Should be true    ${1104告警结果}
    END
    [Teardown]    Run Keywords    设置子工具个数    DMU_IntelAirSwit    40
    ...    AND    sleep    3min
