*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
交流电表电量清零
    [Documentation]    ${协议数据} | ${表单名称} | ${命令名称} | ${参数名} | ${设置值} | ${屏号}=None | ${g_ver}=
    FOR    ${交流电表序号}    IN RANGE    1    4
        ${交流电表序号}    Convert To String    ${交流电表序号}
        ${控制结果}    1104设置单个参数    ${g_prtcl_1363}    <<交流电表遥控~CID1=D2H>>    <<交流电表遥控~CID2=45H>>    交流电表电量清零    ${交流电表序号}    None    ${g_ver_1363}
        should be true    ${控制结果}
        sleep    20
        连接CSU
        @{历史事件内容}    获取web历史事件内容    ${empty}    ${empty}    所有    1    10
        ${是否清零}    Set Variable    False
        FOR    ${i}    IN    @{历史事件内容}
            ${是否清零}    Evaluate    "交流电表 _${交流电表序号} 交流电表电量清零" in "${i}"
            ${操作记录}    Set Variable    ${i}
            Exit For Loop If    ${是否清零}==True
        END
        Should Contain    ${操作记录}    north 1363
        Should Be True    ${是否清零}
    END
