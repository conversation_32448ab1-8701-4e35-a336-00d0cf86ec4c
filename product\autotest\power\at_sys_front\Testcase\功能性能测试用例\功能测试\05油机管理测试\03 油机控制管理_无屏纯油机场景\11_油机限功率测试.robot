*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
三相油机限功率测试
    [Tags]    notest
    油机管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    柴油发电机额定功率_1    4
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机带载率阈值    80
    Wait Until Keyword Succeeds    10    1    设置web参数量    三相油机功率因数    0.8
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动电压使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动SOC使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动时间使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动系统异常使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机定时启动使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止电压使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止SOC使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止电流使能    禁止
    ${设置值}    获取web参数量    油机启动电压
    ${停止电压}    获取web参数量    油机停止电压
    ${可设置范围}    获取web参数可设置范围    油机最短运行时间
    ${时间设置值}    evaluate    ${可设置范围}[0]+5
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机最短运行时间    ${时间设置值}
    ${最短运行时间设置值}    获取web参数量    油机最短运行时间
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    20
    打开负载输出
    ${设置电压}    evaluate    ${设置值}-0.5
    设置电池模拟器电压模式    铅酸电池    1    1    ${设置电压}
    run keyword and ignore error    wait until keyword succeeds    20    1    判断告警不存在    油机异常告警
    Wait Until Keyword Succeeds    14m    1    获取干接点状态直到变化为    ${油机干接点状态}    0    #干接点动作为1（断开），恢复为0（闭合）
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    15m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    运行
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    浮充    #浮充
    ${油机限功率上限}    evaluate    5000*0.8*0.8*0.95*1.1
    ${油机限功率下限}    evaluate    5000*0.8*0.8*0.95*0.9
    ${电池电流}    获取web实时数据    电池电流-1
    ${负载电流}    获取web实时数据    负载总电流
    ${整流器输出电流}    获取web实时数据    整流器总输出电流
    ${电压}    获取web实时数据    直流电压
    ${油机功率}    evaluate    ${电压}*${整流器输出电流}
    should be true    ${油机限功率下限}<${油机功率}<${油机限功率上限}
