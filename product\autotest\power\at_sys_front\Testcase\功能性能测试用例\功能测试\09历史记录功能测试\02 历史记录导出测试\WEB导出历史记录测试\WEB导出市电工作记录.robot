*** Settings ***
Suite Setup       设置历史记录最大条数    mains_work    6    100
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
WEB导出市电工作记录测试
    连接CSU
    ${数据条数1}    ${第一条数据内容1}    ${最新一条数据内容1}    查询指定历史记录    市电工作记录
    ${导出路径}    导出指定历史记录    市电工作记录    ${datapassword}
    ${数据条数2}    ${第一条数据内容2}    ${最新一条数据内容2}    解析指定历史记录文件    市电工作记录    ${导出路径}
    should be equal    ${第一条数据内容1}[0]    ${第一条数据内容2}[0]
    should be equal    ${第一条数据内容1}[1]    ${第一条数据内容2}[1]
    should contain    ${第一条数据内容1}[2]    ${第一条数据内容2}[2]
    should be equal    ${数据条数1}    ${数据条数2}
    should be equal    ${最新一条数据内容1}[0]    ${最新一条数据内容2}[0]
    should be equal    ${最新一条数据内容1}[1]    ${最新一条数据内容2}[1]
    should contain    ${最新一条数据内容1}[2]    ${最新一条数据内容2}[2]

中文界面下WEB导出市电工作记录测试
    连接交流源
    连接电池模拟器
    关闭电池模拟器输出
    关闭交流源输出
    设置电池模拟器模式    铅酸电池    24    5    50
    电池模拟器接触器分合    ON
    打开电池模拟器输出
    连接CSU
    ${开始数量}    获取web事件记录数量    市电工作记录
    run keyword if    ${开始数量}>998    删除历史记录    市电有电记录
    sleep    30
    ${开始数量}    获取web事件记录数量    市电工作记录
    #保证有1个整流器在线，再开始测试
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    在线整流器数量    1
    同时设置三相电压频率    220    50
    打开交流源输出
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流供电状态    市电1
    ${起始时间}    获取系统时间
    ${起始电量}    获取web实时数据    市电供电量
    ${起始工作时间}    获取web实时数据    市电工作时间
    sleep    3m
    关闭交流源输出
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    交流供电状态    市电1
    sleep    3m
    ${结束时间}    获取系统时间
    ${结束电量}    获取web实时数据    市电供电量
    ${结束工作时间}    获取web实时数据    市电工作时间
    ${工作时间}    evaluate    ${结束工作时间}-${起始工作时间}
    ${结束数量}    获取web事件记录数量    市电工作记录
    @{记录内容}    获取web事件记录最新一条    市电工作记录
    should be true    ${记录内容}[0] == ${结束数量}
    should be true    ${结束数量} == ${开始数量} + 1
    ${数据条数1}    ${第一条数据内容1}    ${最新一条数据内容1}    查询指定历史记录    市电工作记录
    ${导出路径}    导出指定历史记录    市电工作记录    ${datapassword}
    ${记录列表}    读CSV文档    ${测试用例地址}/file/download/${导出路径}/mains_on_rec.csv
    log    ${记录列表}[0]
    @{记录列表new}    split string    ${记录列表}[0]    ,
    should be equal    ${记录列表new}[0]    产生时间
    should be equal    ${记录列表new}[1]    恢复时间
    should be equal    ${记录列表new}[2]    市电_1:市电工作时间(Min)
