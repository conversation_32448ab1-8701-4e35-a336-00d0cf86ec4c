*** Settings ***
Suite Setup
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
光伏防雷器异常设置测试
    [Setup]    判断web参数是否存在    光伏防雷器异常
    FOR    ${VAR}    IN    严重    主要    次要    警告
    ...    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    光伏防雷器异常    ${VAR}
        sleep    2
        ${告警级别获取}    获取web参数量    光伏防雷器异常
        should be equal    ${告警级别获取}    ${VAR}
    END
    设置web参数量    光伏防雷器异常    主要
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    光伏防雷器异常干接点    ${VAR}
        ${告警干接点获取}    获取web参数量    光伏防雷器异常干接点
        should be true    ${告警干接点获取}==${VAR}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    光伏防雷器异常干接点    0
    ...    AND    设置web参数量    光伏防雷器异常    主要

光伏组件丢失设置测试
    [Setup]    判断web参数是否存在    光伏组件丢失
    FOR    ${VAR}    IN    严重    主要    次要    警告
    ...    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    光伏组件丢失    ${VAR}
        sleep    2
        ${告警级别获取}    获取web参数量    光伏组件丢失
        should be equal    ${告警级别获取}    ${VAR}
    END
    设置web参数量    光伏组件丢失    主要
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    光伏组件丢失干接点    ${VAR}
        ${告警干接点获取}    获取web参数量    光伏组件丢失干接点
        should be true    ${告警干接点获取}==${VAR}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    光伏组件丢失干接点    0
    ...    AND    设置web参数量    光伏组件丢失    屏蔽

所有PU模块通讯断告警设置测试
    [Setup]    判断web参数是否存在    所有PU模块通讯断告警
    FOR    ${VAR}    IN    严重    主要    次要    警告
    ...    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    所有PU模块通讯断告警    ${VAR}
        sleep    2
        ${告警级别获取}    获取web参数量    所有PU模块通讯断告警
        should be equal    ${告警级别获取}    ${VAR}
    END
    设置web参数量    所有PU模块通讯断告警    主要
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    所有PU模块通讯断告警干接点    ${VAR}
        ${告警干接点获取}    获取web参数量    所有PU模块通讯断告警干接点
        should be true    ${告警干接点获取}==${VAR}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    所有PU模块通讯断告警干接点    0
    ...    AND    设置web参数量    所有PU模块通讯断告警    主要

光伏回路异常告警设置测试
    [Setup]    判断web参数是否存在    光伏回路异常告警
    FOR    ${VAR}    IN    严重    主要    次要    警告
    ...    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    光伏回路异常告警    ${VAR}
        sleep    2
        ${光伏回路异常告警级别获取}    获取web参数量    光伏回路异常告警
        should be equal    ${光伏回路异常告警级别获取}    ${VAR}
    END
    设置web参数量    光伏回路异常告警    次要
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    光伏回路异常告警干接点    ${VAR}
        ${光伏回路异常告警干接点获取}    获取web参数量    光伏回路异常告警干接点
        should be true    ${光伏回路异常告警干接点获取}==${VAR}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    光伏回路异常告警干接点    0
    ...    AND    设置web参数量    光伏回路异常告警    次要

PU告警设置测试
    [Setup]    判断web参数是否存在    PU告警
    FOR    ${VAR}    IN    严重    主要    次要    警告
    ...    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    PU告警    ${VAR}
        sleep    2
        ${告警级别获取}    获取web参数量    PU告警
        should be equal    ${告警级别获取}    ${VAR}
    END
    设置web参数量    PU告警    主要
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    PU告警干接点    ${VAR}
        ${告警干接点获取}    获取web参数量    PU告警干接点
        should be true    ${告警干接点获取}==${VAR}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    PU告警干接点    0
    ...    AND    设置web参数量    PU告警    主要

PU通讯中断设置测试
    [Setup]    判断web参数是否存在    PU通讯中断
    FOR    ${VAR}    IN    严重    主要    次要    警告
    ...    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    PU通讯中断    ${VAR}
        sleep    2
        ${告警级别获取}    获取web参数量    PU通讯中断
        should be equal    ${告警级别获取}    ${VAR}
    END
    设置web参数量    PU通讯中断    主要
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    PU通讯中断干接点    ${VAR}
        ${告警干接点获取}    获取web参数量    PU通讯中断干接点
        should be true    ${告警干接点获取}==${VAR}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    PU通讯中断干接点    0
    ...    AND    设置web参数量    PU通讯中断    主要

PU故障设置测试
    [Setup]    判断web参数是否存在    PU故障
    FOR    ${VAR}    IN    严重    主要    次要    警告
    ...    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    PU故障    ${VAR}
        sleep    2
        ${告警级别获取}    获取web参数量    PU故障
        should be equal    ${告警级别获取}    ${VAR}
    END
    设置web参数量    PU故障    主要
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    PU故障干接点    ${VAR}
        ${告警干接点获取}    获取web参数量    PU故障干接点
        should be true    ${告警干接点获取}==${VAR}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    PU故障干接点    0
    ...    AND    设置web参数量    PU故障    主要
