*** Settings ***
Resource          ../../ztepwrlibrary/Variable/仪器全局变量.robot
Resource          ../../ztepwrlibrary/Variable/V30CSU全局变量.robot
Resource          ../../ztepwrlibrary/Resource/V30CSU关键字/Web关键字_V30.robot
Resource          ../../ztepwrlibrary/Resource/V30CSU关键字/CSU_WEB安全接口关键字_V30.robot
Resource          ../../ztepwrlibrary/Resource/模拟子工具关键字.robot
Resource          ../../ztepwrlibrary/Resource/执行时间及测试报告生成.robot
Resource          ../../ztepwrlibrary/Resource/V30CSU关键字/配置文件校验关键字.robot
Resource          ../../ztepwrlibrary/Resource/DAM网络继电器关键字.robot
Resource          ../../ztepwrlibrary/Resource/调度关键字/场景关键字.robot
Resource          ../../ztepwrlibrary/Resource/调度关键字/电池模拟器关键字.robot
Resource          ../../ztepwrlibrary/Resource/调度关键字/负载模拟器关键字.robot
Resource          ../../ztepwrlibrary/Resource/调度关键字/交流源关键字.robot

*** Variables ***
${g_干接点起始编号}      2
${g_干接点结束编号}      4
${输出干接点状态}        SPB_J2_DI1
${油机干接点状态}        SPB_J16_DI7
${铁锂电池组数}         32
@{铅酸电池排除信号}       租户名称~0xa001050070001    市电电池优先级设置~0x20010500f0001    市电启动电压使能~0x6001050050001    市电启动SOC使能~0x6001050060001    市电启动时间使能~0x6001050070001    市电启动放电时间阈值~0x60010500b0001    市电停止电压使能~0x60010500c0001    市电停止SOC使能~0x60010500d0001    市电停止电流使能~0x60010500e0001    市电最短运行时间~0x6001050120001    市电最长运行时间~0x6001050130001    整流器轮换周期~0x8001050020001    暂时非节能延时时间~0x8001050040001    公共负载配置~0xa001050080001    负载一次下电时间~0xc001050290001    负载二次下电时间~0xc0010502d0001    电池下电时间~0xc001050310001
...               BMS运行模式~0xc0010507d0001    锂电应用场景~0xc0010507e0001    BMS升压充电~0xc001050870001    长充启动周期~0xc001050880001    长充最大时长~0xc0010508a0001    周期长充启动时刻~0xc001050900001    整流器节能关闭最大电流~0x2001050100001    市电启动电压阈值~0x6001050090001    市电启动SOC阈值~0x60010500a0001    市电停止电压~0x60010500f0001    市电关闭SOC阈值~0x6001050100001    市电关闭电池电流~0x6001050110001    负载一次下电SOC阈值~0xc0010502b0001    负载二次下电SOC阈值~0xc0010502f0001    电池下电SOC阈值~0xc001050330001    电池免温补上限~0xc001050450001    长充关闭电池电流~0xc001050890001
...               单相油机功率因数~0x180010501c0001    直流负载回路断~0xa001030020001    直流负载扩展分路断~0xa001030030001    租户一次下电分路断~0xa001030110001    租户二次下电分路断~0xa001030120001    租户电池下电分路断~0xa001030130001    公用二次下电分路断~0xa001030140001    公用电池下电分路断~0xa001030150001    交流输入场景~0x2001050040001    电池配置~0x2001050090001    铅酸类型~0x20010500b0001    锂电类型~0x20010500a0001    常规锂电类型~0x2001050140001    配电单元名称~0xa001050100001    直流配电单元下电使能~0xa001050090001    直流配电单元远程下电使能~0xa0010500d0001    直流配电单元远程下电~0xa0010500e0001
...               配电单元租户设置~0xa0010500f0001    直流配电单元下电电压~0xa0010500b0001    直流配电单元下电SOC阈值~0xa0010500c0001    直流配电单元下电告警~0xa001030190001    直流配电单元下电时间~0xa0010500a0001    充电电压~0xc0010506d0001    配电单元定时下电使能~0xa001050170001    配电单元定时下电起始时刻1~0xa001050180001    配电单元定时下电终止时刻1~0xa001050190001    配电单元定时下电起始时刻2~0xa0010501a0001    配电单元定时下电终止时刻2~0xa0010501b0001    配电单元定时下电起始时刻3~0xa0010501c0001    配电单元定时下电终止时刻3~0xa0010501d0001    配电单元定时下电起始时刻4~0xa0010501e0001    配电单元定时下电终止时刻4~0xa0010501f0001    直流输出低关机使能~0xa001050140001    直流电压检测间隔~0xa001050130001
...               直流输出回路断阈值~0xa001050110001    直流输出过压保护阈值~0xa001050120001    直流输出最低电压阈值~0xa001050150001    PU默认设定电压~0xa001050160001    母排基准电压偏差阈值~0xa001050200001    直流输出最高电压阈值~0xa001050210001    直流输出回路断~0xa0010301a0001    直流输出过压保护~0xa0010301b0001    直流备用输入配置~0x2001050080001    交流电表回路1电流斜率~0x1e001050090001    交流电表回路1电>压斜率~0x1e0010500b0001    交流电表回路2电流斜率~0x1e0010500d0001    交流电表回路2电压斜率~0x1e0010500f0001    交流电表回路3电流斜率~0x1e001050110001    交流电表回路3电压斜率~0x1e001050130001    交流电表回路4电流斜率~0x1e001050150001    交流电表回路4电压斜率~0x1e001050170001    交流电表回路1电流零点~0x1e0010500a0001    交流电表回路1电压零点~0x1e0010500c0001
...               交流电表回路2电流零点~0x1e0010500e0001    交流电表>回路2电压零点~0x1e001050100001    交流电表回路3电流零点~0x1e001050120001    交流电表回路3电压零点~0x1e001050140001    交流电表回路4电流零点~0x1e001050160001    交流电表回路4电压零点~0x1e001050180001    尖峰>时段充电系数~0xc001050da0001    高峰时段充电系数~0xc001050db0001    平期时段充电系数~0xc001050dc0001    风扇配置_1~0x90010500c0001    风扇配置_2~0x90010500c0001    错峰时段类型1~0xc001050e40001    错峰时段类型2~0xc001050e50001    错峰时段类型3~0xc001050e60001    错峰时段类型4~0xc001050e70001    错峰时段类型5~0xc001050e80001    电池充电模式~0xc0010503e0001    错峰启动电压~0xc001050d40001    峰期错峰终止电压~0xc001050d60001    峰期错峰终止容量~0xc001050d70001    平期错峰终止电压~0xc001050d80001    平期错峰终止容量~0xc001050d90001    谷期时段充电系数~0xc001050dd0001    风扇PWM~0x90010500b0001
...               交流输出空开断~0x4001030030001    整流模块限流比率~0x8001050250001    扩展温度高阈值~0x9001050080001    扩展温度无效~0x9001030110001    扩展温度高~0x9001030120001    扩展温度低~0x9001030130001
...               扩展温度低阈值~0x9001050090001    周末错峰用电使能~0xc001050ec0001    远供场景配置异常~0xc0010300d0001    智能锂电池应用场景~0xc001050ea0001    远供放电输出电压~0xc001050810001
@{共享共建排除信号}       租户名称~0xa001050070001    市电电池优先级设置~0x20010500f0001    市电启动电压使能~0x6001050050001    市电启动SOC使能~0x6001050060001    市电启动时间使能~0x6001050070001    市电启动放电时间阈值~0x60010500b0001    市电停止电压使能~0x60010500c0001    市电停止SOC使能~0x60010500d0001    市电停止电流使能~0x60010500e0001    市电最短运行时间~0x6001050120001    市电最长运行时间~0x6001050130001    整流器轮换周期~0x8001050020001    暂时非节能延时时间~0x8001050040001    公共负载配置~0xa001050080001    负载一次下电时间~0xc001050290001    负载二次下电时间~0xc0010502d0001    电池下电时间~0xc001050310001
...               BMS运行模式~0xc0010507d0001    锂电应用场景~0xc0010507e0001    BMS升压充电~0xc001050870001    长充启动周期~0xc001050880001    长充最大时长~0xc0010508a0001    周期长充启动时刻~0xc001050900001    整流器节能关闭最大电流~0x2001050100001    市电启动电压阈值~0x6001050090001    市电启动SOC阈值~0x60010500a0001    市电停止电压~0x60010500f0001    市电关闭SOC阈值~0x6001050100001    市电关闭电池电流~0x6001050110001    负载一次下电SOC阈值~0xc0010502b0001    负载二次下电SOC阈值~0xc0010502f0001    电池下电SOC阈值~0xc001050330001    电池免温补上限~0xc001050450001    长充关闭电池电流~0xc001050890001
...               单相油机功率因数~0x180010501c0001    直流负载回路断~0xa001030020001    直流负载扩展分路断~0xa001030030001    租户一次下电分路断~0xa001030110001    租户二次下电分路断~0xa001030120001    租户电池下电分路断~0xa001030130001    公用二次下电分路断~0xa001030140001    公用电池下电分路断~0xa001030150001    交流输入场景~0x2001050040001    电池配置~0x2001050090001    铅酸类型~0x20010500b0001    锂电类型~0x20010500a0001    常规锂电类型~0x2001050140001    一次下电分路断~0xa001030060001    一次下电扩展分路断~0xa001030070001    二次下电分路断~0xa001030080001    充电电压~0xc0010506d0001
...               直流配电单元下电时间~0xa0010500a0001    直流配电单元下电SOC阈值~0xa0010500c0001    电池下电分路断~0xa0010300a0001    电池下电扩展分路断~0xa0010300b0001    一次下电告警~0xc001030010001    负载高温下电~0xc0010300b0001    负载低温下电~0xc0010300c0001    负载一次下电使能~0xc001050280001    远程一次下电使能~0xc001050480001    远程一次下电~0xc0010504b0001    负载低温下电使能~0xc001050b00001    负载高温下电使能~0xc001050b20001    LHTD判断时间~0xc001050b50001    负载电流斜率~0xa001050030001    负载电流零点~0xa001050040001    负载低温下电温度~0xc001050b10001    负载高温下电温度~0xc001050b30001
...               负载高温下电恢复温度~0xc001050b40001    二次下电扩展分路断~0xa001030090001    负载一次下电电压~0xc0010502a0001    直流输出最高电压阈值~0xa001050210001    母排基准电压偏差阈值~0xa001050200001    PU默认设定电压~0xa001050160001    直流输出过压保护阈值~0xa001050120001    直流输出最低电压阈值~0xa001050150001    直流电压检测间隔~0xa001050130001    直流输出回路断阈值~0xa001050110001    直流输出低关机使能~0xa001050140001    定时一次下电使能~0xc001050c00001    定时一次下电终止时刻~0xc001050c20001    定时一次下电起始时刻~0xc001050c10001    直流输出回路断~0xa0010301a0001    直流输出过压保护~0xa0010301b0001    一次下电恢复回差~0xc0010508b0001
...               一次下电恢复时间~0xc0010508e0001    配电单元定时下电起始时刻4~0xa0010501e0001    配电单元定时下电终止时刻4~0xa0010501f0001
@{排除设备}           高温电池    ATS    铅酸电池25    智能直流配电单元    智能空开    直流配电
@{循环场景排除信号}       租户名称~0xa001050070001    市电电池优先级设置~0x20010500f0001    <<电池检测周期~0xc0010500b0001>>    <<电池测试周期~0xc001050110001>>    <<电池测试启动时刻~0xc0010503c0001>>    <<测试终止电压~0xc001050040001>>    <<测试最长时间~0xc001050120001>>    <<电池检测持续时间~0xc001050350001>>    公共负载配置~0xa001050080001    <<测试失败SOC阈值~0xc001050140001>>    <<电池测试失败~0xb0010300a0001>>    整流器轮换周期~0x8001050020001    <<测试终止SOC阈值~0xc001050130001>>    铅酸类型~0x20010500b0001    电池配置~0x2001050090001    锂电类型~0x20010500a0001    负载一次下电时间~0xc001050290001
...               负载二次下电时间~0xc0010502d0001    暂时非节能延时时间~0x8001050040001    租户二次下电分路断~0xa001030120001    电池下电时间~0xc001050310001    BMS运行模式~0xc0010507d0001    锂电应用场景~0xc0010507e0001    BMS升压充电~0xc001050870001    租户电池下电分路断~0xa001030130001    <<电池测试~0xc001030060001>>    负载一次下电SOC阈值~0xc0010502b0001    负载二次下电SOC阈值~0xc0010502f0001    电池下电SOC阈值~0xc001050330001    公用二次下电分路断~0xa001030140001    电池免温补上限~0xc001050450001    单相油机功率因数~0x180010501c0001    租户一次下电分路断~0xa001030110001    直流负载回路断~0xa001030020001
...               交流输入场景~0x2001050040001    直流负载扩展分路断~0xa001030030001    公用电池下电分路断~0xa001030150001    电池电流温度补偿系数~0xc001050390001    常规锂电类型~0x2001050140001    直流配电单元下电时间~0xa0010500a0001    直流配电单元下电电压~0xa0010500b0001    直流配电单元下电SOC阈值~0xa0010500c0001    直流配电单元下电告警~0xa001030190001    配电单元名称~0xa001050100001    直流配电单元下电使能~0xa001050090001    直流配电单元远程下电使能~0xa0010500d0001    直流配电单元远程下电~0xa0010500e0001    配电单元租户设置~0xa0010500f0001    充电电压~0xc0010506d0001    配电单元定时下电起始时刻1~0xa001050180001    配电单元定时下电终止时刻1~0xa001050190001
...               配电单元定时下电起始时刻2~0xa0010501a0001    配电单元定时下电终止时刻2~0xa0010501b0001    配电单元定时下电起始时刻3~0xa0010501c0001    配电单元定时下电终止时刻3~0xa0010501d0001    配电单元定时下电起始时刻4~0xa0010501e0001    配电单元定时下电终止时刻4~0xa0010501f0001    直流输出低关机使能~0xa001050140001    直流电压检测间隔~0xa001050130001    直流输出回路断阈值~0xa001050110001    直流输出过压保护阈值~0xa001050120001    直流输出最低电压阈值~0xa001050150001    PU默认设定电压~0xa001050160001    母排基准电压偏差阈值~0xa001050200001    直流输出最高电压阈值~0xa001050210001    配电单元定时下电使能~0xa001050170001    直流输出回路断~0xa0010301a0001    直流输出过压保护~0xa0010301b0001
@{快充电池和储能电池排除信号}    租户名称~0xa001050070001    市电电池优先级设置~0x20010500f0001    市电启动电压使能~0x6001050050001    市电启动SOC使能~0x6001050060001    市电启动时间使能~0x6001050070001    市电启动放电时间阈值~0x60010500b0001    市电停止电压使能~0x60010500c0001    市电停止SOC使能~0x60010500d0001    市电停止电流使能~0x60010500e0001    市电最短运行时间~0x6001050120001    市电最长运行时间~0x6001050130001    整流器轮换周期~0x8001050020001    暂时非节能延时时间~0x8001050040001    公共负载配置~0xa001050080001    负载一次下电时间~0xc001050290001    负载二次下电时间~0xc0010502d0001    电池下电时间~0xc001050310001
...               BMS运行模式~0xc0010507d0001    锂电应用场景~0xc0010507e0001    BMS升压充电~0xc001050870001    长充启动周期~0xc001050880001    长充最大时长~0xc0010508a0001    周期长充启动时刻~0xc001050900001    整流器节能关闭最大电流~0x2001050100001    市电启动电压阈值~0x6001050090001    市电启动SOC阈值~0x60010500a0001    市电停止电压~0x60010500f0001    市电关闭SOC阈值~0x6001050100001    市电关闭电池电流~0x6001050110001    负载一次下电SOC阈值~0xc0010502b0001    负载二次下电SOC阈值~0xc0010502f0001    电池下电SOC阈值~0xc001050330001    电池免温补上限~0xc001050450001    长充关闭电池电流~0xc001050890001
...               单相油机功率因数~0x180010501c0001    直流负载回路断~0xa001030020001    直流负载扩展分路断~0xa001030030001    租户一次下电分路断~0xa001030110001    租户二次下电分路断~0xa001030120001    租户电池下电分路断~0xa001030130001    公用二次下电分路断~0xa001030140001    公用电池下电分路断~0xa001030150001    交流输入场景~0x2001050040001    电池配置~0x2001050090001    铅酸类型~0x20010500b0001    锂电类型~0x20010500a0001    电池电流温度补偿系数~0xc001050390001    常规锂电类型~0x2001050140001    直流配电单元远程下电使能~0xa0010500d0001    直流配电单元远程下电~0xa0010500e0001    配电单元租户设置~0xa0010500f0001
...               直流配电单元下电时间~0xa0010500a0001    直流配电单元下电电压~0xa0010500b0001    直流配电单元下电SOC阈值~0xa0010500c0001    直流配电单元下电告警~0xa001030190001    配电单元名称~0xa001050100001    直流配电单元下电使能~0xa001050090001    充电电压~0xc0010506d0001    配电单元定时下电起始时刻1~0xa001050180001    配电单元定时下电终止时刻1~0xa001050190001    配电单元定时下电起始时刻2~0xa0010501a0001    配电单元定时下电终止时刻2~0xa0010501b0001    配电单元定时下电起始时刻3~0xa0010501c0001    配电单元定时下电终止时刻3~0xa0010501d0001    配电单元定时下电起始时刻4~0xa0010501e0001    配电单元定时下电终止时刻4~0xa0010501f0001    直流输出低关机使能~0xa001050140001    直流电压检测间隔~0xa001050130001
...               直流输出回路断阈值~0xa001050110001    直流输出过压保护阈值~0xa001050120001    直流输出最低电压阈值~0xa001050150001    PU默认设定电压~0xa001050160001    母排基准电压偏差阈值~0xa001050200001    直流输出最高电压阈值~0xa001050210001    配电单元定时下电使能~0xa001050170001    直流输出回路断~0xa0010301a0001    直流输出过压保护~0xa0010301b0001
@{FB100B3排除数字量信号}    BMS工作状态~0x17001020340001    电池运行状态~0x17001020020001    电池充电过流保护状态~0x17001020050001    电池放电过流保护状态~0x17001020060001    电池过压保护状态~0x17001020070001    单板过温保护状态~0x17001020080001    电池充电过流告警状态~0x17001020090001    电池放电过流告警状态~0x170010200a0001    电池过压告警状态~0x170010200b0001    机内环境温度高告警状态~0x170010200c0001    机内环境温度低告警状态~0x170010200d0001    电池欠压告警状态~0x170010200e0001    电池欠压保护状态~0x170010200f0001    单体过压告警状态~0x17001020100001    单体过压保护状态~0x17001020110001    单体欠压告警状态~0x17001020120001    单体欠压保护状态~0x17001020130001
...               单体充电高温告警状态~0x17001020140001    单体充电高温保护状态~0x17001020150001    单体放电高温告警状态~0x17001020160001    单体放电高温保护状态~0x17001020170001    单体充电低温告警状态~0x17001020180001    单体充电低温保护状态~0x17001020190001    单体放电低温告警状态~0x170010201a0001    单体放电低温保护状态~0x170010201b0001    单体落后电压差状态~0x170010201c0001    单体落后保护电压差状态~0x170010201d0001    电池SOC低告警状态~0x170010201e0001    电池SOC低保护状态~0x170010201f0001    电池SOH告警状态~0x17001020200001    电池SOH保护状态~0x17001020210001    单体损坏保护状态~0x17001020220001    放电回路开关失效告警状态~0x17001020250001    充电回路开关失效告警状态~0x17001020240001
...               限流回路失效告警状态~0x17001020260001    短路保护状态~0x17001020270001    电池反接告警状态~0x17001020280001    单体温度传感器失效告警状态~0x17001020290001    机内过温保护告警状态~0x170010202a0001    BDCU电池欠压保护状态~0x170010202b0001    BDCU母排欠压保护告警状态~0x170010202c0001    BDCU EEPROM故障告警状态~0x170010202d0001    单体温度异常状态~0x170010202e0001    地址冲突告警状态~0x170010202f0001    振动告警状态~0x17001020300001    单体电压采样异常告警状态~0x17001020310001    BDCU通信断告警状态~0x17001020320001    BMS通信断状态~0x17001020330001    BMS工作状态~0x17001020340001    BMS在位状态~0x17001020350001    BDCU母排过压保护告警状态~0x17001020360001
...               电压采样故障状态~0x17001020380001    回路异常状态~0x170010203d0001    BDCU电池充电欠压保护状态~0x170010203e0001    环境温度高保护状态~0x17001020400001    环境温度低保护状态~0x17001020410001    单板过温告警状态~0x17001020420001
@{FB100B3排除告警量信号}    BMS通信断告警~0x170010302f0001
@{FB100B3排除模拟量信号}
${告警产生}           1
${告警恢复}           0
@{FB100B3排除设备版本信息}    BMS系统名称~0x17001080010001    BDCU名称~0x17001080050001    BMS序列号~0x17001080040001    BDCU序列号~0x17001080090001    电池模组序列号~0x170010800b0001    整机序列号~0x170010800e0001
@{FB100B3排除参数量信号}    充电电压~0xc0010506d0001    锂电充电电流系数~0xc001050740001    BMS运行模式~0xc0010507d0001    锂电应用场景~0xc0010507e0001    BMS升压充电~0xc001050870001    直通放电使能~0xc001050bf0001
@{整流器排除模拟量信号}     整流器组地址~0x70010100b0001    整流器组内地址~0x70010100c0001    整流器槽位地址~0x70010100d0001    整流器输入电压~0x7001010050001
@{整流器排除设备名称信息}    整流器条码~0x70010800b0001    整流器ID~0x7001080010001    整流器前级版本~0x7001080030001    整流器后级软件版本~0x7001080040001    整流器数控平台版本~0x7001080070001
@{整流器排除设备版本信息}    整流器条码~0x70010800b0001    整流器ID~0x7001080010001    整流器系统名称~0x7001080080001
@{整流器排除设备日期信息}    生产日期~0x70010800c0001
@{整流器排除数字量信号}     整流器交流输入相位~0x7001020090001    整流器通讯状态~0x70010200e0001    整流器工作状态~0x70010200c0001
@{整流器排除告警量信号}     整流器通讯中断~0x70010301d0001    整流器故障~0x7001030270001    整流器告警~0x7001030010001    整流器输出过压关机~0x7001030060001    整流器输出过流~0x7001030070001    整流器输出熔丝断~0x70010300a0001    整流器EEPROM故障~0x70010300f0001    整流器机内通讯断~0x7001030100001    整流器缓启动异常~0x7001030130001    整流器输入熔丝断或机内通讯断~0x7001030140001    整流器PFC故障~0x7001030080001    整流器输出过载~0x7001030170001    整流器机型不匹配~0x70010301a0001    整流器机外温度过高~0x70010301b0001    整流器风扇故障~0x7001030020001    整流器Bus不平衡~0x7001030260001    整流器输入反接~0x70010302f0001
@{三相整流器排除告警量信号}    整流器通讯中断~0x70010301d0001    整流器故障~0x7001030270001    整流器告警~0x7001030010001    整流器输出过压关机~0x7001030060001    整流器输出过流~0x7001030070001    整流器输出熔丝断~0x70010300a0001    整流器EEPROM故障~0x70010300f0001    整流器机内通讯断~0x7001030100001    整流器缓启动异常~0x7001030130001    整流器输入熔丝断或机内通讯断~0x7001030140001    整流器PFC故障~0x7001030080001    整流器输出过载~0x7001030170001    整流器机型不匹配~0x70010301a0001    整流器机外温度过高~0x70010301b0001    整流器风扇故障~0x7001030020001    整流器Bus不平衡~0x7001030260001    整流器输入反接~0x70010302f0001
...               整流器交流过压关机~0x7001030040001    整流器交流欠压关机~0x7001030050001    整流器交流输入断~0x70010300c0001    整流器输入频率异常~0x7001030150001    整流器输入电压不平衡~0x7001030280001    整流器缺相或输入熔丝断~0x7001030290001    整流器未到位~0x70010302a0001
@{三相整流器排除告警量信号0告警}    整流器通讯中断~0x70010301d0001    整流器故障~0x7001030270001    整流器告警~0x7001030010001    整流器输出过压关机~0x7001030060001    整流器输出过流~0x7001030070001    整流器输出熔丝断~0x70010300a0001    整流器EEPROM故障~0x70010300f0001    整流器机内通讯断~0x7001030100001    整流器缓启动异常~0x7001030130001    整流器输入熔丝断或机内通讯断~0x7001030140001    整流器PFC故障~0x7001030080001    整流器输出过载~0x7001030170001    整流器机型不匹配~0x70010301a0001    整流器机外温度过高~0x70010301b0001    整流器风扇故障~0x7001030020001    整流器Bus不平衡~0x7001030260001    整流器输入反接~0x70010302f0001
...               整流器交流过压关机~0x7001030040001    整流器交流欠压关机~0x7001030050001    整流器交流输入断~0x70010300c0001    整流器输入频率异常~0x7001030150001    整流器输入电压不平衡~0x7001030280001    整流器缺相或输入熔丝断~0x7001030290001    整流器散热器过温关机~0x7001030030001    整流器交流过压关机~0x7001030040001    整流器交流欠压关机~0x7001030050001    整流器机内温度过高~0x7001030090001    整流器均流不良~0x70010300b0001    整流器交流输入断~0x70010300c0001    整流器PFC输出过压告警~0x70010300d0001    整流器PFC输出欠压告警~0x70010300e0001    整流器原边过流~0x7001030110001    整流器PFC输入过流~0x7001030120001    整流器输入频率异常~0x7001030150001
...               整流器输出欠压~0x7001030160001    整流器序列号冲突~0x7001030180001    整流器协议错误~0x7001030190001    启动电阻过热告警~0x70010301c0001    整流器Bus1欠压~0x70010301e0001    整流器Bus2欠压~0x70010301f0001    整流器Bus3欠压~0x7001030200001    整流器Bus4欠压~0x7001030210001    整流器Bus1过压~0x7001030220001    整流器Bus2过压~0x7001030230001    整流器Bus3过压~0x7001030240001    整流器Bus4过压~0x7001030250001    整流器输出欠压预警~0x70010302b0001    整流器机内温度过温预警~0x7001030310001    整流器输入过压预警~0x70010302e0001    整流器输入欠压预警~0x70010302d0001    整流器输出过压预警~0x70010302c0001
...               整流器输出过流预警~0x7001030300001
@{整流器排除故障量信号}     整流器散热器过温关机~0x7001030030001    整流器交流过压关机~0x7001030040001    整流器交流欠压关机~0x7001030050001    整流器机内温度过高~0x7001030090001    整流器均流不良~0x70010300b0001    整流器交流输入断~0x70010300c0001    整流器PFC输出过压告警~0x70010300d0001    整流器PFC输出欠压告警~0x70010300e0001    整流器原边过流~0x7001030110001    整流器PFC输入过流~0x7001030120001    整流器输入频率异常~0x7001030150001    整流器输出欠压~0x7001030160001    整流器序列号冲突~0x7001030180001    整流器协议错误~0x7001030190001    启动电阻过热告警~0x70010301c0001    整流器Bus1欠压~0x70010301e0001    整流器Bus2欠压~0x70010301f0001
...               整流器Bus3欠压~0x7001030200001    整流器Bus4欠压~0x7001030210001    整流器Bus1过压~0x7001030220001    整流器Bus2过压~0x7001030230001    整流器Bus3过压~0x7001030240001    整流器Bus4过压~0x7001030250001    整流器输出欠压预警~0x70010302b0001    整流器故障~0x7001030270001    整流器告警~0x7001030010001    整流器通讯中断~0x70010301d0001    整流器机内温度过温预警~0x7001030310001    整流器输出过流预警~0x7001030300001    整流器输入过压预警~0x70010302e0001    整流器输入欠压预警~0x70010302d0001    整流器输出过压预警~0x70010302c0001    整流器风扇故障~0x7001030020001
@{三相整流器排除故障量信号}    整流器散热器过温关机~0x7001030030001    整流器输出过压预警~0x70010302c0001    整流器输入欠压预警~0x70010302d0001    整流器机内温度过高~0x7001030090001    整流器均流不良~0x70010300b0001    整流器输入过压预警~0x70010302e0001    整流器PFC输出过压告警~0x70010300d0001    整流器PFC输出欠压告警~0x70010300e0001    整流器原边过流~0x7001030110001    整流器PFC输入过流~0x7001030120001    整流器输出过流预警~0x7001030300001    整流器输出欠压~0x7001030160001    整流器序列号冲突~0x7001030180001    整流器协议错误~0x7001030190001    启动电阻过热告警~0x70010301c0001    整流器Bus1欠压~0x70010301e0001    整流器Bus2欠压~0x70010301f0001
...               整流器Bus3欠压~0x7001030200001    整流器Bus4欠压~0x7001030210001    整流器Bus1过压~0x7001030220001    整流器Bus2过压~0x7001030230001    整流器Bus3过压~0x7001030240001    整流器Bus4过压~0x7001030250001    整流器输出欠压预警~0x70010302b0001    整流器故障~0x7001030270001    整流器告警~0x7001030010001    整流器通讯中断~0x70010301d0001    整流器机内温度过温预警~0x7001030310001    整流器未到位~0x70010302a0001
@{FB100B1排除模拟量信号}    充电剩余时间~0x170010100b0001    放电剩余时间~0x170010100c0001
@{FB100B1排除数字量信号}    BMS工作状态~0x17001020340001    电池运行状态~0x17001020020001    电池充电过流保护状态~0x17001020050001    电池放电过流保护状态~0x17001020060001    电池过压保护状态~0x17001020070001    单板过温保护状态~0x17001020080001    电池充电过流告警状态~0x17001020090001    电池放电过流告警状态~0x170010200a0001    电池过压告警状态~0x170010200b0001    机内环境温度高告警状态~0x170010200c0001    机内环境温度低告警状态~0x170010200d0001    电池欠压告警状态~0x170010200e0001    电池欠压保护状态~0x170010200f0001    单体过压告警状态~0x17001020100001    单体过压保护状态~0x17001020110001    单体欠压告警状态~0x17001020120001    单体欠压保护状态~0x17001020130001
...               单体充电高温告警状态~0x17001020140001    单体充电高温保护状态~0x17001020150001    单体放电高温告警状态~0x17001020160001    单体放电高温保护状态~0x17001020170001    单体充电低温告警状态~0x17001020180001    单体充电低温保护状态~0x17001020190001    单体放电低温告警状态~0x170010201a0001    单体放电低温保护状态~0x170010201b0001    单体落后电压差状态~0x170010201c0001    单体落后保护电压差状态~0x170010201d0001    电池SOC低告警状态~0x170010201e0001    电池SOC低保护状态~0x170010201f0001    电池SOH告警状态~0x17001020200001    电池SOH保护状态~0x17001020210001    单体损坏保护状态~0x17001020220001    放电回路开关失效告警状态~0x17001020250001    充电回路开关失效告警状态~0x17001020240001
...               限流回路失效告警状态~0x17001020260001    短路保护状态~0x17001020270001    电池反接告警状态~0x17001020280001    单体温度传感器失效告警状态~0x17001020290001    机内过温保护告警状态~0x170010202a0001    BDCU电池欠压保护状态~0x170010202b0001    BDCU母排欠压保护告警状态~0x170010202c0001    BDCU EEPROM故障告警状态~0x170010202d0001    单体温度异常状态~0x170010202e0001    地址冲突告警状态~0x170010202f0001    振动告警状态~0x17001020300001    单体电压采样异常告警状态~0x17001020310001    BDCU通信断告警状态~0x17001020320001    BMS通信断状态~0x17001020330001    BMS工作状态~0x17001020340001    BMS在位状态~0x17001020350001    BDCU母排过压保护告警状态~0x17001020360001
...               电压采样故障状态~0x17001020380001    充电输入断状态~0x17001020370001
@{FB100B1排除告警量信号}    BMS通信断告警~0x170010302f0001    电压采样故障~0x17001030310001
@{PU排除模拟量信号}      PU组地址~0x10001010080001    PU组内地址~0x10001010090001    PU槽位地址~0x100010100b0001
@{PU排除数字量信号}      PU输入过压状态~0x100010200b0001    PU输出过压状态~0x100010200c0001    PU过温状态~0x100010200d0001    PU输出过流状态~0x100010200e0001    PU输出熔丝断状态~0x100010200f0001    PU散热器过温状态~0x10001020100001    PU风扇故障状态~0x10001020110001    PU EEPROM异常状态~0x10001020120001    PU 输出欠压状态~0x10001020130001    光伏回路异常状态~0x10001020140001    PU序列号冲突状态~0x10001020150001    PU协议错误状态~0x10001020160001    PU输入过流状态~0x10001020170001    PU通讯状态~0x10001020180001    PU输出短路状态~0x100010201b0001    PU在位状态~0x100010201d0001    PU工作状态~0x10001020040001
...               PU输入欠压状态~0x10001020190001
@{整流器排除参数信号}      整流器轮换周期~0x8001050020001    交流节能模式~0x8001050010001    整流器最小开机数量~0x8001050030001    暂时非节能延时时间~0x8001050040001    节能带载率上限~0x80010500b0001    节能带载率下限~0x80010500c0001    电流缓启动使能~0x80010500d0001    电流缓启动时间~0x80010500e0001    整流器默认限流点比率~0x8001050090001
@{整流器限流点比率排除参数信号}    整流器轮换周期~0x8001050020001    交流节能模式~0x8001050010001    整流器最小开机数量~0x8001050030001    暂时非节能延时时间~0x8001050040001    节能带载率上限~0x80010500b0001    节能带载率下限~0x80010500c0001    电流缓启动使能~0x80010500d0001    电流缓启动时间~0x80010500e0001    整流器软启动间隔~0x8001050060001    整流器输出高停机电压~0x8001050070001    整流器默认输出电压~0x8001050080001    整流器最大数量~0x80010500a0001
${模拟整流器地址}        1
${模拟PU起始地址}       1
@{PU排除告警量信号}      PU输出过压告警~0x10001030020001    PU输出过流~0x10001030040001    PU风扇故障~0x10001030070001    PU EEPROM异常~0x10001030080001    光伏回路异常告警~0x100010300a0001    PU告警~0x100010300e0001    PU通讯中断~0x100010300f0001    PU故障~0x10001030140001    PU输入欠压告警~0x10001030100001    PU无输入告警~0x10001030110001    PU输入倒灌告警~0x10001030130001
@{PU排除故障量信号}      PU输入过压告警~0x10001030010001    PU过温告警~0x10001030030001    PU输出熔丝断告警~0x10001030050001    PU散热器过温告警~0x10001030060001    PU 输出欠压~0x10001030090001    PU序列号冲突~0x100010300b0001    PU协议错误~0x100010300c0001    PU输入过流告警~0x100010300d0001    PU输出短路告警~0x10001030120001    光伏回路异常告警~0x100010300a0001    PU告警~0x100010300e0001    PU通讯中断~0x100010300f0001    PU故障~0x10001030140001    PU输入欠压告警~0x10001030100001    PU无输入告警~0x10001030110001    PU输入倒灌告警~0x10001030130001
@{PU排除特殊数字量信号}    PU输出过压告警~0x10001030020001    PU输出过流~0x10001030040001    PU风扇故障~0x10001030070001    PU EEPROM异常~0x10001030080001    光伏回路异常告警~0x100010300a0001    PU告警~0x100010300e0001    PU通讯中断~0x100010300f0001    PU故障~0x10001030140001 PU输入过压告警~0x10001030010001    PU过温告警~0x10001030030001    PU输出熔丝断告警~0x10001030050001    PU散热器过温告警~0x10001030060001    PU 输出欠压~0x10001030090001    PU序列号冲突~0x100010300b0001    PU协议错误~0x100010300c0001    PU输入过流告警~0x100010300d0001    PU输出短路告警~0x10001030120001    光伏回路异常告警~0x100010300a0001
@{交流电表排除模拟量信号}    交流电表回路1总有功功率~0x1e001010030001    交流电表回路1电量~0x1e001010070001    交流电表回路2总有功功率~0x1e0010100a0001    交流电表回路2电量~0x1e0010100e0001    交流电表回路3电压~0x1e0010100f0001    交流电表回路3电流~0x1e001010100001    交流电表回路3总有功功率~0x1e001010110001    交流电表回路3总功率因数~0x1e001010120001    交流电表回路3相功率因数~0x1e001010130001    交流电表回路3频率~0x1e001010140001    交流电表回路3电量~0x1e001010150001    交流电表回路4电压~0x1e001010160001    交流电表回路4电流~0x1e001010170001    交流电表回路4总有功功率~0x1e001010180001    交流电表回路4总功率因数~0x1e001010190001    交流电表回路4相功率因数~0x1e0010101a0001    交流电表回路4频率~0x1e0010101b0001
...               交流电表回路4电量~0x1e0010101c0001    交流电表回路1相有功功率~0x1e0010101d0001    交流电表回路1总无功功率~0x1e0010101e0001    交流电表回路2相有功功率~0x1e001010200001
...               交流电表回路2总无功功率~0x1e001010210001    交流电表回路2相无功功率~0x1e001010220001    交流电表回路3相有功功率~0x1e001010230001
...               交流电表回路3总无功功率~0x1e001010240001    交流电表回路3相无功功率~0x1e001010250001    交流电表回路4相有功功率~0x1e001010260001
...               交流电表回路4总无功功率~0x1e001010270001    交流电表回路4相无功功率~0x1e001010280001    交流电表回路1相无功功率~0x1e0010101f0001
@{油机排除模拟量信号}      油机视在功率~0x190010100b0001    油机有功功率~0x190010100c0001    油机无功功率~0x190010100d0001
...    油机油温~0x19001010130001    油机油位~0x19001010060001    油机剩余油量~0x19001010070001    油机带载率~0x19001010120001
...    油箱容量~0x19001010080001    油机持续运行时间~0x19001010050001    油机接地电流~0x19001010150001    
...    油机正向有功电量~0x19001010180001    油机反向有功电量~0x19001010190001    油机视在电量~0x190010101a0001
...    油机无功电量~0x190010101b0001        油机总有功功率~0x190010101c0001    油机电流电压相位差~0x19001010160001
...    油机相位旋转~0x19001010170001    油机电池充电机输出电压~0x19001010140001
@{市电排除模拟量信号}      市电总有功功率~0x5001010090001    市电总无功功率~0x50010100a0001    市电总视在功率~0x50010100b0001
...    市电平均功率因数~0x50010100c0001    市电有功功率~0x5001010040001    市电无功功率~0x5001010050001    市电视在功率~0x5001010060001
...    市电相电流~0x5001010030001    市电功率因数~0x5001010070001    市电接地电流~0x5001010100001    市电正向有功电量~0x5001010110001
...    市电反向有功电量~0x5001010120001    市电视在电量~0x5001010130001    市电无功电量~0x5001010140001
@{PU排除设备名称信息}     PU序列号~0x10001080010001    PU软件版本~0x10001080020001    PU数控平台版本~0x10001080040001    PU条码~0x10001080080001
@{PU排除设备版本信息}     PU序列号~0x10001080010001    PU系统名称~0x10001080050001    PU条码~0x10001080080001
@{PU排除设备日期信息}     生产日期~0x10001080090001
@{电表排除设备版本信息}     交流电表系统名称~0x1e001080010001
@{电表排除设备名称信息}     交流电表软件版本~0x1e001080020001    交流电表系统地址~0x1e001080040001
@{油机排除设备版本信息}     油机控制屏系统名称~0x1a001080010001    油机控制屏序列号~0x1a001080040001
@{油机排除设备名称信息}     油机控制屏软件版本~0x1a001080020001    油机控制屏系统名称~0x1a001080010001    油机控制屏软件发布日期~0x1a001080030001
@{FB100B3排除设备名称信息}    BMS软件版本~0x17001080020001    BDCU数控平台版本~0x17001080060001    BDCU软件版本~0x17001080070001    整机序列号~0x170010800e0001    电池模组序列号~0x170010800b0001    BDCU序列号~0x17001080090001    BMS序列号~0x17001080040001
@{FB100B3排除设备序列号信息}    BDCU名称~0x17001080050001    BDCU数控平台版本~0x17001080060001    BDCU软件版本~0x17001080070001    BMS系统名称~0x17001080010001    BMS软件版本~0x17001080020001    BMS序列号~0x17001080040001
@{FB100C2排除数字量信号}    电池过压告警状态~0x210010200a0001    电池过压保护状态~0x210010200b0001    电池欠压告警状态~0x210010200c0001    电池欠压保护状态~0x210010200d0001    单板过温告警状态~0x210010200e0001    单板过温保护状态~0x210010200f0001    电池充电过流告警状态~0x21001020100001    电池充电过流保护状态~0x21001020110001    电池放电过流告警状态~0x21001020120001    电池放电过流保护状态~0x21001020130001    机内环境温度高告警状态~0x21001020140001    机内环境温度低告警状态~0x21001020150001    单体过压告警状态~0x21001020160001    单体过压保护状态~0x21001020170001    单体欠压告警状态~0x21001020180001    单体欠压保护状态~0x21001020190001    单体充电高温告警状态~0x210010201a0001
...               单体充电高温保护告警状态~0x210010201b0001    单体放电高温告警状态~0x210010201c0001    单体放电高温保护告警状态~0x210010201d0001    单体充电低温告警状态~0x210010201e0001    单体充电低温保护告警状态~0x210010201f0001    单体放电低温告警状态~0x21001020200001    单体放电低温保护告警状态~0x21001020210001    电池SOC低告警状态~0x21001020220001    电池SOC低保护状态~0x21001020230001    电池SOH告警状态~0x21001020240001    电池SOH保护状态~0x21001020250001    单体损坏保护状态~0x21001020260001    电池丢失告警状态~0x21001020270001    充电回路开关失效告警状态~0x21001020280001    放电回路开关失效告警状态~0x21001020290001    BMS工作状态~0x210010202a0001    BMS在位状态~0x210010202b0001
...               BMS通信断状态~0x210010202c0001    限流回路失效告警状态0x210010202d0001    短路保护状态~0x210010202e0001    电池反接告警状态~0x210010202f0001    单体温度传感器失效告警状态~0x21001020300001    电压采样故障状态~0x21001020310001    环境温度失效状态~0x21001020320001    单体落后告警状态~0x21001020330001    单体落后保护状态~0x21001020340001
@{FB100C2排除告警量信号}    单体过压告警~0x210010300d0001    单体过压保护~0x210010300e0001    单体欠压告警~0x210010300f0001    单体欠压保护~0x21001030100001    单体充电高温告警~0x21001030110001    单体充电高温保护告警~0x21001030120001    单体放电高温告警~0x21001030130001    单体放电高温保护告警~0x21001030140001    单体充电低温告警~0x21001030150001    单体充电低温保护告警~0x21001030160001    单体放电低温告警~0x21001030170001    单体放电低温保护告警~0x21001030180001    电池过压告警~0x21001030010001    电池过压保护~0x21001030020001    电池欠压告警~0x21001030030001    电池欠压保护~0x21001030040001    BMS通信断告警~0x21001030210001
@{FB100C2排除欠压低温的告警量信号}    单板过温告警~0x21001030050001    单板过温保护~0x21001030060001    电池充电过流告警~0x21001030070001    电池充电过流保护~0x21001030080001    电池放电过流告警~0x21001030090001    电池放电过流保护~0x210010300a0001    机内环境温度高告警~0x210010300b0001    机内环境温度低告警~0x210010300c0001    电池SOC低告警~0x21001030190001    电池SOC低保护~0x210010301a0001    电池SOH告警~0x210010301b0001    电池SOH保护~0x210010301c0001    单体损坏保护~0x210010301d0001    电池丢失告警~0x210010301e0001    充电回路开关失效告警~0x210010301f0001    放电回路开关失效告警~0x21001030200001    BMS通信断告警~0x21001030210001
...               限流回路失效告警~0x21001030220001    短路保护~0x21001030230001    电池反接告警~0x21001030240001    单体温度传感器失效告警~0x21001030250001    电压采样故障~0x21001030260001    环境温度失效~0x21001030270001    单体落后告警~0x21001030280001    单体落后保护~0x21001030290001    单体过压保护~0x210010300e0001    电池过压保护~0x21001030020001    单体过压告警~0x210010300d0001    单体充电高温告警~0x21001030110001    单体放电高温保护告警~0x21001030140001    电池过压告警~0x21001030010001    单体充电高温保护告警~0x21001030120001    单体放电高温告警~0x21001030130001
@{FB100C2排除过压高温的告警量信号}    单板过温告警~0x21001030050001    单板过温保护~0x21001030060001    电池充电过流告警~0x21001030070001    电池充电过流保护~0x21001030080001    电池放电过流告警~0x21001030090001    电池放电过流保护~0x210010300a0001    机内环境温度高告警~0x210010300b0001    机内环境温度低告警~0x210010300c0001    电池SOC低告警~0x21001030190001    电池SOC低保护~0x210010301a0001    电池SOH告警~0x210010301b0001    电池SOH保护~0x210010301c0001    单体损坏保护~0x210010301d0001    电池丢失告警~0x210010301e0001    充电回路开关失效告警~0x210010301f0001    放电回路开关失效告警~0x21001030200001    BMS通信断告警~0x21001030210001
...               限流回路失效告警~0x21001030220001    短路保护~0x21001030230001    电池反接告警~0x21001030240001    单体温度传感器失效告警~0x21001030250001    电压采样故障~0x21001030260001    环境温度失效~0x21001030270001    单体落后告警~0x21001030280001    单体落后保护~0x21001030290001    单体欠压保护~0x21001030100001    单体充电低温保护告警~0x21001030160001    单体放电低温告警~0x21001030170001    单体放电低温保护告警~0x21001030180001    单体欠压告警~0x210010300f0001    单体充电低温告警~0x21001030150001    电池欠压告警~0x21001030030001    电池欠压保护~0x21001030040001
@{FB100C2排除设备版本信息}    BMS系统名称~0x21001080010001    BMS厂家名称~0x21001080030001    整机序列号~0x21001080040001
@{FB100C2排除设备名称信息}    BMS软件版本~0x21001080020001    整机序列号~0x21001080040001
@{FB100C2排除设备序列号信息}    BMS系统名称~0x21001080010001    BMS软件版本~0x21001080020001    BMS厂家名称~0x21001080030001
${大包恢复默认值最新升级包}    3.06.00.01AllDefault/all_update_default.tar.gz
${大包最新升级包}        3.06.00.01All/all_update.tar.gz
${小包最新升级包}        3.06.00.01/powernew.tar.gz
${小包恢复默认值最新升级包}    3.06.00.01Default/powernewdefault.tar.gz
@{智能锂电排除设备}       高温电池    ATS    铅酸电池25    NFBBMS    智能直流配电单元    智能空开    直流配电
@{常规锂电排除设备}       高温电池    ATS    铅酸电池25    FBBMS
${测试台编号}          T1-1
${模拟三相整流器地址}      1
${datapassword}    0063@Zte
@{FB100B3排除str32设备信息}    PACK厂家名称~0x17001080110001    BMS硬件型号~0x17001080100001
@{FB100B3排除str32 BOOT信息}    BOOT版本标识~0x17001080120001
@{FB100B3排除str32参数量信号}    BMS远程IP地址~0xc001050b60001
@{叠光场景排除信号}       租户名称~0xa001050070001    公共负载配置~0xa001050080001    负载一次下电时间~0xc001050290001    负载二次下电时间~0xc0010502d0001    电池下电时间~0xc001050310001    BMS运行模式~0xc0010507d0001    锂电应用场景~0xc0010507e0001    BMS升压充电~0xc001050870001    负载一次下电SOC阈值~0xc0010502b0001    负载二次下电SOC阈值~0xc0010502f0001    电池下电SOC阈值~0xc001050330001    电池免温补上限~0xc001050450001    直流负载扩展分路断~0xa001030030001    租户一次下电分路断~0xa001030110001    租户二次下电分路断~0xa001030120001    租户电池下电分路断~0xa001030130001    公用二次下电分路断~0xa001030140001
...               公用电池下电分路断~0xa001030150001    交流输入场景~0x2001050040001    电池配置~0x2001050090001    铅酸类型~0x20010500b0001    锂电类型~0x20010500a0001    常规锂电类型~0x2001050140001    配电单元名称~0xa001050100001    直流配电单元下电使能~0xa001050090001    直流配电单元远程下电使能~0xa0010500d0001    直流配电单元远程下电~0xa0010500e0001    配电单元租户设置~0xa0010500f0001    直流配电单元下电电压~0xa0010500b0001    直流配电单元下电SOC阈值~0xa0010500c0001    直流配电单元下电告警~0xa001030190001    直流配电单元下电时间~0xa0010500a0001    充电电压~0xc0010506d0001    配电单元定时下电使能~0xa001050170001
...               配电单元定时下电起始时刻1~0xa001050180001    直流负载回路断~0xa001030020001    配电单元定时下电起始时刻2~0xa0010501a0001    配电单元定时下电终止时刻2~0xa0010501b0001    配电单元定时下电起始时刻3~0xa0010501c0001    配电单元定时下电终止时刻3~0xa0010501d0001    配电单元定时下电起始时刻4~0xa0010501e0001    配电单元定时下电终止时刻4~0xa0010501f0001    配电单元定时下电终止时刻1~0xa001050190001
@{叠光场景排除设备}       高温电池    ATS    铅酸电池25    智能直流配电单元    交流配电    交流电表    市电    市电组    油机    油机组    油机控制屏    系统运行环境
@{SDU2排除模拟量信号}    配电单元电量~0x22001010030001      直流电压~0x22001010010001    SDDU总电流~0x22001010050001    配电单元负载功率~0x22001010040001
@{SDU2排除数字量信号}    配电单元回路状态~0x22001020010001    配电单元次要负载下电状态~0x22001020040001    SDDU工作状态~0x22001020050001    SDDU通讯状态~0x22001020060001    SDDU在位状态~0x22001020070001
@{SDU2排除设备版本信息}    SDDU系统名称~0x22001080010001    SDDU软件发布日期~0x22001080030001
@{SDU2排除设备名称信息}    SDDU软件版本~0x22001080020001    SDDU软件发布日期~0x22001080030001
@{SDU2排除设备日期信息}    SDDU系统名称~0x22001080010001    SDDU软件版本~0x22001080020001
@{SDU2排除告警量信号}    SDDU通讯中断~0x22001030010001    配电单元次要负载下电告警~0x22001030020001
@{SPCU排除模拟量}      SPU电量~0x23001010040001    SPU升级进度~0x23001010090001    SPU在位数量~0x230010100a0001    SPCU组地址~0x230010100c0001    SPCU组内地址~0x230010100d0001    SPCU槽位地址~0x230010100e0001
@{SPCU排除数字量}      SPU输入欠压状态~0x23001020030001    SPU过温状态~0x23001020200001    SPU散热器过温告警状态~0x23001020210001    SPU无输入告警状态~0x23001020220001    SPU输出短路告警状态~0x23001020230001    SPCU过温告警状态~0x23001020240001    SPU输入过流告警状态~0x23001020270001
${模拟SPCU起始地址}     1
@{SSW排除模拟量信号}     智能空开槽位地址~0x2c001010090001    智能空开电流~0x2c001010020001
@{SSW排除数字量信号}     智能空开用途~0x2c001020060001    智能空开在位状态~0x2c001020070001    智能空开通讯状态~0x2c001020080001    智能空开工作状态~0x2c001020090001    智能空开下电状态~0x2c001020020001
@{SSW排除告警量信号}     智能空开通讯断~0x2c0010300e0001    智能空开下电告警~0x2c001030060001
@{SSW排除系统名称}      智能空开软件版本~0x2c001080020001    智能空开序列号~0x2c001080040001
@{SSW排除软件版本}      智能空开系统名称~0x2c001080010001    智能空开序列号~0x2c001080040001
@{SSW排除序列号}       智能空开系统名称~0x2c001080010001    智能空开软件版本~0x2c001080020001
@{SSW排除参数量设置}       智能空开下电电压阈值~0x2c001050020001    智能空开上电电压~0x2c001050030001
@{交流空调排除数字量信号}    交流空调工作状态~0x34001020040001    交流空调在位状态~0x34001020050001    交流空调通讯状态~0x34001020060001
...    柜内温度异常告警状态~0x34001020070001    输入电压异常告警状态~0x34001020080001    压缩机故障状态~0x34001020090001
...    内风机故障状态~0x340010200a0001    外风机故障状态~0x340010200b0001    环境温度传感器故障状态~0x340010200c0001
@{交流空调排除告警量信号}    交流空调通讯断~0x34001030010001    输入电压异常告警~0x34001030070001    柜内温度异常告警~0x34001030060001
${backups_dir}    power_para_backups

${plat.smoke}    SPB_J3_SMK
${plat.Water}    SPB_J14_WAT
${plat.MagneticDoor}    SPB_J3_DOOR
${plat.envtemp}    SPB_J12_T4
${plat.Inrelay1Status}    SPB_J2_DI1
${plat.Inrelay2Status}    SPB_J2_DI2
${plat.Inrelay3Status}    SPB_J2_DI3
${plat.Inrelay4Status}    SPB_J2_DI4
${plat.humity}    SPB_J15_HUM
@{plat.batttemps}    SPB_J4_T1    SPB_J4_T2    SPB_J13_T3    SPB_J12_T4
@{plat.battcurrs}    SPB_X3_IB1   SPB_X3_IB2   SPB_X3_IB3    SPB_X3_IB4
${plat.battmidvolt3}    SPB_J16_DI7
${plat.battmidvolt4}    SPB_J16_DI8
${plat.batttemp1}    SPB_J4_T1
${plat.batttemp2}    SPB_J4_T2
${plat.batttemp3}    SPB_J13_T3
${plat.battcurr1}    SPB_X3_IB1
${plat.undefined}    SPB_X4_IL4
${plat.battvolt1}    SPB_X2_VB1
${plat.voltage}    SPB_X1_VIN
${plat.battvolt5}    SPB_X2_VB5
${plat.battvolt6}    SPB_X2_VB6
@{plat.relay_out}    DO1    DO2    DO3    DO4    DO5    DO6    DO7    DO8
${plat.battmidvolt1}    SPB_J16_DI5
${plat.battmidvolt2}    SPB_J16_DI6
${温湿度传感器型号}    1


*** Keywords ***
交直流源上下电操作
    [Arguments]    ${设备名称}    ${上下电操作命令}
    [Documentation]    对交流源和电池模拟器的市电输入进行上电控制
    ...    入参规则：
    ...    ${设备名称}：电池模拟器、交流源、ALL（电池模拟器和交流源均上电）
    ...    ${上下电操作命令}：ON：上电，OFF：下电
    run keyword if    '${设备名称}'=='交流源'    控制网络继电器DO动作    DO1    ${上下电操作命令}    ${网络继电器IP0}
    ...    ELSE IF    '${设备名称}'=='电池模拟器'    控制网络继电器DO动作    D08    ${上下电操作命令}    ${网络继电器IP1}
    ...    ELSE IF    '${设备名称}'=='ALL'    run keywords    控制网络继电器DO动作    DO1    ${上下电操作命令}    ${网络继电器IP0}
    ...    AND    控制网络继电器DO动作    D08    ${上下电操作命令}    ${网络继电器IP1}
    ...    ELSE    fail    ===设备名称错误===
    sleep    1
    #查询反馈状态
    ${交流源输入状态}    run keyword if    '${设备名称}'=='交流源' or '${设备名称}'=='ALL'    查询网络继电器DI状态    DI1    ${网络继电器IP0}
    ${电池模拟器输入状态}    run keyword if    '${设备名称}'=='电池模拟器' or '${设备名称}'=='ALL'    查询网络继电器DI状态    DI5    ${网络继电器IP1}
    #交流源打印提示
    run keyword if    '${上下电操作命令}'=='ON' and '${交流源输入状态}'=='1'    log    ===交流源上电成功===
    run keyword if    '${上下电操作命令}'=='ON' and '${交流源输入状态}'=='0'    log    ===交流源上电失败===
    run keyword if    '${上下电操作命令}'=='OFF' and '${交流源输入状态}'=='1'    log    ===交流源下电失败===
    run keyword if    '${上下电操作命令}'=='OFF' and '${交流源输入状态}'=='0'    log    ===交流源下电成功===
    #电池模拟器打印提示
    run keyword if    '${上下电操作命令}'=='ON' and '${电池模拟器输入状态}'=='1'    log    ===电池模拟器上电成功===
    run keyword if    '${上下电操作命令}'=='ON' and '${电池模拟器输入状态}'=='0'    log    ===电池模拟器上电失败===
    run keyword if    '${上下电操作命令}'=='OFF' and '${电池模拟器输入状态}'=='1'    log    ===电池模拟器下电失败===
    run keyword if    '${上下电操作命令}'=='OFF' and '${电池模拟器输入状态}'=='0'    log    ===电池模拟器下电成功===

自动化测试前置条件
    [Documentation]    在执行自动化测试时，需要先运行该关键字。
    ...    自动化环境_设备侧的UIB_X8_DOOR应不短接，保证有门磁告警，作为实时告警页面刷新成功的判断条件
    ${TIME1}    Get time
    Set Suite Variable    ${测试开始时间}    ${TIME1}
    连接交流源
    连接电池模拟器
    关闭电池模拟器输出
    关闭交流源输出
    设置电池模拟器模式    铅酸电池    24    5    50
    电池模拟器接触器分合    ON
    打开电池模拟器输出
    连接CSU
    #保证有1个整流器在线，再开始测试
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    在线整流器数量    1
    #保证web实时告警刷新完成
    Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    系统运行环境    门磁告警    告警级别=2    #默认级别2
    Comment    Wait Until Keyword Succeeds    10m    2    查询指定告警信息    门磁告警
    电池管理参数恢复默认值
    断开电池模拟器
    断开交流源

自动化测试后置条件
    [Documentation]    在执行完成自动化测试后，需要运行该关键字关闭所有输出
    Comment    关闭负载输出
    Comment    同步系统时间
    Comment    连接电池模拟器
    Comment    关闭电池模拟器输出
    Comment    电池模拟器接触器分合    OFF
    Comment    连接交流源
    Comment    关闭交流源输出
    ${测试结束时间}    Get time
    ${本次运行时长}    Subtract Date From Date    ${测试结束时间}    ${测试开始时间}
    SaveLog    ${测试开始时间}    ${测试结束时间}    ${本次运行时长}

测试用例前置条件
    [Documentation]    在执行每个测试用例时，需要先运行该关键字
    设置启用设备    SMR    100    3
    连接CSU
    run keyword and ignore error    Wait Until Keyword Succeeds    10    2    设置web控制量    CAN总线设备统计
    sleep    30
    Wait until Keyword Succeeds    5m   5    信号量数据值为    在线整流器数量    3
    连接电池模拟器
    连接交流源
    关闭负载输出

    #连接功率分析仪

测试用例后置条件
    [Documentation]    在执行完每个测试用例时，需要先运行该关键字
    #关闭负载输出
    #关闭交流源输出
    sleep    1m
    移除设备  smr
    run keyword and ignore error    Wait Until Keyword Succeeds    10    2    设置web控制量    CAN总线设备统计

测试用例集后置条件
    [Documentation]    测试用例集后置条件：在完成一个测试用例集后，需要关闭待测设备和输出负载等仪器
    连接交流源
    断开Chroma交直流源输出继电器

连接仪器
    [Documentation]    连接仪器：在开始一个测试用例时，需要连接各种仪器
    连接交流源
    #连接功率分析仪

仅有市电条件上电
    [Documentation]    判断市电是否小于100V，如果小于则设置市电为220V/50Hz并输出，同时关闭电池模拟器
    ${交流源电压频率}    读三相电压频率
    run keyword if    ${交流源电压频率}[0]<100    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    15
    关闭电池模拟器输出

直流源转电池模拟器
    [Documentation]    系统已上电，但电池为直流源转电池模拟器
    ...    ==此操作后电池模拟器输出（24节2V），交流关闭
    关闭交流源输出
    关闭电池模拟器输出
    设置电池模拟器模式    铅酸电池    24    5    50
    电池模拟器接触器分合    ON
    打开电池模拟器输出
    连接CSU
    sleep    5

电池模拟器转直流源
    [Documentation]    系统已上电，但电池为电池模拟器转直流源
    ...    ==此操作后直流源输出（53.5V/100A），交流关闭
    关闭交流源输出
    关闭电池模拟器输出
    设置直流源模式    53.5    100    10
    电池模拟器接触器分合    ON
    打开电池模拟器输出
    连接CSU
    sleep    5

实时告警刷新完成
    [Documentation]    查询到门磁告警作为实时告警页面刷新成功的判断条件。
    ...    设备侧的UIB_X8_DOOR应不短接，保证有门磁告警。
    连接CSU
    Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    门磁告警    主要    #默认级别2
    Comment    Wait Until Keyword Succeeds    10m    2    查询指定告警信息    门磁告警
    sleep    3
    log    '===实时告警刷新完成==='

重置交流源输出
    [Documentation]    使交流源重置到220V/50Hz输出
    断开交流源
    连接交流源
    同时设置三相电压频率    220    50
    打开交流源输出
    连接CSU
    sleep    4
    log    '===重置交流源输出完成==='

重置电池模拟器输出
    [Documentation]    为电池模拟器输出。注：没有对交流源进行操作，直接设置电池模拟器器，没有关闭电池模拟器输出。
    断开电池模拟器
    连接电池模拟器
    设置电池模拟器模式    铅酸电池    24    5    50
    电池模拟器接触器分合    ON
    打开电池模拟器输出
    连接CSU
    sleep    9
    log    '===重置电池模拟器输出完成==='

向下调节电池电压
    [Arguments]    ${目标值}
    [Documentation]    为电池模拟器模式；入参：需要的电压目标值
    ...    调节电池电压到目标值（会略低于目标值0.3V左右）；
    ...    电池电压以设备的单组电池_1/电池电压为准。
    Comment    连接CSU
    Comment    @{电池模拟器参数}    查询电池模拟器参数
    Comment    ${电池串联节数}    Evaluate    int(${电池模拟器参数}[1])
    Comment    ${电池初始SOC}    Evaluate    int(${电池模拟器参数}[3])
    Comment    FOR    ${向下调节电池电压_电池节数}    IN RANGE    ${电池串联节数}    0    -1
    Comment    \    设置电池模拟器模式    铅酸电池    ${向下调节电池电压_电池节数}    5    ${电池初始SOC}
    Comment    \    sleep    3
    Comment    \    ${向下调节电池电压_电池电压}    Wait Until Keyword Succeeds    10X    1    获取web实时数据    电池电压-1
    Comment    \    exit for loop if    ${向下调节电池电压_电池电压}<${目标值}+2.3
    Comment    END
    Comment    FOR    ${向下调节电池电压_电池SOC}    IN RANGE    ${电池初始SOC}    0    -2
    Comment    \    设置电池模拟器模式    铅酸电池    ${向下调节电池电压_电池节数}    5    ${向下调节电池电压_电池SOC}
    Comment    \    sleep    3
    Comment    \    ${向下调节电池电压_电池电压}    Wait Until Keyword Succeeds    10X    1    获取web实时数据    电池电压-1
    Comment    \    exit for loop if    ${向下调节电池电压_电池电压}<${目标值}-0.2
    Comment    END
    Comment    sleep    3
    Comment    log    '===向下调节电池电压完成==='
    # 连接CSU
    # ${当前电压}    Wait Until Keyword Succeeds    10X    1    获取web实时数据    电池电压-1
    # FOR    ${向下调节电池电压}    IN RANGE    ${当前电压}    30    -1
    #     ${向下调节电池电压}    Evaluate    str(${向下调节电池电压})
    #     设置直流源模式    ${向下调节电池电压}    500    30000
    #     sleep    3
    #     ${向下调节电池电压_电池电压}    Wait Until Keyword Succeeds    10X    1    获取web实时数据    电池电压-1
    #     exit for loop if    ${向下调节电池电压_电池电压}<${目标值}-0.2
    # END
    # log    '===向下调节电池电压完成==='
    ${目标值}    Evaluate    str(${目标值})
    设置直流源模式    ${目标值}    500    30000
    log    '===向下调节电池电压完成==='

向上调节电池电压
    [Arguments]    ${目标值}
    [Documentation]    为电池模拟器模式；入参：需要的电压目标值
    ...    调节电池电压到目标值（会略高于目标值0.3V左右）；
    ...    电池电压以设备的单组电池_1/电池电压为准。
    # Comment    连接CSU
    # Comment    @{电池模拟器参数}    查询电池模拟器参数
    # Comment    ${电池串联节数}    Evaluate    int(${电池模拟器参数}[1])
    # Comment    ${电池初始SOC}    Evaluate    int(${电池模拟器参数}[3])
    # Comment    FOR    ${向上调节电池电压_电池节数}    IN RANGE    ${电池串联节数}    29
    # Comment    \    设置电池模拟器模式    铅酸电池    ${向上调节电池电压_电池节数}    5    ${电池初始SOC}
    # Comment    \    sleep    3
    # Comment    \    ${向上调节电池电压_电池电压}    Wait Until Keyword Succeeds    10X    1    获取web实时数据    电池电压-1
    # Comment    \    exit for loop if    ${向上调节电池电压_电池电压}>${目标值}-2.3
    # Comment    END
    # Comment    FOR    ${向上调节电池电压_电池SOC}    IN RANGE    ${电池初始SOC}    101    2
    # Comment    \    设置电池模拟器模式    铅酸电池    ${向上调节电池电压_电池节数}    5    ${向上调节电池电压_电池SOC}
    # Comment    \    sleep    3
    # Comment    \    ${向上调节电池电压_电池电压}    Wait Until Keyword Succeeds    10X    1    获取web实时数据    电池电压-1
    # Comment    \    exit for loop if    ${向上调节电池电压_电池电压}>${目标值}+0.2
    # Comment    END
    # Comment    sleep    3
    # Comment    log    '===向上调节电池电压完成==='
    # 连接CSU
    # ${当前电压}    Wait Until Keyword Succeeds    10X    1    获取web实时数据    电池电压-1
    # FOR    ${向上调节电池电压}    IN RANGE    ${当前电压}    60
    #     ${向上调节电池电压}    Evaluate    str(${向上调节电池电压})
    #     设置直流源模式    ${向上调节电池电压}    500    30000
    #     sleep    3
    #     ${向上调节电池电压_电池电压}    Wait Until Keyword Succeeds    10X    1    获取web实时数据    电池电压-1
    #     exit for loop if    ${向上调节电池电压_电池电压}>${目标值}+0.2
    # END
    # log    '===向上调节电池电压完成==='
    ${目标值}    Evaluate    str(${目标值})
    设置直流源模式    ${目标值}    500    30000
    log    '===向上调节电池电压完成==='

电池管理初始化
    [Documentation]    1、电池管理参数可设置;
    ...    2、电池组1为100AH，其他为0；均充允许
    ...    3、电池当前容量比率恢复到100；
    ...    4、电池模拟器输出（24节/5组/60%SOC）：电池电压在52-54V之间；
    ...    5、手动启动一次浮充，使系统恢复到浮充状态；
    ...    6、交流源220V/50Hz；
    ...    7、实时告警刷新完成
    ...    8、交流输入场景设置为市电：0:市电/Mains;1:油电/Mains and DG;2:纯油机/Only DG;3:无/None
    连接CSU
    log    ${交流输入场景}
    log    ${电池配置}
    log    ${铅酸类型}
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流输入场景    ${交流输入场景}    #市电场景测试
    ${场景}    获取web参数量    交流输入场景
    #市电干接点闭合
    # ${强制控制状态}    获取干接点强制控制状态
    # run keyword if    ${强制控制状态}!=1    干接点强制控制开启
    # ${强制控制状态}    获取干接点强制控制状态
    # Wait Until Keyword Succeeds    10    2    干接点动作    5
    # 获取控制输出干接点状态    5    #输出干接点的状态
    # 干接点强制控制关闭
    ${缺省值}    获取web参数上下限范围    交流制式
    ${取值约定}    获取web参数的取值约定    交流制式
    ${val}    Get From Dictionary    ${取值约定}    ${缺省值}[0]
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流制式    ${val}
    Wait Until Keyword Succeeds    10    1    设置web参数量    市电配置_1    有
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    ${电池配置}
    Wait Until Keyword Succeeds    10    1    设置web参数量    铅酸类型    ${铅酸类型}
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池应用场景    备电场景
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    3
    sleep    5
    ${获取数据1}    获取web实时数据    系统交流电压_1
    ${获取数据2}    获取web实时数据    系统交流电压_2
    ${获取数据3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    停电
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    系统停电
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    过渡
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    系统停电
    ${电压}    获取web实时数据    直流电压
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1
    Run keyword if    '${交流输入场景}'=='市电'    Wait Until Keyword Succeeds    5m    1    信号量数据值为    交流供电状态    市电1
    Run keyword if    '${交流输入场景}'=='油电'    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    交流供电状态    无
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
        设置web参数量    电池组容量_${i}    0
    END
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    99
    sleep    2
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    10    1    设置web参数量    温度补偿模式    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池充电电流系数    0.15
    Wait Until Keyword Succeeds    10    1    设置web参数量    均充使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    预约均充使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池周期均充使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池均充周期    0
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池周期测试使能    允许
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    sleep    5
    log    '===电池管理初始化完成==='

电池管理参数恢复默认值
    [Documentation]    先设置下电模式再设置下电使能。
    log    '===恢复电池管理参数默认值开始==='
    连接CSU
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流输入场景    市电    #市电场景测试
    ${缺省值}    获取web参数上下限范围    交流制式
    ${取值约定}    获取web参数的取值约定    交流制式
    ${val}    Get From Dictionary    ${取值约定}    ${缺省值}[0]
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流制式    ${val}
    Wait Until Keyword Succeeds    10    1    设置web参数量    市电配置_1    有
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    纯铅酸
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池应用场景    备电场景
    #整流器相关
    Wait Until Keyword Succeeds    10    1    设置web参数量    均充使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    预约均充使能    禁止
    Comment    sleep    5
    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    整流器输出高停机电压    均充电压    浮充电压
    #环境温度相关
    Wait Until Keyword Succeeds    10    1    设置web参数量    环境监测使能    允许
    Comment    sleep    5
    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    环境温度过高阈值    环境温度过低阈值    环境温度高阈值    环境温度低阈值
    #直流电压相关
    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    直流电压过高阈值    直流电压过低阈值    直流电压高阈值    直流电压低阈值
    #电池温度相关
    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    电池温度高阈值    电池温度低阈值
    #电池电压相关
    Comment    设置web设备参数量为默认值    电池组    测试终止电压    负载一次下电电压    负载二次下电电压    电池下电电压    电池电压低阈值    电池电压过低阈值
    #电池SOC相关
    Comment    设置web设备参数量为默认值    电池组    测试终止SOC阈值    测试失败SOC阈值    负载一次下电SOC阈值    负载二次下电SOC阈值    电池下电SOC阈值
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电模式    电池剩余容量
    Comment    sleep    5
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载一次下电使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池下电使能    允许
    Comment    sleep    5
    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    电池电压过低阈值    电池电压低阈值    测试终止电压    负载一次下电电压    负载二次下电电压    电池下电电压
    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    负载一次下电SOC阈值    负载二次下电SOC阈值    电池下电SOC阈值    测试终止SOC阈值    测试失败SOC阈值
    #电池时间相关
    Comment    设置web设备参数量为默认值    电池组    均充最长时间    均充最短时间    均充末期维持时间    负载一次下电时间    负载二次下电时间    电池下电时间
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电模式    停电时间
    Comment    sleep    5
    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    均充最长时间    均充最短时间    均充末期维持时间    负载一次下电时间    负载二次下电时间
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测周期    30
    Comment    sleep    5
    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    测试最长时间    均充阈值放电时间    电池检测持续时间    #没有约束关系
    #电池周期相关
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池周期均充使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池周期测试使能    允许
    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    电池检测周期    电池测试周期    电池均充周期
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电模式    电池电压
    sleep    3

环境温度参数恢复默认值
    log    '===恢复环境温度参数默认值开始==='
    连接CSU
    设置web设备参数量为默认值    环境温度过高阈值    环境温度过低阈值    环境温度高阈值    环境温度低阈值
    sleep    3

直流电压参数恢复默认值
    log    '===恢复直流电压参数默认值开始==='
    连接CSU
    设置web设备参数量为默认值    直流电压过高阈值    直流电压过低阈值    直流电压高阈值    直流电压低阈值
    sleep    3

恢复为仅电池模拟器供电
    [Documentation]    为电池模拟器输出（24节/5组/60%SOC）。
    ...    关闭交流源
    断开交流源
    连接交流源
    关闭交流源输出
    重置电池模拟器输出
    log    '===仅为电池模拟器供电完成==='

重置DO通道
    连接CSU
    ${通道列表}    create list
    @{通道配置情况}    create list    无    无    断开
    ${通道列表}    获取通道列表    DO
    FOR    ${通道}    IN    @{通道列表}
        ${设置结果}    设置通道配置    ${通道}    @{通道配置情况}
        should be true    ${设置结果}
    END

判断web参数是否存在
    [Arguments]    ${数据名称}
    连接CSU
    ${参数的数量能否获取}    run keyword and ignore error    获取web参数的数量    ${数据名称}
    ${参数的数量}    run keyword if    '${参数的数量能否获取}[0]'=='PASS' and '${参数的数量能否获取}[1]'!='None'    set variable    ${参数的数量能否获取}[1]
    ...    ELSE    set variable    1
    ${参数名称}    run keyword if    ${参数的数量}==1    set variable    ${数据名称}
    ...    ELSE    set variable    ${数据名称}_1
    #20200404增加end
    ${获取参数量}    wait until keyword succeeds    10    1    获取web参数量    ${参数名称}
    #无参数则打印“此参数不存在”，并失败
    run keyword if    '${获取参数量}'=='False' or '${获取参数量}'=='None'    fail    *HTML*<b>===此参数不存在===</b>    ===此参数不存在===
    [Return]    ${获取参数量}

判断web带ID的参数是否存在
    [Arguments]    ${数据名称}
    连接CSU
    ${参数的数量能否获取}    run keyword and ignore error    获取web参数的数量    ${数据名称}
    ${参数的数量}    run keyword if    '${参数的数量能否获取}[0]'=='PASS' and '${参数的数量能否获取}[1]'!='None'    set variable    ${参数的数量能否获取}[1]
    ...    ELSE    set variable    1
    Comment    ${参数名称}    run keyword if    ${参数的数量}==1    set variable    ${数据名称}
    ...    ELSE    set variable    ${数据名称}
    #20200404增加end
    ${获取参数量}    wait until keyword succeeds    10    1    获取web参数量    ${数据名称}
    #无参数则打印“此参数不存在”，并失败
    run keyword if    '${获取参数量}'=='False' or '${获取参数量}'=='None'    fail    *HTML*<b>===此参数不存在===</b>    ===此参数不存在===
    [Return]    ${获取参数量}

测试报告前置条件
    ${path1}    ${path2}    OperatingSystem.Split Path    ${SUITE SOURCE}
    Set Global Variable    ${测试用例地址}    ${SUITE SOURCE}
    WebKeyword_V30.init_dest_TestCasePath    ${测试用例地址}
    WriteTestReport.CreateNewTestReportPath    ${测试用例地址}
    Comment    写入CSV文档    参数测试    参数名称    结果

设置web序列参数
    [Arguments]    ${信号量名称}    ${设置值}    ${设置关键字}=设置web参数量    ${获取关键字}=获取web参数量    ${序列开始值}=1
    #（1）获取信号量的数量
    Comment    ${信号量名称}    findParaName    ${信号量名称}    ${g_data_dict_para}    ${g_data_dict_real}    ${g_data_dict_ctrl}
    #针对输入干接点属性的内容，其数量以“输入干接点状态”为准
    ${输入干接点属性相关的名称}    create list    输入干接点名称    输入干接点告警状态    输入干接点级别    输入干接点输出    输入干接点事件记录    #目前输入干接点属性的5类设置类型
    ${是否为输入干接点属性名称}    run keyword and return status    should contain    ${输入干接点属性相关的名称}    ${信号量名称}
    ${用于获取数量的名称}    run keyword if    ${是否为输入干接点属性名称}==True    set variable    输入干接点状态
    ...    ELSE    set variable    ${信号量名称}
    ${参数的数量能否获取}    run keyword and ignore error    获取web参数的数量    ${用于获取数量的名称}
    ${信号量的数量}    run keyword if    '${参数的数量能否获取}[0]'=='PASS' and '${参数的数量能否获取}[1]'!='None'    set variable    ${参数的数量能否获取}[1]
    ...    ELSE    set variable    0
    #（2）创建信号量名称列表    #信号量数量为1则是没有下标的一个名称，信号量大于1则是有下标的一系列名称
    ${信号量名称列表}    create list
    FOR    ${for_序列}    IN RANGE    ${序列开始值}    ${信号量的数量}+1
        ${信号量名称_中转}    run keyword if    ${信号量的数量}==1    set variable    ${信号量名称}
        ...    ELSE    set variable    ${信号量名称}_${for_序列}
        append to list    ${信号量名称列表}    ${信号量名称_中转}
    END
    #（3）进行设置
    log    ===开始设置===
    FOR    ${for_信号量名称}    IN    @{信号量名称列表}
        嵌入for循环    ${for_信号量名称}    ${设置值}    ${设置关键字}    ${获取关键字}
    END

嵌入for循环
    [Arguments]    ${嵌入for名称}    ${嵌入for值}    ${嵌入for设置关键字}=设置web参数量    ${嵌入for获取关键字}=获取web参数量
    #判断是否值是否是列表
    ${判断是否是列表}    run keyword and return status    create list    @{嵌入for值}
    ${嵌入for值列表}    run keyword if    ${判断是否是列表}==False    create list    ${嵌入for值}
    ...    ELSE    set variable    ${嵌入for值}
    FOR    ${设置值}    IN    @{嵌入for值列表}
    #设置
        run keyword    ${嵌入for设置关键字}    ${嵌入for名称}    ${设置值}
        sleep    2
    #获取参数设置后的值
        ${获取值}    run keyword    ${嵌入for获取关键字}    ${嵌入for名称}
        #参数获取和值，有可能是数字也有可能是字符串    #处理38.0与38不等的情况
        ${判断结果as string}    run keyword and return status    should be equal as strings    ${获取值}    ${设置值}
        ${判断结果}    run keyword if    ${判断结果as string}==False    run keyword and return status    should be equal as numbers    ${获取值}    ${设置值}
        ...    ELSE    set variable    ${判断结果as string}
        should be true    ${判断结果}
    END

仅电池模拟器供电
    断开交流源
    连接交流源
    关闭交流源输出
    重置电池模拟器输出
    log    '===仅为电池模拟器供电完成==='

设置失败的Web控制量
    [Arguments]    ${控制命令}
    ${设置结果}    设置web控制量_带返回值    ${控制命令}
    should not be true    ${设置结果}

节能管理电压温补初始化
    log    '===电压温补初始化==='
    连接CSU
    Wait Until Keyword Succeeds    10    1    设置web参数量    铅酸类型    普通铅酸电池
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池电流温补使能    禁止
    sleep    3

铁锂电池实时告警恢复
    [Arguments]    ${名称}    ${index}    ${数值}
    FOR    ${i}    IN RANGE    1    ${index}+1
        ${b}    Evaluate    str(${i})
        log    ${名称}${b}
        设置子工具值    smartli    all    只读    ${名称}${b}    ${数值}
    END

铁锂电池管理初始化
    [Documentation]    1、电池管理参数可设置;
    ...    2、电池组1为100AH，其他为0；均充允许
    ...    3、电池当前容量比率恢复到100；
    ...    4、电池模拟器输出（24节/5组/60%SOC）：电池电压在52-54V之间；
    ...    5、手动启动一次浮充，使系统恢复到浮充状态；
    ...    6、交流源220V/50Hz；
    ...    7、实时告警刷新完成
    ...    8、交流输入场景设置为市电：0:市电/Mains;1:油电/Mains and DG;2:纯油机/Only DG;3:无/None
    连接CSU
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流输入场景    市电    #市电场景测试
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流制式    L1L2L3N-220V
    Wait Until Keyword Succeeds    10    1    设置web参数量    市电配置_1    有
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    智能锂电
    Wait Until Keyword Succeeds    10    1    设置web参数量    智能锂电类型    FB100B3电池
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    3
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    交流供电状态    市电1
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    停电
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    系统停电
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    过渡
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    系统停电
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
        设置web参数量    电池组容量_${i}    0
    END
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    99
    sleep    2
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    100
    Comment    Wait Until Keyword Succeeds    10    1    设置web参数量    温度补偿模式    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    锂电充电电流系数    0.32
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动充电
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    充电
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池周期测试使能    允许
    sleep    5
    log    '===电池管理初始化完成==='

FB100B3铁锂电池测试前置条件
    [Documentation]    在执行每个测试用例时，需要先运行该关键字
    [Arguments]    ${铁锂电池数}=32
    连接CSU
    设置子工具个数    smartli    ${铁锂电池数}
    控制子工具运行停止    smartli    启动
    配置电池为普通铅酸电池
    Wait Until Keyword Succeeds    5m    60    设置web控制量    RS485总线设备统计
    sleep    30
    run keyword and ignore error    等待子设备工作正常   ${铁锂电池数}   BMS通信断状态    正常    20m
    配置电池为FB100B3
    

FB100B3铁锂电池测试结束条件
    [Documentation]    在执行每个测试用例时，需要先运行该关键字
    [Arguments]    ${恢复默认值}=False
    连接CSU
    控制子工具运行停止    smartli    关闭
    run keyword and ignore error    run keyword and ignore error    Wait Until Keyword Succeeds    10    2    设置web控制量    RS485总线设备统计
    Run Keyword If    ${恢复默认值}==True    Run Keywords    恢复默认值并重新登录
    配置电池为普通铅酸电池

PU测试结束条件
    [Documentation]    在执行每个测试用例时，需要先运行该关键字
    连接CSU
    控制子工具运行停止    pu    关闭
    run keyword and ignore error    Wait Until Keyword Succeeds    10    2    设置web控制量    CAN总线设备统计

PU测试前置条件
    [Documentation]    在执行每个测试用例时，需要先运行该关键字
    连接CSU
    设置子工具个数    pu    48
    控制子工具运行停止    pu    启动
    Wait Until Keyword Succeeds    5m    60   设置web控制量    CAN总线设备统计
    sleep    30
    等待所有PU工作正常

交流电压参数恢复默认值
    log    '===恢复直流电压参数默认值开始==='
    连接CSU
    Wait Until Keyword Succeeds    30    1    设置web设备参数量为默认值    交流电压过高阈值    交流电压过低阈值    交流电压高阈值    交流电压低阈值
    sleep    3

直流数字量获取前置条件
    log    '===恢复直流电压参数默认值开始==='
    连接CSU
    Wait Until Keyword Succeeds    30    1    设置web设备参数量为默认值    交流电压过高阈值    交流电压过低阈值    交流电压高阈值    交流电压低阈值
    sleep    3

油机通讯测试前置条件
    [Documentation]    在执行每个测试用例时，需要先运行该关键字
    连接CSU
    设置子工具个数    oileng    1
    控制子工具运行停止    oileng    启动
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流输入场景    油电
    Wait Until Keyword Succeeds    5m    60    设置web控制量    RS485总线设备统计
    sleep    5m

油机通讯测试结束条件
    [Documentation]    在执行每个测试用例时，需要先运行该关键字
    连接CSU
    控制子工具运行停止    oileng    关闭
    run keyword and ignore error    Wait Until Keyword Succeeds    10    2    设置web控制量    RS485总线设备统计
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流输入场景    市电

油机管理初始化
    [Documentation]    1、电池管理参数可设置;
    ...    2、电池组1为100AH，其他为0；均充允许
    ...    3、电池当前容量比率恢复到100；
    ...    4、电池模拟器输出（24节/5组/60%SOC）：电池电压在52-54V之间；
    ...    5、手动启动一次浮充，使系统恢复到浮充状态；
    ...    6、交流源220V/50Hz；
    ...    7、实时告警刷新完成
    ...    8、交流输入场景设置为市电：0:市电/Mains;1:油电/Mains and DG;2:纯油机/Only DG;3:无/None
    仅电池模拟器供电
    连接CSU
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流输入场景    市电
	wait until keyword succeeds    60    1    设置通道配置    ${plat.battmidvolt3}    无    无    断开
    设置DODI短接配置     7    ${plat.battmidvolt3}
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流输入场景    纯油机
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流制式    L1L2L3N-220V
    Comment    Wait Until Keyword Succeeds    10    1    设置web参数量    交流制式    L1L2-110V
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    纯铅酸
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    3
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
        设置web参数量    电池组容量_${i}    0
    END
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    99
    sleep    2
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    10    1    设置web参数量    温度补偿模式    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池充电电流系数    0.15
    Wait Until Keyword Succeeds    10    1    设置web参数量    浮充电压    53.5
    Wait Until Keyword Succeeds    10    1    设置web参数量    均充电压    56.4
    设置web参数量    均充使能    允许    #默认1
    Wait Until Keyword Succeeds    10    1    设置web参数量    预约均充使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池周期均充使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池均充周期    0
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    系统停电
    #市电干接点闭合
    ${强制控制状态}    获取干接点强制控制状态
    run keyword if    ${强制控制状态}!=1    干接点强制控制开启
    ${强制控制状态}    获取干接点强制控制状态
    Wait Until Keyword Succeeds    10    2    干接点恢复    5
    获取控制输出干接点状态    5    #输出干接点的状态
    干接点强制控制关闭
    sleep    5
    log    '===油机管理初始化完成==='

获取干接点状态直到变化为
    [Arguments]    ${干接点名称}    ${干接点状态}
    ${干接点实际是否动作状态1}    获取干接点状态    ${干接点名称}
    should be true    ${干接点实际是否动作状态1}==${干接点状态}    #干接点动作为1（断开），恢复为0（闭合）

交流电表测试前置条件
    [Documentation]    在执行每个测试用例时，需要先运行该关键字
    连接CSU
    设置子工具个数    ACmeter    9
    控制子工具运行停止    ACmeter    启动
    Wait Until Keyword Succeeds    5m    60    设置web控制量    RS485总线设备统计
    sleep    30
    等待子设备工作正常    3    交流电表工作状态

交流电表测试结束条件
    [Documentation]    在执行每个测试用例时，需要先运行该关键字
    连接CSU
    控制子工具运行停止    ACmeter    关闭
    run keyword and ignore error    Wait Until Keyword Succeeds    10    2    设置web控制量    RS485总线设备统计

液位传感器测试前置条件
    [Documentation]    在执行每个测试用例时，需要先运行该关键字
    连接CSU
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流输入场景    油电
    设置子工具个数    llSensor    1
    控制子工具运行停止    llSensor    启动
    Wait Until Keyword Succeeds    5m    60    设置web控制量    RS485总线设备统计
    sleep    30
    等待子设备工作正常    1    液位传感器工作状态

液位传感器测试结束条件
    [Documentation]    在执行每个测试用例时，需要先运行该关键字
    连接CSU
    控制子工具运行停止    llSensor    关闭
    run keyword and ignore error    Wait Until Keyword Succeeds    10    2    设置web控制量    RS485总线设备统计
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流输入场景    市电

判断油量采样值是否都为有效
    [Arguments]    ${时间}
    [Documentation]    1、电池管理参数可设置;
    ...    2、电池组1为100AH，其他为0；均充允许
    ...    3、电池当前容量比率恢复到100；
    ...    4、电池模拟器输出（24节/5组/60%SOC）：电池电压在52-54V之间；
    ...    5、手动启动一次浮充，使系统恢复到浮充状态；
    ...    6、交流源220V/50Hz；
    ...    7、实时告警刷新完成
    ...    8、交流输入场景设置为市电：0:市电/Mains;1:油电/Mains and DG;2:纯油机/Only DG;3:无/None
    FOR    ${i}    IN RANGE    10    0    -1
        ${获取值1}    获取指定量的调测信息    sample_avg_data_1[1]
        ${获取值2}    获取指定量的调测信息    sample_avg_data_1[2]
        ${获取值3}    获取指定量的调测信息    sample_avg_data_1[3]
        ${获取值4}    获取指定量的调测信息    sample_avg_data_1[4]
        ${获取值5}    获取指定量的调测信息    sample_avg_data_1[5]
        ${获取值6}    获取指定量的调测信息    sample_avg_data_1[6]
        ${获取值7}    获取指定量的调测信息    sample_avg_data_1[7]
        ${获取值8}    获取指定量的调测信息    sample_avg_data_1[8]
        ${获取值9}    获取指定量的调测信息    sample_avg_data_1[9]
        ${获取值10}    获取指定量的调测信息    sample_avg_data_1[10]
        run keyword if    '${获取值${i}}' == '-nan'    sleep    ${时间}m
    END
    ${获取值1}    获取指定量的调测信息    sample_avg_data_1[1]
    ${获取值2}    获取指定量的调测信息    sample_avg_data_1[2]
    ${获取值3}    获取指定量的调测信息    sample_avg_data_1[3]
    ${获取值4}    获取指定量的调测信息    sample_avg_data_1[4]
    ${获取值5}    获取指定量的调测信息    sample_avg_data_1[5]
    ${获取值6}    获取指定量的调测信息    sample_avg_data_1[6]
    ${获取值7}    获取指定量的调测信息    sample_avg_data_1[7]
    ${获取值8}    获取指定量的调测信息    sample_avg_data_1[8]
    ${获取值9}    获取指定量的调测信息    sample_avg_data_1[9]
    ${获取值10}    获取指定量的调测信息    sample_avg_data_1[10]

获取指定内部告警数量大于
    [Arguments]    ${告警数量}
    ${指定告警数量}    查询内部告警数量
    should be true    ${指定告警数量}>=${告警数量}

获取指定告警数量大于
    [Arguments]    ${告警名称}    ${告警数量}
    ${指定告警数量}    查询指定告警数量    ${告警名称}
    should be true    ${指定告警数量}>=${告警数量}

整流器测试前置条件
    [Documentation]    在执行每个测试用例时，需要先运行该关键字
    连接CSU
    设置子工具个数    SMR    48
    控制子工具运行停止    SMR    启动
    Wait Until Keyword Succeeds    5m   60    设置web控制量    CAN总线设备统计
    sleep    30
    #sleep    4m
    Wait until Keyword Succeeds    10m   5    信号量数据值为    工作整流器数量     48

整流器测试结束条件
    [Documentation]    在执行每个测试用例时，需要先运行该关键字
    连接CSU
    控制子工具运行停止    SMR    关闭
    run keyword and ignore error    Wait Until Keyword Succeeds    10    2    设置web控制量    CAN总线设备统计

获取指定告警数量小于
    [Arguments]    ${告警名称}    ${告警数量}
    ${指定告警数量}    查询指定告警数量    ${告警名称}
    should be true    ${指定告警数量}<=${告警数量}

锂电池数据判断
    连接CSU
    FOR    ${k}    IN RANGE    1    21
        ${temp}    获取web实时数据    PACK电流-${k}
        should not be true    '${temp}' == ''
        ${temp}    获取web实时数据    PACK电压-${k}
        should not be true    '${temp}' == ''
        ${temp}    获取web实时数据    机内环境温度-${k}
        should not be true    '${temp}' == ''
    END

FB100B1铁锂电池测试前置条件
    [Documentation]    在执行每个测试用例时，需要先运行该关键字
    连接CSU
    控制子工具运行停止    smartli_fb100b1    启动
    配置电池为普通铅酸电池
    Wait Until Keyword Succeeds    5m    60    设置web控制量    RS485总线设备统计
    sleep    30
    run keyword and ignore error    等待子设备工作正常   32   BMS通信断状态    正常    20m
    配置电池为FB100B1

FB100B1铁锂电池测试结束条件
    [Documentation]    在执行每个测试用例时，需要先运行该关键字
    连接CSU
    控制子工具运行停止    smartli_fb100b1    关闭
    run keyword and ignore error    Wait Until Keyword Succeeds    10    2    设置web控制量    RS485总线设备统计
    恢复默认值并重新登录    #SUPERMAN用户支持用户参数和站点配置参数恢复，
    配置电池为普通铅酸电池

有效设备进行参数设置
    [Arguments]    ${取值约定}    ${信号名称}    ${文档名称}
    run keyword if    '${取值约定}'=='False'    Run Keyword And Continue On Failure    数值类参数设置封装判断结果    ${信号名称}    ${文档名称}
    run keyword if    '${取值约定}'=='True'and ('电池应用场景'not in'${信号名称}')    Run Keyword And Continue On Failure    有取值约定类参数设置封装判断结果    ${信号名称}    ${文档名称}

数值类参数设置
    [Arguments]    ${参数名称}
    判断web带ID的参数是否存在    ${参数名称}
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    ${参数名称}
    ${缺省值}    获取web参数上下限范围    ${参数名称}
    ${可设置范围}    获取web参数可设置范围    ${参数名称}
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    ${参数名称}    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    ${参数名称}
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}
        Wait Until Keyword Succeeds    10    2    设置web参数量    ${参数名称}    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    ${参数名称}
        should be true    ${参数获取}==${参数设置}
    END
    run keyword if    ${缺省值}[0]!=0    Wait Until Keyword Succeeds    10    2    设置web参数量    ${参数名称}    ${缺省值}[0]
    [Teardown]

有取值约定类参数设置
    [Arguments]    ${参数名称}
    判断web带ID的参数是否存在    ${参数名称}
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    ${参数名称}
    ${缺省值}    获取web参数上下限范围    ${参数名称}
    ${取值约定dict}    获取web参数的取值约定    ${参数名称}
    ${取值约定values}    get dictionary values    ${取值约定dict}
    ${val}    Get From Dictionary    ${取值约定dict}    ${缺省值}[0]
    FOR    ${参数设置}    IN    @{取值约定values}
        Wait Until Keyword Succeeds    10    2    设置web参数量    ${参数名称}    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    ${参数名称}
        should be equal    ${参数获取}    ${参数设置}
    END
    run keyword if    '${val}' != '禁止'    Wait Until Keyword Succeeds    10    1    设置web参数量    ${参数名称}    ${val}
    [Teardown]

告警级别和输出干接点类参数设置
    [Arguments]    ${参数名称}
    判断web带ID的参数是否存在    ${参数名称}
    ${告警级别取值约定dict}    获取web参数的取值约定    ${参数名称}
    ${告警级别取值约定values}    get dictionary values    ${告警级别取值约定dict}
    FOR    ${VAR}    IN    @{告警级别取值约定values}
        Wait Until Keyword Succeeds    10    2    设置web参数量    ${参数名称}    ${VAR}
        sleep    3
        ${告警级别获取}    获取web参数量    ${参数名称}
        should be equal    ${告警级别获取}    ${VAR}
    END
    设置web参数量    ${参数名称}    严重
    @{参数名称列表}    Split String    ${参数名称}    ~
    Set List Value    ${参数名称列表}    0    ${参数名称列表}[0]干接点
    ${干接点参数名称}    Evaluate    "~".join(${参数名称列表})
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    5X    2    设置web参数量    ${干接点参数名称}    ${VAR}
        ${告警干接点获取}    获取web参数量    ${干接点参数名称}
        should be true    ${告警干接点获取}==${VAR}
    END
    sleep    5
    [Teardown]

数值类参数设置封装判断结果
    [Arguments]    ${参数名称}    ${文档名称}
    ${status}    Run Keyword And Return Status    数值类参数设置    ${参数名称}
    Run Keyword If    ${status}==False    写入CSV文档    ${文档名称}    ${参数名称}    Failed
    Run Keyword If    ${status}==True    写入CSV文档    ${文档名称}    ${参数名称}    Passed
    should be true    ${status}
    [Teardown]

有取值约定类参数设置封装判断结果
    [Arguments]    ${参数名称}    ${文档名称}
    ${status}    Run Keyword And Return Status    有取值约定类参数设置    ${参数名称}
    Run Keyword If    ${status}==False    写入CSV文档    ${文档名称}    ${参数名称}    Failed
    Run Keyword If    ${status}==True    写入CSV文档    ${文档名称}    ${参数名称}    Passed
    should be true    ${status}
    [Teardown]

告警级别和输出干接点类参数设置封装判断结果
    [Arguments]    ${参数名称}    ${文档名称}
    ${status}    Run Keyword And Return Status    告警级别和输出干接点类参数设置    ${参数名称}
    Run Keyword If    ${status}==False    写入CSV文档    ${文档名称}    ${参数名称}    Failed
    Run Keyword If    ${status}==True    写入CSV文档    ${文档名称}    ${参数名称}    Passed
    should be true    ${status}
    [Teardown]

油机管理测试前置条件
    [Documentation]    在执行每个测试用例时，需要先运行该关键字
    连接CSU
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流输入场景    油电
    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay4Status}    无    无    闭合

油机管理测试结束条件
    [Documentation]    在执行每个测试用例时，需要先运行该关键字
    连接CSU
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流输入场景    市电
    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay4Status}    系统运行环境    温控单元异常    闭合

告警级别和输出干接点恢复默认值
    [Arguments]    ${参数名称}
    [Documentation]    在执行每个测试用例时，需要先运行该关键字
    设置web设备参数量为默认值    ${参数名称}
    @{参数名称列表}    Split String    ${参数名称}    ~
    Set List Value    ${参数名称列表}    0    ${参数名称列表}[0]干接点
    ${干接点参数名称}    Evaluate    "~".join(${参数名称列表})
    设置web设备参数量为默认值    ${干接点参数名称}

开启所有的模拟子工具
    连接CSU
    #启动锂电
    控制子工具运行停止    smartli    启动
    #启动油机
    控制子工具运行停止    oileng    启动
    #启动电表
    设置子工具个数    ACmeter    9
    控制子工具运行停止    ACmeter    启动
    #启动液位传感器
    设置子工具个数    llSensor    1
    控制子工具运行停止    llSensor    启动
    控制子工具运行停止    SDU2    启动
    控制子工具运行停止    DMU_DCAirCondition    启动
    控制子工具运行停止    DMU_WS312M1      启动
    sleep    1m
    Wait Until Keyword Succeeds    30    2    设置web控制量    RS485总线设备统计
    #启动PU
    控制子工具运行停止    pu    启动
    sleep    1m
    Wait Until Keyword Succeeds    30    2    设置web控制量    CAN总线设备统计
    run keyword and ignore error  等待子设备工作正常  32  BMS通信断状态  正常  15m
    sleep    5m
    

关闭所有的模拟子工具
    连接CSU
    #关闭锂电
    控制子工具运行停止    smartli    关闭
    #关闭油机
    控制子工具运行停止    oileng    关闭
    #关闭电表
    设置子工具个数    ACmeter    9
    控制子工具运行停止    ACmeter    关闭
    #关闭液位传感器
    设置子工具个数    llSensor    1
    控制子工具运行停止    llSensor    关闭
    控制子工具运行停止    SDU2    关闭
    控制子工具运行停止    DMU_DCAirCondition    关闭
    控制子工具运行停止    DMU_WS312M1      关闭
    sleep    1m
    Wait Until Keyword Succeeds    10    2    设置web控制量    RS485总线设备统计
    #关闭PU
    控制子工具运行停止    pu    关闭
    sleep    1m
    Wait Until Keyword Succeeds    10    2    设置web控制量    CAN总线设备统计
    sleep    5m

32位字符串型参数设置
    [Arguments]    ${参数名称}
    判断web带ID的参数是否存在    ${参数名称}
    ${原值}    Wait Until Keyword Succeeds    5m    2    获取web参数量    ${参数名称}
    #1）参数下限
    ${参数下限}    set variable    a    #设置为1个字符
    Wait Until Keyword Succeeds    10    2    设置web参数量    ${参数名称}    ${参数下限}
    sleep    5
    ${参数下限_获取}    获取web参数量    ${参数名称}    #获取参数
    should be true    '${参数下限_获取}'=='${参数下限}'
    #2）参数上限_超范围
    ${参数设置}    set variable    xxxxxxxxxx xxxxxxxxxx xxxxxxxxxx x    #34个字符超过32
    ${设置结果}    run keyword and return status    设置web参数量    ${参数名称}    ${参数设置}
    should not be true    ${设置结果}
    #3）正常范围内设置
    ${参数设置}    set variable    ZTE能源自动化安装公司    #utf-8一个中文为3个字节
    Wait Until Keyword Succeeds    10    2    设置web参数量    ${参数名称}    ${参数设置}
    sleep    5
    #获取修改后的参数，是否相应变化
    ${修改后的参数}    获取web参数量    ${参数名称}
    #参数修改后，获取版本信息中实际名称是否相应变化
    should be true    '${修改后的参数}'=='${参数设置}'
    #恢复为原名称
    Wait Until Keyword Succeeds    10    2    设置web参数量    ${参数名称}    ${原值}
    [Teardown]

32位字符串型参数设置封装判断结果
    [Arguments]    ${参数名称}    ${文档名称}
    ${status}    Run Keyword And Return Status    32位字符串型参数设置    ${参数名称}
    Run Keyword If    ${status}==False    写入CSV文档    ${文档名称}    ${参数名称}    Failed
    Run Keyword If    ${status}==True    写入CSV文档    ${文档名称}    ${参数名称}    Passed
    should be true    ${status}
    [Teardown]

64位字符串型参数设置
    [Arguments]    ${参数名称}
    判断web带ID的参数是否存在    ${参数名称}
    ${原值}    Wait Until Keyword Succeeds    5m    2    获取web参数量    ${参数名称}
    #1）参数下限
    ${参数下限}    set variable    a    #设置为1个字符
    Wait Until Keyword Succeeds    10    2    设置web参数量    ${参数名称}    ${参数下限}
    sleep    5
    ${参数下限_获取}    获取web参数量    ${参数名称}    #获取参数
    should be true    '${参数下限_获取}'=='${参数下限}'
    #2）参数上限_超范围
    ${参数设置}    set variable    xxxxxxxxxx xxxxxxxxxx xxxxxxxxxx xxxxxxxxxx xxxxxxxxxx xxxxxxxxxx xx    #65个字符超过64
    ${设置结果}    run keyword and return status    设置web参数量    ${参数名称}    ${参数设置}
    should not be true    ${设置结果}
    #3）正常范围内设置
    ${参数设置}    set variable    ZTE中兴通讯股份有限公司    #utf-8一个中文为3个字节
    Wait Until Keyword Succeeds    10    2    设置web参数量    ${参数名称}    ${参数设置}
    sleep    5
    #获取修改后的参数，是否相应变化
    ${修改后的参数}    获取web参数量    ${参数名称}
    #参数修改后，获取版本信息中实际名称是否相应变化
    should be true    '${修改后的参数}'=='${参数设置}'
    #恢复为原名称
    Wait Until Keyword Succeeds    10    2    设置web参数量    ${参数名称}    ${原值}
    [Teardown]

64位字符串型参数设置封装判断结果
    [Arguments]    ${参数名称}    ${文档名称}
    ${status}    Run Keyword And Return Status    64位字符串型参数设置    ${参数名称}
    Run Keyword If    ${status}==False    写入CSV文档    ${文档名称}    ${参数名称}    Failed
    Run Keyword If    ${status}==True    写入CSV文档    ${文档名称}    ${参数名称}    Passed
    should be true    ${status}
    [Teardown]

date型参数设置
    [Arguments]    ${参数名称}
    判断web带ID的参数是否存在    ${参数名称}
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    ${参数名称}
    ${设置范围}    获取web参数上下限范围    ${参数名称}
    ${缺省值}    convert date    ${设置范围}[0]    result_format=%Y-%m-%d    date_format=%Y-%m-%d
    ${设置范围下限}    convert date    ${设置范围}[1]    result_format=%Y-%m-%d    date_format=%Y-%m-%d
    ${设置范围上限}    convert date    ${设置范围}[2]    result_format=%Y-%m-%d    date_format=%Y-%m-%d
    ${超下限}    subtract time from date    ${设置范围下限}    1d
    ${超上限}    add time to date    ${设置范围上限}    1d
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超下限}    ${超上限}
        ${设置结果}    run keyword and return status    设置web参数量    ${参数名称}    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    ${参数名称}
        Should Be Equal As Strings    ${参数获取}    ${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    ${设置范围下限}    ${设置范围上限}    ${缺省值}
        设置web参数量    ${参数名称}    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    ${参数名称}
        Should Be Equal As Strings    ${参数获取}    ${参数设置}
    END
    [Teardown]

date型参数设置封装判断结果
    [Arguments]    ${参数名称}    ${文档名称}
    ${status}    Run Keyword And Return Status    date型参数设置    ${参数名称}
    Run Keyword If    ${status}==False    写入CSV文档    ${文档名称}    ${参数名称}    Failed
    Run Keyword If    ${status}==True    写入CSV文档    ${文档名称}    ${参数名称}    Passed
    should be true    ${status}
    [Teardown]

hour_minute型参数设置
    [Arguments]    ${参数名称}
    判断web带ID的参数是否存在    ${参数名称}
    Wait Until Keyword Succeeds    10    1    设置web参数量    ${参数名称}    14:20
    ${默认值}    获取web参数上下限范围    ${参数名称}
    ${可设置范围}    获取web参数可设置范围    ${参数名称}
    ${原参数}    获取web参数量    ${参数名称}
    #超范围设置不成功
    ${设置结果}    run keyword and return status    设置web参数量    ${参数名称}    24:00
    should not be true    ${设置结果}
    ${参数获取}    获取web参数量    ${参数名称}
    should be true    '${参数获取}'=='${原参数}'
    ${设置结果}    run keyword and return status    设置web参数量    ${参数名称}    -0:01
    should not be true    ${设置结果}
    ${参数获取}    获取web参数量    ${参数名称}
    should be true    '${参数获取}'=='${原参数}'
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${默认值}[0]
        设置web参数量    ${参数名称}    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    ${参数名称}
        should be equal    '${参数获取}'    '${参数设置}'
    END
    [Teardown]

hour_minute型参数设置封装判断结果
    [Arguments]    ${参数名称}    ${文档名称}
    ${status}    Run Keyword And Return Status    hour_minute型参数设置    ${参数名称}
    Run Keyword If    ${status}==False    写入CSV文档    ${文档名称}    ${参数名称}    Failed
    Run Keyword If    ${status}==True    写入CSV文档    ${文档名称}    ${参数名称}    Passed
    should be true    ${status}
    [Teardown]

禁止类参数设置为允许
    [Arguments]    ${参数名称}
    判断web带ID的参数是否存在    ${参数名称}
    ${原值}    获取web参数量    ${参数名称}
    run keyword if    '${原值}'=='禁止'    Wait Until Keyword Succeeds    10    2    设置web参数量    ${参数名称}    允许
    [Teardown]

默认值为0的数值类参数设置为非0
    [Arguments]    ${参数名称}
    判断web带ID的参数是否存在    ${参数名称}
    ${缺省值}    获取web参数上下限范围    ${参数名称}
    ${可设置范围}    获取web参数可设置范围    ${参数名称}
    ${设置值}    evaluate    ${可设置范围}[0]+${缺省值}[4]
    run keyword if    ${缺省值}[0]==0    Wait Until Keyword Succeeds    10    2    设置web参数量    ${参数名称}    ${设置值}
    [Teardown]


南向子设备模拟量获取值
    [Arguments]    ${子设备名称}    ${信号名称}    ${命令名称}    ${参数值}    ${查询SID序号}
    ${信号量}    拆分信号量名称和SID    ${信号名称}
    ${获取子工具信号名称}    获取子工具信号名称    ${子设备名称}    ${信号量}[${查询SID序号}]
    Return From Keyword If    '${获取子工具信号名称}'==''    获取子工具信号名称为空
    @{子工具信号名称}    split string    ${获取子工具信号名称}    ,
    设置子工具值    ${子设备名称}    all    ${命令名称}    ${子工具信号名称}[0]    ${参数值}
    Wait Until Keyword Succeeds    3m    5    信号量数据值为(强制获取)    ${信号名称}    ${参数值}
    [Teardown]
    [Return]    ${获取子工具信号名称}


南向子设备模拟量获取值封装判断结果
    [Arguments]    ${子设备名称}    ${信号名称}    ${命令名称}    ${参数值}    ${查询SID序号}    ${文档名称}
    ${结果}    ${val}    Run Keyword And Ignore Error    南向子设备模拟量获取值    ${子设备名称}    ${信号名称}    ${命令名称}    ${参数值}    ${查询SID序号}
    Comment    Run Keyword If    ${status}==False    写入CSV文档    ${文档名称}    ${信号名称}    ${参数值}    Failed
    Comment    Run Keyword If    ${status}==True    写入CSV文档    ${文档名称}    ${信号名称}    ${参数值}    Passed
    Comment    should be true    ${status}
    ${结果}    ${val}    Run Keyword And Ignore Error    南向子设备模拟量获取值    ${子设备名称}    ${信号名称}    ${命令名称}    ${参数值}     ${查询SID序号}
    写入CSV文档    ${文档名称}    ${信号名称}    ${参数值}    ${结果}    ${val}
    should be true    '${结果}'=='PASS'
    [Teardown]

南向子设备数字量获取值
    [Arguments]    ${子设备名称}    ${信号名称}    ${命令名称}    ${参数值}     ${查询SID序号}
    ${信号量}    拆分信号量名称和SID    ${信号名称}
    ${获取子工具信号名称}    获取子工具信号名称    ${子设备名称}    ${信号量}[${查询SID序号}]
    Return From Keyword If    '${获取子工具信号名称}'==''    获取子工具信号名称为空
    @{信号名称及关联SID}    split string    ${获取子工具信号名称}    ,
    ${取值约定dict}    获取web参数的取值约定    ${信号名称}
    ${参数值对应的取值约定}    Get From Dictionary    ${取值约定dict}    ${参数值}
    设置子工具值    ${子设备名称}    all    ${命令名称}    ${信号名称及关联SID}[0]    ${参数值}
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    ${信号名称}    ${参数值对应的取值约定}
    [Teardown]
    [Return]    ${获取子工具信号名称}

南向子设备数字量获取值封装判断结果
    [Arguments]    ${子设备名称}    ${信号名称}    ${命令名称}    ${参数值}     ${查询SID序号}      ${文档名称}
    ${结果}    ${val}    Run Keyword And Ignore Error    南向子设备数字量获取值    ${子设备名称}    ${信号名称}    ${命令名称}    ${参数值}     ${查询SID序号}
    写入CSV文档    ${文档名称}    ${信号名称}    ${参数值}    ${结果}    ${val}
    should be true    '${结果}'=='PASS'
    [Teardown]

南向RS485子设备告警量产生
    [Arguments]    ${子设备名称}    ${信号名称}    ${命令名称}    ${信号值}
    ${信号量}    拆分信号量名称和SID    ${信号名称}
    ${级别设置值}    获取web参数量    ${信号名称}
    run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    ${信号名称}    严重
    ${获取子工具信号名称}    获取子工具信号名称    ${子设备名称}    ${信号量}[2]
    Return From Keyword If    '${获取子工具信号名称}'==''    获取子工具信号名称为空
    @{信号名称及关联SID}    split string    ${获取子工具信号名称}    ,
    设置子工具值    ${子设备名称}    all    ${命令名称}    ${信号名称及关联SID}[0]    ${信号值}
    ${列表}    获取数据字典指定查询的信号量列表_通过SID    ${信号名称及关联SID}[1]    ${信号量}[0]
    FOR    ${i}    IN    @{列表}
        ${关联信号名称}    Get From Dictionary    ${i}    signal_name
        ${关联信号量}    拆分信号量名称和SID    ${关联信号名称}
        ${取值约定dict}    获取web参数的取值约定    ${关联信号名称}
        ${信号值}    run keyword if    ${信号值} != 1 and '${子设备名称}' != 'SDU2'    set variable    1
        ...    ELSE IF    ${信号值} == 1 and '${子设备名称}' != 'SDU2'    set variable    1
        ...    ELSE    set variable    0
        ${参数值对应的取值约定}    Get From Dictionary    ${取值约定dict}    ${信号值}
        Run Keyword And Continue On Failure    Wait Until Keyword Succeeds    10m    5    信号量数据值为(强制获取)    ${关联信号名称}    ${参数值对应的取值约定}
        Run Keyword And Continue On Failure    wait until keyword succeeds    10m    5    判断告警存在    ${信号量}[0]
    END
    [Teardown]
    [Return]    ${获取子工具信号名称}

南向RS485子设备告警量产生封装判断结果
    [Arguments]    ${子设备名称}    ${信号名称}    ${命令名称}    ${参数值}    ${文档名称}
    ${结果}    ${val}    Run Keyword And Ignore Error    南向RS485子设备告警量产生    ${子设备名称}    ${信号名称}    ${命令名称}    ${参数值}
    写入CSV文档    ${文档名称}    ${信号名称}    ${参数值}    ${结果}    ${val}
    should be true    '${结果}'=='PASS'
    [Teardown]

南向RS485子设备告警量恢复
    [Arguments]    ${子设备名称}    ${信号名称}    ${命令名称}    ${信号值}
    ${信号量}    拆分信号量名称和SID    ${信号名称}
    ${级别设置值}    获取web参数量    ${信号名称}
    run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    ${信号名称}    严重
    ${获取子工具信号名称}    获取子工具信号名称    ${子设备名称}    ${信号量}[2]
    Return From Keyword If    '${获取子工具信号名称}'==''    获取子工具信号名称为空
    @{信号名称及关联SID}    split string    ${获取子工具信号名称}    ,
    设置子工具值    ${子设备名称}    all    ${命令名称}    ${信号名称及关联SID}[0]    ${信号值}
    ${列表}    获取数据字典指定查询的信号量列表_通过SID    ${信号名称及关联SID}[1]    ${信号量}[0]
    FOR    ${i}    IN    @{列表}
        ${关联信号名称}    Get From Dictionary    ${i}    signal_name
        ${关联信号量}    拆分信号量名称和SID    ${关联信号名称}
        ${取值约定dict}    获取web参数的取值约定    ${关联信号名称}
        ${参数值对应的取值约定}    Get From Dictionary    ${取值约定dict}    ${信号值}
        Run Keyword And Continue On Failure    Wait Until Keyword Succeeds    10m    5    信号量数据值为(强制获取)    ${关联信号名称}    ${参数值对应的取值约定}
        Run Keyword And Continue On Failure    wait until keyword succeeds    10m    5    精准判断告警不存在    ${信号量}[0]
    END
    [Teardown]
    [Return]    ${获取子工具信号名称}

南向RS485子设备告警量恢复封装判断结果
    [Arguments]    ${子设备名称}    ${信号名称}    ${命令名称}    ${参数值}    ${文档名称}
    ${结果}    ${val}    Run Keyword And Ignore Error    南向RS485子设备告警量恢复    ${子设备名称}    ${信号名称}    ${命令名称}    ${参数值}
    写入CSV文档    ${文档名称}    ${信号名称}    ${参数值}    ${结果}    ${val}
    should be true    '${结果}'=='PASS'
    [Teardown]

南向子设备字符型厂家信息
    [Arguments]    ${子设备名称}    ${信号名称}    ${命令名称}    ${信号值}
    ${信号量}    拆分信号量名称和SID    ${信号名称}
    ${获取子工具信号名称}    获取子工具信号名称    ${子设备名称}    ${信号量}[2]
    Return From Keyword If    '${获取子工具信号名称}'==''    获取子工具信号名称为空
    @{信号名称及关联SID}    split string    ${获取子工具信号名称}    ,
    设置子工具值    ${子设备名称}    all    ${命令名称}    ${信号名称及关联SID}[0]    ${信号值}
    sleep    4m
    ${名称}    获取web实时数据    ${信号名称}
    ${名称}    Strip String    ${名称}
    should be equal    '${名称}'    '${信号值}'
    [Teardown]
    [Return]    ${获取子工具信号名称}

南向子设备字符型厂家信息封装判断结果
    [Arguments]    ${子设备名称}    ${信号名称}    ${命令名称}    ${信号值}    ${文档名称}
    ${结果}    ${val}    Run Keyword And Ignore Error    南向子设备字符型厂家信息    ${子设备名称}    ${信号名称}    ${命令名称}    ${信号值}
    写入CSV文档    ${文档名称}    ${信号名称}    ${信号值}    ${结果}    ${val}
    should be true    '${结果}'=='PASS'
    [Teardown]

南向子设备数值型厂家信息
    [Arguments]    ${子设备名称}    ${信号名称}    ${命令名称}    ${信号值}
    ${信号量}    拆分信号量名称和SID    ${信号名称}
    ${获取子工具信号名称}    获取子工具信号名称    ${子设备名称}    ${信号量}[2]
    Return From Keyword If    '${获取子工具信号名称}'==''    获取子工具信号名称为空
    @{信号名称及关联SID}    split string    ${获取子工具信号名称}    ,
    设置子工具值    ${子设备名称}    all    ${命令名称}    ${信号名称及关联SID}[0]    ${信号值}
    sleep    4m
    ${名称}    获取web实时数据    ${信号名称}
    should be equal As Numbers    ${名称}    ${信号值}
    [Teardown]
    [Return]    ${获取子工具信号名称}

南向子设备数值型厂家信息封装判断结果
    [Arguments]    ${子设备名称}    ${信号名称}    ${命令名称}    ${信号值}    ${文档名称}
    ${结果}    ${val}    Run Keyword And Ignore Error    南向子设备数值型厂家信息    ${子设备名称}    ${信号名称}    ${命令名称}    ${信号值}
    写入CSV文档    ${文档名称}    ${信号名称}    ${信号值}    ${结果}    ${val}
    should be true    '${结果}'=='PASS'
    [Teardown]

南向子设备数值型参数设置
    [Arguments]    ${子设备名称}    ${信号名称}    ${命令名称}
    ${信号量}    拆分信号量名称和SID    ${信号名称}
    判断web带ID的参数是否存在    ${信号量}[0]
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    ${信号量}[0]
    ${缺省值}    获取web参数上下限范围    ${信号量}[0]
    ${可设置范围}    获取web参数可设置范围    ${信号量}[0]
    ${获取子工具信号名称}    获取子工具信号名称    ${子设备名称}    ${信号量}[2]
    Return From Keyword If    '${获取子工具信号名称}'==''    获取子工具信号名称为空
    @{信号名称及关联SID}    split string    ${获取子工具信号名称}    ,
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    ${信号量}[0]    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    ${信号量}[0]
        should be true    ${参数获取}==${原参数}
    END
    ${临时值}    set variable    ${缺省值}[0]
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        Comment    Wait Until Keyword Succeeds    20    2    设置web参数量    ${信号量}[0]    ${参数设置}
        Wait Until Keyword Succeeds    6m    20    获取子工具参数直到变化为    ${子设备名称}    ${命令名称}    ${信号名称及关联SID}[0]    ${信号量}[0]    ${参数设置}    ${临时值}
        ${参数获取}    获取web参数量    ${信号量}[0]
        should be true    ${参数获取}==${参数设置}
        ${临时值}    set variable    ${参数设置}
    END
    [Teardown]
    [Return]    ${获取子工具信号名称}

南向子设备数值型参数设置封装判断结果
    [Arguments]    ${子设备名称}    ${信号名称}    ${命令名称}    ${文档名称}
    ${结果}    ${val}    Run Keyword And Ignore Error    南向子设备数值型参数设置    ${子设备名称}    ${信号名称}    ${命令名称}
    写入CSV文档    ${文档名称}    ${信号名称}    ${结果}    ${val}
    should be true    '${结果}'=='PASS'
    [Teardown]


南向子设备有取值约定型设置比对
    [Arguments]    ${子设备名称}    ${命令名称}    ${信号名称}    ${取值约定dict}    ${信号量名称}    ${取值约定values个数}    ${取值约定values}
    FOR    ${i}    IN RANGE    ${取值约定values个数}
        Comment    Wait Until Keyword Succeeds    20    2    设置web参数量    ${信号量}[0]    ${参数设置}
        ${临时值索引}    Run Keyword If    ${i}==0    Evaluate    ${取值约定values个数}-1
        ...   ELSE    Evaluate    ${i}-1
        Wait Until Keyword Succeeds    6m    20    获取子工具有取值约定参数直到变化为    ${子设备名称}    ${命令名称}    ${信号名称}    ${取值约定dict}    ${信号量名称}    ${取值约定values}[${i}]    ${取值约定values}[${临时值索引}]
        ${参数获取}    获取web参数量    ${信号量名称} 
        should be equal    ${参数获取}    ${取值约定values}[${i}]
    END


南向子设备有取值约定型参数设置
    [Arguments]    ${子设备名称}    ${信号名称}    ${命令名称}
    ${信号量}    拆分信号量名称和SID    ${信号名称}
    判断web带ID的参数是否存在    ${信号量}[0]
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    ${信号量}[0]
    ${缺省值}    获取web参数上下限范围    ${信号量}[0]
    ${取值约定dict}    获取web参数的取值约定    ${信号量}[0]
    ${取值约定values}    get dictionary values    ${取值约定dict}
    ${获取子工具信号名称}    获取子工具信号名称    ${子设备名称}    ${信号量}[2]
    Return From Keyword If    '${获取子工具信号名称}'==''    获取子工具信号名称为空
    @{信号名称及关联SID}    split string    ${获取子工具信号名称}    ,
    ${取值约定values个数}    Get Length    ${取值约定values}
    Run Keyword If    ${取值约定values个数} != 0    南向子设备有取值约定型设置比对    ${子设备名称}    ${命令名称}    ${信号名称及关联SID}[0]    ${取值约定dict}    ${信号量}[0]    ${取值约定values个数}    ${取值约定values}
    
    [Teardown]
    [Return]    ${获取子工具信号名称}

南向子设备有取值约定型参数设置封装判断结果
    [Arguments]    ${子设备名称}    ${信号名称}    ${命令名称}    ${文档名称}
    ${结果}    ${val}    Run Keyword And Ignore Error    南向子设备有取值约定型参数设置    ${子设备名称}    ${信号名称}    ${命令名称}
    写入CSV文档    ${文档名称}    ${信号名称}    ${结果}    ${val}
    should be true    '${结果}'=='PASS'
    [Teardown]

获取子工具参数直到变化为
    [Arguments]    ${子设备名称}    ${命令名称}    ${参数名称}    ${web参数名称}    ${参数值}    ${临时值}
    Wait Until Keyword Succeeds    20    2    设置web参数量    ${web参数名称}    ${参数值}
    sleep    30
    ${子工具参数获取}    获取子工具值    ${子设备名称}    1    ${命令名称}    ${参数名称}
    @{子工具参数获取}    split string    @{子工具参数获取}    ,
    Run Keyword If    ${子工具参数获取}[2]!=${参数值}    重新设置web参数量    ${web参数名称}    ${参数值}    ${临时值}
    ${子工具参数获取}    获取子工具值    ${子设备名称}    1    ${命令名称}    ${参数名称}
    @{子工具参数获取}    split string    @{子工具参数获取}    ,
    should be equal as numbers    ${子工具参数获取}[2]    ${参数值}


重新设置web参数量
    [Arguments]    ${web参数名称}    ${目标值}    ${临时值}
    [Documentation]    先设置成临时值，再设置为目标值，确保可以下发参数设置指令给子工具
    Run Keyword And Ignore Error   Wait Until Keyword Succeeds    20    2    设置web参数量    ${web参数名称}    ${临时值}
    sleep    30
    Run Keyword And Ignore Error   Wait Until Keyword Succeeds    20    2    设置web参数量    ${web参数名称}    ${目标值}
    sleep    30



获取子工具有取值约定参数直到变化为
    [Arguments]    ${子设备名称}    ${命令名称}    ${参数名称}    ${取值约定}    ${web参数名称}    ${参数值}    ${临时值}
    Wait Until Keyword Succeeds    20    2    设置web参数量    ${web参数名称}    ${参数值}
    sleep    30
    ${子工具参数获取}    获取子工具值    ${子设备名称}    1    ${命令名称}    ${参数名称}
    @{子工具参数获取}    split string    @{子工具参数获取}    ,
    ${转换值}    Get From Dictionary    ${取值约定}    ${子工具参数获取}[2]
    Run Keyword If    '${转换值}'!='${参数值}'    Run Keyword And Ignore Error    重新设置web参数量    ${web参数名称}    ${参数值}    ${临时值}
    ${子工具参数获取}    获取子工具值    ${子设备名称}    1    ${命令名称}    ${参数名称}
    @{子工具参数获取}    split string    @{子工具参数获取}    ,
    ${转换值}    Get From Dictionary    ${取值约定}    ${子工具参数获取}[2]
    should be equal    ${转换值}    ${参数值}

整流器或PU告警量产生
    [Arguments]    ${子设备名称}    ${信号名称}    ${命令名称}    ${信号值}    ${模拟起始地址}    ${通用告警}
    ${信号量}    拆分信号量名称和SID    ${信号名称}
    @{信号名称及下标}    split string    ${信号量}[0]    -
    ${获取子工具信号名称}    获取子工具信号名称    ${子设备名称}    ${信号量}[2]
    Return From Keyword If    '${获取子工具信号名称}'==''    获取子工具信号名称为空
    @{信号名称及关联SID}    split string    ${获取子工具信号名称}    ,
    ${temp}    evaluate    ${信号名称及下标}[1]-${模拟起始地址}
    ${子工具整流器地址}    evaluate    ${temp}+1
    ${子工具整流器地址}    Convert to string    ${子工具整流器地址}
    设置子工具值    ${子设备名称}    ${子工具整流器地址}    ${命令名称}    ${信号名称及关联SID}[0]    ${信号值}
    ${告警名称}    set variable    ${通用告警}-${信号名称及下标}[1]
    ${列表}    获取数据字典指定查询的信号量列表_通过SID    ${信号名称及关联SID}[1]    ${信号量}[0]
    FOR    ${i}    IN    @{列表}
        ${关联信号名称}    Get From Dictionary    ${i}    signal_name
        ${关联信号量}    拆分信号量名称和SID    ${关联信号名称}
        ${取值约定dict}    获取web参数的取值约定    ${关联信号名称}
        ${参数值对应的取值约定}    Get From Dictionary    ${取值约定dict}    ${信号值}
        Run Keyword And Continue On Failure    Wait Until Keyword Succeeds    15m    5    信号量数据值为(强制获取)    ${关联信号名称}    ${参数值对应的取值约定}
        Run Keyword And Continue On Failure    wait until keyword succeeds    15m    5    判断内部告警存在    ${信号量}[0]
        Run Keyword And Continue On Failure    wait until keyword succeeds    10m    5    判断告警存在    ${告警名称}
    END
    [Teardown]
    [Return]    ${获取子工具信号名称}

整流器或PU告警量产生封装判断结果
    [Arguments]    ${子设备名称}    ${信号名称}    ${命令名称}    ${参数值}    ${模拟起始地址}    ${通用告警}    ${文档名称}
    ${结果}    ${val}    Run Keyword And Ignore Error    整流器或PU告警量产生    ${子设备名称}    ${信号名称}    ${命令名称}    ${参数值}    ${模拟起始地址}    ${通用告警}
    写入CSV文档    ${文档名称}    ${信号名称}    ${参数值}    ${结果}    ${val}
    should be true    '${结果}'=='PASS'
    [Teardown]

整流器或PU告警量恢复封装判断结果
    [Arguments]    ${子设备名称}    ${信号名称}    ${命令名称}    ${参数值}    ${模拟起始地址}    ${通用告警}    ${文档名称}
    ${结果}    ${val}    Run Keyword And Ignore Error    整流器或PU告警量恢复    ${子设备名称}    ${信号名称}    ${命令名称}    ${参数值}    ${模拟起始地址}    ${通用告警}
    写入CSV文档    ${文档名称}    ${信号名称}    ${参数值}    ${结果}    ${val}
    should be true    '${结果}'=='PASS'
    [Teardown]

整流器或PU告警量恢复
    [Arguments]    ${子设备名称}    ${信号名称}    ${命令名称}    ${信号值}    ${模拟起始地址}    ${通用告警}
    ${信号量}    拆分信号量名称和SID    ${信号名称}
    @{信号名称及下标}    split string    ${信号量}[0]    -
    ${获取子工具信号名称}    获取子工具信号名称    ${子设备名称}    ${信号量}[2]
    Return From Keyword If    '${获取子工具信号名称}'==''    获取子工具信号名称为空
    @{信号名称及关联SID}    split string    ${获取子工具信号名称}    ,
    ${temp}    evaluate    ${信号名称及下标}[1]-${模拟起始地址}
    ${子工具整流器地址}    evaluate    ${temp}+1
    ${子工具整流器地址}    Convert to string    ${子工具整流器地址}
    设置子工具值    ${子设备名称}    ${子工具整流器地址}    ${命令名称}    ${信号名称及关联SID}[0]    ${信号值}
    ${告警名称}    set variable    ${通用告警}-${信号名称及下标}[1]
    ${列表}    获取数据字典指定查询的信号量列表_通过SID    ${信号名称及关联SID}[1]    ${信号量}[0]
    FOR    ${i}    IN    @{列表}
        ${关联信号名称}    Get From Dictionary    ${i}    signal_name
        ${关联信号量}    拆分信号量名称和SID    ${关联信号名称}
        ${取值约定dict}    获取web参数的取值约定    ${关联信号名称}
        ${参数值对应的取值约定}    Get From Dictionary    ${取值约定dict}    ${信号值}
        Run Keyword And Continue On Failure    Wait Until Keyword Succeeds    15m    5    信号量数据值为(强制获取)    ${关联信号名称}    ${参数值对应的取值约定}
        Run Keyword And Continue On Failure    wait until keyword succeeds    15m    5    判断内部告警不存在    ${信号量}[0]
        Run Keyword And Continue On Failure    wait until keyword succeeds    10m    5    判断告警不存在    ${告警名称}
    END
    [Teardown]
    [Return]    ${获取子工具信号名称}

整流器或PU故障量产生
    [Arguments]    ${子设备名称}    ${信号名称}    ${命令名称}    ${信号值}    ${模拟起始地址}    ${通用告警}
    ${信号量}    拆分信号量名称和SID    ${信号名称}
    @{信号名称及下标}    split string    ${信号量}[0]    -
    ${获取子工具信号名称}    获取子工具信号名称    ${子设备名称}    ${信号量}[2]
    Return From Keyword If    '${获取子工具信号名称}'==''    获取子工具信号名称为空
    @{信号名称及关联SID}    split string    ${获取子工具信号名称}    ,
    ${temp}    evaluate    ${信号名称及下标}[1]-${模拟起始地址}
    ${子工具整流器地址}    evaluate    ${temp}+1
    ${子工具整流器地址}    Convert to string    ${子工具整流器地址}
    设置子工具值    ${子设备名称}    ${子工具整流器地址}    ${命令名称}    ${信号名称及关联SID}[0]    ${信号值}
    ${故障名称}    set variable    ${通用告警}-${信号名称及下标}[1]
    ${列表}    获取数据字典指定查询的信号量列表_通过SID    ${信号名称及关联SID}[1]    ${信号量}[0]
    FOR    ${i}    IN    @{列表}
        ${关联信号名称}    Get From Dictionary    ${i}    signal_name
        ${关联信号量}    拆分信号量名称和SID    ${关联信号名称}
        ${取值约定dict}    获取web参数的取值约定    ${关联信号名称}
        ${参数值对应的取值约定}    Get From Dictionary    ${取值约定dict}    ${信号值}
        Run Keyword And Continue On Failure    Wait Until Keyword Succeeds    10m    5    信号量数据值为(强制获取)    ${关联信号名称}    ${参数值对应的取值约定}
        Run Keyword And Continue On Failure    wait until keyword succeeds    15m    5    判断内部告警存在    ${信号量}[0]
        Run Keyword And Continue On Failure    wait until keyword succeeds    15m    5    判断告警存在    ${故障名称}
    END
    [Teardown]
    [Return]    ${获取子工具信号名称}

整流器或PU故障量产生封装判断结果
    [Arguments]    ${子设备名称}    ${信号名称}    ${命令名称}    ${参数值}    ${模拟起始地址}    ${通用告警}    ${文档名称}
    ${结果}    ${val}    Run Keyword And Ignore Error    整流器或PU故障量产生    ${子设备名称}    ${信号名称}    ${命令名称}    ${参数值}    ${模拟起始地址}    ${通用告警}
    写入CSV文档    ${文档名称}    ${信号名称}    ${参数值}    ${结果}    ${val}
    should be true    '${结果}'=='PASS'
    [Teardown]

整流器或PU故障量恢复
    [Arguments]    ${子设备名称}    ${信号名称}    ${命令名称}    ${信号值}    ${模拟起始地址}    ${通用告警}
    ${信号量}    拆分信号量名称和SID    ${信号名称}
    @{信号名称及下标}    split string    ${信号量}[0]    -
    ${获取子工具信号名称}    获取子工具信号名称    ${子设备名称}    ${信号量}[2]
    Return From Keyword If    '${获取子工具信号名称}'==''    获取子工具信号名称为空
    @{信号名称及关联SID}    split string    ${获取子工具信号名称}    ,
    ${temp}    evaluate    ${信号名称及下标}[1]-${模拟起始地址}
    ${子工具整流器地址}    evaluate    ${temp}+1
    ${子工具整流器地址}    Convert to string    ${子工具整流器地址}
    设置子工具值    ${子设备名称}    ${子工具整流器地址}    ${命令名称}    ${信号名称及关联SID}[0]    0
    ${故障名称}    set variable    ${通用告警}-${信号名称及下标}[1]
    ${列表}    获取数据字典指定查询的信号量列表_通过SID    ${信号名称及关联SID}[1]    ${信号量}[0]
    FOR    ${i}    IN    @{列表}
        ${关联信号名称}    Get From Dictionary    ${i}    signal_name
        ${关联信号量}    拆分信号量名称和SID    ${关联信号名称}
        ${取值约定dict}    获取web参数的取值约定    ${关联信号名称}
        ${参数值对应的取值约定}    Get From Dictionary    ${取值约定dict}    ${信号值}
        Run Keyword And Continue On Failure    Wait Until Keyword Succeeds    10m    5    信号量数据值为(强制获取)    ${关联信号名称}    ${参数值对应的取值约定}
        Run Keyword And Continue On Failure    wait until keyword succeeds    15m    5    判断内部告警不存在    ${信号量}[0]
        Run Keyword And Continue On Failure    wait until keyword succeeds    15m    5    判断告警不存在    ${故障名称}
    END
    [Teardown]
    [Return]    ${获取子工具信号名称}

整流器或PU故障量恢复封装判断结果
    [Arguments]    ${子设备名称}    ${信号名称}    ${命令名称}    ${参数值}    ${模拟起始地址}    ${通用告警}    ${文档名称}
    ${结果}    ${val}    Run Keyword And Ignore Error    整流器或PU故障量恢复    ${子设备名称}    ${信号名称}    ${命令名称}    ${参数值}    ${模拟起始地址}    ${通用告警}
    写入CSV文档    ${文档名称}    ${信号名称}    ${参数值}    ${结果}    ${val}
    should be true    '${结果}'=='PASS'
    [Teardown]

数值类参数设置最大值最小值默认值
    [Arguments]    ${参数名称}
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    ${参数名称}
    ${缺省值}    获取web参数上下限范围    ${参数名称}
    ${可设置范围}    获取web参数可设置范围    ${参数名称}
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    ${参数名称}    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    ${参数名称}
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        Wait Until Keyword Succeeds    10    2    设置web参数量    ${参数名称}    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    ${参数名称}
        should be true    ${参数获取}==${参数设置}
    END

告警量置位状态量
    [Arguments]    ${子设备名称}    ${信号名称}    ${命令名称}    ${信号值}    ${模拟起始地址}
    ${信号量}    拆分信号量名称和SID    ${信号名称}
    @{信号名称及下标}    split string    ${信号量}[0]    -
    ${获取子工具信号名称}    获取子工具信号名称    ${子设备名称}    ${信号量}[2]
    Return From Keyword If    '${获取子工具信号名称}'==''    获取子工具信号名称为空
    @{信号名称及关联SID}    split string    ${获取子工具信号名称}    ,
    ${temp}    evaluate    ${信号名称及下标}[1]-${模拟起始地址}
    ${子工具整流器地址}    evaluate    ${temp}+1
    ${子工具整流器地址}    Convert to string    ${子工具整流器地址}
    设置子工具值    ${子设备名称}    ${子工具整流器地址}    ${命令名称}    ${信号名称及关联SID}[0]    ${信号值}
    ${列表}    获取数据字典指定查询的信号量列表_通过SID    ${信号名称及关联SID}[1]    ${信号量}[0]
    FOR    ${i}    IN    @{列表}
        ${关联信号名称}    Get From Dictionary    ${i}    signal_name
        ${关联信号量}    拆分信号量名称和SID    ${关联信号名称}
        ${取值约定dict}    获取web参数的取值约定    ${关联信号名称}
        ${参数值对应的取值约定}    Get From Dictionary    ${取值约定dict}    ${信号值}
        Run Keyword And Continue On Failure    Wait Until Keyword Succeeds    10m    5    信号量数据值为(强制获取)    ${关联信号名称}    ${参数值对应的取值约定}
    END
    [Teardown]
    [Return]    ${获取子工具信号名称}

告警量置位状态量封装判断结果
    [Arguments]    ${子设备名称}    ${信号名称}    ${命令名称}    ${参数值}    ${模拟起始地址}    ${文档名称}
    ${结果}    ${val}    Run Keyword And Ignore Error    告警量置位状态量    ${子设备名称}    ${信号名称}    ${命令名称}    ${参数值}    ${模拟起始地址}
    写入CSV文档    ${文档名称}    ${信号名称}    ${参数值}    ${结果}    ${val}
    should be true    '${结果}'=='PASS'
    [Teardown]

特殊量获取值和设置值对比封装判断结果
    [Arguments]    ${子设备名称}    ${信号名称}    ${命令名称}    ${参数值}    ${对比值}    ${文档名称}
    ${结果}    ${val}    Run Keyword And Ignore Error    特殊量获取值和设置值对比    ${子设备名称}    ${信号名称}    ${命令名称}    ${参数值}    ${对比值}
    写入CSV文档    ${文档名称}    ${信号名称}    ${参数值}    ${结果}    ${val}
    should be true    '${结果}'=='PASS'
    [Teardown]

特殊量获取值和设置值对比
    [Arguments]    ${子设备名称}    ${信号名称}    ${命令名称}    ${参数值}    ${对比值}
    ${信号量}    拆分信号量名称和SID    ${信号名称}
    ${获取子工具信号名称}    获取子工具信号名称    ${子设备名称}    ${信号量}[2]
    Return From Keyword If    '${获取子工具信号名称}'==''    获取子工具信号名称为空
    @{子工具信号名称}    split string    ${获取子工具信号名称}    ,
    设置子工具值    ${子设备名称}    all    ${命令名称}    ${子工具信号名称}[0]    ${参数值}
    Wait Until Keyword Succeeds    10m    5    信号量数据值为(强制获取)    ${信号名称}    ${对比值}
    [Teardown]
    [Return]    ${获取子工具信号名称}

南向RS485子设备合并告警量产生
    [Arguments]    ${子设备名称}    ${信号名称}    ${命令名称}    ${信号值}    ${子表SID序号}
    ${信号量}    拆分信号量名称和SID    ${信号名称}
    ${级别设置值}    获取web参数量    ${信号名称}
    run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    ${信号名称}    严重
    ${获取子工具信号名称}    获取子工具信号名称    ${子设备名称}    ${信号量}[2]
    @{信号名称及关联SID}    split string    ${获取子工具信号名称}    ,
    设置子工具值    ${子设备名称}    all    ${命令名称}    ${信号名称及关联SID}[0]    ${信号值}
    ${列表}    获取数据字典指定查询的信号量列表_通过SID    ${信号名称及关联SID}[${子表SID序号}]    ${信号量}[0]
    FOR    ${i}    IN    @{列表}
        ${关联信号名称}    Get From Dictionary    ${i}    signal_name
        ${关联信号量}    拆分信号量名称和SID    ${关联信号名称}
        ${取值约定dict}    获取web参数的取值约定    ${关联信号名称}
        ${参数值对应的取值约定}    Get From Dictionary    ${取值约定dict}    ${信号值}
        Run Keyword And Continue On Failure    Wait Until Keyword Succeeds    10m    5    信号量数据值为(强制获取)    ${关联信号名称}    ${参数值对应的取值约定}
        Run Keyword And Continue On Failure    wait until keyword succeeds    10m    5    判断告警存在    ${信号量}[0]
    END
    [Teardown]

南向RS485子设备合并告警量产生封装判断结果
    [Arguments]    ${子设备名称}    ${信号名称}    ${命令名称}    ${参数值}    ${子表SID序号}    ${文档名称}
    ${status}    Run Keyword And Return Status    南向RS485子设备合并告警量产生    ${子设备名称}    ${信号名称}    ${命令名称}    ${参数值}    ${子表SID序号}
    Run Keyword If    ${status}==False    写入CSV文档    ${文档名称}    ${信号名称}    ${参数值}    Failed
    Run Keyword If    ${status}==True    写入CSV文档    ${文档名称}    ${信号名称}    ${参数值}    Passed
    should be true    ${status}
    [Teardown]

南向RS485子设备合并告警量恢复
    [Arguments]    ${子设备名称}    ${信号名称}    ${命令名称}    ${信号值}    ${子表SID序号}
    ${信号量}    拆分信号量名称和SID    ${信号名称}
    ${级别设置值}    获取web参数量    ${信号名称}
    run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    ${信号名称}    严重
    ${获取子工具信号名称}    获取子工具信号名称    ${子设备名称}    ${信号量}[2]
    @{信号名称及关联SID}    split string    ${获取子工具信号名称}    ,
    设置子工具值    ${子设备名称}    all    ${命令名称}    ${信号名称及关联SID}[0]    ${信号值}
    ${列表}    获取数据字典指定查询的信号量列表_通过SID    ${信号名称及关联SID}[${子表SID序号}]    ${信号量}[0]
    FOR    ${i}    IN    @{列表}
        ${关联信号名称}    Get From Dictionary    ${i}    signal_name
        ${关联信号量}    拆分信号量名称和SID    ${关联信号名称}
        ${取值约定dict}    获取web参数的取值约定    ${关联信号名称}
        ${参数值对应的取值约定}    Get From Dictionary    ${取值约定dict}    ${信号值}
        Run Keyword And Continue On Failure    Wait Until Keyword Succeeds    10m    5    信号量数据值为(强制获取)    ${关联信号名称}    ${参数值对应的取值约定}
        Run Keyword And Continue On Failure    wait until keyword succeeds    10m    5    精准判断告警不存在    ${信号量}[0]
    END
    [Teardown]

南向RS485子设备合并告警量恢复封装判断结果
    [Arguments]    ${子设备名称}    ${信号名称}    ${命令名称}    ${参数值}    ${子表SID序号}    ${文档名称}
    ${status}    Run Keyword And Return Status    南向RS485子设备合并告警量恢复    ${子设备名称}    ${信号名称}    ${命令名称}    ${参数值}    ${子表SID序号}
    Run Keyword If    ${status}==False    写入CSV文档    ${文档名称}    ${信号名称}    ${参数值}    Failed
    Run Keyword If    ${status}==True    写入CSV文档    ${文档名称}    ${信号名称}    ${参数值}    Passed
    should be true    ${status}
    [Teardown]

FB100C2铁锂电池测试前置条件
    [Documentation]    在执行每个测试用例时，需要先运行该关键字
    连接CSU
    控制子工具运行停止    fb100c2    启动
    ${取值约定}    获取web参数的取值约定    电池配置
    ${val}    Get From Dictionary    ${取值约定}    4
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    ${val}
    run keyword and ignore error    Wait Until Keyword Succeeds    10    1    设置web参数量    常规锂电类型    NFB15
    Wait Until Keyword Succeeds    5m    60    设置web控制量    RS485总线设备统计
    sleep    30
    run keyword and ignore error    等待子设备工作正常  16  BMS在位状态  在位  20m

FB100C2铁锂电池测试结束条件
    [Documentation]    在执行每个测试用例时，需要先运行该关键字
    连接CSU
    控制子工具运行停止    fb100c2    关闭
    run keyword and ignore error    Wait Until Keyword Succeeds    10    2    设置web控制量    RS485总线设备统计
    恢复默认值并重新登录    #SUPERMAN用户支持用户参数和站点配置参数恢复，
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    纯铅酸
    Wait Until Keyword Succeeds    10    1    设置web参数量    铅酸类型    普通铅酸电池

模拟数字量告警
    [Arguments]    ${告警名称}    ${继电器动作}
    ${字典}    create dictionary    交流输入空开断=DO1    交流防雷器异常=DO2    直流防雷器异常=DO3    UIB板通讯断=DO4    IDDB板通讯断=DO5    CAN通讯断=DO6    RS485通讯断=DO7    下电告警=DO8    门磁告警=DO9    水淹告警=DO10    烟雾告警=DO11
    ${DO名称}    Get From Dictionary    ${字典}    ${告警名称}
    控制网络继电器DO动作    ${DO名称}    ${继电器动作}

三相整流器测试前置条件
    [Documentation]    在执行每个测试用例时，需要先运行该关键字
    连接CSU
    设置子工具个数    ZXD2500    48
    控制子工具运行停止    ZXD2500    启动
    sleep    1m
    Wait Until Keyword Succeeds    10    2    设置web控制量    CAN总线设备统计
    sleep    4m

三相整流器测试结束条件
    [Documentation]    在执行每个测试用例时，需要先运行该关键字
    连接CSU
    控制子工具运行停止    ZXD2500    关闭
    Wait Until Keyword Succeeds    10    2    设置web控制量    CAN总线设备统计

获取子工具特殊参数直到变化为
    [Arguments]    ${子设备名称}    ${命令名称}    ${参数名称}    ${web参数名称}    ${参数值}
    Wait Until Keyword Succeeds    20    2    设置web参数量    ${web参数名称}    ${参数值}
    sleep    30
    ${子工具参数获取}    获取子工具值    ${子设备名称}    1    ${命令名称}    ${参数名称}
    @{子工具参数获取}    split string    @{子工具参数获取}    ,
    ${参数设置new}    evaluate    ${参数值}/1000
    ${参数设置new}    evaluate    round(${参数设置new}*${65535})
    should be equal as numbers    ${子工具参数获取}[2]    ${参数设置new}

南向子设备32位字符串型参数设置
    [Arguments]    ${子设备名称}    ${信号名称}    ${命令名称}
    ${信号量}    拆分信号量名称和SID    ${信号名称}
    判断web带ID的参数是否存在    ${信号量}[0]
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    ${信号量}[0]
    ${获取子工具信号名称}    获取子工具信号名称    ${子设备名称}    ${信号量}[2]
    Return From Keyword If    '${获取子工具信号名称}'==''    获取子工具信号名称为空
    @{信号名称及关联SID}    split string    ${获取子工具信号名称}    ,
    #1）参数下限
    ${参数下限}    set variable    a    #设置为1个字符
    Wait Until Keyword Succeeds    10    2    设置web参数量    ${信号量}[0]    ${参数下限}
    sleep    5
    ${参数下限_获取}    获取web参数量    ${信号量}[0]    #获取参数
    should be true    '${参数下限_获取}'=='${参数下限}'
    #2）参数上限_超范围
    ${参数设置}    set variable    xxxxxxxxxx xxxxxxxxxx xxxxxxxxxx x    #34个字符超过32
    ${设置结果}    run keyword and return status    设置web参数量    ${信号量}[0]    ${参数设置}
    should not be true    ${设置结果}
    #3）正常范围内设置
    ${参数设置}    set variable    smsong-CMN
    ${临时值}    set variable    smsong-CMN1
    Wait Until Keyword Succeeds    6m    20    获取子工具字符串直到变化为    ${子设备名称}    ${命令名称}    ${信号名称及关联SID}[0]    ${信号量}[0]    ${参数设置}    ${临时值}
    Wait Until Keyword Succeeds    10    2    设置web参数量    ${信号量}[0]    ${参数设置}
    sleep    5
    #获取修改后的参数，是否相应变化
    ${修改后的参数}    获取web参数量    ${信号量}[0]
    #参数修改后，获取版本信息中实际名称是否相应变化
    should be true    '${修改后的参数}'=='${参数设置}'
    #恢复为原名称
    Wait Until Keyword Succeeds    10    2    设置web参数量    ${信号量}[0]    ${原参数}
    [Teardown]

南向子设备32位字符串型参数设置封装判断结果
    [Arguments]    ${子设备名称}    ${信号名称}    ${命令名称}    ${文档名称}
    ${status}    Run Keyword And Return Status    南向子设备32位字符串型参数设置    ${子设备名称}    ${信号名称}    ${命令名称}
    Run Keyword If    ${status}==False    写入CSV文档    ${文档名称}    ${信号名称}    Failed
    Run Keyword If    ${status}==True    写入CSV文档    ${文档名称}    ${信号名称}    Passed
    should be true    ${status}
    [Teardown]

SDU2通讯测试前置条件
    [Documentation]    在执行每个测试用例时，需要先运行该关键字
    连接CSU
    控制子工具运行停止    SDU2    启动
    Wait Until Keyword Succeeds    5m   60    设置web控制量    RS485总线设备统计
    sleep    5m

SDU2通讯测试结束条件
    [Documentation]    在执行每个测试用例时，需要先运行该关键字
    连接CSU
    控制子工具运行停止    SDU2    关闭
    run keyword and ignore error    Wait Until Keyword Succeeds    10    2    设置web控制量    RS485总线设备统计
    
获取子工具字符串直到变化为
    [Arguments]    ${子设备名称}    ${命令名称}    ${参数名称}    ${web参数名称}    ${参数值}    ${临时值}
    Wait Until Keyword Succeeds    20    2    设置web参数量    ${web参数名称}    ${参数值}
    sleep    30
    ${子工具参数获取}    获取子工具值    ${子设备名称}    1    ${命令名称}    ${参数名称}
    @{子工具参数获取}    split string    @{子工具参数获取}    ,
    Run Keyword if    '${子工具参数获取}[2]'!='${参数值}'     重新设置web参数量    ${web参数名称}    ${参数值}    ${临时值}
    ${子工具参数获取}    获取子工具值    ${子设备名称}    1    ${命令名称}    ${参数名称}
    @{子工具参数获取}    split string    @{子工具参数获取}    ,
    should be equal as strings    ${子工具参数获取}[2]    ${参数值}
    
SPCU测试前置条件
    [Documentation]    在执行每个测试用例时，需要先运行该关键字
    连接CSU
    sleep    30
    Wait Until Keyword Succeeds    30    2    设置web控制量    CAN总线设备统计
    控制子工具运行停止    SPCU    启动
    等待子设备工作正常    48    SPCU通讯状态

SPCU测试结束条件
    [Documentation]    在执行每个测试用例时，需要先运行该关键字
    连接CSU
    控制子工具运行停止    SPCU    关闭
    run keyword and ignore error    Wait Until Keyword Succeeds    10    2    设置web控制量    CAN总线设备统计

南向子设备模拟量列表获取值
    [Arguments]    ${子设备名称}        ${命令名称}    ${参数值列表}    ${查询SID序号}   ${文档名称}
    FOR  ${i}      IN    @{参数值列表}
	${信号名称}    Get From Dictionary    ${i}    name
	${参数值}      Get From Dictionary    ${i}    value
    ${信号量}    拆分信号量名称和SID    ${信号名称}
	${获取子工具信号名称}    获取子工具信号名称    ${子设备名称}    ${信号量}[${查询SID序号}]
	Set To Dictionary    ${i}   get_name    ${获取子工具信号名称}
	Continue For Loop If    '${获取子工具信号名称}'==''    
	@{子工具信号名称}    split string    ${获取子工具信号名称}    ,
	设置子工具值    ${子设备名称}    all    ${命令名称}    ${子工具信号名称}[0]    ${参数值}
        
    END	
    FOR   ${i}      IN    @{参数值列表}
	${信号名称}    Get From Dictionary    ${i}    name
	${参数值}      Get From Dictionary    ${i}    value
    ${获取子工具信号名称}     Get From Dictionary   ${i}   get_name
	Continue For Loop If    '${获取子工具信号名称}'==''
	${结果}    ${val}  Run Keyword And Ignore Error    Wait Until Keyword Succeeds    15m    5  信号量数据值为(强制获取)    ${信号名称}    ${参数值}
	Run Keyword And Ignore Error   写入CSV文档    ${文档名称}     ${信号名称}    ${参数值}    ${结果}    ${获取子工具信号名称}
	Run Keyword And Continue On Failure   should be true    '${结果}'=='PASS'
    END
   


南向子设备模拟量获取值列表封装判断结果
    [Arguments]    ${子设备名称}        ${命令名称}    ${参数值列表}    ${查询SID序号}    ${文档名称}
    南向子设备模拟量列表获取值    ${子设备名称}      ${命令名称}    ${参数值列表}    ${查询SID序号}    ${文档名称}
    [Teardown]



南向子设备数字量列表获取值
    [Arguments]    ${子设备名称}       ${命令名称}    ${参数值列表}     ${查询SID序号}     ${文档名称}
    FOR  ${i}      IN    @{参数值列表}
	${信号名称}    Get From Dictionary    ${i}    name
	${参数值}      Get From Dictionary    ${i}    value
	${信号量}    拆分信号量名称和SID    ${信号名称}
	${获取子工具信号名称}    获取子工具信号名称    ${子设备名称}    ${信号量}[${查询SID序号}]
	Set To Dictionary    ${i}   get_name   ${获取子工具信号名称}
	Continue For Loop If    '${获取子工具信号名称}'==''    
	@{信号名称及关联SID}    split string    ${获取子工具信号名称}    ,
	${取值约定dict}    获取web参数的取值约定    ${信号名称}
	${参数值对应的取值约定}    Get From Dictionary    ${取值约定dict}    ${参数值}
	设置子工具值    ${子设备名称}    all    ${命令名称}    ${信号名称及关联SID}[0]    ${参数值} 
	Set To Dictionary    ${i}   get_value   ${参数值对应的取值约定}
    END
    FOR   ${i}      IN    @{参数值列表}
	${信号名称}    Get From Dictionary    ${i}    name
	${参数值}      Get From Dictionary    ${i}    value
	${获取子工具信号名称}	   Get From Dictionary    ${i}    get_name
	Continue For Loop If    '${获取子工具信号名称}'==''
	${参数值对应的取值约定}    Get From Dictionary    ${i}    get_value
	${结果}    ${val}  Run Keyword And Ignore Error    Wait Until Keyword Succeeds    10m    5  信号量数据值为(强制获取)    ${信号名称}    ${参数值对应的取值约定}
	Run Keyword And Ignore Error   写入CSV文档    ${文档名称}     ${信号名称}    ${参数值}    ${结果}    ${获取子工具信号名称}
	Run Keyword And Continue On Failure   should be true    '${结果}'=='PASS'
    END
    

南向子设备数字量获取值列表封装判断结果
    [Arguments]    ${子设备名称}     ${命令名称}    ${参数值列表}     ${查询SID序号}      ${文档名称}
    南向子设备数字量列表获取值    ${子设备名称}       ${命令名称}    ${参数值列表}     ${查询SID序号}     ${文档名称}
    [Teardown]



特殊量获取值列表和设置值列表对比
    [Arguments]    ${子设备名称}      ${命令名称}    ${参数值列表}     ${文档名称}
    FOR  ${i}      IN    @{参数值列表}
		${信号名称}    Get From Dictionary    ${i}    name
		${参数值}	   Get From Dictionary    ${i}    value
		${对比值}  	   Get From Dictionary    ${i}    prepare_value
		${信号量}    拆分信号量名称和SID    ${信号名称}
		${获取子工具信号名称}    获取子工具信号名称    ${子设备名称}    ${信号量}[2]
		Set To Dictionary    ${i}   get_name   ${获取子工具信号名称} 
		Continue For Loop If    '${获取子工具信号名称}'==''    
		@{子工具信号名称}    split string    ${获取子工具信号名称}    ,
		设置子工具值    ${子设备名称}    all    ${命令名称}    ${子工具信号名称}[0]    ${参数值}	
    END
    FOR   ${i}      IN    @{参数值列表}
		${信号名称}    Get From Dictionary    ${i}    name
		${参数值}	   Get From Dictionary    ${i}    value
		${获取子工具信号名称}	   Get From Dictionary    ${i}    get_name
		Continue For Loop If    '${获取子工具信号名称}'==''
		${对比值}	   Get From Dictionary    ${i}    prepare_value
		${结果}    ${val}  Run Keyword And Ignore Error    Wait Until Keyword Succeeds    10m    5    信号量数据值为(强制获取)    ${信号名称}    ${对比值}
	Run Keyword And Ignore Error   写入CSV文档    ${文档名称}     ${信号名称}    ${参数值}    ${结果}    ${获取子工具信号名称}
	Run Keyword And Continue On Failure    should be true    '${结果}'=='PASS'
    END
    


特殊量获取值和设置值列表对比封装判断结果
    [Arguments]    ${子设备名称}      ${命令名称}    ${参数值列表}       ${文档名称}
    Run Keyword And Ignore Error    特殊量获取值列表和设置值列表对比    ${子设备名称}    ${信号名称}    ${命令名称}    ${参数值列表}     ${文档名称}`
    [Teardown]



南向RS485子设备告警量列表产生封装判断结果
    [Arguments]    ${子设备名称}     ${告警列表}   ${命令名称}    ${参数值}    ${文档名称}    ${业务名称}    ${告警设备名称}
    批量设置告警级别    ${业务名称}    ${告警设备名称}    屏蔽    严重
    南向RS485子设备告警量列表产生    ${子设备名称}    ${告警列表}    ${命令名称}    ${参数值}     ${文档名称}
    [Teardown]


南向RS485子设备告警量列表产生
    [Arguments]    ${子设备名称}    ${告警列表}    ${命令名称}    ${信号值}    ${文档名称}
	@{信号量列表}   create list
    FOR   ${i}  IN   @{告警列表}
		${信号名称}    Get From Dictionary    ${i}    name
		${信号量}    拆分信号量名称和SID    ${信号名称}
        ${子工具索引}    根据信号量获取子工具索引    ${信号量}[0] 
		${获取子工具信号名称}    获取子工具信号名称    ${子设备名称}    ${信号量}[2]
		Set To Dictionary    ${i}   get_name   ${获取子工具信号名称}
		Continue For Loop If    '${获取子工具信号名称}'==''    
		@{信号名称及关联SID}    split string    ${获取子工具信号名称}    ,
		设置子工具值    ${子设备名称}     ${子工具索引}  ${命令名称}    ${信号名称及关联SID}[0]    ${信号值} 
		Set To Dictionary    ${i}   get_value_list   ${信号量} 
		Set To Dictionary    ${i}   value_sid   ${信号名称及关联SID} 
		Append To List       ${信号量列表}    ${信号量}[0]
    END
    FOR   ${i}  IN   @{告警列表} 
		${获取子工具信号名称}	   Get From Dictionary    ${i}    get_name
		Continue For Loop If    '${获取子工具信号名称}'=='' 	
		${信号名称}	 Get From Dictionary    ${i}    name
		${信号量}    Get From Dictionary    ${i}    get_value_list
		${信号名称及关联SID}    Get From Dictionary    ${i}    value_sid
		${列表}    获取数据字典指定查询的信号量列表_通过SID    ${信号名称及关联SID}[1]    ${信号量}[0]   
		${结果}    Run Keyword And Ignore Error   Wait Until Keyword Succeeds    10m    5    南向RS485子设备告警量列表产生数据字典    ${列表}    ${信号值}   ${信号量}   ${子设备名称}
	#Run Keyword And Ignore Error   写入CSV文档    ${文档名称}     ${信号名称}    ${信号值}    ${结果}    ${获取子工具信号名称}
	#Run Keyword And Ignore Error   should be true    '${结果}'=='PASS'	
    END
	Run Keyword And Continue On Failure    wait until keyword succeeds    30m    5    批量判断告警存在    ${信号量列表}
	
	

南向RS485子设备告警量列表产生数据字典
    [Arguments]    ${列表}     ${信号值}   ${信号量}   ${子设备名称}
    FOR    ${i}    IN    @{列表} 
		${关联信号名称}    Get From Dictionary    ${i}    signal_name
		${关联信号量}    拆分信号量名称和SID    ${关联信号名称}
		${取值约定dict}    获取web参数的取值约定    ${关联信号名称}
		${信号值}    run keyword if    ${信号值} != 1 and '${子设备名称}' != 'SDU2'    set variable    1
        ...    ELSE IF    ${信号值} == 1 and '${子设备名称}' != 'SDU2'    set variable    1
        ...    ELSE    set variable    0
		${参数值对应的取值约定}    Get From Dictionary    ${取值约定dict}    ${信号值}
        Run Keyword And Continue On Failure    Wait Until Keyword Succeeds    10m    5    信号量数据值为(强制获取)    ${关联信号名称}    ${参数值对应的取值约定}
    END
	
	
		

南向RS485子设备告警量列表恢复封装判断结果
    [Arguments]    ${子设备名称}    ${获取告警列表1}    ${命令名称}    ${参数值}    ${文档名称}    ${业务名称}    ${告警设备名称}
    批量设置告警级别    ${业务名称}    ${告警设备名称}    屏蔽    严重
    南向RS485子设备告警量列表恢复    ${子设备名称}    ${获取告警列表1}    ${命令名称}    ${参数值}    ${文档名称}
    [Teardown]
	
南向RS485子设备告警量列表恢复
    [Arguments]    ${子设备名称}    ${获取告警列表1}    ${命令名称}    ${信号值}    ${文档名称}
	@{信号量列表}     create list
    FOR   ${i}  IN   @{获取告警列表1}
		${信号名称}    Get From Dictionary    ${i}    name
		${信号量}    拆分信号量名称和SID    ${信号名称}
		# ${级别设置值}    获取web参数量    ${信号名称}
		# run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    ${信号名称}    严重
		${获取子工具信号名称}    获取子工具信号名称    ${子设备名称}    ${信号量}[2]
		Set To Dictionary    ${i}   get_name   ${获取子工具信号名称}
		Continue For Loop If   '${获取子工具信号名称}'==''    
		@{信号名称及关联SID}    split string    ${获取子工具信号名称}    ,
		设置子工具值    ${子设备名称}    all    ${命令名称}    ${信号名称及关联SID}[0]    ${信号值} 
		Set To Dictionary    ${i}   value_sid   ${信号名称及关联SID} 
		Set To Dictionary    ${i}   get_value_list   ${信号量}     
		Append To List       ${信号量列表}    ${信号量}[0]
    END
    FOR   ${i}  IN   @{获取告警列表1}
		${获取子工具信号名称}	   Get From Dictionary    ${i}    get_name
		Continue For Loop If   '${获取子工具信号名称}'==''
		${信号量}    Get From Dictionary    ${i}    get_value_list
		${信号名称}	 Get From Dictionary    ${i}    name
		${信号名称及关联SID}    Get From Dictionary    ${i}    value_sid
		${列表}    获取数据字典指定查询的信号量列表_通过SID    ${信号名称及关联SID}[1]    ${信号量}[0]   
		${结果}    Run Keyword And Ignore Error   Wait Until Keyword Succeeds    10m    5    南向RS485子设备告警量列表恢复数据字典    ${列表}    ${信号值}   ${信号量}
	#Run Keyword And Ignore Error   写入CSV文档    ${文档名称}     ${信号名称}    ${信号值}    ${结果}    ${获取子工具信号名称}
	#Run Keyword And Ignore Error   should be true    '${结果}'=='PASS'	
    END
	Run Keyword And Continue On Failure    wait until keyword succeeds    10m    5    精准批量判断告警不存在    ${信号量列表}
  
	
南向RS485子设备告警量列表恢复数据字典
    [Arguments]    ${列表}     ${信号值}   ${信号量} 
    FOR    ${i}    IN    @{列表} 
		${关联信号名称}    Get From Dictionary    ${i}    signal_name
		${关联信号量}    拆分信号量名称和SID    ${关联信号名称}
		${取值约定dict}    获取web参数的取值约定    ${关联信号名称}
		${参数值对应的取值约定}    Get From Dictionary    ${取值约定dict}    ${信号值}
        Run Keyword And Continue On Failure    Wait Until Keyword Succeeds    10m    5    信号量数据值为(强制获取)    ${关联信号名称}    ${参数值对应的取值约定}
    END
	
	
南向子设备字符型厂家信息列表
    [Arguments]    ${子设备名称}    ${信号名称列表}    ${命令名称}    ${信号值}     ${文档名称}
	FOR     ${i}   IN    @{信号名称列表}
        ${信号名称}    Get From Dictionary   ${i}    name
		${信号量}    拆分信号量名称和SID    ${信号名称}
		${获取子工具信号名称}    获取子工具信号名称    ${子设备名称}    ${信号量}[2]
        Set To Dictionary    ${i}     get_name     ${获取子工具信号名称}
		Continue For Loop If    '${获取子工具信号名称}'==''        
		@{信号名称及关联SID}    split string    ${获取子工具信号名称}    , 
		设置子工具值    ${子设备名称}    all    ${命令名称}    ${信号名称及关联SID}[0]    ${信号值}
	END
    FOR     ${i}   IN    @{信号名称列表}   
        ${信号名称}    Get From Dictionary   ${i}    name
        ${获取子工具信号名称}    Get From Dictionary   ${i}    get_name
        Continue For Loop If    '${获取子工具信号名称}'=='' 
        ${结果}	  ${val}  Run Keyword And Ignore Error     Wait Until Keyword Succeeds  30m  5    厂家信号量数据值为(强制获取)     ${信号名称}   ${信号值}
		#${名称}    Strip String    ${名称}
		#${结果} 	Run Keyword And Ignore Error     	should be equal    '${名称}'    '${信号值}'
        Run Keyword And Ignore Error      写入CSV文档    ${文档名称}    ${信号名称}    ${信号值}    ${结果}    ${获取子工具信号名称}
		Run Keyword And Continue On Failure 	should be true    '${结果}'=='PASS'
	END
	
	
南向子设备字符型厂家信息列表封装判断结果
    [Arguments]    ${子设备名称}    ${信号名称列表}    ${命令名称}    ${信号值}    ${文档名称}
    南向子设备字符型厂家信息列表    ${子设备名称}    ${信号名称列表}    ${命令名称}    ${信号值}    ${文档名称}
    [Teardown]

	
南向子设备数值型厂家信息列表
    [Arguments]    ${子设备名称}    ${信号名称列表}    ${命令名称}        ${文档名称}
	FOR    ${i}  IN   @{信号名称列表}
		${信号名称}    Get From Dictionary   ${i}    name
		${信号值}    Get From Dictionary    ${i}    value
		${信号量}    拆分信号量名称和SID    ${信号名称}
		${获取子工具信号名称}    获取子工具信号名称    ${子设备名称}    ${信号量}[2]
        Set To Dictionary    ${i}     get_name     ${获取子工具信号名称}
		Continue For Loop If    '${获取子工具信号名称}'==''   
		@{信号名称及关联SID}    split string    ${获取子工具信号名称}    ,
		设置子工具值    ${子设备名称}    all    ${命令名称}    ${信号名称及关联SID}[0]    ${信号值}
    END
    FOR     ${i}   IN    @{信号名称列表}     
        ${信号名称}    Get From Dictionary   ${i}    name
		${信号值}    Get From Dictionary    ${i}    value
        ${获取子工具信号名称}    Get From Dictionary   ${i}    get_name
        Continue For Loop If    '${获取子工具信号名称}'==''
        ${结果}	   ${val}   Run Keyword And Ignore Error     Wait Until Keyword Succeeds  30m  5    厂家信号量数据值为(强制获取)     ${信号名称}     ${信号值} 
		#${结果}        Run Keyword And Ignore Error 		should be equal As Numbers    ${名称}    ${信号值}
		Run Keyword And Ignore Error   写入CSV文档    ${文档名称}    ${信号名称}    ${信号值}    ${结果}    ${获取子工具信号名称}
		Run Keyword And Continue On Failure   should be true    '${结果}'=='PASS'
    END


南向子设备数值型厂家信息列表封装判断结果
    [Arguments]    ${子设备名称}    ${信号名称列表}    ${命令名称}     ${文档名称}
	南向子设备数值型厂家信息列表    ${子设备名称}    ${信号名称列表}    ${命令名称}      ${文档名称} 
   [Teardown]

   
获取缺省值列表
    [Arguments]     ${信号量列表}   ${缺省值序号}
    @{缺省值列表}   create list
    FOR    ${i}    IN    @{信号量列表}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${缺省值}    	获取web参数上下限范围    ${信号名称}
	    ${dict1}          Create Dictionary 
        Set To Dictionary    ${dict1}     name     ${信号名称}  
        Set To Dictionary    ${dict1}     value    ${缺省值}[${缺省值序号}]
	    Append To List        ${缺省值列表}    ${dict1}
    END
   [Return]     ${缺省值列表}
	

整流器或PU故障量批量产生封装判断结果
    [Arguments]    ${子设备名称}    ${信号量列表}    ${命令名称}    ${参数值}    ${模拟起始地址}    ${通用告警}    ${文档名称}
    整流器或PU故障量批量产生    ${子设备名称}    ${信号量列表}    ${命令名称}    ${参数值}    ${模拟起始地址}    ${通用告警}    ${文档名称}
    [Teardown]


整流器或PU故障量批量产生
    [Arguments]    ${子设备名称}    ${信号量列表}    ${命令名称}    ${参数值}    ${模拟起始地址}    ${通用告警}    ${文档名称}
	@{信号量名称列表}   create list
	@{故障名称列表}   create list
	FOR    ${i}    IN    @{信号量列表}
	    ${信号名称}    Get From Dictionary    ${i}    signal_name
		${信号量}    拆分信号量名称和SID    ${信号名称}
		@{信号名称及下标}    split string    ${信号量}[0]    -
		${获取子工具信号名称}    获取子工具信号名称    ${子设备名称}    ${信号量}[2]
		Set To Dictionary    ${i}   sub_sig_name   ${获取子工具信号名称}
		Continue For Loop If    '${获取子工具信号名称}'==''
		@{信号名称及关联SID}    split string    ${获取子工具信号名称}    ,
		${temp}    evaluate    ${信号名称及下标}[1]-${模拟起始地址}
		${子工具整流器地址}    evaluate    ${temp}+1
		${子工具整流器地址}    Convert to string    ${子工具整流器地址}
		设置子工具值    ${子设备名称}    ${子工具整流器地址}    ${命令名称}    ${信号名称及关联SID}[0]    ${参数值}
		${故障名称}    set variable    ${通用告警}-${信号名称及下标}[1]
		Set To Dictionary    ${i}   sub_sig_name   ${获取子工具信号名称}
        Set To Dictionary    ${i}   signal_v   ${信号量}
        Set To Dictionary    ${i}   name_sid   ${信号名称及关联SID}
		Append To List       ${故障名称列表}    ${故障名称}
		Append To List       ${信号量名称列表}    ${信号量}[0]
	END
	FOR  ${i}     IN    @{信号量列表}
		${获取子工具信号名称}      Get From Dictionary    ${i}    sub_sig_name
		Continue For Loop If    '${获取子工具信号名称}'==''
		${信号名称}      Get From Dictionary    ${i}    signal_name
        ${信号量}    Get From Dictionary    ${i}    signal_v
        ${信号名称及关联SID}    Get From Dictionary    ${i}    name_sid
		${信号量列表__通过SID}    获取数据字典指定查询的信号量列表_通过SID    ${信号名称及关联SID}[1]    ${信号量}[0]
		${结果}    Run Keyword And Continue On Failure   Wait Until Keyword Succeeds    10m    5    判断整流器或PU故障量产生结果    ${信号量列表__通过SID}    ${参数值}
        #Run Keyword And Ignore Error   写入CSV文档    ${文档名称}     ${信号名称}    ${参数值}    ${结果}    ${获取子工具信号名称}
		#Run Keyword And Ignore Error   should be true    '${结果}'=='PASS
	END
	log @{信号量名称列表}
	Run Keyword And Continue On Failure    wait until keyword succeeds    15m    5    批量判断内部告警存在    ${信号量名称列表}
    Run Keyword And Continue On Failure    wait until keyword succeeds    15m    5    批量判断告警存在    ${故障名称列表}

AI通道遍历扩展温度
    [Arguments]    ${扩展温度}    ${扩展温度传感器状态}    ${扩展温度AI通道}
    #扩展温度8路
    FOR    ${i}    IN RANGE    1    9
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}    0    1    系统运行环境    扩展温度_${i}
        ${通道配置}    获取通道配置    ${扩展温度AI通道}
        sleep    5
        ${扩展温度}    获取web实时数据    扩展温度_${i}
        ${扩展温度传感器状态}    获取web实时数据    扩展温度传感器状态_${i}
        Wait Until Keyword Succeeds    2m    2    run keyword if    '${扩展温度}'=='val_invalid'    信号量数据值为    扩展温度传感器状态_${i}    异常
        Continue For Loop If    '${扩展温度}'=='val_invalid'
        Wait Until Keyword Succeeds    2m    2    run keyword if    -40<=${扩展温度}<=100    信号量数据值为    扩展温度传感器状态_${i}    正常
    #斜率设置范围为-10~10
    #零点设置范围为-60~60
    #通过调整零点和斜率使温度超出范围
        run keyword if    -40<=${扩展温度}<=19    设置通道配置    ${扩展温度AI通道}    -60    1    系统运行环境    扩展温度_${i}
        run keyword if    20<=${扩展温度}<=100    设置通道配置    ${扩展温度AI通道}    3    5    系统运行环境    扩展温度_${i}
        sleep    10
        ${扩展温度}    获取web实时数据    扩展温度_${i}
        ${扩展温度传感器状态}    获取web实时数据    扩展温度传感器状态_${i}
        Should Be true    '${扩展温度}'=='val_invalid'
        Should Be true    '${扩展温度传感器状态}'=='异常'
    #将零点与斜率调整为0和1
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}    0    1    系统运行环境    扩展温度_${i}
    END	
	
扩展温度未配置
    #扩展温度8路
    FOR    ${i}    IN RANGE    1    9
        ${扩展温度传感器状态}    获取web实时数据    扩展温度传感器状态_${i}
        Should Be True    '${扩展温度传感器状态}'=='未配置'
        信号量数据值为(强制获取)    扩展温度传感器状态_${i}    未配置
        判断告警不存在    扩展温度无效_${i}
        判断告警不存在    扩展温度低_${i}
        判断告警不存在    扩展温度高_${i}
    END


扩展温度无效告警
    [Arguments]    ${扩展温度}    ${扩展温度AI通道}
    #扩展温度8路
    ${级别设置值}    set Variable
    ${随机序号}    evaluate    random.randint(1, 8)    random
    ${序号列表}    Create List    ${随机序号}
    FOR    ${i}    IN    @{序号列表}
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}    0    1    系统运行环境    扩展温度_${i}
        sleep    10
        ${级别设置值}    获取web参数量    扩展温度无效_${i}
        run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    扩展温度无效_${i}    主要
        ${扩展温度}    获取web实时数据    扩展温度_${i}
        Wait Until Keyword Succeeds    2m    2    Run Keyword If    '${扩展温度}'=='val_invalid'    判断告警存在    扩展温度无效_${i}
        Wait Until Keyword Succeeds    10    2    Run Keyword If    '${扩展温度}'=='val_invalid'    设置web参数量    扩展温度无效_${i}    屏蔽
        Wait Until Keyword Succeeds    10    2    Run Keyword If    '${扩展温度}'=='val_invalid'    判断告警不存在    扩展温度无效_${i}
        Wait Until Keyword Succeeds    10    2    Run Keyword If    '${扩展温度}'=='val_invalid'    设置web参数量    扩展温度无效_${i}    严重
        Wait Until Keyword Succeeds    10    2    Run Keyword If    '${扩展温度}'=='val_invalid'    判断告警存在    扩展温度无效_${i}
        Wait Until Keyword Succeeds    10    2    Run Keyword If    '${扩展温度}'=='val_invalid'    设置web参数量    扩展温度无效_${i}    屏蔽
        sleep    3
        Continue For Loop If    '${扩展温度}'=='val_invalid'
    #斜率设置范围为-10~10
    #零点设置范围为-60~60
    #通过调整零点和斜率使温度超出范围
        run keyword if    -40<=${扩展温度}<=19    设置通道配置    ${扩展温度AI通道}    -60    1    系统运行环境    扩展温度_${i}
        run keyword if    20<=${扩展温度}<=100    设置通道配置    ${扩展温度AI通道}    3    5    系统运行环境    扩展温度_${i}
        sleep    10
        ${扩展温度}    获取web实时数据    扩展温度_${i}
        Should Be true    '${扩展温度}'=='val_invalid'
        ${级别设置值}    获取web参数量    扩展温度无效_${i}
        run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    扩展温度无效_${i}    主要
        Wait Until Keyword Succeeds    2m    2    run keyword if    '${扩展温度}'=='val_invalid'    判断告警存在    扩展温度无效_${i}
        Wait Until Keyword Succeeds    10    2    run keyword if    '${扩展温度}'=='val_invalid'    设置web参数量    扩展温度无效_${i}    屏蔽
        Wait Until Keyword Succeeds    10    2    run keyword if    '${扩展温度}'=='val_invalid'    判断告警不存在    扩展温度无效_${i}
        Wait Until Keyword Succeeds    10    2    run keyword if    '${扩展温度}'=='val_invalid'    设置web参数量    扩展温度无效_${i}    严重
        Wait Until Keyword Succeeds    10    2    run keyword if    '${扩展温度}'=='val_invalid'    判断告警存在    扩展温度无效_${i}
        Wait Until Keyword Succeeds    10    2    run keyword if    '${扩展温度}'=='val_invalid'    设置web参数量    扩展温度无效_${i}    屏蔽
    #将零点与斜率调整为0和1
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}    0    1    系统运行环境    扩展温度_${i}
    END
    

扩展温度低告警
    [Arguments]    ${扩展温度}    ${扩展温度AI通道}
    #扩展温度8路
    ${value}    Set Variable
    ${随机序号}    evaluate    random.randint(1, 8)    random
    ${序号列表}    Create List    ${随机序号}
    FOR    ${i}    IN    @{序号列表}
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}    0    1    系统运行环境    扩展温度_${i}
        wait until keyword succeeds    30    1    设置web参数量    扩展温度低阈值_${i}    20
        sleep    10
        ${级别设置值}    获取web参数量    扩展温度低_${i}
        ${告警阈值}    获取web参数量    扩展温度低阈值_${i}
    #如果扩展温度无效，则跳过校验
        ${扩展温度}    获取web实时数据    扩展温度_${i}
        Continue For Loop If    '${扩展温度}'=='val_invalid'
    #如果扩展温度有效且满足告警产生条件，校验告警
        run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    扩展温度低_${i}    主要
        Wait Until Keyword Succeeds    2m    2    Run Keyword If    ${扩展温度}<${告警阈值}    判断告警存在    扩展温度低_${i}
    #告警屏蔽，告警消失
        Wait Until Keyword Succeeds    10    2    Run Keyword If    ${扩展温度}<${告警阈值}    设置web参数量    扩展温度低_${i}    屏蔽
        Wait Until Keyword Succeeds    10    2    Run Keyword If    ${扩展温度}<${告警阈值}    判断告警不存在    扩展温度低_${i}
        Wait Until Keyword Succeeds    10    2    Run Keyword If    ${扩展温度}<${告警阈值}    设置web参数量    扩展温度低_${i}    严重
        Wait Until Keyword Succeeds    10    2    Run Keyword If    ${扩展温度}<${告警阈值}    判断告警存在    扩展温度低_${i}
    #设置成未配置，告警消失
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}    0    1    无    无
        Wait Until Keyword Succeeds    2m    2    判断告警不存在    扩展温度低_${i}
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}    0    1    系统运行环境    扩展温度_${i}
    #修改告警阈值
    #若满足-40<=扩展温度<=-30,则告警阈值在取值范围内随意取值，告警都存在
        sleep    10
        ${扩展温度}    获取web实时数据    扩展温度_${i}
        ${value}    Evaluate    random.randint(-29,20)
        Wait Until Keyword Succeeds    10    2    设置web参数量    扩展温度低阈值_${i}    ${value}
        Wait Until Keyword Succeeds    2m    2    Run Keyword If    -40<=${扩展温度}<=-30    判断告警存在    扩展温度低_${i}
        Wait Until Keyword Succeeds    10    2    设置web参数量    扩展温度低阈值_${i}    ${告警阈值}
    #如果扩展温度为20，告警阈值为20，将告警阈值修改为16，则告警消失
        ${扩展温度}    获取web实时数据    扩展温度_${i}
        Wait Until Keyword Succeeds    10    2    Run Keyword If    ${扩展温度}==20    设置web参数量    扩展温度低阈值_${i}    16
        Wait Until Keyword Succeeds    2m    2    Run Keyword If    ${扩展温度}==20    判断告警不存在    扩展温度低_${i}
    #如果-30<扩展温度<20,将告警阈值修改成20，则告警存在
        ${扩展温度}    获取web实时数据    扩展温度_${i}
        Wait Until Keyword Succeeds    10    2    Run Keyword If    -30<${扩展温度}<20    设置web参数量    扩展温度低阈值_${i}    20
        Wait Until Keyword Succeeds    2m    2    Run Keyword If    -30<${扩展温度}<20    判断告警存在    扩展温度低_${i}
    #如果20<扩展温度<=40，修改零点和斜率使其产生告警
        ${扩展温度}    获取web实时数据    扩展温度_${i}
        Wait Until Keyword Succeeds    10    2    Run Keyword If    20<${扩展温度}<=40    设置web参数量    扩展温度低阈值_${i}    -20
        wait until keyword succeeds    30    1    Run Keyword If    20<${扩展温度}<=40    设置通道配置    ${扩展温度AI通道}    0    -1    系统运行环境    扩展温度_${i}
        sleep    10
        Wait Until Keyword Succeeds    2m    2    Run Keyword If    20<${扩展温度}<=40    判断告警存在    扩展温度低_${i}
        Wait Until Keyword Succeeds    2m    2    Run Keyword If    20<${扩展温度}<=40    设置web参数量    扩展温度低阈值_${i}    ${告警阈值}
    #如果40<扩展温度<=100,修改零点和斜率使其产生告警
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}    0    1    系统运行环境    扩展温度_${i}
        sleep    10
        ${扩展温度}    获取web实时数据    扩展温度_${i}
        Wait Until Keyword Succeeds    10    2    设置web参数量    扩展温度低阈值_${i}    ${告警阈值}
        wait until keyword succeeds    30    1    Run Keyword If    40<${扩展温度}<=100    设置通道配置    ${扩展温度AI通道}    60    -1    系统运行环境    扩展温度_${i}
        sleep    10
        Wait Until Keyword Succeeds    2m    2    Run Keyword If    40<${扩展温度}<=100    判断告警存在    扩展温度低_${i}
    #告警消失
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}    0    1    系统运行环境    扩展温度_${i}
        Wait Until Keyword Succeeds    2m    2    判断告警不存在    扩展温度低_${i}
    END


扩展温度高告警
    [Arguments]    ${扩展温度}    ${扩展温度AI通道}
    #扩展温度8路
    ${value}    Set Variable
    ${随机序号}    evaluate    random.randint(1, 8)    random
    ${序号列表}    Create List    ${随机序号}
    FOR    ${i}    IN    @{序号列表}
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}    0    1    系统运行环境    扩展温度_${i}
        wait until keyword succeeds    30    1    设置web参数量    扩展温度高阈值_${i}    30
        sleep    10
        ${级别设置值}    获取web参数量    扩展温度高_${i}
        ${告警阈值}    获取web参数量    扩展温度高阈值_${i}
    #如果扩展温度无效，则跳过校验
        ${扩展温度}    获取web实时数据    扩展温度_${i}
        Continue For Loop If    '${扩展温度}'=='val_invalid'
    #如果扩展温度有效且满足告警产生条件，校验告警
        run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    扩展温度高_${i}    主要
        Wait Until Keyword Succeeds    2m    2    Run Keyword If    ${扩展温度}>${告警阈值}    判断告警存在    扩展温度高_${i}
    #告警屏蔽，告警消失
        Wait Until Keyword Succeeds    10    2    Run Keyword If    ${扩展温度}>${告警阈值}    设置web参数量    扩展温度高_${i}    屏蔽
        Wait Until Keyword Succeeds    10    2    Run Keyword If    ${扩展温度}>${告警阈值}    判断告警不存在    扩展温度高_${i}
        Wait Until Keyword Succeeds    10    2    Run Keyword If    ${扩展温度}>${告警阈值}    设置web参数量    扩展温度高_${i}    严重
        Wait Until Keyword Succeeds    10    2    Run Keyword If    ${扩展温度}>${告警阈值}    判断告警存在    扩展温度高_${i}
    #设置成未配置，告警消失
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}    0    1    无    无
        Wait Until Keyword Succeeds    2m    2    判断告警不存在    扩展温度高_${i}
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}    0    1    系统运行环境    扩展温度_${i}
    #修改告警阈值
    #若满足60<扩展温度<=100,则告警阈值在取值范围内随意取值，告警都存在
        sleep    10
        ${扩展温度}    获取web实时数据    扩展温度_${i}
        ${value}    Evaluate    random.randint(30,60)
        Wait Until Keyword Succeeds    10    2    设置web参数量    扩展温度高阈值_${i}    ${value}
        Wait Until Keyword Succeeds    2m    2    Run Keyword If    60<${扩展温度}<=100    判断告警存在    扩展温度高_${i}
        Wait Until Keyword Succeeds    10    2    设置web参数量    扩展温度高阈值_${i}    ${告警阈值}
    #如果扩展温度为30，告警阈值为30，将告警阈值修改为34，则告警消失
        ${扩展温度}    获取web实时数据    扩展温度_${i}
        Wait Until Keyword Succeeds    10    2    Run Keyword If    ${扩展温度}==30    设置web参数量    扩展温度高阈值_${i}    34
        Wait Until Keyword Succeeds    2m    2    Run Keyword If    ${扩展温度}==30    判断告警不存在    扩展温度高_${i}
    #如果30<扩展温度<=60,将告警阈值修改成30，则告警存在
        ${扩展温度}    获取web实时数据    扩展温度_${i}
        Wait Until Keyword Succeeds    10    2    Run Keyword If    30<${扩展温度}<=60    设置web参数量    扩展温度高阈值_${i}    30
        Wait Until Keyword Succeeds    2m    2    Run Keyword If    30<${扩展温度}<=60    判断告警存在    扩展温度高_${i}
    #如果-40<=扩展温度<-30，修改零点和斜率使其产生告警
        ${扩展温度}    获取web实时数据    扩展温度_${i}
        Wait Until Keyword Succeeds    10    2    Run Keyword If    -40<=${扩展温度}<-30    设置web参数量    扩展温度高阈值_${i}    30
        wait until keyword succeeds    30    1    Run Keyword If    -40<=${扩展温度}<-30    设置通道配置    ${扩展温度AI通道}    0    -1    系统运行环境    扩展温度_${i}
        sleep    10
        Wait Until Keyword Succeeds    2m    2    Run Keyword If    -40<=${扩展温度}<-30    判断告警存在    扩展温度高_${i}
    #如果-30<扩展温度<30,修改零点和斜率使其产生告警
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}    0    1    系统运行环境    扩展温度_${i}
        sleep    10
        ${扩展温度}    获取web实时数据    扩展温度_${i}
        Wait Until Keyword Succeeds    10    2    设置web参数量    扩展温度高阈值_${i}    ${告警阈值}
        wait until keyword succeeds    30    1    Run Keyword If    -30<${扩展温度}<30    设置通道配置    ${扩展温度AI通道}    60    -1    系统运行环境    扩展温度_${i}
        sleep    10
        Wait Until Keyword Succeeds    2m    2    Run Keyword If    -30<${扩展温度}<30    判断告警存在    扩展温度高_${i}
    #如果扩展温度=-30,修改零点和斜率使其产生告警
        wait until keyword succeeds    30    1    Run Keyword If    ${扩展温度}==-30    设置通道配置    ${扩展温度AI通道}    1    -1    系统运行环境    扩展温度_${i}
        sleep    10
        Wait Until Keyword Succeeds    2m    2    Run Keyword If    ${扩展温度}==-30    判断告警存在    扩展温度高_${i}
    #告警消失
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}    0    1    系统运行环境    扩展温度_${i}
        Wait Until Keyword Succeeds    2m    2    判断告警不存在    扩展温度高_${i}
    END

温湿度传感器通讯测试前置条件
    [Documentation]    温湿度传感器型号多样，0代表宝力马，1代表雅达
    连接CSU
    Wait Until Keyword Succeeds    10    2    设置web控制量    RS485总线设备统计
    sleep    3m
    ${温湿度传感器设备}    Run Keyword If    '${温湿度传感器型号}'=='0'    Set Variable    DMU_WS312M1
    ${温湿度传感器设备}    Run Keyword If    '${温湿度传感器型号}'=='1'    Set Variable    DMU_YD8779Y
    控制子工具运行停止    ${温湿度传感器设备}    启动
    sleep    3m
    
温湿度传感器通讯测试结束条件
    连接CSU
    ${温湿度传感器设备}    Run Keyword If    '${温湿度传感器型号}'=='0'    Set Variable    DMU_WS312M1
    ${温湿度传感器设备}    Run Keyword If    '${温湿度传感器型号}'=='1'    Set Variable    DMU_YD8779Y
    控制子工具运行停止    ${温湿度传感器设备}    关闭
    Wait Until Keyword Succeeds    10    2    设置web控制量    RS485总线设备统计
    
判断整流器或PU故障量产生结果
	[Arguments]    ${信号量列表__通过SID}    ${参数值}
	FOR    ${i}    IN    @{信号量列表__通过SID}
		${关联信号名称}    Get From Dictionary    ${i}    signal_name
        ${关联信号量}    拆分信号量名称和SID    ${关联信号名称}
        ${取值约定dict}    获取web参数的取值约定    ${关联信号名称}
        ${参数值对应的取值约定}    Get From Dictionary    ${取值约定dict}    ${参数值}
        Run Keyword And Continue On Failure    Wait Until Keyword Succeeds    10m    5    信号量数据值为(强制获取)    ${关联信号名称}    ${参数值对应的取值约定}
	END
	
    
整流器或PU故障量批量恢复
    [Arguments]    ${子设备名称}    ${信号量列表}    ${命令名称}    ${参数值}    ${模拟起始地址}    ${通用告警}    ${文档名称}
	@{信号量名称列表}   create list
	@{故障名称列表}   create list
	FOR  ${i}     IN    @{信号量列表}
	    ${信号名称}    Get From Dictionary    ${i}    signal_name
		${信号量}    拆分信号量名称和SID    ${信号名称}
		@{信号名称及下标}    split string    ${信号量}[0]    -
		${获取子工具信号名称}    获取子工具信号名称    ${子设备名称}    ${信号量}[2]
		Set To Dictionary    ${i}   sub_sig_name   ${获取子工具信号名称}
		Continue For Loop If    '${获取子工具信号名称}'==''
		@{信号名称及关联SID}    split string    ${获取子工具信号名称}    ,
		${temp}    evaluate    ${信号名称及下标}[1]-${模拟起始地址}
		${子工具整流器地址}    evaluate    ${temp}+1
		${子工具整流器地址}    Convert to string    ${子工具整流器地址}
		设置子工具值    ${子设备名称}    ${子工具整流器地址}    ${命令名称}    ${信号名称及关联SID}[0]    0
		${故障名称}    set variable    ${通用告警}-${信号名称及下标}[1]
        Set To Dictionary    ${i}   signal_v   ${信号量}
        Set To Dictionary    ${i}   name_sid   ${信号名称及关联SID}
		Append To List       ${故障名称列表}    ${故障名称}
		Append To List       ${信号量名称列表}    ${信号量}[0]
	END
	FOR  ${i}     IN    @{信号量列表}
		${获取子工具信号名称}      Get From Dictionary    ${i}    sub_sig_name
		Continue For Loop If    '${获取子工具信号名称}'==''
		${信号名称}      Get From Dictionary    ${i}    signal_name
        ${信号量}    Get From Dictionary    ${i}    signal_v
        ${信号名称及关联SID}    Get From Dictionary    ${i}    name_sid
		${信号量列表__通过SID}    获取数据字典指定查询的信号量列表_通过SID    ${信号名称及关联SID}[1]    ${信号量}[0]
		${结果}    Run Keyword And Continue On Failure   Wait Until Keyword Succeeds    10m    5    判断整流器或PU故障量恢复结果    ${信号量列表__通过SID}    ${参数值}
        #Run Keyword And Ignore Error   写入CSV文档    ${文档名称}     ${信号名称}    ${参数值}    ${结果}    ${获取子工具信号名称}
		#Run Keyword And Ignore Error   should be true    '${结果}'=='PASS
	END
	Run Keyword And Continue On Failure    wait until keyword succeeds    15m    5    批量判断内部告警不存在    ${信号量名称列表}
    Run Keyword And Continue On Failure    wait until keyword succeeds    15m    5    批量判断告警不存在    ${故障名称列表}
	
判断整流器或PU故障量恢复结果
	[Arguments]    ${信号量列表__通过SID}    ${参数值}
	FOR    ${i}    IN    @{信号量列表__通过SID}
		${关联信号名称}    Get From Dictionary    ${i}    signal_name
        ${关联信号量}    拆分信号量名称和SID    ${关联信号名称}
        ${取值约定dict}    获取web参数的取值约定    ${关联信号名称}
        ${参数值对应的取值约定}    Get From Dictionary    ${取值约定dict}    ${信号值}
        Run Keyword And Continue On Failure    Wait Until Keyword Succeeds    10m    5    信号量数据值为(强制获取)    ${关联信号名称}    ${参数值对应的取值约定}
	END
	

整流器或PU故障量批量恢复封装判断结果
    [Arguments]    ${子设备名称}    ${信号量列表}    ${命令名称}    ${参数值}    ${模拟起始地址}    ${通用告警}    ${文档名称}
    整流器或PU故障量批量恢复    ${子设备名称}    ${信号量列表}    ${命令名称}    ${参数值}    ${模拟起始地址}    ${通用告警}    ${文档名称}
    [Teardown]

整流器或PU告警量批量产生
    [Arguments]    ${子设备名称}    ${信号量列表}    ${命令名称}    ${参数值}    ${模拟起始地址}    ${通用告警}    ${文档名称}
	@{信号量名称列表}   create list
	@{告警名称列表}   create list
	FOR    ${i}    IN    @{信号量列表}
	    ${信号名称}    Get From Dictionary    ${i}    signal_name
		${信号量}    拆分信号量名称和SID    ${信号名称}
        @{信号名称及下标}    split string    ${信号量}[0]    -
        ${获取子工具信号名称}    获取子工具信号名称    ${子设备名称}    ${信号量}[2]
		Set To Dictionary    ${i}   sub_sig_name   ${获取子工具信号名称}
        Continue For Loop If    '${获取子工具信号名称}'==''
        @{信号名称及关联SID}    split string    ${获取子工具信号名称}    ,
        ${temp}    evaluate    ${信号名称及下标}[1]-${模拟起始地址}
        ${子工具整流器地址}    evaluate    ${temp}+1
        ${子工具整流器地址}    Convert to string    ${子工具整流器地址}
        设置子工具值    ${子设备名称}    ${子工具整流器地址}    ${命令名称}    ${信号名称及关联SID}[0]    ${参数值}
        ${告警名称}    set variable    ${通用告警}-${信号名称及下标}[1]
        Set To Dictionary    ${i}   signal_v   ${信号量}
        Set To Dictionary    ${i}   name_sid   ${信号名称及关联SID}
		Append To List       ${告警名称列表}    ${告警名称}
		Append To List       ${信号量名称列表}    ${信号量}[0]
	END
	
	FOR  ${i}     IN    @{信号量列表}
		${获取子工具信号名称}      Get From Dictionary    ${i}    sub_sig_name
		Continue For Loop If    '${获取子工具信号名称}'==''
		${信号名称}      Get From Dictionary    ${i}    signal_name
        ${信号量}    Get From Dictionary    ${i}    signal_v
        ${信号名称及关联SID}    Get From Dictionary    ${i}    name_sid
		${信号量列表__通过SID}    获取数据字典指定查询的信号量列表_通过SID    ${信号名称及关联SID}[1]    ${信号量}[0]
		${结果}    Run Keyword And Continue On Failure   Wait Until Keyword Succeeds    10m    5    判断整流器或PU告警量产生结果    ${信号量列表__通过SID}    ${参数值}
        #Run Keyword And Ignore Error   写入CSV文档    ${文档名称}     ${信号名称}    ${参数值}    ${结果}    ${获取子工具信号名称}
		#Run Keyword And Ignore Error   should be true    '${结果}'=='PASS
	END
	Run Keyword And Continue On Failure    wait until keyword succeeds    15m    5    批量判断内部告警存在    ${信号量名称列表}
    Run Keyword And Continue On Failure    wait until keyword succeeds    10m    5    批量判断告警存在    ${告警名称列表}
	
	
判断整流器或PU告警量产生结果
	[Arguments]    ${信号量列表__通过SID}    ${参数值}
	FOR    ${i}    IN    @{信号量列表__通过SID}
        ${关联信号名称}    Get From Dictionary    ${i}    signal_name
        ${关联信号量}    拆分信号量名称和SID    ${关联信号名称}
        ${取值约定dict}    获取web参数的取值约定    ${关联信号名称}
        ${参数值对应的取值约定}    Get From Dictionary    ${取值约定dict}    ${参数值}
        Run Keyword And Continue On Failure    Wait Until Keyword Succeeds    15m    5    信号量数据值为(强制获取)    ${关联信号名称}    ${参数值对应的取值约定}
    END
	

整流器或PU告警量批量产生封装判断结果
    [Arguments]    ${子设备名称}    ${信号量列表}    ${命令名称}    ${参数值}    ${模拟起始地址}    ${通用告警}    ${文档名称}
    整流器或PU告警量批量产生    ${子设备名称}    ${信号量列表}    ${命令名称}    ${参数值}    ${模拟起始地址}    ${通用告警}    ${文档名称}
    [Teardown]
	
	
	
整流器或PU告警量批量恢复封装判断结果
    [Arguments]    ${子设备名称}    ${信号量列表}    ${命令名称}    ${参数值}    ${模拟起始地址}    ${通用告警}    ${文档名称}
    整流器或PU告警量批量恢复    ${子设备名称}    ${信号量列表}    ${命令名称}    ${参数值}    ${模拟起始地址}    ${通用告警}    ${文档名称}
    [Teardown]

整流器或PU告警量批量恢复
    [Arguments]    ${子设备名称}    ${信号量列表}    ${命令名称}    ${参数值}    ${模拟起始地址}    ${通用告警}    ${文档名称}
	@{告警名称列表}   create list
	@{信号量名称列表}   create list
	FOR  ${i}     IN    @{信号量列表}
	    ${信号名称}    Get From Dictionary    ${i}    signal_name
		${信号量}    拆分信号量名称和SID    ${信号名称}
        @{信号名称及下标}    split string    ${信号量}[0]    -
        ${获取子工具信号名称}    获取子工具信号名称    ${子设备名称}    ${信号量}[2]
		Set To Dictionary    ${i}   sub_sig_name   ${获取子工具信号名称}
        Continue For Loop If    '${获取子工具信号名称}'==''
        @{信号名称及关联SID}    split string    ${获取子工具信号名称}    ,
        ${temp}    evaluate    ${信号名称及下标}[1]-${模拟起始地址}
        ${子工具整流器地址}    evaluate    ${temp}+1
        ${子工具整流器地址}    Convert to string    ${子工具整流器地址}
        设置子工具值    ${子设备名称}    ${子工具整流器地址}    ${命令名称}    ${信号名称及关联SID}[0]    ${参数值}
        ${告警名称}    set variable    ${通用告警}-${信号名称及下标}[1]
        Set To Dictionary    ${i}   signal_v   ${信号量}
        Set To Dictionary    ${i}   name_sid   ${信号名称及关联SID}
		Append To List       ${告警名称列表}    ${告警名称}
		Append To List       ${信号量名称列表}    ${信号量}[0]
	END
	
	FOR  ${i}     IN    @{信号量列表}
		${获取子工具信号名称}      Get From Dictionary    ${i}    sub_sig_name
		Continue For Loop If    '${获取子工具信号名称}'==''
		${信号名称}      Get From Dictionary    ${i}    signal_name
        ${信号量}    Get From Dictionary    ${i}    signal_v
        ${信号名称及关联SID}    Get From Dictionary    ${i}    name_sid
		${信号量列表__通过SID}    获取数据字典指定查询的信号量列表_通过SID    ${信号名称及关联SID}[1]    ${信号量}[0]
		${结果}    Run Keyword And Continue On Failure   Wait Until Keyword Succeeds    10m    5    判断整流器或PU告警量恢复结果    ${信号量列表__通过SID}    ${参数值}
        #Run Keyword And Ignore Error   写入CSV文档    ${文档名称}     ${信号名称}    ${参数值}    ${结果}    ${获取子工具信号名称}
		#Run Keyword And Ignore Error   should be true    '${结果}'=='PASS
	END
	Run Keyword And Continue On Failure    wait until keyword succeeds    15m    5    批量判断内部告警不存在    ${信号量名称列表}
    Run Keyword And Continue On Failure    wait until keyword succeeds    10m    5    批量判断告警不存在    ${告警名称列表}
	
	
判断整流器或PU告警量恢复结果
	[Arguments]    ${信号量列表__通过SID}    ${参数值}
	FOR    ${i}    IN    @{信号量列表__通过SID}
        ${关联信号名称}    Get From Dictionary    ${i}    signal_name
        ${关联信号量}    拆分信号量名称和SID    ${关联信号名称}
        ${取值约定dict}    获取web参数的取值约定    ${关联信号名称}
        ${参数值对应的取值约定}    Get From Dictionary    ${取值约定dict}    ${参数值}
        Run Keyword And Continue On Failure    Wait Until Keyword Succeeds    15m    5    信号量数据值为(强制获取)    ${关联信号名称}    ${参数值对应的取值约定}
    END

告警量置位状态量列表封装判断结果
    [Arguments]    ${子设备名称}    ${信号名称列表}    ${命令名称}    ${参数值}    ${模拟起始地址}    ${文档名称}
    告警量置位状态量批量    ${子设备名称}    ${信号名称列表}    ${命令名称}    ${参数值}    ${模拟起始地址}    ${文档名称}
    [Teardown]
	
告警量置位状态量批量
    [Arguments]    ${子设备名称}    ${信号名称列表}    ${命令名称}    ${信号值}    ${模拟起始地址}    ${文档名称}
	FOR  ${i}     IN    @{信号名称列表}
	    ${信号名称}    Get From Dictionary    ${i}    name
		${信号量}    拆分信号量名称和SID    ${信号名称}
		@{信号名称及下标}    split string    ${信号量}[0]    -
		${获取子工具信号名称}    获取子工具信号名称    ${子设备名称}    ${信号量}[2]
		Set To Dictionary    ${i}     get_name     ${获取子工具信号名称}  
		Continue For Loop If    '${获取子工具信号名称}'=='' 
		@{信号名称及关联SID}    split string    ${获取子工具信号名称}    ,
		${temp}    evaluate    ${信号名称及下标}[1]-${模拟起始地址}
		${子工具整流器地址}    evaluate    ${temp}+1
		${子工具整流器地址}    Convert to string    ${子工具整流器地址}
		设置子工具值    ${子设备名称}    ${子工具整流器地址}    ${命令名称}    ${信号名称及关联SID}[0]    ${信号值}
		Set To Dictionary    ${i}     signal_value     ${信号量}  
		Set To Dictionary	 ${i}     sid             ${信号名称及关联SID}
	END
	FOR  ${i}     IN    @{信号名称列表}
		${获取子工具信号名称}    Get From Dictionary    ${i}    get_name
		Continue For Loop If    '${获取子工具信号名称}'=='' 
		${信号名称}    Get From Dictionary    ${i}    name
		${信号量}    Get From Dictionary    ${i}    signal_value
		${信号名称及关联SID}    Get From Dictionary    ${i}    sid  
	    ${列表}    获取数据字典指定查询的信号量列表_通过SID    ${信号名称及关联SID}[1]    ${信号量}[0]
		${结果}    Run Keyword And Continue On Failure   Wait Until Keyword Succeeds    10m    5    告警量置位状态量获取    ${列表}    ${信号值}
		Run Keyword And Ignore Error   写入CSV文档    ${文档名称}     ${信号名称}    ${信号值}     ${结果}    ${获取子工具信号名称}
		Run Keyword And Ignore Error   should be true    '${结果}'=='PASS'
	END

告警量置位状态量获取
    [Arguments]    ${列表}    ${信号值}
	FOR    ${i}    IN    @{列表}
        ${关联信号名称}    Get From Dictionary    ${i}    signal_name
        ${关联信号量}    拆分信号量名称和SID    ${关联信号名称}
        ${取值约定dict}    获取web参数的取值约定    ${关联信号名称}
        ${参数值对应的取值约定}    Get From Dictionary    ${取值约定dict}    ${信号值}
        Run Keyword And Continue On Failure    Wait Until Keyword Succeeds    10m    5    信号量数据值为(强制获取)    ${关联信号名称}    ${参数值对应的取值约定}
    END

短接所有do至di
    [Documentation]   8个输出干接点都并接到指定的输入干接点上
    [Arguments]    ${DI通道名称}
    ${DO通道序号列表}    Create List
    FOR    ${i}    IN RANGE    1    9
        Append To List    ${DO通道序号列表}    ${i}
    END
    设置8路DODI短接配置    ${DO通道序号列表}    ${DI通道名称}

等待所有PU工作正常
    FOR    ${地址}    IN RANGE    1    49
        Run Keyword And Ignore Error    Wait Until keyword Succeeds    10m    2    信号量数据值为    PU工作状态-${地址}    正常
    END

等待子设备工作正常
    [Arguments]    ${子设备数量}   ${工作状态名称}    ${状态值}=正常    ${等待时间}=10m
    ${RANGE_END}    evaluate    ${子设备数量}+1
    FOR    ${地址}    IN RANGE    1    ${RANGE_END}
        Wait Until keyword Succeeds    ${等待时间}    2    信号量数据值为    ${工作状态名称}-${地址}    ${状态值}
    END

等待所有锂电在位
    [Arguments]    ${子设备数量}
    ${RANGE_END}    evaluate    ${子设备数量}+1
    FOR    ${地址}    IN RANGE    1    ${RANGE_END}
        Wait Until keyword Succeeds    20m   2    信号量数据值为    BMS在位状态-${地址}    在位
    END


直流电压及电池电压在指定范围内
    [Arguments]    ${设置值}
    ${上限}    evaluate    ${设置值}+0.5
    ${下限}    evaluate    ${设置值}-0.5
    ${获取数据}    获取web实时数据    直流电压
    ${获取数据_bat}    获取web实时数据    电池电压-1
    should be true    ${下限}<=${获取数据}<=${上限}
    should be true    -0.5<${获取数据}-${获取数据_bat}<0.5

交流输入场景切换
    连接CSU
    设置web参数量    交流输入场景    ${场景参数设置}
    sleep    5


电池配置切换
    连接CSU
    设置web参数量    电池配置    铅酸&锂电混用
    sleep    5


导入指定参数文件
    [Arguments]    ${参数文件名}
    连接CSU
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${参数文件名}
    sleep    2m
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    100
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    sleep    2m
	
	
	
	
设置历史记录最大条数
    [Arguments]      ${类型名称}      ${记录类型}     ${最大值}
    ${结果}   设置最大条数      ${类型名称}      ${记录类型}     ${最大值}
    should be true    '${结果}' == 'success'


导入直流配电条件下参数文件
    连接CSU
    ${用户参数文件}    导出参数文件
    &{dict1}    create dictionary    id=pdt.power_subrack    para_name=Subrack Res Integer1    para_val=1
    &{dict2}    create dictionary    id=pdt.power_subrack    para_name=Subrack Res Integer2    para_val=4
    @{配置列表}    create list    ${dict1}    ${dict2}
    修改导出配置文件    ${配置列表}    ${用户参数文件}/config.xml
    压缩文件夹    ${用户参数文件}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${用户参数文件}.zip
    sleep    6m
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    0    1    100
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}


备份参数文件
    连接CSU
    ${用户参数文件}    导出参数文件
    复制文件目录    download//${用户参数文件}    download//${backups_dir}
    压缩文件夹    ${backups_dir}


恢复备份参数文件
    连接CSU
    ${导入结果}    Web关键字_V30.导入参数文件并重新登录  ${backups_dir}.zip
    删除文件    download/${backups_dir}.zip


重新启动FB100B3
    [Arguments]    ${铁锂电池数}=32    ${等待时间}=15m
    控制子工具运行停止    smartli    启动
    配置电池为普通铅酸电池
    run keyword and ignore error    等待子设备工作正常   ${铁锂电池数}   BMS通信断状态    正常    ${等待时间}
    配置电池为FB100B3

重新启动FB100B1
    [Arguments]    ${铁锂电池数}=32    ${等待时间}=15m
    控制子工具运行停止    smartli_fb100b1    启动
    配置电池为普通铅酸电池
    run keyword and ignore error    等待子设备工作正常   ${铁锂电池数}   BMS通信断状态    正常    ${等待时间}
    配置电池为FB100B1

配置电池为FB100B3
    ${取值约定}    获取web参数的取值约定    电池配置
    ${val}    Get From Dictionary    ${取值约定}    2
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    ${val}
    Wait Until Keyword Succeeds    10    1    设置web参数量    智能锂电类型    FB100B3电池


配置电池为FB100B1
    ${取值约定}    获取web参数的取值约定    电池配置
    ${val}    Get From Dictionary    ${取值约定}    2
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    ${val}
    Wait Until Keyword Succeeds    10    1    设置web参数量    智能锂电类型    FB15

配置电池为普通铅酸电池
    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池配置    纯铅酸
    Wait Until Keyword Succeeds    1m    1    设置web参数量    铅酸类型    普通铅酸电池

设置web参数的范围并校验设置成功
    [Arguments]    ${参数名}
    ${可设置范围}    获取web参数可设置范围    ${参数名}
    FOR    ${val}    IN    @{可设置范围}
        设置web参数量    ${参数名}		${val}
    END

导入参数文件压缩包
    [Arguments]    ${参数文件压缩包}    ${密码}=
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件并重新登录    ${参数文件压缩包}
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    0    1    100
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}

自动导入参数文件
    [Arguments]    ${配置列表}    ${用户参数文件}
    修改导出配置文件    ${配置列表}    ${用户参数文件}/config.xml
    ${临时目录}    set variable    ${用户参数文件}_temp
    复制文件目录    download/${用户参数文件}    download/${临时目录}
    压缩文件夹    ${临时目录}
    导入参数文件压缩包    ${临时目录}.zip
    删除文件夹以及文件夹下所有文件  download/${临时目录}
    删除文件    download/${临时目录}.zip

配置为共享共建场景
    ${导出目录}    导出参数文件
    @{配置列表}   create list
    &{dict1}    create dictionary    id=pdt.power_subrack    para_name=Subrack Res Integer1    para_val=1
    &{dict2}    create dictionary    id=pdt.power_subrack    para_name=Subrack Res Integer2   para_val=2
    Append To List    ${配置列表}    ${dict1}    ${dict2}
    自动导入参数文件     ${配置列表}    ${导出目录}
    删除文件夹以及文件夹下所有文件  download/${导出目录}
    set global variable    ${普通场景参数文件压缩包}    ${导出目录}.zip

智能空开通讯测试前置条件
    [Arguments]    ${数量}=40
    [Documentation]    在执行每个测试用例时，需要先运行该关键字
    连接CSU
    sleep    30
    Wait Until Keyword Succeeds    3m    20    设置web控制量    RS485总线设备统计
    设置子工具个数    DMU_IntelAirSwit    ${数量}
    控制子工具运行停止    DMU_IntelAirSwit    启动
    等待子设备工作正常  ${数量}  智能空开工作状态

智能空开通讯测试结束条件
    连接CSU
    控制子工具运行停止    DMU_IntelAirSwit    关闭
    run keyword and ignore error    Wait Until Keyword Succeeds    3m    20    设置web控制量    RS485总线设备统计


获取参数取值约定并比对取值范围
    [Arguments]    ${参数}    ${下限}    ${上限}
    ${取值范围}    获取web参数的取值约定    ${参数}
    should be equal    ${取值范围}[0]    ${下限}
    should be equal    ${取值范围}[1]    ${上限}


批量获取模拟量测试
    [Arguments]   ${模拟量列表}    ${模拟子工具设备名称}    ${模拟子工具变量类型}    ${结果文件名称}
    写入CSV文档    ${结果文件名称}    信号名称    信号值    结果    备注
    连接CSU
    ${缺省值列表}   获取缺省值列表  ${模拟量列表}    1
	Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    ${模拟子工具设备名称}    ${模拟子工具变量类型}
    ...    ${缺省值列表}    2    ${结果文件名称}
    ${缺省值列表}   获取缺省值列表  ${模拟量列表}    2
	Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    ${模拟子工具设备名称}    ${模拟子工具变量类型}
    ...    ${缺省值列表}    2    ${结果文件名称}
    ${缺省值列表}   获取缺省值列表  ${模拟量列表}    0
	Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    ${模拟子工具设备名称}    ${模拟子工具变量类型}
    ...    ${缺省值列表}    2    ${结果文件名称}

批量获取数字量测试
    [Arguments]    ${数字量列表}    ${模拟子工具设备名称}    ${模拟子工具变量类型}    ${结果文件名称}
    写入CSV文档    ${结果文件名称}    信号名称    信号值    结果    备注
    连接CSU
    ${缺省值列表}   获取缺省值列表  ${数字量列表}    1
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    ${模拟子工具设备名称}    ${模拟子工具变量类型}
    ...    ${缺省值列表}    2    ${结果文件名称}
    ${缺省值列表}   获取缺省值列表  ${数字量列表}    2
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    ${模拟子工具设备名称}    ${模拟子工具变量类型}
    ...    ${缺省值列表}    2    ${结果文件名称}
    ${缺省值列表}   获取缺省值列表  ${数字量列表}    0
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    ${模拟子工具设备名称}    ${模拟子工具变量类型}
    ...    ${缺省值列表}    2    ${结果文件名称}

批量获取实时告警测试
    [Arguments]    ${告警列表}    ${模拟子工具设备名称}    ${模拟子工具变量名称}    ${结果文件}    ${业务名称}    ${告警设备名称}
    Run Keyword And Continue On Failure    南向RS485子设备告警量列表产生封装判断结果    ${模拟子工具设备名称}    ${告警列表}
    ...    ${模拟子工具变量名称}    ${告警产生}    ${结果文件}    ${业务名称}    ${告警设备名称}
    Run Keyword And Continue On Failure    南向RS485子设备告警量列表产生封装判断结果    ${模拟子工具设备名称}    ${告警列表}
    ...    ${模拟子工具变量名称}    ${告警恢复}    ${结果文件}    ${业务名称}    ${告警设备名称}

交流空调测试前置条件
    [Arguments]    ${设备名称}
    [Documentation]    设备名称： 英雄克 DMU_EC30HDNC1C， 黑盾 DMU_AC3000X   
    连接CSU
    设置子工具个数    ${设备名称}    1
    控制子工具运行停止    ${设备名称}    启动
    sleep    10
    Wait Until Keyword Succeeds    5m    60    设置web控制量    RS485总线设备统计
    Wait Until keyword Succeeds    5m    5    信号量数据值为    交流空调通讯状态    正常

交流空调测试结束条件
    [Arguments]    ${设备名称}
    [Documentation]    设备名称： 英雄克 DMU_EC30HDNC1C， 黑盾 DMU_AC3000X   
    连接CSU
    控制子工具运行停止    ${设备名称}    关闭
    run keyword and ignore error    Wait Until Keyword Succeeds    10    2    设置web控制量    RS485总线设备统计


根据信号量获取子工具索引
    [Arguments]     ${信号量}
    ${contains}    Run Keyword And Return Status    Should Contain    ${信号量}    -
    @{信号名称及下标}    Run Keyword If    ${contains}   split string    ${信号量}    -
    ${子工具索引}    Run Keyword If    ${contains}    Set Variable    ${信号名称及下标}[1]
    ...    ELSE    Set Variable    all
    [Return]    ${子工具索引}