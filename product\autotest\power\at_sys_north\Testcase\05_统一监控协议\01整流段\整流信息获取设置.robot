*** Settings ***
Test Setup        Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
uniform_0002_整流模拟量√
    [Documentation]     ${power_sm命令名称} | ${节点名} | ${节点序号} | ${查询SID序号}=2 | ${1104/1363表单名称}=None | ${1104/1363命令名称}=None | ${1104/1363数据名称}=None | ${屏号}=None | ${sm子表名称}=锂电池模拟量
    连接CSU
    ${工作整流器地址}    获取工作整流器地址
    ${协议数据}    统一监控协议_获取数据    整流模拟量    #得到[u'1', u'2', u'3']
    ${web数据}    统一监控协议_获取web对应数据    ${协议数据}
    ${对比结果}    批量对比数据_统一监控协议_WEB    ${协议数据}    ${web数据}    整流模拟量    ${工作整流器地址}
    should be true    ${对比结果}

uniform_0004_整流状态量
    [Documentation]    整流器工作状态
    ...
    ...    0:无/Null;1:正常/Normal;2:告警/Alarm;3:通讯断/CommFail;4:升级/Update
    ...
    ...
    ...
    ...    整流器工作状态出错，snmp为0,web为1
    连接CSU
    ${工作整流器地址}    获取工作整流器地址
    ${协议数据}    统一监控协议_获取数据    整流状态量    #得到[u'1', u'2', u'3']
    ${web数据}    统一监控协议_获取web对应数据    ${协议数据}
    ${对比结果}    批量对比数据_统一监控协议_WEB    ${协议数据}    ${web数据}    整流状态量    ${工作整流器地址}
    should be true    ${对比结果}

uniform_0006_整流资产信息√
    连接CSU
    ${工作整流器地址}    获取工作整流器地址
    FOR    ${SMR序号}    IN RANGE    ${模拟整流器地址}    ${北向协议SMR最大数}+1
        Comment    ${整流器ID原始序列号}    获取web实时数据    <<整流器ID-${SMR序号}~0x7001080010001>>
        ${SMR地址}    Evaluate    ${SMR序号}-${模拟整流器地址}+1
        ${SMR地址}    Convert To String    ${SMR地址}
    #序列号4294967295
        ${SMR序列号}    Set Variable    4294967295
        ${SMR序列号}    Convert To String    ${SMR序列号}
        设置子工具值    SMR    ${SMR地址}    版本    SMR序列号    ${SMR序列号}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<整流器ID-${SMR序号}~0x7001080010001>>    ${SMR序列号}
        ${协议数据}    统一监控协议_获取数据    整流资产信息
        ${web数据}    统一监控协议_获取web对应数据    ${协议数据}
        ${对比结果}    批量对比数据_统一监控协议_WEB    ${协议数据}    ${web数据}    整流资产信息    ${工作整流器地址}
        should be true    ${对比结果}
    #序列号2147483646
        ${SMR序列号}    Set Variable    2147483646
        ${SMR序列号}    Convert To String    ${SMR序列号}
        设置子工具值    SMR    ${SMR地址}    版本    SMR序列号    ${SMR序列号}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<整流器ID-${SMR序号}~0x7001080010001>>    ${SMR序列号}
        ${协议数据}    统一监控协议_获取数据    整流资产信息
        ${web数据}    统一监控协议_获取web对应数据    ${协议数据}
        ${对比结果}    批量对比数据_统一监控协议_WEB    ${协议数据}    ${web数据}    整流资产信息    ${工作整流器地址}
        should be true    ${对比结果}
    #序列号2415919102
        ${SMR序列号}    Set Variable    2415919102
        ${SMR序列号}    Convert To String    ${SMR序列号}
        设置子工具值    SMR    ${SMR地址}    版本    SMR序列号    ${SMR序列号}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<整流器ID-${SMR序号}~0x7001080010001>>    ${SMR序列号}
        ${协议数据}    统一监控协议_获取数据    整流资产信息
        ${web数据}    统一监控协议_获取web对应数据    ${协议数据}
        ${对比结果}    批量对比数据_统一监控协议_WEB    ${协议数据}    ${web数据}    整流资产信息    ${工作整流器地址}
        should be true    ${对比结果}
    #序列号-0
        ${SMR序列号}    Set Variable    0
        ${SMR序列号}    Convert To String    ${SMR序列号}
        设置子工具值    SMR    ${SMR地址}    版本    SMR序列号    ${SMR序列号}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<整流器ID-${SMR序号}~0x7001080010001>>    ${SMR序列号}
        ${协议数据}    统一监控协议_获取数据    整流资产信息
        ${web数据}    统一监控协议_获取web对应数据    ${协议数据}
        ${对比结果}    批量对比数据_统一监控协议_WEB    ${协议数据}    ${web数据}    整流资产信息    ${工作整流器地址}
        should be true    ${对比结果}
    #序列号3803448745
        ${SMR序列号}    Set Variable    3803448745
        ${SMR序列号}    Convert To String    ${SMR序列号}
        设置子工具值    SMR    ${SMR地址}    版本    SMR序列号    ${SMR序列号}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<整流器ID-${SMR序号}~0x7001080010001>>    ${SMR序列号}
        ${协议数据}    统一监控协议_获取数据    整流资产信息
        ${web数据}    统一监控协议_获取web对应数据    ${协议数据}
        ${对比结果}    批量对比数据_统一监控协议_WEB    ${协议数据}    ${web数据}    整流资产信息    ${工作整流器地址}
        should be true    ${对比结果}
    #序列号3803448752
        ${SMR序列号}    Set Variable    3803448752
        ${SMR序列号}    Convert To String    ${SMR序列号}
        设置子工具值    SMR    ${SMR地址}    版本    SMR序列号    ${SMR序列号}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<整流器ID-${SMR序号}~0x7001080010001>>    ${SMR序列号}
        ${协议数据}    统一监控协议_获取数据    整流资产信息
        ${web数据}    统一监控协议_获取web对应数据    ${协议数据}
        ${对比结果}    批量对比数据_统一监控协议_WEB    ${协议数据}    ${web数据}    整流资产信息    ${工作整流器地址}
        should be true    ${对比结果}
        ${SMR序号}    Convert To String    ${SMR序号}
        设置子工具值    SMR    ${SMR地址}    版本    SMR序列号    ${SMR序号}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<整流器ID-${SMR序号}~0x7001080010001>>    ${SMR序号}
    END
