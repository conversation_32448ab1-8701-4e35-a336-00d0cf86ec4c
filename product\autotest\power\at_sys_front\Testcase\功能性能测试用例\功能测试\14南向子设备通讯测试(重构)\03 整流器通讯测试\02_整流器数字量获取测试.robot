*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
批量获取整流器数字量测试
    [Tags]    PMSA-NTest
    [Setup]    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    自由
    写入CSV文档    整流器数字量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=digital data    INDEX_DEVICE_CH=整流器
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除数字量信号}    ${排除列表}    1
    ...    ${模拟整流器地址}
    ${缺省值列表}   获取缺省值列表  ${列表1}    1
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    SMR    数字量    ${缺省值列表}    2    整流器数字量获取测试
    ${缺省值列表}   获取缺省值列表  ${列表1}    2
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    SMR    数字量    ${缺省值列表}    2    整流器数字量获取测试
    ${缺省值列表}   获取缺省值列表  ${列表1}    0
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    SMR    数字量    ${缺省值列表}    2    整流器数字量获取测试
    [Teardown]    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全