*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
snmp批量获取SSW模拟量
    写入CSV文档    SSW模拟量获取测试    信号名称    信号值    结果    备注
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=智能空开
    @{排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SSW排除模拟量信号}    ${排除列表}    1
    ${设备名称}    Set Variable    smartSwitch
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    ${设备名称}    analogData
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    1    snmp
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    snmp    DMU_IntelAirSwit    呼叫    ${缺省值列表}    SSW模拟量获取测试    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    2    snmp
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    snmp    DMU_IntelAirSwit    呼叫    ${缺省值列表}    SSW模拟量获取测试    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    0    snmp
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    snmp    DMU_IntelAirSwit    呼叫    ${缺省值列表}    SSW模拟量获取测试    null
    断开连接SNMP

snmp获取智能空开电流
    连接CSU
    ${缺省值}    获取web参数上下限范围    <<智能空开电流~0x2c001010020001>>
    进行SNMP_V2/V3连接    ${snmp连接方式}
    设置子工具值    DMU_IntelAirSwit    all    呼叫    空开电流    125
    FOR    ${直流配电单元序号}    IN RANGE    1    ${SSW最大数目}+1
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<智能空开电流-${直流配电单元序号}~0x2c001010020001>>    125
        ${snmp获取值1}    获取SNMP数据_单个    smartSwitchSmartSwitchCurrent${直流配电单元序号}value
        should be equal as numbers    ${snmp获取值1}    125
    END
    设置子工具值    DMU_IntelAirSwit    all    呼叫    空开电流    0
    FOR    ${直流配电单元序号}    IN RANGE    1    ${SSW最大数目}+1
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<智能空开电流-${直流配电单元序号}~0x2c001010020001>>    0
        ${snmp获取值1}    获取SNMP数据_单个    smartSwitchSmartSwitchCurrent${直流配电单元序号}value
        should be equal as numbers    ${snmp获取值1}    0
    END
    设置子工具值    DMU_IntelAirSwit    all    呼叫    空开电流    10
    FOR    ${直流配电单元序号}    IN RANGE    1    ${SSW最大数目}+1
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<智能空开电流-${直流配电单元序号}~0x2c001010020001>>    10
        ${snmp获取值1}    获取SNMP数据_单个    smartSwitchSmartSwitchCurrent${直流配电单元序号}value
        should be equal as numbers    ${snmp获取值1}    10
    END
