*** Settings ***
Suite Setup       run keywords    连接CSU
...               AND    Wait Until Keyword Succeeds    10    1    设置web参数量    交流制式    L1L2L3N-220V
Test Setup        Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接
Force Tags
Default Tags      T1-1
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
uniform_0004_交流避雷器异常
    实时告警刷新完成
    ${级别设置值}    获取web参数量    交流防雷器异常
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    交流防雷器异常    主要
    wait until keyword succeeds    1m    1    判断告警不存在    交流防雷器异常
    设置web参数量    交流防雷器异常    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    交流防雷器异常
    ${告警}    判断告警存在_带返回值    交流防雷器异常
    should not be true    ${告警}
    模拟数字量告警    交流防雷器异常    ON
    FOR    ${告警级别设置}    IN    主要    次要    警告    严重
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流防雷器异常    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    交流防雷器异常
        sleep    5
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流防雷器异常
        should be true    ${统一监控协议告警结果}
        ${sm告警级别}    获取web告警属性    交流防雷器异常    告警级别
        should be equal    ${告警级别设置}    ${sm告警级别}
    END
    模拟数字量告警    交流防雷器异常    OFF
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流防雷器异常
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流防雷器异常
    should not be true    ${统一监控协议告警结果}
    [Teardown]    run keywords    设置web设备参数量为默认值    交流防雷器异常
    ...    AND    模拟数字量告警    交流防雷器异常    OFF
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0006_交流输出空开断
    [Documentation]    硬件台不支持模拟此告警信息
    [Tags]    3
    Log    硬件台不支持模拟此告警信息
    Log    暂不自动化
    [Teardown]

uniform_0008_交流输入空开断
    [Documentation]    需要修改配置文件
    ...    web上才会显示此告警参数信息
    实时告警刷新完成
    ${级别设置值}    获取web参数量    交流输入空开断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    交流输入空开断    主要
    wait until keyword succeeds    1m    1    判断告警不存在    交流输入空开断
    设置web参数量    交流输入空开断    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    交流输入空开断
    ${告警}    判断告警存在_带返回值    交流输入空开断
    should not be true    ${告警}
    模拟数字量告警    交流输入空开断    ON
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流输入空开断    ${告警级别设置}
        wait until keyword succeeds    3m    1    查询指定告警信息    交流输入空开断
        sleep    5
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流输入空开断
        should be true    ${统一监控协议告警结果}
        ${sm告警级别}    获取web告警属性    交流输入空开断    告警级别
        should be equal    ${sm告警级别}    ${告警级别设置}
    END
    模拟数字量告警    交流输入空开断    OFF
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流输入空开断
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流输入空开断
    should not be true    ${统一监控协议告警结果}
    [Teardown]    run keywords    设置web设备参数量为默认值    交流输入空开断
    ...    AND    模拟数字量告警    交流输入空开断    OFF
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0010_交流相不平衡√
    [Documentation]    告警级别，默认，主要
    [Setup]    run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压不平衡
    ${可设置范围}    获取web参数可设置范围    交流电压不平衡阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压不平衡阈值    ${可设置范围}[0]
    ${电压低设置值}    获取web参数量    交流电压不平衡阈值
    ${判断点1}    evaluate    220-${电压低设置值}-5
    Comment    分别设置各相电压频率    190    250    220    50
    分别设置各相电压频率    ${判断点1}    250    220    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压不平衡    屏蔽    #默认级别：2
    sleep    3
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流电压不平衡
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流电压不平衡    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    交流电压不平衡
        sleep    5
    #统一监控协议查询告警存在
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流电压不平衡
        should be true    ${统一监控协议告警结果}
    #查询web端级别
        ${交流电压不平衡告警级别}    获取web告警属性    交流电压不平衡    告警级别
        should be equal    ${告警级别设置}    ${交流电压不平衡告警级别}
    END
    设置web参数量    交流电压不平衡    主要
    #无告警后查询判断
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流电压不平衡
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流电压不平衡
    should not be true    ${统一监控协议告警结果}
    [Teardown]    run keywords    设置web参数量    交流电压不平衡干接点    0
    ...    AND    设置web设备参数量为默认值    交流电压不平衡阈值
    ...    AND    同时设置三相电压频率    220    50
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0012_交流缺相L1√
    [Setup]    run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流缺相_1
    分别设置各相电压频率    0    220    220    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流缺相_1    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    2m    1    查询指定告警信息不为    交流缺相
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流缺相_1    ${告警级别设置}
        sleep    2
        wait until keyword succeeds    1m    1    查询指定告警信息    交流缺相
    #统一监控协议查询告警存在
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流缺相
        should be true    ${统一监控协议告警结果}
    #查询web端级别
        ${交流缺相告警级别}    获取web告警属性    交流缺相_1    告警级别
        should be equal    '${告警级别设置}'    '${交流缺相告警级别}'
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    交流缺相_1    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    #无告警后查询判断
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流缺相_1
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流缺相
    should not be true    ${统一监控协议告警结果}
    [Teardown]    run keywords    同时设置三相电压频率    220    50
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0014_交流缺相L2√
    [Setup]    run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流缺相_2
    分别设置各相电压频率    220    0    220    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流缺相_2    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    2m    1    查询指定告警信息不为    交流缺相
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流缺相_2    ${告警级别设置}
        sleep    2
        wait until keyword succeeds    1m    1    查询指定告警信息    交流缺相
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流缺相
        should be true    ${统一监控协议告警结果}
        ${交流缺相告警级别}    获取web告警属性    交流缺相_2    告警级别
        should be equal    '${告警级别设置}'    '${交流缺相告警级别}'
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    交流缺相_2    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    #无告警后查询判断
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流缺相_2
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流缺相
    should not be true    ${统一监控协议告警结果}
    [Teardown]    run keywords    同时设置三相电压频率    220    50
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0016_交流缺相L3√
    [Setup]    run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流缺相_3
    分别设置各相电压频率    220    220    0    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流缺相_3    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    2m    1    查询指定告警信息不为    交流缺相
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流缺相_3    ${告警级别设置}
        sleep    2
        wait until keyword succeeds    1m    1    查询指定告警信息    交流缺相
    #统一监控协议查询告警存在
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流缺相
        should be true    ${统一监控协议告警结果}
    #查询web端级别
        ${交流缺相告警级别}    获取web告警属性    交流缺相_3    告警级别
        should be equal    '${告警级别设置}'    '${交流缺相告警级别}'
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    交流缺相_3    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    #无告警后查询判断
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流缺相_3
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流缺相
    should not be true    ${统一监控协议告警结果}
    [Teardown]    run keywords    同时设置三相电压频率    220    50
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0018_交流停电√
    [Documentation]    获取1104一条告警值并判断告警存在
    ...
    ...    输入：
    ...
    ...
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${理应值}= | ${屏号}=None | ${g_ver}=
    [Setup]    run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    停电
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    交流供电状态    市电1
    关闭交流源输出
    Wait Until Keyword Succeeds    5m    2    设置web参数量    市电停电    屏蔽    #默认级别：2
    sleep    3
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    市电停电
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流停电    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    交流停电
        sleep    5
    #统一监控协议查询告警存在
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流停电
        should be true    ${统一监控协议告警结果}
    #查询web端级别
        ${交流停电告警级别}    获取web告警属性    交流停电    告警级别
        should be equal    '${告警级别设置}'    '${交流停电告警级别}'
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    市电停电    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    #无告警后查询判断
    打开交流源输出
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流停电
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流停电
    should not be true    ${统一监控协议告警结果}
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接
    ...    AND    打开交流源输出

uniform_0020_输入电流L3过流
    [Setup]    run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电流
    ${可设置范围}    获取web参数可设置范围    交流电流高阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电流高阈值    ${可设置范围}[0]
    设置负载电压电流    53.5    80
    打开负载输出
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    系统停电
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电流高_3    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    2    判断告警不存在    交流电流高_3
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    10    2    设置web参数量    交流电流高_3    ${告警级别设置}
        sleep    2m
        wait until keyword succeeds    1m    1    查询指定告警信息    交流电流高
    #统一监控协议查询告警存在
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流电流高_3
        should be true    ${统一监控协议告警结果}
    #查询web端级别
        ${交流电压低告警级别}    获取web告警属性    交流电流高_3    告警级别
        should be equal    ${告警级别设置}    ${交流电压低告警级别}
    END
    #无告警后查询判断
    Wait Until Keyword Succeeds    2m    1    设置web设备参数量为默认值    交流电流高阈值
    同时设置三相电压频率    220    50
    关闭负载输出
    Wait Until Keyword Succeeds    4m    1    判断告警不存在    交流电流高_3
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流电流高_3
    should not be true    ${统一监控协议告警结果}
    [Teardown]    run keywords    设置web设备参数量为默认值    交流电流高阈值
    ...    AND    关闭负载输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0022_输入电流L2过流
    [Setup]    run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电流
    ${可设置范围}    获取web参数可设置范围    交流电流高阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电流高阈值    ${可设置范围}[0]
    设置负载电压电流    53.5    80
    打开负载输出
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    系统停电
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电流高_2    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    2    判断告警不存在    交流电流高_2
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    10    2    设置web参数量    交流电流高_2    ${告警级别设置}
        sleep    2m
        wait until keyword succeeds    1m    1    查询指定告警信息    交流电流高
    #统一监控协议查询告警存在
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流电流高_2
        should be true    ${统一监控协议告警结果}
    #查询web端级别
        ${交流电压低告警级别}    获取web告警属性    交流电流高_2    告警级别
        should be equal    ${告警级别设置}    ${交流电压低告警级别}
    END
    #无告警后查询判断
    Wait Until Keyword Succeeds    2m    1    设置web设备参数量为默认值    交流电流高阈值
    同时设置三相电压频率    220    50
    关闭负载输出
    Wait Until Keyword Succeeds    4m    1    判断告警不存在    交流电流高_2
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流电流高_2
    should not be true    ${统一监控协议告警结果}
    [Teardown]    run keywords    设置web设备参数量为默认值    交流电流高阈值
    ...    AND    关闭负载输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0024_输入电流L1过流
    [Setup]    run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电流
    ${可设置范围}    获取web参数可设置范围    交流电流高阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电流高阈值    ${可设置范围}[0]
    设置负载电压电流    53.5    80
    打开负载输出
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    系统停电
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电流高_1    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    2    判断告警不存在    交流电流高
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    10    2    设置web参数量    交流电流高_1    ${告警级别设置}
        sleep    2m
        wait until keyword succeeds    1m    1    查询指定告警信息    交流电流高
    #统一监控协议查询告警存在
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流电流高_1
        should be true    ${统一监控协议告警结果}
    #查询web端级别
        ${交流电压低告警级别}    获取web告警属性    交流电流高_1    告警级别
        should be equal    ${告警级别设置}    ${交流电压低告警级别}
    END
    #无告警后查询判断
    Wait Until Keyword Succeeds    2m    1    设置web设备参数量为默认值    交流电流高阈值
    同时设置三相电压频率    220    50
    关闭负载输出
    Wait Until Keyword Succeeds    4m    1    判断告警不存在    交流电流高_1
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流电流高_1
    should not be true    ${统一监控协议告警结果}
    [Teardown]    run keywords    设置web设备参数量为默认值    交流电流高阈值
    ...    AND    关闭负载输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0026_输入线/相电压L3 L1/ L3缺相√
    [Setup]    run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流缺相_3
    分别设置各相电压频率    220    220    0    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流缺相_3    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    2m    1    查询指定告警信息不为    交流缺相
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流缺相_3    ${告警级别设置}
        sleep    2
        wait until keyword succeeds    1m    1    查询指定告警信息    交流缺相
    #统一监控协议查询告警存在
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流缺相_3
        should be true    ${统一监控协议告警结果}
    #查询web端级别
        ${交流缺相告警级别}    获取web告警属性    交流缺相_3    告警级别
        should be equal    '${告警级别设置}'    '${交流缺相告警级别}'
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    交流缺相_3    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    #无告警后查询判断
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流缺相_3
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流缺相_3
    should not be true    ${统一监控协议告警结果}
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接
    ...    AND    同时设置三相电压频率    220    50

uniform_0028_输入线/相电压L2 L3/ L2缺相√
    [Setup]    run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流缺相_2
    分别设置各相电压频率    220    0    220    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流缺相_2    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    2m    1    查询指定告警信息不为    交流缺相
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流缺相_2    ${告警级别设置}
        sleep    2
        wait until keyword succeeds    1m    1    查询指定告警信息    交流缺相
    #统一监控协议查询告警存在
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流缺相_2
        should be true    ${统一监控协议告警结果}
    #查询web端级别
        ${交流缺相告警级别}    获取web告警属性    交流缺相_2    告警级别
        should be equal    '${告警级别设置}'    '${交流缺相告警级别}'
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    交流缺相_2    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    #无告警后查询判断
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流缺相_2
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流缺相_2
    should not be true    ${统一监控协议告警结果}
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接
    ...    AND    同时设置三相电压频率    220    50

uniform_0030_输入线/相电压L1 L2/ L1缺相√
    [Setup]    run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流缺相_1
    分别设置各相电压频率    0    220    220    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流缺相_1    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    2m    1    查询指定告警信息不为    交流缺相
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流缺相_1    ${告警级别设置}
        sleep    2
        wait until keyword succeeds    1m    1    查询指定告警信息    交流缺相
    #统一监控协议查询告警存在
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流缺相_1
        should be true    ${统一监控协议告警结果}
    #查询web端级别
        ${交流缺相告警级别}    获取web告警属性    交流缺相_1    告警级别
        should be equal    '${告警级别设置}'    '${交流缺相告警级别}'
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    交流缺相_1    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    #无告警后查询判断
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流缺相_1
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流缺相_1
    should not be true    ${统一监控协议告警结果}
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接
    ...    AND    同时设置三相电压频率    220    50

uniform_0032_输入线/相电压L3 L1/ L3欠压√
    [Documentation]    获取1104一条告警值并判断告警存在
    ...    输入
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${理应值}= | ${屏号}=None | ${g_ver}=
    [Setup]    run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压低_3
    ${可设置范围}    获取web参数可设置范围    交流电压低阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压低阈值    ${可设置范围}[1]
    ${电压低设置值}    获取web参数量    交流电压低阈值
    ${判断点1}    evaluate    ${电压低设置值}-5
    分别设置各相电压频率    220    220    ${判断点1}    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压低_3    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压低_3
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流电压低_3    ${告警级别设置}
        sleep    2
        wait until keyword succeeds    1m    1    查询指定告警信息    交流电压低
    #统一监控协议查询告警存在
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流电压低_3
        should be true    ${统一监控协议告警结果}
    #查询web端级别
        ${交流电压低告警级别}    获取web告警属性    交流电压低_3    告警级别
        should be equal    ${告警级别设置}    ${交流电压低告警级别}
    END
    设置web参数量    交流电压低_3    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    #无告警后查询判断
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流电压低_3
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流电压低_3
    should not be true    ${统一监控协议告警结果}
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接
    ...    AND    同时设置三相电压频率    220    50

uniform_0034_输入线/相电压L2 L3/ L2欠压√
    [Documentation]    获取1104一条告警值并判断告警存在
    ...    输入
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${理应值}= | ${屏号}=None | ${g_ver}=
    [Setup]    run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压低_2
    ${可设置范围}    获取web参数可设置范围    交流电压低阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压低阈值    ${可设置范围}[1]
    ${电压低设置值}    获取web参数量    交流电压低阈值
    ${判断点1}    evaluate    ${电压低设置值}-5
    分别设置各相电压频率    220    ${判断点1}    220    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压低_2    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压低_2
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流电压低_2    ${告警级别设置}
        sleep    2
        wait until keyword succeeds    1m    1    查询指定告警信息    交流电压低
    #统一监控协议查询告警存在
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流电压低_2
        should be true    ${统一监控协议告警结果}
    #查询web端级别
        ${交流电压低告警级别}    获取web告警属性    交流电压低_2    告警级别
        should be equal    ${告警级别设置}    ${交流电压低告警级别}
    END
    设置web参数量    交流电压低_2    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    #无告警后查询判断
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流电压低_2
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流电压低_2
    should not be true    ${统一监控协议告警结果}
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接
    ...    AND    同时设置三相电压频率    220    50

uniform_0036_输入线/相电压L1 L2/ L1欠压√
    [Documentation]    获取1104一条告警值并判断告警存在
    ...    输入
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${理应值}= | ${屏号}=None | ${g_ver}=
    ...
    ...
    ...
    ...    0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning; 0xFF：无效值
    [Setup]    run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压低_1
    ${可设置范围}    获取web参数可设置范围    交流电压低阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压低阈值    ${可设置范围}[1]
    ${电压低设置值}    获取web参数量    交流电压低阈值
    ${判断点1}    evaluate    ${电压低设置值}-5
    分别设置各相电压频率    ${判断点1}    220    220    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压低_1    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压低
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流电压低_1    ${告警级别设置}
        sleep    2
        wait until keyword succeeds    1m    1    查询指定告警信息    交流电压低
    #统一监控协议查询告警存在
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流电压低_1
        should be true    ${统一监控协议告警结果}
    #查询web端级别
        ${交流电压低告警级别}    获取web告警属性    交流电压低_1    告警级别
        should be equal    ${告警级别设置}    ${交流电压低告警级别}
    END
    设置web参数量    交流电压低_1    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    #无告警后查询判断
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流电压低_1
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流电压低_1
    should not be true    ${统一监控协议告警结果}
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接
    ...    AND    同时设置三相电压频率    220    50

uniform_0038_输入线/相电压L3 L1/ L3过压√
    [Setup]    run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压高_3
    ${可设置范围}    获取web参数可设置范围    交流电压高阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高阈值    ${可设置范围}[0]
    ${电压高设置值}    获取web参数量    交流电压高阈值
    ${判断点1}    evaluate    ${电压高设置值}+5
    分别设置各相电压频率    220    220    ${判断点1}    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高_3    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    交流电压高_3
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流电压高_3    ${告警级别设置}
        sleep    2
        wait until keyword succeeds    1m    1    查询指定告警信息    交流电压高
    #统一监控协议查询告警存在
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流电压高_3
        should be true    ${统一监控协议告警结果}
    #查询web端级别
        ${交流电压低告警级别}    获取web告警属性    交流电压高_3    告警级别
        should be equal    ${告警级别设置}    ${交流电压低告警级别}
    END
    设置web参数量    交流电压高_3    主要
    #无告警后查询判断
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流电压高_3
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流电压高_3
    should not be true    ${统一监控协议告警结果}
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接
    ...    AND    同时设置三相电压频率    220    50

uniform_0040_输入线/相电压L2 L3/ L2过压√
    [Setup]    run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压高_2
    ${可设置范围}    获取web参数可设置范围    交流电压高阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高阈值    ${可设置范围}[0]
    ${电压高设置值}    获取web参数量    交流电压高阈值
    ${判断点1}    evaluate    ${电压高设置值}+5
    分别设置各相电压频率    220    ${判断点1}    220    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高_2    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    交流电压高_2
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流电压高_2    ${告警级别设置}
        sleep    2
        wait until keyword succeeds    1m    1    查询指定告警信息    交流电压高
    #统一监控协议查询告警存在
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流电压高_2
        should be true    ${统一监控协议告警结果}
    #查询web端级别
        ${交流电压低告警级别}    获取web告警属性    交流电压高_2    告警级别
        should be equal    ${告警级别设置}    ${交流电压低告警级别}
    END
    设置web参数量    交流电压高_2    主要
    #无告警后查询判断
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流电压高_2
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流电压高_2
    should not be true    ${统一监控协议告警结果}
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接
    ...    AND    同时设置三相电压频率    220    50

uniform_0042_输入线/相电压L1 L2/ L1过压√
    [Setup]    run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压高_1
    ${可设置范围}    获取web参数可设置范围    交流电压高阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高阈值    ${可设置范围}[0]
    ${电压高设置值}    获取web参数量    交流电压高阈值
    ${判断点1}    evaluate    ${电压高设置值}+5
    分别设置各相电压频率    ${判断点1}    220    220    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高_1    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    交流电压高
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流电压高_1    ${告警级别设置}
        sleep    2
        wait until keyword succeeds    1m    1    查询指定告警信息    交流电压高
    #统一监控协议查询告警存在
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流电压高_1
        should be true    ${统一监控协议告警结果}
    #查询web端级别
        ${交流电压低告警级别}    获取web告警属性    交流电压高_1    告警级别
        should be equal    ${告警级别设置}    ${交流电压低告警级别}
    END
    设置web参数量    交流电压高_1    主要
    #无告警后查询判断
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流电压高_1
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    交流告警量    交流电压高_1
    should not be true    ${统一监控协议告警结果}
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接
    ...    AND    同时设置三相电压频率    220    50
