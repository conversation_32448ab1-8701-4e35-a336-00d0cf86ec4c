*** Settings ***
Suite Setup       #解析本地MIB文件
Suite Teardown    #生成测试报告
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
snmp_0004_systemACInput1analogDatal√
    Sleep    10min
    ${比较结果}    对比数据_V2C    systemACInput1analogData
    should be true    ${比较结果}

snmp_0006_systemACInput1digitalDatal√
    ${比较结果}    对比数据_V2C    systemACInput1digitalData
    should be true    ${比较结果}

snmp_0008_systemACInput1alarml√
    ${比较结果}    对比告警_V2C    systemACInput1alarm
    should be true    ${比较结果}

snmp_0010_systemACInput1alarm_levell√
    ${比较结果}    对比告警级别_V2C    systemACInput1alarm
    should be true    ${比较结果}

snmp_0012_systemACInput1alarm_level_writel√
    Comment    ${比较结果}    批量修改告警级别_V2C    systemACInput1alarm
    Comment    should be true    ${比较结果}
    ${比较结果}    批量设置四种/五种告警级别    systemACInput1alarm
    should be true    ${比较结果}

snmp_0014_systemACInput1_relayl√
    ${比较结果}    对比告警干接点_V2C    systemACInput1alarm
    should be true    ${比较结果}

snmp_0016_systemACInput1_relay_writel√
    ${比较结果}    批量修改告警干接点_V2C    systemACInput1alarm
    should be true    ${比较结果}

snmp_0018_systemACInput1parameterX
    ${比较结果}    对比数据_V2C    systemACInput1parameter
    should be true    ${比较结果}

snmp_0020_systemACInput1parameter_writeX
    [Documentation]    精度问题 0.0、100.0等
    ...
    ...
    ...    抽空改一下 交流输入场景为 纯油机，以保证柴油发电机功率能设置
    Comment    ${比较结果}    批量修改参数_V2C    systemACInput1parameter
    Comment    should be true    ${比较结果}
    ${比较结果}    SNMP批量参数设置    systemACInput1parameter
    should be true    ${比较结果}
