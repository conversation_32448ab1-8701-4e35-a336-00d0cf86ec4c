*** Settings ***
Suite Setup       设置历史记录最大条数    solar_work    5    100
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
WEB导出太阳能工作记录测试
    连接CSU
    ${数据条数1}    ${第一条数据内容1}    ${最新一条数据内容1}    查询指定历史记录    太阳能工作记录
    ${导出路径}    导出指定历史记录    太阳能工作记录    ${datapassword}
    ${数据条数2}    ${第一条数据内容2}    ${最新一条数据内容2}    解析指定历史记录文件    太阳能工作记录    ${导出路径}
    should be equal    ${第一条数据内容1}[0]    ${第一条数据内容2}[0]
    should be equal    ${数据条数1}    ${数据条数2}
    should be equal    ${最新一条数据内容1}[1]    ${最新一条数据内容2}[1]
    Run Keyword if    ${数据条数1}!=False and ${数据条数2}!=False    should be equal    ${第一条数据内容1}[0]    ${第一条数据内容2}[0]
    Run Keyword if    ${数据条数1}!=False and ${数据条数2}!=False    should be equal    ${数据条数1}    ${数据条数2}
    Run Keyword if    ${数据条数1}!=False and ${数据条数2}!=False    should be equal    ${最新一条数据内容1}[1]    ${最新一条数据内容2}[1]
