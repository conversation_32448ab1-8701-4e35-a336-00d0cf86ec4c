*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
1104_0002_V_3.0_1104_通用命令_厂家信息1-4_1
    [Tags]    3
    ${1104数据}    通用命令_获取厂家信息    ${g_prtcl_1104}    <<获取设备（监控模块）厂家信息1-6 (自定义)~CID1=40H>>    ${g_ver_1104}
    ${web数据}    通过1104_data获取web数据    ${1104数据}
    ${对比结果}    批量对比数据_1104_WEB    ${1104数据}    ${web数据}    1104_厂家信息_1
    should be true    ${对比结果}

1104_0004_V_3.0_1104_通用命令_厂家信息1-4_2
    [Tags]    3
    ${1104数据}    通用命令_获取厂家信息    ${g_prtcl_1104}    <<获取设备（监控模块）厂家信息1-6 (自定义)~CID1=41H>>    ${g_ver_1104}
    ${web数据}    通过1104_data获取web数据    ${1104数据}
    ${对比结果}    批量对比数据_1104_WEB    ${1104数据}    ${web数据}    1104_厂家信息_2
    should be true    ${对比结果}

1104_0006_V_3.0_1104_通用命令_厂家信息1-4_3
    [Tags]    3
    ${1104数据}    通用命令_获取厂家信息    ${g_prtcl_1104}    <<获取设备（监控模块）厂家信息1-6 (自定义)~CID1=42H>>    ${g_ver_1104}
    ${web数据}    通过1104_data获取web数据    ${1104数据}
    ${对比结果}    批量对比数据_1104_WEB    ${1104数据}    ${web数据}    1104_厂家信息_3
    should be true    ${对比结果}

1104_0008_V_3.0_1104_通用命令_厂家信息1-4_4
    [Tags]    3
    ${1104数据}    通用命令_获取厂家信息    ${g_prtcl_1104}    <<获取设备（监控模块）厂家信息1-6 (自定义)~CID1=80H>>    ${g_ver_1104}
    ${web数据}    通过1104_data获取web数据    ${1104数据}
    ${对比结果}    批量对比数据_1104_WEB    ${1104数据}    ${web数据}    1104_厂家信息_4
    should be true    ${对比结果}

1104_0010_V_3.0_1104_特定厂家信息
    [Tags]    1
    ${协议数据}    1104获取数据    ${g_prtcl_1104}    <<获取设备特定厂家信息 ~CID1=42H>>    <<获取设备特定厂家信息 ~CID2=81H>>    None    ${g_ver_1104}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${对比结果}    批量对比数据_1104_WEB    ${协议数据}    ${web数据}    1104_设备特定厂家信息
    should be true    ${对比结果}
