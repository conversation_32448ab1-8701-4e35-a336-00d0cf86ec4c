*** Settings ***
Suite Setup       Run Keywords    测试用例关键字.测试用例前置条件
...               AND     电池管理初始化
Suite Teardown    测试用例关键字.测试用例后置条件
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
snmp_0002_batteryGroup1analogData√
    ${比较结果}    对比数据_V2C    dCDistribution1analogData
    should be true    ${比较结果}
    Comment    ${比较结果}    对比数据_V3    cSU1analogData
    Comment    should be true    ${比较结果}

snmp_0004_batteryGroup1digitalData√
    ${比较结果}    对比数据_V2C    batteryGroup1digitalData
    should be true    ${比较结果}

snmp_0006_batteryGroup1alarmX
    [Documentation]    电池均充、电池高温下电、电池低温下电
    ...
    ...
    ...
    ...    使能需要先打开
    [Tags]    T1-1
    [Setup]    run keywords    Wait Until Keyword Succeeds    30    1    设置web参数量    电池高温下电使能    允许
    ...    AND    设置web参数量    电池低温下电使能    允许
    Comment    ${比较结果}    对比告警_V2C    batteryGroup1alarm
    Comment    should be true    ${比较结果}
    Log    此项在trap中测
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置web设备参数量为默认值    电池高温下电使能    电池低温下电使能
    ...    AND    关闭负载输出

snmp_0008_batteryGroup1alarm_level√
    ${比较结果}    对比告警级别_V2C    batteryGroup1alarm
    should be true    ${比较结果}

snmp_0010_batteryGroup1alarm_level_write√
    Comment    ${比较结果}    批量修改告警级别_V2C    batteryGroup1alarm
    Comment    should be true    ${比较结果}
    ${比较结果}    批量设置四种/五种告警级别    batteryGroup1alarm
    should be true    ${比较结果}

snmp_0012_batteryGroup1alarm_relay√
    ${比较结果}    对比告警干接点_V2C    batteryGroup1alarm
    should be true    ${比较结果}

snmp_0014_batteryGroup1alarm_relay_write√
    ${比较结果}    批量修改告警干接点_V2C    batteryGroup1alarm
    should be true    ${比较结果}

snmp_0016_batteryGroup1parameterX
    [Setup]    Run Keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    电池周期测试使能    允许
    ...        AND    batteryGroup参数获取/设置前置条件
    ${比较结果}    对比数据_V2C    batteryGroup1parameter
    should be true    ${比较结果}
    [Teardown]    batteryGroup参数获取/设置结束条件

snmp_0018_batteryGroup1parameter_writeX
    [Setup]    Run Keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    电池周期测试使能    允许
    ...        AND    batteryGroup参数获取/设置前置条件
    Comment    ${比较结果}    批量修改参数_V2C    batteryGroup1parameter
    Comment    should be true    ${比较结果}
    ${比较结果}    SNMP批量参数设置    batteryGroup1parameter
    should be true    ${比较结果}
    [Teardown]    batteryGroup参数获取/设置结束条件

snmp_0020_batteryGroup1control_startFloatCharge
    [Setup]    run keywords    连接CSU
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    纯铅酸
    ...    AND    sleep    1m
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${设置控制量}    设置SNMP控制量    startFloatChargevalue
    sleep    10
    ${存在结果}    判断web历史记录存在snmp控制内容    电池组    启动浮充    60
    should be true    ${存在结果}
    断开连接SNMP

snmp_0022_batteryGroup1control_startEqualizedCharge
    [Setup]    设置web参数量    均充使能    允许
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${设置控制量}    设置SNMP控制量    startEqualizedChargevalue
    sleep    10
    ${存在结果}    判断web历史记录存在snmp控制内容    电池组    启动均充    60
    should be true    ${存在结果}
    断开连接SNMP

snmp_0024_batteryGroup1control_startTest
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${设置控制量}    设置SNMP控制量    startTestvalue
    sleep    10
    ${存在结果}    判断web历史记录存在snmp控制内容    电池组    启动测试    60
    should be true    ${存在结果}
    断开连接SNMP

snmp_0026_batteryGroup1control_startBatteryDetect
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${设置控制量}    设置SNMP控制量    startBatteryDetectvalue
    sleep    10
    ${存在结果}    判断web历史记录存在snmp控制内容    电池组    启动电池检测    60
    should be true    ${存在结果}
    断开连接SNMP

# snmp_0028_batteryGroup1control_startCharge
#     [Setup]    FB100B3铁锂电池测试前置条件
#     进行SNMP_V2/V3连接    ${snmp连接方式}
#     ${设置控制量}    设置SNMP控制量    startChargevalue
#     sleep    10
#     ${存在结果}    判断web历史记录存在snmp控制内容    电池组    启动充电    60
#     should be true    ${存在结果}
#     断开连接SNMP

# snmp_0030_batteryGroup1control_bMSDeviceStatistic
#     [Setup]
#     进行SNMP_V2/V3连接    ${snmp连接方式}
#     ${设置控制量}    设置SNMP控制量    bMSDeviceStatisticvalue
#     sleep    10
#     ${存在结果}    判断web历史记录存在snmp控制内容    电池组    BMS设备统计    60
#     should be true    ${存在结果}
#     断开连接SNMP
#     [Teardown]    FB100B3铁锂电池测试结束条件
