*** Settings ***
Suite Setup       #Run keywords    测试用例前置条件    # AND    仅有市电条件上电
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
PU关机状态测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具值    PU    all    数字量    PU关机状态    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU关机状态-1    是
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU关机状态-10    是
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU关机状态-20    是
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU关机状态-30    是
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU关机状态-40    是
    设置子工具值    PU    all    数字量    PU关机状态    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU关机状态-1    否
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU关机状态-10    否
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU关机状态-20    否
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU关机状态-30    否
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU关机状态-40    否

PU一键功能状态测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具值    PU    all    数字量    PU一键功能状态    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU一键功能状态-1    是
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU一键功能状态-10    是
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU一键功能状态-20    是
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU一键功能状态-30    是
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU一键功能状态-40    是
    设置子工具值    PU    all    数字量    PU一键功能状态    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU一键功能状态-1    否
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU一键功能状态-10    否
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU一键功能状态-20    否
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU一键功能状态-30    否
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU一键功能状态-40    否

PU MPPT状态测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具值    PU    all    数字量    PU MPPT状态    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU MPPT状态-1    是
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU MPPT状态-10    是
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU MPPT状态-20    是
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU MPPT状态-30    是
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU MPPT状态-40    是
    设置子工具值    PU    all    数字量    PU MPPT状态    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU MPPT状态-1    否
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU MPPT状态-10    否
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU MPPT状态-20    否
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU MPPT状态-30    否
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU MPPT状态-40    否

PU输入无效状态测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具值    PU    all    数字量    PU输入无效状态    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入无效状态-1    是
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入无效状态-10    是
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入无效状态-20    是
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入无效状态-30    是
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入无效状态-40    是
    设置子工具值    PU    all    数字量    PU输入无效状态    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入无效状态-1    否
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入无效状态-10    否
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入无效状态-20    否
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入无效状态-30    否
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入无效状态-40    否

PU输出限流状态测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具值    PU    all    数字量    PU输出限流状态    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出限流状态-1    是
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出限流状态-10    是
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出限流状态-20    是
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出限流状态-30    是
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出限流状态-40    是
    设置子工具值    PU    all    数字量    PU输出限流状态    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出限流状态-1    否
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出限流状态-10    否
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出限流状态-20    否
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出限流状态-30    否
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出限流状态-40    否

PU休眠状态测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具值    PU    all    数字量    PU休眠状态    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU休眠状态-1    是
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU休眠状态-10    是
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU休眠状态-20    是
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU休眠状态-30    是
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU休眠状态-40    是
    设置子工具值    PU    all    数字量    PU休眠状态    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU休眠状态-1    否
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU休眠状态-10    否
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU休眠状态-20    否
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU休眠状态-30    否
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU休眠状态-40    否

PU风扇控制状态测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具值    PU    all    数字量    PU风扇控制状态    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU风扇控制状态-1    全速
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU风扇控制状态-10    全速
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU风扇控制状态-20    全速
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU风扇控制状态-30    全速
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU风扇控制状态-40    全速
    设置子工具值    PU    all    数字量    PU风扇控制状态    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU风扇控制状态-1    自动
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU风扇控制状态-10    自动
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU风扇控制状态-20    自动
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU风扇控制状态-30    自动
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU风扇控制状态-40    自动

PU升级使能状态测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具值    PU    all    数字量    PU升级使能状态    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU升级使能状态-1    允许
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU升级使能状态-10    允许
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU升级使能状态-20    允许
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU升级使能状态-30    允许
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU升级使能状态-40    允许
    设置子工具值    PU    all    数字量    PU升级使能状态    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU升级使能状态-1    禁止
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU升级使能状态-10    禁止
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU升级使能状态-20    禁止
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU升级使能状态-30    禁止
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU升级使能状态-40    禁止

PU输入限流状态测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具值    PU    all    数字量    PU输入限流    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入限流状态-1    是
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入限流状态-10    是
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入限流状态-20    是
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入限流状态-30    是
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入限流状态-40    是
    设置子工具值    PU    all    数字量    PU输入限流    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入限流状态-1    否
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入限流状态-10    否
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入限流状态-20    否
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入限流状态-30    否
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入限流状态-40    否

PU无输入状态测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具值    PU    all    数字量    PU无输入告警    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU无输入状态-1    是
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU无输入状态-10    是
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU无输入状态-20    是
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU无输入状态-30    是
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU无输入状态-40    是
    设置子工具值    PU    all    数字量    PU无输入告警    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU无输入状态-1    否
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU无输入状态-10    否
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU无输入状态-20    否
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU无输入状态-30    否
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU无输入状态-40    否
