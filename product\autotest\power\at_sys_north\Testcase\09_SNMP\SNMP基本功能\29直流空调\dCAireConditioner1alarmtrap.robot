*** Settings ***
Default Tags      trap
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
批量获取直流空调告警量
    写入CSV文档    直流空调数字量和告警量获取测试    信号名称    信号值    结果
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=直流空调
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${直流空调排除告警量信号}    ${排除列表}    2
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    dCAirConditioner    alarm    True
    ${信号名称列表1}    Create List
    FOR    ${i}    IN    @{snmp待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    signal_node
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
        Set To Dictionary    ${dict}     snmp_name     ${节点名称}
        Set To Dictionary    ${dict}     Vindex     ${信号序号}
        Set To Dictionary    ${dict}     device_name     直流空调
        Append To List       ${信号名称列表1}    ${dict}
    END
    Run Keyword And Continue On Failure    南向RS485子设备告警量列表产生封装判断结果    snmp    DMU_DCAirCondition    ${信号名称列表1}    
    ...    告警    ${告警产生}    直流空调数字量和告警量获取测试    环境    直流空调    
    ...    null    null    null    null    null    null
    Run Keyword And Continue On Failure    南向RS485子设备告警量列表恢复封装判断结果    snmp    DMU_DCAirCondition    ${信号名称列表1}    
    ...    告警    ${告警恢复}    直流空调数字量和告警量获取测试    环境    直流空调
    ...    null    null    null    null    null    null
    断开连接SNMP

直流空调通讯断告警
    连接CSU
    ${级别设置值}    获取web参数量    <<直流空调通讯断~0x25001030010001>>
    run keyword if    '${级别设置值}'=='屏蔽'    wait until keyword succeeds    10    2    设置web参数量    <<直流空调通讯断~0x25001030010001>>    严重
    #子设备工具模拟
    ${告警级别取值约定dict}    获取web参数的取值约定    <<直流空调通讯断~0x25001030010001>>
    ${级别设置值}    获取web参数量    <<直流空调通讯断~0x25001030010001>>
    进行SNMP_V2/V3连接    ${snmp连接方式}
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    FOR    ${直流空调序号}    IN RANGE    ${直流空调最大数}    1    -1
        ${空调个数}    Evaluate    ${直流空调序号}-1
        ${空调个数}    Convert To String    ${空调个数}
        设置子工具个数    DMU_DCAirCondition    ${空调个数}
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    直流空调工作状态-${直流空调序号}    通讯断
        wait until keyword succeeds    5m    2    查询指定告警信息    直流空调通讯断-${直流空调序号}
        ${snmp获取值1}    获取SNMP数据_单个    dCAirConditionerWorkStatus${直流空调序号}value
        should be equal as strings    ${snmp获取值1}    3
        ${snmp获取值1}    获取SNMP数据_单个    dCAirConditionerCommunicationStatus${直流空调序号}value
        should be equal as strings    ${snmp获取值1}    1
        ${snmp英文名}    获取snmp单个告警英文名    直流空调通讯断_${直流空调序号}
        ${告警产生}    判断Trap告警产生    ${snmp英文名}
        should be true    ${告警产生}
    END
    FOR    ${直流空调序号}    IN RANGE    2    ${直流空调最大数}+1
        ${空调个数}    Convert To String    ${直流空调序号}
        设置子工具个数    DMU_DCAirCondition    ${空调个数}
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    直流空调工作状态-${直流空调序号}    正常
        wait until keyword succeeds    5m    1    判断告警不存在    直流空调通讯断-${直流空调序号}
        ${snmp获取值1}    获取SNMP数据_单个    dCAirConditionerWorkStatus${直流空调序号}value
        should be equal as strings    ${snmp获取值1}    1
        ${snmp获取值1}    获取SNMP数据_单个    dCAirConditionerCommunicationStatus${直流空调序号}value
        should be equal as strings    ${snmp获取值1}    0
        ${snmp英文名}    获取snmp单个告警英文名    直流空调通讯断_${直流空调序号}
        ${告警消失}    判断Trap告警消失    ${snmp英文名}
        should be true    ${告警消失}
    END
    wait until keyword succeeds    5m    2    查询指定告警信息不为    直流空调通讯断
    [Teardown]    Run Keywords    设置子工具个数    DMU_DCAirCondition    ${直流空调最大数}
    ...    AND    sleep    3min