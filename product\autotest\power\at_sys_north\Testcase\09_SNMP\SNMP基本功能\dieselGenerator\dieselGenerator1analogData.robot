*** Settings ***
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp_test关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/Web关键字_V30.robot
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
# snmp批量获取油机模拟量
#     写入CSV文档    油机模拟量获取测试    信号名称    信号值    结果    备注
#     连接CSU
#     进行SNMP_V2/V3连接    ${snmp连接方式}
#     ${油机油压取值范围}    create list    66    5600    1800
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=油机
#     ${排除列表}    create list
#     Append To List    ${油机排除模拟量信号}    油机油压~0x190010100f0001
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${油机排除模拟量信号}    ${排除列表}
#     ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    dieselGenerator    analogData
#     Comment    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    FBBMS模块    analog data    ${PU排除模拟量信号}
#     Comment    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1
#     FOR    ${i}    IN    @{snmp待测}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${节点名称}    Get From Dictionary    ${i}    snmp_name
#         ${信号序号}    Get From Dictionary    ${i}    Vindex
#         Comment    Run Keyword if    '${信号名称}'=='<<油机油压-1~0x190010100f0001>>'    ${缺省值}[1]    evaluate    ${油机油压取值范围}[1]    else    ${缺省值}    获取web参数上下限范围    ${信号名称}
#         ${缺省值}    获取web参数上下限范围    ${信号名称}
#         Run Keyword And Continue On Failure    南向子设备模拟量获取值封装判断结果    snmp    oileng    ${信号名称}    只读    ${缺省值}[1]    油机模拟量获取测试    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    南向子设备模拟量获取值封装判断结果    snmp    oileng    ${信号名称}    只读    ${缺省值}[2]    油机模拟量获取测试    null    ${节点名称}    ${信号序号}
#         Run keyword IF    ${缺省值}[1]!=${缺省值}[0] and ${缺省值}[2]!=${缺省值}[0]    Run Keyword And Continue On Failure    南向子设备模拟量获取值封装判断结果    snmp    oileng    ${信号名称}    只读    ${缺省值}[0]    油机模拟量获取测试    null    ${节点名称}    ${信号序号}
#     END
#     断开连接SNMP

# snmp批量获取市电模拟量
#     [Documentation]    9min
#     写入CSV文档    市电模拟量获取测试    信号名称    信号值    结果    备注
#     连接CSU
#     进行SNMP_V2/V3连接    ${snmp连接方式}
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=市电
#     ${排除列表}    create list
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${市电排除模拟量信号}    ${排除列表}
#     ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    mains    analogData
#     Comment    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    FBBMS模块    analog data    ${PU排除模拟量信号}
#     Comment    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
#     Comment    FOR    ${i}    IN    ${snmp待测}[0]
#     FOR    ${i}    IN    @{snmp待测}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${节点名称}    Get From Dictionary    ${i}    snmp_name
#         ${信号序号}    Get From Dictionary    ${i}    Vindex
#         ${缺省值}    获取web参数上下限范围    ${信号名称}
#         Run Keyword And Continue On Failure    南向子设备模拟量获取值封装判断结果    snmp    oileng    ${信号名称}    只读    ${缺省值}[1]    市电模拟量获取测试    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    南向子设备模拟量获取值封装判断结果    snmp    oileng    ${信号名称}    只读    ${缺省值}[2]    市电模拟量获取测试    null    ${节点名称}    ${信号序号}
#         Run keyword IF    ${缺省值}[1]!=${缺省值}[0] and ${缺省值}[2]!=${缺省值}[0]    Run Keyword And Continue On Failure    南向子设备模拟量获取值封装判断结果    snmp    oileng    ${信号名称}    只读    ${缺省值}[0]    市电模拟量获取测试    null    ${节点名称}    ${信号序号}
#     END
#     断开连接SNMP

snmp批量获取油机模拟量
    写入CSV文档    油机模拟量获取测试    信号名称    信号值    结果    备注
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${油机油压取值范围}    create list    66    5600    1800
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=油机
    ${排除列表}    create list
    Append To List    ${油机排除模拟量信号}    油机油压~0x190010100f0001
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${油机排除模拟量信号}    ${排除列表}
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    dieselGenerator    analogData
    Comment    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    FBBMS模块    analog data    ${PU排除模拟量信号}
    Comment    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    1    snmp
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    snmp    oileng    只读    ${缺省值列表}    油机模拟量获取测试    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    2    snmp
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    snmp    oileng    只读    ${缺省值列表}    油机模拟量获取测试    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    0    snmp
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    snmp    oileng    只读    ${缺省值列表}    油机模拟量获取测试    null
    断开连接SNMP

snmp批量获取市电模拟量
    [Documentation]    9min
    写入CSV文档    市电模拟量获取测试    信号名称    信号值    结果    备注
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=市电
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${市电排除模拟量信号}    ${排除列表}
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    mains    analogData
    Comment    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    FBBMS模块    analog data    ${PU排除模拟量信号}
    Comment    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    Comment    FOR    ${i}    IN    ${snmp待测}[0]
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    1    snmp
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    snmp    oileng    只读    ${缺省值列表}    市电模拟量获取测试    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    2    snmp
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    snmp    oileng    只读    ${缺省值列表}    市电模拟量获取测试    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    0    snmp
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    snmp    oileng    只读    ${缺省值列表}    市电模拟量获取测试    null
    断开连接SNMP

snmp_0012_油机视在功率√
    [Documentation]    def,min,max:50,40,75
    连接CSU
    设置子工具值    oileng    all    只读    发电 A 相视在功率    1001
    设置子工具值    oileng    all    只读    发电 B 相视在功率    2002
    设置子工具值    oileng    all    只读    发电 C 相视在功率    3003
    sleep    1m
    ${获取值1}    获取web实时数据    油机视在功率_1
    ${获取值2}    获取web实时数据    油机视在功率_2
    ${获取值3}    获取web实时数据    油机视在功率_3
    #
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${snmp获取值1}    获取SNMP数据_单个    dieselGeneratorDGApparentPower1value    序号=1
    ${snmp获取值2}    获取SNMP数据_单个    dieselGeneratorDGApparentPower1value    序号=2
    ${snmp获取值3}    获取SNMP数据_单个    dieselGeneratorDGApparentPower1value    序号=3
    should be true    ${获取值1}==3003
    should be true    ${获取值2}==2002
    should be true    ${获取值3}==1001
    should be true    ${snmp获取值1}==3003
    should be true    ${snmp获取值2}==2002
    should be true    ${snmp获取值3}==1001
    设置子工具值    oileng    all    只读    发电 A 相视在功率    3010
    设置子工具值    oileng    all    只读    发电 B 相视在功率    3120
    设置子工具值    oileng    all    只读    发电 C 相视在功率    3478
    sleep    1m
    ${获取值1}    获取web实时数据    油机视在功率_1
    ${获取值2}    获取web实时数据    油机视在功率_2
    ${获取值3}    获取web实时数据    油机视在功率_3
    ${snmp获取值1}    获取SNMP数据_单个    dieselGeneratorDGApparentPower1value    序号=1
    ${snmp获取值2}    获取SNMP数据_单个    dieselGeneratorDGApparentPower1value    序号=2
    ${snmp获取值3}    获取SNMP数据_单个    dieselGeneratorDGApparentPower1value    序号=3
    should be true    ${获取值1}==3478
    should be true    ${获取值2}==3120
    should be true    ${获取值3}==3010
    should be true    ${snmp获取值1}==3478
    should be true    ${snmp获取值2}==3120
    should be true    ${snmp获取值3}==3010
    设置子工具值    oileng    all    只读    发电 A 相视在功率    4300
    设置子工具值    oileng    all    只读    发电 B 相视在功率    4334
    设置子工具值    oileng    all    只读    发电 C 相视在功率    4367
    sleep    1m
    ${获取值1}    获取web实时数据    油机视在功率_1
    ${获取值2}    获取web实时数据    油机视在功率_2
    ${获取值3}    获取web实时数据    油机视在功率_3
    ${snmp获取值1}    获取SNMP数据_单个    dieselGeneratorDGApparentPower1value    序号=1
    ${snmp获取值2}    获取SNMP数据_单个    dieselGeneratorDGApparentPower1value    序号=2
    ${snmp获取值3}    获取SNMP数据_单个    dieselGeneratorDGApparentPower1value    序号=3
    #
    断开连接SNMP
    should be true    ${获取值1}==4367
    should be true    ${获取值2}==4334
    should be true    ${获取值3}==4300
    should be true    ${snmp获取值1}==4367
    should be true    ${snmp获取值2}==4334
    should be true    ${snmp获取值3}==4300

snmp_0014_油机有功功率√
    [Documentation]    def,min,max:50,40,75
    连接CSU
    设置子工具值    oileng    all    只读    发电 A 相有功功率    4000
    设置子工具值    oileng    all    只读    发电 B 相有功功率    5000
    设置子工具值    oileng    all    只读    发电 C 相有功功率    6000
    sleep    1m
    ${获取值1}    获取web实时数据    油机有功功率_1
    ${获取值2}    获取web实时数据    油机有功功率_2
    ${获取值3}    获取web实时数据    油机有功功率_3
    #
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${snmp获取值1}    获取SNMP数据_单个    dieselGeneratorDGActivePower1value    序号=1
    ${snmp获取值2}    获取SNMP数据_单个    dieselGeneratorDGActivePower1value    序号=2
    ${snmp获取值3}    获取SNMP数据_单个    dieselGeneratorDGActivePower1value    序号=3
    should be true    ${获取值1}==6000
    should be true    ${获取值2}==5000
    should be true    ${获取值3}==4000
    should be true    ${snmp获取值1}==6000
    should be true    ${snmp获取值2}==5000
    should be true    ${snmp获取值3}==4000
    设置子工具值    oileng    all    只读    发电 A 相有功功率    2000
    设置子工具值    oileng    all    只读    发电 B 相有功功率    3000
    设置子工具值    oileng    all    只读    发电 C 相有功功率    3568
    sleep    1m
    ${获取值1}    获取web实时数据    油机有功功率_1
    ${获取值2}    获取web实时数据    油机有功功率_2
    ${获取值3}    获取web实时数据    油机有功功率_3
    ${snmp获取值1}    获取SNMP数据_单个    dieselGeneratorDGActivePower1value    序号=1
    ${snmp获取值2}    获取SNMP数据_单个    dieselGeneratorDGActivePower1value    序号=2
    ${snmp获取值3}    获取SNMP数据_单个    dieselGeneratorDGActivePower1value    序号=3
    should be true    ${获取值1}==3568
    should be true    ${获取值2}==3000
    should be true    ${获取值3}==2000
    should be true    ${snmp获取值1}==3568
    should be true    ${snmp获取值2}==3000
    should be true    ${snmp获取值3}==2000
    设置子工具值    oileng    all    只读    发电 A 相有功功率    2200
    设置子工具值    oileng    all    只读    发电 B 相有功功率    2234
    设置子工具值    oileng    all    只读    发电 C 相有功功率    2267
    sleep    1m
    ${获取值1}    获取web实时数据    油机有功功率_1
    ${获取值2}    获取web实时数据    油机有功功率_2
    ${获取值3}    获取web实时数据    油机有功功率_3
    ${snmp获取值1}    获取SNMP数据_单个    dieselGeneratorDGActivePower1value    序号=1
    ${snmp获取值2}    获取SNMP数据_单个    dieselGeneratorDGActivePower1value    序号=2
    ${snmp获取值3}    获取SNMP数据_单个    dieselGeneratorDGActivePower1value    序号=3
    #
    断开连接SNMP
    should be true    ${获取值1}==2267
    should be true    ${获取值2}==2234
    should be true    ${获取值3}==2200
    should be true    ${snmp获取值1}==2267
    should be true    ${snmp获取值2}==2234
    should be true    ${snmp获取值3}==2200

snmp_0016_油机无功功率√
    [Documentation]    def,min,max:50,40,75
    连接CSU
    设置子工具值    oileng    all    只读    发电 A 相无功功率    98
    设置子工具值    oileng    all    只读    发电 B 相无功功率    88
    设置子工具值    oileng    all    只读    发电 C 相无功功率    68
    sleep    1m
    ${获取值1}    获取web实时数据    油机无功功率_1
    ${获取值2}    获取web实时数据    油机无功功率_2
    ${获取值3}    获取web实时数据    油机无功功率_3
    #
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${snmp获取值1}    获取SNMP数据_单个    dieselGeneratorDGReactivePower1value    序号=1
    ${snmp获取值2}    获取SNMP数据_单个    dieselGeneratorDGReactivePower1value    序号=2
    ${snmp获取值3}    获取SNMP数据_单个    dieselGeneratorDGReactivePower1value    序号=3
    should be true    ${获取值1}==68
    should be true    ${获取值2}==88
    should be true    ${获取值3}==98
    should be true    ${snmp获取值1}==68
    should be true    ${snmp获取值2}==88
    should be true    ${snmp获取值3}==98
    设置子工具值    oileng    all    只读    发电 A 相无功功率    1000
    设置子工具值    oileng    all    只读    发电 B 相无功功率    1100
    设置子工具值    oileng    all    只读    发电 C 相无功功率    1468
    sleep    1m
    ${获取值1}    获取web实时数据    油机无功功率_1
    ${获取值2}    获取web实时数据    油机无功功率_2
    ${获取值3}    获取web实时数据    油机无功功率_3
    ${snmp获取值1}    获取SNMP数据_单个    dieselGeneratorDGReactivePower1value    序号=1
    ${snmp获取值2}    获取SNMP数据_单个    dieselGeneratorDGReactivePower1value    序号=2
    ${snmp获取值3}    获取SNMP数据_单个    dieselGeneratorDGReactivePower1value    序号=3
    should be true    ${获取值1}==1468
    should be true    ${获取值2}==1100
    should be true    ${获取值3}==1000
    should be true    ${snmp获取值1}==1468
    should be true    ${snmp获取值2}==1100
    should be true    ${snmp获取值3}==1000
    设置子工具值    oileng    all    只读    发电 A 相无功功率    200
    设置子工具值    oileng    all    只读    发电 B 相无功功率    234
    设置子工具值    oileng    all    只读    发电 C 相无功功率    267
    sleep    1m
    ${获取值1}    获取web实时数据    油机无功功率_1
    ${获取值2}    获取web实时数据    油机无功功率_2
    ${获取值3}    获取web实时数据    油机无功功率_3
    ${snmp获取值1}    获取SNMP数据_单个    dieselGeneratorDGReactivePower1value    序号=1
    ${snmp获取值2}    获取SNMP数据_单个    dieselGeneratorDGReactivePower1value    序号=2
    ${snmp获取值3}    获取SNMP数据_单个    dieselGeneratorDGReactivePower1value    序号=3
    #
    断开连接SNMP
    should be true    ${获取值1}==267
    should be true    ${获取值2}==234
    should be true    ${获取值3}==200
    should be true    ${snmp获取值1}==267
    should be true    ${snmp获取值2}==234
    should be true    ${snmp获取值3}==200

snmp_0020_油机油压√
    [Documentation]    def,min,max:0,0,5000
    连接CSU
    设置子工具值    oileng    all    只读    油压    5600
    sleep    1m
    ${获取值1}    获取web实时数据    油机油压
    #
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${snmp获取值1}    获取SNMP数据_单个    dieselGeneratorDGOilPressure1value
    should be true    ${获取值1}==5600
    should be true    ${snmp获取值1}==5600
    设置子工具值    oileng    all    只读    油压    1800
    sleep    1m
    ${获取值1}    获取web实时数据    油机油压
    ${snmp获取值1}    获取SNMP数据_单个    dieselGeneratorDGOilPressure1value
    should be true    ${获取值1}==1800
    should be true    ${snmp获取值1}==1800
    设置子工具值    oileng    all    只读    油压    67
    sleep    1m
    ${获取值1}    获取web实时数据    油机油压
    ${snmp获取值1}    获取SNMP数据_单个    dieselGeneratorDGOilPressure1value
    断开连接SNMP
    should be true    ${获取值1}==67
    should be true    ${snmp获取值1}==67

snmp_0026_油机带载率_无
    [Documentation]    子工具无
    ...    暂时无法自动化
    [Tags]    3
    Log    子工具无，暂时无法自动化
    Log    后续若有需补充用例

snmp_0028_油机油温_无
    [Documentation]    子工具无
    ...    暂时无法自动化
    [Tags]    3
    Log    子工具无，暂时无法自动化
    Log    后续若有需补充用例

snmp_0030_油机持续运行时间_无
    [Documentation]    子工具无
    ...    暂时无法自动化
    [Tags]    3
    Log    子工具无，暂时无法自动化
    Log    后续若有需补充用例

snmp_0032_油机油位_无
    [Documentation]    子工具无
    ...    暂时无法自动化
    [Tags]    3
    Log    子工具无，暂时无法自动化
    Log    后续若有需补充用例

snmp_0034_油机剩余油量_无
    [Documentation]    子工具无
    ...    暂时无法自动化
    [Tags]    3
    Log    子工具无，暂时无法自动化
    Log    后续若有需补充用例

snmp_0036_油箱容量_无
    [Documentation]    子工具无
    ...    暂时无法自动化
    [Tags]    3
    Log    子工具无，暂时无法自动化
    Log    后续若有需补充用例
