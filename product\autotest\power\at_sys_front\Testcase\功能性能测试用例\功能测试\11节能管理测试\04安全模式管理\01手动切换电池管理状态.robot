*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
安全模式启动浮充
    
    电池管理初始化
    系统复位
    #进入节能模式
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池充电电流系数    0.6
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    浮充
    ${设置值}    获取web参数量    浮充电压
    Wait Until Keyword Succeeds    5m    2    直流电压及电池电压在指定范围内    ${设置值}
    # ${上限}    evaluate    ${设置值}+0.5
    # ${下限}    evaluate    ${设置值}-0.5
    # sleep    5m
    # ${获取数据}    获取web实时数据    直流电压
    # ${获取数据_bat}    获取web实时数据    电池电压-1
    # should be true    ${下限}<=${获取数据}<=${上限}
    # should be true    -0.5<${获取数据}-${获取数据_bat}<0.5
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    [Teardown]    Wait Until Keyword Succeeds    10    1    设置web参数量    电池充电电流系数    0.15

安全模式启动均充
    
    电池管理初始化
    系统复位
    #进入节能模式
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动均充
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    均充
    ${设置值}    获取web参数量    均充电压
    # ${上限}    evaluate    ${设置值}+0.5
    # ${下限}    evaluate    ${设置值}-0.5
    设置电池模拟器电压模式    铅酸电池    1    1    ${设置值}
    Wait Until Keyword Succeeds    5m    2    直流电压及电池电压在指定范围内    ${设置值}
    # sleep    5m
    # ${获取数据}    获取web实时数据    直流电压
    # ${获取数据_bat}    获取web实时数据    电池电压-1
    # should be true    ${下限}<=${获取数据}<=${上限}
    # should be true    -0.5<${获取数据}-${获取数据_bat}<0.5
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    浮充
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    浮充
    ${设置值}    获取web参数量    浮充电压
    设置电池模拟器电压模式    铅酸电池    1    1    ${设置值}
    sleep    5m
    [Teardown]    重置电池模拟器输出

安全模式启动测试
    
    电池管理初始化
    系统复位
    #进入节能模式
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    测试最长时间    2
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动测试
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    测试
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    4m    1    信号量数据值不为    电池管理状态    测试
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    工作整流器数量    3
    [Teardown]    设置web设备参数量为默认值    测试最长时间

安全模式启动电池检测
    
    电池管理初始化
    系统复位
    #进入节能模式
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    [Teardown]
