*** Settings ***
Suite Setup       Run Keywords    主动告警测试前置条件    ${CSU_role}
...               AND    测试用例前置条件
Suite Teardown    Run Keywords    设置web设备参数量为默认值    CSU主动告警使能
...               AND    测试用例后置条件
Default Tags      T1-1
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
1363_0006_1输入线/相电压L1交流电压低告警
    [Documentation]    获取1104一条告警值并判断告警存在
    ...    输入
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${理应值}= | ${屏号}=None | ${g_ver}=
    ...
    ...
    ...
    ...    0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning; 0xFF：无效值
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压低_1
    ${可设置范围}    获取web参数可设置范围    交流电压低阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压低阈值    ${可设置范围}[1]
    ${电压低设置值}    获取web参数量    交流电压低阈值
    ${判断点1}    evaluate    ${电压低设置值}-5
    分别设置各相电压频率    ${判断点1}    220    220    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压低_1    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压低
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流电压低_1    ${告警级别设置}
        Comment    ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置交流段告警级别~CID1=40H>>    <<设置交流段告警级别~CID2=82H>>    交流电压高    ${告警级别设置}    FF    ${g_ver_1363}
        Comment    should be true    ${设置结果}
        sleep    2
        wait until keyword succeeds    1m    1    查询指定告警信息    交流电压低
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流段告警量~CID1=40H>>    <<获取交流段告警量~CID2=44H>>    输入线/相电压L1 L2/ L1    1    FF    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${交流电压低告警级别}    获取web告警属性    交流电压低_1    告警级别
        should be equal    ${告警级别设置}    ${交流电压低告警级别}
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流告警级别~CID2=81H>>    交流电压低    FF    ${g_ver_1363}
        ${1104告警级别}    获取1104告警级别取值约定    交流电压低_1    ${1104告警级别}
        should be equal    ${告警级别设置}    ${1104告警级别}
        ${web实时告警名}    由子工具获取web实时告警名称    交流电压低_1
        Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    1363    系统交流输入    ${web实时告警名}
    END
    设置web参数量    交流电压低_1    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    #无告警后查询判断
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流电压低_1
    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流段告警量~CID2=44H>>    输入线/相电压L1 L2/ L1    1    FF    ${g_ver_1363}
    should not be true    ${1104告警结果}
    [Teardown]    同时设置三相电压频率    220    50

1363_0008_1输入线/相电压L1交流电压高告警
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压高_1
    ${可设置范围}    获取web参数可设置范围    交流电压高阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高阈值    ${可设置范围}[0]
    ${电压高设置值}    获取web参数量    交流电压高阈值
    ${判断点1}    evaluate    ${电压高设置值}+5
    分别设置各相电压频率    ${判断点1}    220    220    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高_1    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    交流电压高
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流电压高_1    ${告警级别设置}
        sleep    2
        wait until keyword succeeds    1m    1    查询指定告警信息    交流电压高
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流段告警量~CID1=40H>>    <<获取交流段告警量~CID2=44H>>    输入线/相电压L1 L2/ L1    2    FF    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${交流电压低告警级别}    获取web告警属性    交流电压高_1    告警级别
        should be equal    ${告警级别设置}    ${交流电压低告警级别}
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流告警级别~CID2=81H>>    交流电压高    FF    ${g_ver_1363}
        ${1104告警级别}    获取1104告警级别取值约定    交流电压高_1    ${1104告警级别}
        should be equal    ${告警级别设置}    ${1104告警级别}
        ${web实时告警名}    由子工具获取web实时告警名称    交流电压高_1
        Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    1363    系统交流输入    ${web实时告警名}
    END
    设置web参数量    交流电压高_1    主要
    #无告警后查询判断
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流电压高_1
    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流告警级别~CID2=81H>>    输入线/相电压L1 L2/ L1    2    FF    ${g_ver_1363}
    should not be true    ${1104告警结果}
    [Teardown]    同时设置三相电压频率    220    50

1363_0010_1输入线/相电压L1缺相告警
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流缺相_1
    分别设置各相电压频率    0    220    220    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流缺相_1    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    2m    1    查询指定告警信息不为    交流缺相
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流缺相_1    ${告警级别设置}
        sleep    2
        wait until keyword succeeds    1m    1    查询指定告警信息    交流缺相
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流段告警量~CID1=40H>>    <<获取交流段告警量~CID2=44H>>    输入线/相电压L1 L2/ L1    3    FF    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${交流缺相告警级别}    获取web告警属性    交流缺相_1    告警级别
        should be equal    '${告警级别设置}'    '${交流缺相告警级别}'
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流告警级别~CID2=81H>>    交流缺相    FF    ${g_ver_1363}
        ${1104告警级别}    获取1104告警级别取值约定    交流缺相_1    ${1104告警级别}
        should be equal    ${告警级别设置}    ${1104告警级别}
        ${web实时告警名}    由子工具获取web实时告警名称    交流缺相_1
        Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    1363    系统交流输入    ${web实时告警名}
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    交流缺相_1    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    #无告警后查询判断
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流缺相_1
    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流告警级别~CID2=81H>>    输入线/相电压L1 L2/ L1    3    FF    ${g_ver_1363}
    should not be true    ${1104告警结果}
    [Teardown]    同时设置三相电压频率    220    50

1363_0012_2输入线/相电压L2交流电压低告警
    [Documentation]    获取1104一条告警值并判断告警存在
    ...    输入
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${理应值}= | ${屏号}=None | ${g_ver}=
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压低_2
    ${可设置范围}    获取web参数可设置范围    交流电压低阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压低阈值    ${可设置范围}[1]
    ${电压低设置值}    获取web参数量    交流电压低阈值
    ${判断点1}    evaluate    ${电压低设置值}-5
    分别设置各相电压频率    220    ${判断点1}    220    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压低_2    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压低_2
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流电压低_2    ${告警级别设置}
        sleep    2
        wait until keyword succeeds    1m    1    查询指定告警信息    交流电压低
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流段告警量~CID1=40H>>    <<获取交流段告警量~CID2=44H>>    输入线/相电压L2 L3/ L2    1    FF    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${交流电压低告警级别}    获取web告警属性    交流电压低_2    告警级别
        should be equal    ${告警级别设置}    ${交流电压低告警级别}
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流告警级别~CID2=81H>>    交流电压低    FF    ${g_ver_1363}
        ${1104告警级别}    获取1104告警级别取值约定    交流电压低_2    ${1104告警级别}
        should be equal    ${告警级别设置}    ${1104告警级别}
        ${web实时告警名}    由子工具获取web实时告警名称    交流电压低_2
        Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    1363    系统交流输入    ${web实时告警名}
    END
    设置web参数量    交流电压低_2    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    #无告警后查询判断
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流电压低_2
    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流告警级别~CID2=81H>>    输入线/相电压L2 L3/ L2    1    FF    ${g_ver_1363}
    should not be true    ${1104告警结果}
    [Teardown]    同时设置三相电压频率    220    50

1363_0014_2输入线/相电压L2交流电压高告警
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压高_2
    ${可设置范围}    获取web参数可设置范围    交流电压高阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高阈值    ${可设置范围}[0]
    ${电压高设置值}    获取web参数量    交流电压高阈值
    ${判断点1}    evaluate    ${电压高设置值}+5
    分别设置各相电压频率    220    ${判断点1}    220    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高_2    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    交流电压高_2
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流电压高_2    ${告警级别设置}
        sleep    2
        wait until keyword succeeds    1m    1    查询指定告警信息    交流电压高
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流段告警量~CID1=40H>>    <<获取交流段告警量~CID2=44H>>    输入线/相电压L2 L3/ L2    2    FF    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${交流电压低告警级别}    获取web告警属性    交流电压高_2    告警级别
        should be equal    ${告警级别设置}    ${交流电压低告警级别}
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流告警级别~CID2=81H>>    交流电压高    FF    ${g_ver_1363}
        ${1104告警级别}    获取1104告警级别取值约定    交流电压高_2    ${1104告警级别}
        should be equal    ${告警级别设置}    ${1104告警级别}
        ${web实时告警名}    由子工具获取web实时告警名称    交流电压高_2
        Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    1363    系统交流输入    ${web实时告警名}
    END
    设置web参数量    交流电压高_2    主要
    #无告警后查询判断
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流电压高_2
    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流告警级别~CID2=81H>>    输入线/相电压L2 L3/ L2    2    FF    ${g_ver_1363}
    should not be true    ${1104告警结果}
    [Teardown]    同时设置三相电压频率    220    50

1363_0016_2输入线/相电压L2缺相告警
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流缺相_2
    分别设置各相电压频率    220    0    220    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流缺相_2    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    2m    1    查询指定告警信息不为    交流缺相
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流缺相_2    ${告警级别设置}
        sleep    2
        wait until keyword succeeds    1m    1    查询指定告警信息    交流缺相
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流段告警量~CID1=40H>>    <<获取交流段告警量~CID2=44H>>    输入线/相电压L2 L3/ L2    3    FF    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${交流缺相告警级别}    获取web告警属性    交流缺相_2    告警级别
        should be equal    '${告警级别设置}'    '${交流缺相告警级别}'
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流告警级别~CID2=81H>>    交流缺相    FF    ${g_ver_1363}
        ${1104告警级别}    获取1104告警级别取值约定    交流缺相_2    ${1104告警级别}
        should be equal    ${告警级别设置}    ${1104告警级别}
        ${web实时告警名}    由子工具获取web实时告警名称    交流缺相_2
        Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    1363    系统交流输入    ${web实时告警名}
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    交流缺相_2    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    #无告警后查询判断
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流缺相_2
    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流告警级别~CID2=81H>>    输入线/相电压L2 L3/ L2    3    FF    ${g_ver_1363}
    should not be true    ${1104告警结果}
    [Teardown]    同时设置三相电压频率    220    50

1363_0018_3输入线/相电压L3交流电压低告警
    [Documentation]    获取1104一条告警值并判断告警存在
    ...    输入
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${理应值}= | ${屏号}=None | ${g_ver}=
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压低_3
    ${可设置范围}    获取web参数可设置范围    交流电压低阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压低阈值    ${可设置范围}[1]
    ${电压低设置值}    获取web参数量    交流电压低阈值
    ${判断点1}    evaluate    ${电压低设置值}-5
    分别设置各相电压频率    220    220    ${判断点1}    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压低_3    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压低_3
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流电压低_3    ${告警级别设置}
        sleep    2
        wait until keyword succeeds    1m    1    查询指定告警信息    交流电压低
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流段告警量~CID1=40H>>    <<获取交流段告警量~CID2=44H>>    输入线/相电压L3 L1/ L3    1    FF    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${交流电压低告警级别}    获取web告警属性    交流电压低_3    告警级别
        should be equal    ${告警级别设置}    ${交流电压低告警级别}
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流告警级别~CID2=81H>>    交流电压低    FF    ${g_ver_1363}
        ${1104告警级别}    获取1104告警级别取值约定    交流电压低_3    ${1104告警级别}
        should be equal    ${告警级别设置}    ${1104告警级别}
        ${web实时告警名}    由子工具获取web实时告警名称    交流电压低_3
        Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    1363    系统交流输入    ${web实时告警名}
    END
    设置web参数量    交流电压低_3    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    #无告警后查询判断
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流电压低_3
    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流告警级别~CID2=81H>>    输入线/相电压L3 L1/ L3    1    FF    ${g_ver_1363}
    should not be true    ${1104告警结果}
    [Teardown]    同时设置三相电压频率    220    50

1363_0020_3输入线/相电压L3交流电压高告警
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压高_3
    ${可设置范围}    获取web参数可设置范围    交流电压高阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高阈值    ${可设置范围}[0]
    ${电压高设置值}    获取web参数量    交流电压高阈值
    ${判断点1}    evaluate    ${电压高设置值}+5
    分别设置各相电压频率    220    220    ${判断点1}    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高_3    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    交流电压高_3
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流电压高_3    ${告警级别设置}
        sleep    2
        wait until keyword succeeds    1m    1    查询指定告警信息    交流电压高
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流段告警量~CID1=40H>>    <<获取交流段告警量~CID2=44H>>    输入线/相电压L3 L1/ L3    2    FF    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${交流电压低告警级别}    获取web告警属性    交流电压高_3    告警级别
        should be equal    ${告警级别设置}    ${交流电压低告警级别}
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流告警级别~CID2=81H>>    交流电压高    FF    ${g_ver_1363}
        ${1104告警级别}    获取1104告警级别取值约定    交流电压高_3    ${1104告警级别}
        should be equal    ${告警级别设置}    ${1104告警级别}
        ${web实时告警名}    由子工具获取web实时告警名称    交流电压高_3
        Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    1363    系统交流输入    ${web实时告警名}
    END
    设置web参数量    交流电压高_3    主要
    #无告警后查询判断
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流电压高_3
    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流告警级别~CID2=81H>>    输入线/相电压L3 L1/ L3    2    FF    ${g_ver_1363}
    should not be true    ${1104告警结果}
    [Teardown]    同时设置三相电压频率    220    50

1363_0022_3输入线/相电压L3缺相告警
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流缺相_3
    分别设置各相电压频率    220    220    0    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流缺相_3    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    2m    1    查询指定告警信息不为    交流缺相
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流缺相_3    ${告警级别设置}
        sleep    2
        wait until keyword succeeds    1m    1    查询指定告警信息    交流缺相
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流段告警量~CID1=40H>>    <<获取交流段告警量~CID2=44H>>    输入线/相电压L3 L1/ L3    3    FF    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${交流缺相告警级别}    获取web告警属性    交流缺相_3    告警级别
        should be equal    '${告警级别设置}'    '${交流缺相告警级别}'
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流告警级别~CID2=81H>>    交流缺相    FF    ${g_ver_1363}
        ${1104告警级别}    获取1104告警级别取值约定    交流缺相_3    ${1104告警级别}
        should be equal    ${告警级别设置}    ${1104告警级别}
        ${web实时告警名}    由子工具获取web实时告警名称    交流缺相_3
        Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    1363    系统交流输入    ${web实时告警名}
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    交流缺相_3    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    #无告警后查询判断
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流缺相_3
    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流告警级别~CID2=81H>>    输入线/相电压L3 L1/ L3    3    FF    ${g_ver_1363}
    should not be true    ${1104告警结果}
    [Teardown]    同时设置三相电压频率    220    50

1363_0024_4交流停电告警
    [Documentation]    获取1104一条告警值并判断告警存在
    ...
    ...    输入：
    ...
    ...
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${理应值}= | ${屏号}=None | ${g_ver}=
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    停电
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    交流供电状态    市电1
    关闭交流源输出
    Wait Until Keyword Succeeds    5m    2    设置web参数量    市电停电    屏蔽    #默认级别：2
    sleep    3
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    市电停电
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流停电    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    交流停电
        sleep    5
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流段告警量~CID1=40H>>    <<获取交流段告警量~CID2=44H>>    交流停电    E0    FF    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${交流停电告警级别}    获取web告警属性    交流停电    告警级别
        should be equal    '${告警级别设置}'    '${交流停电告警级别}'
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流告警级别~CID2=81H>>    交流停电    FF    ${g_ver_1363}
        ${1104告警级别}    获取1104告警级别取值约定    交流停电    ${1104告警级别}
        should be equal    ${告警级别设置}    ${1104告警级别}
        ${web实时告警名}    由子工具获取web实时告警名称    交流停电
        Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    1363    系统交流输入    ${web实时告警名}
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    市电停电    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    #无告警后查询判断
    打开交流源输出
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流停电
    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流段数字量~CID1=40H>>    <<获取交流段数字量~CID2=43H>>    交流停电    E0    FF    ${g_ver_1363}
    should not be true    ${1104告警结果}
    [Teardown]    打开交流源输出

1363_0026_5交流电压不平衡告警
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压不平衡
    ${可设置范围}    获取web参数可设置范围    交流电压不平衡阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压不平衡阈值    ${可设置范围}[0]
    ${电压低设置值}    获取web参数量    交流电压不平衡阈值
    ${判断点1}    evaluate    220-${电压低设置值}-5
    Comment    分别设置各相电压频率    190    250    220    50
    分别设置各相电压频率    ${判断点1}    250    220    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压不平衡    屏蔽    #默认级别：2
    sleep    3
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流电压不平衡
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流电压不平衡    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    交流电压不平衡
        sleep    5
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流段告警量~CID1=40H>>    <<获取交流段告警量~CID2=44H>>    交流电压不平衡    E2    FF    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${交流电压不平衡告警级别}    获取web告警属性    交流电压不平衡    告警级别
        should be equal    ${告警级别设置}    ${交流电压不平衡告警级别}
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流告警级别~CID2=81H>>    交流电压不平衡    FF    ${g_ver_1363}
        Log    ${1104告警级别}
        ${1104告警级别}    获取1104告警级别取值约定    交流电压不平衡    ${1104告警级别}
        should be equal    ${告警级别设置}    ${1104告警级别}
        ${web实时告警名}    由子工具获取web实时告警名称    交流电压不平衡
        Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    1363    系统交流输入    ${web实时告警名}
    END
    设置web参数量    交流电压不平衡    主要
    #无告警后查询判断
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流电压不平衡
    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流告警级别~CID2=81H>>    交流电压不平衡    E2    FF    ${g_ver_1363}
    should not be true    ${1104告警结果}
    [Teardown]    同时设置三相电压频率    220    50

1363_0036_交流相电流IΦ1高告警
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电流
    ${可设置范围}    获取web参数可设置范围    交流电流高阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电流高阈值    ${可设置范围}[0]
    设置负载电压电流    53.5    80
    打开负载输出
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    系统停电
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电流高_1    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    2    判断告警不存在    交流电流高
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        设置web参数量    交流电流高_1    ${告警级别设置}
        sleep    60
        wait until keyword succeeds    1m    1    查询指定告警信息    交流电流高
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流段告警量~CID1=40H>>    <<获取交流段告警量~CID2=44H>>    交流相电流IΦ1    2    FF    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${交流电压低告警级别}    获取web告警属性    交流电流高_1    告警级别
        should be equal    ${告警级别设置}    ${交流电压低告警级别}
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流告警级别~CID2=81H>>    交流电流高    FF    ${g_ver_1363}
        ${1104告警级别}    获取1104告警级别取值约定    交流电流高_1    ${1104告警级别}
        should be equal    ${告警级别设置}    ${1104告警级别}
        ${web实时告警名}    由子工具获取web实时告警名称    交流电压高_1
        Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    1363    系统交流输入    ${web实时告警名}
    END
    #无告警后查询判断
    Comment    同时设置三相电压频率    220    50
    Comment    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流电流高_1
    Comment    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流告警级别~CID2=81H>>    交流相电流IΦ1    2    FF    ${g_ver_1363}
    Comment    should not be true    ${1104告警结果}
    [Teardown]    run keywords    设置web设备参数量为默认值    交流电流高阈值
    ...    AND    关闭负载输出

1363_0028_交流相电流IΦ2高告警
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电流
    ${可设置范围}    获取web参数可设置范围    交流电流高阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电流高阈值    ${可设置范围}[0]
    设置负载电压电流    53.5    80
    打开负载输出
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    系统停电
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电流高_2    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    2    判断告警不存在    交流电流高_2
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        设置web参数量    交流电流高_2    ${告警级别设置}
        sleep    60
        wait until keyword succeeds    1m    1    查询指定告警信息    交流电流高
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流段告警量~CID1=40H>>    <<获取交流段告警量~CID2=44H>>    交流相电流IΦ2    2    FF    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${交流电压低告警级别}    获取web告警属性    交流电流高_2    告警级别
        should be equal    ${告警级别设置}    ${交流电压低告警级别}
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流告警级别~CID2=81H>>    交流电流高    FF    ${g_ver_1363}
        ${1104告警级别}    获取1104告警级别取值约定    交流电流高_2    ${1104告警级别}
        should be equal    ${告警级别设置}    ${1104告警级别}
        ${web实时告警名}    由子工具获取web实时告警名称    交流电流高_2
        Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    1363    系统交流输入    ${web实时告警名}
    END
    #无告警后查询判断
    Comment    同时设置三相电压频率    220    50
    Comment    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流电流高_2
    Comment    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流告警级别~CID2=81H>>    交流相电流IΦ2    2    FF    ${g_ver_1363}
    Comment    should not be true    ${1104告警结果}
    [Teardown]    run keywords    设置web设备参数量为默认值    交流电流高阈值
    ...    AND    关闭负载输出

1363_0030_交流相电流IΦ3高告警
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电流
    ${可设置范围}    获取web参数可设置范围    交流电流高阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电流高阈值    ${可设置范围}[0]
    设置负载电压电流    53.5    80
    打开负载输出
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    系统停电
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电流高_3    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    2    判断告警不存在    交流电流高_3
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        设置web参数量    交流电流高_3    ${告警级别设置}
        sleep    60
        wait until keyword succeeds    1m    1    查询指定告警信息    交流电流高
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流段告警量~CID1=40H>>    <<获取交流段告警量~CID2=44H>>    交流相电流IΦ3    2    FF    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${交流电压低告警级别}    获取web告警属性    交流电流高_3    告警级别
        should be equal    ${告警级别设置}    ${交流电压低告警级别}
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流告警级别~CID2=81H>>    交流电流高    FF    ${g_ver_1363}
        ${1104告警级别}    获取1104告警级别取值约定    交流电流高_3    ${1104告警级别}
        should be equal    ${告警级别设置}    ${1104告警级别}
        ${web实时告警名}    由子工具获取web实时告警名称    交流电流高_3
        Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    1363    系统交流输入    ${web实时告警名}
    END
    #无告警后查询判断
    Comment    同时设置三相电压频率    220    50
    Comment    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流电流高_3
    Comment    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流告警级别~CID2=81H>>    交流相电流IΦ3    2    FF    ${g_ver_1363}
    Comment    should not be true    ${1104告警结果}
    [Teardown]    run keywords    设置web设备参数量为默认值    交流电流高阈值
    ...    AND    关闭负载输出

1363_0032_交流限功率预警
    [Documentation]    子表无该告警级别设置
    [Tags]    3
    电池管理初始化
    Wait Until Keyword Succeeds    10    2    设置web参数量    市电额定有功功率    2.1
    Wait Until Keyword Succeeds    10    2    设置web参数量    市电降额系数    85
    ${可设置范围}    获取web参数可设置范围    交流输入限功率预警阈值
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流输入限功率预警阈值    ${可设置范围}[0]
    ${设置值}    获取web参数量    交流输入限功率预警阈值
    ${电压}    获取web实时数据    直流电压
    run keyword if    ${电压}>54    向下调节电池电压    53.5
    run keyword if    ${电压}<52    向上调节电池电压    53.5
    缓慢设置负载电压电流    ${电压}    28
    打开负载输出
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流输入限功率预警    屏蔽
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    交流输入限功率预警
    设置web参数量    交流输入限功率预警    严重
    #1363查询告警级别与web端级别
    wait until keyword succeeds    3m    1    查询指定告警信息    交流输入限功率预警
    ${告警级别}    获取web告警属性    交流输入限功率预警    告警级别
    should be equal    ${告警级别}    '严重'
    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流段告警量~CID1=40H>>    <<获取交流段告警量~CID2=44H>>    交流输入限功率预警    E3    FF    ${g_ver_1363}
    should be true    ${1104告警结果}
    ${web实时告警名}    由子工具获取web实时告警名称    交流输入限功率预警
    Run Keyword if    '${CSU_role}'=='Client'    主动告警上送判断2    1363    系统交流输入    ${web实时告警名}
    设置web参数量    交流输入限功率预警    严重
    关闭负载输出
    Wait Until Keyword Succeeds    10    2    设置web参数量    市电额定有功功率    15
    设置WEB设备参数量为默认值    交流输入限功率预警阈值
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流输入限功率预警
    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流段告警量~CID1=40H>>    <<获取交流段告警量~CID2=44H>>    交流输入限功率预警    E3    FF    ${g_ver_1363}
    should not be true    ${1104告警结果}
    [Teardown]    run keywords    设置web参数量    交流输入限功率预警    严重
    ...    AND    关闭负载输出

1363_0034_交流限功率告警
    [Documentation]    子表无该告警级别设置
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    10    2    设置web参数量    市电额定有功功率    2.1
    Wait Until Keyword Succeeds    10    2    设置web参数量    市电降额系数    85
    ${可设置范围}    获取web参数可设置范围    交流输入限功率告警阈值
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流输入限功率告警阈值    ${可设置范围}[0]
    ${设置值}    获取web参数量    交流输入限功率告警阈值
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    80
    打开负载输出
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流输入限功率告警    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    交流输入限功率告警
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        wait until keyword succeeds    1m    1    设置web参数量    交流输入限功率告警    ${告警级别设置}
        sleep    5
        wait until keyword succeeds    1m    1    查询指定告警信息    交流输入限功率告警
        ${交流电压过高告警级别}    获取web告警属性    交流输入限功率告警    告警级别
        should be equal    ${告警级别设置}    ${交流电压过高告警级别}
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流段告警量~CID1=40H>>    <<获取交流段告警量~CID2=44H>>    交流输入限功率告警    E4    FF    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${web实时告警名}    由子工具获取web实时告警名称    交流输入限功率告警
        Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    1363    系统交流输入    ${web实时告警名}
    END
    设置WEB设备参数量为默认值    交流输入限功率告警阈值
    Wait Until Keyword Succeeds    10    2    设置web参数量    市电额定有功功率    0
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流输入限功率告警
    [Teardown]    run keywords    Wait Until Keyword Succeeds    10    2    设置web参数量    市电额定有功功率    2.1
    ...    AND    Wait Until Keyword Succeeds    10    2    设置web参数量    交流输入限功率告警    严重
    ...    AND    Wait Until Keyword Succeeds    10    2    设置web参数量    市电额定有功功率    0
    ...    AND    关闭负载输出

1363_0036_交流输入空开告警测试
    实时告警刷新完成
    ${级别设置值}    获取web参数量    交流输入空开断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    交流输入空开断    主要
    wait until keyword succeeds    1m    1    判断告警不存在    交流输入空开断
    设置web参数量    交流输入空开断    屏蔽
    wait until keyword succeeds    3m    1    判断告警不存在    交流输入空开断
    ${告警}    判断告警存在_带返回值    交流输入空开断
    should not be true    ${告警}
    模拟数字量告警    交流输入空开断    ON
    sleep    10
    FOR    ${告警级别设置}    IN    主要    次要    警告    严重
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流输入空开断    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    交流输入空开断
        sleep    5
    #1363查询告警存在
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流段告警量~CID1=40H>>    <<获取交流段告警量~CID2=44H>>    交流输入主空开断    05    FF    ${g_ver_1363}
        should be true    ${1104告警结果}
    #1363查询告警级别与web端级别
        ${交流停电告警级别}    获取web告警属性    交流输入空开断    告警级别
        should be equal    '${告警级别设置}'    '${交流停电告警级别}'
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流告警级别~CID2=81H>>    交流输入空开断    FF    ${g_ver_1363}
        ${1104告警级别}    获取1104告警级别取值约定    交流输入空开断    ${1104告警级别}
        should be equal    ${告警级别设置}    ${1104告警级别}
        Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    1363    交流配电    交流输入空开断
    END
    Comment    设置web参数量    交流输入空开断    次要
    Comment    模拟数字量告警    交流输入空开断    OFF
    [Teardown]    run keywords    设置web参数量    交流输入空开断    次要
    ...    AND    模拟数字量告警    交流输入空开断    OFF

1363_0038_交流防雷器异常测试
    实时告警刷新完成
    ${级别设置值}    获取web参数量    交流防雷器异常
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    交流防雷器异常    主要
    wait until keyword succeeds    1m    1    判断告警不存在    交流防雷器异常
    设置web参数量    交流防雷器异常    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    交流防雷器异常
    ${告警}    判断告警存在_带返回值    交流防雷器异常
    should not be true    ${告警}
    模拟数字量告警    交流防雷器异常    ON
    sleep    10
    FOR    ${告警级别设置}    IN    主要    次要    警告    严重
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流防雷器异常    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    交流防雷器异常
        sleep    5
    #1363查询告警存在
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流段告警量~CID1=40H>>    <<获取交流段告警量~CID2=44H>>    交流防雷器异常    E1    FF    ${g_ver_1363}
        should be true    ${1104告警结果}
    #1363查询告警级别与web端级别
        ${交流停电告警级别}    获取web告警属性    交流防雷器异常    告警级别
        should be equal    '${告警级别设置}'    '${交流停电告警级别}'
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流告警级别~CID2=81H>>    交流防雷器异常    FF    ${g_ver_1363}
        ${1104告警级别}    获取1104告警级别取值约定    交流防雷器异常    ${1104告警级别}
        should be equal    ${告警级别设置}    ${1104告警级别}
        ${web实时告警名}    由子工具获取web实时告警名称    交流防雷器异常
        Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    1363    交流配电    ${web实时告警名}
    END
    Comment    设置web参数量    交流防雷器异常    次要
    Comment    模拟数字量告警    交流防雷器异常    OFF
    [Teardown]    run keywords    设置web参数量    交流防雷器异常    次要
    ...    AND    模拟数字量告警    交流防雷器异常    OFF

1363_0040_交流输出空开断告警测试
    [Tags]    3
    实时告警刷新完成
    ${级别设置值}    获取web参数量    交流输出空开断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    交流输出空开断    主要
    wait until keyword succeeds    1m    1    判断告警不存在    交流输出空开断
    设置web参数量    交流输出空开断    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    交流输出空开断
    ${告警}    判断告警存在_带返回值    交流输出空开断
    should not be true    ${告警}
    Comment    模拟数字量告警    交流输出空开断    ON
    sleep    10
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流输出空开断    ${告警级别设置}
        Comment    wait until keyword succeeds    3m    1    查询指定告警信息    交流输出空开断
        sleep    5
    #1363查询告警存在
        Comment    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流段告警量~CID1=40H>>    <<获取交流段告警量~CID2=44H>>    交流输出空开断    05    FF    ${g_ver_1363}
        Comment    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流段告警量~CID1=40H>>    <<获取交流段告警量~CID2=44H>>    交流输入主空开断    05    FF    ${g_ver_1363}
        Comment    should be true    ${1104告警结果}
    #1363查询告警级别与web端级别
        ${交流停电告警级别}    获取web告警属性    交流输出空开断    告警级别
        should be equal    '${告警级别设置}'    '${交流停电告警级别}'
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取交流告警级别~CID1=40H>>    <<获取交流告警级别~CID2=81H>>    交流输出空开断    FF    ${g_ver_1363}
        ${1104告警级别}    获取1104告警级别取值约定    交流输出空开断    ${1104告警级别}
        should be equal    ${告警级别设置}    ${1104告警级别}
        ${web实时告警名}    由子工具获取web实时告警名称    交流输入主空开断
        Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    1363    交流配电    ${web实时告警名}
    END
    Comment    模拟数字量告警    交流输出空开断    OFF
    [Teardown]    run keywords    设置web参数量    交流输出空开断    次要
    ...    AND    模拟数字量告警    交流输出空开断    OFF
