*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
批量获取油机设备信息测试
    写入CSV文档    油机设备信息获取测试    信号名称    信号值    结果    备注
    连接CSU
    Comment    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string64    INDEX_DEVICE_CH=油机控制屏
    Comment    ${排除列表}    create list
    Comment    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${油机排除设备版本信息}    ${排除列表}
    ...    1
    Comment    : FOR    ${i}    IN    @{列表1}
    Comment        ${信号名称}    Get From Dictionary    ${i}    signal_name
    Comment        Run Keyword And Continue On Failure    南向子设备字符型厂家信息封装判断结果    oileng    ${信号名称}    只读
    ...    V99.23    油机设备信息获取测试
    Comment        Run Keyword And Continue On Failure    南向子设备字符型厂家信息封装判断结果    oileng    ${信号名称}    只读
    ...    V10.10    油机设备信息获取测试
    Comment        Run Keyword And Continue On Failure    南向子设备字符型厂家信息封装判断结果    oileng    ${信号名称}    只读
    ...    V1.81    油机设备信息获取测试
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string64    INDEX_DEVICE_CH=油机控制屏
    ${排除列表}    create list
	@{信号名称列表1}  create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${油机排除设备名称信息}    ${排除列表}    1
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${dict1}    Create Dictionary
        Set To Dictionary    ${dict1}     name     ${信号名称}
		Append To List       ${信号名称列表1}    ${dict1}
    END
	Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    oileng    ${信号名称列表1}    只读    65535
        ...    油机设备信息获取测试
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    oileng    ${信号名称列表1}    只读    45576
        ...    油机设备信息获取测试
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    oileng    ${信号名称列表1}    只读    12312
        ...    油机设备信息获取测试
		
		
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=float    INDEX_DEVICE_CH=油机控制屏
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    1
      Comment    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=int    INDEX_DEVICE_CH=油机控制屏
    Comment    ${排除列表}    create list
    Comment    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}
    ...    1
    Comment    :FOR    ${i}    IN    @{列表1}
    Comment        ${信号名称}    Get From Dictionary    ${i}    signal_name
    Comment        ${缺省值}    获取web参数上下限范围    ${信号名称}
	Comment       ${缺省值列表}   获取缺省值列表  ${列表1}    1
    Comment        Run Keyword And Continue On Failure    南向子设备数值型厂家信息列表封装判断结果    oileng    ${缺省值列表}    只读
    ...    ${缺省值}[1]    油机设备信息获取测试
	Comment       ${缺省值列表}   获取缺省值列表  ${列表1}    2
    Comment        Run Keyword And Continue On Failure    南向子设备数值型厂家信息列表封装判断结果    oileng    ${缺省值列表}    只读
    ...    ${缺省值}[2]    油机设备信息获取测试
	Comment       ${缺省值列表}   获取缺省值列表  ${列表1}    0
    Comment        Run Keyword And Continue On Failure    南向子设备数值型厂家信息列表封装判断结果    oileng    ${缺省值列表}    只读
    ...    ${缺省值}[0]    油机设备信息获取测试
    ${缺省值列表}   获取缺省值列表  ${列表1}    1
	Run Keyword And Continue On Failure    南向子设备数值型厂家信息列表封装判断结果    oileng    ${缺省值列表}    只读   
        ...    油机设备信息获取测试
	${缺省值列表}   获取缺省值列表  ${列表1}    2
	Run Keyword And Continue On Failure    南向子设备数值型厂家信息列表封装判断结果    oileng    ${缺省值列表}    只读    
        ...    油机设备信息获取测试
    ${缺省值列表}   获取缺省值列表  ${列表1}    0
	Run Keyword And Continue On Failure    南向子设备数值型厂家信息列表封装判断结果    oileng    ${缺省值列表}    只读    
        ...    油机设备信息获取测试
    

油机控制屏系统名称获取测试
    连接CSU
    设置子工具值    oileng    all    只读    控制器类型    GM
    Wait Until Keyword Succeeds    10m   5    设备信息获取值    油机控制屏系统名称    GM631
    设置子工具值    oileng    all    只读    控制器类型    TM
    Wait Until Keyword Succeeds    10m   5    设备信息获取值    油机控制屏系统名称    TM631
    设置子工具值    oileng    all    只读    控制器类型    GM
    Wait Until Keyword Succeeds    10m   5    设备信息获取值    油机控制屏系统名称    GM631

油机控制屏软件版本号获取测试
    连接CSU
    设置子工具值    oileng    all    只读    软件版本号    303
    Wait Until Keyword Succeeds    10m   5    设备信息获取值    油机控制屏软件版本    V3.03
    设置子工具值    oileng    all    只读    软件版本号    304
    Wait Until Keyword Succeeds    10m   5    设备信息获取值    油机控制屏软件版本    V3.04
    设置子工具值    oileng    all    只读    软件版本号    305
    Wait Until Keyword Succeeds    10m   5    设备信息获取值    油机控制屏软件版本    V3.05

油机控制屏软件发布日期获取测试
    连接CSU
    设置子工具值    oileng    all    只读    软件发布日期year    5139
    设置子工具值    oileng    all    只读    软件发布日期month    3095
    Wait Until Keyword Succeeds    10m   5    设备信息获取值    油机控制屏软件发布日期    2019-12-23
    设置子工具值    oileng    all    只读    软件发布日期year    5140
    设置子工具值    oileng    all    只读    软件发布日期month    3097
    Wait Until Keyword Succeeds    10m   5    设备信息获取值    油机控制屏软件发布日期    2020-12-25
