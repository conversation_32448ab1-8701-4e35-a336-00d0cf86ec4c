*** Settings ***
Suite Setup       PU测试前置条件    #Run keywords | 测试用例前置条件 | # AND | 仅有市电条件上电
Suite Teardown    PU测试结束条件
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
snmp批量获取状态量和PU告警测试
    写入CSV文档    PU数字量和告警量获取测试    信号名称    信号值    结果
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${级别设置值}    获取web参数量    PU告警
    run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    PU告警    严重
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=太阳能模块
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除告警量信号}    ${排除列表}    3    ${模拟PU起始地址}    1
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    powerUnit    alarm    True    1
    Comment    FOR    ${i}    IN    @{snmp待测}
    ${待测数据长度}    Get Length    ${snmp待测}
    ${待测}    Evaluate    ${待测数据长度}-1
    ${待测索引}    Evaluate    random.randint(0,${待测})    random
    FOR    ${i}    IN    ${snmp待测}[${待测索引}]
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    signal_node
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        Run Keyword And Continue On Failure    整流器或PU告警量产生封装判断结果    snmp    pu    ${信号名称}    数字量    ${告警产生}    ${模拟PU起始地址}    PU告警    PU数字量和告警量获取测试    太阳能模块    ${节点名称}    ${信号序号}    null    null    null
        ...    null
        Run Keyword And Continue On Failure    整流器或PU告警量恢复封装判断结果    snmp    pu    ${信号名称}    数字量    ${告警恢复}    ${模拟PU起始地址}    PU告警    PU数字量和告警量获取测试    太阳能模块    ${节点名称}    ${信号序号}    null    null    null
        ...    null
    END
    断开连接SNMP

snmp批量获取状态量和PU故障测试
    写入CSV文档    PU数字量和故障量获取测试    信号名称    信号值    结果
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${级别设置值}    获取web参数量    PU故障
    run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    PU故障    严重
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=太阳能模块
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除故障量信号}    ${排除列表}    1    ${模拟PU起始地址}    1
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    powerUnit    alarm    True    2
    ${待测数据长度}    Get Length    ${snmp待测}
    ${待测}    Evaluate    ${待测数据长度}-1
    ${待测索引}    Evaluate    random.randint(0,${待测})    random
    FOR    ${i}    IN    ${snmp待测}[${待测索引}]
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    signal_node
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        Run Keyword And Continue On Failure    整流器或PU故障量产生封装判断结果    snmp    pu    ${信号名称}    数字量    ${告警产生}    ${模拟PU起始地址}    PU故障    PU数字量和告警量获取测试    太阳能模块    ${节点名称}    ${信号序号}    null    null    null
        ...    null
        Run Keyword And Continue On Failure    整流器或PU故障量恢复封装判断结果    snmp    pu    ${信号名称}    数字量    ${告警恢复}    ${模拟PU起始地址}    PU故障    PU数字量和告警量获取测试    太阳能模块    ${节点名称}    ${信号序号}    null    null    null
        ...    null
    END
    断开连接SNMP

snmp_0004_光伏回路异常告警_all
    [Documentation]    经讨论，此告警无法自动化
    [Tags]    3
    [Setup]
    连接CSU
    显示属性配置    光伏回路异常状态    数字量    web_attr=On    gui_attr=On
    #判断告警级别
    ${级别设置值}    获取web参数量    光伏回路异常告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    光伏回路异常告警    次要
    ###三个PU，此处开始循环Trap
    FOR    ${PU序号}    IN RANGE    1    ${北向协议PU最大数}+1
        ${PU序号}    evaluate    str(${PU序号})
    #判断无告警
        ${告警不存在}    判断告警存在_带返回值    光伏回路异常告警
        should not be true    ${告警不存在}
    #清零Trap缓存
        ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
        设置子工具告警量    ${PU序号}    呼叫    光伏回路异常状态    1
        sleep    2m
        ${PU1输入过压状态}    获取web实时数据    光伏回路异常状态-${PU序号}
        should be true    '${PU1输入过压状态}' == '异常'
        wait until keyword succeeds    5m    2    查询指定告警信息    光伏回路异常告警
    #snmp产生
        ${snmp英文名}    获取snmp单个告警英文名    光伏回路异常告警_${PU序号}
        ${告警产生}    判断Trap告警产生    ${snmp英文名}
    #消失
        设置子工具告警量    ${PU序号}    呼叫    光伏回路异常状态    0
        sleep    2m
        ${PU1输入过压状态}    获取web实时数据    光伏回路异常状态-${PU序号}
        should be true    '${PU1输入过压状态}' == '正常'
        wait until keyword succeeds    5m    1    判断告警不存在    光伏回路异常告警
        ${snmp英文名}    获取snmp单个告警英文名    光伏回路异常告警_${PU序号}
        ${告警产生}    判断Trap告警消失    ${snmp英文名}
        should be true    ${告警产生}
    END
    显示属性配置    光伏回路异常状态    数字量    web_attr=Off    gui_attr=Off

snmp_0008_PU通讯中断_all
    [Setup]
    连接CSU
    显示属性配置    PU通讯状态    数字量    web_attr=On    gui_attr=On
    #判断告警级别
    Comment    ${级别设置值}    获取web参数量    PU输入过压告警
    Comment    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    PU输入过压告警    主要
    #判断无告警
    ${告警不存在}    判断告警存在_带返回值    PU通讯中断
    should not be true    ${告警不存在}
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    ${设置pu为max-1}    evaluate    ${北向协议PU最大数}-1
    ${设置pu为max-1}    evaluate    str(${设置pu为max-1})
    设置子工具个数    pu    ${设置pu为max-1}
    FOR    ${PU序号}    IN RANGE    1    ${北向协议PU最大数}
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU通讯状态-${PU序号}    正常
    END
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU通讯状态-${北向协议PU最大数}    异常
    wait until keyword succeeds    5m    2    查询指定告警信息    PU通讯中断
    打印web实时告警信息名称
    #snmp产生
    ${snmp英文名}    获取snmp单个告警英文名    PU通讯中断_${北向协议PU最大数}
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    Comment    ${snmp英文名}    获取snmp单个告警英文名    PU通讯中断_3
    Comment    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    Comment    should be true    ${告警产生}
    #消失
    设置子工具个数    pu    ${北向协议PU最大数}
    Comment    设置子工具告警量    2    呼叫    PU输入过压告警    0
    Comment    设置子工具告警量    3    呼叫    PU输入过压告警    0
    FOR    ${PU序号}    IN RANGE    1    ${北向协议PU最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU通讯状态-${PU序号}    正常
    END
    wait until keyword succeeds    5m    2    查询指定告警信息不为    PU通讯中断
    Comment    ${snmp英文名}    获取snmp单个告警英文名    PU告警_1
    Comment    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    Comment    should be true    ${告警产生}
    ${snmp英文名}    获取snmp单个告警英文名    PU通讯中断_${北向协议PU最大数}
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    Comment    ${snmp英文名}    获取snmp单个告警英文名    PU通讯中断_3
    Comment    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    Comment    should be true    ${告警产生}
    显示属性配置    PU通讯状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    run keywords    设置子工具个数    pu    ${北向协议PU最大数}
    ...    AND    sleep    3m

snmp_0001_所有PU模块通讯断告警
    [Setup]
    连接CSU
    显示属性配置    PU通讯状态    数字量    web_attr=On    gui_attr=On
    ${级别设置值}    获取web参数量    所有PU模块通讯断告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    所有PU模块通讯断告警    严重
    ${告警不存在}    判断告警存在_带返回值    所有PU模块通讯断告警
    should not be true    ${告警不存在}
    ${清零Trap缓存}    清零Trap缓存
    控制子工具运行停止    pu    关闭
    FOR    ${PU序号}    IN RANGE    1    ${北向协议PU最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU通讯状态-${PU序号}    异常
    END
    wait until keyword succeeds    5m    2    查询指定告警信息    所有PU模块通讯断告警
    ${snmp英文名}    获取snmp单个告警英文名    所有PU模块通讯断告警
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    控制子工具运行停止    pu    开启
    FOR    ${PU序号}    IN RANGE    1    ${北向协议PU最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU通讯状态-${PU序号}    正常
    END
    wait until keyword succeeds    5m    2    查询指定告警信息不为    PU通讯中断
    wait until keyword succeeds    5m    2    查询指定告警信息不为    所有PU模块通讯断告警
    ${告警消失}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警消失}
    显示属性配置    PU通讯状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    run keywords    设置子工具个数    pu    ${北向协议PU最大数}
    ...    AND    sleep    3m
