*** Settings ***
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_油机控制器工作模式√
    [Documentation]    0:自动/Auto;1:手动/Manual;2:其他模式/Other Mode
    ...
    ...    默认为2
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    设置子工具值    oileng    all    只读    灯亮为    4
    Wait Until Keyword Succeeds    1m    5    信号量数据值为    油机控制器工作模式    手动
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取油机数字量    油机控制器工作模式-1    ${SSH}
    should be equal as numbers    ${power_sm获取值1}    1
    设置子工具值    oileng    all    只读    灯亮为    8
    Wait Until Keyword Succeeds    1m    5    信号量数据值为    油机控制器工作模式    自动
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取油机数字量    油机控制器工作模式-1    ${SSH}
    should be equal as numbers    ${power_sm获取值1}    0
    设置子工具值    oileng    all    只读    灯亮为    0
    Wait Until Keyword Succeeds    1m    5    信号量数据值为    油机控制器工作模式    其他模式
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取油机数字量    油机控制器工作模式-1    ${SSH}
    should be equal as numbers    ${power_sm获取值1}    2
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
    ...    AND    设置子工具值    oileng    all    只读    灯亮为    8

power_sm_0002_油机状态
    [Documentation]    0:未配置/Not Config;1:停止/OFF;2:运行/ON;3:异常/Abnormal;4:过渡/Transition;
    [Tags]    T1-1
    [Setup]    测试用例前置条件
    关闭交流源输出
    仅电池模拟器供电
    连接CSU
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流输入场景    纯油机
    ${获取值1}    获取web实时数据    油机控制器工作模式
    run keyword if    '${获取值1}' != '自动'    Wait Until Keyword Succeeds    5m    1    设置web控制量    油机进入自动模式
    设置子工具值    oileng    all    只读    灯亮为    8
    Wait Until Keyword Succeeds    1m    5    信号量数据值为    油机控制器工作模式    自动
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取油机数字量    油机控制器工作模式-1    ${SSH}
    should be equal as numbers    ${power_sm获取值1}    0
    Wait Until Keyword Succeeds    5m    1    设置web控制量    油机开启
    设置子工具值    oileng    all    只读    发电 A 相电压    299
    设置子工具值    oileng    all    只读    发电 B 相电压    300
    设置子工具值    oileng    all    只读    发电 C 相电压    298
    设置子工具值    oileng    all    只读    转速    1500
    sleep    30s
    ${获取值1}    获取web实时数据    油机相电压_1
    ${获取值2}    获取web实时数据    油机相电压_2
    ${获取值3}    获取web实时数据    油机相电压_3
    ${获取值4}    获取web实时数据    油机转速
    should be true    ${获取值1}==298
    should be true    ${获取值2}==300
    should be true    ${获取值3}==299
    should be true    ${获取值4}==1500
    Wait Until Keyword Succeeds    10m    1    信号量数据值为    油机状态    异常
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取油机数字量    油机状态-1    ${SSH}
    should be equal as numbers    ${power_sm获取值1}    3
    Wait Until Keyword Succeeds    5m    1    设置web控制量    油机开启
    同时设置三相电压频率    220    50
    打开交流源输出
    设置子工具值    oileng    all    只读    发电 A 相电压    299
    设置子工具值    oileng    all    只读    发电 B 相电压    300
    设置子工具值    oileng    all    只读    发电 C 相电压    298
    设置子工具值    oileng    all    只读    转速    1500
    sleep    30s
    ${获取值1}    获取web实时数据    油机相电压_1
    ${获取值2}    获取web实时数据    油机相电压_2
    ${获取值3}    获取web实时数据    油机相电压_3
    ${获取值4}    获取web实时数据    油机转速
    should be true    ${获取值1}==298
    should be true    ${获取值2}==300
    should be true    ${获取值3}==299
    should be true    ${获取值4}==1500
    Wait Until Keyword Succeeds    10m    1    信号量数据值为    油机状态    过渡
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取油机数字量    油机状态-1    ${SSH}
    should be equal as numbers    ${power_sm获取值1}    4
    Wait Until Keyword Succeeds    6m    1    信号量数据值为    油机状态    运行
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取油机数字量    油机状态-1    ${SSH}
    should be equal as numbers    ${power_sm获取值1}    2
    Wait Until Keyword Succeeds    5m    1    设置web控制量    油机关闭
    关闭交流源输出
    设置子工具值    oileng    all    只读    发电 A 相电压    0
    设置子工具值    oileng    all    只读    发电 B 相电压    0
    设置子工具值    oileng    all    只读    发电 C 相电压    0
    设置子工具值    oileng    all    只读    转速    0
    sleep    30s
    ${获取值1}    获取web实时数据    油机相电压_1
    ${获取值2}    获取web实时数据    油机相电压_2
    ${获取值3}    获取web实时数据    油机相电压_3
    ${获取值4}    获取web实时数据    油机转速
    should be true    ${获取值1}==0
    should be true    ${获取值2}==0
    should be true    ${获取值3}==0
    should be true    ${获取值4}==0
    Wait Until Keyword Succeeds    10m    1    信号量数据值为    油机状态    停止
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取油机数字量    油机状态-1    ${SSH}
    should be equal as numbers    ${power_sm获取值1}    1
    [Teardown]    Run keywords    同时设置三相电压频率    220    50
    ...    AND    打开交流源输出
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    交流输入场景    油电
    ...    AND    测试用例后置条件
