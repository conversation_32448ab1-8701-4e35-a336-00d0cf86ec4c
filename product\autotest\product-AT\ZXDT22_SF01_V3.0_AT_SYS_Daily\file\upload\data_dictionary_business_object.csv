业务对象,业务对象英文,业务对象英文简称,业务对象编码,业务对象宏定义,文件名称,设备对象,设备对象英文,设备对象宏定义,系统最大数量,设备有效性判据,备注
系统,System,Sys.,1,BO_SYSTEM,BO_System.c,能源系统,Power System,DEV_PS,1,默认有效,
系统,System,Sys.,1,BO_SYSTEM,BO_System.c,CSU,CSU,DEV_CSU,1,默认有效,
系统,System,Sys.,1,BO_SYSTEM,BO_System.c,GMU,GMU,DEV_GMU,1,暂不支持,
交流,AC,AC,2,BO_AC,BO_Ac.c,系统交流输入,System AC Input,DEV_ACIN,1,交流输入场景 != 无,
交流,AC,AC,2,BO_AC,BO_Ac.c,交流配电,AC Distribution,DEV_ACD,1,交流输入场景 != 无,
交流,AC,AC,2,<PERSON><PERSON>_AC,BO_Ac.c,市电组,Mains Group,DEV_MAINSS,1,市电路数配置 为有效,
交流,AC,AC,2,BO_AC,BO_Ac.c,市电,Mains,DEV_MAINS,2,能源系统-新增“市电路数配置”参数，数量：1;取值：1；2,
交流,AC,AC,2,BO_AC,BO_Ac.c,油机组,Diesel Generator Group,DEV_DGS,1,,
交流,AC,AC,2,BO_AC,BO_Ac.c,油机,Diesel Generator,DEV_DG,2,,
交流,AC,AC,2,BO_AC,BO_Ac.c,油机屏监控,GCP,DEV_GCP,2,,
交流,AC,AC,2,BO_AC,BO_Ac.c,空滤压强传感器,Air Filter Pressure Sensor,DEV_AFPS,2,,
交流,AC,AC,2,BO_AC,BO_Ac.c,油箱,Fuel Tank,DEV_TANK,2,,
交流,AC,AC,2,BO_AC,BO_Ac.c,液位传感器,Liquid Level Sensor,DEV_LLS,2,,
交流,AC,AC,2,BO_AC,BO_Ac.c,油品传感器,Fuel Quality Sensor,DEV_FQS,2,,
交流,AC,AC,2,BO_AC,BO_Ac.c,ATS,ATS,DEV_ATS,2,,
交流,AC,AC,2,BO_AC,BO_Ac.c,ATSCP,ATSCP,DEV_ATSCP,2,,
交流,AC,AC,2,BO_AC,BO_Ac.c,交流电表,ACEM,DEV_ACEM,4,,
整流,SMR,SMR,3,BO_SMR,BO_Smr.c,整流器组,Rectifier Group,DEV_SMRS,1,任一整流器工作状态有效且非“无”,
整流,SMR,SMR,3,BO_SMR,BO_Smr.c,整流器,Rectifier,DEV_SMR,48,整流器工作状态有效且非“无”,
太阳能,Solar,Solar,4,BO_SOLAR,BO_Solar.c,太阳能,Solor Energy,DEV_SOLAR,1,,
太阳能,Solar,Solar,4,BO_SOLAR,BO_Solar.c,光伏组件,PV Module,DEV_PV,1,,
太阳能,Solar,Solar,4,BO_SOLAR,BO_Solar.c,太阳能模块,Solor Energy Modual,DEV_PU,48,,
风能,Wind,Wind,5,BO_WIND,BO_Wind.c,风能,Wind Turbine Energy,DEV_WTE,1,,
风能,Wind,Wind,5,BO_WIND,BO_Wind.c,风机,Wind Turbine,DEV_WT,2,,
电池,Battery,Batt.,6,BO_BATTERY,BO_Battery.c,电池组,Battery Group,DEV_BATTS,1,电池类型有效,"面向业务的公共设备对象，铅酸电池组、铁锂电池组等个性化设备取消，全部合入“电池组”设备对象，以可选属性方式体现。注：不区分电池类型，电池组包含所有电池信息，和平台SID进行数据交互。
除执行目前定义的电池充放电管理外，下电业务也需要处理。"
电池,Battery,Batt.,6,BO_BATTERY,BO_Battery.c,电池,Battery,DEV_BATT,4,电池组容量 >= 10AH,面向业务的公共设备对象，电池容量决定对象是否存在，铅酸电池、铁锂电池等个性化属性，以可选属性方式体现。
电池,Battery,Batt.,6,BO_BATTERY,BO_Battery.c,普通铅酸电池,VRLA Battery,DEV_LAB,1,,电池类型个性化属性，用于电池组场景配置化，通常情况不呈现
电池,Battery,Batt.,6,BO_BATTERY,BO_Battery.c,铅酸电池25,VRLA25 Battery,DEV_LAB25,1,,电池类型个性化属性，用于电池组场景配置化，通常情况不呈现
电池,Battery,Batt.,6,BO_BATTERY,BO_Battery.c,深循环电池,DCYC Battery,DEV_DCYCB,1,,电池类型个性化属性，用于电池组场景配置化，通常情况不呈现
电池,Battery,Batt.,6,BO_BATTERY,BO_Battery.c,快充电池,FC Battery,DEV_FCB,1,,电池类型个性化属性，用于电池组场景配置化，通常情况不呈现
电池,Battery,Batt.,6,BO_BATTERY,BO_Battery.c,高温电池,HT Battery,DEV_HTB,1,,电池类型个性化属性，用于电池组场景配置化，通常情况不呈现
电池,Battery,Batt.,6,BO_BATTERY,BO_Battery.c,储能电池,SE Battery,DEV_SEB,1,,电池类型个性化属性，用于电池组场景配置化，通常情况不呈现
电池,Battery,Batt.,6,BO_BATTERY,BO_Battery.c,FB15电池,FB15 Battery,DEV_FB15,1,,电池类型个性化属性，用于电池组场景配置化，通常情况不呈现
电池,Battery,Batt.,6,BO_BATTERY,BO_Battery.c,FBBMS,FBBMS,DEV_FBBMS,16,,FB电池通讯相关数据
电池,Battery,Batt.,6,BO_BATTERY,BO_Battery.c,FB500电池,FB500 Battery,DEV_FB500,1,,"电池类型个性化属性，用于电池组场景配置化，该对象包含：
1 FA电池组管理信息；
2 FA电池系统特性（油机、市电相关参数）；
3 FA控制器相关集成的信息。"
电池,Battery,Batt.,6,BO_BATTERY,BO_Battery.c,FB500控制器,FB500 Battery Controller,DEV_FB500CU,16,,"FB500铁锂电池控制器,AM:acquistion module"
电池,Battery,Batt.,6,BO_BATTERY,BO_Battery.c,FA电池,FA Battery,DEV_FA,1,,"电池类型个性化属性，用于电池组场景配置化；
该对象包含：
1 FA电池组管理信息；
2 FA电池系统特性（油机、市电相关参数）；
3 FA控制器相关集成的信息。"
电池,Battery,Batt.,6,BO_BATTERY,BO_Battery.c,FA控制器,FA Battery Controller,DEV_FACU,16,,FA铁锂电池控制器
电池,Battery,Batt.,6,BO_BATTERY,BO_Battery.c,TB101电池,TB101 Battery,DEV_TB101,1,,电池类型个性化属性，用于电池组场景配置化
电池,Battery,Batt.,6,BO_BATTERY,BO_Battery.c,TB101控制器,TB101 Battery Controller,DEV_TB101CU,16,,TB101锂电池控制器
电池,Battery,Batt.,6,BO_BATTERY,BO_Battery.c,BCU检测单元,BCU,DEV_BCU,4,,铅酸电池内阻检测单元，BCU与BRU合并为一个对象，便于实现BCU对BRU属性的管理
电池,Battery,Batt.,6,BO_BATTERY,BO_Battery.c,PBMU检测单元,PBMU ,DEV_PBMU,4,,铅酸电池单体检测单元
电池,Battery,Batt.,6,BO_BATTERY,BO_Battery.c,电池切换单元,Battery Switch Unit,DEV_HBTS,1,,
负载,Load,Load,7,BO_LOAD,BO_Load.c,直流负载,DC Load,DEV_DCL,1,,
负载,Load,Load,7,BO_LOAD,BO_Load.c,直流配电,DC Distribution,DEV_DCD,1,任一功率模块在位,"1、硬件单元，是数据采样、控制执行单元，下电只确定下电模式（一次、二次、电池），不处理下电业务。
2、新增电池分流器与电池空开的对应关系配置、电池分流器配置、分流器电流零点、斜率、电池电压检测通道零点等定义，充分体现软件适配直流配电灵活性的特点。"
负载,Load,Load,7,BO_LOAD,BO_Load.c,SDU,SDU,DEV_SDU,1,,
负载,Load,Load,7,BO_LOAD,BO_Load.c,租户直流负载,Tenant DC Load,DEV_TENANT,4,,只有配置了租户，才存在租户负载这个对象
负载,Load,Load,7,BO_LOAD,BO_Load.c,共享共建配电,Sharing and Co-construction Distribution,DEV_SCD,1,,只有配置了共享共建场景，才存在共享共建配电单元
负载,Load,Load,7,BO_LOAD,BO_Load.c,逆变器,Inverter,DEV_INVR,1,,
负载,Load,Load,7,BO_LOAD,BO_Load.c,直流备用输入,DC Reserve Input,DEV_DCR,1,,
负载,Load,Load,7,BO_LOAD,BO_Load.c,直流变换器,DC Converter,DEV_DCC,6,,
负载,Load,Load,7,BO_LOAD,BO_Load.c,直流电表,DCEM,DEV_DCEM,2,,需要配置各通道对应的租户电流
环境,Environment,Envi.,8,BO_ENVIRONMENT,BO_Environment.c,系统运行环境,System Running Environment,DEV_ENV,1,,"后续实现：
新增“系统运行环境检测”配置参数，0:无/Null;1:有/Exist"
环境,Environment,Envi.,8,BO_ENVIRONMENT,BO_Environment.c,FCTL,FCTL,DEV_FCTL,2,,
环境,Environment,Envi.,8,BO_ENVIRONMENT,BO_Environment.c,TEC空调,TEC Airconditioner,DEV_TEC,2,,
环境,Environment,Envi.,8,BO_ENVIRONMENT,BO_Environment.c,柜内空调,Air Conditioning in Cabinet,DEV_CABAIR,4,,
环境,Environment,Envi.,8,BO_ENVIRONMENT,BO_Environment.c,交流空调,AC Airconditioner,DEV_ACAIR,2,,
环境,Environment,Envi.,8,BO_ENVIRONMENT,BO_Environment.c,直流空调,DC Airconditioner,DEV_DCAIR,4,,
环境,Environment,Envi.,8,BO_ENVIRONMENT,BO_Environment.c,空调,Airconditioner,DEV_AIR,2,,
环境,Environment,Envi.,8,BO_ENVIRONMENT,BO_Environment.c,热交换器,Heat Exchanger,DEV_HEAT,4,,
环境,Environment,Envi.,8,BO_ENVIRONMENT,BO_Environment.c,新风系统,Fan Control System,DEV_FCS,1,,
视频管理系统,Video Management System,VMS,9,BO_VIDEO,BO_Video.c,视频系统,Video System,DEV_VIDEO,1,,
