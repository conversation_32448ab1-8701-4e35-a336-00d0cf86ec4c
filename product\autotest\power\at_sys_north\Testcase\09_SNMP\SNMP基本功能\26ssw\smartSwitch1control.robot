*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
smartSwitchLVD1
    [Documentation]    smartSwitchLVD1value
    连接CSU
    同步系统时间
    进行SNMP_V2/V3连接    ${snmp连接方式}
    @{SSW随机list}    从1-15个数中随机选n个不重复的单体    ${SSW最大数目}    4
    FOR    ${智能空开序号}    IN    @{SSW随机list}
        ${设置结果}    snmp控制量循环体    smartSwitchSmartSwitchLVD${智能空开序号}value
        should be true    ${设置结果}
        ${存在结果}    判断web历史记录存在snmp控制内容    智能空开    智能空开下电    300    ${智能空开序号}
        should be true    ${存在结果}
    END
    断开连接SNMP

smartSwitchUpload1
    连接CSU
    同步系统时间
    进行SNMP_V2/V3连接    ${snmp连接方式}
    @{SSW随机list}    从1-15个数中随机选n个不重复的单体    ${SSW最大数目}    4
    FOR    ${智能空开序号}    IN    @{SSW随机list}
        ${设置结果}    snmp控制量循环体    smartSwitchSmartSwitchUpload${智能空开序号}value
        should be true    ${设置结果}
        sleep    10
        ${存在结果}    判断web历史记录存在snmp控制内容    智能空开    智能空开上电    300    ${智能空开序号}
        should be true    ${存在结果}
    END
    断开连接SNMP

smartSwitchEnergyReset1
    连接CSU
    同步系统时间
    进行SNMP_V2/V3连接    ${snmp连接方式}
    @{SSW随机list}    从1-15个数中随机选n个不重复的单体    ${SSW最大数目}    3
    FOR    ${智能空开序号}    IN    @{SSW随机list}
        ${设置结果}    snmp控制量循环体    smartSwitchSmartSwitchEnergyReset${智能空开序号}value
        should be true    ${设置结果}
        sleep    10
        ${存在结果}    判断web历史记录存在snmp控制内容    智能空开    智能空开电量清零    300    ${智能空开序号}
        should be true    ${存在结果}
    END
    断开连接SNMP

smartSwitchReset1
    连接CSU
    同步系统时间
    进行SNMP_V2/V3连接    ${snmp连接方式}
    @{SSW随机list}    从1-15个数中随机选n个不重复的单体    ${SSW最大数目}    3
    FOR    ${智能空开序号}    IN    @{SSW随机list}
        ${设置结果}    snmp控制量循环体    smartSwitchSmartSwitchReset${智能空开序号}value
        should be true    ${设置结果}
        sleep    10
        ${存在结果}    判断web历史记录存在snmp控制内容    智能空开    智能空开复位    300    ${智能空开序号}
        should be true    ${存在结果}
    END
    断开连接SNMP
