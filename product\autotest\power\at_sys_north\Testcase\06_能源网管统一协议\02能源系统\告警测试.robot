*** Settings ***
Suite Setup       Run Keywords   主动告警测试前置条件    ${CSU_role}
...    AND    测试用例前置条件
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Suite Teardown    测试用例后置条件
Default Tags      T1-1
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0002_系统过载告警
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    ${级别设置值}    获取web参数量    系统过载告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web设备参数量为默认值    系统过载告警
    Comment    wait until keyword succeeds    10    1    设置web参数量    市电额定有功功率    0    #交流输入限功率告警会屏蔽系统过载
    wait until keyword succeeds    30    1    设置web参数量    系统过载告警阈值    80
    设置负载电压电流    53.5    70
    打开负载输出
    Wait Until Keyword Succeeds    5m    2    判断告警不存在    系统过载告警
    wait until keyword succeeds    30    1    设置web参数量    系统过载告警阈值    20
    ${负载总电流}    获取web实时数据    负载总电流
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    系统过载告警
    ${告警级别取值约定dict}    获取web参数的取值约定    系统过载告警
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    系统过载告警    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    系统过载告警
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别}'=='主要'    主动告警上送判断2    power_sm    能源系统    系统过载告警
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    系统过载告警    ${SSH}
        should be true    ${power_sm告警存在}[0]
        Comment    将数字型告警级别转换为中文
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别}
    END
    设置web设备参数量为默认值    系统过载告警阈值
    关闭负载输出
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    系统过载告警
    ${power_Sm告警存在}    判断power_sm是否存在单个实时告警    系统过载告警    ${SSH}
    should not be true    ${power_Sm告警存在}[0]
    [Teardown]    Run keywords    设置web设备参数量为默认值    系统过载告警
    ...    AND    设置web设备参数量为默认值    系统过载告警阈值
    ...    AND    关闭负载输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0003_交流输入场景配置错误告警
    电池管理初始化
    ${级别设置值}    获取web参数量    交流输入场景配置错误告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web设备参数量为默认值    交流输入场景配置错误告警
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流输入场景    无
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    交流输入场景配置错误告警
    设置web参数量    交流输入场景配置错误告警    屏蔽
    ${告警级别取值约定dict}    获取web参数的取值约定    交流输入场景配置错误告警
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        设置web参数量    交流输入场景配置错误告警    ${告警级别}
        Wait Until Keyword Succeeds    1m    1    查询指定告警信息    交流输入场景配置错误告警
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别}'=='主要'    主动告警上送判断2    power_sm    能源系统    交流输入场景配置错误告警
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    交流输入场景配置错误告警    ${SSH}
        should be true    ${power_sm告警存在}[0]
        Comment    将数字型告警级别转换为中文
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别}
    END
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流输入场景    市电    #市电场景测试
    wait until keyword succeeds    3m    2    判断告警不存在    交流输入场景配置错误告警
    设置web设备参数量为默认值    交流输入场景配置错误告警
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流输入场景配置错误告警
    ${power_Sm告警存在}    判断power_sm是否存在单个实时告警    交流输入场景配置错误告警    ${SSH}
    should not be true    ${power_Sm告警存在}[0]
    [Teardown]    Run keywords    设置web设备参数量为默认值    交流输入场景配置错误告警
    ...    AND    设置web参数量    交流输入场景    市电
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
