*** Settings ***
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
周期测试时间到时进测试
    [Documentation]    0:浮充/Float;1:均充/Equal;2:测试/Test;3:放电/Discharge;4:检测/Detect;5:过渡/Transition;6:充电/Charge
    
    电池管理初始化
    设置web参数量    电池均充周期    0    #默认90
    设置web参数量    电池测试周期    60    #默认0天
    设置web参数量    电池检测周期    0    #默认30
    sleep    5
    ${电池测试周期}    获取web参数量    电池测试周期
    ${下次测试时间}    获取web实时数据    下次测试时间
    ${下次测试时间0}    Subtract Time From Date    ${下次测试时间}    5s    #比10:00:00差5s
    设置系统时间    ${下次测试时间0}
    sleep    8
    ${修改时间后的系统时间}    获取系统时间
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    测试
    ${下次测试时间_cal}    add time to date    ${下次测试时间}    ${电池测试周期}d    exclude_millis=yes
    sleep    5
    ${下次测试时间_new}    获取web实时数据    下次测试时间
    should be equal    ${下次测试时间_new}    ${下次测试时间_cal}
    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    浮充
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池均充周期    电池测试周期    电池检测周期

测试周期为0时无周期测试功能
    
    电池管理初始化
    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    浮充
    设置web参数量    电池均充周期    0    #默认90
    设置web参数量    电池测试周期    60    #默认0天
    设置web参数量    电池检测周期    0    #默认0
    sleep    5
    ${电池测试周期}    获取web参数量    电池测试周期
    ${下次测试时间}    获取web实时数据    下次测试时间
    ${下次测试时间0}    Subtract Time From Date    ${下次测试时间}    5s    #比10:00:00差5s
    设置web参数量    电池测试周期    0
    sleep    5
    设置系统时间    ${下次测试时间0}
    sleep    1m
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    浮充
    ${系统时间}    Subtract Time From Date    ${下次测试时间0}    7 days
    设置系统时间    ${系统时间}
    sleep    5
    设置web参数量    电池测试周期    60    #默认0天
    sleep    5
    ${下次测试时间_new}    获取web实时数据    下次测试时间
    should be equal    ${下次测试时间_new}    ${下次测试时间}
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池均充周期    电池测试周期    电池检测周期

测试时周期测试时间到
    
    电池管理初始化
    ${测试最长时间范围}    获取web参数上下限范围_有单位    测试最长时间
    ${测试最长时间}    run keyword if    '${测试最长时间范围}[3]'== 'Hour'    evaluate    ${测试最长时间范围}[1]+1
    ...    ELSE    evaluate    ${测试最长时间范围}[1]+5
    设置web参数量    电池均充周期    0
    设置web参数量    电池测试周期    80
    设置web参数量    电池检测周期    0
    设置web参数量    测试最长时间    ${测试最长时间}
    设置web参数量    均充使能    允许
    sleep    5
    ${均充末期维持时间范围}    获取web参数上下限范围_有单位    均充末期维持时间
    ${均充最短时间范围}    获取web参数上下限范围_有单位    均充最短时间
    ${均充最长时间范围}    获取web参数上下限范围_有单位    均充最长时间
    ${均充末期维持时间}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间范围}[1]+1
    ...    ELSE    evaluate    ${均充末期维持时间范围}[1]+3
    ${均充最短时间}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间范围}[1]+1
    ...    ELSE    evaluate    ${均充最短时间范围}[1]+3
    ${均充最长时间}    run keyword if    '${均充最长时间范围}[3]'== 'Hour'    evaluate    ${均充最长时间范围}[1]+1
    ...    ELSE    evaluate    ${均充最长时间范围}[1]+5
    设置web参数量    均充末期维持时间    ${均充末期维持时间}
    设置web参数量    均充最短时间    ${均充最短时间}
    设置web参数量    均充最长时间    ${均充最长时间}
    ${均充末期维持时间转换后}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间}*60
    ...    ELSE    set variable    ${均充末期维持时间}
    ${均充最短时间转换后}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间}*60
    ...    ELSE    set variable    ${均充最短时间}
    ${均充最长时间转换后}    run keyword if    '${均充最长时间范围}[3]'== 'Hour'    evaluate    ${均充最长时间}*60
    ...    ELSE    set variable    ${均充最长时间}
    ${测试最长时间转换后}    run keyword if    '${测试最长时间范围}[3]'== 'Hour'    evaluate    ${测试最长时间}*60
    ...    ELSE    set variable    ${测试最长时间}
    log    ===单位换算完成===
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动测试
    Wait Until Keyword Succeeds    10    1    信号量数据值为    电池管理状态    测试
    sleep    1m
    ${电池测试周期}    获取web参数量    电池测试周期
    ${下次测试时间}    获取web实时数据    下次测试时间
    ${下次测试时间0}    Subtract Time From Date    ${下次测试时间}    5s    #比10:00:00差5s
    ${测试持续时间1}    获取web实时数据    电池状态持续时间
    设置系统时间    ${下次测试时间0}
    sleep    1m30s
    ${测试持续时间2}    获取web实时数据    电池状态持续时间
    should be true    ${测试持续时间2}>${测试持续时间1}
    Wait Until Keyword Succeeds    ${测试最长时间转换后}m    1    信号量数据值不为    电池管理状态    测试
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    均充
    ${等待时间}    evaluate    ${均充最长时间转换后}+3
    Wait Until Keyword Succeeds    ${等待时间}m    1    信号量数据值不为    电池管理状态    均充
    ${下次测试时间1}    获取web实时数据    下次测试时间
    should be equal    ${下次测试时间1}    ${下次测试时间}
    sleep    10
    #如下为周期测试
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    测试
    ${下次测试时间_cal}    add time to date    ${下次测试时间}    ${电池测试周期}d    exclude_millis=yes
    ${下次测试时间_new}    获取web实时数据    下次测试时间
    should be equal    ${下次测试时间_new}    ${下次测试时间_cal}
    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    浮充
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池测试周期    电池均充周期    电池检测周期    测试最长时间
    ...    均充最长时间    均充最短时间    均充末期维持时间

均充时周期测试时间到
    
    电池管理初始化
    设置web参数量    电池均充周期    0
    设置web参数量    电池测试周期    80
    设置web参数量    电池检测周期    0
    设置web参数量    均充使能    允许
    sleep    5
    ${均充末期维持时间范围}    获取web参数上下限范围_有单位    均充末期维持时间
    ${均充最短时间范围}    获取web参数上下限范围_有单位    均充最短时间
    ${均充最长时间范围}    获取web参数上下限范围_有单位    均充最长时间
    ${均充末期维持时间}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间范围}[1]+1
    ...    ELSE    evaluate    ${均充末期维持时间范围}[1]+3
    ${均充最短时间}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间范围}[1]+1
    ...    ELSE    evaluate    ${均充最短时间范围}[1]+3
    ${均充最长时间}    run keyword if    '${均充最长时间范围}[3]'== 'Hour'    evaluate    ${均充最长时间范围}[1]+1
    ...    ELSE    evaluate    ${均充最长时间范围}[1]+5
    设置web参数量    均充末期维持时间    ${均充末期维持时间}
    设置web参数量    均充最短时间    ${均充最短时间}
    设置web参数量    均充最长时间    ${均充最长时间}
    ${均充末期维持时间转换后}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间}*60
    ...    ELSE    set variable    ${均充末期维持时间}
    ${均充最短时间转换后}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间}*60
    ...    ELSE    set variable    ${均充最短时间}
    ${均充最长时间转换后}    run keyword if    '${均充最长时间范围}[3]'== 'Hour'    evaluate    ${均充最长时间}*60
    ...    ELSE    set variable    ${均充最长时间}
    log    ===单位换算完成===
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动均充
    Wait Until Keyword Succeeds    10    1    信号量数据值为    电池管理状态    均充
    sleep    1m
    ${电池测试周期}    获取web参数量    电池测试周期
    ${下次测试时间}    获取web实时数据    下次测试时间
    ${下次测试时间0}    Subtract Time From Date    ${下次测试时间}    5s    #比10:00:00差5s
    设置系统时间    ${下次测试时间0}
    sleep    1m
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    均充
    Wait Until Keyword Succeeds    ${均充最长时间转换后}m    1    信号量数据值不为    电池管理状态    均充
    sleep    10
    #均充结束进入周期测试
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    测试
    ${下次测试时间_cal}    add time to date    ${下次测试时间}    ${电池测试周期}d    exclude_millis=yes
    ${下次测试时间_new}    获取web实时数据    下次测试时间
    should be equal    ${下次测试时间_new}    ${下次测试时间_cal}
    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    浮充
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池测试周期    电池均充周期    电池检测周期    均充最长时间
    ...    均充最短时间    均充末期维持时间

检测时周期测试时间到
    
    电池管理初始化
    设置web参数量    电池均充周期    0
    设置web参数量    电池测试周期    80
    设置web参数量    电池检测周期    0
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    10    1    信号量数据值为    电池管理状态    检测
    sleep    30
    ${电池测试周期}    获取web参数量    电池测试周期
    ${下次测试时间}    Wait Until Keyword Succeeds    30    1    获取web实时数据    下次测试时间
    ${下次测试时间0}    Subtract Time From Date    ${下次测试时间}    5s    #比10:00:00差5s
    设置系统时间    ${下次测试时间0}
    sleep    8
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    检测
    Wait Until Keyword Succeeds    3m    2    信号量数据值不为    电池管理状态    检测
    sleep    8
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    测试
    ${下次测试时间_cal}    add time to date    ${下次测试时间}    ${电池测试周期}d    exclude_millis=yes
    ${下次测试时间_new}    获取web实时数据    下次测试时间
    should be equal    ${下次测试时间_new}    ${下次测试时间_cal}
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池均充周期    电池测试周期    电池检测周期

测试周期对下次测试时间影响
    
    电池管理初始化
    设置web参数量    电池均充周期    0    #默认90
    设置web参数量    电池测试周期    60    #默认0天
    设置web参数量    电池检测周期    0    #默认30
    sleep    5
    ${电池测试周期}    获取web参数量    电池测试周期
    ${下次测试时间}    获取web实时数据    下次测试时间
    ${下次测试时间0}    Subtract Time From Date    ${下次测试时间}    5s    #比10:00:00差5s
    设置系统时间    ${下次测试时间0}
    sleep    8
    ${修改时间后的系统时间}    获取系统时间
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    测试
    ${下次测试时间_cal}    add time to date    ${下次测试时间}    ${电池测试周期}d    exclude_millis=yes
    sleep    5
    ${下次测试时间_new}    获取web实时数据    下次测试时间
    should be equal    ${下次测试时间_new}    ${下次测试时间_cal}
    设置web参数量    电池测试周期    70    #默认0天
    ${电池测试周期new}    获取web参数量    电池测试周期
    ${下次测试时间_cal1}    add time to date    ${下次测试时间}    ${电池测试周期new}d    exclude_millis=yes
    sleep    5
    ${下次测试时间_new1}    获取web实时数据    下次测试时间
    should be equal    ${下次测试时间_new1}    ${下次测试时间_cal1}
    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    浮充
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池均充周期    电池测试周期    电池检测周期

周期测试时设置测试周期为0
    
    电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池均充周期    0    #默认90
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池测试周期    60    #默认0天
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测周期    0    #默认30
    ${测试最长时间范围}    获取web参数上下限范围_有单位    测试最长时间
    ${测试最长时间}    run keyword if    '${测试最长时间范围}[3]'== 'Hour'    evaluate    ${测试最长时间范围}[1]+1
    ...    ELSE    evaluate    ${测试最长时间范围}[1]+5
    Wait Until Keyword Succeeds    10    1    设置web参数量    测试最长时间    ${测试最长时间}
    sleep    5
    ${电池测试周期}    获取web参数量    电池测试周期
    ${下次测试时间}    获取web实时数据    下次测试时间
    ${下次测试时间0}    Subtract Time From Date    ${下次测试时间}    5s    #比10:00:00差5s
    设置系统时间    ${下次测试时间0}
    sleep    8
    ${修改时间后的系统时间}    获取系统时间
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    测试
    ${下次测试时间_cal}    add time to date    ${下次测试时间}    ${电池测试周期}d    exclude_millis=yes
    sleep    5
    ${下次测试时间_new}    获取web实时数据    下次测试时间
    should be equal    ${下次测试时间_new}    ${下次测试时间_cal}
    ${测试时长l}    获取web实时数据    电池状态持续时间
    should be true    ${测试时长l} < 5
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池测试周期    0    #默认0天
    sleep    1m
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    测试
    ${测试时长newl}    获取web实时数据    电池状态持续时间
    should be true    ${测试时长l} <= ${测试时长newl}
    ${下次测试时间3}    run keyword and ignore error    获取web实时数据    下次测试时间
    should be true    '${下次测试时间3}[0]' == 'PASS' and '${下次测试时间3}[1]' == ''
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池均充周期    电池测试周期    电池检测周期    测试最长时间
