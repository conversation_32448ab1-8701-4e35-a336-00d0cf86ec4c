*** Settings ***
Force Tags        3
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
1363_0002_V_3.0_1363_通用命令_厂家信息1-6_1
    [Tags]    2
    ${1104数据}    通用命令_获取厂家信息    ${g_prtcl_1363}    <<获取设备（监控模块）厂家信息1-6 (自定义)~CID1=40H>>    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${1104数据}
    ${对比结果}    批量对比数据_1104_WEB    ${1104数据}    ${web数据}    1363_厂家信息_1
    should be true    ${对比结果}

1363_0004_V_3.0_1363_通用命令_厂家信息1-6_2
    [Tags]    2
    ${1104数据}    通用命令_获取厂家信息    ${g_prtcl_1363}    <<获取设备（监控模块）厂家信息1-6 (自定义)~CID1=41H>>    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${1104数据}
    ${对比结果}    批量对比数据_1104_WEB    ${1104数据}    ${web数据}    1363_厂家信息_2
    should be true    ${对比结果}

1363_0006_V_3.0_1363_通用命令_厂家信息1-6_3
    [Tags]    2
    ${1104数据}    通用命令_获取厂家信息    ${g_prtcl_1363}    <<获取设备（监控模块）厂家信息1-6 (自定义)~CID1=42H>>    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${1104数据}
    ${对比结果}    批量对比数据_1104_WEB    ${1104数据}    ${web数据}    1363_厂家信息_3
    should be true    ${对比结果}

1363_0008_V_3.0_1363_通用命令_厂家信息1-6_4
    [Tags]    2
    ${1104数据}    通用命令_获取厂家信息    ${g_prtcl_1363}    <<获获取设备（监控模块）厂家信息1-6 (自定义)~CID1=91H>>    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${1104数据}
    ${对比结果}    批量对比数据_1104_WEB    ${1104数据}    ${web数据}    1363_厂家信息_4
    should be true    ${对比结果}

1363_0010_V_3.0_1363_通用命令_厂家信息1-6_5
    [Tags]    2
    ${1104数据}    通用命令_获取厂家信息    ${g_prtcl_1363}    <<获取设备（监控模块）厂家信息1-6 (自定义)~CID1=D0H>>    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${1104数据}
    ${对比结果}    批量对比数据_1104_WEB    ${1104数据}    ${web数据}    1363_厂家信息_5
    should be true    ${对比结果}

1363_0012_V_3.0_1363_通用命令_厂家信息1-6_6
    [Tags]    2
    ${1104数据}    通用命令_获取厂家信息    ${g_prtcl_1363}    <<获取设备（监控模块）厂家信息1-6 (自定义)~CID1=46H>>    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${1104数据}
    ${对比结果}    批量对比数据_1104_WEB    ${1104数据}    ${web数据}    1363_厂家信息_6
    should be true    ${对比结果}
