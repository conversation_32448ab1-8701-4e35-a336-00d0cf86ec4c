*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
CAN总线设备统计测试
    [Setup]    测试用例前置条件
    连接CSU
    Wait Until Keyword Succeeds    10    2    设置web控制量    CAN总线设备统计
    Wait Until Keyword Succeeds    10    2    设置web控制量    CAN总线设备统计
    Wait Until Keyword Succeeds    10    2    设置web控制量    CAN总线设备统计
    sleep    10
    ${起始时间}    ${结束时间}    获取起始结束时间段    60
    ${历史数据数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    CAN总线设备统计
    should be true    4>=${历史数据数量}>=2
    Wait Until Keyword Succeeds    5m    2    判断告警不存在    整流器通讯中断
    Wait Until Keyword Succeeds    5m    2    判断告警不存在    PU通讯中断

RS485总线设备统计测试
    [Setup]    测试用例前置条件
    连接CSU
    Wait Until Keyword Succeeds    20    2    设置web控制量    RS485总线设备统计
    Wait Until Keyword Succeeds    20    2    设置web控制量    RS485总线设备统计
    Wait Until Keyword Succeeds    20    2    设置web控制量    RS485总线设备统计
    sleep    10
    ${起始时间}    ${结束时间}    获取起始结束时间段    100
    ${历史数据数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    RS485总线设备统计
    should be true    4>=${历史数据数量}>=2
    Wait Until Keyword Succeeds    5m    2    判断告警不存在    BMS通信断告警

开启SSH功能测试
    [Setup]    测试用例前置条件
    连接CSU
    Wait Until Keyword Succeeds    10    2    设置web控制量    开启SSH
    Wait Until Keyword Succeeds    10    2    设置web控制量    开启SSH
    Wait Until Keyword Succeeds    10    2    设置web控制量    开启SSH
    sleep    10
    ${起始时间}    ${结束时间}    获取起始结束时间段    60
    ${历史数据数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    开启SSH
    should be true    4>=${历史数据数量}>=2

关闭SSH功能测试
    [Setup]    测试用例前置条件
    连接CSU
    Wait Until Keyword Succeeds    10    2    设置web控制量    关闭SSH
    Wait Until Keyword Succeeds    10    2    设置web控制量    关闭SSH
    Wait Until Keyword Succeeds    10    2    设置web控制量    关闭SSH
    sleep    10
    ${起始时间}    ${结束时间}    获取起始结束时间段    60
    ${历史数据数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    关闭SSH
    should be true    4>=${历史数据数量}>=2

监控单元复位测试
    [Setup]    测试用例前置条件
    连接CSU
    系统复位
    sleep    90
    连接CSU
    ${起始时间}    ${结束时间}    获取起始结束时间段    300
    ${历史数据数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    运行记录    系统复位 Web复位
    should be true    2>=${历史数据数量}>=1

蜂鸣器控制测试
    [Setup]    测试用例前置条件
    连接CSU
    蜂鸣器开启
    sleep    5
    蜂鸣器关闭
    sleep    5
    蜂鸣器开启
    sleep    5
    蜂鸣器关闭
    sleep    5
    蜂鸣器开启
    sleep    5
    ${起始时间}    ${结束时间}    获取起始结束时间段    30
    @{111时间}    获取web历史事件内容    ${起始时间}    ${结束时间}    200    1    10
    ${历史数据数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    蜂鸣器控制 \ 控制开关 1 \ -> \ 0
    should be true    4>=${历史数据数量}>=2    #0是开启，1是关闭蜂鸣器
    ${历史数据数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    蜂鸣器控制 \ 控制开关 0 \ -> \ 1
    should be true    3>=${历史数据数量}>=2
    蜂鸣器关闭

输出干接点强制控制测试
    [Setup]
    连接CSU
    ${历史事件数量}    获取web历史事件数量    ${empty}    ${empty}    所有
    run keyword if    ${历史事件数量}>1900    删除历史记录    操作记录
    ${起始时间}    获取系统时间
    ${强制控制状态}    获取干接点强制控制状态
    run keyword if    ${强制控制状态}!=1    干接点强制控制开启
    ${强制控制状态}    获取干接点强制控制状态
    FOR    ${干接点编号}    IN RANGE    8    1    -1
        ${终止时间1}    获取系统时间
        ${操作记录数量1}    获取web历史事件数量    ${起始时间}    ${终止时间1}    200
        Wait Until Keyword Succeeds    10    2    干接点动作    ${干接点编号}
        ${终止时间2}    获取系统时间
        ${操作记录数量2}    获取web历史事件数量    ${起始时间}    ${终止时间2}    200
        should be true    ${操作记录数量2}==${操作记录数量1}+1
        @{操作记录内容1}    获取web历史事件内容    ${起始时间}    ${终止时间2}    200    1    10
        should contain    ${操作记录内容1}[-1]    设置输出干接点 ${干接点编号} on
        获取控制输出干接点状态    ${干接点编号}    #输出干接点的状态
        Wait Until Keyword Succeeds    10    2    干接点恢复    ${干接点编号}
        ${终止时间2}    获取系统时间
        ${操作记录数量3}    获取web历史事件数量    ${起始时间}    ${终止时间2}    200
        should be true    ${操作记录数量3}==${操作记录数量2}+1
        @{操作记录内容1}    获取web历史事件内容    ${起始时间}    ${终止时间2}    200    1    10
        should contain    ${操作记录内容1}[-1]    设置输出干接点 ${干接点编号} off
        获取控制输出干接点状态    ${干接点编号}    #输出干接点的状态
    END
    ${终止时间3}    获取系统时间
    ${操作记录数量4}    获取web历史事件数量    ${起始时间}    ${终止时间3}    200
    干接点强制控制关闭
    ${强制控制状态}    获取干接点强制控制状态
    ${终止时间4}    获取系统时间
    ${操作记录数量5}    获取web历史事件数量    ${起始时间}    ${终止时间4}    200
    should be true    ${操作记录数量5}==${操作记录数量4}+1
    @{操作记录内容1}    获取web历史事件内容    ${起始时间}    ${终止时间4}    200    1    10
    should contain    ${操作记录内容1}[-1]    设置输出干接点强制控制模式 : 1 -> 0
    [Teardown]    干接点强制控制关闭

系统重启输出干接点退出强制控制测试
    [Setup]
    连接CSU
    ${强制控制状态}    获取干接点强制控制状态
    run keyword if    ${强制控制状态}!=1    干接点强制控制开启
    ${强制控制状态}    获取干接点强制控制状态
    should be true    ${强制控制状态}==1
    系统复位
    sleep    90
    连接CSU
    ${强制控制状态}    获取干接点强制控制状态
    should be true    ${强制控制状态}==0
