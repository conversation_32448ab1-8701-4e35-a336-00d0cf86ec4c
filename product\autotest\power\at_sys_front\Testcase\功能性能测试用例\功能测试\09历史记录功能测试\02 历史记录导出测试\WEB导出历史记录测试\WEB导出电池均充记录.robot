*** Settings ***
Suite Setup       设置历史记录最大条数    his_equ    2    100
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
WEB导出电池均充记录测试
    连接CSU
    ${数据条数1}    ${第一条数据内容1}    ${最新一条数据内容1}    查询指定历史记录    电池均充记录
    ${导出路径}    导出指定历史记录    电池均充记录    ${datapassword}
    ${数据条数2}    ${第一条数据内容2}    ${最新一条数据内容2}    解析指定历史记录文件    电池均充记录    ${导出路径}
    should be equal    ${第一条数据内容1}[0]    ${第一条数据内容2}[0]
    should be equal    ${第一条数据内容1}[1]    ${第一条数据内容2}[1]
    should be equal    ${第一条数据内容1}[2]    ${第一条数据内容2}[2]
    should be equal    ${第一条数据内容1}[3]    ${第一条数据内容2}[3]
    should be equal    ${第一条数据内容1}[4]    ${第一条数据内容2}[4]
    should be equal    ${第一条数据内容1}[5]    ${第一条数据内容2}[5]
    should be equal    ${第一条数据内容1}[6]    ${第一条数据内容2}[6]
    should be equal    ${第一条数据内容1}[7]    ${第一条数据内容2}[7]
    should be equal    ${第一条数据内容1}[8]    ${第一条数据内容2}[8]
    should be equal    ${第一条数据内容1}[9]    ${第一条数据内容2}[9]
    should be equal    ${第一条数据内容1}[10]    ${第一条数据内容2}[10]
    should be equal    ${数据条数1}    ${数据条数2}
    should be equal    ${最新一条数据内容1}[0]    ${最新一条数据内容2}[0]
    should be equal    ${最新一条数据内容1}[1]    ${最新一条数据内容2}[1]
    should be equal    ${最新一条数据内容1}[2]    ${最新一条数据内容2}[2]
    should be equal    ${最新一条数据内容1}[3]    ${最新一条数据内容2}[3]
    should be equal    ${最新一条数据内容1}[4]    ${最新一条数据内容2}[4]
    should be equal    ${最新一条数据内容1}[5]    ${最新一条数据内容2}[5]
    should be equal    ${最新一条数据内容1}[6]    ${最新一条数据内容2}[6]
    should be equal    ${最新一条数据内容1}[7]    ${最新一条数据内容2}[7]
    should be equal    ${最新一条数据内容1}[8]    ${最新一条数据内容2}[8]
    should be equal    ${最新一条数据内容1}[9]    ${最新一条数据内容2}[9]
    should be equal    ${最新一条数据内容1}[10]    ${最新一条数据内容2}[10]

中文界面下WEB导出电池均充记录测试
    电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    市电额定有功功率    2.1
    Wait Until Keyword Succeeds    10    1    设置web参数量    市电降额系数    85
    ${开始数量}    获取web事件记录数量    电池均充记录
    run keyword if    ${开始数量}>990    删除历史记录    电池均充记录
    sleep    30
    ${开始数量}    获取web事件记录数量    电池均充记录
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动均充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    均充
    ${起始时间}    获取系统时间
    ${起始SOC}    获取web实时数据    电池组当前容量比率-1
    ${起始电池电压}    获取web实时数据    电池电压-1
    ${起始电量}    获取web实时数据    电池充电电量-1
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    80
    打开负载输出
    Wait Until Keyword Succeeds    30m    5    信号量数据值小于    电池组当前容量比率-1    100
    ${持续时间}    获取web实时数据    电池状态持续时间
    关闭负载输出
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    ${结束时间}    获取系统时间
    ${结束SOC}    获取web实时数据    电池组当前容量比率-1
    ${结束电池电压}    获取web实时数据    电池电压-1
    ${结束电量}    获取web实时数据    电池充电电量-1
    sleep    30
    ${结束数量}    获取web事件记录数量    电池均充记录
    @{均充记录内容}    获取web事件记录最新一条    电池均充记录
    Comment    @{均充记录内容}    split string    ${记录内容}    ,
    should be true    ${均充记录内容}[0] == ${结束数量}
    should be true    ${结束数量} == ${开始数量} + 1
    should be equal    '${均充记录内容}[4]'    '手动'
    should be equal    '${均充记录内容}[5]'    '手动'
    ${数据条数1}    ${第一条数据内容1}    ${最新一条数据内容1}    查询指定历史记录    电池均充记录
    ${导出路径}    导出指定历史记录    电池均充记录    ${datapassword}
    ${记录列表}    读CSV文档    ${测试用例地址}/file/download/${导出路径}/batt_equ_rec.csv
    log    ${记录列表}[0]
    @{记录列表new}    split string    ${记录列表}[0]    ,
    should be equal    ${记录列表new}[0]    产生时间
    should be equal    ${记录列表new}[1]    恢复时间
    should be equal    ${记录列表new}[3]    电池组:记录开始原因
    ${数据条数2}    ${第一条数据内容2}    ${最新一条数据内容2}    解析指定历史记录文件    电池均充记录    ${导出路径}
    should be equal    ${最新一条数据内容2}[3]    手动
    should be equal    ${最新一条数据内容2}[4]    手动

英文界面下WEB导出电池均充记录测试
    电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    市电额定有功功率    2.1
    Wait Until Keyword Succeeds    10    1    设置web参数量    市电降额系数    85
    ${开始数量}    获取web事件记录数量    电池均充记录
    run keyword if    ${开始数量}>990    删除历史记录    电池均充记录
    sleep    30
    ${开始数量}    获取web事件记录数量    电池均充记录
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动均充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    均充
    ${起始时间}    获取系统时间
    ${起始SOC}    获取web实时数据    电池组当前容量比率-1
    ${起始电池电压}    获取web实时数据    电池电压-1
    ${起始电量}    获取web实时数据    电池充电电量-1
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    80
    打开负载输出
    Wait Until Keyword Succeeds    30m    5    信号量数据值小于    电池组当前容量比率-1    100
    ${持续时间}    获取web实时数据    电池状态持续时间
    关闭负载输出
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    ${结束时间}    获取系统时间
    ${结束SOC}    获取web实时数据    电池组当前容量比率-1
    ${结束电池电压}    获取web实时数据    电池电压-1
    ${结束电量}    获取web实时数据    电池充电电量-1
    sleep    30
    ${结束数量}    获取web事件记录数量    电池均充记录
    @{均充记录内容}    获取web事件记录最新一条    电池均充记录
    should be true    ${均充记录内容}[0] == ${结束数量}
    should be true    ${结束数量} == ${开始数量} + 1
    should be equal    '${均充记录内容}[4]'    '手动'
    should be equal    '${均充记录内容}[5]'    '手动'
    设置语言    EN
    连接CSU
    ${数据条数1}    ${第一条数据内容1}    ${最新一条数据内容1}    查询指定历史记录    电池均充记录
    ${导出路径}    导出指定历史记录    电池均充记录    ${datapassword}
    ${记录列表}    读CSV文档    ${测试用例地址}/file/download/${导出路径}/batt_equ_rec.csv
    log    ${记录列表}[0]
    @{记录列表new}    split string    ${记录列表}[0]    ,
    should be equal    ${记录列表new}[0]    start_time
    should be equal    ${记录列表new}[1]    end_time
    should be equal    ${记录列表new}[3]    Battery Group:Record Start Cause
    ${数据条数2}    ${第一条数据内容2}    ${最新一条数据内容2}    解析指定历史记录文件    电池均充记录    ${导出路径}
    should be equal    ${最新一条数据内容2}[3]    Manual
    should be equal    ${最新一条数据内容2}[4]    Manual
    [Teardown]    run keywords    设置语言    CN
    ...    AND    连接CSU
