*** Settings ***
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp_test关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/Web关键字_V30.robot
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
snmp_0002_aCEM1alarm_level√
    FOR    ${交流电表序号}    IN RANGE    1    4
        ${比较结果}    对比告警级别_V2C    aCEM${交流电表序号}alarm
        should be true    ${比较结果}
    END

snmp_0004_aCEM1alarm_level_writeX
    [Documentation]    SnmpKeyword.py 1315 dict_SID= 0x1e001010010001
    ...    ==============================交流电表回路1电压_0========================================================
    ...    SnmpKeyword.py 1334 alm_level_conv= None
    ...    20210205 20:15:06.192 : \ FAIL : AttributeError: 'NoneType' object has no attribute 'items'
    Comment    ${比较结果}    批量修改告警级别_V2C    cSU1alarm
    Comment    should be true    ${比较结果}
    Comment    : FOR    ${交流电表序号}    IN RANGE    1    4
    Comment    ${比较结果}    批量设置四种/五种告警级别    aCEM${交流电表序号}alarm
    Comment    should be true    ${比较结果}
    Comment    ${比较结果}    批量设置四种/五种告警级别    aCEM1alarm
    Comment    should be true    ${比较结果}
    Comment    ${比较结果}    批量设置四种/五种告警级别    aCEM2alarm
    Comment    should be true    ${比较结果}
    Comment    ${比较结果}    批量设置四种/五种告警级别    aCEM3alarm
    Comment    should be true    ${比较结果}
    ###
    FOR    ${交流电表序号}    IN RANGE    1    4
        ${比较结果}    批量设置四种/五种告警级别    aCEM${交流电表序号}alarm
        should be true    ${比较结果}
    END

snmp_0006_aCEM1alarm_relay√
    FOR    ${交流电表序号}    IN RANGE    1    4
        ${比较结果}    对比告警干接点_V2C    aCEM${交流电表序号}alarm
        should be true    ${比较结果}
    END

snmp_0008_aCEM1alarm_relay_write√
    FOR    ${交流电表序号}    IN RANGE    1    4
        ${比较结果}    批量修改告警干接点_V2C    aCEM${交流电表序号}alarm
        should be true    ${比较结果}
    END
