*** Settings ***
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_获取系统资源配置
    [Documentation]    \#只验证获得的资源在固有资源中，未对是否是有效资源进行判断
    ${能源网管数据}    能源网管协议_获取数据    获取系统资源配置    ${SSH}
    ${数据对比结果}    能源网管协议_对比系统资源配置    ${能源网管数据}    获取系统资源配置
    Should Be True    ${数据对比结果}
