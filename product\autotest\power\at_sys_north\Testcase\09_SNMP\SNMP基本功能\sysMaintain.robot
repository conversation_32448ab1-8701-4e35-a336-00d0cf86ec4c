*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
获取时间
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${SNMP数据}    获取SNMP数据_批量    systemTimeSync
    ${web数据}    获取系统时间
    should be equal as strings    ${SNMP数据}    ${web数据}
    ${设置结果}    SnmpKeyword.set_value_to_oid    .1.3.6.1.4.1.3902.2800.4.200.2    2021-09-09 09:09:09    ${g_mib_oid}    idx=(0,)
    should be true    ${设置结果}
    ${web数据}    获取系统时间
    ${同步时间}    同步系统时间
    断开连接SNMP

获取输出干接点
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${SNMP数据}    获取SNMP数据_批量    dryContactEntry
    断开连接SNMP

远程控制复位
    Comment    进行SNMP_V2/V3连接    ${snmp连接方式}
    Comment    断开连接SNMP
    Comment    ${SNMP数据}    获取SNMP数据_批量    dryContactEntry
    Comment    ${比较结果}    对比数据_V2C    dryContactEntry
    Comment    should be true    ${比较结果}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${设置结果}    SnmpKeyword.set_value_to_oid    .1.3.6.1.4.1.3902.2800.4.200.1    0    ${g_mib_oid}    idx=(0,)
    Comment    should be true    ${设置结果}
    sleep    3m
    连接CSU
    ${是否复位}    Set Variable    False
    FOR    ${page}    IN RANGE    1    4
        @{历史事件内容}    获取web历史事件内容    ${empty}    ${empty}    所有    ${page}    100
        FOR    ${i}    IN    @{历史事件内容}
            ${是否复位}    Evaluate    "远程控制复位" in "${i}"
            Exit For Loop If    ${是否复位}==True
        END
        Exit For Loop If    ${是否复位}==True
    END
    should be true    ${是否复位}
    测试台上下电操作  ${测试台编号}  OFF
    sleep   5
    测试台上下电操作  ${测试台编号}  ON

