*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
用户参数复位保存测试
    [Setup]
    连接CSU
    ${CPU占用率高阈值获取}    获取web参数量    CPU利用率高阈值
    ${CPU占用率高阈值获取new}    evaluate    ${CPU占用率高阈值获取}+1
    Wait Until Keyword Succeeds    60    2    设置web参数量    CPU利用率高阈值    ${CPU占用率高阈值获取new}
    ${系统过载告警阈值获取}    获取web参数量    系统过载告警阈值
    ${系统过载告警阈值获取new}    evaluate    ${系统过载告警阈值获取}+1
    Wait Until Keyword Succeeds    60    2    设置web参数量    系统过载告警阈值    ${系统过载告警阈值获取new}
    ${获取值}    获取web参数量    交流输入场景
    run keyword if    '${获取值}' != '油电'    Wait Until Keyword Succeeds    10    2    设置web参数量    交流输入场景    油电
    run keyword if    '${获取值}' == '油电'    Wait Until Keyword Succeeds    10    2    设置web参数量    市电额定有功功率    2
    ${导出路径1}    导出参数文件    ${datapassword}
    系统复位
    实时告警刷新完成
    ${导出路径2}    导出参数文件    ${datapassword}
    ${比对结果}    比对两个导出文档    ${导出路径1}//parameter.csv    ${导出路径2}//parameter.csv
    should be true    ${比对结果}
    删除文件夹以及文件夹下所有文件    download//${导出路径1}
    删除文件    download//${导出路径1}.zip
    删除文件夹以及文件夹下所有文件    download//${导出路径2}
    删除文件    download//${导出路径2}.zip
    恢复默认值并重新登录    #SUPERMAN用户支持用户参数和站点配置参数恢复，
    sleep    1m
    系统复位
    sleep    4m

告警属性参数复位保存测试
    [Setup]    测试用例前置条件
    连接CSU    #要先恢复所有参数
    Wait Until Keyword Succeeds    60    2    设置web参数量    <<禁止所有告警~0x1001030010001>>    次要
    ${告警级别获取}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    60    2    设置web参数量    系统过载告警    次要
    ${告警级别获取}    获取web参数量    系统过载告警
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    60    2    设置web参数量    交流停电    次要
    ${告警级别获取}    获取web参数量    交流停电
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    60    2    设置web参数量    市电频率高    次要
    ${告警级别获取}    获取web参数量    市电频率高
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    60    2    设置web参数量    整流器告警    次要
    ${告警级别获取}    获取web参数量    整流器告警
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    60    2    设置web参数量    多个整流器模块告警    次要
    ${告警级别获取}    获取web参数量    多个整流器模块告警
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    60    2    设置web参数量    一次下电告警    次要
    ${告警级别获取}    获取web参数量    一次下电告警
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    60    2    设置web参数量    电池温度高    次要
    ${告警级别获取}    获取web参数量    电池温度高
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    60    2    设置web参数量    直流防雷器异常    次要
    ${告警级别获取}    获取web参数量    直流防雷器异常
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    60    2    设置web参数量    烟雾告警    次要
    ${告警级别获取}    获取web参数量    烟雾告警
    should be true    '${告警级别获取}'=='次要'
    sleep    10
    ${导出路径1}    导出参数文件    ${datapassword}
    系统复位
    实时告警刷新完成
    ${导出路径2}    导出参数文件    ${datapassword}
    ${比对结果}    比对两个导出文档    ${导出路径1}//alarm_attr_para.csv    ${导出路径2}//alarm_attr_para.csv
    should be true    ${比对结果}
    删除文件夹以及文件夹下所有文件    download//${导出路径1}
    删除文件    download//${导出路径1}.zip
    删除文件夹以及文件夹下所有文件    download//${导出路径2}
    删除文件    download//${导出路径2}.zip
    恢复默认值并重新登录    #SUPERMAN用户支持用户参数和站点配置参数恢复，
    sleep    1m
    系统复位
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流输入场景    市电
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    纯铅酸

显示属性参数复位保存测试
    [Setup]    测试用例前置条件
    连接CSU
    ${channel_config}    显示属性配置    CPU利用率    模拟量    ON    ON    #初始值为OFF，不显示
    @{channel_config}    获取显示属性配置    CPU利用率    模拟量
    should be equal    ${channel_config}[0]    ON
    should be equal    ${channel_config}[1]    ON
    ${channel_config}    显示属性配置    <<禁止所有告警~0x1001040010001>>    控制量    OFF    OFF
    @{channel_config}    获取显示属性配置    <<禁止所有告警~0x1001040010001>>    控制量
    should be equal    ${channel_config}[0]    OFF
    should be equal    ${channel_config}[1]    OFF
    ${channel_config}    显示属性配置    站点名称    参数量    OFF    OFF
    @{channel_config}    获取显示属性配置    站点名称    参数量
    should be equal    ${channel_config}[0]    OFF
    should be equal    ${channel_config}[1]    OFF
    ${channel_config}    显示属性配置    相电压UL1    模拟量    OFF    OFF
    @{channel_config}    获取显示属性配置    相电压UL1    模拟量
    should be equal    ${channel_config}[0]    OFF
    should be equal    ${channel_config}[1]    OFF
    ${channel_config}    显示属性配置    交流防雷器状态    数字量    ON    ON
    @{channel_config}    获取显示属性配置    交流防雷器状态    数字量
    should be equal    ${channel_config}[0]    ON
    should be equal    ${channel_config}[1]    ON
    ${channel_config}    显示属性配置    市电输入状态    数字量    OFF    OFF
    @{channel_config}    获取显示属性配置    市电输入状态    数字量
    should be equal    ${channel_config}[0]    OFF
    should be equal    ${channel_config}[1]    OFF
    ${channel_config}    显示属性配置    整流器输出电压    模拟量    OFF    OFF
    @{channel_config}    获取显示属性配置    整流器输出电压    模拟量
    should be equal    ${channel_config}[0]    OFF
    should be equal    ${channel_config}[1]    OFF
    ${channel_config}    显示属性配置    电池组总电流    模拟量    OFF    OFF
    @{channel_config}    获取显示属性配置    电池组总电流    模拟量
    should be equal    ${channel_config}[0]    OFF
    should be equal    ${channel_config}[1]    OFF
    ${channel_config}    显示属性配置    直流电压    模拟量    OFF    OFF
    @{channel_config}    获取显示属性配置    直流电压    模拟量
    should be equal    ${channel_config}[0]    OFF
    should be equal    ${channel_config}[1]    OFF
    ${导出路径1}    导出参数文件    ${datapassword}
    系统复位
    实时告警刷新完成
    ${导出路径2}    导出参数文件    ${datapassword}
    ${比对结果}    比对两个导出文档    ${导出路径1}//show_attr_para.csv    ${导出路径2}//show_attr_para.csv
    should be true    ${比对结果}
    删除文件夹以及文件夹下所有文件    download//${导出路径1}
    删除文件    download//${导出路径1}.zip
    删除文件夹以及文件夹下所有文件    download//${导出路径2}
    删除文件    download//${导出路径2}.zip
    恢复默认值并重新登录    #SUPERMAN用户支持用户参数和站点配置参数恢复，
    sleep    1m
    系统复位
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流输入场景    市电
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    纯铅酸
