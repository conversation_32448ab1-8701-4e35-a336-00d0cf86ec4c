*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
1363_0002_1PU告警设置
    [Documentation]    ${设置结果} set_data ${协议数据} ${表单名称} ${命令名称} var_name=${参数名} val=${设置值} cmd_group=${屏号} ver=${g_ver} port=${SERIAL_PORT}
    ...
    ...
    ...
    ...
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${屏号}=None | ${g_ver}=
    ...
    ...
    ...    0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning
    [Setup]    判断web参数是否存在    PU告警
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置PU段告警级别~CID1=D0H>>    <<设置PU段告警级别~CID2=82H>>    PU告警    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取PU段告警级别~CID1=D0H>>    <<获取PU段告警级别~CID2=81H>>    PU告警    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    PU告警    ${VAR}
        ${交流电压低告警级别}    获取web参数量    PU告警
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    设置web参数量    交流电压低_1    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    END
    [Teardown]    设置web参数量    PU告警    主要

1363_0004_2PU故障设置
    [Setup]    判断web参数是否存在    PU故障
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置PU段告警级别~CID1=D0H>>    <<设置PU段告警级别~CID2=82H>>    PU模块故障    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取PU段告警级别~CID1=D0H>>    <<获取PU段告警级别~CID2=81H>>    PU模块故障    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    PU故障    ${VAR}
        ${交流电压低告警级别}    获取web参数量    PU故障
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    设置web参数量    交流电压低_1    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    END
    [Teardown]    设置web参数量    PU故障    主要

1363_0006_3PU通讯中断设置
    [Setup]    判断web参数是否存在    PU通讯中断
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置PU段告警级别~CID1=D0H>>    <<设置PU段告警级别~CID2=82H>>    PU通讯断    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取PU段告警级别~CID1=D0H>>    <<获取PU段告警级别~CID2=81H>>    PU通讯断    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    PU通讯中断    ${VAR}
        ${交流电压低告警级别}    获取web参数量    PU通讯中断
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    设置web参数量    交流电压低_1    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    END
    [Teardown]    设置web参数量    PU通讯中断    主要
