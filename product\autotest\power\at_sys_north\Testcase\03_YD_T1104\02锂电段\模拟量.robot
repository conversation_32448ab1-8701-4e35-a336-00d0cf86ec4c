*** Settings ***
Suite Setup
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
# 1104批量获取FBBMS模拟量
#     [Setup]
#     写入CSV文档    FB100B3模拟量获取测试    信号名称    信号值    结果    备注
#     连接CSU
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=FBBMS
#     ${排除列表}    create list
#     P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除模拟量信号}    ${排除列表}    2    1    0    2
#     ${协议数据}    1104获取数据    ${g_prtcl_1104}    <<获取锂电池段模拟量~CID1=4AH>>    <<获取锂电池段模拟量~CID2=42H>>    1    ${g_ver_1104}
#     ${web数据}    通过1104_data获取web数据    ${协议数据}    True    1
#     ${1104待测}    1104_获取1104/1363指定设备指定信号量    ${协议数据}    ${web数据}    ${列表1}    1
#     FOR    ${i}    IN    @{1104待测}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${1104协议名称}    Get From Dictionary    ${i}    1104_name
#         ${缺省值}    获取web参数上下限范围    ${信号名称}
#         Run Keyword And Continue On Failure    南向子设备模拟量获取值封装判断结果    1104    smartli    ${信号名称}    只读    ${缺省值}[1]    FB100B3模拟量获取测试    null    null    null    <<获取锂电池段模拟量~CID1=4AH>>    <<获取锂电池段模拟量~CID2=42H>>    ${1104协议名称}    1
#         Run Keyword And Continue On Failure    南向子设备模拟量获取值封装判断结果    1104    smartli    ${信号名称}    只读    ${缺省值}[2]    FB100B3模拟量获取测试    null    null    null    <<获取锂电池段模拟量~CID1=4AH>>    <<获取锂电池段模拟量~CID2=42H>>    ${1104协议名称}    1
#         Run Keyword IF    ${缺省值}[1]!=${缺省值}[0] and ${缺省值}[2]!=${缺省值}[0]    Run Keyword And Continue On Failure    南向子设备模拟量获取值封装判断结果    1104    smartli    ${信号名称}    只读    ${缺省值}[0]    FB100B3模拟量获取测试    null    null    null    <<获取锂电池段模拟量~CID1=4AH>>    <<获取锂电池段模拟量~CID2=42H>>    ${1104协议名称}
#         ...    1
#     END
1104批量获取FBBMS模拟量
    [Setup]
    写入CSV文档    FB100B3模拟量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=FBBMS
    ${排除列表}    create list
    P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除模拟量信号}    ${排除列表}    2    1    0    2
    ${协议数据}    1104获取数据    ${g_prtcl_1104}    <<获取锂电池段模拟量~CID1=4AH>>    <<获取锂电池段模拟量~CID2=42H>>    1    ${g_ver_1104}
    ${web数据}    通过1104_data获取web数据    ${协议数据}    True    1
    ${1104待测}    1104_获取1104/1363指定设备指定信号量    ${协议数据}    ${web数据}    ${列表1}    1
    # FOR    ${i}    IN    @{1104待测}
    #     ${信号名称}    Get From Dictionary    ${i}    signal_name
    #     ${1104协议名称}    Get From Dictionary    ${i}    1104_name
    #     ${缺省值}    获取web参数上下限范围    ${信号名称}
    #     Run Keyword And Continue On Failure    南向子设备模拟量获取值封装判断结果    1104    smartli    ${信号名称}    只读    ${缺省值}[1]    FB100B3模拟量获取测试    null    null    null    <<获取锂电池段模拟量~CID1=4AH>>    <<获取锂电池段模拟量~CID2=42H>>    ${1104协议名称}    1
    #     Run Keyword And Continue On Failure    南向子设备模拟量获取值封装判断结果    1104    smartli    ${信号名称}    只读    ${缺省值}[2]    FB100B3模拟量获取测试    null    null    null    <<获取锂电池段模拟量~CID1=4AH>>    <<获取锂电池段模拟量~CID2=42H>>    ${1104协议名称}    1
    #     Run Keyword IF    ${缺省值}[1]!=${缺省值}[0] and ${缺省值}[2]!=${缺省值}[0]    Run Keyword And Continue On Failure    南向子设备模拟量获取值封装判断结果    1104    smartli    ${信号名称}    只读    ${缺省值}[0]    FB100B3模拟量获取测试    null    null    null    <<获取锂电池段模拟量~CID1=4AH>>    <<获取锂电池段模拟量~CID2=42H>>    ${1104协议名称}
    #     ...    1
    # END

    ${缺省值列表1}    获取缺省值列表    ${1104待测}    1    1104
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    1104    smartli    只读    ${缺省值列表1}    FB100B3模拟量获取测试    null    <<获取整流段模拟量~CID1=4AH>>    <<获取整流段模拟量~CID2=42H>>    1
    ${缺省值列表2}    获取缺省值列表    ${1104待测}    2    1104
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    1104    smartli    只读    ${缺省值列表2}    FB100B3模拟量获取测试    null    <<获取整流段模拟量~CID1=4AH>>    <<获取整流段模拟量~CID2=42H>>    1
    ${缺省值列表0}    获取缺省值列表    ${1104待测}    0    1104
    # ${缺省值列表0}    1104检查缺省值列表    ${缺省值列表0}    ${缺省值列表1}    ${缺省值列表2} 
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    1104    smartli    只读    ${缺省值列表0}    FB100B3模拟量获取测试    null    <<获取整流段数字量~CID1=4AH>>    <<获取整流段数字量~CID2=42H>>    1
