*** Settings ***
Suite Setup       主动告警测试前置条件    ${CSU_role}
Suite Teardown    设置web设备参数量为默认值    CSU主动告警使能
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_油机输出欠压
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    设置子工具值    oileng    all    告警    备用8    256
    ${级别设置值}    获取web参数量    油机输出欠压
    run keyword if    '${级别设置值}'=='屏蔽'    设置web设备参数量为默认值    油机输出欠压
    ${告警级别取值约定dict}    获取web参数的取值约定    油机输出欠压
    wait until keyword succeeds    5m    5    查询指定告警信息    油机输出欠压-1
    Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client'    主动告警上送判断2    power_sm    油机_1    油机输出欠压
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机输出欠压-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${级别设置值}    获取web参数量    油机输出欠压
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    设置子工具值    oileng    all    告警    备用8    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机输出欠压
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机输出欠压-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    设置子工具值    oileng    all    告警    备用8    512
    wait until keyword succeeds    5m    5    查询指定告警信息    油机输出欠压-1
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机输出欠压-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${级别设置值}    获取web参数量    油机输出欠压
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    设置子工具值    oileng    all    告警    备用8    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机输出欠压
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机输出欠压-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
    ...    AND    设置子工具值    oileng    all    告警    备用8    0

power_sm_0002_油机输出过压
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    设置子工具值    oileng    all    告警    备用8    1024
    ${级别设置值}    获取web参数量    油机输出过压
    run keyword if    '${级别设置值}'=='屏蔽'    设置web设备参数量为默认值    油机输出过压
    ${告警级别取值约定dict}    获取web参数的取值约定    油机输出过压
    wait until keyword succeeds    10m    5    查询指定告警信息    油机输出过压-1
    Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client'    主动告警上送判断2    power_sm    油机_1    油机输出过压
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机输出过压-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${级别设置值}    获取web参数量    油机输出过压
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    设置子工具值    oileng    all    告警    备用8    0
    wait until keyword succeeds    10m    5    查询指定告警信息不为    油机输出过压
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机输出过压-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    设置子工具值    oileng    all    告警    备用8    2048
    wait until keyword succeeds    10m    5    查询指定告警信息    油机输出过压-1
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机输出过压-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${级别设置值}    获取web参数量    油机输出过压
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    设置子工具值    oileng    all    告警    备用8    0
    wait until keyword succeeds    10m    5    查询指定告警信息不为    油机输出过压
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机输出过压-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
    ...    AND    设置子工具值    oileng    all    告警    备用8    0

power_sm_0003_油机输出过流
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    设置子工具值    oileng    all    告警    备用7    1
    ${级别设置值}    获取web参数量    油机输出过流
    run keyword if    '${级别设置值}'=='屏蔽'    设置web设备参数量为默认值    油机输出过流
    ${告警级别取值约定dict}    获取web参数的取值约定    油机输出过流
    wait until keyword succeeds    5m    5    查询指定告警信息    油机输出过流-1
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机输出过流-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${级别设置值}    获取web参数量    油机输出过流
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    设置子工具值    oileng    all    告警    备用7    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机输出过流
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机输出过流-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    设置子工具值    oileng    all    告警    备用7    2
    wait until keyword succeeds    5m    5    查询指定告警信息    油机输出过流-1
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机输出过流-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${级别设置值}    获取web参数量    油机输出过流
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    设置子工具值    oileng    all    告警    备用7    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机输出过流
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机输出过流-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
    ...    AND    设置子工具值    oileng    all    告警    备用7    0

power_sm_0004_油机输出过载
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    设置子工具值    oileng    all    告警    备用7    4
    ${级别设置值}    获取web参数量    油机输出过载
    run keyword if    '${级别设置值}'=='屏蔽'    设置web设备参数量为默认值    油机输出过载
    ${告警级别取值约定dict}    获取web参数的取值约定    油机输出过载
    wait until keyword succeeds    5m    5    查询指定告警信息    油机输出过载-1
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机输出过载-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${级别设置值}    获取web参数量    油机输出过载
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    设置子工具值    oileng    all    告警    备用7    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机输出过载
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机输出过载-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    设置子工具值    oileng    all    告警    备用7    8
    wait until keyword succeeds    5m    5    查询指定告警信息    油机输出过载-1
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机输出过载-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${级别设置值}    获取web参数量    油机输出过载
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    设置子工具值    oileng    all    告警    备用7    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机输出过载
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机输出过载-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
    ...    AND    设置子工具值    oileng    all    告警    备用7    0

power_sm_0005_油机输出频率低
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    设置子工具值    oileng    all    告警    备用8    4096
    ${级别设置值}    获取web参数量    油机输出频率低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web设备参数量为默认值    油机输出频率低
    ${告警级别取值约定dict}    获取web参数的取值约定    油机输出频率低
    wait until keyword succeeds    5m    5    查询指定告警信息    油机输出频率低-1
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机输出频率低-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${级别设置值}    获取web参数量    油机输出频率低
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    设置子工具值    oileng    all    告警    备用8    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机输出频率低
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机输出频率低-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    设置子工具值    oileng    all    告警    备用8    8192
    wait until keyword succeeds    5m    5    查询指定告警信息    油机输出频率低-1
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机输出频率低-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${级别设置值}    获取web参数量    油机输出频率低
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    设置子工具值    oileng    all    告警    备用8    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机输出频率低
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机输出频率低-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
    ...    AND    设置子工具值    oileng    all    告警    备用8    0

power_sm_0006_油机输出频率高
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    设置子工具值    oileng    all    告警    备用8    16384
    ${级别设置值}    获取web参数量    油机输出频率高
    run keyword if    '${级别设置值}'=='屏蔽'    设置web设备参数量为默认值    油机输出频率高
    ${告警级别取值约定dict}    获取web参数的取值约定    油机输出频率高
    wait until keyword succeeds    5m    5    查询指定告警信息    油机输出频率高-1
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机输出频率高-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${级别设置值}    获取web参数量    油机输出频率高
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    设置子工具值    oileng    all    告警    备用8    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机输出频率高
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机输出频率高-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    设置子工具值    oileng    all    告警    备用8    32768
    wait until keyword succeeds    5m    5    查询指定告警信息    油机输出频率高-1
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机输出频率高-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${级别设置值}    获取web参数量    油机输出频率高
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    设置子工具值    oileng    all    告警    备用8    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机输出频率高
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机输出频率高-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
    ...    AND    设置子工具值    oileng    all    告警    备用8    0

power_sm_0007_油机低速告警
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    设置子工具值    oileng    all    告警    备用9    16384
    ${级别设置值}    获取web参数量    油机低速告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web设备参数量为默认值    油机低速告警
    ${告警级别取值约定dict}    获取web参数的取值约定    油机低速告警
    wait until keyword succeeds    5m    5    查询指定告警信息    油机低速告警-1
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机低速告警-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${级别设置值}    获取web参数量    油机低速告警
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    设置子工具值    oileng    all    告警    备用9    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机低速告警
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机低速告警-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    设置子工具值    oileng    all    告警    备用9    32768
    wait until keyword succeeds    5m    5    查询指定告警信息    油机低速告警-1
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机低速告警-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${级别设置值}    获取web参数量    油机低速告警
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    设置子工具值    oileng    all    告警    备用9    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机低速告警
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机低速告警-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
    ...    AND    设置子工具值    oileng    all    告警    备用9    0

power_sm_0008_油机超速告警
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    设置子工具值    oileng    all    告警    备用8    1
    ${级别设置值}    获取web参数量    油机超速告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web设备参数量为默认值    油机超速告警
    ${告警级别取值约定dict}    获取web参数的取值约定    油机超速告警
    wait until keyword succeeds    5m    5    查询指定告警信息    油机超速告警-1
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机超速告警-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${级别设置值}    获取web参数量    油机超速告警
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    设置子工具值    oileng    all    告警    备用8    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机超速告警
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机超速告警-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    设置子工具值    oileng    all    告警    备用8    2
    wait until keyword succeeds    5m    5    查询指定告警信息    油机超速告警-1
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机超速告警-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${级别设置值}    获取web参数量    油机超速告警
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    设置子工具值    oileng    all    告警    备用8    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机超速告警
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机超速告警-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
    ...    AND    设置子工具值    oileng    all    告警    备用8    0

power_sm_0009_油机油压低告警
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    设置子工具值    oileng    all    告警    备用8    4
    ${级别设置值}    获取web参数量    油机油压低告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web设备参数量为默认值    油机油压低告警
    ${告警级别取值约定dict}    获取web参数的取值约定    油机油压低告警
    wait until keyword succeeds    5m    5    查询指定告警信息    油机油压低告警-1
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机油压低告警-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${级别设置值}    获取web参数量    油机油压低告警
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    设置子工具值    oileng    all    告警    备用8    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机油压低告警
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机油压低告警-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    设置子工具值    oileng    all    告警    备用8    8
    wait until keyword succeeds    5m    5    查询指定告警信息    油机油压低告警-1
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机油压低告警-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${级别设置值}    获取web参数量    油机油压低告警
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    设置子工具值    oileng    all    告警    备用8    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机油压低告警
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机油压低告警-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
    ...    AND    设置子工具值    oileng    all    告警    备用8    0

power_sm_0010_油机电池欠压告警
    连接CSU
    设置子工具值    oileng    all    告警    备用9    8192
    ${级别设置值}    获取web参数量    油机电池欠压告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web设备参数量为默认值    油机电池欠压告警
    ${告警级别取值约定dict}    获取web参数的取值约定    油机电池欠压告警
    wait until keyword succeeds    5m    5    查询指定告警信息    油机电池欠压告警-1
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机电池欠压告警-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${级别设置值}    获取web参数量    油机电池欠压告警
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    设置子工具值    oileng    all    告警    备用9    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机电池欠压告警
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机电池欠压告警-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
    ...    AND    设置子工具值    oileng    all    告警    备用9    0

power_sm_0011_油机电池过压告警
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    ${级别设置值}    获取web参数量    油机电池过压告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web设备参数量为默认值    油机电池过压告警
    ${告警级别取值约定dict}    获取web参数的取值约定    油机电池过压告警
    设置子工具值    oileng    all    告警    备用9    4096
    wait until keyword succeeds    5m    5    查询指定告警信息    油机电池过压告警-1
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机电池过压告警-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${级别设置值}    获取web参数量    油机电池过压告警
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    设置子工具值    oileng    all    告警    备用9    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机电池过压告警
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机电池过压告警-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
    ...    AND    设置子工具值    oileng    all    告警    备用9    0

power_sm_0012_油机充电失败告警
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    ${级别设置值}    获取web参数量    油机充电失败告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web设备参数量为默认值    油机充电失败告警
    ${告警级别取值约定dict}    获取web参数的取值约定    油机充电失败告警
    设置子工具值    oileng    all    告警    备用9    2048
    wait until keyword succeeds    5m    5    查询指定告警信息    油机充电失败告警-1
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机充电失败告警-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${级别设置值}    获取web参数量    油机充电失败告警
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    设置子工具值    oileng    all    告警    备用9    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机充电失败告警
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机充电失败告警-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
    ...    AND    设置子工具值    oileng    all    告警    备用9    0

power_sm_0013_油机发动机高温告警
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    ${级别设置值}    获取web参数量    油机发动机高温告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web设备参数量为默认值    油机发动机高温告警
    ${告警级别取值约定dict}    获取web参数的取值约定    油机发动机高温告警
    设置子工具值    oileng    all    告警    备用8    16
    wait until keyword succeeds    5m    5    查询指定告警信息    油机发动机高温告警-1
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机发动机高温告警-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${级别设置值}    获取web参数量    油机发动机高温告警
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    设置子工具值    oileng    all    告警    备用8    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机发动机高温告警
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机发动机高温告警-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    设置子工具值    oileng    all    告警    备用8    32
    wait until keyword succeeds    5m    5    查询指定告警信息    油机发动机高温告警-1
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机发动机高温告警-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${级别设置值}    获取web参数量    油机发动机高温告警
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    设置子工具值    oileng    all    告警    备用8    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机发动机高温告警
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机发动机高温告警-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
    ...    AND    设置子工具值    oileng    all    告警    备用8    0

power_sm_0014_油机启动失败告警
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    ${级别设置值}    获取web参数量    油机启动失败告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web设备参数量为默认值    油机启动失败告警
    ${告警级别取值约定dict}    获取web参数的取值约定    油机启动失败告警
    设置子工具值    oileng    all    告警    备用9    4
    wait until keyword succeeds    5m    5    查询指定告警信息    油机启动失败告警-1
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机启动失败告警-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${级别设置值}    获取web参数量    油机启动失败告警
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    设置子工具值    oileng    all    告警    备用9    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机启动失败告警
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机启动失败告警-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
    ...    AND    设置子工具值    oileng    all    告警    备用9    0

power_sm_0015_油机停机失败告警
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    ${级别设置值}    获取web参数量    油机停机失败告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web设备参数量为默认值    油机停机失败告警
    ${告警级别取值约定dict}    获取web参数的取值约定    油机停机失败告警
    设置子工具值    oileng    all    告警    备用9    8
    wait until keyword succeeds    5m    5    查询指定告警信息    油机停机失败告警-1
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机停机失败告警-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${级别设置值}    获取web参数量    油机停机失败告警
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    设置子工具值    oileng    all    告警    备用9    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机停机失败告警
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机停机失败告警-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
    ...    AND    设置子工具值    oileng    all    告警    备用9    0

power_sm_0016_油机油位高告警
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    ${级别设置值}    获取web参数量    油机油位高告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web设备参数量为默认值    油机油位高告警
    ${告警级别取值约定dict}    获取web参数的取值约定    油机油位高告警
    设置子工具值    oileng    all    告警    备用3    64
    wait until keyword succeeds    5m    5    查询指定告警信息    油机油位高告警-1
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机油位高告警-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${级别设置值}    获取web参数量    油机油位高告警
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    设置子工具值    oileng    all    告警    备用3    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机油位高告警
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机油位高告警-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    设置子工具值    oileng    all    告警    备用3    128
    wait until keyword succeeds    5m    5    查询指定告警信息    油机油位高告警-1
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机油位高告警-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${级别设置值}    获取web参数量    油机油位高告警
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    设置子工具值    oileng    all    告警    备用3    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机油位高告警
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机油位高告警-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
    ...    AND    设置子工具值    oileng    all    告警    备用3    0

power_sm_0017_油压传感器开路告警
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    ${级别设置值}    获取web参数量    油压传感器开路告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web设备参数量为默认值    油压传感器开路告警
    ${告警级别取值约定dict}    获取web参数的取值约定    油压传感器开路告警
    设置子工具值    oileng    all    告警    备用7    4096
    wait until keyword succeeds    5m    5    查询指定告警信息    油压传感器开路告警-1
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油压传感器开路告警-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${级别设置值}    获取web参数量    油压传感器开路告警
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    设置子工具值    oileng    all    告警    备用7    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油压传感器开路告警
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油压传感器开路告警-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
    ...    AND    设置子工具值    oileng    all    告警    备用7    0

power_sm_0018_油机合闸失败
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    ${级别设置值}    获取web参数量    油机合闸失败
    run keyword if    '${级别设置值}'=='屏蔽'    设置web设备参数量为默认值    油机合闸失败
    ${告警级别取值约定dict}    获取web参数的取值约定    油机合闸失败
    设置子工具值    oileng    all    告警    备用9    2
    wait until keyword succeeds    5m    5    查询指定告警信息    油机合闸失败-1
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机合闸失败-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${级别设置值}    获取web参数量    油机合闸失败
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    设置子工具值    oileng    all    告警    备用9    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机合闸失败
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机合闸失败-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
    ...    AND    设置子工具值    oileng    all    告警    备用9    0

power_sm_0019_油机分闸失败
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    ${级别设置值}    获取web参数量    油机分闸失败
    run keyword if    '${级别设置值}'=='屏蔽'    设置web设备参数量为默认值    油机分闸失败
    ${告警级别取值约定dict}    获取web参数的取值约定    油机分闸失败
    设置子工具值    oileng    all    告警    预报警代码    28
    设置子工具值    oileng    all    告警    报警代码    28
    wait until keyword succeeds    5m    5    查询指定告警信息    油机分闸失败-1
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机分闸失败-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${级别设置值}    获取web参数量    油机分闸失败
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    设置子工具值    oileng    all    告警    预报警代码    0
    设置子工具值    oileng    all    告警    报警代码    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机分闸失败
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机分闸失败-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
    ...    AND    设置子工具值    oileng    all    告警    预报警代码    0
    ...    AND    设置子工具值    oileng    all    告警    报警代码    0

power_sm_0020_市电合闸失败
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    ${级别设置值}    获取web参数量    市电合闸失败
    run keyword if    '${级别设置值}'=='屏蔽'    设置web设备参数量为默认值    市电合闸失败
    ${告警级别取值约定dict}    获取web参数的取值约定    市电合闸失败
    设置子工具值    oileng    all    告警    备用9    1
    wait until keyword succeeds    5m    5    查询指定告警信息    市电合闸失败-1
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    市电合闸失败-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${级别设置值}    获取web参数量    市电合闸失败
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    设置子工具值    oileng    all    告警    备用9    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    市电合闸失败
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    市电合闸失败-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
    ...    AND    设置子工具值    oileng    all    告警    备用9    0

power_sm_0021_市电分闸失败
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    ${级别设置值}    获取web参数量    市电分闸失败
    run keyword if    '${级别设置值}'=='屏蔽'    设置web设备参数量为默认值    市电分闸失败
    ${告警级别取值约定dict}    获取web参数的取值约定    市电分闸失败
    设置子工具值    oileng    all    告警    预报警代码    30
    设置子工具值    oileng    all    告警    报警代码    30
    wait until keyword succeeds    5m    5    查询指定告警信息    市电分闸失败-1
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    市电分闸失败-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${级别设置值}    获取web参数量    市电分闸失败
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    设置子工具值    oileng    all    告警    预报警代码    0
    设置子工具值    oileng    all    告警    报警代码    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    市电分闸失败
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    市电分闸失败-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
    ...    AND    设置子工具值    oileng    all    告警    预报警代码    0
    ...    AND    设置子工具值    oileng    all    告警    报警代码    0

power_sm_0022_油机控制屏通讯中断
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    ${级别设置值}    获取web参数量    油机控制屏通讯中断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web设备参数量为默认值    油机控制屏通讯中断
    ${告警级别取值约定dict}    获取web参数的取值约定    油机控制屏通讯中断
    控制子工具运行停止    oileng    关闭
    wait until keyword succeeds    5m    2    查询指定告警信息    油机控制屏通讯中断-1
    Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client'    主动告警上送判断2    power_sm    油机控制屏_1    油机控制屏通讯中断
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机控制屏通讯中断-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${级别设置值}    获取web参数量    油机控制屏通讯中断
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    控制子工具运行停止    oileng    开启
    wait until keyword succeeds    5m    2    查询指定告警信息不为    油机控制屏通讯中断
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机控制屏通讯中断-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
    ...    AND    控制子工具运行停止    oileng    开启
