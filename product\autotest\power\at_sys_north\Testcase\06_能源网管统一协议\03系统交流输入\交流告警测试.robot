*** Settings ***
Suite Setup       Run Keywords   主动告警测试前置条件    ${CSU_role}
...    AND    测试用例前置条件
Suite Teardown    Run Keywords   设置web设备参数量为默认值    CSU主动告警使能
...    AND    测试用例后置条件

Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Default Tags      T1-1
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0002_交流停电
    [Setup]    run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    停电
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    交流供电状态    市电1
    关闭交流源输出
    Wait Until Keyword Succeeds    5m    2    设置web参数量    市电停电    屏蔽    #默认级别：2
    sleep    3
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    市电停电
    ${告警级别取值约定dict}    获取web参数的取值约定    市电停电
    ${web时间1}    获取系统时间
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    市电停电    ${告警级别设置}
        sleep    5
        Wait Until Keyword Succeeds     1m    1    查询指定告警信息    市电停电
        ${web实时告警名}    由子工具获取web实时告警名称    市电停电-1
        Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    市电    ${web实时告警名}
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    市电停电-1    ${SSH}   
        should be true    ${power_sm告警存在}[0]
        Comment    将数字型告警级别转换为中文
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    市电停电    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    #无告警后查询判断
    打开交流源输出
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    市电停电
    ${power_Sm告警存在}    判断power_sm是否存在单个实时告警    市电停电-1    ${SSH}
    should not be true    ${power_Sm告警存在}[0]
    [Teardown]    run keywords    打开交流源输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0003_交流电压低1
    [Documentation]    获取1104一条告警值并判断告警存在
    ...    输入
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${理应值}= | ${屏号}=None | ${g_ver}=
    ...
    ...
    ...
    ...    0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning; 0xFF：无效值
    [Setup]    run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压低_1
    ${可设置范围}    获取web参数可设置范围    交流电压低阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压低阈值    ${可设置范围}[1]
    ${电压低设置值}    获取web参数量    交流电压低阈值
    ${判断点1}    evaluate    ${电压低设置值}-5
    分别设置各相电压频率    ${判断点1}    220    220    50
    sleep    30
    ${电压1}    获取web实时数据    相电压UL1
    ${电压1}    获取web实时数据    相电压UL2
    ${电压1}    获取web实时数据    相电压UL3
    wait until keyword succeeds    5m    1    查询指定告警信息    交流电压低
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压低_1    屏蔽
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压低
    ${告警级别取值约定dict}    获取web参数的取值约定    交流电压低
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流电压低_1    ${告警级别设置}
        sleep   5
        wait until keyword succeeds    2m    1    查询指定告警信息    交流电压低
        ${web实时告警名}    由子工具获取web实时告警名称    交流电压低_1
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    系统交流输入    ${web实时告警名}
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    交流电压低_1    ${SSH}
        should be true    ${power_sm告警存在}[0]
        Comment    将数字型告警级别转换为中文
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    设置web参数量    交流电压低_1    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    #无告警后查询判断
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流电压低_1
    ${power_Sm告警存在}    判断power_sm是否存在单个实时告警    交流电压低_1    ${SSH}
    should not be true    ${power_Sm告警存在}[0]
    [Teardown]    run keywords    同时设置三相电压频率    220    50
    ...    AND    设置web设备参数量为默认值    交流电压低_1
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0004_交流电压高1
    [Setup]    run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压高_1
    ${可设置范围}    获取web参数可设置范围    交流电压高阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高阈值    ${可设置范围}[0]
    ${电压高设置值}    获取web参数量    交流电压高阈值
    ${判断点1}    evaluate    ${电压高设置值}+5
    分别设置各相电压频率    ${判断点1}    220    220    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高_1    屏蔽
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    交流电压高
    ${告警级别取值约定dict}    获取web参数的取值约定    交流电压高
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流电压高_1    ${告警级别设置}
        sleep   5
        wait until keyword succeeds    5m    1    查询指定告警信息    交流电压高
        ${web实时告警名}    由子工具获取web实时告警名称    交流电压高_1
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    系统交流输入    ${web实时告警名}
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    交流电压高_1    ${SSH}
        should be true    ${power_sm告警存在}[0]
        Comment    将数字型告警级别转换为中文
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    设置web参数量    交流电压高_1    主要
    #无告警后查询判断
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流电压高_1
    ${power_Sm告警存在}    判断power_sm是否存在单个实时告警    交流电压高_1    ${SSH}
    should not be true    ${power_Sm告警存在}[0]
    [Teardown]    run keywords    同时设置三相电压频率    220    50
    ...    AND    设置web设备参数量为默认值    交流电压高_1
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0005_交流缺相1
    [Setup]    run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流缺相_1
    分别设置各相电压频率    0    220    220    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流缺相_1    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    2m    1    查询指定告警信息不为    交流缺相
    ${告警级别取值约定dict}    获取web参数的取值约定    交流电压高
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流缺相_1    ${告警级别设置}
        sleep   5
        wait until keyword succeeds    15m    1    查询指定告警信息    交流缺相
        wait until keyword succeeds    2m    1    查询指定告警信息    交流缺相
        ${web实时告警名}    由子工具获取web实时告警名称    交流缺相_1
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    系统交流输入    ${web实时告警名}
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    交流缺相_1    ${SSH}
        should be true    ${power_sm告警存在}[0]
        Comment    将数字型告警级别转换为中文
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    交流缺相_1    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    #无告警后查询判断
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流缺相_1
    ${power_Sm告警存在}    判断power_sm是否存在单个实时告警    交流缺相_1    ${SSH}
    should not be true    ${power_Sm告警存在}[0]
    [Teardown]    run keywords    同时设置三相电压频率    220    50
    ...    AND    设置web设备参数量为默认值    交流缺相_1
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0006_交流电压低2
    [Setup]    run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压低_2
    ${可设置范围}    获取web参数可设置范围    交流电压低阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压低阈值    ${可设置范围}[1]
    ${电压低设置值}    获取web参数量    交流电压低阈值
    ${判断点1}    evaluate    ${电压低设置值}-5
    分别设置各相电压频率    220    ${判断点1}    220    50
    wait until keyword succeeds    5m    1    查询指定告警信息    交流电压低
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压低_2    屏蔽
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压低
    ${告警级别取值约定dict}    获取web参数的取值约定    交流电压低
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流电压低_2    ${告警级别设置}
        sleep   5
        wait until keyword succeeds    2m    1    查询指定告警信息    交流电压低
        ${web实时告警名}    由子工具获取web实时告警名称    交流电压低_2
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    系统交流输入    ${web实时告警名}
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    交流电压低_2    ${SSH}
        should be true    ${power_sm告警存在}[0]
        Comment    将数字型告警级别转换为中文
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    设置web参数量    交流电压低_1    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    #无告警后查询判断
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流电压低_2
    ${power_Sm告警存在}    判断power_sm是否存在单个实时告警    交流电压低_2    ${SSH}
    should not be true    ${power_Sm告警存在}[0]
    [Teardown]    run keywords    同时设置三相电压频率    220    50
    ...    AND    设置web设备参数量为默认值    交流电压低_2
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0007_交流电压高2
    [Setup]    run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压高_2
    ${可设置范围}    获取web参数可设置范围    交流电压高阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高阈值    ${可设置范围}[0]
    ${电压高设置值}    获取web参数量    交流电压高阈值
    ${判断点1}    evaluate    ${电压高设置值}+5
    分别设置各相电压频率    220    ${判断点1}    220    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高_2    屏蔽
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    交流电压高
    ${告警级别取值约定dict}    获取web参数的取值约定    交流电压高
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流电压高_2    ${告警级别设置}
        sleep   5
        wait until keyword succeeds    2m    1    查询指定告警信息    交流电压高
        ${web实时告警名}    由子工具获取web实时告警名称    交流电压高_2
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    系统交流输入    ${web实时告警名}
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    交流电压高_2    ${SSH}
        should be true    ${power_sm告警存在}[0]
        Comment    将数字型告警级别转换为中文
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    设置web参数量    交流电压高_2    主要
    #无告警后查询判断
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流电压高_2
    ${power_Sm告警存在}    判断power_sm是否存在单个实时告警    交流电压高_2    ${SSH}
    should not be true    ${power_Sm告警存在}[0]
    [Teardown]    run keywords    同时设置三相电压频率    220    50
    ...    AND    设置web设备参数量为默认值    交流电压高_2
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0008_交流缺相2
    [Setup]    run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流缺相_2
    分别设置各相电压频率    220    0    220    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流缺相_2    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    2m    1    查询指定告警信息不为    交流缺相
    ${告警级别取值约定dict}    获取web参数的取值约定    交流电压高
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流缺相_2    ${告警级别设置}
        sleep   5
        wait until keyword succeeds    1m    1    查询指定告警信息    交流缺相
        wait until keyword succeeds    2m    1    查询指定告警信息    交流缺相
        ${web实时告警名}    由子工具获取web实时告警名称    交流缺相_2
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    系统交流输入    ${web实时告警名}
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    交流缺相_2    ${SSH}
        should be true    ${power_sm告警存在}[0]
        Comment    将数字型告警级别转换为中文
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    交流缺相_2    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    #无告警后查询判断
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流缺相_2
    ${power_Sm告警存在}    判断power_sm是否存在单个实时告警    交流缺相_2    ${SSH}
    should not be true    ${power_Sm告警存在}[0]
    [Teardown]    run keywords    同时设置三相电压频率    220    50
    ...    AND    设置web设备参数量为默认值    交流缺相_2
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0009_交流电压低3
    [Documentation]    获取1104一条告警值并判断告警存在
    ...    输入
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${理应值}= | ${屏号}=None | ${g_ver}=
    [Setup]    run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压低_3
    ${可设置范围}    获取web参数可设置范围    交流电压低阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压低阈值    ${可设置范围}[1]
    ${电压低设置值}    获取web参数量    交流电压低阈值
    ${判断点1}    evaluate    ${电压低设置值}-5
    分别设置各相电压频率    220    220    ${判断点1}    50
    wait until keyword succeeds    5m    1    查询指定告警信息    交流电压低
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压低_3    屏蔽
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压低
    ${告警级别取值约定dict}    获取web参数的取值约定    交流电压低
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流电压低_3    ${告警级别设置}
        打印web实时告警信息名称
        wait until keyword succeeds    2m    1    查询指定告警信息    交流电压低
        ${web实时告警名}    由子工具获取web实时告警名称    交流电压低_3
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    系统交流输入    ${web实时告警名}
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    交流电压低_3    ${SSH}
        should be true    ${power_sm告警存在}[0]
        Comment    将数字型告警级别转换为中文
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    设置web参数量    交流电压低_1    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    #无告警后查询判断
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流电压低_3
    ${power_Sm告警存在}    判断power_sm是否存在单个实时告警    交流电压低_3    ${SSH}
    should not be true    ${power_Sm告警存在}[0]
    [Teardown]    run keywords    同时设置三相电压频率    220    50
    ...    AND    设置web设备参数量为默认值    交流电压低_3
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0010_交流电压高3
    [Setup]    run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压高_3
    ${可设置范围}    获取web参数可设置范围    交流电压高阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高阈值    ${可设置范围}[0]
    ${电压高设置值}    获取web参数量    交流电压高阈值
    ${判断点1}    evaluate    ${电压高设置值}+5
    分别设置各相电压频率    220    220    ${判断点1}    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高_3    屏蔽
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    交流电压高
    ${告警级别取值约定dict}    获取web参数的取值约定    交流电压高
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流电压高_3    ${告警级别设置}
        sleep   5
        wait until keyword succeeds    2m    1    查询指定告警信息    交流电压高
        ${web实时告警名}    由子工具获取web实时告警名称    交流电压高_3
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    系统交流输入    ${web实时告警名}
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    交流电压高_3    ${SSH}
        should be true    ${power_sm告警存在}[0]
        Comment    将数字型告警级别转换为中文
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    设置web参数量    交流电压高_3    主要
    #无告警后查询判断
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流电压高_3
    ${power_Sm告警存在}    判断power_sm是否存在单个实时告警    交流电压高_3    ${SSH}
    should not be true    ${power_Sm告警存在}[0]
    [Teardown]    run keywords    同时设置三相电压频率    220    50
    ...    AND    设置web设备参数量为默认值    交流电压高_3
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0011_交流缺相3
    [Setup]    run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流缺相_3
    分别设置各相电压频率    220    220    0    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流缺相_3    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    2m    1    查询指定告警信息不为    交流缺相
    ${告警级别取值约定dict}    获取web参数的取值约定    交流电压高
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流缺相_2    ${告警级别设置}
        sleep   5
        wait until keyword succeeds    1m    1    查询指定告警信息    交流缺相
        wait until keyword succeeds    2m    1    查询指定告警信息    交流缺相
        ${web实时告警名}    由子工具获取web实时告警名称    交流缺相_3
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    系统交流输入    ${web实时告警名}
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    交流缺相_3    ${SSH}
        should be true    ${power_sm告警存在}[0]
        Comment    将数字型告警级别转换为中文
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    交流缺相_3    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    #无告警后查询判断
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流缺相_3
    ${power_Sm告警存在}    判断power_sm是否存在单个实时告警    交流缺相_3    ${SSH}
    should not be true    ${power_Sm告警存在}[0]
    [Teardown]    run keywords    同时设置三相电压频率    220    50
    ...    AND    设置web设备参数量为默认值    交流缺相_3
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0012_交流电压不平衡
    [Setup]    run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压不平衡
    ${可设置范围}    获取web参数可设置范围    交流电压不平衡阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压不平衡阈值    ${可设置范围}[0]
    ${电压低设置值}    获取web参数量    交流电压不平衡阈值
    ${判断点1}    evaluate    220-${电压低设置值}-5
    Comment    分别设置各相电压频率    190    250    220    50
    分别设置各相电压频率    ${判断点1}    250    220    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压不平衡    屏蔽    #默认级别：2
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流电压不平衡
    ${告警级别取值约定dict}    获取web参数的取值约定    交流电压不平衡
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流电压不平衡    ${告警级别设置}
        sleep   5
        wait until keyword succeeds    1m    1    查询指定告警信息    交流电压不平衡
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    系统交流输入    交流电压不平衡
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    交流电压不平衡    ${SSH}
        should be true    ${power_sm告警存在}[0]
        Comment    将数字型告警级别转换为中文
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    设置web参数量    交流电压不平衡    主要
    #无告警后查询判断
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流电压不平衡
    ${power_Sm告警存在}    判断power_sm是否存在单个实时告警    交流电压不平衡    ${SSH}
    should not be true    ${power_Sm告警存在}[0]
    [Teardown]    run keywords    同时设置三相电压频率    220    50
    ...    AND    设置web设备参数量为默认值    交流电压不平衡
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0012_交流电流高
    [Setup]    run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电流
    ${可设置范围}    获取web参数可设置范围    交流电流高阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电流高阈值    ${可设置范围}[0]
    设置负载电压电流    53.5    80
    打开负载输出
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    系统停电
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电流高_1    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    2    判断告警不存在    交流电流高
    ${告警级别取值约定dict}    获取web参数的取值约定    交流电流高
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        设置web参数量    交流电流高_1    ${告警级别设置}
        sleep   5
        wait until keyword succeeds    15m    1    查询指定告警信息    交流电流高_1
        ${交流电流高告警级别}    获取web告警属性    交流电流高_1    告警级别
        should be equal    ${告警级别设置}    ${交流电流高告警级别}
        ${web实时告警名}    由子工具获取web实时告警名称    交流电流高_1
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    交流电流高_1    ${web实时告警名}
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    交流电流高_1    ${SSH}
        should be true    ${power_sm告警存在}[0]
        Comment    将数字型告警级别转换为中文
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    Wait Until Keyword Succeeds    2m    1    设置web设备参数量为默认值    交流电流高阈值
    同时设置三相电压频率    220    50
    关闭负载输出
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流电流高_1
    ${power_Sm告警存在}    判断power_sm是否存在单个实时告警    交流电流高_1    ${SSH}
    should not be true    ${power_Sm告警存在}[0]
    [Teardown]    run keywords    设置web设备参数量为默认值    交流电流高阈值
    ...    AND    关闭负载输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
