*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
智能空开设备统计测试
    连接CSU
    控制子工具运行停止    DMU_IntelAirSwit    关闭
    sleep  4m
    FOR    ${i}    IN RANGE    40
        ${temp}    evaluate    1+${i}
        ${temp}    Convert to string    ${temp}
        Wait Until Keyword Succeeds    20m    5    信号量数据值为    智能空开通讯状态-${temp}    异常
    END
    ${级别设置值}    获取web参数量    智能空开通讯断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    智能空开通讯断    严重
    wait until keyword succeeds    5m    2    查询指定告警信息    智能空开通讯断
    wait until keyword succeeds    1m    2    设置web控制量    智能空开设备统计
    wait until keyword succeeds    10m    2    查询指定告警信息不为    智能空开通讯断
    控制子工具运行停止    DMU_IntelAirSwit    启动
    FOR    ${i}    IN RANGE    40
        ${temp}    evaluate    1+${i}
        ${temp}    Convert to string    ${temp}
        Wait Until Keyword Succeeds    10m    5    信号量数据值为    智能空开通讯状态-${temp}    正常
    END
    wait until keyword succeeds    10m    2    查询指定告警信息不为    智能空开通讯断

智能空开下电测试
    连接CSU
    #显示属性配置    空开下电状态    数字量    web_attr=On    gui_attr=On
    设置子工具值    DMU_IntelAirSwit    1    呼叫    空开下电状态    0
    Wait Until Keyword Succeeds    15m    5    信号量数据值为(强制获取)    智能空开下电状态-1    0
    ${智能空开下电状态_1}    获取web实时数据    智能空开下电状态-1
    should be true    '${智能空开下电状态_1}'=='上电'
    Wait Until Keyword Succeeds    10X    2    设置web控制量    智能空开下电-1
    Wait Until Keyword Succeeds    15m    5    信号量数据值为(强制获取)    智能空开下电状态-1    1

智能空开上电测试
    连接CSU
    设置子工具值    DMU_IntelAirSwit    1    呼叫    空开下电状态    1
    Wait Until Keyword Succeeds    15m    5    信号量数据值为(强制获取)    智能空开下电状态-1    1
    ${智能空开下电状态_1}    获取web实时数据    智能空开下电状态-1
    should be true    '${智能空开下电状态_1}'=='下电'
    Wait Until Keyword Succeeds    10X    2    设置web控制量    智能空开上电-1
    Wait Until Keyword Succeeds    15m    5    信号量数据值为(强制获取)    智能空开下电状态-1    0

智能空开电量清零测试
    连接CSU
    设置子工具值    DMU_IntelAirSwit    1    呼叫    空开输出电量    12522
    Wait Until Keyword Succeeds    15m    5    信号量数据值为(强制获取)    智能空开输出电量-1    12522   
    Wait Until Keyword Succeeds    10X    2    设置web控制量    智能空开电量清零-1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    智能空开输出电量-1    0










