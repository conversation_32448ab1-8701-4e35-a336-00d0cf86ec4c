*** Settings ***
Suite Setup       #Run keywords    设置web控制量    允许所有告警    # AND    设置web参数量    <<禁止所有告警~0x1001030010001>>    严重
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
snmp_0002_禁止所有告警√
    [Documentation]    allAlarmBlocked
    ...    此告警不能屏蔽
    [Setup]    #Run keywords    设置web控制量    允许所有告警    # AND    设置web参数量    <<禁止所有告警~0x1001030010001>>    #Run keywords | 设置web控制量 | 允许所有告警 | # AND | 设置web参数量 | <<禁止所有告警~0x1001030010001>> | # 严重
    #演示关键字：    判断告警在Trap中的状态    #前置条件：运行 Trap_Receiver
    Comment    wait until keyword succeeds    30    1    设置web控制量    允许所有告警
    Comment    ${TEST}    设置web控制量    <<允许所有告警~0x1001040020001>>
    ${级别设置值}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    <<禁止所有告警~0x1001030010001>>    严重
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    禁止所有告警
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    wait until keyword succeeds    30    1    设置web控制量    <<禁止所有告警~0x1001040010001>>
    wait until keyword succeeds    3m    2    判断告警存在    禁止所有告警
    #产生
    ${snmp英文名}    获取snmp单个告警英文名    禁止所有告警
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    wait until keyword succeeds    30    1    设置web控制量    允许所有告警
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    禁止所有告警
    ${告警消失}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警消失}
    [Teardown]    Wait Until Keyword Succeeds    10    1    设置web控制量    允许所有告警

snmp_0004_MAC地址未设置_无
    [Tags]    T1-1    notest
    # [Setup]    测试用例前置条件
    连接CSU
    #获取告警级别
    ${级别设置值}    获取web参数量    MAC地址未设置
    ${干接点设置值}    获取web参数量    MAC地址未设置
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    MAC地址未设置    主要
    #产生告警后屏蔽
    #设置${plat.Inrelay2Status}
    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay2Status}    CSU    MAC地址未设置    断开
    Wait Until Keyword Succeeds    5m    2    设置web参数量    MAC地址未设置    屏蔽
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    MAC地址未设置
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    Wait Until Keyword Succeeds    5m    2    设置web参数量    MAC地址未设置    严重
    wait until keyword succeeds    10m    1    查询指定告警信息    MAC地址未设置
    #产生
    ${snmp英文名}    获取snmp单个告警英文名    MAC地址未设置
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay2Status}    无    无    闭合
    sleep    30
    Wait Until Keyword Succeeds    10m    1    判断告警不存在    MAC地址未设置
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    ###
    # [Teardown]    Run keywords    Wait Until Keyword Succeeds    30    1    设置web参数量    MAC地址未设置    主要
    # ...    AND    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay2Status}    无    无    闭合

snmp_0006_输入干接点告警2
    [Documentation]    通过DI2作为输入干接点告警的输入。输出干接点状态通过DI1的状态读出。
    [Setup]    #测试用例前置条件
    连接CSU
    #获取参数/默认值
    ${干接点上下限}    获取web参数量    输入干接点告警_2干接点
    ${告警干接点默认值}    run keyword if    '${干接点上下限}]'=='0'    set variable    无干接点
    ...    ELSE    set variable    A1
    #获取告警级别和输出干接点设置值
    ${级别设置值}    获取web参数量    输入干接点告警_2
    ${干接点设置值}    获取web参数量    输入干接点告警_2干接点
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    输入干接点告警_2    严重
    #产生告警后屏蔽
    #设置${plat.Inrelay2Status}，对应的CSU:输入干接点告警[2]
    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay2Status}    CSU    输入干接点告警_2    断开
    Wait Until Keyword Succeeds    5m    2    设置web参数量    输入干接点告警_2    屏蔽
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    输入干接点告警_2
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    Wait Until Keyword Succeeds    5m    2    设置web参数量    输入干接点告警_2    严重
    wait until keyword succeeds    1m    1    查询指定告警信息    输入干接点告警_2
    #产生
    ${snmp英文名}    获取snmp单个告警英文名    输入干接点告警_2
    ${snmp英文名}    Set Variable    inputRelayAlarmValueTable.2.2
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay2Status}    无    无    闭合
    sleep    30
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    输入干接点告警_2
    ${告警消失}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警消失}
    ###
    [Teardown]    Run keywords    设置web参数量    输入干接点告警_2干接点    0
    ...    AND    设置web参数量    输入干接点告警_2    次要
    ...    AND    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay2Status}    无    无    闭合

snmp_0008_CPU利用率高告警√
    [Documentation]    1.3.6.1.4.1.3902.2800.4.1.1.3.4:cPUUsageRateHighAlarm
    ...    默认 屏蔽
    [Setup]    #测试用例前置条件
    [Timeout]
    连接CSU
    显示属性配置    CPU利用率    模拟量    web_attr=On    gui_attr=On
    #获取参数/默认值
    ${级别设置值}    获取web参数量    CPU利用率高告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    CPU利用率高告警    严重
    ${可设置范围}    获取web参数可设置范围    CPU利用率高阈值
    设置web参数量    CPU利用率高阈值    ${可设置范围}[1]
    ${获取值}    获取web实时数据    CPU利用率
    ${设置值}    获取web参数量    CPU利用率高阈值
    should be true    ${获取值}<=${设置值}
    Wait Until Keyword Succeeds    5m    1    设置web参数量    CPU利用率高告警    屏蔽
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    CPU利用率高告警
    ${告警不存在}    判断告警存在_带返回值    CPU利用率高告警
    should not be true    ${告警不存在}    #告警不存在返回FALSE    #CPU占用率无法设置10    #关键字设置成功不提示失败
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    CPU利用率高告警    严重
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    设置web参数量    CPU利用率高阈值    ${可设置范围}[0]
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    CPU利用率高告警
    sleep    30
    #产生
    Comment    @{查询结果}    查询Trap告警    产生    ##list
    ${snmp英文名}    获取snmp单个告警英文名    CPU利用率高告警
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    Wait Until Keyword Succeeds    5m    2    设置web参数量    CPU利用率高告警    屏蔽
    sleep    30
    ${告警消失}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警消失}
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    CPU利用率高告警
    [Teardown]    Run keywords    设置web参数量    CPU利用率高告警    屏蔽
    ...    AND    设置web设备参数量为默认值    CPU利用率高阈值

snmp_0010_内存利用率高告警√
    [Tags]    V3.0
    # [Setup]    测试用例前置条件
    连接CSU
    显示属性配置    内存利用率    模拟量    web_attr=On    gui_attr=On
    #获取参数/默认值
    ${级别设置值}    获取web参数量    内存利用率高告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    内存利用率高告警    严重
    ${可设置范围}    获取web参数可设置范围    内存利用率高阈值
    设置web参数量    内存利用率高阈值    ${可设置范围}[1]
    ${获取值}    获取web实时数据    内存利用率
    ${设置值}    获取web参数量    内存利用率高阈值
    should be true    ${获取值}<=${设置值}
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    内存利用率高告警
    ${告警不存在}    判断告警存在_带返回值    内存利用率高告警
    should not be true    ${告警不存在}    #告警不存在返回FALSE    #CPU占用率无法设置10    #关键字设置成功不提示失败
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    设置web参数量    内存利用率高阈值    ${可设置范围}[0]
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    内存利用率高告警
    sleep    30
    #产生
    Comment    @{查询结果}    查询Trap告警    产生    ##list
    ${snmp英文名}    获取snmp单个告警英文名    内存利用率高告警
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    Wait Until Keyword Succeeds    5m    2    设置web参数量    内存利用率高告警    屏蔽
    sleep    30
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    内存利用率高告警
    显示属性配置    内存利用率    模拟量    web_attr=Off    gui_attr=Off
    # [Teardown]    Run keywords    设置web参数量    内存利用率高告警    屏蔽
    # ...    AND    设置web设备参数量为默认值    内存利用率高阈值

snmp_0012_UIB通讯断
    [Documentation]    此告警不能屏蔽
    [Tags]    T1-1
    [Setup]
    ${级别设置值}    获取web参数量    UIB通讯断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    UIB通讯断    主要
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    UIB通讯断
    模拟数字量告警    UIB板通讯断    ON
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    wait until keyword succeeds    3m    2    判断告警存在    UIB通讯断
    ${snmp英文名}    获取snmp单个告警英文名    UIB通讯断
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    模拟数字量告警    UIB板通讯断    OFF
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    UIB通讯断
    ${告警消失}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警消失}
    [Teardown]    run keywords    设置web设备参数量为默认值    UIB通讯断
    ...    AND    模拟数字量告警    UIB板通讯断    OFF

snmp_0014_IDDB通讯断
    [Tags]    T1-1
    ${级别设置值}    获取web参数量    IDDB通讯断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    IDDB通讯断    主要
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    IDDB通讯断
    模拟数字量告警    IDDB板通讯断    ON
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    wait until keyword succeeds    3m    2    判断告警存在    IDDB通讯断
    ${snmp英文名}    获取snmp单个告警英文名    IDDB通讯断
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    模拟数字量告警    IDDB板通讯断    OFF
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    IDDB通讯断
    ${告警消失}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警消失}
    [Teardown]    run keywords    设置web设备参数量为默认值    IDDB通讯断
    ...    AND    模拟数字量告警    IDDB板通讯断    OFF

snmp_0016_历史告警满_无
    [Tags]    3
    Log    硬件台不支持模拟此告警信息
    Log    暂不自动化

snmp_0018_监控单元故障告警_无
    [Tags]    3
    Log    硬件台不支持模拟此告警信息
    Log    暂不自动化
