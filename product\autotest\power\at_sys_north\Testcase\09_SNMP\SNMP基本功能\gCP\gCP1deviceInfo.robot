*** Settings ***
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp_test关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/Web关键字_V30.robot
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
# snmp批量获取油机设备信息
#     写入CSV文档    油机控制屏设备信息获取测试    信号名称    信号值    结果    备注
#     连接CSU
#     进行SNMP_V2/V3连接    ${snmp连接方式}
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string64    INDEX_DEVICE_CH=油机控制屏
#     ${排除列表}    create list
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${油机排除设备版本信息}    ${排除列表}    1
#     ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    gCP    deviceInfo
#     FOR    ${i}    IN    @{snmp待测}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${节点名称}    Get From Dictionary    ${i}    snmp_name
#         ${信号序号}    Get From Dictionary    ${i}    Vindex
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    oileng    ${信号名称}    只读    V99.23    油机控制屏设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    oileng    ${信号名称}    只读    V10.10    油机控制屏设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    oileng    ${信号名称}    只读    V1.81    油机控制屏设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
#     END
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string64    INDEX_DEVICE_CH=油机控制屏
#     ${排除列表}    create list
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${油机排除设备名称信息}    ${排除列表}    1
#     ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    gCP    deviceInfo
#     FOR    ${i}    IN    @{snmp待测}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${节点名称}    Get From Dictionary    ${i}    snmp_name
#         ${信号序号}    Get From Dictionary    ${i}    Vindex
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    oileng    ${信号名称}    只读    65535    油机控制屏设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    oileng    ${信号名称}    只读    45576    油机控制屏设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    oileng    ${信号名称}    只读    12312    油机控制屏设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
#     END
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=float    INDEX_DEVICE_CH=油机控制屏
#     ${排除列表}    create list
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    1
#     ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    gCP    deviceInfo
#     FOR    ${i}    IN    @{snmp待测}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${节点名称}    Get From Dictionary    ${i}    snmp_name
#         ${信号序号}    Get From Dictionary    ${i}    Vindex
#         ${缺省值}    获取web参数上下限范围    ${信号名称}
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    oileng    ${信号名称}    只读    ${缺省值}[1]    油机控制屏设备信息获取测试    数值    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    oileng    ${信号名称}    只读    ${缺省值}[2]    油机控制屏设备信息获取测试    数值    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    oileng    ${信号名称}    只读    ${缺省值}[0]    油机控制屏设备信息获取测试    数值    null    ${节点名称}    ${信号序号}
#     END
#     #以下不建议删
#     #子工具暂不支持设置油机控制屏系统地址
#     Comment    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=int    INDEX_DEVICE_CH=油机控制屏
#     Comment    ${排除列表}    create list
#     Comment    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    1
#     Comment    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    gCP    deviceInfo
#     Comment    FOR    ${i}    IN    @{snmp待测}
#     Comment    \    ${信号名称}    Get From Dictionary    ${i}    signal_name
#     Comment    \    ${节点名称}    Get From Dictionary    ${i}    snmp_name
#     Comment    \    ${信号序号}    Get From Dictionary    ${i}    Vindex
#     Comment    \    Comment    ${缺省值}    获取web参数上下限范围    ${信号名称}
#     Comment    \    Run Keyword And Continue On Failure    北向协议_南向厂家信息封装判断结果    snmp    oileng    ${信号名称}    只读    0    油机控制屏设备信息获取测试    数值    null    ${节点名称}    ${信号序号}
#     Comment    \    Run Keyword And Continue On Failure    北向协议_南向厂家信息封装判断结果    snmp    oileng    ${信号名称}    只读    255    油机控制屏设备信息获取测试    数值    null    ${节点名称}    ${信号序号}
#     Comment    \    Run Keyword And Continue On Failure    北向协议_南向厂家信息封装判断结果    snmp    oileng    ${信号名称}    只读    0    油机控制屏设备信息获取测试    数值    null    ${节点名称}    ${信号序号}
#     Comment    END
#     Comment    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=date    INDEX_DEVICE_CH=整流器
#     Comment    ${排除列表}    create list
#     Comment    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除设备日期信息}    ${排除列表}    1    ${模拟整流器地址}
#     Comment    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    gCP    deviceInfo
#     Comment    FOR    ${i}    IN    @{snmp待测}
#     Comment    \    ${信号名称}    Get From Dictionary    ${i}    signal_name
#     Comment    \    ${节点名称}    Get From Dictionary    ${i}    snmp_name
#     Comment    \    ${信号序号}    Get From Dictionary    ${i}    Vindex
#     Comment    \    Run Keyword And Continue On Failure    snmp_南向字符型厂家信息封装判断结果    oileng    ${信号名称}    只读    2018-11-15    油机控制屏设备信息获取测试    ${节点名称}    ${信号序号}
#     Comment    \    Run Keyword And Continue On Failure    snmp_南向字符型厂家信息封装判断结果    oileng    ${信号名称}    只读    2021-08-23    油机控制屏设备信息获取测试    ${节点名称}    ${信号序号}
#     Comment    \    Run Keyword And Continue On Failure    snmp_南向字符型厂家信息封装判断结果    oileng    ${信号名称}    只读    2018-07-28    油机控制屏设备信息获取测试    ${节点名称}    ${信号序号}
#     Comment    END
#     断开连接SNMP

snmp批量获取油机设备信息
    写入CSV文档    油机控制屏设备信息获取测试    信号名称    信号值    结果    备注
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    # @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string64    INDEX_DEVICE_CH=油机控制屏
    # ${排除列表}    create list
    # @{信号名称列表1}   create_list
    # ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${油机排除设备版本信息}    ${排除列表}    1
    # ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    gCP    deviceInfo
    # FOR    ${i}    IN    @{snmp待测}
    #     ${信号名称}    Get From Dictionary    ${i}    signal_name
    #     ${节点名称}    Get From Dictionary    ${i}    snmp_name
    #     ${信号序号}    Get From Dictionary    ${i}    Vindex
    #     ${dict}    Create Dictionary
    #     Set To Dictionary    ${dict}     signal_name     ${信号名称}
    #     Set To Dictionary    ${dict}     snmp_name     ${节点名称}
    #     Set To Dictionary    ${dict}     Vindex     ${信号序号}
	# 	Append To List       ${信号名称列表1}    ${dict}
    # END
    # Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    oileng    ${信号名称列表1}    只读    V99.23    油机控制屏设备信息获取测试    字符    null
    # Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    oileng    ${信号名称列表1}    只读    V10.10    油机控制屏设备信息获取测试    字符    null
    # Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    oileng    ${信号名称列表1}    只读    V1.81    油机控制屏设备信息获取测试    字符    null
    
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string64    INDEX_DEVICE_CH=油机控制屏
    ${排除列表}    create list
    @{信号名称列表2}    create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${油机排除设备名称信息}    ${排除列表}    1
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    gCP    deviceInfo
    FOR    ${i}    IN    @{snmp待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    snmp_name
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
        Set To Dictionary    ${dict}     snmp_name     ${节点名称}
        Set To Dictionary    ${dict}     Vindex     ${信号序号}
		Append To List       ${信号名称列表2}    ${dict}
    END
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    oileng    ${信号名称列表2}    只读    65535    油机控制屏设备信息获取测试    字符    null
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    oileng    ${信号名称列表2}    只读    45576    油机控制屏设备信息获取测试    字符    null
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    oileng    ${信号名称列表2}    只读    12312    油机控制屏设备信息获取测试    字符    null

    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=float    INDEX_DEVICE_CH=油机控制屏
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    1
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    gCP    deviceInfo
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    1    snmp
    Run Keyword And Continue On Failure    南向子设备厂家信息列表封装判断结果    snmp    oileng    ${缺省值列表}    只读    油机控制屏设备信息获取测试    数值    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    2    snmp
    Run Keyword And Continue On Failure    南向子设备厂家信息列表封装判断结果    snmp    oileng    ${缺省值列表}    只读    油机控制屏设备信息获取测试    数值    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    0    snmp
    Run Keyword And Continue On Failure    南向子设备厂家信息列表封装判断结果    snmp    oileng    ${缺省值列表}    只读    油机控制屏设备信息获取测试    数值    null
    #以下不建议删
    #子工具暂不支持设置油机控制屏系统地址
    Comment    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=int    INDEX_DEVICE_CH=油机控制屏
    Comment    ${排除列表}    create list
    Comment    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    1
    Comment    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    gCP    deviceInfo
    Comment    FOR    ${i}    IN    @{snmp待测}
    Comment    \    ${信号名称}    Get From Dictionary    ${i}    signal_name
    Comment    \    ${节点名称}    Get From Dictionary    ${i}    snmp_name
    Comment    \    ${信号序号}    Get From Dictionary    ${i}    Vindex
    Comment    \    Comment    ${缺省值}    获取web参数上下限范围    ${信号名称}
    Comment    \    Run Keyword And Continue On Failure    北向协议_南向厂家信息封装判断结果    snmp    oileng    ${信号名称}    只读    0    油机控制屏设备信息获取测试    数值    null    ${节点名称}    ${信号序号}
    Comment    \    Run Keyword And Continue On Failure    北向协议_南向厂家信息封装判断结果    snmp    oileng    ${信号名称}    只读    255    油机控制屏设备信息获取测试    数值    null    ${节点名称}    ${信号序号}
    Comment    \    Run Keyword And Continue On Failure    北向协议_南向厂家信息封装判断结果    snmp    oileng    ${信号名称}    只读    0    油机控制屏设备信息获取测试    数值    null    ${节点名称}    ${信号序号}
    Comment    END
    Comment    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=date    INDEX_DEVICE_CH=整流器
    Comment    ${排除列表}    create list
    Comment    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除设备日期信息}    ${排除列表}    1    ${模拟整流器地址}
    Comment    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    gCP    deviceInfo
    Comment    FOR    ${i}    IN    @{snmp待测}
    Comment    \    ${信号名称}    Get From Dictionary    ${i}    signal_name
    Comment    \    ${节点名称}    Get From Dictionary    ${i}    snmp_name
    Comment    \    ${信号序号}    Get From Dictionary    ${i}    Vindex
    Comment    \    Run Keyword And Continue On Failure    snmp_南向字符型厂家信息封装判断结果    oileng    ${信号名称}    只读    2018-11-15    油机控制屏设备信息获取测试    ${节点名称}    ${信号序号}
    Comment    \    Run Keyword And Continue On Failure    snmp_南向字符型厂家信息封装判断结果    oileng    ${信号名称}    只读    2021-08-23    油机控制屏设备信息获取测试    ${节点名称}    ${信号序号}
    Comment    \    Run Keyword And Continue On Failure    snmp_南向字符型厂家信息封装判断结果    oileng    ${信号名称}    只读    2018-07-28    油机控制屏设备信息获取测试    ${节点名称}    ${信号序号}
    Comment    END
    断开连接SNMP

# snmp_0002_油机控制屏系统名称1√
#     连接CSU
#     设置子工具值    oileng    all    只读    控制器类型    GM
#     sleep    1m
#     ${获取值1}    获取web实时数据    油机控制屏系统名称
#     进行SNMP_V2/V3连接    ${snmp连接方式}
#     ${snmp获取值1}    获取SNMP数据_单个    gCPSystemName1value
#     should be true    '${获取值1}'=='GM631'
#     should be equal    '${snmp获取值1}'    'GM631'
#     设置子工具值    oileng    all    只读    控制器类型    TM
#     sleep    1m
#     ${获取值1}    获取web实时数据    油机控制屏系统名称
#     ${snmp获取值1}    获取SNMP数据_单个    gCPSystemName1value
#     should be true    '${获取值1}'=='TM631'
#     should be equal    '${snmp获取值1}'    'TM631'
#     断开连接SNMP

snmp_0002_油机控制屏系统名称1√
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    设置子工具值    oileng    all    只读    控制器类型    GM
    sleep    20s
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    油机控制屏系统名称    GM631
    ${snmp获取值1}    获取SNMP数据_单个    gCPSystemName1value
    should be equal    '${snmp获取值1}'    'GM631'
    设置子工具值    oileng    all    只读    控制器类型    TM
    sleep    20s
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    油机控制屏系统名称    TM631
    ${snmp获取值1}    获取SNMP数据_单个    gCPSystemName1value
    should be equal    '${snmp获取值1}'    'TM631'
    断开连接SNMP

snmp_0004_油机控制屏软件版本号2√
    连接CSU
    设置子工具值    oileng    all    只读    软件版本号    303
    sleep    20s
    ${获取值1}    获取web实时数据    油机控制屏软件版本
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${snmp获取值1}    获取SNMP数据_单个    gCPSoftwareVersion1value
    should be true    '${获取值1}'=='V3.03'
    should be equal    '${snmp获取值1}'    'V3.03'
    设置子工具值    oileng    all    只读    软件版本号    304
    sleep    20s
    ${获取值1}    获取web实时数据    油机控制屏软件版本
    ${snmp获取值1}    获取SNMP数据_单个    gCPSoftwareVersion1value
    should be true    '${获取值1}'=='V3.04'
    should be equal    '${snmp获取值1}'    'V3.04'
    设置子工具值    oileng    all    只读    软件版本号    305
    sleep    20s
    ${获取值1}    获取web实时数据    油机控制屏软件版本
    ${snmp获取值1}    获取SNMP数据_单个    gCPSoftwareVersion1value
    should be true    '${获取值1}'=='V3.05'
    should be equal    '${snmp获取值1}'    'V3.05'
    断开连接SNMP

snmp_0006_油机控制屏软件发布日期3√
    连接CSU
    设置子工具值    oileng    all    只读    软件发布日期year    5139
    设置子工具值    oileng    all    只读    软件发布日期month    3095
    sleep    20s
    ${日期1}    获取web实时数据    油机控制屏软件发布日期
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${snmp获取值1}    获取SNMP数据_单个    gCPSoftwareReleaseDate1value
    should be equal    '${日期1}'    '2019-12-23'
    should be equal    '${snmp获取值1}'    '2019-12-23'
    设置子工具值    oileng    all    只读    软件发布日期year    5140
    设置子工具值    oileng    all    只读    软件发布日期month    3097
    sleep    20s
    ${日期1}    获取web实时数据    油机控制屏软件发布日期
    ${snmp获取值1}    获取SNMP数据_单个    gCPSoftwareReleaseDate1value
    should be equal    '${日期1}'    '2020-12-25'
    should be equal    '${snmp获取值1}'    '2020-12-25'
    断开连接SNMP
