*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Keywords ***
错峰用电场景初始化
    连接CSU
    ${未停电过}    获取指定量的调测信息    is_power_on_24h
    run keyword if    '${未停电过}' == '0'    系统复位
    连接CSU

错峰用电场景前置条件
    连接CSU
    ${电压低退出错峰}    获取指定量的调测信息    peak_shift_volt_quit
    run keyword if    '${电压低退出错峰}' == '1'    系统复位
    连接CSU
    重置电池模拟器输出

错峰用电不同时段不同类型测试初始化
    [Arguments]    ${充电系数}
    错峰用电场景前置条件
    错峰设置初始化    纯铅酸    1    ${充电系数}
    sleep    10
    设置web参数量    错峰时段1_1    06:00~07:00
    设置web参数量    错峰时段1_2    08:00~09:00
    设置web参数量    错峰时段1_3    10:00~11:00
    设置web参数量    错峰时段1_4    12:00~13:00
    sleep    10
    设置web参数量    错峰时段类型1_1    尖峰
    设置web参数量    错峰时段类型1_2    高峰
    设置web参数量    错峰时段类型1_3    平期
    设置web参数量    错峰时段类型1_4    谷期
    ${日期}    Get Current Date    result_format=%Y-%m-%d
    ${时间}    set variable    ${日期} 05:00:00
    设置系统时间    ${时间}
    退出错峰状态
    [Return]    ${日期}

错峰用电不同时段不同类型测试后置条件
    设置web设备参数量为默认值    尖峰时段充电系数    高峰时段充电系数    平期时段充电系数    谷期时段充电系数
    设置时段默认值    1
    设置web参数量    错峰日段_1    12-31~01-01
    退出错峰状态
    设置web参数量    电池充电模式    普通
    非错峰模式
    错峰用电场景后置条件

错峰用电节假日测试初始化
    [Arguments]    ${充电系数}
    错峰用电场景前置条件
    错峰设置初始化    纯铅酸    1    ${充电系数}
    设置web参数量    错峰时段1_1    06:00~07:00
    设置web参数量    错峰时段1_2    08:00~09:00
    设置web参数量    错峰时段1_3    10:00~11:00
    设置web参数量    错峰时段1_4    12:00~13:00
    sleep    10
    设置web参数量    错峰时段类型1_1    尖峰
    设置web参数量    错峰时段类型1_2    高峰
    设置web参数量    错峰时段类型1_3    平期
    设置web参数量    错峰时段类型1_4    谷期

错峰用电节假日测试后置条件
    [Arguments]    ${错峰用电时段跟周末重合}=False
    设置web设备参数量为默认值    尖峰时段充电系数    高峰时段充电系数    平期时段充电系数    谷期时段充电系数
    设置时段默认值    1
    设置web参数量    错峰日段_1    12-31~01-01
    退出错峰状态
    Run Keyword If     ${错峰用电时段跟周末重合}==True    设置web参数量    周末错峰用电使能    允许
    设置web参数量    电池充电模式    普通
    非错峰模式
    错峰用电场景后置条件


错峰用电时段功能测试初始化
    [Arguments]     ${错峰时段类型}
    设置web参数量    错峰时段1_1    00:00~23:59
    sleep    10
    设置web参数量    错峰时段类型1_1    ${错峰时段类型}
    sleep    5

错峰用电时段功能测试后置条件
    设置web设备参数量为默认值    尖峰时段充电系数    高峰时段充电系数    平期时段充电系数    谷期时段充电系数
    设置web参数量    错峰时段1_1    23:59~00:00
    设置web参数量    错峰日段_1    12-31~01-01
    退出错峰状态
    设置web参数量    电池充电模式    普通
    非错峰模式
    错峰用电场景后置条件

错峰用电场景后置条件
    重置电池模拟器输出
    设置web设备参数量为默认值    电池充电模式    电池配置    下电模式    交流输入场景
    同步系统时间

错峰放电校验
    [Arguments]    ${错峰类型}
    Wait Until Keyword Succeeds    3m    1    信号量数据值为    电池管理状态    错峰
    Wait Until Keyword Succeeds    3m    1    信号量数据值为    错峰用电工作状态    错峰放电
    Wait Until Keyword Succeeds    3m    1    信号量数据值为    错峰用电时段类型    ${错峰类型}
    Comment    Wait Until Keyword Succeeds    3m    1    信号量数据值小于    电池组总电流    0.00
    Comment    Wait Until Keyword Succeeds    3m    1    信号量数据值为    整流器总输出电流    0.00
    ${负载总电流}    获取web实时数据    负载总电流
    ${电池组总电流}    获取web实时数据    电池组总电流
    Comment    ${电池总电流}    evaluate    abs(${电池组总电流})
    Comment    should be equal as numbers    ${负载总电流}    ${电池总电流}    precision=1

错峰维持校验
    [Arguments]    ${错峰类型}
    Wait Until Keyword Succeeds    3m    1    信号量数据值为    电池管理状态    错峰
    Wait Until Keyword Succeeds    3m    1    信号量数据值为    错峰用电工作状态    错峰维持
    Wait Until Keyword Succeeds    3m    1    信号量数据值为    错峰用电时段类型    ${错峰类型}
    Comment    Wait Until Keyword Succeeds    3m    1    信号量数据值为    电池组总电流    0.00
    ${负载总电流}    获取web实时数据    负载总电流
    ${整流器输出电流}    获取web实时数据    整流器总输出电流
    Comment    should be equal as numbers    ${负载总电流}    ${整流器输出电流}    precision=1

非错峰状态校验
    [Arguments]    ${错峰类型}
    Wait Until Keyword Succeeds    3m    1    信号量数据值不为    电池管理状态    错峰
    Wait Until Keyword Succeeds    3m    1    信号量数据值为    错峰用电工作状态    无错峰
    Wait Until Keyword Succeeds    3m    1    信号量数据值为    错峰用电时段类型    ${错峰类型}
    Comment    Wait Until Keyword Succeeds    3m    1    信号量数据值不为    整流器总输出电流    0.00
    ${负载总电流}    获取web实时数据    负载总电流
    ${整流器输出电流}    获取web实时数据    整流器总输出电流
    Comment    should be equal as numbers    ${负载总电流}    ${整流器输出电流}    precision=1

退出错峰状态
    Wait Until Keyword Succeeds    3m    1    信号量数据值不为    电池管理状态    错峰
    Wait Until Keyword Succeeds    3m    1    信号量数据值为    错峰用电工作状态    无错峰
    Wait Until Keyword Succeeds    3m    1    信号量数据值为    错峰用电时段类型    其他
    Comment    Wait Until Keyword Succeeds    3m    1    信号量数据值不为    整流器总输出电流    0.00
    ${负载总电流}    获取web实时数据    负载总电流
    ${整流器输出电流}    获取web实时数据    整流器总输出电流
    Comment    should be equal as numbers    ${负载总电流}    ${整流器输出电流}    precision=1

非错峰模式
    sleep    10
    Wait Until Keyword Succeeds    3m    1    信号量数据值不为    电池管理状态    错峰
    ${工作状态}    获取web实时数据    错峰用电工作状态
    ${错峰用电类型}    获取web实时数据    错峰用电时段类型
    ${状态}    convert to boolean    ${工作状态}
    ${错峰类型}    convert to boolean    ${错峰用电类型}
    should not be true    ${状态}
    should not be true    ${错峰类型}

错峰谷期校验
    Wait Until Keyword Succeeds    3m    1    信号量数据值不为    电池管理状态    错峰
    Wait Until Keyword Succeeds    3m    1    信号量数据值为    错峰用电工作状态    无错峰
    Wait Until Keyword Succeeds    3m    1    信号量数据值为    错峰用电时段类型    谷期

错峰充电
    [Arguments]    ${错峰类型}
    Wait Until Keyword Succeeds    3m    1    信号量数据值为    电池管理状态    均充
    Wait Until Keyword Succeeds    3m    1    信号量数据值为    错峰用电工作状态    无错峰
    Wait Until Keyword Succeeds    3m    1    信号量数据值为    错峰用电时段类型    ${错峰类型}
    Comment    Wait Until Keyword Succeeds    3m    1    信号量数据值为    电池组总电流    0.00
    ${负载总电流}    获取web实时数据    负载总电流
    ${整流器输出电流}    获取web实时数据    整流器总输出电流
    Comment    should be equal as numbers    ${负载总电流}    ${整流器输出电流}    precision=1

设置日段默认值
    ${错峰日段}    Create List    错峰日段_1    错峰日段_2    错峰日段_3    错峰日段_4    错峰日段_5
    FOR    ${val}    IN    @{错峰日段}
        设置web参数量    ${val}    12-31~01-01
        ${获取参数值}    获取web参数量    ${val}
        should be equal as strings    ${获取参数值}    12-31~01-01
    END

设置时段默认值
    [Arguments]    ${日段序号}
    FOR    ${i}    IN RANGE    1    5
        ${错峰时段名称}    catenate    SEPARATOR=    错峰时段${日段序号}_    ${i}
        设置web参数量    ${错峰时段名称}    23:59~00:00
        ${获取参数值}    获取web参数量    ${错峰时段名称}
        should be equal as strings    ${获取参数值}    23:59~00:00
    END

错峰设置初始化
    [Arguments]    ${电池类型}    ${日段序号}    ${充电系数}
    设置web参数量    交流输入场景    市电
    设置web参数量    电池配置    ${电池类型}
    run keyword if    '${电池类型}' == '纯铅酸'    设置web参数量    铅酸类型    循环电池
    设置web参数量    电池充电模式    时段
    sleep    10
    设置web参数量    尖峰时段充电系数    ${充电系数}
    设置web参数量    高峰时段充电系数    ${充电系数}
    设置web参数量    平期时段充电系数    ${充电系数}
    # 设置web参数量    谷期时段充电系数    ${充电系数}
    设置web参数量    节假日日段_1    12-31~01-01
    设置web参数量    周末错峰用电使能    允许
    ${日段}    set variable    错峰日段_${日段序号}
    设置web参数量    ${日段}    01-01~12-31
    sleep    10
    设置时段默认值    ${日段序号}


设置节假日时段默认值
    FOR    ${i}    IN RANGE    1    21
        ${节假日日段名称}    catenate    SEPARATOR=    节假日日段_    ${i}
        设置web参数量    ${节假日日段名称}    12-31~01-01
        ${获取参数值}    获取web参数量    ${节假日日段名称}
        should be equal as strings    ${获取参数值}    12-31~01-01
    END

获取电池调节电压
    ${错峰启动电压}    获取错峰启动电压
    ${错峰退出电压}    获取错峰退出电压
    [Return]    ${错峰启动电压}    ${错峰退出电压}

获取错峰启动电压
    ${错峰启动电压}    获取web参数量    错峰启动电压
    ${错峰启动电压}    evaluate    ${错峰启动电压}+0.5
    [Return]    ${错峰启动电压}

获取错峰退出电压
    ${峰期}    获取web参数量    峰期错峰终止电压
    ${错峰退出电压}    evaluate    ${峰期}-0.1
    [Return]    ${错峰退出电压}

错峰放电校验/维持/校验
    [Arguments]    ${电池类型}    ${错峰类型}
    错峰放电校验    ${错峰类型}
    ${错峰启动电压}    ${错峰退出电压}    获取电池调节电压
    # 电压低进入错峰维持后，再进入错峰放电的电压回差为2
    ${错峰启动电压}    evaluate   ${错峰启动电压}+1.6
    Run Keyword If    '${电池类型}'=='智能锂电'    设置子工具值    smartli    1    只读    PACK电压    ${错峰退出电压}
    ...    ELSE    向下调节电池电压    ${错峰退出电压}
    错峰维持校验    ${错峰类型}
    Run Keyword If    '${电池类型}'=='智能锂电'    设置子工具值    smartli    1    只读    PACK电压    ${错峰启动电压}
    ...    ELSE    向上调节电池电压    ${错峰启动电压} 
    错峰放电校验    ${错峰类型}

错峰谷期校验/校验/校验
    [Arguments]    ${电池类型}
    错峰谷期校验
    ${错峰启动电压}    ${错峰退出电压}    获取电池调节电压
    # 电压低进入错峰维持后，再进入错峰放电的电压回差为2
    ${错峰启动电压}    evaluate   ${错峰启动电压}+1.6
    Run Keyword If    '${电池类型}'=='智能锂电'    设置子工具值    smartli    1    只读    PACK电压    ${错峰退出电压}
    ...    ELSE    向下调节电池电压    ${错峰退出电压}
    错峰谷期校验
    Run Keyword If    '${电池类型}'=='智能锂电'    设置子工具值    smartli    1    只读    PACK电压    ${错峰启动电压}
    ...    ELSE    向上调节电池电压    ${错峰启动电压} 
    错峰谷期校验