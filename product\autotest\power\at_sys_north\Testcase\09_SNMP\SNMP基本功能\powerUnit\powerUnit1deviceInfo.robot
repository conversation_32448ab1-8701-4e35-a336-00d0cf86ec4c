*** Settings ***
Suite Setup       #Run keywords    测试用例前置条件    # AND    仅有市电条件上电
Resource          ../../../../测试用例关键字.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp_test关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/Web关键字_V30.robot

*** Test Cases ***
# snmp批量获取PU设备信息
#     写入CSV文档    PU设备信息获取测试    信号名称    信号值    结果    备注
#     连接CSU
#     进行SNMP_V2/V3连接    ${snmp连接方式}
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string64    INDEX_DEVICE_CH=太阳能模块
#     ${排除列表}    create list
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除设备版本信息}    ${排除列表}    3    ${模拟PU起始地址}
#     ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    powerUnit    deviceInfo
#     FOR    ${i}    IN    @{snmp待测}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${节点名称}    Get From Dictionary    ${i}    snmp_name
#         ${信号序号}    Get From Dictionary    ${i}    Vindex
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    pu    ${信号名称}    版本    V99.23    PU设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    pu    ${信号名称}    版本    V10.10    PU设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    pu    ${信号名称}    版本    V1.81    PU设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
#     END
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string32    INDEX_DEVICE_CH=太阳能模块
#     ${排除列表}    create list
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除设备名称信息}    ${排除列表}    3    ${模拟PU起始地址}
#     ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    powerUnit    deviceInfo
#     FOR    ${i}    IN    @{snmp待测}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${节点名称}    Get From Dictionary    ${i}    snmp_name
#         ${信号序号}    Get From Dictionary    ${i}    Vindex
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    pu    ${信号名称}    厂家信息    VZXDU48 FB100B3    PU设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    pu    ${信号名称}    厂家信息    ZTE-smartli    PU设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    pu    ${信号名称}    厂家信息    VZXDU48 FB100C2    PU设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
#     END
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=float    INDEX_DEVICE_CH=太阳能模块
#     ${排除列表}    create list
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    3    ${模拟PU起始地址}
#     ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    powerUnit    deviceInfo
#     FOR    ${i}    IN    @{snmp待测}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${节点名称}    Get From Dictionary    ${i}    snmp_name
#         ${信号序号}    Get From Dictionary    ${i}    Vindex
#         ${缺省值}    获取web参数上下限范围    ${信号名称}
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    pu    ${信号名称}    版本    ${缺省值}[1]    PU设备信息获取测试    数值    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    pu    ${信号名称}    版本    ${缺省值}[2]    PU设备信息获取测试    数值    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    pu    ${信号名称}    版本    ${缺省值}[0]    PU设备信息获取测试    数值    null    ${节点名称}    ${信号序号}
#     END
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=int    INDEX_DEVICE_CH=太阳能模块
#     ${排除列表}    create list
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    3    ${模拟PU起始地址}
#     ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    powerUnit    deviceInfo
#     FOR    ${i}    IN    @{snmp待测}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${节点名称}    Get From Dictionary    ${i}    snmp_name
#         ${信号序号}    Get From Dictionary    ${i}    Vindex
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    pu    ${信号名称}    资产管理信息    0    PU设备信息获取测试    数值    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    pu    ${信号名称}    资产管理信息    255    PU设备信息获取测试    数值    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    pu    ${信号名称}    资产管理信息    0    PU设备信息获取测试    数值    null    ${节点名称}    ${信号序号}
#     END
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=date    INDEX_DEVICE_CH=太阳能模块
#     ${排除列表}    create list
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除设备日期信息}    ${排除列表}    3    ${模拟PU起始地址}
#     ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    powerUnit    deviceInfo
#     FOR    ${i}    IN    @{snmp待测}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${节点名称}    Get From Dictionary    ${i}    snmp_name
#         ${信号序号}    Get From Dictionary    ${i}    Vindex
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    pu    ${信号名称}    版本    2018-11-15    PU设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    pu    ${信号名称}    版本    2021-08-23    PU设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    pu    ${信号名称}    版本    2018-07-28    PU设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
#     END

snmp批量获取PU设备信息
    写入CSV文档    PU设备信息获取测试    信号名称    信号值    结果    备注
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string64    INDEX_DEVICE_CH=太阳能模块
    ${排除列表}    create list
    @{信号名称列表1}   create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除设备版本信息}    ${排除列表}    3    ${模拟PU起始地址}
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    powerUnit    deviceInfo
    FOR    ${i}    IN    @{snmp待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    snmp_name
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
        Set To Dictionary    ${dict}     snmp_name     ${节点名称}
        Set To Dictionary    ${dict}     Vindex     ${信号序号}
		Append To List       ${信号名称列表1}    ${dict}
    END
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    pu    ${信号名称列表1}    版本    V99.23    PU设备信息获取测试    字符    null
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    pu    ${信号名称列表1}    版本    V10.10    PU设备信息获取测试    字符    null
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    pu    ${信号名称列表1}    版本    V1.81    PU设备信息获取测试    字符    null
        
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string32    INDEX_DEVICE_CH=太阳能模块
    ${排除列表}    create list
    @{信号名称列表2}    create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除设备名称信息}    ${排除列表}    3    ${模拟PU起始地址}
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    powerUnit    deviceInfo
    FOR    ${i}    IN    @{snmp待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    snmp_name
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
        Set To Dictionary    ${dict}     snmp_name     ${节点名称}
        Set To Dictionary    ${dict}     Vindex     ${信号序号}
		Append To List       ${信号名称列表2}    ${dict}
    END
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    pu    ${信号名称列表2}    厂家信息    VZXDU48 FB100B3    PU设备信息获取测试    字符    null
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    pu    ${信号名称列表2}    厂家信息    ZTE-smartli    PU设备信息获取测试    字符    null
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    pu    ${信号名称列表2}    厂家信息    VZXDU48 FB100C2    PU设备信息获取测试    字符    null
    
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=float    INDEX_DEVICE_CH=太阳能模块
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    3    ${模拟PU起始地址}
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    powerUnit    deviceInfo
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    1    snmp
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    pu    ${缺省值列表}    版本    null    PU设备信息获取测试    数值    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    2    snmp
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    pu    ${缺省值列表}    版本    null    PU设备信息获取测试    数值    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    0    snmp
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    pu    ${缺省值列表}    版本    null    PU设备信息获取测试    数值    null

    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=int    INDEX_DEVICE_CH=太阳能模块
    ${排除列表}    create list
    @{信号名称列表3}    create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    3    ${模拟PU起始地址}
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    powerUnit    deviceInfo
    FOR    ${i}    IN    @{snmp待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    snmp_name
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
        Set To Dictionary    ${dict}     snmp_name     ${节点名称}
        Set To Dictionary    ${dict}     Vindex     ${信号序号}
		Append To List       ${信号名称列表3}    ${dict}
    END
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    pu    ${信号名称列表3}    资产管理信息    0    PU设备信息获取测试    数值    null
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    pu    ${信号名称列表3}    资产管理信息    255    PU设备信息获取测试    数值    null
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    pu    ${信号名称列表3}    资产管理信息    0    PU设备信息获取测试    数值    null

    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=date    INDEX_DEVICE_CH=太阳能模块
    ${排除列表}    create list
    @{信号名称列表4}    create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除设备日期信息}    ${排除列表}    3    ${模拟PU起始地址}
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    powerUnit    deviceInfo
    FOR    ${i}    IN    @{snmp待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    snmp_name
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
        Set To Dictionary    ${dict}     snmp_name     ${节点名称}
        Set To Dictionary    ${dict}     Vindex     ${信号序号}
		Append To List       ${信号名称列表4}    ${dict}
    END
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    pu    ${信号名称列表4}    版本    2018-11-15    PU设备信息获取测试    字符    null
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    pu    ${信号名称列表4}    版本    2021-08-23    PU设备信息获取测试    字符    null
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    pu    ${信号名称列表4}    版本    2018-07-28    PU设备信息获取测试    字符    null

snmp_0016_PU条码8
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具值    PU    all    资产管理信息    PU条码3    12522
    设置子工具值    PU    all    资产管理信息    PU条码4    50416
    设置子工具值    PU    all    资产管理信息    PU条码5g    178
    sleep    120
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ###3个PU
    FOR    ${PU序号}    IN RANGE    1    ${北向协议PU最大数}+1
        ${PU1条码}    获取web实时数据    <<PU条码-${PU序号}~0x10001080080001>>
        ${PU1条码}    evaluate    str(${PU1条码})
        should be true    ${PU1条码}==210097205426
        ${snmp获取值1}    获取SNMP数据_单个    pUBarcodes${PU序号}value
        should be true    ${snmp获取值1}==210097205426
    END
    断开连接SNMP

snmp_0018_生产日期9
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具值    PU    all    资产管理信息    生产日期年5d6g    2020
    设置子工具值    PU    all    资产管理信息    生产日期月6d    11
    设置子工具值    PU    all    资产管理信息    生产日期日7g    19
    sleep    1m
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ###3个PU
    FOR    ${PU序号}    IN RANGE    1    ${北向协议PU最大数}+1
        ${PU1生产日期}    获取web实时数据    <<生产日期-${PU序号}~0x10001080090001>>
        should be equal    '${PU1生产日期}'    '2020-11-19'
        ${snmp获取值1}    获取SNMP数据_单个    pUManufactureDate${PU序号}value
        should be true    '${snmp获取值1}'    '2020-11-19'
    END
    断开连接SNMP
