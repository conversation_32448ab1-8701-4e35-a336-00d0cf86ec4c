*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
自由模式下电池管理状态设置
    
    电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    自由
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自由
    Wait Until Keyword Succeeds    10    2s    设置web控制量    启动测试
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    测试
    Wait Until Keyword Succeeds    10    2s    设置web控制量    启动均充
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    均充
    Wait Until Keyword Succeeds    10    2s    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    浮充
    Wait Until Keyword Succeeds    10    2s    设置web控制量    启动均充
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    均充
    Wait Until Keyword Succeeds    10    2s    设置web控制量    启动测试
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    测试
    Wait Until Keyword Succeeds    10    2s    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    浮充
    [Teardown]    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全

自由模式下整流器轮换测试
    
    电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器轮换周期    1    #1天
    ${下次轮换时间}    获取web实时数据    下次轮换时间
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    自由
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自由
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    50    2
    打开负载输出
    sleep    5
    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-1    是
    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-2    是
    ${起始时间}    获取系统时间
    ${设置轮换时间}    Subtract Time From Date    ${起始时间}    50s    #比16:00:00差5s
    设置系统时间    ${设置轮换时间}
    sleep    5m
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-1    是
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-2    是
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-3    否
    [Teardown]    Run keywords    同步系统时间
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式
    ...    节能
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器轮换周期
    ...    7
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式
    ...    安全
    ...    AND    关闭负载输出

自由模式下开关整流器测试
    
    电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    自由
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自由
    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-1    是
    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-2    是
    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器休眠-3
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-3    是
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-1    否
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-2    否
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-3    否
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    系统停电
    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-1    是
    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器唤醒-1
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-1    否
    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-2    是
    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器唤醒-2
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-2    否
    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器休眠-3
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-3    是
    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器唤醒-3
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-3    否
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    系统停电
    [Teardown]    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全

监控复位开所有整流器测试
    
    电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    自由
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自由
    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-1    是
    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-2    是
    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器休眠-3
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-3    是
    系统复位
    sleep    90
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-1    否
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-2    否
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-3    否
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自由
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    工作整流器数量    3
    [Teardown]    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全

自由模式下整流器故障
    
    电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    自由
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自由
    ${均充电压可设置范围}    获取web参数可设置范围    均充电压
    设置web参数量    均充电压    ${均充电压可设置范围}[0]
    ${输出高停机电压可设置范围}    获取web参数可设置范围    整流器输出高停机电压
    设置web参数量    整流器输出高停机电压    ${输出高停机电压可设置范围}[0]
    #使整流器输出过压保证无正常工作的整流器
    ${整流器输出高电压}    获取web参数量    整流器输出高停机电压
    向上调节电池电压    ${整流器输出高电压}+0.5
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    工作整流器数量    0
    ${状态}    获取web实时数据    电池管理状态
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-3
    Comment    Wait Until Keyword Succeeds    1m    2    设置失败的web控制量    整流器唤醒-1
    Comment    Wait Until Keyword Succeeds    1m    2    设置失败的web控制量    整流器唤醒-2
    Comment    Wait Until Keyword Succeeds    1m    2    设置失败的web控制量    整流器唤醒-3
    向下调节电池电压    53.5
    ${在线整流器数量}    获取web实时数据    在线整流器数量
    Wait Until Keyword Succeeds    5m10s    1    信号量数据值为    工作整流器数量    ${在线整流器数量}
    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-1    是
    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器唤醒-1
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-1    否
    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-2    是
    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器唤醒-2
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-2    否
    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器休眠-3
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-3    是
    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器唤醒-3
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-3    否
    [Teardown]    Run keywords    电池管理参数恢复默认值
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式
    ...    安全
    ...    AND    重置电池模拟器输出

自由模式下交流停电
    
    电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    自由
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自由
    [Teardown]    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
