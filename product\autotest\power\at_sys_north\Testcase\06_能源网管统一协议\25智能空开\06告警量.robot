*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
批量获取SDDU告警量
    写入CSV文档    SDDU数字量和告警量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=智能直流配电单元
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SDDU2排除告警量信号}    ${排除列表}
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    智能直流配电单元    alarm    ${SDDU2排除告警量信号}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    @{SDDU2告警量数据1}    create list
    @{SDDU2告警量数据5}    create list
    @{SDDU2告警量数据9}    create list
    @{SDDU2告警量数据13}    create list
    FOR    ${i}    IN    @{power_sm待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        @{信号名称和序号}    split string    ${信号名称}    _
        @{信号序号和设备序号}    split string    ${信号名称和序号}[1]    -
        ${信号序号}    Set Variable    ${信号序号和设备序号}[0]
        ${信号序号}    BuiltIn.Convert To Integer    ${信号序号}
        Run Keyword IF    ${信号序号}==1    Append to List    ${SDDU2告警量数据1}    ${i}
        Run Keyword IF    ${信号序号}==5    Append to List    ${SDDU2告警量数据5}    ${i}
        Run Keyword IF    ${信号序号}==9    Append to List    ${SDDU2告警量数据9}    ${i}
        Run Keyword IF    ${信号序号}==13    Append to List    ${SDDU2告警量数据13}    ${i}
    END
    FOR    ${j}    IN    1  5  9  13
        ${呼叫命令序号}    Evaluate    (${j}-1)//4+1
        ${工具命令}    Run Keyword IF    "${呼叫命令序号}"=="1"    Set Variable    呼叫
        ...    ELSE    Set Variable    呼叫${呼叫命令序号}
        Run Keyword And Continue On Failure    南向RS485子设备告警量列表产生封装判断结果   power_sm
        ...    IntelAirSwitExtend    ${SDDU2告警量数据${j}}    ${工具命令}    ${告警产生}    SDDU数字量和告警量获取测试
        ...    负载    智能直流配电单元    null    null    null    null    null    null
        Run Keyword And Continue On Failure    南向RS485子设备告警量列表恢复封装判断结果    power_sm
        ...    IntelAirSwitExtend    ${SDDU2告警量数据${j}}    ${工具命令}    ${告警恢复}    SDDU数字量和告警量获取测试
        ...    负载    智能直流配电单元    null    null    null    null    null    null
    END
