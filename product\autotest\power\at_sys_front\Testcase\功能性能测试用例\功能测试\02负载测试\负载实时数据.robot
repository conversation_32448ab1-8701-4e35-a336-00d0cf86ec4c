*** Settings ***
Resource          ../../../../测试用例关键字.robot
Suite Setup       测试用例前置条件
Suite Teardown    测试用例后置条件

*** Test Cases ***
直流电压显示
    仅有市电条件上电
    连接CSU
    ${当前数据值}    Wait Until Keyword Succeeds    5m    2    获取web实时数据    直流电压
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
        设置web参数量    电池组容量_${i}    0
    END
    设置web参数量    电池组容量_1    300
    #将浮充电压可设置范围放宽，42~58V
    #放开上限
    ${一次下电电压数量}    run keyword and ignore error    获取web参数的数量    负载一次下电电压    #获取数量，web上可能有多个，负载一次下电电压_1，_2...
    ${一次下电电压数量}    run keyword if    '${一次下电电压数量}[0]'=='PASS' and '${一次下电电压数量}[1]'!='None'    set variable    ${一次下电电压数量}[1]
    ...    ELSE    evaluate    0
    ${二次下电电压数量}    run keyword and ignore error    获取web参数的数量    负载二次下电电压
    ${二次下电电压数量}    run keyword if    '${二次下电电压数量}[0]'=='PASS' and '${二次下电电压数量}[1]'!='None'    set variable    ${二次下电电压数量}[1]
    ...    ELSE    evaluate    0
    ${三次下电电压数量}    run keyword and ignore error    获取web参数的数量    负载三次下电电压
    ${三次下电电压数量}    run keyword if    '${三次下电电压数量}[0]'=='PASS' and '${三次下电电压数量}[1]'!='None'    set variable    ${三次下电电压数量}[1]
    ...    ELSE    evaluate    0
    ${租户一次下电电压数量}    run keyword and ignore error    获取web参数的数量    租户一次下电电压    #获取数量，web上可能有多个，负载一次下电电压_1，_2...
    ${租户一次下电电压数量}    run keyword if    '${租户一次下电电压数量}[0]'=='PASS' and '${租户一次下电电压数量}[1]'!='None'    set variable    ${租户一次下电电压数量}[1]
    ...    ELSE    evaluate    0
    ${整流器输出高停机电压}    获取web参数上下限范围    整流器输出高停机电压
    设置web参数量    整流器输出高停机电压    ${整流器输出高停机电压}[0]
    ${均充电压范围}    获取web参数可设置范围    均充电压
    设置web参数量    均充电压    ${均充电压范围}[1]
    #放开下限
    ${电池下电电压}    获取web参数上下限范围    电池下电电压
    ${二次下电电压}    run keyword and ignore error    获取web参数上下限范围    负载二次下电电压
    ${二次下电电压}    set variable    ${二次下电电压}[1]
    ${一次下电电压}    run keyword and ignore error    获取web参数上下限范围    负载一次下电电压
    ${一次下电电压}    set variable    ${一次下电电压}[1]
    ${三次下电电压}    run keyword and ignore error    获取web参数上下限范围    负载三次下电电压
    ${三次下电电压}    set variable    ${三次下电电压}[1]
    ${租户一次下电电压}    run keyword and ignore error    获取web参数上下限范围    租户一次下电电压
    ${租户一次下电电压}    set variable    ${租户一次下电电压}[1]
    ${电池电压过低阈值}    获取web参数上下限范围    电池电压过低阈值
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池电压过低阈值    ${电池电压过低阈值}[1]
    ${电池电压低阈值}    获取web参数上下限范围    电池电压低阈值
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池下电电压    ${电池下电电压}[1]    #设为下限
    run keyword if    ${三次下电电压数量}!=0    run keyword and ignore error    设置web序列参数    负载三次下电电压    ${三次下电电压}[1]
    run keyword if    ${二次下电电压数量}!=0    run keyword and ignore error    设置web序列参数    负载二次下电电压    ${二次下电电压}[1]
    run keyword if    ${一次下电电压数量}!=0    run keyword and ignore error    设置web序列参数    负载一次下电电压    ${一次下电电压}[1]
    run keyword if    ${租户一次下电电压数量}!=0    run keyword and ignore error    设置web序列参数    租户一次下电电压    ${租户一次下电电压}[1]
    设置web参数量    电池电压低阈值    ${电池电压低阈值}[1]
    设置web参数量    均充使能    禁止    #均充禁止，通过设置浮充电压来获取直流电压值
    设置web控制量    启动浮充
    ${浮充电压可设置范围}    获取web参数可设置范围    浮充电压
    should be true    ${浮充电压可设置范围}[0]<=42
    should be true    ${浮充电压可设置范围}[1]>=58
    #改变浮充电压来改变直流电压
    #42V
    ${浮充电压范围}    获取web参数上下限范围    浮充电压
    Wait Until Keyword Succeeds    3m    2    设置web参数量    浮充电压    ${浮充电压范围}[1]
    Wait Until Keyword Succeeds    6m    2    信号量数据值小于    直流电压    42.5
    sleep    4m
    ${获取数据}    获取web实时数据    直流电压
    ${获取数据_bat}    获取web实时数据    电池电压-1
    ${浮充电压设置值}    获取web参数量    浮充电压
    ${输出电压上限}    evaluate    ${浮充电压设置值}+0.3
    ${输出电压下限}    evaluate    ${浮充电压设置值}-0.3
    should be true    ${输出电压下限}<=${获取数据}<=${输出电压上限}    #直流电压偏差0.1V，电池电压偏差0.5V，整流器电压偏差1V
    should be true    -0.5<${获取数据}-${获取数据_bat}<0.5
    #58V
    Wait Until Keyword Succeeds    3m    2    设置web参数量    浮充电压    58
    #下限恢复参数默认值    #先恢复
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池电压低阈值    ${电池电压低阈值}[0]
    run keyword if    ${三次下电电压数量}!=0    run keyword and ignore error    设置web序列参数    负载三次下电电压    ${三次下电电压}[0]
    run keyword if    ${二次下电电压数量}!=0    run keyword and ignore error    设置web序列参数    负载二次下电电压    ${二次下电电压}[0]
    run keyword if    ${一次下电电压数量}!=0    run keyword and ignore error    设置web序列参数    负载一次下电电压    ${一次下电电压}[0]
    run keyword if    ${租户一次下电电压数量}!=0    run keyword and ignore error    设置web序列参数    租户一次下电电压    ${租户一次下电电压}[0]
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池下电电压    ${电池下电电压}[0]
    设置web参数量    电池组容量_1    0    #将电池容量均设为0，电压调节会更快速
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    直流电压    57
    设置web参数量    电池组容量_1    300    #有电池，电压控制更精准
    sleep    5    #设置参数后，等待页面刷新出来
    Wait Until Keyword Succeeds    3m    2    信号量数据值大于    直流电压    57.5
    sleep    5m
    ${获取数据}    获取web实时数据    直流电压
    ${获取数据_bat}    获取web实时数据    电池电压-1
    ${浮充电压设置值}    获取web参数量    浮充电压
    ${输出电压上限}    evaluate    ${浮充电压设置值}+0.3
    ${输出电压下限}    evaluate    ${浮充电压设置值}-0.3
    should be true    ${输出电压下限}<=${获取数据}<=${输出电压上限}    #直流电压偏差0.1V，电池电压偏差0.5V，整流器电压偏差1V
    should be true    -0.5<${获取数据}-${获取数据_bat}<0.5
    #53.5V
    设置web参数量    浮充电压    53.5
    Wait Until Keyword Succeeds    3m    2    信号量数据值小于    直流电压    54.0
    sleep    5m
    ${获取数据}    获取web实时数据    直流电压
    ${获取数据_bat}    获取web实时数据    电池电压-1
    should be true    53.3<=${获取数据}<=53.7    #直流电压偏差0.1V，电池电压偏差0.5V，整流器电压偏差1V
    should be true    -0.5<${获取数据}-${获取数据_bat}<0.5
    [Teardown]    电池管理参数恢复默认值

负载电流显示
    电池管理初始化
    仅有市电条件上电
    Wait Until Keyword Succeeds    3m    2    信号量数据值为    交流供电状态    市电1
    wait until keyword succeeds    3m    1    信号量数据值大于    工作整流器数量    0
    ${缺省值}    获取web参数上下限范围    电池充电电流系数
    ${电流可设置范围}    获取web参数可设置范围    电池充电电流系数
    Wait Until Keyword Succeeds    1m    2    设置web参数量    电池充电电流系数    ${电流可设置范围}[0]
    设置web参数量    浮充电压    53.5
    设置web控制量    启动浮充
    #电流5%精度
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    50    2
    打开负载输出
    Comment    关闭交流源输出
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    负载总电流    40
    Wait Until Keyword Succeeds    5m    2    判断信号量数据值稳定    负载总电流    3
    ${负载总电流}    获取web实时数据    负载总电流
    ${负载电流}    查询负载电压电流
    ${电流偏差}    evaluate    ${负载电流}[1]*0.05
    should be true    ${负载电流}[1]-${电流偏差}<${负载总电流}<${负载电流}[1]+${电流偏差}
    #增大电流
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    100    2
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    负载总电流    80
    Wait Until Keyword Succeeds    5m    2    判断信号量数据值稳定    负载总电流    3
    ${负载总电流}    获取web实时数据    负载总电流
    ${负载电流}    查询负载电压电流
    ${电流偏差}    evaluate    ${负载电流}[1]*0.05
    should be true    ${负载电流}[1]-${电流偏差}<${负载总电流}<${负载电流}[1]+${电流偏差}
    关闭负载输出
    设置web参数量    电池充电电流系数    ${缺省值}[0]
    [Teardown]    run keywords    关闭负载输出
    ...    AND    仅电池模拟器供电

负载总功率显示
    电池管理初始化
    仅有市电条件上电
    Wait Until Keyword Succeeds    3m    2    信号量数据值为    交流供电状态    市电1
    wait until keyword succeeds    3m    1    信号量数据值大于    工作整流器数量    0
    ${缺省值}    获取web参数上下限范围    电池充电电流系数
    ${电流可设置范围}    获取web参数可设置范围    电池充电电流系数
    Wait Until Keyword Succeeds    1m    2    设置web参数量    电池充电电流系数    ${电流可设置范围}[0]
    设置web参数量    浮充电压    53.5
    设置web控制量    启动浮充
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    50    2
    打开负载输出
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    负载总电流    40
    Wait Until Keyword Succeeds    5m    2    判断信号量数据值稳定    负载总电流    3
    ${负载总电流}    获取web实时数据    负载总电流
    ${总直流电压}    获取WEB实时数据    直流电压
    ${负载总功率计算值}    evaluate    ${总直流电压}*${负载总电流}/1000
    ${负载总功率上限}    evaluate    ${负载总功率计算值}+0.1
    ${负载总功率下限}    evaluate    ${负载总功率计算值}-0.1
    ${负载总功率显示值}    获取WEB实时数据    直流负载总功率
    should be true    ${负载总功率下限}<${负载总功率显示值}<${负载总功率上限}
    #增大电流
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    100    2
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    负载总电流    80
    Wait Until Keyword Succeeds    5m    2    判断信号量数据值稳定    负载总电流    3
    ${总直流电压}    获取WEB实时数据    直流电压
    ${负载总电流}    获取WEB实时数据    负载总电流
    ${负载总功率计算值}    evaluate    ${总直流电压}*${负载总电流}/1000
    ${负载总功率上限}    evaluate    ${负载总功率计算值}+0.1
    ${负载总功率下限}    evaluate    ${负载总功率计算值}-0.1
    ${负载总功率显示值}    获取WEB实时数据    直流负载总功率
    should be true    ${负载总功率下限}<${负载总功率显示值}<${负载总功率上限}
    关闭负载输出
    设置web参数量    电池充电电流系数    ${缺省值}[0]
    [Teardown]    run keywords    关闭负载输出
    ...    AND    仅电池模拟器供电
    ...    AND    设置web参数量    电池充电电流系数    ${缺省值}[0]

电池组总电流
    电池管理初始化
    仅有市电条件上电
    Wait Until Keyword Succeeds    3m    2    信号量数据值为    交流供电状态    市电1
    wait until keyword succeeds    3m    1    信号量数据值大于    工作整流器数量    0
    sleep    5    #等待电池信息页面刷新
    ${缺省值}    获取web参数上下限范围    电池充电电流系数
    ${电流可设置范围}    获取web参数可设置范围    电池充电电流系数
    Wait Until Keyword Succeeds    1m    2    设置web参数量    电池充电电流系数    ${电流可设置范围}[0]
    #设置电池分流器为默认值
    设置web参数量    浮充电压    53.5
    #恢复电池电流零点
    设置web控制量    启动浮充
    sleep    5
    ${电池电流1}    获取web实时数据    电池电流-1
    run keyword if    ${电池电流1}>0    Wait Until Keyword Succeeds    30    1    设置通道配置    CIB_X9_IB1    -${电池电流1}    1    直流配电    电池分流器电流_1
    Comment    run keyword if    ${电池电流1}>0    设置web参数量    <<电池电流零点_1~0x3310>>    -${电池电流1}
    ...    ELSE    设置web参数量    <<电池电流零点_1~0x3310>>    0
    ${电池电流调节后1}    获取web实时数据    电池电流-1
    #充电电流，不做精度要求
    ${电池组总电流}    获取web实时数据    电池组总电流
    should be true    ${电池组总电流}>=-1    #充电电流不稳定，暂不做充电精度要求。会有-0.几的显示，在要求内
    #放电电流    #电流5%精度
    打开电池模拟器输出
    sleep    5
    关闭交流源输出
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    50    2
    打开负载输出
    sleep    10
    wait until keyword succeeds    1m    2    信号量数据值小于    电池组总电流    -40
    ${负载电流}    查询负载电压电流
    ${电池组总电流}    获取web实时数据    电池组总电流
    ${电流偏差}    evaluate    ${负载电流}[1]*0.05
    should be true    ${电池组总电流}<0    #放电为负
    Comment    should be true    -${负载电流}[1]-${电流偏差}<${电池组总电流}<-${负载电流}[1]+${电流偏差}    #电池电流在变化
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    80    2
    wait until keyword succeeds    30    1    信号量数据值小于    电池组总电流    -73
    ${负载电流}    查询负载电压电流
    ${电池组总电流}    获取web实时数据    电池组总电流
    ${电流偏差}    evaluate    ${负载电流}[1]*0.05
    should be true    ${电池组总电流}<0    #放电为负
    should be true    -${负载电流}[1]-${电流偏差}<${电池组总电流}<-${负载电流}[1]+${电流偏差}    #电池电流在变化
    关闭负载输出
    打开交流源输出
    Wait Until Keyword Succeeds    30    1    设置通道配置    CIB_X9_IB1    0    1    直流配电    电池分流器电流_1
    设置web参数量    电池充电电流系数    ${缺省值}[0]
    [Teardown]    run keywords    关闭负载输出
    ...    AND    打开交流源输出
    ...    AND    Wait Until Keyword Succeeds    30    1    设置通道配置    CIB_X9_IB1    0    1    直流配电    电池分流器电流_1
    ...    AND    向下调节电池电压    53.5
    ...    AND    重置电池模拟器输出
