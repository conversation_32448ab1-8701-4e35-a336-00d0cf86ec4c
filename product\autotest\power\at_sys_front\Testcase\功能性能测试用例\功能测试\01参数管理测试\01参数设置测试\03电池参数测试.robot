*** Settings ***
Suite Setup
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
电池组容量参数测试
    [Tags]    view    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    ${电池组当前容量比率}    获取web参数的数量    电池组当前容量比率
    ${缺省值}    获取web参数上下限范围    电池组容量
    ${可设置范围}    获取web参数可设置范围    电池组容量_1
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    ${数量}    run keyword and ignore error    获取web参数的数量    主从机选择    #并机系统只测试主机
    ${测试电池组数量}    run keyword if    '${数量}[0]'=='PASS' and '${数量}[1]'=='1'    set variable    4
    ...    ELSE    set variable    ${电池组数量}
    FOR    ${i}    IN RANGE    ${测试电池组数量}    0    -1
        ${原参数}    Wait Until Keyword Succeeds    30    2    获取web参数量    电池组容量_${i}
    #(1)超范围设置不成功
    #超下限
        ${设置结果}    run keyword and return status    设置web参数量    电池组容量_${i}    ${超下限}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    电池组容量_${i}
        should be true    ${参数获取}==${原参数}
    #超上限
        ${设置结果}    run keyword and return status    设置web参数量    电池组容量_${i}    ${超上限}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    电池组容量_${i}
        should be true    ${参数获取}==${原参数}
    #(2)范围内设置成功
    #容量设置为10AH（不含）以下认为是无效电池容量
    #设置一次
        设置web参数量    电池组容量_${i}    0
        sleep    3
        ${参数获取0}    获取web参数量    电池组容量_${i}
        should be true    ${参数获取0}==0
        Comment    ${参数获取0}    获取web实时数据    电池组当前容量比率-${i}
        Comment    should be true    '${参数获取0}'==''
    #设置两次
        设置web参数量    电池组容量_${i}    5
        sleep    3
        ${参数获取0}    获取web参数量    电池组容量_${i}
        should be true    ${参数获取0}==5
        Comment    ${参数获取0}    获取web实时数据    电池组当前容量比率_${i}
        Comment    should be true    '${参数获取0}'==''
    #容量设置为10AH（含）以上才有效
    #设置一次
        设置web参数量    电池组容量_${i}    10
        sleep    8
        ${参数获取1}    获取web参数量    电池组容量_${i}
        should be true    ${参数获取1}==10
        ${参数获取1}    获取web实时数据    电池组当前容量比率-${i}
        should be true    ${参数获取1}==100
    #设置两次
        设置web参数量    电池组容量_${i}    ${可设置范围}[1]
        sleep    8
        ${参数获取1}    获取web参数量    电池组容量_${i}
        should be true    ${参数获取1}==${可设置范围}[1]
        ${参数获取1}    获取web实时数据    电池组当前容量比率-${i}
        should be true    ${参数获取1}==100
    #设置三次（缺省值）
        设置web参数量    电池组容量_${i}    ${缺省值}[0]
        sleep    8
        ${参数获取1}    获取web参数量    电池组容量_${i}
        should be true    ${参数获取1}==${缺省值}[0]
        ${参数获取1}    获取web实时数据    电池组当前容量比率-${i}
        should be true    ${参数获取1}==100
    END
    #只设置一组电池组
    FOR    ${i}    IN RANGE    ${电池组数量}    0    -1
        设置web参数量    电池组容量_${i}    0
    END
    设置web参数量    电池组容量_1    100
    [Teardown]    设置web参数量    电池组容量_1    100

浮充电压设置测试
    [Documentation]    42~58V；默认53.5V；浮充电压<=均充电压；浮充电压<=整流器输出高停机电压-1
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    浮充电压
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    浮充电压
    ${缺省值}    获取web参数上下限范围    浮充电压
    ${可设置范围}    获取web参数可设置范围    浮充电压
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    浮充电压    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    浮充电压
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    浮充电压    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    浮充电压
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    浮充电压

均充电压设置测试
    [Documentation]    42~58V；默认56.4V；均充电压>=浮充电压；均充电压<=整流器输出高停机电压
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    均充电压
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    均充电压
    ${缺省值}    获取web参数上下限范围    均充电压
    ${可设置范围}    获取web参数可设置范围    均充电压
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    均充电压    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    均充电压
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    均充电压    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    均充电压
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    均充电压

测试终止电压设置测试
    [Documentation]    42~50V；默认46V；大于等于负载一次下电电压、负载二次下电电压、电池下电电压
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    测试终止电压
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    测试终止电压
    ${缺省值}    获取web参数上下限范围    测试终止电压
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    测试终止电压    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    测试终止电压
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    测试终止电压    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    测试终止电压
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    测试终止电压

均充使能设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    均充使能
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    均充使能
    ${取值约定dict}    获取web参数的取值约定    均充使能
    ${取值约定values}    get dictionary values    ${取值约定dict}
    FOR    ${参数设置}    IN    @{取值约定values}
        设置web参数量    均充使能    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    均充使能
        should be equal    ${参数获取}    ${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    均充使能

过渡阶段启调电压偏差设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    过渡阶段启调电压偏差
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    过渡阶段启调电压偏差
    ${缺省值}    获取web参数上下限范围    过渡阶段启调电压偏差
    ${可设置范围}    获取web参数可设置范围    过渡阶段启调电压偏差
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    过渡阶段启调电压偏差    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    过渡阶段启调电压偏差
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    过渡阶段启调电压偏差    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    过渡阶段启调电压偏差
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    过渡阶段启调电压偏差

分流器限流比率设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    分流器限流比率
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    分流器限流比率
    ${缺省值}    获取web参数上下限范围    分流器限流比率
    ${可设置范围}    获取web参数可设置范围    分流器限流比率
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    分流器限流比率    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    分流器限流比率
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    分流器限流比率    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    分流器限流比率
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    分流器限流比率

铅酸类型设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    Run keywords    判断web参数是否存在    电池配置
    ...    AND    设置web参数量    电池配置    纯铅酸
    ...    AND    判断web参数是否存在    铅酸类型
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池配置    纯铅酸
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    铅酸类型
    ${取值约定dict}    获取web参数的取值约定    铅酸类型
    ${取值约定values}    get dictionary values    ${取值约定dict}
    FOR    ${参数设置}    IN    @{取值约定values}
        设置web参数量    铅酸类型    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    铅酸类型
        should be equal    ${参数获取}    ${参数设置}
    END
    [Teardown]    Run keywords    设置web设备参数量为默认值    铅酸类型
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    纯铅酸

电池温度高阈值设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    电池温度高阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    电池温度高阈值
    ${缺省值}    获取web参数上下限范围    电池温度高阈值
    ${可设置范围}    获取web参数可设置范围    电池温度高阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    电池温度高阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    电池温度高阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    电池温度高阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    电池温度高阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    电池温度高阈值

电池高温下电使能设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    电池高温下电使能
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    电池高温下电使能
    ${取值约定dict}    获取web参数的取值约定    电池高温下电使能
    ${取值约定values}    get dictionary values    ${取值约定dict}
    FOR    ${参数设置}    IN    @{取值约定values}
        设置web参数量    电池高温下电使能    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    电池高温下电使能
        should be equal    ${参数获取}    ${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    电池高温下电使能

电池高温下电温度设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    Run keywords    判断web参数是否存在    电池高温下电使能
    ...    AND    设置web参数量    电池高温下电使能    允许
    ...    AND    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    电池高温下电温度
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池高温下电使能    允许
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    电池高温下电温度
    ${缺省值}    获取web参数上下限范围    电池高温下电温度
    ${可设置范围}    获取web参数可设置范围    电池高温下电温度
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    电池高温下电温度    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    电池高温下电温度
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    电池高温下电温度    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    电池高温下电温度
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    电池高温下电温度    电池高温下电使能

电池温度低阈值设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    电池温度低阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    电池温度低阈值
    ${缺省值}    获取web参数上下限范围    电池温度低阈值
    ${可设置范围}    获取web参数可设置范围    电池温度低阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    电池温度低阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    电池温度低阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    电池温度低阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    电池温度低阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    电池温度低阈值

电池低温下电使能设置测试
    [Tags]    notest
    [Setup]    判断web参数是否存在    电池低温下电使能
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    电池低温下电使能
    ${取值约定dict}    获取web参数的取值约定    电池低温下电使能
    ${取值约定values}    get dictionary values    ${取值约定dict}
    FOR    ${参数设置}    IN    @{取值约定values}
        设置web参数量    电池低温下电使能    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    电池低温下电使能
        should be equal    ${参数获取}    ${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    电池低温下电使能

电池低温下电温度设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    Run keywords    判断web参数是否存在    电池低温下电使能
    ...    AND    设置web参数量    电池低温下电使能    允许
    ...    AND    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    电池低温下电温度
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池低温下电使能    允许
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    电池低温下电温度
    ${缺省值}    获取web参数上下限范围    电池低温下电温度
    ${可设置范围}    获取web参数可设置范围    电池低温下电温度
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    电池低温下电温度    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    电池低温下电温度
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    电池低温下电温度    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    电池低温下电温度
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    电池低温下电温度    电池低温下电使能

电池检测周期设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    电池检测周期
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    电池检测周期
    ${缺省值}    获取web参数上下限范围    电池检测周期
    ${可设置范围}    获取web参数可设置范围    电池检测周期
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    电池检测周期    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    电池检测周期
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    电池检测周期    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    电池检测周期
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    电池检测周期

电池回路断阈值电压设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    电池回路断阈值电压
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    电池回路断阈值电压
    ${缺省值}    获取web参数上下限范围    电池回路断阈值电压
    ${可设置范围}    获取web参数可设置范围    电池回路断阈值电压
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    电池回路断阈值电压    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    电池回路断阈值电压
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    电池回路断阈值电压    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    电池回路断阈值电压
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    电池回路断阈值电压

电池电压低阈值设置测试
    [Documentation]    39~52V；默认46V；电池电压低阈值>=电池电压过低阈值
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    电池电压低阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    电池电压低阈值
    ${缺省值}    获取web参数上下限范围    电池电压低阈值
    ${可设置范围}    获取web参数可设置范围    电池电压低阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    电池电压低阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    电池电压低阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    电池电压低阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    电池电压低阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    电池电压过低阈值    电池电压低阈值

电池放电阈值设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    电池放电阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    电池放电阈值
    ${缺省值}    获取web参数上下限范围    电池放电阈值
    ${可设置范围}    获取web参数可设置范围    电池放电阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    电池放电阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    电池放电阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    电池放电阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    电池放电阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    电池放电阈值

电池充电电流系数设置测试
    [Documentation]    0.051~0.6；默认0.32
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    电池充电电流系数
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    电池充电电流系数
    ${缺省值}    获取web参数上下限范围    电池充电电流系数
    ${可设置范围}    获取web参数可设置范围    电池充电电流系数
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    电池充电电流系数    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    电池充电电流系数
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    电池充电电流系数    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    电池充电电流系数
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    电池充电电流系数

电池测试周期设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    run keywords    测试用例前置条件
    ...    AND    电池管理参数恢复默认值
    ...    AND    判断web参数是否存在    电池测试周期
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    电池测试周期
    ${缺省值}    获取web参数上下限范围    电池测试周期
    ${可设置范围}    获取web参数可设置范围    电池测试周期
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    电池测试周期    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    电池测试周期
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    电池测试周期    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    电池测试周期
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    电池测试周期

测试最长时间设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    测试最长时间
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    测试最长时间
    ${缺省值}    获取web参数上下限范围    测试最长时间
    ${可设置范围}    获取web参数可设置范围    测试最长时间
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    测试最长时间    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    测试最长时间
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    测试最长时间    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    测试最长时间
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    测试最长时间

测试终止SOC阈值设置测试
    [Documentation]    普通铅酸：41~100，默认65；
    ...    负载一次、二次下电SOC阈值<=测试终止SOC阈值-1；测试终止SOC阈值<=测试失败SOC阈值；
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    测试终止SOC阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    测试终止SOC阈值
    ${缺省值}    获取web参数上下限范围    测试终止SOC阈值
    ${可设置范围}    获取web参数可设置范围    测试终止SOC阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    测试终止SOC阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    测试终止SOC阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    测试终止SOC阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    测试终止SOC阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    测试终止SOC阈值    测试失败SOC阈值

测试失败SOC阈值设置测试
    [Documentation]    普通铅酸：50~100，默认85；
    ...    负载一次、二次下电SOC阈值<=测试终止SOC阈值-1；测试终止SOC阈值<=测试失败SOC阈值；
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    测试失败SOC阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    测试失败SOC阈值
    ${缺省值}    获取web参数上下限范围    测试失败SOC阈值
    ${可设置范围}    获取web参数可设置范围    测试失败SOC阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    测试失败SOC阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    测试失败SOC阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    测试失败SOC阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    测试失败SOC阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    测试终止SOC阈值    测试失败SOC阈值

负载一次下电SOC阈值设置测试
    [Documentation]    铅酸：10~80，默认20；铁锂：10~90，默认15；负载一次下电SOC阈值<=测试终止SOC阈值-1；测试终止SOC阈值<=测试失败SOC阈值；
    [Tags]    PMSA-NTest    Re
    [Setup]    Run keywords    判断web参数是否存在    下电模式
    ...    AND    设置web参数量    下电模式    电池剩余容量
    ...    AND    判断web参数是否存在    负载一次下电使能
    ...    AND    设置web参数量    负载一次下电使能    允许
    ...    AND    判断web参数是否存在    负载一次下电SOC阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    负载一次下电SOC阈值
    ${缺省值}    获取web参数上下限范围    负载一次下电SOC阈值
    ${可设置范围}    获取web参数可设置范围    负载一次下电SOC阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    负载一次下电SOC阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    负载一次下电SOC阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    负载一次下电SOC阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    负载一次下电SOC阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    负载一次下电SOC阈值    负载二次下电SOC阈值    电池下电SOC阈值    测试终止SOC阈值    测试失败SOC阈值

负载二次下电SOC阈值设置测试
    [Documentation]    普通铅酸：10~80，默认10；
    ...    铁锂：5~90，默认5；
    ...    负载二次下电SOC阈值<=测试终止SOC阈值-1；测试终止SOC阈值<=测试失败SOC阈值；
    [Tags]    PMSA-NTest    Re
    [Setup]    Run keywords    判断web参数是否存在    下电模式
    ...    AND    设置web参数量    下电模式    电池剩余容量
    ...    AND    判断web参数是否存在    负载二次下电使能
    ...    AND    设置web参数量    负载二次下电使能    允许
    ...    AND    判断web参数是否存在    负载二次下电SOC阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    负载二次下电SOC阈值
    ${缺省值}    获取web参数上下限范围    负载二次下电SOC阈值
    ${可设置范围}    获取web参数可设置范围    负载二次下电SOC阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    负载二次下电SOC阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    负载二次下电SOC阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    负载二次下电SOC阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    负载二次下电SOC阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    负载一次下电SOC阈值    负载二次下电SOC阈值    电池下电SOC阈值    测试终止SOC阈值    测试失败SOC阈值

电池下电SOC阈值设置测试
    [Documentation]    10~80，默认10；
    ...    小于测试终止SOC阈值-1
    [Tags]    PMSA-NTest    Re
    [Setup]    Run keywords    判断web参数是否存在    下电模式
    ...    AND    设置web参数量    下电模式    电池剩余容量
    ...    AND    判断web参数是否存在    电池下电使能
    ...    AND    设置web参数量    电池下电使能    允许
    ...    AND    判断web参数是否存在    电池下电SOC阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    电池下电SOC阈值
    ${缺省值}    获取web参数上下限范围    电池下电SOC阈值
    ${可设置范围}    获取web参数可设置范围    电池下电SOC阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    电池下电SOC阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    电池下电SOC阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    电池下电SOC阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    电池下电SOC阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    负载一次下电SOC阈值    负载二次下电SOC阈值    电池下电SOC阈值    测试终止SOC阈值    测试失败SOC阈值

预约均充使能设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    预约均充使能
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    预约均充使能
    ${取值约定dict}    获取web参数的取值约定    预约均充使能
    ${取值约定values}    get dictionary values    ${取值约定dict}
    FOR    ${参数设置}    IN    @{取值约定values}
        设置web参数量    预约均充使能    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    预约均充使能
        should be equal    ${参数获取}    ${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    预约均充使能

预约均充日期设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    run keywords    判断web参数是否存在    预约均充使能
    ...    AND    设置web参数量    预约均充使能    允许
    ...    AND    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    预约均充日期
    ${原参数}    Wait Until Keyword Succeeds    2m    2    获取web参数量    预约均充日期
    ${设置范围}    获取web参数上下限范围    预约均充日期
    ${缺省值}    convert date    ${设置范围}[0]    result_format=%Y-%m-%d    date_format=%Y-%m-%d
    ${设置范围下限}    convert date    ${设置范围}[1]    result_format=%Y-%m-%d    date_format=%Y-%m-%d
    ${设置范围上限}    convert date    ${设置范围}[2]    result_format=%Y-%m-%d    date_format=%Y-%m-%d
    ${超下限}    subtract time from date    ${设置范围下限}    1d
    ${超上限}    add time to date    ${设置范围上限}    1d
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超下限}    ${超上限}
        ${设置结果}    run keyword and return status    设置web参数量    预约均充日期    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    预约均充日期
        Should Be Equal As Strings    ${参数获取}    ${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    ${设置范围下限}    ${设置范围上限}    ${缺省值}
        设置web参数量    预约均充日期    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    预约均充日期
        Should Be Equal As Strings    ${参数获取}    ${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    预约均充日期    预约均充使能

预约均充时长设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    run keywords    判断web参数是否存在    预约均充使能
    ...    AND    设置web参数量    预约均充使能    允许
    ...    AND    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    预约均充时长
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    预约均充时长
    ${缺省值}    获取web参数上下限范围    预约均充时长
    ${可设置范围}    获取web参数可设置范围    预约均充时长
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    预约均充时长    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    预约均充时长
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    预约均充时长    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    预约均充时长
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    预约均充时长    预约均充使能

下电模式设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    下电模式
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    下电模式
    ${取值约定dict}    获取web参数的取值约定    下电模式
    ${取值约定values}    get dictionary values    ${取值约定dict}
    FOR    ${参数设置}    IN    @{取值约定values}
        设置web参数量    下电模式    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    下电模式
        should be equal    ${参数获取}    ${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    下电模式

下电控制延时设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    run keywords    判断web参数是否存在    下电模式
    ...    AND    设置web参数量    下电模式    电池电压
    ...    AND    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    下电控制延时
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    下电控制延时
    ${缺省值}    获取web参数上下限范围    下电控制延时
    ${可设置范围}    获取web参数可设置范围    下电控制延时
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    下电控制延时    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    下电控制延时
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    下电控制延时    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    下电控制延时
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    下电控制延时    下电模式

一次下电恢复回差设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    run keywords    判断web参数是否存在    下电模式
    ...    AND    设置web参数量    下电模式    电池电压
    ...    AND    设置web参数量    负载一次下电使能    允许
    ...    AND    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    一次下电恢复回差
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    一次下电恢复回差
    ${缺省值}    获取web参数上下限范围    一次下电恢复回差
    ${可设置范围}    获取web参数可设置范围    一次下电恢复回差
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    一次下电恢复回差    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    一次下电恢复回差
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    一次下电恢复回差    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    一次下电恢复回差
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    一次下电恢复回差    负载一次下电使能    下电模式

温度补偿模式设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    温度补偿模式
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    温度补偿模式
    ${取值约定dict}    获取web参数的取值约定    温度补偿模式
    ${取值约定values}    get dictionary values    ${取值约定dict}
    FOR    ${参数设置}    IN    @{取值约定values}
        设置web参数量    温度补偿模式    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    温度补偿模式
        should be equal    ${参数获取}    ${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    温度补偿模式

电池温度补偿基准设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    电池温度补偿基准
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    电池温度补偿基准
    ${缺省值}    获取web参数上下限范围    电池温度补偿基准
    ${可设置范围}    获取web参数可设置范围    电池温度补偿基准
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    电池温度补偿基准    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    电池温度补偿基准
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    电池温度补偿基准    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    电池温度补偿基准
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    电池温度补偿基准

电池均充周期设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    run keywords    测试用例前置条件
    ...    AND    电池管理参数恢复默认值
    ...    AND    判断web参数是否存在    电池均充周期
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    电池均充周期
    ${缺省值}    获取web参数上下限范围    电池均充周期
    ${可设置范围}    获取web参数可设置范围    电池均充周期
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    电池均充周期    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    电池均充周期
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    电池均充周期    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    电池均充周期
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    电池均充周期

均充最长时间设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    Run keywords    判断web参数是否存在    均充使能
    ...    AND    设置web参数量    均充使能    允许
    ...    AND    判断web参数是否存在    均充最长时间
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    均充最长时间
    ${缺省值}    获取web参数上下限范围    均充最长时间
    ${可设置范围}    获取web参数可设置范围    均充最长时间
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    均充最长时间    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    均充最长时间
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    均充最长时间    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    均充最长时间
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    均充最长时间    均充使能

均充最短时间设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    Run keywords    判断web参数是否存在    均充使能
    ...    AND    设置web参数量    均充使能    允许
    ...    AND    判断web参数是否存在    均充最短时间
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    均充最短时间
    ${缺省值}    获取web参数上下限范围    均充最短时间
    ${可设置范围}    获取web参数可设置范围    均充最短时间
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    均充最短时间    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    均充最短时间
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    均充最短时间    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    均充最短时间
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    均充末期维持时间    均充最短时间    均充最长时间    均充使能

均充末期电流系数设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    均充末期电流系数
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    均充末期电流系数
    ${缺省值}    获取web参数上下限范围    均充末期电流系数
    ${可设置范围}    获取web参数可设置范围    均充末期电流系数
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    均充末期电流系数    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    均充末期电流系数
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    均充末期电流系数    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    均充末期电流系数
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    均充末期电流系数

均充末期维持时间设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    Run keywords    判断web参数是否存在    均充使能
    ...    AND    设置web参数量    均充使能    允许
    ...    AND    判断web参数是否存在    均充末期维持时间
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    均充末期维持时间
    ${缺省值}    获取web参数上下限范围    均充末期维持时间
    ${可设置范围}    获取web参数可设置范围    均充末期维持时间
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    均充末期维持时间    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    均充末期维持时间
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    均充末期维持时间    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    均充末期维持时间
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    均充末期维持时间    均充最短时间    均充最长时间    均充使能

均充阈值放电时间设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    均充阈值放电时间
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    均充阈值放电时间
    ${缺省值}    获取web参数上下限范围    均充阈值放电时间
    ${可设置范围}    获取web参数可设置范围    均充阈值放电时间
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    均充阈值放电时间    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    均充阈值放电时间
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    均充阈值放电时间    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    均充阈值放电时间
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    均充阈值放电时间

均充阈值电压设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    均充阈值电压
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    均充阈值电压
    ${缺省值}    获取web参数上下限范围    均充阈值电压
    ${可设置范围}    获取web参数可设置范围    均充阈值电压
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    均充阈值电压    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    均充阈值电压
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    均充阈值电压    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    均充阈值电压
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    均充阈值电压

均充阈值电流设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    均充阈值电流
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    均充阈值电流
    ${缺省值}    获取web参数上下限范围    均充阈值电流
    ${可设置范围}    获取web参数可设置范围    均充阈值电流
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    均充阈值电流    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    均充阈值电流
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    均充阈值电流    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    均充阈值电流
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    均充阈值电流

均充阈值SOC设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    均充阈值SOC
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    均充阈值SOC
    ${缺省值}    获取web参数上下限范围    均充阈值SOC
    ${可设置范围}    获取web参数可设置范围    均充阈值SOC
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    均充阈值SOC    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    均充阈值SOC
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    均充阈值SOC    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    均充阈值SOC
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    均充阈值SOC

环境温度控制基准值设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    环境温度控制基准值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    环境温度控制基准值
    ${缺省值}    获取web参数上下限范围    环境温度控制基准值
    ${可设置范围}    获取web参数可设置范围    环境温度控制基准值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    环境温度控制基准值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    环境温度控制基准值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    环境温度控制基准值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    环境温度控制基准值
        should be true    ${参数获取}==${参数设置}
    END
    连接CSU
    Wait Until Keyword Succeeds    5m    2    设置web参数量    环境温度控制基准值    80    #上限
    sleep    5
    ${环境温度控制基准值获取}    获取web参数量    环境温度控制基准值
    ${环境温度控制基准值获取}    create list    ${环境温度控制基准值获取}
    ${环境温度控制基准值确认}    create list    80
    should be true    ${环境温度控制基准值获取}==${环境温度控制基准值确认}
    ${起始时间}    ${结束时间}    获取起始结束时间段    9
    ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    环境温度控制基准值
    should be true    3>=${获取历史事件数量}>=1
    Wait Until Keyword Succeeds    5m    2    设置web参数量    环境温度控制基准值    45    #下限
    sleep    5
    ${环境温度控制基准值获取}    获取web参数量    环境温度控制基准值
    ${环境温度控制基准值获取}    create list    ${环境温度控制基准值获取}
    ${环境温度控制基准值确认}    create list    45
    should be true    ${环境温度控制基准值获取}==${环境温度控制基准值确认}
    ${起始时间}    ${结束时间}    获取起始结束时间段    9
    ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    环境温度控制基准值
    should be true    3>=${获取历史事件数量}>=1
    Wait Until Keyword Succeeds    5m    2    设置web参数量    环境温度控制基准值    55    #中间值
    sleep    5
    ${环境温度控制基准值获取}    获取web参数量    环境温度控制基准值
    ${环境温度控制基准值获取}    create list    ${环境温度控制基准值获取}
    ${环境温度控制基准值确认}    create list    55
    should be true    ${环境温度控制基准值获取}==${环境温度控制基准值确认}
    ${起始时间}    ${结束时间}    获取起始结束时间段    9
    ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    环境温度控制基准值
    should be true    3>=${获取历史事件数量}>=1
    [Teardown]    设置web设备参数量为默认值    环境温度控制基准值

环境温度控制补偿系数设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    环境温度控制补偿系数
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    环境温度控制补偿系数
    ${缺省值}    获取web参数上下限范围    环境温度控制补偿系数
    ${可设置范围}    获取web参数可设置范围    环境温度控制补偿系数
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    环境温度控制补偿系数    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    环境温度控制补偿系数
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    环境温度控制补偿系数    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    环境温度控制补偿系数
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    环境温度控制补偿系数

负载一次下电使能设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    run keywords    判断web参数是否存在    下电模式
    ...    AND    设置web参数量    下电模式    电池电压
    ...    AND    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    负载一次下电使能
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    负载一次下电使能
    ${取值约定dict}    获取web参数的取值约定    负载一次下电使能
    ${取值约定values}    get dictionary values    ${取值约定dict}
    FOR    ${参数设置}    IN    @{取值约定values}
        设置web参数量    负载一次下电使能    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    负载一次下电使能
        should be equal    ${参数获取}    ${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    负载一次下电使能    下电模式

负载一次下电电压设置测试
    [Documentation]    默认45V；小于等于电池电压低阈值-1、测试终止电压-1
    [Tags]    PMSA-NTest    Re
    [Setup]    run keywords    判断web参数是否存在    下电模式
    ...    AND    设置web参数量    下电模式    电池电压
    ...    AND    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    负载一次下电使能
    ...    AND    设置web参数量    负载一次下电使能    允许
    ...    AND    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    负载一次下电电压
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    负载一次下电电压
    ${缺省值}    获取web参数上下限范围    负载一次下电电压
    ${可设置范围}    获取web参数可设置范围    负载一次下电电压
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    负载一次下电电压    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    负载一次下电电压
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    负载一次下电电压    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    负载一次下电电压
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    测试终止电压    电池电压低阈值    负载一次下电电压    负载一次下电使能    下电模式

负载二次下电使能设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    run keywords    判断web参数是否存在    下电模式
    ...    AND    设置web参数量    下电模式    电池电压
    ...    AND    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    负载二次下电使能
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    负载二次下电使能
    ${取值约定dict}    获取web参数的取值约定    负载二次下电使能
    ${取值约定values}    get dictionary values    ${取值约定dict}
    FOR    ${参数设置}    IN    @{取值约定values}
        设置web参数量    负载二次下电使能    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    负载二次下电使能
        should be equal    ${参数获取}    ${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    负载二次下电使能    下电模式

负载二次下电电压设置测试
    [Documentation]    普通铅酸：38~49V，默认44V；
    ...    铅酸25: 38~51V，默认45.8V
    ...    铁锂：42~58V，默认46V；
    ...    小于等于电池电压低阈值-1、测试终止电压-1
    [Tags]    PMSA-NTest    Re
    [Setup]    run keywords    判断web参数是否存在    下电模式
    ...    AND    设置web参数量    下电模式    电池电压
    ...    AND    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    负载二次下电使能
    ...    AND    设置web参数量    负载二次下电使能    允许
    ...    AND    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    负载二次下电电压
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    负载二次下电电压
    ${缺省值}    获取web参数上下限范围    负载二次下电电压
    ${可设置范围}    获取web参数可设置范围    负载二次下电电压
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    负载二次下电电压    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    负载二次下电电压
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    负载二次下电电压    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    负载二次下电电压
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    测试终止电压    电池电压低阈值    负载二次下电电压    负载二次下电使能    下电模式

电池下电使能设置测试
    [Tags]    no test
    [Setup]    run keywords    判断web参数是否存在    下电模式
    ...    AND    设置web参数量    下电模式    电池电压
    ...    AND    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    电池下电使能
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    电池下电使能
    ${取值约定dict}    获取web参数的取值约定    电池下电使能
    ${取值约定values}    get dictionary values    ${取值约定dict}
    FOR    ${参数设置}    IN    @{取值约定values}
        设置web参数量    电池下电使能    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    电池下电使能
        should be equal    ${参数获取}    ${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    电池下电使能    下电模式

电池下电电压设置测试
    [Documentation]    38~49；默认44
    [Tags]    no test
    [Setup]    run keywords    判断web参数是否存在    下电模式
    ...    AND    设置web参数量    下电模式    电池电压
    ...    AND    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    电池下电使能
    ...    AND    设置web参数量    电池下电使能    允许
    ...    AND    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    电池下电电压
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    电池下电电压
    ${缺省值}    获取web参数上下限范围    电池下电电压
    ${可设置范围}    获取web参数可设置范围    电池下电电压
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    电池下电电压    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    电池下电电压
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    电池下电电压    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    电池下电电压
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    测试终止电压    电池电压低阈值    电池下电电压    电池下电使能    下电模式

电池检测持续时间设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    run keywords    判断web参数是否存在    电池检测周期
    ...    AND    设置web参数量    电池检测周期    30
    ...    AND    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    电池检测持续时间
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    电池检测持续时间
    ${缺省值}    获取web参数上下限范围    电池检测持续时间
    ${可设置范围}    获取web参数可设置范围    电池检测持续时间
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    电池检测持续时间    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    电池检测持续时间
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    电池检测持续时间    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    电池检测持续时间
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    电池检测持续时间    电池检测周期

电池电压温度补偿系数设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    run keywords    判断web参数是否存在    温度补偿模式
    ...    AND    设置web参数量    温度补偿模式    允许
    ...    AND    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    电池电压温度补偿系数
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    电池电压温度补偿系数
    ${缺省值}    获取web参数上下限范围    电池电压温度补偿系数
    ${可设置范围}    获取web参数可设置范围    电池电压温度补偿系数
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    电池电压温度补偿系数    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    电池电压温度补偿系数
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    电池电压温度补偿系数    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    电池电压温度补偿系数
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    电池电压温度补偿系数    温度补偿模式

电池电流温补使能设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    电池电流温补使能
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    电池电流温补使能
    ${取值约定dict}    获取web参数的取值约定    电池电流温补使能
    ${取值约定values}    get dictionary values    ${取值约定dict}
    FOR    ${参数设置}    IN    @{取值约定values}
        设置web参数量    电池电流温补使能    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    电池电流温补使能
        should be equal    ${参数获取}    ${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    电池电流温补使能

电池电流温度补偿系数设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    run keywords    判断web参数是否存在    电池电流温补使能
    ...    AND    设置web参数量    电池电流温补使能    允许
    ...    AND    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    电池电流温度补偿系数
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    电池电流温度补偿系数
    ${缺省值}    获取web参数上下限范围    电池电流温度补偿系数
    ${可设置范围}    获取web参数可设置范围    电池电流温度补偿系数
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    电池电流温度补偿系数    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    电池电流温度补偿系数
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    电池电流温度补偿系数    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    电池电流温度补偿系数
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    电池电流温度补偿系数    温度补偿模式

SOH异常设定容量比例设置测试
    [Documentation]    此参数不用了
    [Tags]    notest
    连接CSU
    Wait Until Keyword Succeeds    5m    2    设置web参数量    SOH异常设定容量比例    100    #上限
    sleep    5
    ${SOH异常设定容量比例获取}    获取web参数量    SOH异常设定容量比例
    ${SOH异常设定容量比例获取}    create list    ${SOH异常设定容量比例获取}
    ${SOH异常设定容量比例确认}    create list    100
    should be true    ${SOH异常设定容量比例获取}==${SOH异常设定容量比例确认}
    ${起始时间}    ${结束时间}    获取起始结束时间段    9
    ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    操作记录    SOH异常设定容量比例
    should be true    3>=${获取历史事件数量}>=1
    Wait Until Keyword Succeeds    5m    2    设置web参数量    SOH异常设定容量比例    0    #下限
    sleep    5
    ${SOH异常设定容量比例获取}    获取web参数量    SOH异常设定容量比例
    ${SOH异常设定容量比例获取}    create list    ${SOH异常设定容量比例获取}
    ${SOH异常设定容量比例确认}    create list    0
    should be true    ${SOH异常设定容量比例获取}==${SOH异常设定容量比例确认}
    ${起始时间}    ${结束时间}    获取起始结束时间段    9
    ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    操作记录    SOH异常设定容量比例
    should be true    3>=${获取历史事件数量}>=1
    Wait Until Keyword Succeeds    5m    2    设置web参数量    SOH异常设定容量比例    20    #中间值
    sleep    5
    ${SOH异常设定容量比例获取}    获取web参数量    SOH异常设定容量比例
    ${SOH异常设定容量比例获取}    create list    ${SOH异常设定容量比例获取}
    ${SOH异常设定容量比例确认}    create list    20
    should be true    ${SOH异常设定容量比例获取}==${SOH异常设定容量比例确认}
    ${起始时间}    ${结束时间}    获取起始结束时间段    9
    ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    操作记录    SOH异常设定容量比例
    should be true    3>=${获取历史事件数量}>=1

温度补偿电压上限设置测试
    [Documentation]    50~58；默认57.6
    [Tags]    PMSA-NTest    Re
    [Setup]    run keywords    判断web参数是否存在    温度补偿模式
    ...    AND    设置web参数量    温度补偿模式    允许
    ...    AND    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    温度补偿电压上限
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    温度补偿电压上限
    ${缺省值}    获取web参数上下限范围    温度补偿电压上限
    ${可设置范围}    获取web参数可设置范围    温度补偿电压上限
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    温度补偿电压上限    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    温度补偿电压上限
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    温度补偿电压上限    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    温度补偿电压上限
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    温度补偿电压下限    温度补偿电压上限    温度补偿模式

温度补偿电压下限设置测试
    [Documentation]    50~58；默认52.8
    [Tags]    PMSA-NTest    Re
    [Setup]    run keywords    判断web参数是否存在    温度补偿模式
    ...    AND    设置web参数量    温度补偿模式    允许
    ...    AND    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    温度补偿电压下限
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    温度补偿电压下限
    ${缺省值}    获取web参数上下限范围    温度补偿电压下限
    ${可设置范围}    获取web参数可设置范围    温度补偿电压下限
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    温度补偿电压下限    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    温度补偿电压下限
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    温度补偿电压下限    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    温度补偿电压下限
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    温度补偿电压下限    温度补偿电压上限    温度补偿模式

电池电压过低阈值设置测试
    [Documentation]    39~52，默认45
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    电池电压过低阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    电池电压过低阈值
    ${缺省值}    获取web参数上下限范围    电池电压过低阈值
    ${可设置范围}    获取web参数可设置范围    电池电压过低阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    电池电压过低阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    电池电压过低阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    电池电压过低阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    电池电压过低阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    电池电压过低阈值

电池系列设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    电池系列
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    电池系列
    ${取值约定dict}    获取web参数的取值约定    电池系列
    ${取值约定values}    get dictionary values    ${取值约定dict}
    FOR    ${参数设置}    IN    @{取值约定values}
        设置web参数量    电池系列    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    电池系列
        should be equal    ${参数获取}    ${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    电池系列

电池启用日期设置测试
    [Documentation]    2000-01-01~~2037-12-31;默认：2037-12-31
    [Tags]    view    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    <<电池启用日期-1~0xb001050010001>>
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    <<电池启用日期-1~0xb001050010001>>
    ${设置范围}    获取web参数上下限范围    <<电池启用日期-1~0xb001050010001>>
    ${缺省值}    convert date    ${设置范围}[0]    result_format=%Y-%m-%d    date_format=%Y-%m-%d
    ${设置范围下限}    convert date    ${设置范围}[1]    result_format=%Y-%m-%d    date_format=%Y-%m-%d
    ${设置范围上限}    convert date    ${设置范围}[2]    result_format=%Y-%m-%d    date_format=%Y-%m-%d
    ${超下限}    subtract time from date    ${设置范围下限}    1d
    ${超上限}    add time to date    ${设置范围上限}    1d
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超下限}    ${超上限}
        ${设置结果}    run keyword and return status    设置web参数量    <<电池启用日期-1~0xb001050010001>>    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    <<电池启用日期-1~0xb001050010001>>
        Should Be Equal As Strings    ${参数获取}    ${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    ${设置范围下限}    ${设置范围上限}    ${缺省值}
        设置web参数量    <<电池启用日期-1~0xb001050010001>>    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    <<电池启用日期-1~0xb001050010001>>
        Should Be Equal As Strings    ${参数获取}    ${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    <<电池启用日期-1~0xb001050010001>>

电池中点电压不平衡阈值设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    电池中点电压不平衡阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    电池中点电压不平衡阈值
    ${缺省值}    获取web参数上下限范围    电池中点电压不平衡阈值
    ${可设置范围}    获取web参数可设置范围    电池中点电压不平衡阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    电池中点电压不平衡阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    电池中点电压不平衡阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    电池中点电压不平衡阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    电池中点电压不平衡阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    电池中点电压不平衡阈值

远程一次下电使能测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    远程一次下电使能
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    远程一次下电使能
    ${取值约定dict}    获取web参数的取值约定    远程一次下电使能
    ${取值约定values}    get dictionary values    ${取值约定dict}
    FOR    ${参数设置}    IN    @{取值约定values}
        设置web参数量    远程一次下电使能    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    远程一次下电使能
        should be equal    ${参数获取}    ${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    远程一次下电使能

远程二次下电使能测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    远程二次下电使能
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    远程二次下电使能
    ${取值约定dict}    获取web参数的取值约定    远程二次下电使能
    ${取值约定values}    get dictionary values    ${取值约定dict}
    FOR    ${参数设置}    IN    @{取值约定values}
        设置web参数量    远程二次下电使能    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    远程二次下电使能
        should be equal    ${参数获取}    ${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    远程二次下电使能

铅酸切换电压测试
    [Tags]    PMSA-NTest    Re
    [Setup]    Run keywords    电池配置切换
    ...    AND    判断web参数是否存在    锂电切换SOC
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    锂电切换SOC
    ${缺省值}    获取web参数上下限范围    锂电切换SOC
    ${可设置范围}    获取web参数可设置范围    锂电切换SOC
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    锂电切换SOC    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    锂电切换SOC
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    锂电切换SOC    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    锂电切换SOC
        should be true    ${参数获取}==${参数设置}
    END

锂电放电DOD设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    Run keywords    电池配置切换
    ...    AND    判断web参数是否存在    锂电放电DOD
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    锂电放电DOD
    ${缺省值}    获取web参数上下限范围    锂电放电DOD
    ${可设置范围}    获取web参数可设置范围    锂电放电DOD
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    锂电放电DOD    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    锂电放电DOD
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    锂电放电DOD    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    锂电放电DOD
        should be true    ${参数获取}==${参数设置}
    END

输出电压偏差阈值设置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    Run keywords    电池配置切换
    ...    AND    判断web参数是否存在    输出电压偏差阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    输出电压偏差阈值
    ${缺省值}    获取web参数上下限范围    输出电压偏差阈值
    ${可设置范围}    获取web参数可设置范围    输出电压偏差阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    输出电压偏差阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    输出电压偏差阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    输出电压偏差阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    输出电压偏差阈值
        should be true    ${参数获取}==${参数设置}
    END
