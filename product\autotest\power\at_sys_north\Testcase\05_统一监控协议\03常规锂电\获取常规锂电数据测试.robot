*** Settings ***
Test Setup        Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
获取NFBBMS告警量
    [Setup]    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    @{锂电序号随机list}    从1-15个数中随机选n个不重复的单体    ${北向协议NFBBMS最大数}    2
    FOR    ${锂电池序号}    IN    @{锂电序号随机list}
        ${锂电池序号}    Convert To String    ${锂电池序号}
        设置子工具值    fb100c2    ${锂电池序号}    all    all    max
        sleep    10min
        ${协议数据}    统一监控协议_获取数据    锂电池告警量
        ${协议数据1}    统一监控协议_选择锂电类型对应SID    ${协议数据}    NFBBMS
        ${web告警信息}    统一监控协议_获取web对应告警
        ${对比结果}    批量对比告警_统一监控协议_WEB    ${协议数据1}    ${web告警信息}    锂电池告警量
        should be true    ${对比结果}
        设置子工具值    fb100c2    ${锂电池序号}    all    all    def
        sleep    10min
        ${协议数据}    统一监控协议_获取数据    锂电池告警量
        ${获取结果}    Set Variable    ${协议数据}[0][result]
        ${获取值}    Set Variable    ${协议数据}[0][value]
        should be equal as strings    ${获取结果}    ok
        should be equal as strings    ${获取值}    0
    END
    [Teardown]    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

批量获取NFBBMS数字量
    写入CSV文档    fb100c2数字量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=digital data    INDEX_DEVICE_CH=NFBBMS
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    1    1    0    0
    Comment    Log many    ${列表1}
    ${协议数据}    统一监控协议_获取数据    锂电池状态量
    Comment    Log    ${协议数据}
    ${协议数据1}    统一监控协议_选择锂电类型对应SID    ${协议数据}    NFBBMS
    ${web数据}    统一监控协议_获取web对应数据    ${协议数据1}
    #获取web和数字字典都有的量
    ${sm待测}    uniform_获取1104/1363指定设备指定信号量    ${协议数据}    ${web数据}    ${列表1}
    Log many    ${sm待测}

    ${待测数据长度}    Get Length    ${sm待测}
    ${缺省值列表}   获取缺省值列表  ${sm待测}    1        sm
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    sm    fb100c2      获取开关量    ${缺省值列表}    fb100c2数字量获取测试    锂电池状态量    
    ${缺省值列表}   获取缺省值列表  ${sm待测}    2        sm
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    sm    fb100c2      获取开关量    ${缺省值列表}    fb100c2数字量获取测试    锂电池状态量   
    ${缺省值列表}   获取缺省值列表  ${sm待测}    0        sm
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    sm    fb100c2      获取开关量    ${缺省值列表}    fb100c2数字量获取测试    锂电池状态量   

批量获取NFBBMS模拟量
    写入CSV文档    fb100c2模拟量获取测试    信号名称    信号值    结果
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=NFBBMS
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    1    1    0    0
    ${协议数据}    统一监控协议_获取数据    常规锂电池模拟量
    ${协议数据1}    统一监控协议_选择锂电类型对应SID    ${协议数据}    NFBBMS
    ${web数据}    统一监控协议_获取web对应数据    ${协议数据1}
    #获取web和数字字典都有的量
    ${sm待测}    uniform_获取1104/1363指定设备指定信号量    ${协议数据1}    ${web数据}    ${列表1}
    Log many    ${sm待测}
    ${缺省值列表}   获取缺省值列表  ${sm待测}    1        sm    
	Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    sm    fb100c2      获取模拟量    ${缺省值列表}    fb100c2模拟量获取测试    常规锂电池模拟量    
    ${缺省值列表}   获取缺省值列表  ${sm待测}    2        sm
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    sm    fb100c2      获取模拟量    ${缺省值列表}    fb100c2模拟量获取测试    常规锂电池模拟量    
    ${缺省值列表}   获取缺省值列表  ${sm待测}    0         sm
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    sm    fb100c2      获取模拟量    ${缺省值列表}    fb100c2模拟量获取测试    常规锂电池模拟量    
     


获取NFBBMS通讯断告警
    控制子工具运行停止    fb100c2    关闭
    sleep    3m
    ${协议数据}    统一监控协议_获取数据    锂电池告警量
    ${协议数据1}    统一监控协议_选择锂电类型对应SID    ${协议数据}    NFBBMS
    ${web告警信息}    统一监控协议_获取web对应告警
    ${对比结果}    批量对比告警_统一监控协议_WEB    ${协议数据1}    ${web告警信息}    锂电池告警量
    should be true    ${对比结果}
