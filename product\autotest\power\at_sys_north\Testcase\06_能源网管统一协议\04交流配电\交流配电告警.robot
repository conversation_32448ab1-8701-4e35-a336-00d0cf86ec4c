*** Settings ***
Suite Setup       主动告警测试前置条件    ${CSU_role}
Suite Teardown    设置web设备参数量为默认值    CSU主动告警使能
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Default Tags      T1-1
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_交流输入空开断
    [Documentation]    需要修改配置文件
    ...    web上才会显示此告警参数信息
    实时告警刷新完成
    ${级别设置值}    获取web参数量    交流输入空开断
    #run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    交流输入空开断    主要
    #wait until keyword succeeds    1m    1    判断告警不存在    交流输入空开断
    设置web参数量    交流输入空开断    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    交流输入空开断
    ${告警}    判断告警存在_带返回值    交流输入空开断
    should not be true    ${告警}
    ${告警级别取值约定dict}    获取web参数的取值约定    交流停电
    模拟数字量告警    交流输入空开断    ON
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    6m    1    设置web参数量    交流输入空开断    ${告警级别设置}
        wait until keyword succeeds    5m    1    查询指定告警信息    交流输入空开断
        sleep    5
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    交流配电    交流输入空开断
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    交流输入空开断    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${sm告警级别}    获取web告警属性    交流输入空开断    告警级别
        should be equal    ${sm告警级别}    ${告警级别设置}
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    模拟数字量告警    交流输入空开断    OFF
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流输入空开断
    ${power_Sm告警存在}    判断power_sm是否存在单个实时告警    交流输入空开断    ${SSH}
    should not be true    ${power_Sm告警存在}[0]
    [Teardown]    run keywords    设置web设备参数量为默认值    交流输入空开断
    ...    AND    模拟数字量告警    交流输入空开断    OFF
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0002_交流避雷器异常
    实时告警刷新完成
    ${级别设置值}    获取web参数量    交流防雷器异常
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    交流防雷器异常    主要
    wait until keyword succeeds    1m    1    判断告警不存在    交流防雷器异常
    设置web参数量    交流防雷器异常    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    交流防雷器异常
    ${告警}    判断告警存在_带返回值    交流防雷器异常
    should not be true    ${告警}
    ${告警级别取值约定dict}    获取web参数的取值约定    交流停电
    模拟数字量告警    交流防雷器异常    ON
    FOR    ${告警级别设置}    IN    主要    次要    警告    严重
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流防雷器异常    ${告警级别设置}
        sleep   5
        wait until keyword succeeds    1m    1    查询指定告警信息    交流防雷器异常
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    交流配电    交流防雷器异常
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    交流防雷器异常    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${sm告警级别}    获取web告警属性    交流防雷器异常    告警级别
        should be equal    ${sm告警级别}    ${告警级别设置}
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    模拟数字量告警    交流防雷器异常    OFF
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流防雷器异常
    ${power_Sm告警存在}    判断power_sm是否存在单个实时告警    交流防雷器异常    ${SSH}
    should not be true    ${power_Sm告警存在}[0]
    [Teardown]    run keywords    设置web设备参数量为默认值    交流防雷器异常
    ...    AND    模拟数字量告警    交流防雷器异常    OFF
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0003_交流输出空开断
    [Documentation]    硬件台不支持模拟此告警信息
    [Tags]    3
    Log    硬件台不支持模拟此告警信息
    Log    暂不自动化
    [Teardown]
