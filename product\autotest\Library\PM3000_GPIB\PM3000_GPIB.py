
#-*-coding:utf-8 -*-
#####################################################################################
# File Name:    PM3000_GPIB.py
# Discription:  实现与PM3000的连接和数据读写
# Author:   Pengy；
# DevEnv:   winXP/7x86+python2.7
# Notes:    使用GPIB与PM3000连接，读取和控制PM3000
#           建议使用19200bps的波特率，以提高通讯速率 
# Log:      20180110Pengy 
################################################################################
import sys,os
import time
import visa
import FLUKE_8808A1
import FLUKE_8808A2

####读取文件地址，使用RIDE时，改为ADD[1]；使用本地编译时改为ADD[0]####
ADD={0:'..\.',
     1:''
    }

add=ADD[0]
#####################################################

ERR_CODE = {1:'1:非法的功能码',
            2:'2:非法的数据地址',
            3:'3:非法的数据值',
            4:'4:服务/从机故障'
            }
########################################
#设置一个时间的格式
ISOTIME = '%Y-%m-%d %X'
########################################
class PM3000_GPIB():
    def __init__(self):
        os.chdir(os.path.split(os.path.realpath(__file__))[0])#回到当前脚本目录
##        self.bSerialIsOpen = False
##        self.ser = serial.Serial()
        self.rm = visa.ResourceManager()
        print(self.rm.list_resources())
##        self.inst = self.rm.open_resource('GPIB0::2::INSTR')
##        print self.inst.read(':FNC:CH1:VLT?')
##        self.inst.write(':FNC:CH1:VLT?')
##        print self.inst.read()
    #############定位到TestRecord文件夹#######################        
    def FindTestRecordFolder(self,testPath=''):
        '''
        通过RF传入的测试用例绝对路径，找到TestRecord路径，然后返回该路径
        '''
        newpath=''
        HeadTableList=testPath.split('\\')

        if 'test' not in HeadTableList:
            return os.path.abspath(testPath+os.path.sep+"..")+'\\TestRecord\\'
        else:
            for j in range(0,HeadTableList.index('test')):
                newpath=newpath+HeadTableList[j]+'\\'
            return newpath+'TestRecord\\'
##打开GPIB====================================================================================================================
    def Open_PM3000_GPIB(self,add):
        """
        根据仪器设置的地址进行设置
        例如：GPIB0::1::INSTR
        """
        flag=1
        add='GPIB0::'+add+'::INSTR'
        self.rm = visa.ResourceManager()
##        print(self.rm.list_resources())
##        self.SetCmd(u'选择ATHD')#选择ATHD
##        self.SetCmd(u'选择VTHD')#选择VTHD
##        print self.QueryPM3000Cmd(u'仪器查询命令')
        while(flag!=0 and flag<5):
            try:
                self.inst = self.rm.open_resource(add)
                print self.QueryPM3000Cmd(u'仪器查询命令','','no')
##                print self.inst.query('*IDN?').strip('\n')
                print u'打开PM3000的GPIB成功：',add
                flag=0
                
            except:
                print u'打开PM3000的GPIB失败'
                time.sleep(1)
                flag=flag+1
    def Clear_PM3000_GPIB(self):
        self.rm.clear()
#创建测试文档
##    def CreateTestDoc(self,name):
##        try:
##            savef=open(add+r'.\TestRecord\''+name+'.csv','a')
##            sStr=(u'时间,'+u'输入电压(V),'+u'输出电压(V),'
##                  +u'输出电流（A）,'+u'源效应,'+u'测试结果,'
##                  )
##            print>>savef,sStr.encode('gbk')
##            savef.close()
##        except:
##            print u'打开文件出错！'
            
#创建源效应测试文档
    def CreatePowerEffectDoc(self,testPath=''):
        try:
            if testPath=='':
                savef=open(add+r'.\TestRecord'+u'\源效应测试.csv','a')
            else:
                savef=open(self.FindTestRecordFolder(testPath)+u'源效应测试.csv','a')
            sStr=(u'时间,'+u'输入电压(V),'+u'输出电压(V),'
                  +u'输出电流（A）,'+u'源效应,'+u'测试结果,'
                  )
            print>>savef,sStr.encode('gbk')
            savef.close()
        except:
            print u'打开源效应测试文件出错！'

#创建负载效应测试文档
    def CreateLoadEffectDoc(self,testPath=''):
        """""""""""""""""""""""""""""""""""""""
        创建负载效应测试文档或者表头，在每测试完一组数据后，
        需要创建一次表头
        """""""""""""""""""""""""""""""""""""""
        try:
            if testPath=='':
                savef=open(add+r'.\TestRecord'+u'\负载效应测试.csv','a')
            else:
                savef=open(self.FindTestRecordFolder(testPath)+u'负载效应测试.csv','a') 
            sStr=(u'时间,'+u'输入电压(V),'+u'输出电压(V),'
                  +u'输出电流（A）,'+u'负载效应,'+u'测试结果,'
                  )
            print>>savef,sStr.encode('gbk')
            savef.close()
        except:
            print u'打开负载效应测试文件出错！'

#创建性能指标测试文档
    def CreatePerformanceTestDoc(self,testPath=''):
        try:
            if testPath=='':
                savef=open(add+r'.\TestRecord'+u'\性能指标测试.csv','a')
            else:
                savef=open(self.FindTestRecordFolder(testPath)+u'性能指标测试.csv','a')            

            sStr=(u'时间,'+u'输入电压(V),'+u'输出电压(V),'
                  +u'输出电流（A）,'+u'输出功率（W）,'+u'输入功率（W）,'+u'效率,'+u'效率测试结果,'
                  +u'A相PF,'+u'B相PF,'+u'C相PF,'+u'PF测试结果,'
                  +u'A相电流THD,'+u'B相电流THD,'+u'C相电流THD,'+u'电流THD测试结果'
                  )
            print>>savef,sStr.encode('gbk')
            savef.close()
        except:
            print u'打开性能指标测试文件出错！'

#创建稳压精度测试文档
    def CreateVoltagePrecisionDoc(self,testPath=''):
        try:
            if testPath=='':
                savef=open(add+r'.\TestRecord'+u'\稳压精度测试.csv','a')
            else:
                savef=open(self.FindTestRecordFolder(testPath)+u'稳压精度测试.csv','a')               

            sStr=(u'时间,'+u'输出电压设定值(V),'+u'输入电压(V),'
                  +u'输出电流（A）,'+u'输出电压(V),'
                  +u'稳压精度,'+u'测试结果,'
                  )
            print>>savef,sStr.encode('gbk')
            savef.close()
        except:
            print u'打开稳压精度测试文件出错！'
#创建稳流精度测试文档
    def CreateCurrentPrecisionDoc(self,testPath=''):
        try:
            if testPath=='':
                savef=open(add+r'.\TestRecord'+u'\稳流精度测试.csv','a')
            else:
                savef=open(self.FindTestRecordFolder(testPath)+u'稳流精度测试.csv','a')              

            sStr=(u'时间,'+u'输出电流设定值(V),'+u'输入电压(V),'
                  +u'输出电压(V),'+u'输出电流（A）,'
                  +u'稳流精度,'+u'测试结果,'
                  )
            print>>savef,sStr.encode('gbk')
            savef.close()
        except:
            print u'打开稳流精度测试文件出错！'
##设置FLUKE8808A电压模式======================================================================
    def SetVolMode(self):
        """""""""""""""""""""""""""""""""""""""
        设置FLUKE8808A电压模式
        """""""""""""""""""""""""""""""""""""""
        FLUKE_1=FLUKE_8808A1.FLUKE_8808A()
        FLUKE_2=FLUKE_8808A2.FLUKE_8808A()
        FLUKE_1.ConnectCom_FLUKE_8808A(4,9600)#测量电压
        FLUKE_2.ConnectCom_FLUKE_8808A(5,9600)#测量电流
        FLUKE_1.SetVolMode('VDC')
        FLUKE_2.SetVolMode('VDC')
        FLUKE_1.CloseCom_FLUKE_8808A()
        FLUKE_2.CloseCom_FLUKE_8808A()
        print u'设置FLUKE8808A电压模式成功！'
##使用FLUKE8808A测量输出电压电流功率=========================
    def ReadOutPut(self):
        """""""""""""""""""""""""""""""""""""""
        COM1:接FLUKE表测量电压
        COM2:接FLUKE表测量电流
        """""""""""""""""""""""""""""""""""""""
        Rlist=[]
        Voltage=''
        Current=''
        FLUKE_1=FLUKE_8808A1.FLUKE_8808A()
        FLUKE_2=FLUKE_8808A2.FLUKE_8808A()
        FLUKE_1.ConnectCom_FLUKE_8808A(4,9600)#测量电压
        FLUKE_2.ConnectCom_FLUKE_8808A(5,9600)#测量电流
        Voltage=round(float(FLUKE_1.ReadVol('VDC')),6)   #单位V
        Current=round(float(FLUKE_2.ReadVol('VDC'))*4*1000,6)   #单位A
        Power=round(float(Voltage*Current),6)   #单位W
        FLUKE_1.CloseCom_FLUKE_8808A()
        FLUKE_2.CloseCom_FLUKE_8808A()
        Rlist=[Voltage,Current,Power]#输出电压、电流、功率
##        print u'输出数据：',Rlist
        return Rlist
##等待电压达到指定值=============================================
    def WaitVolReachValue(self,Vol='700',waittime='10'):
        """""""""""""""""""""""""""""""""""""""
        等待电压达到指定值
        入参：指定电压
        返回结果
        """""""""""""""""""""""""""""""""""""""
        OutPutList=[]        
        cnt=0
        while(1):
            OutPutList=self.ReadOutPut()
            realVol=OutPutList[0]
            time.sleep(1)
            cnt=cnt+1
            if realVol>=float(Vol):
##                print '电压：',realVol
                return True
            elif cnt>=float(waittime):
                return False
#稳压精度测试=============================================
    def VoltagePrecisionTest(self,OutputVol='750',InputVol='380',testPath=''):
        """""""""""""""""""""""""""""""""""""""
        稳压精度测试
        生成测试文档
        OutputVol:给定的输出电压
        InputVol：给定的输入电压
        COM1:接FLUKE表测量电压
        COM2:接FLUKE表测量电流
        """""""""""""""""""""""""""""""""""""""
        OutPutList=[]
        OutPutList=self.ReadOutPut()
##组包，准备打印：
        OutStr=(time.strftime("%H:%M:%S")+','   #时间
                +OutputVol+','                   #输出电压设定值
                +InputVol+','                   #输入电压                
                +str(round(float(OutPutList[1]),3))+',' #输出电流
                +str(round(float(OutPutList[0]),3))+',' #输出电压
                +'####'+','                     #稳压精度
                +'####'+','                     #通过准则
                )

        print u'稳压精度测试数据：',OutStr

        try:
            if testPath=='':
                savef=open(add+r'.\TestRecord'+u'\稳压精度测试.csv','a')
            else:
                savef=open(self.FindTestRecordFolder(testPath)+u'稳压精度测试.csv','a')
            print>>savef,OutStr.encode('gbk')
            savef.close()
        except:
            print u'打开稳压精度测试文件出错！'    
##稳压精度测试结果判断=============================================
    def VoltagePrecisionJudge(self,rule='0.1',testPath=''):
        """""""""""""""""""""""""""""""""""""""
        读取负载效应数据文档，根据通过准则判断测试结果
        rule：通过准则
        """""""""""""""""""""""""""""""""""""""
        if testPath=='':
            f = open( add+r'.\TestRecord'+u'\稳压精度测试.csv', 'r')
        else:
            f = open( self.FindTestRecordFolder(testPath)+u'稳压精度测试.csv', 'r')
        csvlist = []
        tmplist=[]
        addlist=[]
        savelist=[]
        calclist=[]
        failtag=0
        testtag=0
        outStr=''
        csvlist = f.readlines()
        f.close()

        if len(csvlist)<10:
            print u'测试结果判断数据不全，请重新测试！1'
            return        
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')            
            if tmplist[0].decode('gbk')==u'时间':
                addlist.append(i)
##        print addlist
        for i in addlist:
##            print 't1',addlist,len(csvlist[i+1:i+10])
##            print 't2',csvlist[i+1:i+10]
            if (len(csvlist[i+1:i+10])>8 and
                csvlist[i+1][0:4].decode('gbk')!=u'时间' and
                csvlist[i+2][0:4].decode('gbk')!=u'时间' and
                csvlist[i+3][0:4].decode('gbk')!=u'时间' and
                csvlist[i+4][0:4].decode('gbk')!=u'时间' and
                csvlist[i+5][0:4].decode('gbk')!=u'时间' and
                csvlist[i+6][0:4].decode('gbk')!=u'时间' and
                csvlist[i+7][0:4].decode('gbk')!=u'时间' and
                csvlist[i+8][0:4].decode('gbk')!=u'时间' and
                csvlist[i+9][0:4].decode('gbk')!=u'时间' 
                ):
                calclist=[float(csvlist[i+1].split(',')[4]),float(csvlist[i+2].split(',')[4]),float(csvlist[i+3].split(',')[4]),
                          float(csvlist[i+4].split(',')[4]),float(csvlist[i+5].split(',')[4]),float(csvlist[i+6].split(',')[4]),
                          float(csvlist[i+7].split(',')[4]),float(csvlist[i+8].split(',')[4]),float(csvlist[i+9].split(',')[4])
                          ]
##                print calclist
                #计算稳压精度
                maxvolt=max(calclist)
                minvolt=min(calclist)
                DeltaVolt=max(abs(maxvolt-float(csvlist[i+5].split(',')[4])),abs(minvolt-float(csvlist[i+5].split(',')[4])))
##                print DeltaVolt
                VoltagePrecision=round(abs(DeltaVolt/float(csvlist[i+5].split(',')[4])*100),3)
                print u'稳压精度：',VoltagePrecision
                #判断稳压精度
                if VoltagePrecision>float(rule):
                    testreslut='FAIL'
                else:
                    testreslut='PASS'
                savelist=csvlist[i+9][0:-1].split(',')
                savelist[5]=str(VoltagePrecision)
                savelist[6]=testreslut
                csvlist[i+9]=','.join(savelist)+'\n'
                testtag+=1
        outStr=''.join(csvlist)[:-1]
##        print outStr

        try:
            if testPath=='':
                savef=open(add+r'.\TestRecord'+u'\稳压精度测试.csv','w')
            else:
                savef=open(self.FindTestRecordFolder(testPath)+u'稳压精度测试.csv','w')
            print>>savef,outStr
            savef.close()
        except:
            print u'打开稳压精度测试文件出错！'

        ##判断稳压精度测试结果是否通过
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
##            print tmplist,len(tmplist)
            if len(tmplist)>=7 and tmplist[6]=='FAIL':
                print u'测试不通过！'
                failtag=1
                return False
        if failtag==0 and testtag>0:
            print u'测试通过！'
            return True
        else:
            print u'测试结果判断数据不全，请重新测试！2'
            return False

##稳压精度测试结果判断2=============================================
    def VoltagePrecisionJudge2(self,rule='0.1',Sum='15',midvoltIndex='',testPath=''):
        """""""""""""""""""""""""""""""""""""""
        读取负载稳压精度数据文档，根据通过准则判断测试结果
        rule：通过准则
        Sum：每次测试的项目数
        midvoltIndex：中间电压值位置
        """""""""""""""""""""""""""""""""""""""
        if testPath=='':
            f = open( add+r'.\TestRecord'+u'\稳压精度测试.csv', 'r')
        else:
            f = open( self.FindTestRecordFolder(testPath)+u'稳压精度测试.csv', 'r')
        csvlist = []
        tmplist=[]
        addlist=[]
        savelist=[]
        calclist=[]
        failtag=0
        testtag=0
        cnt=0
        outStr=''
        csvlist = f.readlines()
        f.close()
        
##        if Sum!='15' and Sum!='9':
##            print u'测试方法错误，未达到指定数据量',Sum
##            return False

        if len(csvlist)<=int(Sum):
            print u'测试结果判断数据不全，请重新测试！1'
            return
        
        #获取每次测试的地址
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')            
            if tmplist[0].decode('gbk')==u'时间':
                addlist.append(i)
##        print addlist
        
        for i in addlist:
            #判断测试条件是否满足要求
            if (len(csvlist[i+1:])<int(Sum)):
                print u'该条件测试数据不全！第',i+1,u'行'
                continue
                
            #判断本次测试条件数据是否完整
            for x in range(1,int(Sum)+1):
##                print csvlist[i+x][0:4].decode('gbk'),i
                if csvlist[i+x][0:4].decode('gbk')==u'时间':                    
                    cnt+=1
##            print cnt

            if cnt!=0:
                cnt=0
                print u'该条件测试数据不全！第',i+1,u'行'
                return False
            elif cnt==0:          #如果本次测试完整，则进行公式计算
                for y in range(1,int(Sum)+1):
                    calclist.append(float(csvlist[i+y].split(',')[4]))
                print u'本次测试结果：',calclist
                #计算稳压精度
                maxvolt=max(calclist)
                minvolt=min(calclist)
                if midvoltIndex=='':
                    midvolt=float(csvlist[i+(int(Sum)+1)/2].split(',')[4])
                elif int(midvoltIndex)<=int(Sum):
                    midvolt=float(csvlist[i+int(midvoltIndex)].split(',')[4])
                    print 'midvolt:',midvolt
                else:
                    print u'中间取值超出范围','Sum:',int(Sum),'midvoltIndex:',int(midvoltIndex)
                    return False
                DeltaVolt=max(abs(maxvolt-midvolt),abs(minvolt-midvolt))
##                print DeltaVolt
                VoltagePrecision=round(abs(DeltaVolt/midvolt*100),3)
                print u'稳压精度：',VoltagePrecision
                #判断稳压精度
                if VoltagePrecision>float(rule):
                    testreslut='FAIL'
                else:
                    testreslut='PASS'
                savelist=csvlist[i+int(Sum)][0:-1].split(',')
                savelist[5]=str(VoltagePrecision)
                savelist[6]=testreslut
                csvlist[i+int(Sum)]=','.join(savelist)+'\n'
                testtag+=1
                calclist=[]
            else:
                cnt=0 #将计算清空
        outStr=''.join(csvlist)[:-1]
##        print outStr

        try:
            if testPath=='':
                savef=open(add+r'.\TestRecord'+u'\稳压精度测试.csv','w')
            else:
                savef=open(self.FindTestRecordFolder(testPath)+u'稳压精度测试.csv','w')
            print>>savef,outStr
            savef.close()
        except:
            print u'打开稳压精度测试文件出错！'

        ##判断稳压精度测试结果是否通过
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
##            print tmplist,len(tmplist)
            if len(tmplist)>=7 and tmplist[6].strip('\n')=='FAIL':
                print u'测试不通过！'
                failtag=1
                return False
        if failtag==0 and testtag>0:
            print u'测试通过！'
            return True
        else:
            print u'测试结果判断数据不全，请重新测试！2'
            return False

#稳流精度测试=============================================
    def CurrentPrecisionTest(self,OutputCur='750',InputVol='380',testPath=''):
        """""""""""""""""""""""""""""""""""""""
        稳流精度测试
        生成测试文档
        OutputCur:输出电流给定值
        InputVol：输入电压
        COM1:接FLUKE表测量电压
        COM2:接FLUKE表测量电流
        """""""""""""""""""""""""""""""""""""""
        OutPutList=[]
        OutPutList=self.ReadOutPut()
##组包，准备打印：
        OutStr=(time.strftime("%H:%M:%S")+','   #时间
                +OutputCur+','                   #输出电流设定值
                +InputVol+','                   #输入电压                
                +str(round(float(OutPutList[0]),3))+',' #输出电压
                +str(round(float(OutPutList[1]),3))+',' #输出电流
                +'####'+','                     #稳流精度
                +'####'+','                     #通过准则
                )

        print u'稳流精度测试数据：',OutStr

        try:
            if testPath=='':
                savef=open(add+r'.\TestRecord'+u'\稳流精度测试.csv','a')
            else:
                savef=open(self.FindTestRecordFolder(testPath)+u'稳流精度测试.csv','a')
            print>>savef,OutStr.encode('gbk')
            savef.close()
        except:
            print u'打开稳流精度测试文件出错！'    
##稳流精度测试结果判断=============================================
    def CurrentPrecisionJudge(self,rule='0.1',testPath=''):
        """""""""""""""""""""""""""""""""""""""
        读取稳流精度数据文档，根据通过准则判断测试结果
        rule：通过准则
        """""""""""""""""""""""""""""""""""""""
        if testPath=='':
            f = open( add+r'.\TestRecord'+u'\稳流精度测试.csv', 'r')
        else:
            f = open( self.FindTestRecordFolder(testPath)+u'稳流精度测试.csv', 'r')
        csvlist = []
        tmplist=[]
        addlist=[]
        savelist=[]
        calclist=[]
        failtag=0
        testtag=0
        outStr=''
        csvlist = f.readlines()
        f.close()
        if len(csvlist)<10:
            print u'测试结果判断数据不全，请重新测试！'
            return False
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')            
            if tmplist[0].decode('gbk')==u'时间':
                addlist.append(i)
##        print addlist
        for i in addlist:
##            print len(csvlist[i+1:i+9])
            if (len(csvlist[i+1:i+10])>8 and
                csvlist[i+1][0:4].decode('gbk')!=u'时间' and
                csvlist[i+2][0:4].decode('gbk')!=u'时间' and
                csvlist[i+3][0:4].decode('gbk')!=u'时间' and
                csvlist[i+4][0:4].decode('gbk')!=u'时间' and
                csvlist[i+5][0:4].decode('gbk')!=u'时间' and
                csvlist[i+6][0:4].decode('gbk')!=u'时间' and
                csvlist[i+7][0:4].decode('gbk')!=u'时间' and
                csvlist[i+8][0:4].decode('gbk')!=u'时间' and
                csvlist[i+9][0:4].decode('gbk')!=u'时间' 
                ):
                calclist=[float(csvlist[i+1].split(',')[4]),float(csvlist[i+2].split(',')[4]),float(csvlist[i+3].split(',')[4]),
                          float(csvlist[i+4].split(',')[4]),float(csvlist[i+5].split(',')[4]),float(csvlist[i+6].split(',')[4]),
                          float(csvlist[i+7].split(',')[4]),float(csvlist[i+8].split(',')[4]),float(csvlist[i+9].split(',')[4])
                          ]
##                print calclist
                #计算稳流精度
                maxCurr=max(calclist)
                minCurr=min(calclist)
                DeltaCurr=max(abs(maxCurr-float(csvlist[i+5].split(',')[4])),abs(minCurr-float(csvlist[i+5].split(',')[4])))
##                print DeltaCurr
                CurrentPrecision=round(abs(DeltaCurr/float(csvlist[i+5].split(',')[4])*100),3)
                print u'稳流精度：',CurrentPrecision
                #判断稳流精度
                if CurrentPrecision>float(rule):
                    testreslut='FAIL'
                else:
                    testreslut='PASS'
                savelist=csvlist[i+9][0:-1].split(',')
##                print 't1',savelist
                savelist[5]=str(CurrentPrecision)
                savelist[6]=testreslut
                csvlist[i+9]=','.join(savelist)+'\n'
                testtag+=1

        outStr=''.join(csvlist)[:-1]
##        print testtag

        try:
            if testPath=='':
                savef=open(add+r'.\TestRecord'+u'\稳流精度测试.csv','w')
            else:
                savef=open(self.FindTestRecordFolder(testPath)+u'稳流精度测试.csv','w')
            print>>savef,outStr
            savef.close()
        except:
            print u'打开稳流精度测试文件出错！'

        ##判断稳流精度测试结果是否通过
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
##            print tmplist,len(tmplist)
            if len(tmplist)>=7 and tmplist[6]=='FAIL':
                print u'测试不通过！'
                failtag=1
                return False
        if failtag==0 and testtag>0:
            print u'测试通过！'
            return True
        else:
            print u'测试结果判断数据不全，请重新测试！'
            return False

##稳流精度测试结果判断2=============================================
    def CurrentPrecisionJudge2(self,rule='0.1',Sum='15',midCurrIndex='',testPath=''):
        """""""""""""""""""""""""""""""""""""""
        读取负载稳流精度数据文档，根据通过准则判断测试结果
        rule：通过准则
        Sum：每次测试的项目数
        midCurrIndex：中间电流值位置
        """""""""""""""""""""""""""""""""""""""
        if testPath=='':
            f = open( add+r'.\TestRecord'+u'\稳流精度测试.csv', 'r')
        else:
            f = open( self.FindTestRecordFolder(testPath)+u'稳流精度测试.csv', 'r')
        csvlist = []
        tmplist=[]
        addlist=[]
        savelist=[]
        calclist=[]
        failtag=0
        testtag=0
        cnt=0
        outStr=''
        csvlist = f.readlines()
        f.close()
        
##        if Sum!='15' and Sum!='9':
##            print u'测试方法错误，未达到指定数据量',Sum
##            return False

        if len(csvlist)<=int(Sum):
            print u'测试结果判断数据不全，请重新测试！1'
            return
        
        #获取每次测试的地址
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')            
            if tmplist[0].decode('gbk')==u'时间':
                addlist.append(i)
##        print addlist
        
        for i in addlist:
            #判断测试条件是否满足要求
            if (len(csvlist[i+1:])<int(Sum)):
                print u'该条件测试数据不全！第',i+1,u'行'
                continue
                
            #判断本次测试条件数据是否完整
            for x in range(1,int(Sum)+1):
##                print csvlist[i+x][0:4].decode('gbk'),i
                if csvlist[i+x][0:4].decode('gbk')==u'时间':                    
                    cnt+=1
##            print cnt

            if cnt!=0:
                cnt=0
                print u'该条件测试数据不全！第',i+1,u'行'
                return False
            elif cnt==0:          #如果本次测试完整，则进行公式计算
                for y in range(1,int(Sum)+1):
                    calclist.append(float(csvlist[i+y].split(',')[4]))
                print u'本次测试结果：',calclist
                #计算稳流精度
                maxCurr=max(calclist)
                minCurr=min(calclist)
                if midCurrIndex=='':
                    midCurr=float(csvlist[i+(int(Sum)+1)/2].split(',')[4])
                elif int(midCurrIndex)<=int(Sum):
                    midCurr=float(csvlist[i+int(midCurrIndex)].split(',')[4])
##                print 'midCurr:',midCurr
                else:
                    print u'中间取值超出范围','Sum:',int(Sum),'midCurrIndex:',int(midCurrIndex)
                    return False
                DeltaCurr=max(abs(maxCurr-midCurr),abs(minCurr-midCurr))
##                print DeltaVolt
                CurrentPrecision=round(abs(DeltaCurr/midCurr*100),3)
                print u'稳流精度：',CurrentPrecision
                #判断稳流精度
                if CurrentPrecision>float(rule):
                    testreslut='FAIL'
                else:
                    testreslut='PASS'
                savelist=csvlist[i+int(Sum)][0:-1].split(',')
                savelist[5]=str(CurrentPrecision)
                savelist[6]=testreslut
                csvlist[i+int(Sum)]=','.join(savelist)+'\n'
                testtag+=1
                calclist=[]
            else:
                cnt=0 #将计算清空
        outStr=''.join(csvlist)[:-1]
##        print outStr

        try:
            if testPath=='':
                savef=open(add+r'.\TestRecord'+u'\稳流精度测试.csv','w')
            else:
                savef=open(self.FindTestRecordFolder(testPath)+u'稳流精度测试.csv','w')
            print>>savef,outStr
            savef.close()
        except:
            print u'打开稳流精度测试文件出错！'

        ##判断稳流精度测试结果是否通过
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
##            print tmplist,len(tmplist),tmplist[6],tmplist[6].strip('\n')=='FAIL'
            if len(tmplist)>=7 and tmplist[6].strip('\n')=='FAIL':
                print u'测试不通过！'
                failtag=1
                return False
        if failtag==0 and testtag>0:
            print u'测试通过！'
            return True
        else:
            print u'测试结果判断数据不全，请重新测试！2'
            return False


#负载效应测试=============================================
    def LoadEffectTest(self,InputVol='380',testPath=''):
        """""""""""""""""""""""""""""""""""""""
        负载效应测试
        生成测试文档
        COM1:接FLUKE表测量电压
        COM2:接FLUKE表测量电流
        """""""""""""""""""""""""""""""""""""""
        OutPutList=[]
        OutPutList=self.ReadOutPut()
##组包，准备打印：
        OutStr=(time.strftime("%H:%M:%S")+','   #时间
                +InputVol+','                   #设置的输入电压   
                +str(round(float(OutPutList[0]),3))+',' #输出电压
                +str(round(float(OutPutList[1]),3))+',' #输出电流
                +'####'+','
                +'####'+','
                )

        print u'负载效应测试数据：',OutStr

        try:
            if testPath=='':
                savef=open(add+r'.\TestRecord'+u'\负载效应测试.csv','a')
            else:
                savef=open(self.FindTestRecordFolder(testPath)+u'负载效应测试.csv','a')
            print>>savef,OutStr.encode('gbk')
            savef.close()
        except:
            print u'打开负载效应测试文件出错！'        

##负载效应测试结果判断=============================================
    def LoadEffectJudge(self,rule='0.1',testPath=''):
        """""""""""""""""""""""""""""""""""""""
        读取负载效应数据文档，根据通过准则判断测试结果
        rule：负载效应通过准则
        """""""""""""""""""""""""""""""""""""""
        if testPath=='':
            f = open( add+r'.\TestRecord'+u'\负载效应测试.csv', 'r')
        else:
            f = open( self.FindTestRecordFolder(testPath)+u'负载效应测试.csv', 'r')
        csvlist = []
        tmplist=[]
        addlist=[]
        savelist=[]
        calclist=[]
        failtag=0
        outStr=''
        csvlist = f.readlines()
        f.close()
        
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')            
            if tmplist[0].decode('gbk')==u'时间':
                addlist.append(i)
##        print addlist
        for i in addlist:
            if (csvlist[i+1][0:4].decode('gbk')!=u'时间' and
                csvlist[i+2][0:4].decode('gbk')!=u'时间' and
                csvlist[i+3][0:4].decode('gbk')!=u'时间' 
                ):
                calclist=[csvlist[i+1].split(','),csvlist[i+2].split(','),csvlist[i+3].split(',')]
##                print calclist
                voltage1=float(calclist[0][2])
                voltage2=float(calclist[1][2])
                voltage3=float(calclist[2][2])
                maxvolt=max(voltage1,voltage2,voltage3)
                minvolt=min(voltage1,voltage2,voltage3)
                LoadEffect=round(abs(maxvolt-minvolt)/voltage1*100,3)
                if LoadEffect>float(rule):
                    testreslut='FAIL'
                else:
                    testreslut='PASS'
                savelist=csvlist[i+3][0:-1].split(',')
                savelist[4]=str(LoadEffect)
                savelist[5]=testreslut
                csvlist[i+3]=','.join(savelist)+'\n'

        outStr=''.join(csvlist)


        try:
            if testPath=='':
                savef=open(add+r'.\TestRecord'+u'\负载效应测试.csv','w')
            else:
                savef=open(self.FindTestRecordFolder(testPath)+u'负载效应测试.csv','w')
            print>>savef,outStr
            savef.close()
        except:
            print u'打开负载效应测试文件出错！'

        ##判断负载效应结果是否通过
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
##            print tmplist,len(tmplist)
            if len(tmplist)>=7 and tmplist[5]=='FAIL':
                print u'测试不通过！'
                failtag=1
                return False
        if failtag==0:
            print u'测试通过！'
            return True
        
#源效应测试=============================================
    def PowerEffectTest(self,InputVol='380',testPath=''):
        """""""""""""""""""""""""""""""""""""""
        源效应测试
        生成测试文档
        COM1:接FLUKE表测量电压
        COM2:接FLUKE表测量电流
        """""""""""""""""""""""""""""""""""""""
        OutPutList=[]
        OutPutList=self.ReadOutPut()
##组包，准备打印：
        OutStr=(time.strftime("%H:%M:%S")+','   #时间
                +InputVol+','                   #设置的输入电压   
                +str(round(float(OutPutList[0]),3))+',' #输出电压
                +str(round(float(OutPutList[1]),3))+',' #输出电流
                +'####'+','
                +'####'+','
                )

        print u'源效应测试数据：',OutStr

        try:
            if testPath=='':
                savef=open(add+r'.\TestRecord'+u'\源效应测试.csv','a')
            else:
                savef=open(self.FindTestRecordFolder(testPath)+u'源效应测试.csv','a')
            print>>savef,OutStr.encode('gbk')
            savef.close()
        except:
            print u'打开源效应测试文件出错！'        

##源效应测试结果判断=============================================
    def PowerEffectJudge(self,rule='-0.1',testPath=''):
        """""""""""""""""""""""""""""""""""""""
        读取源效应数据文档，根据通过准则判断测试结果
        rule：源效应通过准则
        """""""""""""""""""""""""""""""""""""""
        if testPath=='':
            f = open( add+r'.\TestRecord'+u'\源效应测试.csv', 'r')
        else:
            f = open( self.FindTestRecordFolder(testPath)+u'源效应测试.csv', 'r')
        csvlist = []
        tmplist=[]
        addlist=[]
        savelist=[]
        calclist=[]
        outStr=''
        failtag=0
        csvlist = f.readlines()
        f.close()
        
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')            
            if tmplist[0].decode('gbk')==u'时间':
                addlist.append(i)
##        print addlist
        for i in addlist:
            if (csvlist[i+1][0:4].decode('gbk')!=u'时间' and
                csvlist[i+2][0:4].decode('gbk')!=u'时间' and
                csvlist[i+3][0:4].decode('gbk')!=u'时间' 
                ):
                calclist=[csvlist[i+1].split(','),csvlist[i+2].split(','),csvlist[i+3].split(',')]
##                print calclist
                voltage1=float(calclist[0][2])
                voltage2=float(calclist[1][2])
                voltage3=float(calclist[2][2])
                maxvolt=max(voltage1,voltage2,voltage3)
                minvolt=min(voltage1,voltage2,voltage3)
                LoadEffect=round(abs(maxvolt-minvolt)/voltage1*100,3)
                if LoadEffect>float(rule):
                    testreslut='FAIL'
                else:
                    testreslut='PASS'
                savelist=csvlist[i+3][0:-1].split(',')
                savelist[4]=str(LoadEffect)
                savelist[5]=testreslut
                csvlist[i+3]=','.join(savelist)+'\n'

        outStr=''.join(csvlist)
    

        try:
            if testPath=='':
                savef=open(add+r'.\TestRecord'+u'\源效应测试.csv','w')
            else:
                savef=open(self.FindTestRecordFolder(testPath)+u'源效应测试.csv','w')
            print>>savef,outStr
            savef.close()
        except:
            print u'打开源效应测试文件出错！'

        ##判断源效应测试结果是否通过
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
##            print tmplist,len(tmplist)
            if len(tmplist)>=7 and tmplist[5]=='FAIL':
                print u'测试不通过！'
                failtag=1
                return False
        if failtag==0:
            print u'测试通过！'
            return True
##性能指标测试=============================================
    def PerformanceTest(self,Efficiency='0.95',PF='0.95',THDA='12',testPath=''):
        """""""""""""""""""""""""""""""""""""""
        性能指标测试
        生成测试文档
        Efficiency:效率测试通过准则
        PF:PF通过准则
        THDA：电流THD通过准则
        COM1:接FLUKE表测量电压
        COM2:接FLUKE表测量电流
        """""""""""""""""""""""""""""""""""""""
        print u'=========性能指标测试============'
        OutPutList=[]
        eff=0
        PFResult='PASS'
        THDAResult='PASS'
##        self.SetVolMode()
        OutPutList=self.ReadOutPut()
##        print OutPutList
        ReadList1=self.ReadCH('CH1')
        ReadList2=self.ReadCH('CH2')
        ReadList3=self.ReadCH('CH3')
        ReadList4=self.ReadCH('SUM')
##        self.SetCmd(u'设置接线方式','3P4','no')
##        time.sleep(1)
##        self.SetCmd(u'选择通道','CH1','no')
##        time.sleep(2)#等2s再读数据
##        ReadStr=self.QueryPM3000Cmd(u'读CH1的数据','','no')
##        ReadList1=ReadStr.strip('\n').split(',') #转为列表
##        self.SetCmd(u'选择通道','CH2','no')
##        time.sleep(2)#等2s再读数据
##        ReadStr=self.QueryPM3000Cmd(u'读CH2的数据','','no')
##        ReadList2=ReadStr.strip('\n').split(',') #转为列表        
##        self.SetCmd(u'选择通道','CH3','no')
##        time.sleep(2)#等2s再读数据
##        ReadStr=self.QueryPM3000Cmd(u'读CH3的数据','','no')
##        ReadList3=ReadStr.strip('\n').split(',') #转为列表
##        self.SetCmd(u'选择通道','SUM','no')
##        time.sleep(2)#等2s再读数据
##        ReadStr=self.QueryPM3000Cmd(u'读SUM的数据','','no')
##        ReadList4=ReadStr.strip('\n').split(',') #转为列表
##计算效率：
        if float(ReadList4[0])!=0:
            eff=round(OutPutList[2]/float(ReadList4[0]),4)   #效率
            print u'转换效率为：',eff
        else:
            print u'输入功率为0，请检查是否开机,输入功率为：',float(ReadList4[0])
            return False
##PF判断
        MaxPF=max(round(float(ReadList1[5]),2),round(float(ReadList2[5]),2),round(float(ReadList3[5]),2))
        if MaxPF<float(PF):
            PFResult='FAIL'
        else:
            PFResult='PASS'
##电流THD判断
        MaxTHDA=max(round(float(ReadList1[14]),2),round(float(ReadList2[14]),2),round(float(ReadList3[14]),2))
        if MaxTHDA>float(THDA):
            THDAResult='FAIL'
        else:
            THDAResult='PASS'            
##效率判断
        if eff<float(Efficiency):
            EfficiencyResult='FAIL'
        else:
            EfficiencyResult='PASS' 

##组包，准备打印：
        OutStr=(time.strftime("%H:%M:%S")+','   #时间
                +str(round(float(ReadList1[3]),2))+','               #输入电压
                +str(OutPutList[0])+','         #输出电压
                +str(OutPutList[1])+','         #输出电流
                +str(round(OutPutList[2],2))+','         #输出功率
                +str(round(float(ReadList4[0]),2))+','               #输入功率
                +str(eff)+','                   #效率
                +EfficiencyResult+','           #效率测试结果
                +str(round(float(ReadList1[5]),4))+','               #A相PF
                +str(round(float(ReadList2[5]),4))+','               #B相PF
                +str(round(float(ReadList3[5]),4))+','               #C相PF
                +PFResult+',' 
                +str(round(float(ReadList1[14]),3))+','              #A相电流THD
                +str(round(float(ReadList2[14]),3))+','              #B相电流THD
                +str(round(float(ReadList3[14]),3))+','              #C相电流THD
                +THDAResult+',' 
                )

        print u'性能指标数据：',OutStr

        try:
            if testPath=='':
                savef=open(add+r'.\TestRecord'+u'\性能指标测试.csv','a')
            else:
                savef=open(self.FindTestRecordFolder(testPath)+u'性能指标测试.csv','a')
            print>>savef,OutStr.encode('gbk')
            savef.close()
        except:
            print u'打开性能指标测试文件出错！'



##PF测试结果判断=============================================
    def PFTestJudge(self,testPath=''):
        """""""""""""""""""""""""""""""""""""""
        读取性能指标数据文档，根据通过准则判断测试结果
        """""""""""""""""""""""""""""""""""""""
        if testPath=='':
            f = open( add+r'.\TestRecord'+u'\性能指标测试.csv', 'r')
        else:
            f = open( self.FindTestRecordFolder(testPath)+u'性能指标测试.csv', 'r')
        csvlist = []
        tmplist=[]
        savelist=[]
        csvlist = f.readlines()
        f.close()
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
##            print tmplist
            if tmplist[11]=='FAIL':
                savelist.append(tmplist)

        if savelist!=[]:
            print u'有',len(savelist),u'条测试不通过'
            print savelist
            return False
        else:
            print u'测试通过！'
            return True
##THD测试结果判断=============================================
    def THDTestJudge(self,testPath=''):
        """""""""""""""""""""""""""""""""""""""
        读取性能指标数据文档，根据通过准则判断测试结果
        """""""""""""""""""""""""""""""""""""""
        if testPath=='':
            f = open( add+r'.\TestRecord'+u'\性能指标测试.csv', 'r')
        else:
            f = open( self.FindTestRecordFolder(testPath)+u'性能指标测试.csv', 'r')
        csvlist = []
        tmplist=[]
        savelist=[]
        csvlist = f.readlines()
        f.close()
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
##            print tmplist[15]
            if tmplist[15].strip('\n')=='FAIL':
                savelist.append(tmplist)

        if savelist!=[]:
            print u'有',len(savelist),u'条测试不通过'
            print savelist
            return False
        else:
            print u'测试通过！'
            return True
##转换效率测试结果判断=============================================
    def EffTestJudge(self,testPath=''):
        """""""""""""""""""""""""""""""""""""""
        读取性能指标数据文档，根据通过准则判断测试结果
        """""""""""""""""""""""""""""""""""""""
        if testPath=='':
            f = open( add+r'.\TestRecord'+u'\性能指标测试.csv', 'r')
        else:
            f = open( self.FindTestRecordFolder(testPath)+u'性能指标测试.csv', 'r')
        csvlist = []
        tmplist=[]
        savelist=[]
        csvlist = f.readlines()
        f.close()
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
##            print tmplist
            if tmplist[7]=='FAIL':
                savelist.append(tmplist)

        if savelist!=[]:
            print u'有',len(savelist),u'条测试不通过'
            print savelist
            return False
        else:
            print u'测试通过！'
            return True        
##最大效率测试结果判断=============================================
    def MaxEffTestJudge(self,MaxEff='0.96',testPath=''):
        """""""""""""""""""""""""""""""""""""""
        读取性能指标数据文档，根据通过准则判断测试结果
        """""""""""""""""""""""""""""""""""""""
        if testPath=='':
            f = open( add+r'.\TestRecord'+u'\性能指标测试.csv', 'r')
        else:
            f = open( self.FindTestRecordFolder(testPath)+u'性能指标测试.csv', 'r')
        csvlist = []
        tmplist=[]
        savelist=[]
        csvlist = f.readlines()
        f.close()
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
##            print tmplist
            if tmplist[6].decode('gbk')!=u'效率':
                savelist.append(tmplist[6])

        print u'最大转换效率为：',max(savelist)
        if max(savelist)<float(MaxEff):
            print u'测试不通过'
            return False
        else:
            print u'测试通过！'
            return True 

##电压THD测试=============================================
    def THDVTest(self):
        """""""""""""""""""""""""""""""""""""""
        电压THD测试
        COM1:接FLUKE表测量电压
        COM2:接FLUKE表测量电流
        """""""""""""""""""""""""""""""""""""""
        OutPutList=[]
        ReadList1=self.ReadCH('CH1')
        ReadList2=self.ReadCH('CH2')
        ReadList3=self.ReadCH('CH3')
        OutPutList.append(ReadList1[13])
        OutPutList.append(ReadList2[13])
        OutPutList.append(ReadList3[13])

##        print u'输出电压THD为：',ReadList1[13],ReadList2[13],ReadList3[13]

        return OutPutList

##读通道所有数据=============================================
    def ReadCH(self, CH='CH1'):
        """""""""""""""""""""""""""""""""""""""
        CH:选择读取哪个通道数据
        可设置：CH1、CH2、CH3、CHN、SUM
        返回一个列表，内容如下：
        0. Watts 1. VA 2. VAr 3. Vrms 4. Arms 5. PF 6. Vpeak 7. Apeak
        8. Vcf 9.Acf 10.Imp 13.Vthd 14.Athd 15.Frq

        """""""""""""""""""""""""""""""""""""""
        flag=1
        ReadStr=''
        ReadList1=[]
        ReadList=[]
        time.sleep(1)
##        print u'====读数据====='

            
        self.SetCmd(u'设置接线方式','3P4','no')

        self.SetCmd(u'选择ATHD','','no')#选择ATHD
        self.SetCmd(u'选择VTHD','','no')#选择VTHD
  
        self.SetCmd(u'选择通道',CH,'no')
        time.sleep(5)#等2s再读数据
        while(flag!=0): #防止频率读到0值
            flag=flag+1
            ReadStr=self.QueryPM3000Cmd(u'读'+CH+u'的数据','','no')
            ReadList=ReadStr.split(',') #转为列表
##            print ReadList
            if len(ReadList)<16 and CH!='SUM':
                print u'PM3000收到的频率数据异常！',ReadList
                time.sleep(1)
                continue
##            print float(ReadList[15]),float(ReadList[15])!=0
            
            if CH!='SUM' and float(ReadList[15])!=0:
                flag=0
            elif CH=='SUM':
                flag=0

            if flag>5:
                print u'读到的频率一直为0，测试失败！',ReadList
                return False
            

        for i in ReadList:
            ReadList1.append(str(float(i)))
        return ReadList1
##        return ReadList

##查询命令=============================================
    def QueryPM3000Cmd(self, sComment=':BSR?',value='',printflag=''):     
        f = open( add+r'.\Library\PM3000_GPIB\PM3000_command_list.csv', 'r')
        csvlist = []
        csvlist = f.readlines()
        f.close()
        receiveData=''
        returnStr=''
        tmplist=[]
        flag=1
        flag0=1
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
            if tmplist[0] == sComment.encode('gbk'):
                sCmd = tmplist[1].strip('\n')+value   #查找到命令

        if printflag=='':
            print u'=======',tmplist[0].decode('gbk'),value,'========='
        while(flag!=0):
            try:
                returnStr=self.inst.query(sCmd)#查询命令用query
                flag=0
            except :
                flag=flag+1  #出错了重发
                print u'PM3000发送查询通讯超时！flag=',flag#先复位
                while(flag0):
                    try:
                        self.inst.write('*RST')
                        time.sleep(3)
                        self.inst.write(':PRG:LOD 1')
                        print 'RST！'
                        time.sleep(3)
                        flag0=0
                    except:                        
                        flag0+=1
                        print u'PM3000发送设备复位命令失败！flag0=',flag0
                        time.sleep(3)
                    if flag0>3:
                        print u'PM3000查询命令时，发送设备复位命令多次失败，请检查设备连接！',sComment
                        self.inst.clear()
                        time.sleep(3)
                        break
                     
##                time.sleep(3)
            if flag>4:
                try:
                    print u'PM3000重新连接后查询！'
                    self.inst.clear()
                    time.sleep(3)
                    returnStr=self.inst.query(sCmd)#查询命令用query
                except:
                    print u'PM3000通讯异常，请检查设备连接',sComment
                    return False

        if printflag=='':
            print u'PM3000收到数据为：',returnStr.strip('\n')
        return returnStr.strip('\n')

##设置命令=============================================
    def SetCmd(self, sComment,value='',printflag=''):     
        f = open( add+r'.\Library\PM3000_GPIB\PM3000_command_list.csv', 'r')
        csvlist = []
        csvlist = f.readlines()
        f.close()
        receiveData=''
        returnList=[]
        tmplist=[]
        flag=1
        flag0=1
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
            if tmplist[0] == sComment.encode('gbk'):
                sCmd = tmplist[1].strip('\n')+' '+value   #查找到命令
                
        if printflag=='':
            print u'=======',tmplist[0].decode('gbk'),value,'========='
        while(flag!=0):
            try:
                self.inst.write(sCmd)#设置命令用write
                flag=0
            except:
                flag=flag+1  #出错了重发
                print u'PM3000发送设置命令通讯超时！',flag
                while(flag0):
                    try:
                        self.inst.write('*RST')
                        time.sleep(3)
                        self.inst.write(':PRG:LOD 1')
                        time.sleep(3)
                        flag0=0
                    except:
##                        print u'发送设备复位命令失败！'
                        flag0+=1
                        time.sleep(3)
                    if flag0>3:
                        print u'PM3000设置命令时，发送设备复位命令多次失败，请检查设备连接！',sComment
                        self.inst.clear()
                        time.sleep(3)
                        break
##                time.sleep(3)
            if flag>4:
                print u'PM3000通讯异常，请检查设备连接',sComment
                return False


#======================================================================================================    
if __name__ == "__main__":
    myapp = PM3000_GPIB()
    myapp.Open_PM3000_GPIB('2')
    print myapp.ReadCH('SUM')
##    myapp.inst.clear()
##    time.sleep(3)
    
##    myapp.VoltagePrecisionJudge()
##    flag=1
##    while(1):
##    print myapp.ReadOutPut()
####        while(flag!=0 and flag<3):
##        try:
##            print myapp.inst.write(':SEL:CH1')
##            print myapp.inst.write(':SEL:FRQ')
##            print myapp.inst.write(':BSE 4')
##            print myapp.inst.write('*TRG')
##            
##            print myapp.inst.query(':BRD:CH1?').strip('\n')#查询命令用query
####            print myapp.inst.query('*WAI?').strip('\n')
##            time.sleep(0.5)
##        except :
##            flag=flag+1  #出错了重发
##            print u'通讯超时！'
##            time.sleep(1)
##    while(1):
##        
####        myapp.QueryPM3000Cmd(u'读CH1','FRQ?')
####        print myapp.inst.write(':DSE 2')
####        print myapp.inst.query(':DSR?')
####        print myapp.inst.query(':FNC:CH1:FRQ?')
####        time.sleep(1)
##    print myapp.ReadOutPut()
####        time.sleep(1)
##        myapp.ReadCH('CH3')
####        time.sleep(1)

##    myapp.sendBySerial(':FNC:CH1:VLT?\r')
####    myapp.sendBySerial('\n')
##    myapp.QueryCmd(u'选择CH1')

##    print myapp.inst.write('*RST')
##    for i in range(0,5):
##        myapp.CreatePowerEffectDoc()
##        for i in range(0,3):
##    myapp.PowerEffectTest() 
##    myapp.CreateCurrentPrecisionDoc()
##    for i in range(0,9):
##        time.sleep(0.5)
##        myapp.CurrentPrecisionTest()
##    myapp.PerformanceTest()
##    myapp.THDTestJudge()
##    myapp.CreateTestDoc('test')
