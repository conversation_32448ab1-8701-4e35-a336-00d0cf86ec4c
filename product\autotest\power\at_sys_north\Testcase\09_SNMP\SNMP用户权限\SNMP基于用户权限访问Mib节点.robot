*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
SNMP用户只读权限测试
    [Setup]    北向协议配置_修改V3用户参数    zteuser    SNMP权限范围    只读
    Comment    连接SNMP_V3    zteuser1    PowerAuth123!    PowerPri123!    MD5    DES
    北向协议配置_读取V3用户参数
    #GET权限
    # ${比较结果}    对比数据_V2C    cSU1analogData
    # should be true    ${比较结果}
    ${比较结果}    对比数据_V2C    cSU1digitalData
    should be true    ${比较结果}
    ${比较结果}    对比告警级别_V2C    cSU1alarm
    should be true    ${比较结果}
    ${比较结果}    对比告警干接点_V2C    cSU1alarm
    should be true    ${比较结果}
    ${比较结果}    对比数据_V2C    cSU1parameter
    should be true    ${比较结果}
    ${比较结果}    对比数据_V2C    cSU1deviceInfo
    should be true    ${比较结果}
    #SET权限
    # ${设置成功参数个数}    SNMP批量参数设置_用户权限    cSU1parameter
    # should be equal as integers    0    ${设置成功参数个数}
    ${比较结果}    SNMP批量参数设置    cSU1parameter
    should not be true    ${比较结果}
    [Teardown]    北向协议配置_修改V3用户参数    zteuser    SNMP权限范围    读写

SNMP用户读写权限测试
    [Setup]    北向协议配置_修改V3用户参数    zteuser    SNMP权限范围    读写
    Comment    连接SNMP_V3    zteuser1    PowerAuth123!    PowerPri123!    MD5    DES
    北向协议配置_读取V3用户参数
    #GET权限
    # ${比较结果}    对比数据_V2C    cSU1analogData
    # should be true    ${比较结果}
    ${比较结果}    对比数据_V2C    cSU1digitalData
    should be true    ${比较结果}
    ${比较结果}    对比告警级别_V2C    cSU1alarm
    should be true    ${比较结果}
    ${比较结果}    对比告警干接点_V2C    cSU1alarm
    should be true    ${比较结果}
    ${比较结果}    对比数据_V2C    cSU1parameter
    should be true    ${比较结果}
    ${比较结果}    对比数据_V2C    cSU1deviceInfo
    should be true    ${比较结果}
    #SET权限
    ${比较结果}    SNMP批量参数设置    cSU1parameter
    should be true    ${比较结果}
    ${比较结果}    批量设置四种/五种告警级别    cSU1alarm
    should be true    ${比较结果}
    ${比较结果}    批量修改告警干接点_V2C    cSU1alarm
    should be true    ${比较结果}
    [Teardown]    北向协议配置_修改V3用户参数    zteuser    SNMP权限范围    读写
