*** Settings ***
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
周期均充时间到时均充
    [Tags]    notest
    [Documentation]    0:浮充/Float;1:均充/Equal;2:测试/Test;3:放电/Discharge;4:检测/Detect;5:过渡/Transition;6:充电/Charge
    ...    ==20190322-F：下次均充时间到没有进入均充，而是浮充
    
    电池管理初始化
    设置web参数量    电池均充周期    180
    设置web参数量    电池测试周期    0
    设置web参数量    电池检测周期    0
    设置web参数量    均充使能    允许
    sleep    5
    ${均充末期维持时间范围}    获取web参数上下限范围_有单位    均充末期维持时间
    ${均充最短时间范围}    获取web参数上下限范围_有单位    均充最短时间
    ${均充最长时间范围}    获取web参数上下限范围_有单位    均充最长时间
    ${均充末期维持时间}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间范围}[1]+1
    ...    ELSE    evaluate    ${均充末期维持时间范围}[1]+3
    ${均充最短时间}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间范围}[1]+1
    ...    ELSE    evaluate    ${均充最短时间范围}[1]+3
    ${均充最长时间}    run keyword if    '${均充最长时间范围}[3]'== 'Hour'    evaluate    ${均充最长时间范围}[1]+1
    ...    ELSE    evaluate    ${均充最长时间范围}[1]+5
    设置web参数量    均充末期维持时间    ${均充末期维持时间}
    设置web参数量    均充最短时间    ${均充最短时间}
    设置web参数量    均充最长时间    ${均充最长时间}
    ${均充末期维持时间转换后}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间}*60
    ...    ELSE    set variable    ${均充末期维持时间}
    ${均充最短时间转换后}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间}*60
    ...    ELSE    set variable    ${均充最短时间}
    ${均充最长时间转换后}    run keyword if    '${均充最长时间范围}[3]'== 'Hour'    evaluate    ${均充最长时间}*60
    ...    ELSE    set variable    ${均充最长时间}
    log    ===单位换算完成===
    ${电池均充周期}    获取web参数量    电池均充周期
    ${下次均充时间}    获取web实时数据    下次均充时间
    ${下次均充时间0}    Subtract Time From Date    ${下次均充时间}    5s    #比16:00:00差5s
    设置系统时间    ${下次均充时间0}
    sleep    8
    ${修改后的系统时间}    获取系统时间
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    均充
    ${下次均充时间1}    获取web实时数据    下次均充时间
    Comment    Should Be Equal    ${下次均充时间1}    ${下次均充时间}
    ${等待时间}    evaluate    ${均充最长时间转换后}+3
    Wait Until Keyword Succeeds    ${等待时间}m    2    信号量数据值为    电池管理状态    浮充
    ${下次均充时间_cal}    add time to date    ${下次均充时间}    ${电池均充周期}d    exclude_millis=yes
    sleep    5
    ${下次均充时间_new}    获取web实时数据    下次均充时间
    should be equal    ${下次均充时间_new}    ${下次均充时间_cal}
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池测试周期    电池均充周期    电池检测周期    均充最长时间    均充最短时间    均充末期维持时间

均充周期为0时无周期均充功能
    
    电池管理初始化
    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池均充周期    60
    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池测试周期    0
    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池检测周期    0
    Wait Until Keyword Succeeds    1m    1    设置web参数量    均充使能    允许
    sleep    30
    ${下次均充时间}    获取web实时数据    下次均充时间
    ${下次均充时间0}    Subtract Time From Date    ${下次均充时间}    5s    #比16:00:00差5s
    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池均充周期    0
    sleep    30
    Wait Until Keyword Succeeds    1m    1    设置系统时间    ${下次均充时间0}
    sleep    1m
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    浮充
    ${系统时间}    Subtract Time From Date    ${下次均充时间0}    7 days
    Wait Until Keyword Succeeds    1m    1    设置系统时间    ${系统时间}
    sleep    5
    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池均充周期    60    #默认90
    sleep    30
    ${下次均充时间_new}    获取web实时数据    下次均充时间
    should be equal    ${下次均充时间_new}    ${下次均充时间}
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池测试周期    电池均充周期    电池检测周期

均充使能禁止时无周期均充功能
    
    电池管理初始化
    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池均充周期    90    #默认90
    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池测试周期    0    #默认0
    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池检测周期    0    #默认0
    Wait Until Keyword Succeeds    1m    1    设置web参数量    均充使能    允许
    sleep    30
    ${下次均充时间}    获取web实时数据    下次均充时间
    ${下次均充时间0}    Subtract Time From Date    ${下次均充时间}    5s    #比16:00:00差5s
    Wait Until Keyword Succeeds    1m    1    设置web参数量    均充使能    禁止
    sleep    30
    Wait Until Keyword Succeeds    1m    1    设置系统时间    ${下次均充时间0}
    sleep    1m
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    浮充
    ${系统时间}    Subtract Time From Date    ${下次均充时间0}    7 days
    Wait Until Keyword Succeeds    1m    1    设置系统时间    ${系统时间}
    sleep    5
    Wait Until Keyword Succeeds    1m    1    设置web参数量    均充使能    允许
    sleep    30
    ${下次均充时间_new}    获取web实时数据    下次均充时间
    should be equal    ${下次均充时间_new}    ${下次均充时间}
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池测试周期    电池均充周期    电池检测周期

测试时周期均充时间到
    [Documentation]    0:浮充/Float;1:均充/Equal;2:测试/Test;3:放电/Discharge;4:检测/Detect;5:过渡/Transition;6:充电/Charge
    ...
    ...    =测试时均充时间到，等待测试完成，立即进入均充
    ...    ==20190321-F：测试结束直接进入浮充
    
    电池管理初始化
    ${测试最长时间范围}    获取web参数上下限范围_有单位    测试最长时间
    ${测试最长时间}    run keyword if    '${测试最长时间范围}[3]'== 'Hour'    evaluate    ${测试最长时间范围}[1]+1
    ...    ELSE    evaluate    ${测试最长时间范围}[1]+5
    设置web参数量    测试最长时间    ${测试最长时间}
    设置web参数量    电池均充周期    90    #默认90
    设置web参数量    电池测试周期    0    #默认0
    设置web参数量    电池检测周期    0    #默认0
    Comment    设置web参数量    <<测试最长时间~0x0317>>    3    #默认480
    设置web参数量    均充使能    允许
    sleep    5
    ${均充末期维持时间范围}    获取web参数上下限范围_有单位    均充末期维持时间
    ${均充最短时间范围}    获取web参数上下限范围_有单位    均充最短时间
    ${均充最长时间范围}    获取web参数上下限范围_有单位    均充最长时间
    ${均充末期维持时间}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间范围}[1]+1
    ...    ELSE    evaluate    ${均充末期维持时间范围}[1]+3
    ${均充最短时间}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间范围}[1]+1
    ...    ELSE    evaluate    ${均充最短时间范围}[1]+3
    ${均充最长时间}    run keyword if    '${均充最长时间范围}[3]'== 'Hour'    evaluate    ${均充最长时间范围}[1]+1
    ...    ELSE    evaluate    ${均充最长时间范围}[1]+5
    设置web参数量    均充末期维持时间    ${均充末期维持时间}
    设置web参数量    均充最短时间    ${均充最短时间}
    设置web参数量    均充最长时间    ${均充最长时间}
    ${均充末期维持时间转换后}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间}*60
    ...    ELSE    set variable    ${均充末期维持时间}
    ${均充最短时间转换后}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间}*60
    ...    ELSE    set variable    ${均充最短时间}
    ${均充最长时间转换后}    run keyword if    '${均充最长时间范围}[3]'== 'Hour'    evaluate    ${均充最长时间}*60
    ...    ELSE    set variable    ${均充最长时间}
    ${测试最长时间转换后}    run keyword if    '${测试最长时间范围}[3]'== 'Hour'    evaluate    ${测试最长时间}*60
    ...    ELSE    set variable    ${测试最长时间}
    log    ===单位换算完成===
    ${电池均充周期}    获取web参数量    电池均充周期
    ${下次均充时间}    获取web实时数据    下次均充时间
    ${下次均充时间0}    Subtract Time From Date    ${下次均充时间}    5s    #比16:00:00差5s
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动测试
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    测试
    sleep    1m
    设置系统时间    ${下次均充时间0}
    sleep    8
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    测试
    ${等待时间}    evaluate    ${测试最长时间转换后}+2
    Wait Until Keyword Succeeds    ${等待时间}m    2    信号量数据值不为    电池管理状态    测试
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    均充
    ${退出均充等待时间}    evaluate    ${均充最长时间转换后}+2
    Wait Until Keyword Succeeds    ${退出均充等待时间}m    2    信号量数据值不为    电池管理状态    均充
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    浮充
    sleep    5
    ${下次均充时间_cal}    add time to date    ${下次均充时间}    ${电池均充周期}d    exclude_millis=yes
    ${下次均充时间_new}    获取web实时数据    下次均充时间
    should be equal    ${下次均充时间_new}    ${下次均充时间_cal}
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池测试周期    电池均充周期    电池检测周期    测试最长时间    均充最长时间    均充最短时间    均充末期维持时间

检测时周期均充时间到
    [Documentation]    ==20190321-F：检测结束进入均充，但下次均充时间未更新
    
    电池管理初始化
    设置web参数量    电池均充周期    90    #默认90
    设置web参数量    电池测试周期    0    #默认0
    设置web参数量    电池检测周期    0    #默认0
    设置web参数量    均充使能    允许
    sleep    5
    ${均充末期维持时间范围}    获取web参数上下限范围_有单位    均充末期维持时间
    ${均充最短时间范围}    获取web参数上下限范围_有单位    均充最短时间
    ${均充最长时间范围}    获取web参数上下限范围_有单位    均充最长时间
    ${均充末期维持时间}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间范围}[1]+1
    ...    ELSE    evaluate    ${均充末期维持时间范围}[1]+3
    ${均充最短时间}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间范围}[1]+1
    ...    ELSE    evaluate    ${均充最短时间范围}[1]+3
    ${均充最长时间}    run keyword if    '${均充最长时间范围}[3]'== 'Hour'    evaluate    ${均充最长时间范围}[1]+1
    ...    ELSE    evaluate    ${均充最长时间范围}[1]+5
    设置web参数量    均充末期维持时间    ${均充末期维持时间}
    设置web参数量    均充最短时间    ${均充最短时间}
    设置web参数量    均充最长时间    ${均充最长时间}
    ${均充末期维持时间转换后}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间}*60
    ...    ELSE    set variable    ${均充末期维持时间}
    ${均充最短时间转换后}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间}*60
    ...    ELSE    set variable    ${均充最短时间}
    ${均充最长时间转换后}    run keyword if    '${均充最长时间范围}[3]'== 'Hour'    evaluate    ${均充最长时间}*60
    ...    ELSE    set variable    ${均充最长时间}
    log    ===单位换算完成===
    连接CSU
    ${电池均充周期}    获取web参数量    电池均充周期
    ${下次均充时间}    获取web实时数据    下次均充时间
    ${下次均充时间0}    Subtract Time From Date    ${下次均充时间}    5s    #比16:00:00差5s
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    sleep    30
    设置系统时间    ${下次均充时间0}
    sleep    8
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    sleep    10
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    均充
    ${退出均充等待时间}    evaluate    ${均充最长时间转换后}+2
    Wait Until Keyword Succeeds    ${退出均充等待时间}m    2    信号量数据值不为    电池管理状态    均充
    sleep    5
    ${下次均充时间_cal}    add time to date    ${下次均充时间}    ${电池均充周期}d    exclude_millis=yes
    ${下次均充时间_new}    获取web实时数据    下次均充时间
    should be equal    ${下次均充时间_new}    ${下次均充时间_cal}
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池测试周期    电池均充周期    电池检测周期    均充最长时间    均充最短时间    均充末期维持时间

测试异常时周期均充时间到
    
    电池管理初始化
    ${测试终止电压缺省值}    获取web参数上下限范围    测试终止电压
    ${测试终止电压可设置范围}    获取web参数可设置范围    测试终止电压
    Comment    设置web参数量    测试终止电压    ${测试终止电压可设置范围}[1]
    设置web参数量    测试终止电压    49
    #设置测试最大时间
    ${测试最长时间缺省值}    获取web参数上下限范围    测试最长时间
    ${测试最长时间可设置范围}    获取web参数可设置范围    测试最长时间
    设置web参数量    测试最长时间    ${测试最长时间可设置范围}[1]
    #设置测试终止容量
    ${测试终止容量缺省值}    获取web参数上下限范围_有单位    测试终止SOC阈值
    ${测试终止容量可设置范围}    获取web参数可设置范围    测试终止SOC阈值
    设置web参数量    测试终止SOC阈值    ${测试终止容量可设置范围}[0]
    ${测试失败容量阈值缺省值}    获取web参数上下限范围_有单位    测试失败SOC阈值
    ${测试失败容量阈值可设置范围}    获取web参数可设置范围    测试失败SOC阈值
    设置web参数量    测试失败SOC阈值    ${测试失败容量阈值可设置范围}[0]
    设置web参数量    均充使能    允许
    设置web参数量    电池均充周期    90    #默认90
    设置web参数量    电池测试周期    0    #默认0
    设置web参数量    电池检测周期    0    #默认0
    ${均充末期维持时间范围}    获取web参数上下限范围_有单位    均充末期维持时间
    ${均充最短时间范围}    获取web参数上下限范围_有单位    均充最短时间
    ${均充最长时间范围}    获取web参数上下限范围_有单位    均充最长时间
    ${均充末期维持时间}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间范围}[1]+1
    ...    ELSE    evaluate    ${均充末期维持时间范围}[1]+3
    ${均充最短时间}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间范围}[1]+1
    ...    ELSE    evaluate    ${均充最短时间范围}[1]+3
    ${均充最长时间}    run keyword if    '${均充最长时间范围}[3]'== 'Hour'    evaluate    ${均充最长时间范围}[1]+1
    ...    ELSE    evaluate    ${均充最长时间范围}[1]+5
    设置web参数量    均充末期维持时间    ${均充末期维持时间}
    设置web参数量    均充最短时间    ${均充最短时间}
    设置web参数量    均充最长时间    ${均充最长时间}
    ${均充末期维持时间转换后}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间}*60
    ...    ELSE    set variable    ${均充末期维持时间}
    ${均充最短时间转换后}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间}*60
    ...    ELSE    set variable    ${均充最短时间}
    ${均充最长时间转换后}    run keyword if    '${均充最长时间范围}[3]'== 'Hour'    evaluate    ${均充最长时间}*60
    ...    ELSE    set variable    ${均充最长时间}
    log    ===单位换算完成===
    ${电池均充周期}    获取web参数量    电池均充周期
    ${下次均充时间}    获取web实时数据    下次均充时间
    ${下次均充时间}    Run Keyword If     '${下次均充时间}'==''    Run Keywords    sleep    10
    ...    AND     获取web实时数据    下次均充时间
    ...    ELSE    set variable    ${下次均充时间} 
    ${下次均充时间0}    Subtract Time From Date    ${下次均充时间}    5s    #比16:00:00差5s
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动测试
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    测试
    sleep    1m
    设置系统时间    ${下次均充时间0}
    sleep    1m
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    测试
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动浮充
    sleep    8
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    均充
    sleep    5
    ${等待均充退出时间}    evaluate    ${均充最长时间转换后}+3
    Wait Until Keyword Succeeds    ${等待均充退出时间}m    1    信号量数据值不为    电池管理状态    均充
    sleep    5
    ${下次均充时间_cal}    add time to date    ${下次均充时间}    ${电池均充周期}d    exclude_millis=yes
    ${下次均充时间_new}    获取web实时数据    下次均充时间
    should be equal    ${下次均充时间_new}    ${下次均充时间_cal}
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    测试终止电压    测试失败SOC阈值    测试终止SOC阈值    均充最长时间    均充最短时间    均充末期维持时间

均充时周期均充时间到
    [Documentation]    均充时周期均充时间到，继续均充，下次周期均充时间更新
    
    电池管理初始化
    设置web参数量    电池均充周期    90
    设置web参数量    电池测试周期    0
    设置web参数量    电池检测周期    0
    设置web参数量    均充使能    允许
    sleep    5
    ${均充末期维持时间范围}    获取web参数上下限范围_有单位    均充末期维持时间
    ${均充最短时间范围}    获取web参数上下限范围_有单位    均充最短时间
    ${均充最长时间范围}    获取web参数上下限范围_有单位    均充最长时间
    ${均充末期维持时间}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间范围}[1]+1
    ...    ELSE    evaluate    ${均充末期维持时间范围}[1]+3
    ${均充最短时间}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间范围}[1]+1
    ...    ELSE    evaluate    ${均充最短时间范围}[1]+3
    ${均充最长时间}    run keyword if    '${均充最长时间范围}[3]'== 'Hour'    evaluate    ${均充最长时间范围}[1]+1
    ...    ELSE    evaluate    ${均充最长时间范围}[1]+5
    设置web参数量    均充末期维持时间    ${均充末期维持时间}
    设置web参数量    均充最短时间    ${均充最短时间}
    设置web参数量    均充最长时间    ${均充最长时间}
    ${均充末期维持时间转换后}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间}*60
    ...    ELSE    set variable    ${均充末期维持时间}
    ${均充最短时间转换后}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间}*60
    ...    ELSE    set variable    ${均充最短时间}
    ${均充最长时间转换后}    run keyword if    '${均充最长时间范围}[3]'== 'Hour'    evaluate    ${均充最长时间}*60
    ...    ELSE    set variable    ${均充最长时间}
    log    ===单位换算完成===
    ${电池均充周期}    获取web参数量    电池均充周期
    ${下次均充时间}    获取web实时数据    下次均充时间
    ${下次均充时间0}    Subtract Time From Date    ${下次均充时间}    5s    #比16:00:00差5s
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动均充
    Wait Until Keyword Succeeds    10    1    信号量数据值为    电池管理状态    均充
    sleep    1m
    ${均充持续时间1}    获取web实时数据    电池状态持续时间
    设置系统时间    ${下次均充时间0}
    sleep    1m30s
    ${均充持续时间2}    获取web实时数据    电池状态持续时间
    should be true    ${均充持续时间2}>${均充持续时间1}
    ${退出均充等待时间}    evaluate    ${均充最长时间转换后}+2
    Wait Until Keyword Succeeds    ${退出均充等待时间}m    1    信号量数据值为    电池状态持续时间    0
    sleep    5
    ${下次均充时间_cal}    add time to date    ${下次均充时间}    ${电池均充周期}d    exclude_millis=yes
    ${下次均充时间_new}    获取web实时数据    下次均充时间
    should be equal    ${下次均充时间_new}    ${下次均充时间_cal}
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池测试周期    电池均充周期    电池检测周期    均充最长时间    均充最短时间    均充末期维持时间

停电时周期均充时间到
    
    电池管理初始化
    设置web参数量    电池均充周期    90
    设置web参数量    电池测试周期    0
    设置web参数量    电池检测周期    0
    设置web参数量    均充使能    允许
    sleep    5
    设置web参数量    均充阈值电压    48    #默认48
    ${均充阈值容量缺省值}    获取web参数上下限范围_有单位    均充阈值SOC
    ${均充阈值容量可设置范围}    获取web参数可设置范围    均充阈值SOC
    设置web参数量    均充阈值SOC    ${均充阈值容量可设置范围}[0]    #默认90
    ${均充末期维持时间范围}    获取web参数上下限范围_有单位    均充末期维持时间
    ${均充最短时间范围}    获取web参数上下限范围_有单位    均充最短时间
    ${均充最长时间范围}    获取web参数上下限范围_有单位    均充最长时间
    ${均充末期维持时间}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间范围}[1]+1
    ...    ELSE    evaluate    ${均充末期维持时间范围}[1]+3
    ${均充最短时间}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间范围}[1]+1
    ...    ELSE    evaluate    ${均充最短时间范围}[1]+3
    ${均充最长时间}    run keyword if    '${均充最长时间范围}[3]'== 'Hour'    evaluate    ${均充最长时间范围}[1]+1
    ...    ELSE    evaluate    ${均充最长时间范围}[1]+5
    设置web参数量    均充末期维持时间    ${均充末期维持时间}
    设置web参数量    均充最短时间    ${均充最短时间}
    设置web参数量    均充最长时间    ${均充最长时间}
    ${均充末期维持时间转换后}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间}*60
    ...    ELSE    set variable    ${均充末期维持时间}
    ${均充最短时间转换后}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间}*60
    ...    ELSE    set variable    ${均充最短时间}
    ${均充最长时间转换后}    run keyword if    '${均充最长时间范围}[3]'== 'Hour'    evaluate    ${均充最长时间}*60
    ...    ELSE    set variable    ${均充最长时间}
    log    ===单位换算完成===
    设置负载电压电流    53.5    10
    打开负载输出
    关闭交流源输出
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    电池管理状态    系统停电
    sleep    1m
    ${电池均充周期}    获取web参数量    电池均充周期
    ${下次均充时间}    获取web实时数据    下次均充时间
    ${下次均充时间0}    Subtract Time From Date    ${下次均充时间}    5s    #比16:00:00差5s
    设置系统时间    ${下次均充时间0}
    sleep    8
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    系统停电
    打开交流源输出
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    系统停电
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    过渡
    sleep    10
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    均充
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    均充
    sleep    5
    ${下次均充时间_cal}    add time to date    ${下次均充时间}    ${电池均充周期}d    exclude_millis=yes
    ${下次均充时间_new}    获取web实时数据    下次均充时间
    should be equal    ${下次均充时间_new}    ${下次均充时间_cal}
    关闭负载输出
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    均充阈值SOC    电池均充周期    电池测试周期    电池检测周期    均充最长时间    均充最短时间    均充末期维持时间
    ...    AND    打开交流源输出
    ...    AND    关闭负载输出

手动均充异常结束后不更新下次均充时间
    
    电池管理初始化
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池温度高
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动均充
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    均充
    ${下次均充日期}    获取web实时数据    下次均充时间    #比下次周期均充时间要提前，否则会进入周期均充，而无法判断是否没进预约均充
    #获取告警级别
    ${级别设置值}    获取web参数量    电池温度高
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池温度高    严重
    sleep    3
    ${级别设置值}    获取web参数量    电池温度高
    #（1）告警产生
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    10    1.6    电池_1    电池温度
    #产生电池温度高告警
    ${电池温度高阈值范围}    获取web参数上下限范围    电池温度高阈值
    ${电池温度高阈值可设置范围}    获取web参数可设置范围    电池温度高阈值
    设置web参数量    电池温度高阈值    ${电池温度高阈值可设置范围}[0]
    ${电池温度}    获取web实时数据    电池温度-1
    Wait Until Keyword Succeeds    10    1    判断告警存在    电池温度高
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    浮充
    ${下次均充时间_new}    获取web实时数据    下次均充时间
    should be equal    ${下次均充时间_new}    ${下次均充日期}
    #（2）告警恢复
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    0    1    电池_1    电池温度
    设置web参数量    电池温度高阈值    ${电池温度高阈值范围}[0]
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池温度高
    [Teardown]    Run keywords    设置web设备参数量为默认值    电池温度高阈值
    ...    AND    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    0    1    电池_1    电池温度
    ...    AND    设置web参数量    电池温度高    主要
