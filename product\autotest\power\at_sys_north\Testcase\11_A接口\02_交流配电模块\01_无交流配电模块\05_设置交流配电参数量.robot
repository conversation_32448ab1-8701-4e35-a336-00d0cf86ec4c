*** Settings ***
Resource          ../../../../../../ztepwrlibrary/Resource/协议平台关键字.robot
Resource          ../../A接口关键字_V30.robot

*** Test Cases ***
设置交流配电参数量为有效值
    ${可设置范围}    获取web参数上下限范围    交流电压高阈值
    ${范围}    get slice from list    ${可设置范围}    0    -2
    FOR    ${val}    IN    @{范围}
        ${设置结果}    ${信息}    协议平台设置北向数据    设置交流段参数量_40H_48H    交流电压高阈值=${val}
        Should not be True    ${设置结果}
        should be equal as strings    ${信息}    无效数据
    END
    ${可设置范围}    获取web参数上下限范围    交流电压低阈值
    ${范围}    get slice from list    ${可设置范围}    0    -2
    FOR    ${val}    IN    @{范围}
        ${设置结果}    ${信息}    协议平台设置北向数据    设置交流段参数量_40H_48H    交流电压低阈值=${val}
        Should not be True    ${设置结果}
        should be equal as strings    ${信息}    无效数据
    END
    ${可设置范围}    获取web参数上下限范围    交流电流高阈值
    ${范围}    get slice from list    ${可设置范围}    0    -2
    FOR    ${val}    IN    @{范围}
        ${设置结果}    ${信息}    协议平台设置北向数据    设置交流段参数量_40H_48H    交流电流高阈值=${val}
        Should not be True    ${设置结果}
        should be equal as strings    ${信息}    无效数据
    END
    ${可设置范围}    获取web参数上下限范围    交流输入频率高阈值
    ${范围}    get slice from list    ${可设置范围}    0    -2
    FOR    ${val}    IN    @{范围}
        ${设置结果}    ${信息}    协议平台设置北向数据    设置交流段参数量_40H_48H    交流输入频率上限=${val}
        Should not be True    ${设置结果}
        should be equal as strings    ${信息}    无效数据
    END
    ${可设置范围}    获取web参数上下限范围    交流输入频率低阈值
    ${范围}    get slice from list    ${可设置范围}    0    -2
    FOR    ${val}    IN    @{范围}
        ${设置结果}    ${信息}    协议平台设置北向数据    设置交流段参数量_40H_48H    交流输入频率下限=${val}
        Should not be True    ${设置结果}
        should be equal as strings    ${信息}    无效数据
    END
    ${设置结果}    ${信息}    协议平台设置北向数据    设置交流段参数量_40H_48H    油机额定功率=10
    Should not be True    ${设置结果}
    should be equal as strings    ${信息}    无效数据
