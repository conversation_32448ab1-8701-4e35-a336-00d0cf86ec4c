*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
定时下电使能设置测试
    获取参数取值约定并比对取值范围    <<配电单元定时下电使能_1~0xa001050170001>>    禁止    允许
    设置web参数量    <<配电单元定时下电使能_1~0xa001050170001>>    禁止
    FOR    ${val}    IN RANGE    1    6
        ${起始时刻}    Catenate    SEPARATOR=    <<配电单元定时下电起始时刻${val}_    1    ~0xa001050180001>>
        ${终止时刻}    Catenate    SEPARATOR=    <<配电单元定时下电终止时刻${val}_    1    ~0xa001050190001>>
        ${起始时刻值}    获取web参数量    ${起始时刻}
        ${终止时刻值}    获取web参数量    ${终止时刻}
        ${转换值}    Convert To Boolean    ${起始时刻值}
        should not be true    ${转换值}
        ${转换值}    Convert To Boolean    ${终止时刻值}
        should not be true    ${转换值}
    END
    设置web参数量    <<配电单元定时下电使能_1~0xa001050170001>>    允许
    FOR    ${val}    IN RANGE    1    6
        获取并校验定时下电起止时刻范围    ${val}
    END
    [Teardown]    设置web设备参数量为默认值  <<配电单元定时下电使能_1~0xa001050170001>>

定时下电时段设置测试
    ${定时下电使能}    获取web参数量    <<配电单元定时下电使能_1~0xa001050170001>>
    设置web参数量    <<配电单元定时下电使能_1~0xa001050170001>>    允许
    FOR    ${val}    IN RANGE    1    6
        ${起始时刻}    ${终止时刻}    获取并校验定时下电起止时刻范围    ${val}
        设置web参数的范围并校验设置成功    ${起始时刻}
        设置web参数的范围并校验设置成功    ${终止时刻}
    END
    设置web参数量    <<配电单元定时下电使能_2~0xa001050170001>>    允许
    FOR    ${val}    IN RANGE    1    6
        ${起始时刻}    ${终止时刻}    获取并校验定时下电起止时刻范围    ${val}    2
        设置web参数的范围并校验设置成功    ${起始时刻}
        设置web参数的范围并校验设置成功    ${终止时刻}
    END
    [Teardown]    设置web设备参数量为默认值    <<配电单元定时下电使能_1~0xa001050170001>>     <<配电单元定时下电使能_2~0xa001050170001>>

定时下电时段设置容错测试-输入错误
    ${定时下电使能}    获取web参数量    <<配电单元定时下电使能_1~0xa001050170001>>
    设置web参数量    <<配电单元定时下电使能_1~0xa001050170001>>    允许
    设置web参数量    <<配电单元定时下电起始时刻1_1~0xa001050180001>>    00:00
    设置web参数量    <<配电单元定时下电终止时刻1_1~0xa001050190001>>    00:00
    设置web参数量    <<配电单元定时下电起始时刻1_1~0xa001050180001>>    01:00
    设置web参数量    <<配电单元定时下电终止时刻1_1~0xa001050190001>>    00:00
    @{无效值}    Create List    24:00    00:60    -00:00    0:00    00:1    时间:00    01:分钟    AA:01    02:cd    )!:""    01-02    01：01
    FOR    ${val}    IN    @{无效值}
        ${设置结果}    设置web参数量_带返回值    <<配电单元免责下电起始时刻1_1~0xa0010502f0001>>    ${val}
        should not be true    ${设置结果}
        ${设置结果}    设置web参数量_带返回值    <<配电单元免责下电起始时刻1_1~0xa001050300001>>    ${val}
        should not be true    ${设置结果}
    END
    [Teardown]    设置web设备参数量为默认值    <<配电单元定时下电起始时刻1_1~0xa001050180001>>    <<配电单元定时下电终止时刻1_1~0xa001050190001>>    <<配电单元定时下电使能_1~0xa001050170001>>

定时下电功能测试(定时使能允许，时刻1正常上下电)
    定时下电功能测试初始化
    ${起始时间}    ${结束时间}    获取定时下电起始时间和终止时间
    设置定时下电起始时间和终止时间    1    ${起始时间}    ${结束时间}
    ${开始时间}    获取系统时间
    sleep    130
    判断告警存在    直流配电:直流配电单元下电告警[1]
    @{实时量名称}    Create List    租户负载电流_1    配电单元电流_1    租户一次下电状态_1    租户负载分路断状态_1
    @{下电实时值}    Create List    0.00    0.00    动作    断开
    @{上电实时值}    Create List    1    1    恢复    闭合
    FOR    ${val}    IN RANGE    2    3
        ${名称}    Get From List    ${实时量名称}    ${val}
        ${预期值}    Get From List    ${下电实时值}    ${val}
        ${获取值}    获取web实时数据    ${名称}
        should be equal    ${预期值}    ${获取值}
    END
    sleep    200
    判断告警不存在    直流配电:直流配电单元下电告警[1]
    FOR    ${val}    IN RANGE    2    3
        ${名称}    Get From List    ${实时量名称}    ${val}
        ${预期值}    Get From List    ${上电实时值}    ${val}
        ${获取值}    获取web实时数据    ${名称}
        Run keyword if    '电流' in $名称    should be true    ${获取值}
        Run Keyword Unless    '电流' in $名称    should be equal    ${预期值}    ${获取值}
    END
    ${结束时间}    获取系统时间
    ${告警内容}    获取web历史告警内容    ${开始时间}    ${结束时间}    1    10
    ${value}    Evaluate    False
    FOR    ${val}    IN    @{告警内容}
        ${array}    Split String    ${val}    ,
        ${value}=    set variable if    '直流配电:直流配电单元下电告警[1]' in '${array}[5]'    True    False
        Run keyword if    ${value}    Exit For Loop
    END
    should be true    ${value}
    [Teardown]    设置web设备参数量为默认值    <<配电单元定时下电使能_1~0xa001050170001>>

定时下电功能测试(定时使能禁止，时刻2不下电)
    定时下电功能测试初始化
    ${起始时间}    ${结束时间}    获取定时下电起始时间和终止时间
    设置定时下电起始时间和终止时间    2    ${起始时间}    ${结束时间}
    设置web参数量    <<配电单元定时下电使能_1~0xa001050170001>>    禁止
    ${开始时间}    获取系统时间
    sleep    130
    判断告警不存在    直流配电:直流配电单元下电告警[1]
    @{实时量名称}    Create List    租户负载电流_1    配电单元电流_1    租户一次下电状态_1    租户负载分路断状态_1
    @{下电实时值}    Create List    0.00    0.00    动作    断开
    @{上电实时值}    Create List    1    1    恢复    闭合
    FOR    ${val}    IN RANGE    2    3
        ${名称}    Get From List    ${实时量名称}    ${val}
        ${预期值}    Get From List    ${上电实时值}    ${val}
        ${获取值}    获取web实时数据    ${名称}
        Run keyword if    '电流' in $名称    should be true    ${获取值}
        Run Keyword Unless    '电流' in $名称    should be equal    ${预期值}    ${获取值}
    END
    sleep    200
    判断告警不存在    直流配电:直流配电单元下电告警[1]
    FOR    ${val}    IN RANGE    2    3
        ${名称}    Get From List    ${实时量名称}    ${val}
        ${预期值}    Get From List    ${上电实时值}    ${val}
        ${获取值}    获取web实时数据    ${名称}
        Run keyword if    '电流' in $名称    should be true    ${获取值}
        Run Keyword Unless    '电流' in $名称    should be equal    ${预期值}    ${获取值}
    END
    ${结束时间}    获取系统时间
    ${告警内容}    获取web历史告警内容    ${开始时间}    ${结束时间}    1    10
    ${value}    Evaluate    False
    FOR    ${val}    IN    @{告警内容}
        ${array}    Split String    ${val}    ,
        ${value}=    set variable if    '直流配电:直流配电单元下电告警[1]' in '${array}[5]'    True    False
        Run keyword if    ${value}    Exit For Loop
    END
    should not be true    ${value}
    [Teardown]    设置web设备参数量为默认值    <<配电单元定时下电使能_1~0xa001050170001>>


定时下电功能测试(定时使能允许，时刻4跨天正常上下电)
    定时下电功能测试初始化
    ${日期}    Get Current Date    result_format=%Y-%m-%d    exclude_millis=yes
    ${日期时间}    Catenate    ${日期}    23:58:30
    ${开始时间}    获取系统时间
    设置定时下电起始时间和终止时间    4    23:59    00:02
    设置系统时间    ${日期时间}
    Wait Until Keyword Succeeds    2m    1    判断告警存在    直流配电:直流配电单元下电告警[1]
    @{实时量名称}    Create List    租户负载电流_1    配电单元电流_1    租户一次下电状态_1    租户负载分路断状态_1
    @{下电实时值}    Create List    0.00    0.00    动作    断开
    @{上电实时值}    Create List    1    1    恢复    闭合
    FOR    ${val}    IN RANGE    2    3
        ${名称}    Get From List    ${实时量名称}    ${val}
        ${预期值}    Get From List    ${下电实时值}    ${val}
        ${获取值}    获取web实时数据    ${名称}
        should be equal    ${预期值}    ${获取值}
    END
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    直流配电:直流配电单元下电告警[1]
    FOR    ${val}    IN RANGE    2    3
        ${名称}    Get From List    ${实时量名称}    ${val}
        ${预期值}    Get From List    ${上电实时值}    ${val}
        ${获取值}    获取web实时数据    ${名称}
        Run keyword if    '电流' in $名称    should be true    ${获取值}
        Run Keyword Unless    '电流' in $名称    should be equal    ${预期值}    ${获取值}
    END
    ${结束时间}    获取系统时间
    ${告警内容}    获取web历史告警内容    ${开始时间}    ${结束时间}    1    10
    ${value}    Evaluate    False
    FOR    ${val}    IN    @{告警内容}
        ${array}    Split String    ${val}    ,
        ${value}=    set variable if    '直流配电:直流配电单元下电告警[1]' in '${array}[5]'    True    False
        Run keyword if    ${value}    Exit For Loop
    END
    should be true    ${value}
    [Teardown]    Run Keywords    设置web设备参数量为默认值    <<配电单元定时下电使能_1~0xa001050170001>>    
    ...           AND    同步系统时间

定时下电功能测试(上电时段修改系统时间直接跨过下电时段）
    定时下电功能测试初始化
    ${系统时间}    获取系统时间
    ${起始时间}    ${结束时间}    获取定时下电起始时间和终止时间    00:05:00    00:10:00    ${系统时间}
    设置定时下电起始时间和终止时间    4    ${起始时间}    ${结束时间}
    ${开始时间}    获取系统时间
    FOR    ${val}    IN RANGE    5
        sleep    20
        判断告警不存在    直流配电:直流配电单元下电告警[1]
        ${下电状态}    获取web实时数据    租户一次下电状态_1
        should be equal    ${下电状态}    恢复
    END
    ${日期时间New}    add time to date    ${系统时间}    15m
    设置系统时间    ${日期时间New}
    FOR    ${val}    IN RANGE    5
        sleep    20
        判断告警不存在    直流配电:直流配电单元下电告警[1]
        ${下电状态}    获取web实时数据    租户一次下电状态_1
        should be equal    ${下电状态}    恢复
    END
    ${结束时间}    获取系统时间
    ${告警内容}    获取web历史告警内容    ${开始时间}    ${结束时间}    1    10
    ${value}    Evaluate    False
    FOR    ${val}    IN    @{告警内容}
        ${array}    Split String    ${val}    ,
        ${value}=    set variable if    '直流配电:直流配电单元下电告警[1]' in '${array}[5]'    True    False
        Run keyword if    ${value}    Exit For Loop
    END
    should not be true    ${value}
    [Teardown]    Run Keywords    设置web设备参数量为默认值    <<配电单元定时下电使能_1~0xa001050170001>>
    ...           AND    同步系统时间

定时下电功能测试(下电时段修改系统时间直接跨过下电时段）
    定时下电功能测试初始化
    ${系统时间}    获取系统时间
    ${起始时间}    ${结束时间}    获取定时下电起始时间和终止时间    系统时间=${系统时间}
    设置定时下电起始时间和终止时间    1    ${起始时间}    ${结束时间}
    ${开始时间}    获取系统时间
    ${系统时间new}    add time to date    ${系统时间}    00:02:00    exclude_millis=yes
    设置系统时间    ${系统时间new}
    Wait Until Keyword Succeeds    1m    1    判断告警存在    直流配电:直流配电单元下电告警[1]
    @{实时量名称}    Create List    租户负载电流_1    配电单元电流_1    租户一次下电状态_1    租户负载分路断状态_1
    @{下电实时值}    Create List    0.00    0.00    动作    断开
    @{上电实时值}    Create List    1    1    恢复    闭合
    FOR    ${val}    IN RANGE    2    3
        ${名称}    Get From List    ${实时量名称}    ${val}
        ${预期值}    Get From List    ${下电实时值}    ${val}
        ${获取值}    获取web实时数据    ${名称}
        should be equal    ${预期值}    ${获取值}
    END
    ${系统时间new}    add time to date    ${系统时间}    00:05:00    exclude_millis=yes
    设置系统时间    ${系统时间new}
    Wait Until Keyword Succeeds    1m    1    判断告警不存在    直流配电:直流配电单元下电告警[1]
    FOR    ${val}    IN RANGE    2    3
        ${名称}    Get From List    ${实时量名称}    ${val}
        ${预期值}    Get From List    ${上电实时值}    ${val}
        ${获取值}    获取web实时数据    ${名称}
        Run keyword if    '电流' in $名称    should be true    ${获取值}
        Run Keyword Unless    '电流' in $名称    should be equal    ${预期值}    ${获取值}
    END
    ${结束时间}    获取系统时间
    ${告警内容}    获取web历史告警内容    ${开始时间}    ${结束时间}    1    10
    ${value}    Evaluate    False
    FOR    ${val}    IN    @{告警内容}
        ${array}    Split String    ${val}    ,
        ${value}=    set variable if    '直流配电:直流配电单元下电告警[1]' in '${array}[5]'    True    False
        Run keyword if    ${value}    Exit For Loop
    END
    should be true    ${value}
    [Teardown]    同步系统时间

定时下电功能测试(未下电时段修改系统时间直接进入下电时段）
    定时下电功能测试初始化
    ${系统时间}    获取系统时间
    ${起始时间1}    ${结束时间1}    获取定时下电起始时间和终止时间    00:10:00    00:15:00    ${系统时间}
    设置定时下电起始时间和终止时间    1    ${起始时间1}    ${结束时间1} 
    ${系统时间new}    add time to date    ${系统时间}    00:12:00    exclude_millis=yes
    设置系统时间    ${系统时间new}
    ${开始时间}    获取系统时间
    Wait Until Keyword Succeeds    2m    1    判断告警存在    直流配电:直流配电单元下电告警[1]
    @{实时量名称}    Create List    租户负载电流_1    配电单元电流_1    租户一次下电状态_1    租户负载分路断状态_1
    @{下电实时值}    Create List    0.00    0.00    动作    断开
    @{上电实时值}    Create List    1    1    恢复    闭合
    FOR    ${val}    IN RANGE    2    3
        ${名称}    Get From List    ${实时量名称}    ${val}
        ${预期值}    Get From List    ${下电实时值}    ${val}
        ${获取值}    获取web实时数据    ${名称}
        should be equal    ${预期值}    ${获取值}
    END
    ${起始时间2}    ${结束时间2}    获取定时下电起始时间和终止时间    00:20:00    00:25:00    ${系统时间}
    设置定时下电起始时间和终止时间    2    ${起始时间2}    ${结束时间2}
    ${系统时间new}    add time to date    ${系统时间}    00:22:00    exclude_millis=yes
    设置系统时间    ${系统时间new}
    FOR    ${val}    IN RANGE    3
        sleep    20
        判断告警存在    直流配电:直流配电单元下电告警[1]
        ${下电状态}    获取web实时数据    租户一次下电状态_1
        should be equal    ${下电状态}    动作
    END
    ${系统时间new}    add time to date    ${系统时间}    00:25:01    exclude_millis=yes
    设置系统时间    ${系统时间new}
    Wait Until Keyword Succeeds    1m    1    判断告警不存在    直流配电:直流配电单元下电告警[1]
    FOR    ${val}    IN RANGE    2    3
        ${名称}    Get From List    ${实时量名称}    ${val}
        ${预期值}    Get From List    ${上电实时值}    ${val}
        ${获取值}    获取web实时数据    ${名称}
        Run keyword if    '电流' in $名称    should be true    ${获取值}
        Run Keyword Unless    '电流' in $名称    should be equal    ${预期值}    ${获取值}
    END
    ${结束时间}    获取系统时间
    ${告警内容}    获取web历史告警内容    ${开始时间}    ${结束时间}    1    10
    ${value}    Evaluate    False
    FOR    ${val}    IN    @{告警内容}
        ${array}    Split String    ${val}    ,
        ${value}=    set variable if    '直流配电:直流配电单元下电告警[1]' in '${array}[5]'    True    False
        Run keyword if    ${value}    Exit For Loop
    END
    should be true    ${value}
    [Teardown]    Run Keywords    设置web设备参数量为默认值    <<配电单元定时下电使能_1~0xa001050170001>>
    ...           AND    同步系统时间

*** Keywords ***

定时下电功能测试初始化
    设置web参数量    <<配电单元定时下电使能_1~0xa001050170001>>    允许
    FOR    ${val}    IN RANGE    1    6
        ${参数}    Catenate    SEPARATOR=    <<配电单元定时下电起始时刻${val}_    1    ~0xa001050180001>>
        设置web参数量    ${参数}    08:00
        ${参数}    Catenate    SEPARATOR=    <<配电单元定时下电终止时刻${val}_    1    ~0xa001050190001>>
        设置web参数量    ${参数}    08:00
    END

设置定时下电起始时间和终止时间
    [Arguments]     ${时刻序号}    ${起始时间}    ${结束时间}
    ${参数}    Catenate    SEPARATOR=    <<配电单元定时下电起始时刻${时刻序号}_    1    ~0xa001050180001>>
    设置web参数量    ${参数}    ${起始时间}
    ${参数}    Catenate    SEPARATOR=    <<配电单元定时下电终止时刻${时刻序号}_    1    ~0xa001050190001>>
    设置web参数量    ${参数}    ${结束时间}

获取定时下电起始时间和终止时间
    [Arguments]    ${起始时间偏移量}=00:02:00    ${终止时间偏移量}=00:05:00    ${系统时间}=None
    ${系统时间1}    Run Keyword if    '${系统时间}'=='${None}'    获取系统时间
    ...    ELSE    Set Variable    ${系统时间}
    ${起始时间}    add time to date    ${系统时间1}    ${起始时间偏移量}    result_format=%H:%M    exclude_millis=yes
    ${结束时间}    add time to date    ${系统时间1}    ${终止时间偏移量}   result_format=%H:%M    exclude_millis=yes
    [Return]    ${起始时间}    ${结束时间}


获取并校验定时下电起止时刻范围
    [Arguments]    ${起止时刻序号}    ${直流配电单元序号}=1
    ${起始时刻}    Catenate    SEPARATOR=    <<配电单元定时下电起始时刻${起止时刻序号}_    ${直流配电单元序号}    ~0xa001050180001>>
    ${终止时刻}    Catenate    SEPARATOR=    <<配电单元定时下电终止时刻${起止时刻序号}_    ${直流配电单元序号}   ~0xa001050190001>>
    ${起始时刻范围}    获取web参数可设置范围    ${起始时刻}
    should be equal as strings    ${起始时刻范围}[0]    00:00
    should be equal as strings    ${起始时刻范围}[1]    23:59
    ${起始时刻范围}    获取web参数可设置范围    ${终止时刻}
    should be equal as strings    ${起始时刻范围}[0]    00:00
    should be equal as strings    ${起始时刻范围}[1]    23:59
    [Return]    ${起始时刻}    ${终止时刻}