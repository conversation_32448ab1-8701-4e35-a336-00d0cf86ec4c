*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
web修改站点参数导入测试
    [Tags]    PMSA-NTest
    连接CSU
    强制恢复默认值并重新登录
    ${用户参数文件}    导出参数文件    ${datapassword}
    Wait Until Keyword Succeeds    30    2    设置web参数量    <<禁止所有告警~0x1001030010001>>    次要
    ${告警级别获取}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    30    2    设置web参数量    系统过载告警    次要
    ${告警级别获取}    获取web参数量    系统过载告警
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    30    2    设置web参数量    站点名称    Site-X#8    #站点名称需要继承
    ${站点名称获取值}    获取web参数量    站点名称
    should be true    '${站点名称获取值}'=='Site-X#8'
    Wait Until Keyword Succeeds    30    2    设置web参数量    经度    -180    #经度需要继承
    ${经度获取值}    获取web参数量    经度
    should be true    ${经度获取值}==-180
    Wait Until Keyword Succeeds    30    2    设置web参数量    纬度    -90    #纬度需要继承
    ${纬度获取值}    获取web参数量    纬度
    should be true    ${纬度获取值}==-90
    Wait Until Keyword Succeeds    30    2    设置web参数量    海拔高度    -1000    #海拔高度需要继承
    ${海拔高度获取值}    获取web参数量    海拔高度
    should be true    ${海拔高度获取值} ==-1000
    Wait Until Keyword Succeeds    30    2    设置web参数量    街道    Site-X#1    #街道需要继承
    ${街道获取值}    获取web参数量    街道
    should be true    '${街道获取值}'=='Site-X#1'
    Wait Until Keyword Succeeds    30    2    设置web参数量    城市    Site-X#2    #城市需要继承
    ${城市获取值}    获取web参数量    城市
    should be true    '${城市获取值}'=='Site-X#2'
    Wait Until Keyword Succeeds    30    2    设置web参数量    行政区    Site-X#3    #行政区需要继承
    ${行政区获取值}    获取web参数量    行政区
    should be true    '${行政区获取值}'=='Site-X#3'
    Wait Until Keyword Succeeds    30    2    设置web参数量    市电配置_1    无
    ${市电配置获取}    获取web参数量    市电配置_1
    should be true    '${市电配置获取}'=='无'
    Wait Until Keyword Succeeds    30    2    设置web参数量    CPU利用率高阈值    10    #下限
    ${CPU占用率高阈值获取}    获取web参数量    CPU利用率高阈值
    should be true    ${CPU占用率高阈值获取}==10
    Wait Until Keyword Succeeds    30    2    设置web参数量    系统过载告警阈值    10    #下限
    ${系统过载告警阈值获取}    获取web参数量    系统过载告警阈值
    should be true    ${系统过载告警阈值获取}==10
    压缩文件夹    ${用户参数文件}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${用户参数文件}.zip    ${datapassword}
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    ${告警级别获取}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    should be true    '${告警级别获取}'=='严重'
    ${告警级别获取}    获取web参数量    系统过载告警
    should be true    '${告警级别获取}'=='严重'
    ${获取值}    获取web参数量    市电配置_1
    Should Be Equal    ${获取值}    有
    ${CPU利用率高阈值获取}    获取web参数量    CPU利用率高阈值
    ${缺省值}    获取web参数上下限范围    CPU利用率高阈值
    should be true    ${CPU利用率高阈值获取}==${缺省值}[0]
    ${系统过载告警阈值获取}    获取web参数量    系统过载告警阈值
    ${缺省值}    获取web参数上下限范围    系统过载告警阈值
    should be true    ${系统过载告警阈值获取}==${缺省值}[0]
    #需要继承的参数
    ${获取值}    获取web参数量    站点名称
    Should Be Equal    ${获取值}    ${站点名称获取值}
    ${获取值}    获取web参数量    经度
    Should Be Equal    ${获取值}    ${经度获取值}
    ${获取值}    获取web参数量    纬度
    Should Be Equal    ${获取值}    ${纬度获取值}
    ${获取值}    获取web参数量    海拔高度
    Should Be Equal    ${获取值}    ${海拔高度获取值}
    ${获取值}    获取web参数量    街道
    Should Be Equal    ${获取值}    ${街道获取值}
    ${获取值}    获取web参数量    城市
    Should Be Equal    ${获取值}    ${城市获取值}
    ${获取值}    获取web参数量    行政区
    Should Be Equal    ${获取值}    ${行政区获取值}
    删除文件夹以及文件夹下所有文件    download//${用户参数文件}
    删除文件    download//${用户参数文件}.zip
    [Teardown]    强制恢复默认值并重新登录

文件中修改站点参数导入测试
    [Tags]    PMSA-NTest
    连接CSU
    ${站点名称获取值}    获取web参数量    站点名称
    ${经度获取值}    获取web参数量    经度
    ${纬度获取值}    获取web参数量    纬度
    ${海拔高度获取值}    获取web参数量    海拔高度
    ${街道获取值}    获取web参数量    街道
    ${城市获取值}    获取web参数量    城市
    ${行政区获取值}    获取web参数量    行政区
    ${CPU占用率高阈值获取}    获取web参数量    CPU利用率高阈值
    ${系统过载告警阈值获取}    获取web参数量    系统过载告警阈值
    ${用户参数文件}    导出参数文件    ${datapassword}
    修改导出参数值    ${用户参数文件}/parameter.csv    CPU利用率高阈值=84    系统过载告警阈值=90    经度=100    纬度=80    海拔高度=8800
    修改导出参数值    ${用户参数文件}/parameter.csv    街道=Site-X#a    城市=Site-X#b    行政区=Site-X#c    站点名称=Site-X#d
    压缩文件夹    ${用户参数文件}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${用户参数文件}.zip    ${datapassword}
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    ${CPU利用率高阈值获取}    获取web参数量    CPU利用率高阈值
    should be true    ${CPU利用率高阈值获取}==84
    ${系统过载告警阈值获取}    获取web参数量    系统过载告警阈值
    should be true    ${系统过载告警阈值获取}==90
    #需要继承的参数
    ${获取值}    获取web参数量    站点名称
    Should Be Equal    ${获取值}    ${站点名称获取值}
    ${获取值}    获取web参数量    经度
    Should Be Equal    ${获取值}    ${经度获取值}
    ${获取值}    获取web参数量    纬度
    Should Be Equal    ${获取值}    ${纬度获取值}
    ${获取值}    获取web参数量    海拔高度
    Should Be Equal    ${获取值}    ${海拔高度获取值}
    ${获取值}    获取web参数量    街道
    Should Be Equal    ${获取值}    ${街道获取值}
    ${获取值}    获取web参数量    城市
    Should Be Equal    ${获取值}    ${城市获取值}
    ${获取值}    获取web参数量    行政区
    Should Be Equal    ${获取值}    ${行政区获取值}
    删除文件夹以及文件夹下所有文件    download//${用户参数文件}
    删除文件    download//${用户参数文件}.zip
    [Teardown]    强制恢复默认值并重新登录
