*** Settings ***
Suite Setup       主动告警测试前置条件    ${CSU_role}    #run keywords | 控制子工具运行停止 | pu | 启动 | # AND | # sleep | #run keywords | 初始化_1363协议 | # AND | 控制子工具运行停止 | pu | 启动 | # AND | # sleep | 10m
Suite Teardown    设置web设备参数量为默认值    CSU主动告警使能
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
1363批量获取状态量和PU故障测试
    写入CSV文档    PU数字量和故障量获取测试    信号名称    信号值    结果    备注
    连接CSU
    ${级别设置值}    获取web参数量    PU故障
    run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    PU故障    严重
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=太阳能模块
    ${排除列表}    create list
    P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除故障量信号}    ${排除列表}    1    ${模拟PU起始地址}    1
    ${列表1}    处理1104/1363SMR或PU设备序号大于40的部分    ${列表1}    ${模拟PU起始地址}
    #1363协议中只显示SMR告警/SMR故障/SMR通讯中断
    #所以此处无需从1363表中查找数据，以data_dict得到的为主
    ${待测数据长度}    Get Length    ${列表1}
    ${待测}    Evaluate    ${待测数据长度}-1
    ${待测索引}    Evaluate    random.randint(0,${待测})    random
    FOR    ${i}    IN    ${列表1}[${待测索引}]
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        @{序号}    evaluate    re.split('[-~]','${信号名称}')    re
        ${1363告警名}    set variable    模块故障
        ${1363告警名}    Catenate    SEPARATOR=    ${1363告警名}    -    ${序号}[1]
        ${实时告警中的设备名称}    主动告警_组合实时告警中的设备名称    太阳能模块    ${信号名称}
        Run Keyword And Continue On Failure    整流器或PU故障量产生封装判断结果    1363    pu    ${信号名称}    数字量    ${告警产生}    ${模拟PU起始地址}    PU故障    PU数字量和故障量获取测试    ${实时告警中的设备名称}    null    null    <<获取PU段告警~CID1=D0H>>    <<获取PU段告警~CID2=44H>>    ${1363告警名}
        ...    None
        Run Keyword And Continue On Failure    整流器或PU故障量恢复封装判断结果    1363    pu    ${信号名称}    数字量    ${告警恢复}    ${模拟PU起始地址}    PU故障    PU数字量和故障量获取测试    null    null    null    <<获取PU段告警~CID1=D0H>>    <<获取PU段告警~CID2=44H>>    ${1363告警名}
        ...    None
    END

1363批量获取状态量和PU告警测试
    写入CSV文档    PU数字量和告警量获取测试    信号名称    信号值    结果    备注
    连接CSU
    ${级别设置值}    获取web参数量    PU告警
    run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    PU告警    严重
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=太阳能模块
    ${排除列表}    create list
    P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除告警量信号}    ${排除列表}    3    ${模拟PU起始地址}    1
    ${列表1}    处理1104/1363SMR或PU设备序号大于40的部分    ${列表1}    ${模拟整流器地址}
    #1363协议中只显示SMR告警/SMR故障/SMR通讯中断
    #所以此处无需从1363表中查找数据，以data_dict得到的为主
    ${待测数据长度}    Get Length    ${列表1}
    ${待测}    Evaluate    ${待测数据长度}-1
    ${待测索引}    Evaluate    random.randint(0,${待测})    random
    FOR    ${i}    IN    ${列表1}[${待测索引}]
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        @{序号}    evaluate    re.split('[-~]','${信号名称}')    re
        ${1363告警名}    set variable    PU告警
        ${1363告警名}    Catenate    SEPARATOR=    ${1363告警名}    -    ${序号}[1]
        ${实时告警中的设备名称}    主动告警_组合实时告警中的设备名称    太阳能模块    ${信号名称}
        Run Keyword And Continue On Failure    整流器或PU告警量产生封装判断结果    1363    pu    ${信号名称}    数字量    ${告警产生}    ${模拟PU起始地址}    PU告警    PU数字量和告警量获取测试    ${实时告警中的设备名称}    null    null    <<获取PU段告警~CID1=D0H>>    <<获取PU段告警~CID2=44H>>    ${1363告警名}
        ...    None
        Run Keyword And Continue On Failure    整流器或PU告警量恢复封装判断结果    1363    pu    ${信号名称}    数字量    ${告警恢复}    ${模拟PU起始地址}    PU告警    PU数字量和告警量获取测试    null    null    null    <<获取PU段告警~CID1=D0H>>    <<获取PU段告警~CID2=44H>>    ${1363告警名}
        ...    None
    END

1363_0004_PU通讯断告警
    [Documentation]    OK
    [Setup]
    连接CSU
    显示属性配置    PU通讯状态    数字量    web_attr=On    gui_attr=On
    ${级别设置值}    获取web参数量    PU通讯中断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    PU通讯中断    严重
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    PU通讯中断_1
    设置子工具个数    pu    1
    #1363
    FOR    ${PU序号}    IN RANGE    2    ${北向协议PU最大数}+1
        Comment    wait until keyword succeeds    5m    2    查询指定告警信息    PU通讯中断
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU通讯状态-${PU序号}    异常
    END
    #告警出的慢，先确保web上状态全部更新再判断协议
    sleep    90
    FOR    ${PU序号}    IN RANGE    2    ${1104/1363协议PU最大数}+1
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取PU段告警~CID1=D0H>>    <<获取PU段告警~CID2=44H>>    PU通讯断-${PU序号}    1    None    ${g_ver_1363}
        should be true    ${1104告警结果}
    END
    #消除
    ${类型}    evaluate    type(${北向协议PU最大数})
    设置子工具个数    pu    ${北向协议PU最大数}
    #1363    +1
    FOR    ${PU序号}    IN RANGE    1    ${北向协议PU最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU通讯状态-${PU序号}    正常
    END
    sleep    60
    FOR    ${PU序号}    IN RANGE    1   ${1104/1363协议PU最大数}+1
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取PU段告警~CID1=D0H>>    <<获取PU段告警~CID2=44H>>    PU通讯断-${PU序号}    1    None    ${g_ver_1363}
        should not be true    ${1104告警结果}
    END
    wait until keyword succeeds    1m    2    查询指定告警信息不为    PU通讯中断
    显示属性配置    PU通讯状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    run keywords    设置子工具个数    pu    ${北向协议PU最大数}
    ...    AND    sleep    3m
