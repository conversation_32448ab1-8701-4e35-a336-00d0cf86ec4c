*** Settings ***
Documentation     get_var 中没有此类信息
...
...
...               资产信息处，可选属性软件适用性，均为否
Force Tags        3
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
uniform_0010_整机序列号
    [Tags]    3
    Log    'tags 3'

uniform_0012_BMS系统名称
    [Setup]
    连接CSU
    设置子工具值    smartli    all    只读    BMS系统名称    ZXDC48 FB101B1
    sleep    2m
    @{锂电序号lsit}    Create list    1    8    16
    FOR    ${锂电序号}    IN    @{锂电序号lsit}
        ${名称}    获取web实时数据    <<BMS软件版本-${锂电序号}~0x17001080020001>>
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    铁锂资产信息    电池名称-${锂电序号}
        should be equal    '${名称}'    'ZXDC48 FB101B1'
        should be equal    '${uniform_获取值1}'    'ZXDC48 FB101B1'
    END
    设置子工具值    smartli    all    只读    BMS系统名称    ZXDC48 FB100B3
    sleep    2m
    @{锂电序号lsit}    Create list    1    8    16
    FOR    ${锂电序号}    IN    @{锂电序号lsit}
        ${名称}    获取web实时数据    <<BMS软件版本-${锂电序号}~0x17001080020001>>
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    铁锂资产信息    电池名称-${锂电序号}
        should be equal    '${名称}'    'ZXDC48 FB100B3'
        should be equal    '${uniform_获取值1}'    'ZXDC48 FB100B3'
    END

uniform_0014_BMS软件版本
    [Setup]
    连接CSU
    设置子工具值    smartli    all    只读    BMS软件版本    V12345678
    sleep    2m
    @{锂电序号lsit}    Create list    1    8    16
    FOR    ${锂电序号}    IN    @{锂电序号lsit}
        ${名称}    获取web实时数据    <<BMS软件版本-${锂电序号}~0x17001080020001>>
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    铁锂资产信息    电池软件版本-${锂电序号}
        should be equal    '${名称}'    'V12345678'
        should be equal    '${uniform_获取值1}'    'V12345678'
    END
    设置子工具值    smartli    all    只读    BMS软件版本    V1.00.00.01
    sleep    2m
    @{锂电序号lsit}    Create list    1    8    16
    FOR    ${锂电序号}    IN    @{锂电序号lsit}
        ${名称}    获取web实时数据    <<BMS软件版本-${锂电序号}~0x17001080020001>>
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    铁锂资产信息    电池软件版本-${锂电序号}
        should be equal    '${名称}'    'V1.00.00.01'
        should be equal    '${uniform_获取值1}'    'V1.00.00.01'
    END

uniform_0016_PACK厂家
    [Tags]    3
    [Setup]
    连接CSU
    设置子工具值    smartli    all    只读    BMS软件版本    V12345678
    sleep    2m
    @{锂电序号lsit}    Create list    1    8    16
    FOR    ${锂电序号}    IN    @{锂电序号lsit}
        ${名称}    获取web实时数据    BMS软件版本-${锂电序号}
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    铁锂资产信息    电池软件版本-${锂电序号}
        should be equal    '${名称}'    'V12345678'
        should be equal    '${uniform_获取值1}'    'V12345678'
    END
    设置子工具值    smartli    all    只读    BMS软件版本    V1.00.00.01
    sleep    2m
    @{锂电序号lsit}    Create list    1    8    16
    FOR    ${锂电序号}    IN    @{锂电序号lsit}
        ${名称}    获取web实时数据    BMS软件版本-${锂电序号}
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    铁锂资产信息    电池软件版本-${锂电序号}
        should be equal    '${名称}'    'V1.00.00.01'
        should be equal    '${uniform_获取值1}'    'V1.00.00.01'
    END

uniform_0018_电池模组序列号
    [Tags]    3
    [Setup]
    连接CSU
    设置子工具值    smartli    all    只读    BMS软件版本    V12345678
    sleep    2m
    @{锂电序号lsit}    Create list    1    8    16
    FOR    ${锂电序号}    IN    @{锂电序号lsit}
        ${名称}    获取web实时数据    BMS软件版本-${锂电序号}
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    铁锂资产信息    电池软件版本-${锂电序号}
        should be equal    '${名称}'    'V12345678'
        should be equal    '${uniform_获取值1}'    'V12345678'
    END
    设置子工具值    smartli    all    只读    BMS软件版本    V1.00.00.01
    sleep    2m
    @{锂电序号lsit}    Create list    1    8    16
    FOR    ${锂电序号}    IN    @{锂电序号lsit}
        ${名称}    获取web实时数据    BMS软件版本-${锂电序号}
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    铁锂资产信息    电池软件版本-${锂电序号}
        should be equal    '${名称}'    'V1.00.00.01'
        should be equal    '${uniform_获取值1}'    'V1.00.00.01'
    END

uniform_0020_电池生产日期
    [Tags]    3
    [Setup]
    连接CSU
    设置子工具值    smartli    all    只读    BMS软件版本    V12345678
    sleep    2m
    @{锂电序号lsit}    Create list    1    8    16
    FOR    ${锂电序号}    IN    @{锂电序号lsit}
        ${名称}    获取web实时数据    BMS软件版本-${锂电序号}
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    铁锂资产信息    电池软件版本-${锂电序号}
        should be equal    '${名称}'    'V12345678'
        should be equal    '${uniform_获取值1}'    'V12345678'
    END
    设置子工具值    smartli    all    只读    BMS软件版本    V1.00.00.01
    sleep    2m
    @{锂电序号lsit}    Create list    1    8    16
    FOR    ${锂电序号}    IN    @{锂电序号lsit}
        ${名称}    获取web实时数据    BMS软件版本-${锂电序号}
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    铁锂资产信息    电池软件版本-${锂电序号}
        should be equal    '${名称}'    'V1.00.00.01'
        should be equal    '${uniform_获取值1}'    'V1.00.00.01'
    END

uniform_0022_BDCU名称
    [Setup]
    连接CSU
    设置子工具值    smartli    all    只读    BDU名称    ZTE BDCU
    sleep    3m
    @{锂电序号lsit}    Create list    1    8    16
    FOR    ${锂电序号}    IN    @{锂电序号lsit}
        ${名称}    获取web实时数据    <<BDCU名称-${锂电序号}~0x17001080050001>>
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    铁锂资产信息    BDCU名称-${锂电序号}
        should be equal    '${名称}'    'ZTE BDCU'
        should be equal    '${uniform_获取值1}'    'ZTE BDCU'
    END
    设置子工具值    smartli    all    只读    BDU名称    ZTE smartli
    sleep    3m
    @{锂电序号lsit}    Create list    1    8    16
    FOR    ${锂电序号}    IN    @{锂电序号lsit}
        ${名称}    获取web实时数据    <<BDCU名称-${锂电序号}~0x17001080050001>>
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    铁锂资产信息    BDCU名称-${锂电序号}
        should be equal    '${名称}'    'ZTE smartli'
        should be equal    '${uniform_获取值1}'    'ZTE smartli'
    END

uniform_0024_BDCU数控平台版本
    [Setup]
    连接CSU
    设置子工具值    smartli    all    只读    数控平台版本    V1.00
    sleep    3m
    @{锂电序号lsit}    Create list    1    8    16
    FOR    ${锂电序号}    IN    @{锂电序号lsit}
        ${平台版本}    获取web实时数据    <<BDCU数控平台版本-${锂电序号}~0x17001080060001>>
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    铁锂资产信息    BDCU数控平台版本-${锂电序号}
        should be equal    '${平台版本}'    'V1.00'
        should be equal    '${uniform_获取值1}'    'V1.00'
    END
    设置子工具值    smartli    all    只读    数控平台版本    V2.00
    sleep    3m
    @{锂电序号lsit}    Create list    1    8    16
    FOR    ${锂电序号}    IN    @{锂电序号lsit}
        ${平台版本}    获取web实时数据    <<BDCU数控平台版本-${锂电序号}~0x17001080060001>>
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    铁锂资产信息    BDCU数控平台版本-${锂电序号}
        should be equal    '${平台版本}'    'V2.00'
        should be equal    '${uniform_获取值1}'    'V2.00'
    END

uniform_0026_BDCU软件版本
    [Setup]
    连接CSU
    设置子工具值    smartli    all    只读    BDU软件版本    V1.00
    sleep    3m
    @{锂电序号lsit}    Create list    1    8    16
    FOR    ${锂电序号}    IN    @{锂电序号lsit}
        ${版本}    获取web实时数据    <<BDCU软件版本-${锂电序号}~0x17001080070001>>
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    铁锂资产信息    BDCU软件版本-${锂电序号}
        should be equal    '${版本}'    'V1.00'
        should be equal    '${uniform_获取值1}'    'V1.00'
    END
    设置子工具值    smartli    all    只读    BDU软件版本    V1.02
    sleep    3m
    @{锂电序号lsit}    Create list    1    8    16
    FOR    ${锂电序号}    IN    @{锂电序号lsit}
        ${版本}    获取web实时数据    <<BDCU软件版本-${锂电序号}~0x17001080070001>>
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    铁锂资产信息    BDCU软件版本-${锂电序号}
        should be equal    '${版本}'    'V1.02'
        should be equal    '${uniform_获取值1}'    'V1.02'
    END

uniform_0028_BDCU序列号

uniform_0030_BDCU软件发布日期
    连接CSU
    设置子工具值    smartli    all    只读    BDU版本日期    2020
    设置子工具值    smartli    all    只读    BDU版本日期mouth    12
    设置子工具值    smartli    all    只读    BDU版本日期day    12
    sleep    3m
    @{锂电序号lsit}    Create list    1    8    16
    FOR    ${锂电序号}    IN    @{锂电序号lsit}
        ${日期1}    获取web实时数据    <<BDCU软件发布日期-${锂电序号}~0x17001080080001>>
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    铁锂资产信息    BDCU软件发布日期-${锂电序号}
        should be equal    '${日期1}'    '2020-12-12'
        should be equal    '${uniform_获取值1}'    '2020-12-12'
    END
    设置子工具值    smartli    all    只读    BDU版本日期    2020
    设置子工具值    smartli    all    只读    BDU版本日期mouth    11
    设置子工具值    smartli    all    只读    BDU版本日期day    11
    sleep    3m
    @{锂电序号lsit}    Create list    1    8    16
    FOR    ${锂电序号}    IN    @{锂电序号lsit}
    ${日期1}    获取web实时数据    <<BDCU软件发布日期-${锂电序号}~0x17001080080001>>
    ${uniform_获取值1}    统一监控协议_获取一个数据的值    铁锂资产信息    BDCU软件发布日期-${锂电序号}
    should be equal    '${日期1}'    '2020-11-11'
    should be equal    '${uniform_获取值1}'    '2020-11-11'
