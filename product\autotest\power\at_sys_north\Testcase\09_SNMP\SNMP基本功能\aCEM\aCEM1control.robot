*** Settings ***
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp_test关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/Web关键字_V30.robot
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
snmp_0002_交流电表电量清零
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    #
    FOR    ${i}    IN RANGE    0    4
        ${设置控制量}    设置SNMP控制量    aCEMEnergyReset1value
        ${存在结果}    判断web历史记录存在snmp控制内容    交流电表    交流电表电量清零    30    1
        Exit For Loop if    '${存在结果}'=='True'
        sleep    10
    END
    #
    FOR    ${i}    IN RANGE    0    4
        ${设置控制量}    设置SNMP控制量    aCEMEnergyReset2value
        ${存在结果}    判断web历史记录存在snmp控制内容    交流电表    交流电表电量清零    30    2
        Exit For Loop if    '${存在结果}'=='True'
        sleep    10
    END
    #
    FOR    ${i}    IN RANGE    0    4
        ${设置控制量}    设置SNMP控制量    aCEMEnergyReset3value
        ${存在结果}    判断web历史记录存在snmp控制内容    交流电表    交流电表电量清零    30    3
        Exit For Loop if    '${存在结果}'=='True'
        sleep    10
    END
    Comment    ${设置控制量}    设置SNMP控制量    aCEMEnergyReset2value
    Comment    Comment    should be true    ${设置控制量}
    Comment    sleep    10
    Comment    ${存在结果}    判断web历史记录存在snmp控制内容    交流电表    交流电表电量清零    30    2
    Comment    should be true    ${存在结果}
    Comment    #
    Comment    ${设置控制量}    设置SNMP控制量    aCEMEnergyReset3value
    Comment    Comment    should be true    ${设置控制量}
    Comment    sleep    10
    Comment    ${存在结果}    判断web历史记录存在snmp控制内容    交流电表    交流电表电量清零    30    3
    Comment    should be true    ${存在结果}
    断开连接SNMP
