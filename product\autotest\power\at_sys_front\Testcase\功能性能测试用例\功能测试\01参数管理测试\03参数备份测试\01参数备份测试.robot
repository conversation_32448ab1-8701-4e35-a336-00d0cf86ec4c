*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
备份参数测试
    [Setup]    Run keywords
    连接CSU
    ${导出路径}    参数备份    ${datapassword}    #支持所有类型的参数备份与恢复
    ${WEB与导出文件对比}    ${导出文件与WEB对比}    比对导出参数    ${导出路径}//parameter.csv
    should be true    ${WEB与导出文件对比}
    should be true    ${导出文件与WEB对比}
    ${WEB与导出文件对比}    ${导出文件与WEB对比}    比对导出参数    ${导出路径}//alarm_attr_para.csv
    should be true    ${WEB与导出文件对比}
    should be true    ${导出文件与WEB对比}
    ${WEB与导出文件对比}    ${导出文件与WEB对比}    比对导出参数    ${导出路径}//show_attr_para.csv
    should be true    ${WEB与导出文件对比}
    should be true    ${导出文件与WEB对比}
    删除文件夹以及文件夹下所有文件    download//${导出路径}
    删除文件    download//${导出路径}.zip

恢复备份参数测试
    [Setup]
    连接CSU
    ${channel_config}    显示属性配置    CPU利用率    模拟量    ON    ON
    @{channel_config}    获取显示属性配置    CPU利用率    模拟量
    should be equal    ${channel_config}[0]    ON
    should be equal    ${channel_config}[1]    ON
    Wait Until Keyword Succeeds    10    2    设置web参数量    <<禁止所有告警~0x1001030010001>>    次要
    ${告警级别获取}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    should be true    '${告警级别获取}' == '次要'
    Wait Until Keyword Succeeds    10    2    设置web参数量    系统过载告警    次要
    ${告警级别获取}    获取web参数量    系统过载告警
    should be true    '${告警级别获取}' == '次要'
    Wait Until Keyword Succeeds    10    2    设置web参数量    CPU利用率高阈值    10    #下限
    ${阈值获取}    获取web参数量    CPU利用率高阈值
    should be true    ${阈值获取}==10
    ${channel_config}    通道配置_AI    ${plat.batttemp1}    0.0000    3.0000    电池_1    电池温度
    should be true    ${channel_config} == True
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    should be true    ${电池温度}>40.0
    参数备份    ${datapassword}    #支持所有类型的参数备份与恢复
    sleep    5
    恢复备份值并重新登录
    sleep    5
    @{channel_config}    获取显示属性配置    CPU利用率    模拟量
    should be equal    ${channel_config}[0]    ON
    should be equal    ${channel_config}[1]    ON
    ${告警级别获取}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    should be true    '${告警级别获取}'=='次要'
    ${告警级别获取}    获取web参数量    系统过载告警
    should be true    '${告警级别获取}'=='次要'
    ${CPU占用率高阈值获取}    获取web参数量    CPU利用率高阈值
    should be true    ${CPU占用率高阈值获取}==10
    ${电池温度}    获取web实时数据    电池温度-1    #配置参数恢复默认值后，电池温度应变为正常
    should be true    ${电池温度}>40.0
