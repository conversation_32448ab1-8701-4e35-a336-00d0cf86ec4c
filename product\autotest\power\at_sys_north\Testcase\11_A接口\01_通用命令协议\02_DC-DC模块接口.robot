*** Settings ***
Resource          ../../../../../ztepwrlibrary/Resource/协议平台关键字.robot
Resource          ../A接口关键字_V30.robot

*** Test Cases ***
DC-DC接口-返回无效值
    Comment    获取模拟量
    ${返回值}    ${A接口获取数据}    协议平台获取北向数据    获取DC-DC的模拟量_44H_41H
    should not be true    ${返回值}
    should be equal as strings    ${A接口获取数据}    无数据
    Comment    获取数字量
    ${返回值}    ${A接口获取数据}    协议平台获取北向数据    获取DC-DC的开关量_44H_43H
    should not be true    ${返回值}
    should be equal as strings    ${A接口获取数据}    无数据
    Comment    获取告警量
    ${返回值}    ${A接口获取数据}    协议平台获取北向数据    获取DC-DC的告警量_44H_44H
    should not be true    ${返回值}
    should be equal as strings    ${A接口获取数据}    无数据
    Comment    遥控DC
    ${返回值}    ${信息}    协议平台设置北向数据    遥控DC-DC_44H_45H    开DC-DC模块=1
    should not be true    ${返回值}
    should be equal as strings    ${A接口获取数据}    无数据
    ${返回值}    ${信息}    协议平台设置北向数据    遥控DC-DC_44H_45H    关DC-DC模块=1
    should not be true    ${返回值}
    should be equal as strings    ${A接口获取数据}    无数据
    Comment    获取版本信息
    ${返回值}    ${A接口获取数据}    协议平台获取北向数据    获取DC-DC的版本_44H_87H
    should not be true    ${返回值}
    should be equal as strings    ${A接口获取数据}    无数据
    Comment    遥控DC
