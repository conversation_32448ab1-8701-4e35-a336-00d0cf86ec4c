*** Settings ***
Suite Setup       整流器测试前置条件    #整流器测试前置条件
Suite Teardown    整流器测试结束条件    #整流器测试结束条件
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
内部告警历史告警保存正确性测试
    [Setup]
    连接CSU
    ${实时告警数量}    查询内部告警数量
    ${历史告警数量}    获取web历史内部告警数量    ${empty}    ${empty}
    log    ${历史告警数量}
    log    ${实时告警数量}
    #子设备工具模拟
    设置子工具值    SMR    all    数字量    整流器原边过流状态    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    整流器原边过流状态-4    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    整流器原边过流状态-10    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    整流器原边过流状态-20    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    整流器原边过流状态-30    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    整流器原边过流状态-40    异常
    wait until keyword succeeds    6m    2    查询指定内部告警信息    整流器原边过流
    wait until keyword succeeds    6m    2    查询指定告警信息    整流器告警
    wait until keyword succeeds    30m    60    获取指定内部告警数量大于    37
    ${历史告警数量1}    获取web历史内部告警数量    ${empty}    ${empty}
    log    ${历史告警数量1}
    ${实时告警数量1}    查询内部告警数量
    log    ${实时告警数量1}
    设置子工具值    SMR    all    数字量    整流器原边过流状态    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    整流器原边过流状态-4    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    整流器原边过流状态-10    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    整流器原边过流状态-20    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    整流器原边过流状态-30    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    整流器原边过流状态-40    正常
    wait until keyword succeeds    6m    1    判断内部告警不存在    整流器原边过流
    wait until keyword succeeds    6m    1    判断告警不存在    整流器告警
    ${历史告警数量2}    获取web历史内部告警数量    ${empty}    ${empty}
    log    ${历史告警数量2}
    ${实时告警数量2}    查询内部告警数量
    log    ${实时告警数量2}
    should be true    ${历史告警数量2} == ${历史告警数量1}+${实时告警数量1}
    [Teardown]    run keywords    设置子工具值    SMR    all    数字量    整流器原边过流状态    0
    ...    AND    wait until keyword succeeds    6m    1    判断告警不存在    整流器告警

系统断电内部告警历史告警保存测试
    [Setup]
    实时告警刷新完成
    ${实时告警数量}    查询内部告警数量
    ${历史告警数量}    获取web历史内部告警数量    ${empty}    ${empty}
    log    ${历史告警数量}
    log    ${实时告警数量}
    #子设备工具模拟
    设置子工具值    SMR    all    数字量    整流器原边过流状态    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    整流器原边过流状态-4    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    整流器原边过流状态-10    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    整流器原边过流状态-20    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    整流器原边过流状态-30    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    整流器原边过流状态-40    异常
    wait until keyword succeeds    6m    2    查询指定内部告警信息    整流器原边过流
    wait until keyword succeeds    6m    2    查询指定告警信息    整流器告警
    wait until keyword succeeds    30m    60    获取指定内部告警数量大于    37
    ${历史告警数量1}    获取web历史内部告警数量    ${empty}    ${empty}
    log    ${历史告警数量1}
    ${实时告警数量1}    查询内部告警数量
    log    ${实时告警数量1}
    测试台上下电操作    T1-1    OFF
    sleep    15
    测试台上下电操作    T1-1    ON
    实时告警刷新完成
    sleep    5
    ${历史告警数量2}    获取web历史内部告警数量    ${empty}    ${empty}
    should be true    ${历史告警数量2}==${历史告警数量1}+${实时告警数量1}
    [Teardown]    run keywords    设置子工具值    SMR    all    数字量    整流器原边过流状态    0
    ...    AND    wait until keyword succeeds    6m    1    判断告警不存在    整流器告警
