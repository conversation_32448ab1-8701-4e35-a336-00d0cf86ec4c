*** Settings ***
Default Tags      T1-1
Suite Setup       测试用例前置条件
Suite Teardown    测试用例后置条件
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
snmp_0002_系统过载告警√
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    #获取参数/默认值
    #获取告警级别和输出干接点设置值
    ${级别设置值}    获取web参数量    系统过载告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    系统过载告警    严重
    wait until keyword succeeds    30    1    设置web参数量    系统过载告警阈值    80    #系统配置3个整流器，满载150A。大于30A给系统过载告警
    设置负载电压电流    53.5    70
    打开负载输出
    Wait Until Keyword Succeeds    5m    2    判断告警不存在    系统过载告警
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    wait until keyword succeeds    30    1    设置web参数量    系统过载告警阈值    20
    sleep    10
    ${负载总电流}    获取web实时数据    负载总电流
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    系统过载告警
    获取系统时间
    打印web实时告警信息名称
    #产生
    ${snmp英文名}    获取snmp单个告警英文名    系统过载告警
    FOR    ${i}    IN RANGE    0    4
        ${告警产生}    判断Trap告警产生    ${snmp英文名}
        Exit For Loop if    '${告警产生}'=='True'
        sleep    10
    END
    获取系统时间
    #消失
    Wait Until Keyword Succeeds    5m    2    设置web参数量    系统过载告警    屏蔽
    Wait Until Keyword Succeeds    5m    2    判断告警不存在    系统过载告警
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    Run keywords    设置web参数量    系统过载告警    严重
    ...    AND    设置web设备参数量为默认值    系统过载告警阈值
    ...    AND    关闭负载输出

snmp_0004_模块槽位异常告警_无
    [Tags]    3

snmp_0006_交流输入场景配置错误告警√
    电池管理初始化
    #获取参数/默认值
    ${级别设置值}    获取web参数量    交流输入场景配置错误告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    交流输入场景配置错误告警    严重
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流输入场景    无
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    交流输入场景配置错误告警
    # sleep    30
    #产生
    Comment    @{查询结果}    查询Trap告警    产生    ##list
    ${snmp英文名}    获取snmp单个告警英文名    交流输入场景配置错误告警
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流输入场景    市电    #市电场景测试
    wait until keyword succeeds    3m    2    判断告警不存在    交流输入场景配置错误告警
    # sleep    30
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    Run keywords    设置web参数量    交流输入场景配置错误告警    严重
    ...    AND    设置web参数量    交流输入场景    市电
