*** Settings ***
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp_test关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/Web关键字_V30.robot
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
snmp_0002_液位传感器设备信息√
    连接CSU
    设置子工具值    llSensor    all    只读    厂家信息    0000000KAN1100000000V1.00
    sleep    1m
    ${名称1}    获取web实时数据    液位传感器系统名称-1
    Comment    ${名称2}    获取web实时数据    液位传感器系统名称-2
    ${名称3}    获取web实时数据    液位传感器软件版本-1
    Comment    ${名称4}    获取web实时数据    液位传感器软件版本-2
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${snmp获取值1}    获取SNMP数据_单个    lLSSystemName1value
    Comment    ${snmp获取值2}    获取SNMP数据_单个    lLSSystemName2value
    ${snmp获取值11}    获取SNMP数据_单个    lLSSoftwareVersion1value
    Comment    ${snmp获取值22}    获取SNMP数据_单个    lLSSoftwareVersion2value
    should be equal    '${名称1}'    'KAN110'
    Comment    should be equal    '${名称2}'    'KAN110'
    should be equal    '${名称3}'    'V1.00'
    Comment    should be equal    '${名称4}'    'V1.00'
    should be equal    '${snmp获取值1}'    'KAN110'
    Comment    should be equal    '${snmp获取值2}'    'KAN110'
    should be equal    '${snmp获取值11}'    'V1.00'
    Comment    should be equal    '${snmp获取值22}'    'V1.00'
    设置子工具值    llSensor    all    只读    厂家信息    0000000ZHANGH0000000V7.28
    sleep    1m
    ${名称1}    获取web实时数据    液位传感器系统名称-1
    Comment    ${名称2}    获取web实时数据    液位传感器系统名称-2
    ${名称3}    获取web实时数据    液位传感器软件版本-1
    Comment    ${名称4}    获取web实时数据    液位传感器软件版本-2
    ${snmp获取值1}    获取SNMP数据_单个    lLSSystemName1value
    Comment    ${snmp获取值2}    获取SNMP数据_单个    lLSSystemName2value
    ${snmp获取值11}    获取SNMP数据_单个    lLSSoftwareVersion1value
    Comment    ${snmp获取值22}    获取SNMP数据_单个    lLSSoftwareVersion2value
    should be equal    '${名称1}'    'ZHANGH'
    Comment    should be equal    '${名称2}'    'ZHANGH'
    should be equal    '${名称3}'    'V7.28'
    Comment    should be equal    '${名称4}'    'V7.28'
    should be equal    '${snmp获取值1}'    'ZHANGH'
    Comment    should be equal    '${snmp获取值2}'    'ZHANGH'
    should be equal    '${snmp获取值11}'    'V7.28'
    Comment    should be equal    '${snmp获取值22}'    'V7.28'
    设置子工具值    llSensor    all    只读    厂家信息    0000000KANGYU0000000V1.01
    sleep    1m
    ${名称1}    获取web实时数据    液位传感器系统名称-1
    Comment    ${名称2}    获取web实时数据    液位传感器系统名称-2
    ${名称3}    获取web实时数据    液位传感器软件版本-1
    Comment    ${名称4}    获取web实时数据    液位传感器软件版本-2
    ${snmp获取值1}    获取SNMP数据_单个    lLSSystemName1value
    Comment    ${snmp获取值2}    获取SNMP数据_单个    lLSSystemName2value
    ${snmp获取值11}    获取SNMP数据_单个    lLSSoftwareVersion1value
    Comment    ${snmp获取值22}    获取SNMP数据_单个    lLSSoftwareVersion2value
    should be equal    '${名称1}'    'KANGYU'
    Comment    should be equal    '${名称2}'    'KANGYU'
    should be equal    '${名称3}'    'V1.01'
    Comment    should be equal    '${名称4}'    'V1.01'
    should be equal    '${snmp获取值1}'    'KANGYU'
    Comment    should be equal    '${snmp获取值2}'    'KANGYU'
    should be equal    '${snmp获取值11}'    'V1.01'
    Comment    should be equal    '${snmp获取值22}'    'V1.01'
    断开连接SNMP
