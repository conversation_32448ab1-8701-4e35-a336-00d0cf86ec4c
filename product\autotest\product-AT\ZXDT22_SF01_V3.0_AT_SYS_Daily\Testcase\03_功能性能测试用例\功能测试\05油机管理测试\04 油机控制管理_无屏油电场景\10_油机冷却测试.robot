*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
油机冷却功能测试
    [Setup]    重置电池模拟器输出
    油机管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动电压使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动SOC使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动时间使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动系统异常使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机定时启动使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止电压使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止SOC使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止电流使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止电流使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机冷却使能    允许
    ${设置值}    获取web参数量    油机启动电压
    ${停止电压}    获取web参数量    油机停止电压
    ${可设置范围}    获取web参数可设置范围    油机最短运行时间
    ${时间设置值}    evaluate    @{可设置范围}[0]+3
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机最短运行时间    ${时间设置值}
    ${最短运行时间设置值}    获取web参数量    油机最短运行时间
    ${可设置范围}    获取web参数可设置范围    油机冷却时间
    ${时间设置值}    evaluate    @{可设置范围}[0]+60
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机冷却时间    ${时间设置值}
    ${冷却时间设置值}    获取web参数量    油机冷却时间
    Comment    向下调节电池电压    ${设置值}-0.5
    ${设置电压}    evaluate    ${设置值}-0.5
    设置电池模拟器电压模式    铅酸电池    1    1    ${设置电压}
    Wait Until Keyword Succeeds    4m    1    获取干接点状态直到变化为    ${油机干接点状态}    0    #干接点动作为1（断开），恢复为0（闭合）
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    运行
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    30    2
    打开负载输出
    sleep    1m
    ${设置电压}    evaluate    ${停止电压}+1
    设置电池模拟器电压模式    铅酸电池    1    1    ${设置电压}
    Comment    向上调节电池电压    ${停止电压}+1
    ${等待时间}    evaluate    ${最短运行时间设置值}-1
    ${油机运行时间}    获取web实时数据    油机持续运行时间
    sleep    ${等待时间}m
    ${整流器电流}    获取web实时数据    整流器总输出电流
    Wait Until Keyword Succeeds    3m    1    信号量数据值小于    整流器总输出电流    2
    Wait Until Keyword Succeeds    5    1    信号量数据值为    油机状态    运行
    ${冷却时间}    evaluate    ${冷却时间设置值}-40
    ${状态}    Run Keyword And Return Status    Wait Until Keyword Succeeds    ${冷却时间}    2    信号量数据值大于    整流器总输出电流    2
    should not be true    ${状态}
    Wait Until Keyword Succeeds    5    1    信号量数据值为    油机状态    运行
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    停止
    Wait Until Keyword Succeeds    4m    1    获取干接点状态直到变化为    ${油机干接点状态}    1    #干接点动作为1（断开），恢复为0（闭合）
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    油机启动时间使能    油机停止电压使能    油机停止SOC使能    油机最短运行时间    油机冷却时间    油机冷却使能
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动系统异常使能    允许
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止电流使能    禁止
    ...    AND    关闭负载输出
    ...    AND    重置电池模拟器输出
    ...    AND    关闭交流源输出
