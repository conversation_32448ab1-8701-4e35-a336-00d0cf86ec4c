*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
错峰用电开关设置测试
    [Setup]    连接CSU
    设置web参数量    交流输入场景    市电
    设置web参数量    电池配置    纯铅酸
    ${铅酸类型}    Create List    快充电池    循环电池    储能电池
    FOR    ${val}    IN    @{铅酸类型}
        设置web参数量    铅酸类型    ${val}
        设置web设备参数量为默认值    电池充电模式
        ${电池充电模式}    获取web参数量    电池充电模式
        should be equal as strings    ${电池充电模式}    普通
        ${可设置范围}    获取web参数的取值约定    电池充电模式
        should be equal as strings    ${可设置范围}[0]    普通
        should be equal as strings    ${可设置范围}[2]    时段
    END
    设置web参数量    铅酸类型    普通铅酸电池
    ${电池充电模式}    获取web参数量    电池充电模式
    ${转换值}    Convert to boolean    ${电池充电模式}
    should not be true    ${转换值}
    设置web参数量    电池配置    铅酸&锂电混用
    ${铅酸类型}    Create List    普通铅酸电池    快充电池    循环电池    储能电池
    FOR    ${val}    IN    @{铅酸类型}
        设置web参数量    铅酸类型    ${val}
        ${电池充电模式}    获取web参数量    电池充电模式
        ${转换值}    Convert to boolean    ${电池充电模式}
        should not be true    ${转换值}
    END
    设置web参数量    电池配置    智能锂电
    ${智能锂电类型}    Create List    FB15    FB100B3电池
    FOR    ${val}    IN    @{智能锂电类型}
        设置web参数量    智能锂电类型    ${val}
        ${电池充电模式}    获取web参数量    电池充电模式
        should be equal as strings    ${电池充电模式}    普通
        ${可设置范围}    获取web参数的取值约定    电池充电模式
        should be equal as strings    ${可设置范围}[0]    普通
        should be equal as strings    ${可设置范围}[2]    时段
    END
    设置web参数量    电池配置    常规锂电池
    ${常规锂电类型}    Create List    NFB15    NFB16
    FOR    ${val}    IN    @{常规锂电类型}
        设置web参数量    常规锂电类型    ${val}
        ${电池充电模式}    获取web参数量    电池充电模式
        should be equal as strings    ${电池充电模式}    普通
        ${可设置范围}    获取web参数的取值约定    电池充电模式
        should be equal as strings    ${可设置范围}[0]    普通
        should be equal as strings    ${可设置范围}[2]    时段
    END
    设置web参数量    电池充电模式    时段
    sleep    10
    ${参数}    Create List    错峰启动电压    峰期错峰终止电压    峰期错峰终止容量    平期错峰终止电压    平期错峰终止容量    尖峰时段充电系数    高峰时段充电系数    平期时段充电系数    谷期时段充电系数    错峰日段_1    错峰日段_2    错峰日段_3    错峰日段_4    错峰日段_5
    FOR    ${val}    IN    @{参数}
        ${参数量}    获取web参数量    ${val}
        ${转换值}    Convert to boolean    ${参数量}
        should be true    ${转换值}
    END
    设置web参数量    电池充电模式    普通
    sleep    10
    FOR    ${val}    IN    @{参数}
        ${参数量}    获取web参数量    ${val}
        ${转换值}    Convert to boolean    ${参数量}
        should not be true    ${转换值}
    END
    [Teardown]    设置web设备参数量为默认值    电池充电模式    电池配置    交流输入场景

错峰用电时段设置测试
    [Setup]    连接CSU
    设置web参数量    交流输入场景    市电
    设置web参数量    电池配置    纯铅酸
    ${铅酸类型}    Create List    快充电池    循环电池    储能电池
    FOR    ${val}    IN    @{铅酸类型}
        设置web参数量    铅酸类型    ${val}
        设置web参数量    电池充电模式    时段
        sleep    10
        错峰日段默认值校验
    END
    设置web参数量    电池配置    智能锂电
    ${智能锂电类型}    Create List    FB15    FB100B3电池
    FOR    ${val}    IN    @{智能锂电类型}
        设置web参数量    智能锂电类型    ${val}
        设置web参数量    电池充电模式    时段
        sleep    10
        错峰日段默认值校验
    END
    设置web参数量    电池配置    常规锂电池
    ${常规锂电类型}    Create List    NFB15    NFB16
    FOR    ${val}    IN    @{常规锂电类型}
        设置web参数量    常规锂电类型    ${val}
        设置web参数量    电池充电模式    时段
        sleep    10
        错峰日段默认值校验
    END
    [Teardown]    设置web设备参数量为默认值    电池充电模式    电池配置    交流输入场景

错峰用电日段设置容错测试-逻辑错误
    [Setup]    连接CSU
    设置web参数量    交流输入场景    市电
    设置web参数量    电池配置    纯铅酸
    设置web参数量    铅酸类型    快充电池
    设置web参数量    电池充电模式    时段
    sleep    10
    Comment    设置web参数量    错峰日段_1    01-01~01-01
    ${错峰日段}    Create List    错峰日段_0    错峰日段_1    错峰日段_2    错峰日段_3    错峰日段_4    错峰日段_5
    ${有效日段}    Create List    01-01~01-01    01-31~01-31    12-31~12-31    01-01~02-01
    ${无效日段}    Create List    01-02~01-01    02-01~01-31    12-31~12-30    02-01~01-01
    FOR    ${val}    IN RANGE    1    6
        ${日段}    get from list    ${错峰日段}    ${val}
        ${日段取值}    evaluate    random.choice(@{有效日段})
        设置web参数量    ${日段}    ${日段取值}
        sleep    5
        校验有错峰时段    ${val}
        ${日段取值}    evaluate    random.choice(@{无效日段})
        设置web参数量    ${日段}    ${日段取值}
        sleep    5
        校验无错峰时段    ${val}
    END
    设置web参数量    错峰日段_1    01-01~01-31
    ${日段}    get slice from list    ${错峰日段}    2
    FOR    ${val}    IN    @{日段}
        ${返回值}    设置web参数量_带返回值    ${val}    01-31~02-01
        should not be true    ${返回值}
    END
    设置web设备参数量为默认值    错峰日段_1
    [Teardown]    run keywords    错峰日段默认值校验
    ...    AND    设置web设备参数量为默认值    电池充电模式    电池配置    交流输入场景

错峰启动电压设置容错测试 -输入错误
    [Setup]    连接CSU
    设置web参数量    交流输入场景    市电
    设置web参数量    电池配置    纯铅酸
    设置web参数量    铅酸类型    快充电池
    设置web参数量    电池充电模式    时段
    sleep    10
    设置web设备参数量为默认值    错峰启动电压
    ${可设置范围}    获取web参数可设置范围    错峰启动电压
    ${设置结果}    设置web参数量_带返回值    错峰启动电压    ${可设置范围}[0]-0.1
    should not be true    ${设置结果}
    ${设置结果}    设置web参数量_带返回值    错峰启动电压    ${可设置范围}[1]+0.1
    should not be true    ${设置结果}
    参数异常值测试    错峰启动电压
    [Teardown]    设置web设备参数量为默认值    电池充电模式    电池配置    交流输入场景

错峰启动电压设置容错测试 -逻辑错误
    [Setup]    连接CSU
    设置web参数量    交流输入场景    市电
    设置web参数量    电池配置    纯铅酸
    设置web参数量    铅酸类型    快充电池
    设置web参数量    电池充电模式    时段
    sleep    10
    ${峰期错峰终止电压}    获取web实时数据    峰期错峰终止电压
    ${平期错峰终止电压}    获取web实时数据    平期错峰终止电压
    ${均充电压}    获取web实时数据    均充电压
    ${浮充电压}    获取web实时数据    浮充电压
    ${设置结果}    设置web参数量_带返回值    错峰启动电压    ${峰期错峰终止电压}
    should not be true    ${设置结果}
    ${设置结果}    设置web参数量_带返回值    错峰启动电压    ${平期错峰终止电压}
    should not be true    ${设置结果}
    ${设置结果}    设置web参数量_带返回值    错峰启动电压    ${峰期错峰终止电压}+0.9
    should not be true    ${设置结果}
    ${设置结果}    设置web参数量_带返回值    错峰启动电压    ${平期错峰终止电压}+0.9
    should not be true    ${设置结果}
    ${设置结果}    设置web参数量_带返回值    错峰启动电压    ${均充电压}
    should not be true    ${设置结果}
    ${设置结果}    设置web参数量_带返回值    错峰启动电压    ${浮充电压}
    should not be true    ${设置结果}
    ${设置结果}    设置web参数量_带返回值    错峰启动电压    ${均充电压}-0.9
    should not be true    ${设置结果}
    ${设置结果}    设置web参数量_带返回值    错峰启动电压    ${浮充电压}+0.9
    should not be true    ${设置结果}
    ${电池类型}    Create List    智能锂电    常规锂电池
    FOR    ${val}    IN    @{电池类型}
        设置web参数量    电池配置    ${val}
        ${峰期错峰终止电压}    获取web实时数据    峰期错峰终止电压
        ${平期错峰终止电压}    获取web实时数据    平期错峰终止电压
        ${均充电压}    获取web实时数据    均充电压
        ${浮充电压}    获取web实时数据    浮充电压
        ${设置结果}    设置web参数量_带返回值    错峰启动电压    ${峰期错峰终止电压}
        should not be true    ${设置结果}
        ${设置结果}    设置web参数量_带返回值    错峰启动电压    ${平期错峰终止电压}
        should not be true    ${设置结果}
        ${设置结果}    设置web参数量_带返回值    错峰启动电压    ${峰期错峰终止电压}+0.9
        should not be true    ${设置结果}
        ${设置结果}    设置web参数量_带返回值    错峰启动电压    ${平期错峰终止电压}+0.9
        should not be true    ${设置结果}
    END
    [Teardown]    设置web设备参数量为默认值    电池充电模式    电池配置    交流输入场景

错峰终止电压设置容错测试 -输入错误
    [Setup]    连接CSU
    设置web参数量    交流输入场景    市电
    设置web参数量    电池配置    纯铅酸
    设置web参数量    铅酸类型    快充电池
    设置web参数量    电池充电模式    时段
    sleep    10
    设置web设备参数量为默认值    峰期错峰终止电压
    ${可设置范围}    获取web参数可设置范围    峰期错峰终止电压
    ${设置结果}    设置web参数量_带返回值    峰期错峰终止电压    ${可设置范围}[0]-0.1
    should not be true    ${设置结果}
    ${设置结果}    设置web参数量_带返回值    峰期错峰终止电压    ${可设置范围}[1]+0.1
    should not be true    ${设置结果}
    参数异常值测试    峰期错峰终止电压
    设置web设备参数量为默认值    平期错峰终止电压
    ${可设置范围}    获取web参数可设置范围    平期错峰终止电压
    ${设置结果}    设置web参数量_带返回值    平期错峰终止电压    ${可设置范围}[0]-0.1
    should not be true    ${设置结果}
    ${设置结果}    设置web参数量_带返回值    平期错峰终止电压    ${可设置范围}[1]+0.1
    should not be true    ${设置结果}
    参数异常值测试    平期错峰终止电压
    [Teardown]    设置web设备参数量为默认值    电池充电模式    电池配置    交流输入场景

错峰终止电压设置容错测试 -逻辑错误
    [Setup]    连接CSU
    设置web参数量    交流输入场景    市电
    设置web参数量    电池配置    纯铅酸
    设置web参数量    铅酸类型    快充电池
    设置web参数量    电池充电模式    时段
    sleep    10
    ${错峰终止电压}    Create List    峰期错峰终止电压    平期错峰终止电压
    FOR    ${val}    IN    @{错峰终止电压}
        ${错峰启动电压}    获取web实时数据    错峰启动电压
        ${电池电压低阈值}    获取web实时数据    电池电压低阈值
        ${设置结果}    设置web参数量_带返回值    ${val}    ${错峰启动电压}
        should not be true    ${设置结果}
        ${设置结果}    设置web参数量_带返回值    ${val}    ${电池电压低阈值}
        should not be true    ${设置结果}
        ${设置结果}    设置web参数量_带返回值    ${val}    ${电池电压低阈值}+0.4
        should not be true    ${设置结果}
        ${设置结果}    设置web参数量_带返回值    ${val}    ${错峰启动电压}-0.9
        should not be true    ${设置结果}
    END
    [Teardown]    设置web设备参数量为默认值    电池充电模式    电池配置    交流输入场景

错峰终止容量设置容错测试 -输入错误
    [Setup]    连接CSU
    设置web参数量    交流输入场景    市电
    设置web参数量    电池配置    纯铅酸
    设置web参数量    铅酸类型    快充电池
    设置web参数量    电池充电模式    时段
    sleep    10
    设置web设备参数量为默认值    峰期错峰终止容量
    ${可设置范围}    获取web参数可设置范围    峰期错峰终止容量
    ${设置结果}    设置web参数量_带返回值    峰期错峰终止容量    ${可设置范围}[0]-0.1
    should not be true    ${设置结果}
    ${设置结果}    设置web参数量_带返回值    峰期错峰终止容量    ${可设置范围}[1]+0.1
    should not be true    ${设置结果}
    参数异常值测试    峰期错峰终止容量
    设置web设备参数量为默认值    平期错峰终止容量
    ${可设置范围}    获取web参数可设置范围    平期错峰终止容量
    ${设置结果}    设置web参数量_带返回值    平期错峰终止容量    ${可设置范围}[0]-0.1
    should not be true    ${设置结果}
    ${设置结果}    设置web参数量_带返回值    平期错峰终止容量    ${可设置范围}[1]+0.1
    should not be true    ${设置结果}
    参数异常值测试    平期错峰终止容量
    [Teardown]    设置web设备参数量为默认值    电池充电模式    电池配置    下电模式    交流输入场景

电池各时段充电系数设置容错测试 -逻辑错误
    [Setup]    连接CSU
    设置web参数量    交流输入场景    市电
    设置web参数量    电池配置    纯铅酸
    设置web参数量    铅酸类型    快充电池
    设置web参数量    电池充电模式    时段
    sleep    10
    ${参数名称}    Create List    尖峰时段充电系数    高峰时段充电系数    平期时段充电系数    谷期时段充电系数
    FOR    ${val}    IN    @{参数名称}
        设置web设备参数量为默认值    ${val}
        ${可设置范围}    获取web参数可设置范围    ${val}
        ${设置结果}    设置web参数量_带返回值    ${val}    ${可设置范围}[0]-0.1
        should not be true    ${设置结果}
        ${设置结果}    设置web参数量_带返回值    ${val}    ${可设置范围}[1]+0.1
        should not be true    ${设置结果}
        参数异常值测试    ${val}
    END
    [Teardown]    设置web设备参数量为默认值    电池充电模式    电池配置    交流输入场景

错峰启动电压配置测试
    [Setup]    连接CSU
    设置web参数量    交流输入场景    市电
    设置web参数量    电池配置    纯铅酸
    设置web参数量    铅酸类型    快充电池
    设置web参数量    电池充电模式    时段
    sleep    10
    ${可设置范围}    获取web参数可设置范围    错峰启动电压
    ${随机值}    evaluate    round(random.uniform(${可设置范围}[0],${可设置范围}[1]),1)
    append to list    ${可设置范围}    ${随机值}
    FOR    ${val}    IN    @{可设置范围}
        设置web参数量    错峰启动电压    ${val}
        ${参数值}    获取web参数量    错峰启动电压
        should be equal as numbers    ${参数值}    ${val}
    END
    [Teardown]    设置web设备参数量为默认值    错峰启动电压    电池充电模式    电池配置    交流输入场景

错峰终止容量设置测试
    [Setup]    连接CSU
    设置web参数量    交流输入场景    市电
    设置web参数量    电池配置    纯铅酸
    设置web参数量    铅酸类型    循环电池
    设置web参数量    电池充电模式    时段
    sleep    10
    ${可设置范围}    获取web参数可设置范围    峰期错峰终止容量
    ${随机值}    evaluate    random.randint(${可设置范围}[0],${可设置范围}[1])
    append to list    ${可设置范围}    ${随机值}
    FOR    ${val}    IN    @{可设置范围}
        设置web参数量    峰期错峰终止容量    ${val}
        ${参数值}    获取web参数量    峰期错峰终止容量
        should be equal as numbers    ${参数值}    ${val}
    END
    ${可设置范围}    获取web参数可设置范围    平期错峰终止容量
    ${随机值}    evaluate    random.randint(${可设置范围}[0],${可设置范围}[1])
    append to list    ${可设置范围}    ${随机值}
    FOR    ${val}    IN    @{可设置范围}
        设置web参数量    平期错峰终止容量    ${val}
        ${参数值}    获取web参数量    平期错峰终止容量
        should be equal as numbers    ${参数值}    ${val}
    END
    [Teardown]    设置web设备参数量为默认值    峰期错峰终止容量    平期错峰终止容量    电池充电模式    电池配置    交流输入场景

错峰终止电压设置测试
    [Setup]    连接CSU
    设置web参数量    交流输入场景    市电
    设置web参数量    电池配置    纯铅酸
    设置web参数量    铅酸类型    储能电池
    设置web参数量    电池充电模式    时段
    sleep    10
    ${可设置范围}    获取web参数可设置范围    峰期错峰终止电压
    ${随机值}    evaluate    round(random.uniform(${可设置范围}[0],${可设置范围}[1]),1)
    append to list    ${可设置范围}    ${随机值}
    FOR    ${val}    IN    @{可设置范围}
        设置web参数量    峰期错峰终止电压    ${val}
        ${参数值}    获取web参数量    峰期错峰终止电压
        should be equal as numbers    ${参数值}    ${val}
    END
    ${可设置范围}    获取web参数可设置范围    平期错峰终止电压
    ${随机值}    evaluate    round(random.uniform(${可设置范围}[0],${可设置范围}[1]),1)
    append to list    ${可设置范围}    ${随机值}
    FOR    ${val}    IN    @{可设置范围}
        设置web参数量    平期错峰终止电压    ${val}
        ${参数值}    获取web参数量    平期错峰终止电压
        should be equal as numbers    ${参数值}    ${val}
    END
    [Teardown]    设置web设备参数量为默认值    峰期错峰终止电压    平期错峰终止电压    电池充电模式    电池配置    交流输入场景

电池各时段充电系数设置测试
    [Setup]    连接CSU
    设置web参数量    交流输入场景    市电
    设置web参数量    电池配置    纯铅酸
    设置web参数量    铅酸类型    快充电池
    设置web参数量    电池充电模式    时段
    sleep    10
    ${参数名称}    Create List    尖峰时段充电系数    高峰时段充电系数    平期时段充电系数    谷期时段充电系数
    FOR    ${val}    IN    @{参数名称}
        ${可设置范围}    获取web参数可设置范围    ${val}
        ${随机值}    evaluate    round(random.uniform(${可设置范围}[0],${可设置范围}[1]),3)
        设置web参数量    ${val}    ${可设置范围}[0]
        ${参数值}    获取web参数量    ${val}
        should be equal as numbers    ${参数值}    ${可设置范围}[0]
        设置web参数量    ${val}    ${可设置范围}[1]
        ${参数值}    获取web参数量    ${val}
        should be equal as numbers    ${参数值}    ${可设置范围}[1]
        设置web参数量    ${val}    ${随机值}
        ${参数值}    获取web参数量    ${val}
        should be equal as numbers    ${参数值}    ${随机值}
    END
    [Teardown]    设置web设备参数量为默认值    尖峰时段充电系数    高峰时段充电系数    平期时段充电系数    谷期时段充电系数    电池充电模式    电池配置    交流输入场景

周末和节假日错峰用例开关设置测试
    [Setup]    连接CSU
    设置web参数量    交流输入场景    市电
    设置web参数量    电池配置    纯铅酸
    设置web参数量    铅酸类型    储能电池
    设置web参数量    电池充电模式    时段
    sleep    10
    ${参数量}    获取web参数量    周末错峰用电使能
    ${转换值}    Convert to boolean    ${参数量}
    should be true    ${转换值}
    FOR    ${val}    IN RANGE    1    21
        ${参数名称}    catenate    SEPARATOR=    节假日日段_    ${val}
        ${参数量}    获取web参数量    ${参数名称}
        ${转换值}    Convert to boolean    ${参数量}
        should be true    ${转换值}
    END
    设置web参数量    电池充电模式    普通
    sleep    10
    FOR    ${val}    IN RANGE    1    21
        ${参数名称}    catenate    SEPARATOR=    节假日日段_    ${val}
        ${参数量}    获取web参数量    ${参数名称}
        ${转换值}    Convert to boolean    ${参数量}
        should not be true    ${转换值}
    END
    ${参数量}    获取web参数量    周末错峰用电使能
    ${转换值}    Convert to boolean    ${参数量}
    should not be true    ${转换值}
    [Teardown]    设置web设备参数量为默认值    电池充电模式    电池配置    交流输入场景

错峰终止容量设置容错测试 -逻辑错误
    [Setup]
    连接CSU   
    设置web参数量    交流输入场景    市电
    设置web参数量    电池配置    纯铅酸
    设置web参数量    铅酸类型    循环电池
    Comment    设置web参数量    下电模式    电池剩余容量
    判断web参数是否存在    下电模式
    设置web参数量    下电模式    电池剩余容量
    判断web参数是否存在    负载一次下电使能
    设置web参数量    负载一次下电使能    允许
    判断web参数是否存在    负载一次下电SOC阈值
    判断web参数是否存在    负载二次下电使能
    设置web参数量    负载二次下电使能    允许
    判断web参数是否存在    负载二次下电SOC阈值
    设置web参数量    电池充电模式    时段
    sleep    10
    ${错峰终止容量}    Create List    峰期错峰终止容量    平期错峰终止容量
    FOR    ${val}    IN    @{错峰终止容量}
        ${一次下电SOC值}    获取web实时数据    负载一次下电SOC阈值
        ${二次下电SOC值}    获取web实时数据    负载二次下电SOC阈值
        ${设置结果}    设置web参数量_带返回值    ${val}    ${一次下电SOC值}
        should not be true    ${设置结果}
        ${设置结果}    设置web参数量_带返回值    ${val}    ${二次下电SOC值}
        should not be true    ${设置结果}
        ${设置结果}    设置web参数量_带返回值    ${val}    ${一次下电SOC值}+4.9
        should not be true    ${设置结果}
        ${设置结果}    设置web参数量_带返回值    ${val}    ${二次下电SOC值}+4.9
        should not be true    ${设置结果}
    END
    [Teardown]    设置web设备参数量为默认值    电池充电模式    电池配置    下电模式    交流输入场景

错峰用电日段设置容错测试-输入错误
    [Setup]    连接CSU
    设置web参数量    交流输入场景    市电
    设置web参数量    电池配置    纯铅酸
    设置web参数量    铅酸类型    循环电池
    设置web参数量    电池充电模式    时段
    sleep    10
    ${异常数据}    Create list    01    01-01    00-01    01~01    00-00~00-00    04-31~05-01    06-01~06-31    02-30~02-02    02-01~02-31    01-00~01-01    01-30~01-32    12-20~12-32    12-30~13-01    日期1~日期2    riQi~eryue
    ...    @13~><)
    FOR    ${val}    IN    @{异常数据}
        ${返回值}    设置web参数量_带返回值    错峰日段_1    ${val}
        should not be true    ${返回值}
    END
    设置系统时间    2023-01-01 12:00:00
    设置web参数量    错峰日段_1    01-01~02-28
    设置web参数量    错峰日段_2    02-29~02-29
    设置web参数量    错峰日段_3    03-01~03-28
    sleep    10
    校验有错峰时段    1
    校验有错峰时段    2
    校验有错峰时段    3
    设置系统时间    2024-01-01 12:00:00
    sleep    10
    校验有错峰时段    1
    校验有错峰时段    2
    校验有错峰时段    3
    [Teardown]    Run keywords    同步系统时间
    ...    AND    错峰日段默认值校验
    ...    AND    设置web设备参数量为默认值    电池充电模式    电池配置    下电模式    交流输入场景

错峰用电时段设置容错测试-逻辑错误
    [Setup]    连接CSU
    设置web参数量    交流输入场景    市电
    设置web参数量    电池配置    智能锂电
    设置web参数量    电池充电模式    时段
    sleep    5
    ${错峰日段序号}    evaluate    random.randint(1,5)
    ${错峰日段}    catenate    SEPARATOR=    错峰日段_    ${错峰日段序号}
    设置web参数量    ${错峰日段}    01-01~01-01
    sleep    5
    错峰时段默认值校验    ${错峰日段序号}
    FOR    ${i}    IN RANGE    1    13
        ${错峰时段名称}    catenate    SEPARATOR=    错峰时段${错峰日段序号}_    ${i}
        设置web参数量    ${错峰时段名称}    12:00~11:00
        sleep    3
        校验错峰时段设置是否成功    ${错峰日段序号}    ${i}    False
        设置web参数量    ${错峰时段名称}    00:00~00:00
        sleep    5
        校验错峰时段设置是否成功    ${错峰日段序号}    ${i}    True
        设置web参数量    ${错峰时段名称}    23:59~00:00
        sleep    5
        校验错峰时段设置是否成功    ${错峰日段序号}    ${i}    False
    END
    ${错峰时段}    catenate    SEPARATOR=    错峰时段${错峰日段序号}    _1
    设置web参数量    ${错峰时段}    00:00~01:00
    FOR    ${i}    IN RANGE    2    13
        ${错峰时段名称}    catenate    SEPARATOR=    错峰时段${错峰日段序号}_    ${i}
        ${返回值}    设置web参数量_带返回值    ${错峰时段名称}    00:30~01:01
        should not be true    ${返回值}
    END
    设置web设备参数量为默认值    ${错峰时段}
    [Teardown]    Run keywords    错峰日段默认值校验
    ...    AND    设置web设备参数量为默认值    电池充电模式    电池配置    下电模式    交流输入场景

错峰用电时段设置容错测试-输入错误
    [Setup]    连接CSU
    设置web参数量    交流输入场景    市电
    设置web参数量    电池配置    常规锂电池
    设置web参数量    电池充电模式    时段
    sleep    5
    ${错峰日段序号}    evaluate    random.randint(1,5)
    ${错峰日段}    catenate    SEPARATOR=    错峰日段_    ${错峰日段序号}
    设置web参数量    ${错峰日段}    01-01~01-01
    sleep    5
    ${错峰时段序号}    evaluate    random.randint(1,12)
    ${错峰时段}    catenate    SEPARATOR=    错峰时段${错峰日段序号}_    ${错峰时段序号}
    ${有效时段}    Create List    00:10~02:00    00:00~00:00    23:59~23:59
    ${无效时段}    Create List    00:60~01:01    00:00~00:60    24:00~23:00    23:00~24:00    23:59-23:60    23:60~01:00    时间1~时间2    shi:Jian1~SHI:fen    *(&:!@~~"<>:)(    00-00~01-00
    FOR    ${val}    IN    @{有效时段}
        设置web参数量    ${错峰时段}    ${val}
        sleep    5
        校验错峰时段设置是否成功    ${错峰日段序号}    ${错峰时段序号}    True
        设置web设备参数量为默认值    ${错峰时段}
    END
    FOR    ${val}    IN    @{无效时段}
        ${返回值}    设置web参数量_带返回值    ${错峰时段}    ${val}
        should not be true    ${返回值}
        sleep    5
        校验错峰时段设置是否成功    ${错峰日段序号}    ${错峰时段序号}    False
    END
    [Teardown]    Run keywords    错峰日段默认值校验
    ...    AND    设置web设备参数量为默认值    电池充电模式    电池配置    下电模式    交流输入场景

*** Keywords ***
错峰日段默认值校验
    ${错峰日段}    Create List    错峰日段_1    错峰日段_2    错峰日段_3    错峰日段_4    错峰日段_5
    FOR    ${val}    IN    @{错峰日段}
        设置web设备参数量为默认值    ${val}
        ${获取参数值}    获取web参数量    ${val}
        should be equal as strings    ${获取参数值}    12-31~01-01
    END

参数异常值测试
    [Arguments]    ${参数名称}
    ${异常值}    Create List    电压    dy    A    >9)*&
    FOR    ${val}    IN    @{异常值}
        ${设置结果}    设置web参数量_带返回值    ${参数名称}    ${val}
        should not be true    ${设置结果}
    END

校验无错峰时段
    [Arguments]    ${日段序号}
    FOR    ${i}    IN RANGE    1    13
        ${错峰时段名称}    catenate    SEPARATOR=    错峰时段${日段序号}_    ${i}
        ${错峰时段数据}    获取web参数量    ${错峰时段名称}
        ${转换值}    Convert to boolean    ${错峰时段数据}
        should not be true    ${转换值}
    END

校验有错峰时段
    [Arguments]    ${日段序号}
    FOR    ${i}    IN RANGE    1    13
        ${错峰时段名称}    catenate    SEPARATOR=    错峰时段${日段序号}_    ${i}
        ${错峰时段数据}    获取web参数量    ${错峰时段名称}
        ${转换值}    Convert to boolean    ${错峰时段数据}
        should be true    ${转换值}
    END

错峰时段默认值校验
    [Arguments]    ${日段序号}
    FOR    ${i}    IN RANGE    1    13
        ${错峰时段名称}    catenate    SEPARATOR=    错峰时段${日段序号}_    ${i}
        设置web设备参数量为默认值    ${错峰时段名称}
        ${获取参数值}    获取web参数量    ${错峰时段名称}
        should be equal as strings    ${获取参数值}    23:59~00:00
    END

校验错峰时段设置是否成功
    [Arguments]    ${错峰日段序号}    ${错峰时段序号}    ${是否成功}
    ${错峰时段类型}    catenate    SEPARATOR=    错峰时段类型    ${错峰日段序号}    _    ${错峰时段序号}
    ${错峰时段类型数据}    获取web参数量    ${错峰时段类型}
    ${转换值}    Convert to boolean    ${错峰时段类型数据}
    should be equal as strings    ${转换值}    ${是否成功}
