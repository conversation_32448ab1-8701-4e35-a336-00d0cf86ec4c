*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_获取太阳能参数量
    ${能源网管数据}    能源网管协议_获取数据    获取太阳能参数量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取太阳能参数量
    Should Be True    ${数据对比结果}
    ${校验结果}    能源网管协议_校验数据上送完整性    ${能源网管数据}    太阳能    parameter
    Comment    Should Be True    ${校验结果}

power_sm_0002_设置太阳能参数量
    ${能源网管数据}    能源网管协议_设置单个参数    设置太阳能参数    ${BOARD_IP}    ${SSH}
    ${对比结果}    能源网管协议_批量对比参数设置    ${能源网管数据}    设置太阳能参数
    Should Be True    ${对比结果}

power_sm_0003_设置太阳能控制量
    @{PU随机list}    从1-15个数中随机选n个不重复的单体    ${北向协议PU最大数}    4
    FOR    ${PU序号}    IN    @{PU随机list}
        Wait Until Keyword Succeeds    30    4    能源网管协议_设置单个设备单个控制量    太阳能    3    PU设备统计    ${SSH}
    END

power_sm_0004_获取太阳能统计量
    ${能源网管数据}    能源网管协议_获取数据    获取太阳能统计量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取太阳能统计量
    Should Be True    ${数据对比结果}
    ${校验结果}    能源网管协议_校验数据上送完整性    ${能源网管数据}    太阳能    stastic data
    Comment    Should Be True    ${校验结果}
