*** Settings ***
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_获取实时告警测试（所有设备）
    [Tags]    4
    @{内部实时告警内容}    能源网管协议关键字_V30.获取web内部实时告警
    @{实时告警内容}    能源网管协议关键字_V30.获取web实时告警内容
    @{实时告警}    Combine Lists    ${实时告警内容}    ${内部实时告警内容}
    ${获取能源网管统一监控协议数据}    能源网管协议_获取历史记录数据    获取实时告警    ${EMPTY}    ${EMPTY}
    ${对比结果}    能源网管协议_对比实时告警    ${获取能源网管统一监控协议数据}    ${实时告警}    获取实时告警
    Should Be True    ${对比结果}
