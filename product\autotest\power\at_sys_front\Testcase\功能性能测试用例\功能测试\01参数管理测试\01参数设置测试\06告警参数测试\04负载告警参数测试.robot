*** Settings ***
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
01 直流电压高告警参数测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    直流电压高
    FOR    ${告警级别}    IN    严重    主要    次要    警告    屏蔽
        wait until keyword succeeds    10    1    设置web参数量    直流电压高    ${告警级别}
        sleep    5
        ${直流电压高告警级别}    wait until keyword succeeds    10    1    获取web参数量    直流电压高
        should be equal    ${直流电压高告警级别}    ${告警级别}
    END
    设置web参数量    直流电压高    主要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        ${告警干接点获取}    获取web参数量    直流电压高干接点
        run keyword if    ${告警干接点获取}!=${告警干接点设置}    设置web参数量    直流电压高干接点    ${告警干接点设置}
        sleep    3
        ${告警干接点获取}    获取web参数量    直流电压高干接点
        should be true    ${告警干接点获取}==${告警干接点设置}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    直流电压高干接点    0
    ...    AND    设置web参数量    直流电压高    主要

02 直流电压过高告警参数测试
    [Documentation]    干接点实际是否动作需要将输出干接点连接到输入干接点，输出干接点接UIB_X3_DI1；请在测试前连接好
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    直流电压过高
    FOR    ${告警级别}    IN    严重    主要    次要    警告    屏蔽
        wait until keyword succeeds    10    1    设置web参数量    直流电压过高    ${告警级别}
        sleep    5
        ${直流电压过高告警级别}    wait until keyword succeeds    10    1    获取web参数量    直流电压过高
        should be equal    ${直流电压过高告警级别}    ${告警级别}
    END
    设置web参数量    直流电压过高    主要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        ${告警干接点获取}    获取web参数量    直流电压过高干接点
        run keyword if    ${告警干接点获取}!=${告警干接点设置}    设置web参数量    直流电压过高干接点    ${告警干接点设置}
        sleep    3
        ${告警干接点获取}    获取web参数量    直流电压过高干接点
        should be true    ${告警干接点获取}==${告警干接点设置}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    直流电压过高干接点    0
    ...    AND    设置web参数量    直流电压过高    主要

03 直流电压低告警参数测试
    [Documentation]    直流电压高范围：53-59，默认58.5；>=直流电压低+1，<=直流电压过高。。。
    ...    直流电压低范围：44-55，默认46.5；<=直流电压高-1，>=直流电压过低。。。
    ...    直流电压过高：53-59，默认59。。。
    ...    直流电压过低：44-55，默认46.。。
    [Tags]    view    PMSA-NTest
    [Setup]    判断web参数是否存在    直流电压低
    FOR    ${告警级别}    IN    严重    主要    次要    警告    屏蔽
        wait until keyword succeeds    10    1    设置web参数量    直流电压低    ${告警级别}
        sleep    5
        ${直流电压低告警级别}    wait until keyword succeeds    10    1    获取web参数量    直流电压低
        should be equal    ${直流电压低告警级别}    ${告警级别}
    END
    设置web参数量    直流电压低    主要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        ${告警干接点获取}    获取web参数量    直流电压低干接点
        run keyword if    ${告警干接点获取}!=${告警干接点设置}    设置web参数量    直流电压低干接点    ${告警干接点设置}
        sleep    3
        ${告警干接点获取}    获取web参数量    直流电压低干接点
        should be true    ${告警干接点获取}==${告警干接点设置}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    直流电压低干接点    0
    ...    AND    设置web参数量    直流电压低    主要

04 直流电压过低告警参数测试
    [Documentation]    直流电压高范围：53-59，默认58.5；>=直流电压低+1，<=直流电压过高。。。
    ...    直流电压低范围：44-55，默认46.5；<=直流电压高-1，>=直流电压过低。。。
    ...    直流电压过高：53-59，默认59。。。
    ...    直流电压过低：44-55，默认46.。。
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    直流电压过低
    FOR    ${告警级别}    IN    严重    主要    次要    警告    屏蔽
        wait until keyword succeeds    10    1    设置web参数量    直流电压过低    ${告警级别}
        sleep    5
        ${直流电压过低告警级别}    wait until keyword succeeds    10    1    获取web参数量    直流电压过低
        should be equal    ${直流电压过低告警级别}    ${告警级别}
    END
    设置web参数量    直流电压过低    主要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        ${告警干接点获取}    获取web参数量    直流电压过低干接点
        run keyword if    ${告警干接点获取}!=${告警干接点设置}    设置web参数量    直流电压过低干接点    ${告警干接点设置}
        sleep    3
        ${告警干接点获取}    获取web参数量    直流电压过低干接点
        should be true    ${告警干接点获取}==${告警干接点设置}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    直流电压过低干接点    0
    ...    AND    设置web参数量    直流电压过低    主要

05 直流防雷器异常告警参数测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    直流防雷器异常
    FOR    ${告警级别}    IN    严重    主要    次要    警告    屏蔽
        wait until keyword succeeds    10    1    设置web参数量    直流防雷器异常    ${告警级别}
        sleep    5
        ${直流防雷器异常告警级别}    wait until keyword succeeds    10    1    获取web参数量    直流防雷器异常
        should be equal    ${直流防雷器异常告警级别}    ${告警级别}
    END
    设置web参数量    直流防雷器异常    主要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        ${告警干接点获取}    获取web参数量    直流防雷器异常干接点
        run keyword if    ${告警干接点获取}!=${告警干接点设置}    设置web参数量    直流防雷器异常干接点    ${告警干接点设置}
        sleep    3
        ${告警干接点获取}    获取web参数量    直流防雷器异常干接点
        should be true    ${告警干接点获取}==${告警干接点设置}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    直流防雷器异常干接点    0
    ...    AND    设置web参数量    直流防雷器异常    主要

06 一次下电分路断告警参数测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    一次下电分路断
    FOR    ${告警级别}    IN    严重    主要    次要    警告    屏蔽
        wait until keyword succeeds    10    1    设置web参数量    一次下电分路断_1    ${告警级别}
        sleep    5
        ${一次下电分路断告警级别}    wait until keyword succeeds    10    1    获取web参数量    一次下电分路断_1
        should be equal    ${一次下电分路断告警级别}    ${告警级别}
    END
    设置web参数量    一次下电分路断_1    主要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        ${告警干接点获取}    获取web参数量    一次下电分路断_1干接点
        run keyword if    ${告警干接点获取}!=${告警干接点设置}    设置web参数量    一次下电分路断_1干接点    ${告警干接点设置}
        sleep    3
        ${告警干接点获取}    获取web参数量    一次下电分路断_1干接点
        should be true    ${告警干接点获取}==${告警干接点设置}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    一次下电分路断_1干接点    0
    ...    AND    设置web参数量    一次下电分路断_1    主要

07 一次下电扩展分路断告警参数测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    一次下电扩展分路断
    FOR    ${告警级别}    IN    严重    主要    次要    警告    屏蔽
        wait until keyword succeeds    10    1    设置web参数量    一次下电扩展分路断    ${告警级别}
        sleep    5
        ${一次下电扩展分路断告警级别}    wait until keyword succeeds    10    1    获取web参数量    一次下电扩展分路断
        should be equal    ${一次下电扩展分路断告警级别}    ${告警级别}
    END
    设置web参数量    一次下电扩展分路断    主要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${告警干接点获取}    获取web参数量    一次下电扩展分路断干接点
        run keyword if    ${告警干接点获取}!=${告警干接点}    设置web参数量    一次下电扩展分路断干接点    ${告警干接点}
        sleep    3
        ${告警干接点获取}    获取web参数量    一次下电扩展分路断干接点
        should be true    ${告警干接点获取}==${告警干接点}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    一次下电扩展分路断干接点    0
    ...    AND    设置web参数量    一次下电扩展分路断    主要

08 二次下电分路断告警参数测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    二次下电分路断
    FOR    ${告警级别}    IN    严重    主要    次要    警告    屏蔽
        wait until keyword succeeds    10    1    设置web参数量    二次下电分路断_1    ${告警级别}
        sleep    5
        ${二次下电分路断告警级别}    wait until keyword succeeds    10    1    获取web参数量    二次下电分路断_1
        should be equal    ${二次下电分路断告警级别}    ${告警级别}
    END
    设置web参数量    二次下电分路断_1    主要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        ${告警干接点获取}    获取web参数量    二次下电分路断_1干接点
        run keyword if    ${告警干接点获取}!=${告警干接点设置}    设置web参数量    二次下电分路断_1干接点    ${告警干接点设置}
        sleep    3
        ${告警干接点获取}    获取web参数量    二次下电分路断_1干接点
        should be true    ${告警干接点获取}==${告警干接点设置}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    二次下电分路断_1干接点    0
    ...    AND    设置web参数量    二次下电分路断_1    主要

09 二次下电扩展分路断告警参数测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    二次下电扩展分路断
    FOR    ${告警级别}    IN    严重    主要    次要    警告    屏蔽
        wait until keyword succeeds    10    1    设置web参数量    二次下电扩展分路断    ${告警级别}
        sleep    5
        ${二次下电扩展分路断告警级别}    wait until keyword succeeds    10    1    获取web参数量    二次下电扩展分路断
        should be equal    ${二次下电扩展分路断告警级别}    ${告警级别}
    END
    设置web参数量    二次下电扩展分路断    主要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${告警干接点获取}    获取web参数量    二次下电扩展分路断干接点
        run keyword if    ${告警干接点获取}!=${告警干接点}    设置web参数量    二次下电扩展分路断干接点    ${告警干接点}
        sleep    3
        ${告警干接点获取}    获取web参数量    二次下电扩展分路断干接点
        should be true    ${告警干接点获取}==${告警干接点}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    二次下电扩展分路断干接点    0
    ...    AND    设置web参数量    二次下电扩展分路断    主要
