*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
节能模式最小开机数限制
    # [Setup]    测试用例前置条件
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    ${直流电压}    获取web实时数据    直流电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    2
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    2
    ${直流电压}    获取web实时数据    直流电压
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-3
    ${整流器1休眠状态}    获取web实时数据    整流器休眠状态-1
    run keyword if    '${整流器1休眠状态}'== '是'    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器唤醒-1
    ${整流器2休眠状态}    获取web实时数据    整流器休眠状态-2
    run keyword if    '${整流器2休眠状态}'== '是'    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器唤醒-2
    ${整流器3休眠状态}    获取web实时数据    整流器休眠状态-3
    run keyword if    '${整流器3休眠状态}'== '是'    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器唤醒-3
    sleep    20
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    2
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3

节能模式手动开关整流器
    # [Setup]    测试用例前置条件
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    ${整流器1休眠状态}    获取web实时数据    整流器休眠状态-1
    run keyword if    '${整流器1休眠状态}'=='是'    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器唤醒-1
    ${整流器2休眠状态}    获取web实时数据    整流器休眠状态-2
    run keyword if    '${整流器2休眠状态}'=='是'    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器唤醒-2
    ${整流器3休眠状态}    获取web实时数据    整流器休眠状态-3
    run keyword if    '${整流器3休眠状态}'=='是'    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器唤醒-3
    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-3
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    ${整流器1休眠状态}    获取web实时数据    整流器休眠状态-1
    run keyword if    '${整流器1休眠状态}'=='是'    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器唤醒-1
    ${整流器2休眠状态}    获取web实时数据    整流器休眠状态-2
    run keyword if    '${整流器2休眠状态}'=='是'    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器唤醒-2
    ${整流器3休眠状态}    获取web实时数据    整流器休眠状态-3
    run keyword if    '${整流器3休眠状态}'=='是'    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器唤醒-3
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3

节能模式下负载变化
    [Tags]    notest
    # [Setup]    测试用例前置条件
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3

节能模式下电池组信息变化
    [Tags]    notest
    # [Setup]    测试用例前置条件
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3

节能模式下负载率参数变化
    [Tags]    notest
    # [Setup]    测试用例前置条件
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3

节能模式下整流器轮换
    [Tags]    notest
    # [Setup]    测试用例前置条件
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3
