*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
自动非节能手动控制整流器
    # [Setup]    测试用例前置条件
    电池管理初始化
    系统复位
    #进入节能模式
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-3
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3

自动非节能不轮换整流器测试
    # [Setup]    测试用例前置条件
    电池管理初始化
    系统复位
    #进入节能模式
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器轮换周期    7
    ${下次轮换时间}    获取web实时数据    下次轮换时间
    ${起始时间}    获取系统时间
    ${设置轮换时间}    Subtract Time From Date    ${起始时间}    50s    #比16:00:00差5s
    设置系统时间    ${设置轮换时间}
    sleep    5m
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    ${下次轮换时间1}    获取web实时数据    下次轮换时间
    should be equal    ${下次轮换时间}    ${下次轮换时间1}
    [Teardown]    Run keywords    同步系统时间
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3

自动非节能手动切换电池管理状态
    # [Setup]    测试用例前置条件
    电池管理初始化
    系统复位
    #进入节能模式
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    测试最长时间    2
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池充电电流系数    0.6
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    浮充
    ${设置值}    获取web参数量    浮充电压
    Wait Until Keyword Succeeds    5m    2    直流电压及电池电压在指定范围内    ${设置值}
    # ${上限}    evaluate    ${设置值}+0.5
    # ${下限}    evaluate    ${设置值}-0.5
    # sleep    5m
    # ${获取数据}    获取web实时数据    直流电压
    # ${获取数据_bat}    获取web实时数据    电池电压-1
    # should be true    ${下限}<=${获取数据}<=${上限}
    # should be true    -0.5<${获取数据}-${获取数据_bat}<0.5
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    ${设置值}    获取web参数量    均充电压
    # ${上限}    evaluate    ${设置值}+0.3
    # ${下限}    evaluate    ${设置值}-0.3
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动均充
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    均充
    设置电池模拟器电压模式    铅酸电池    1    1    ${设置值}
    Wait Until Keyword Succeeds    5m    2    直流电压及电池电压在指定范围内    ${设置值}
    # sleep    5m
    # ${获取数据}    获取web实时数据    直流电压
    # ${获取数据_bat}    获取web实时数据    电池电压-1
    # should be true    ${下限}<=${获取数据}<=${上限}
    # should be true    -0.5<${获取数据}-${获取数据_bat}<0.5
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    ${设置值}    获取web参数量    浮充电压
    设置电池模拟器电压模式    铅酸电池    1    1    ${设置值}
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动测试
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    测试
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    4m    1    信号量数据值不为    电池管理状态    测试
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池充电电流系数    0.15
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3
    ...    AND    设置web参数量    电池充电电流系数    0.15
    ...    AND    重置电池模拟器输出

自动非节能切换到自动节能
    # [Setup]    测试用例前置条件
    电池管理初始化
    系统复位
    #进入节能模式
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    系统复位
    #进入节能模式
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    1m    1    设置web控制量    人工维护检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    人工维护检测
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    100

自动非节能整流器故障
    # [Setup]    测试用例前置条件
    电池管理初始化
    系统复位
    #进入节能模式
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    ${均充电压可设置范围}    获取web参数可设置范围    均充电压
    设置web参数量    均充电压    ${均充电压可设置范围}[0]
    ${输出高停机电压可设置范围}    获取web参数可设置范围    整流器输出高停机电压
    设置web参数量    整流器输出高停机电压    ${输出高停机电压可设置范围}[0]
    #使整流器输出过压保证无正常工作的整流器
    ${整流器输出高电压}    获取web参数量    整流器输出高停机电压
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动均充
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    均充
    向上调节电池电压    ${整流器输出高电压}+0.5
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    工作整流器数量    0
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    3m    1    信号量数据值为    电池管理状态    系统停电
    向下调节电池电压    53.5
    ${在线整流器数量}    获取web实时数据    在线整流器数量
    Wait Until Keyword Succeeds    5m10s    1    信号量数据值为    工作整流器数量    ${在线整流器数量}
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3
    ...    AND    重置电池模拟器输出
