*** Settings ***
Suite Setup
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
# 1363批量获取FBBMS数字量01
#     [Documentation]    21min
#     [Setup]
#     写入CSV文档    FB100B3数字量01获取测试    信号名称    信号值    结果    备注
#     连接CSU
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=digital data    INDEX_DEVICE_CH=FBBMS
#     ${排除列表}    create list
#     P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除数字量信号}    ${排除列表}    2    1    0    2
#     ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取锂电池段数字量~CID1=4AH>>    <<获取锂电池段数字量~CID2=43H>>    1    ${g_ver_1363}
#     ${web数据}    通过1104_data获取web数据    ${协议数据}    True    1
#     ${1104待测}    1104_获取1104/1363指定设备指定信号量    ${协议数据}    ${web数据}    ${列表1}    1
#     FOR    ${i}    IN    @{1104待测}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${1104协议名称}    Get From Dictionary    ${i}    1104_name
#         ${缺省值}    获取web参数上下限范围    ${信号名称}
#         Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    1363    smartli    ${信号名称}    只读    ${缺省值}[1]    FB100B3数字量01获取测试    null    null    null    <<获取锂电池段数字量~CID1=4AH>>    <<获取锂电池段数字量~CID2=43H>>    ${1104协议名称}    1
#         Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    1363    smartli    ${信号名称}    只读    ${缺省值}[2]    FB100B3数字量01获取测试    null    null    null    <<获取锂电池段数字量~CID1=4AH>>    <<获取锂电池段数字量~CID2=43H>>    ${1104协议名称}    1
#         Run Keyword IF    ${缺省值}[1]!=${缺省值}[0] and ${缺省值}[2]!=${缺省值}[0]    Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    1363    smartli    ${信号名称}    只读    ${缺省值}[0]    FB100B3数字量01获取测试    null    null    null    <<获取锂电池段数字量~CID1=4AH>>    <<获取锂电池段数字量~CID2=43H>>    ${1104协议名称}
#         ...    1
#     END
1363批量获取FBBMS数字量01
    [Documentation]    21min
    [Setup]
    写入CSV文档    FB100B3数字量01获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=digital data    INDEX_DEVICE_CH=FBBMS
    ${排除列表}    create list
    P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除数字量信号}    ${排除列表}    2    1    0    2
    ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取锂电池段数字量~CID1=4AH>>    <<获取锂电池段数字量~CID2=43H>>    1    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${协议数据}    True    1
    ${1104待测}    1104_获取1104/1363指定设备指定信号量    ${协议数据}    ${web数据}    ${列表1}    1
    # FOR    ${i}    IN    @{1104待测}
    #     ${信号名称}    Get From Dictionary    ${i}    signal_name
    #     ${1104协议名称}    Get From Dictionary    ${i}    1104_name
    #     ${缺省值}    获取web参数上下限范围    ${信号名称}
    #     Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    1363    smartli    ${信号名称}    只读    ${缺省值}[1]    FB100B3数字量01获取测试    null    null    null    <<获取锂电池段数字量~CID1=4AH>>    <<获取锂电池段数字量~CID2=43H>>    ${1104协议名称}    1
    #     Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    1363    smartli    ${信号名称}    只读    ${缺省值}[2]    FB100B3数字量01获取测试    null    null    null    <<获取锂电池段数字量~CID1=4AH>>    <<获取锂电池段数字量~CID2=43H>>    ${1104协议名称}    1
    #     Run Keyword IF    ${缺省值}[1]!=${缺省值}[0] and ${缺省值}[2]!=${缺省值}[0]    Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    1363    smartli    ${信号名称}    只读    ${缺省值}[0]    FB100B3数字量01获取测试    null    null    null    <<获取锂电池段数字量~CID1=4AH>>    <<获取锂电池段数字量~CID2=43H>>    ${1104协议名称}
    #     ...    1
    # END

    ${缺省值列表1}    获取缺省值列表    ${1104待测}    1    1363
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    1363    smartli    只读    ${缺省值列表1}    FB100B3数字量01获取测试    null    <<获取整流段数字量~CID1=4AH>>    <<获取整流段数字量~CID2=43H>>    1
    ${缺省值列表2}    获取缺省值列表    ${1104待测}    2    1363
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    1363    smartli    只读    ${缺省值列表2}    FB100B3数字量01获取测试   null    <<获取整流段数字量~CID1=4AH>>    <<获取整流段数字量~CID2=43H>>    1
    ${缺省值列表0}    获取缺省值列表    ${1104待测}    0    1363
    # ${缺省值列表0}    1104检查缺省值列表    ${缺省值列表0}    ${缺省值列表1}    ${缺省值列表2} 
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    1363    smartli    只读    ${缺省值列表0}   FB100B3数字量01获取测试    null    <<获取整流段数字量~CID1=4AH>>    <<获取整流段数字量~CID2=43H>>    1

1363_0002_电池运行状态获取1
    [Documentation]    0:充电/Charge;1:放电管理/Discharge;2:在线非浮充/On Not Float;3:离线/Off-line
    [Setup]    配置电池为普通铅酸电池
    连接CSU
    #子设备工具模拟
    Log    ${g_prtcl_1363}
    设置子工具值    smartli    all    只读    电池运行状态    1
    Wait Until Keyword Succeeds    15m    5    信号量数据值为    电池运行状态-1    放电管理
    ${1363_状态1}    获取一个数据的值    ${g_prtcl_1363}    <<获取锂电池段数字量~CID1=4AH>>    <<获取锂电池段数字量~CID2=43H>>    电池运行状态-1    1    ${g_ver_1363}
    should be true    ${1363_状态1}==1
    设置子工具值    smartli    all    只读    电池运行状态    2
    Wait Until Keyword Succeeds    15m    5    信号量数据值为    电池运行状态-1    在线非浮充
    ${1363_状态1}    获取一个数据的值    ${g_prtcl_1363}    <<获取锂电池段数字量~CID1=4AH>>    <<获取锂电池段数字量~CID2=43H>>    电池运行状态-1    1    ${g_ver_1363}
    should be true    ${1363_状态1}==2
    设置子工具值    smartli    all    只读    电池运行状态    3
    Wait Until Keyword Succeeds    15m    5    信号量数据值为    电池运行状态-1    离线
    ${1363_状态1}    获取一个数据的值    ${g_prtcl_1104}    <<获取锂电池段数字量~CID1=4AH>>    <<获取锂电池段数字量~CID2=43H>>    电池运行状态-1    1    ${g_ver_1363}
    should be true    ${1363_状态1}==3
    设置子工具值    smartli    all    只读    电池运行状态    0
    Wait Until Keyword Succeeds    15m    5    信号量数据值为    电池运行状态-1    充电
    ${1363_状态1}    获取一个数据的值    ${g_prtcl_1104}    <<获取锂电池段数字量~CID1=4AH>>    <<获取锂电池段数字量~CID2=43H>>    电池运行状态-1    1    ${g_ver_1363}
    should be true    ${1363_状态1}==0
    [Teardown]    配置电池为FB100B3