*** Settings ***
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***

系统停电时设置检测
    
    铁锂电池管理初始化
    关闭交流源输出
    设置负载电压电流    53.5    10
    打开负载输出
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    系统停电
    Wait Until Keyword Succeeds    5m    1    设置失败的Web控制量    启动电池检测
    [Teardown]    Run keywords    关闭负载输出
    ...    AND    打开交流源输出

电池组无效时设置检测
    
    铁锂电池管理初始化
    Wait Until Keyword Succeeds    5m    1s    设置web参数量    电池组容量_1    0
    sleep    3
    Wait Until Keyword Succeeds    5m    1    设置失败的web控制量    启动电池检测
    [Teardown]    设置web参数量    电池组容量_1    100

整流器异常时设置检测
    
    铁锂电池管理初始化
    Wait Until Keyword Succeeds    3m    2    信号量数据值为    交流供电状态    市电1
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    过渡
    ${充电电压可设置范围}    获取web参数可设置范围    充电电压
    设置web参数量    充电电压    ${充电电压可设置范围}[0]
    ${输出高停机电压可设置范围}    获取web参数可设置范围    整流器输出高停机电压
    设置web参数量    整流器输出高停机电压    ${输出高停机电压可设置范围}[0]
    #使整流器输出过压保证无正常工作的整流器
    ${整流器输出高电压}    获取web参数量    整流器输出高停机电压
    向上调节电池电压    ${整流器输出高电压}+0.5
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    工作整流器数量    0
    ${状态}    获取web实时数据    电池管理状态
    #无整流器放电状态启动浮充，应F
    设置负载电压电流    52.5    10
    打开负载输出
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    系统停电
    Wait Until Keyword Succeeds    5m    1    设置失败的Web控制量    启动电池检测
    #整流器过压恢复，5min应能恢复
    关闭负载输出
    向下调节电池电压    52.5
    ${在线整流器数量}    获取web实时数据    在线整流器数量
    Wait Until Keyword Succeeds    5m10s    1    信号量数据值为    工作整流器数量    ${在线整流器数量}
    [Teardown]    Run keywords    设置web设备参数量为默认值    充电电压    整流器输出高停机电压
    ...    AND    重置电池模拟器输出

充电保护时设置检测
    
    铁锂电池管理初始化
    显示属性配置    电池充电保护    数字量    web_attr=On    gui_attr=On
    设置子工具值    smartli    all    只读    电池充电保护    1
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    电池充电保护-1    是
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    电池充电保护-2    是
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    电池充电保护-3    是
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    检测
    显示属性配置    电池充电保护    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    设置子工具值    smartli    all    只读    电池充电保护    0

放电保护时设置检测
    
    铁锂电池管理初始化
    显示属性配置    电池放电保护    数字量    web_attr=On    gui_attr=On
    设置子工具值    smartli    all    只读    电池放电保护    1
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    电池放电保护-1    是
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    电池放电保护-2    是
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    电池放电保护-3    是
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    检测
    显示属性配置    电池充电保护    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    设置子工具值    smartli    all    只读    电池放电保护    0

BMS通讯断时设置检测
    
    铁锂电池管理初始化
    显示属性配置    BMS通信断状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    控制子工具运行停止    smartli    关闭
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    BMS通信断状态-1    异常
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    BMS通信断状态-2    异常
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    BMS通信断状态-3    异常
    ${级别设置值}    获取web参数量    BMS通信断告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    BMS通信断告警    严重
    wait until keyword succeeds    5m    2    查询指定告警信息    BMS通信断告警
    Wait Until Keyword Succeeds    5m    1    设置失败的web控制量    启动电池检测
    重新启动FB100B3    3
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    BMS通信断状态-1    正常
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    BMS通信断状态-2    正常
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    BMS通信断状态-3    正常
    wait until keyword succeeds    5m    2    查询指定告警信息不为    BMS通信断告警
    显示属性配置    BMS通信断状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    重新启动FB100B3    3
