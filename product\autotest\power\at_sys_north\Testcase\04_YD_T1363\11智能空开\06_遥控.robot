*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
智能空开电量清零
    FOR    ${智能空开序号}    IN RANGE    1    41
    ${智能空开序号}    Convert To String    ${智能空开序号}
    ${控制结果}    1104设置单个参数    ${g_prtcl_1363}    <<智能空开遥控~CID1=D3H>>    <<智能空开遥控~CID2=45H>>    智能空开电量清零    ${智能空开序号}    None    ${g_ver_1363}
    should be true    ${控制结果}
    sleep    20
    连接CSU
    @{历史事件内容}    获取web历史事件内容    ${empty}    ${empty}    所有    1    10
    ${是否清零}    Set Variable    False
    FOR    ${i}    IN    @{历史事件内容}
        ${是否清零}    Evaluate    "智能空开 _${智能空开序号} 智能空开电量清零" in "${i}"
        ${操作记录}    Set Variable    ${i}
        Exit For Loop If    ${是否清零}==True
    END
    Should Contain    ${操作记录}    north 1363
    Should Be True    ${是否清零}
    END

智能空开复位
    FOR    ${智能空开序号}    IN RANGE    1    41
    ${智能空开序号}    Convert To String    ${智能空开序号}
    ${控制结果}    1104设置单个参数    ${g_prtcl_1363}    <<智能空开遥控~CID1=D3H>>    <<智能空开遥控~CID2=45H>>    智能空开复位    ${智能空开序号}    None    ${g_ver_1363}
    should be true    ${控制结果}
    sleep    20
    连接CSU
    @{历史事件内容}    获取web历史事件内容    ${empty}    ${empty}    所有    1    10
    ${是否清零}    Set Variable    False
    FOR    ${i}    IN    @{历史事件内容}
        ${是否清零}    Evaluate    "智能空开 _${智能空开序号} 智能空开复位" in "${i}"
        ${操作记录}    Set Variable    ${i}
        Exit For Loop If    ${是否清零}==True
    END
    Should Contain    ${操作记录}    north 1363
    Should Be True    ${是否清零}
    END
