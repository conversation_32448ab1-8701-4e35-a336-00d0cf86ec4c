*** Settings ***
Suite Setup       交流电表测试前置条件
Suite Teardown    交流电表测试结束条件
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
snmp_0002_交流电表通讯断告警
    [Setup]
    连接CSU
    实时告警刷新完成
    ###电表2告警
    wait until keyword succeeds    1m    1    设置web参数量    交流电表通讯断告警    主要
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    设置子工具个数    ACmeter    2
    Comment    wait until keyword succeeds    2m    1    判断告警存在    交流电表通讯断告警-2
    wait until keyword succeeds    2m    1    判断告警存在    交流电表通讯断告警-3
    #产生
    Comment    ${snmp英文名1}    获取snmp单个告警英文名    交流电表通讯断告警_2
    Comment    ${告警产生1}    判断Trap告警产生    ${snmp英文名1}
    Comment    should be true    ${告警产生1}
    ${snmp英文名2}    获取snmp单个告警英文名    交流电表通讯断告警_3
    ${告警产生2}    判断Trap告警产生    ${snmp英文名2}
    should be true    ${告警产生2}
    #消失
    设置子工具个数    ACmeter    9
    Comment    Wait Until Keyword Succeeds    5m    1    判断告警不存在    交流电表通讯断告警-2
    Comment    打印web实时告警信息名称
    Comment    ${告警产生}    判断Trap告警消失    ${snmp英文名1}
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    交流电表通讯断告警-3
    Comment    should be true    ${告警产生}
    ${告警产生}    判断Trap告警消失    ${snmp英文名2}
    should be true    ${告警产生}
