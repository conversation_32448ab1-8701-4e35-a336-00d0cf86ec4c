*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
强制恢复默认参数测试
    连接CSU
    Wait Until Keyword Succeeds    30    2    设置web参数量    <<禁止所有告警~0x1001030010001>>    次要
    ${告警级别获取}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    30    2    设置web参数量    系统过载告警    次要
    ${告警级别获取}    获取web参数量    系统过载告警
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    30    2    设置web参数量    站点名称    Site-X#8    #站点名称需要继承
    ${站点名称获取值}    获取web参数量    站点名称
    should be true    '${站点名称获取值}'=='Site-X#8'
    Wait Until Keyword Succeeds    30    2    设置web参数量    经度    -180    #经度需要继承
    ${经度获取值}    获取web参数量    经度
    should be true    ${经度获取值}==-180
    Wait Until Keyword Succeeds    30    2    设置web参数量    纬度    -90    #纬度需要继承
    ${纬度获取值}    获取web参数量    纬度
    should be true    ${纬度获取值}==-90
    Wait Until Keyword Succeeds    30    2    设置web参数量    海拔高度    -1000    #海拔高度需要继承
    ${海拔高度获取值}    获取web参数量    海拔高度
    should be true    ${海拔高度获取值} ==-1000
    Wait Until Keyword Succeeds    30    2    设置web参数量    街道    Site-X#1    #街道需要继承
    ${街道获取值}    获取web参数量    街道
    should be true    '${街道获取值}'=='Site-X#1'
    Wait Until Keyword Succeeds    30    2    设置web参数量    城市    Site-X#2    #城市需要继承
    ${城市获取值}    获取web参数量    城市
    should be true    '${城市获取值}'=='Site-X#2'
    Wait Until Keyword Succeeds    30    2    设置web参数量    行政区    Site-X#3    #行政区需要继承
    ${行政区获取值}    获取web参数量    行政区
    should be true    '${行政区获取值}'=='Site-X#3'
    Wait Until Keyword Succeeds    30    2    设置web参数量    市电配置_1    无
    ${市电配置获取}    获取web参数量    市电配置_1
    should be true    '${市电配置获取}'=='无'
    Wait Until Keyword Succeeds    30    2    设置web参数量    CPU利用率高阈值    10    #下限
    ${CPU占用率高阈值获取}    获取web参数量    CPU利用率高阈值
    should be true    ${CPU占用率高阈值获取}==10
    Wait Until Keyword Succeeds    30    2    设置web参数量    系统过载告警阈值    10    #下限
    ${系统过载告警阈值获取}    获取web参数量    系统过载告警阈值
    should be true    ${系统过载告警阈值获取}==10
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    10    2    电池_1    电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.battcurr1}    8    3    直流配电    电池分流器电流_1
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.undefined}    9    4    无    无
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.battvolt1}    7    2    电池_1    电池电压
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.voltage}    5    2    直流配电    直流电压
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.humity}    6    2    系统运行环境    环境湿度
    强制恢复默认值并重新登录
    ${告警级别获取}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    should be true    '${告警级别获取}'=='严重'
    ${告警级别获取}    获取web参数量    系统过载告警
    should be true    '${告警级别获取}'=='严重'
    ${获取值}    获取web参数量    市电配置_1
    Should Be Equal    ${获取值}    有
    ${CPU利用率高阈值获取}    获取web参数量    CPU利用率高阈值
    ${缺省值}    获取web参数上下限范围    CPU利用率高阈值
    should be true    ${CPU利用率高阈值获取}==${缺省值}[0]
    ${系统过载告警阈值获取}    获取web参数量    系统过载告警阈值
    ${缺省值}    获取web参数上下限范围    系统过载告警阈值
    should be true    ${系统过载告警阈值获取}==${缺省值}[0]
    ${获取值}    获取web参数量    站点名称
    ${缺省值}    获取web参数上下限范围    站点名称
    Should Be Equal    ${获取值}    ${缺省值}[0]
    ${获取值}    获取web参数量    经度
    ${缺省值}    获取web参数上下限范围    经度
    Should Be Equal As Numbers    ${获取值}    ${缺省值}[0]    precision=4
    ${获取值}    获取web参数量    纬度
    ${缺省值}    获取web参数上下限范围    纬度
    Should Be Equal As Numbers    ${获取值}    ${缺省值}[0]    precision=4
    ${获取值}    获取web参数量    海拔高度
    ${缺省值}    获取web参数上下限范围    海拔高度
    Should Be Equal As Numbers    ${获取值}    ${缺省值}[0]    precision=4
    ${获取值}    获取web参数量    街道
    ${缺省值}    获取web参数上下限范围    街道
    Should Be Equal    ${获取值}    ${缺省值}[0]
    ${获取值}    获取web参数量    城市
    ${缺省值}    获取web参数上下限范围    城市
    Should Be Equal    ${获取值}    ${缺省值}[0]
    ${获取值}    获取web参数量    行政区
    ${缺省值}    获取web参数上下限范围    行政区
    Should Be Equal    ${获取值}    ${缺省值}[0]
    ${T1通道}    获取通道配置    ${plat.batttemp1}
    should be true    ${T1通道}[1]==0
    should be true    ${T1通道}[2]==1
    ${IB1通道}    获取通道配置    ${plat.battcurr1}
    should be true    ${IB1通道}[1]==0
    should be true    ${IB1通道}[2]==1
    ${IL1通道}    获取通道配置    ${plat.undefined}
    should be true    ${IL1通道}[1]==0
    should be true    ${IL1通道}[2]==1
    ${VB1通道}    获取通道配置    ${plat.battvolt1}
    should be true    ${VB1通道}[1]==0
    should be true    ${VB1通道}[2]==1
    ${VIN通道}    获取通道配置    ${plat.voltage}
    should be true    ${VIN通道}[1]==0
    should be true    ${VIN通道}[2]==1
    ${HUM通道}    获取通道配置    ${plat.humity}
    should be true    ${HUM通道}[1]==0
    should be true    ${HUM通道}[2]==1
