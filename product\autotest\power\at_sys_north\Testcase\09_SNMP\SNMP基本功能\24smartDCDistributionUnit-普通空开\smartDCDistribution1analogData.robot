*** Settings ***
Resource          ../../../../测试用例关键字.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp_test关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/Web关键字_V30.robot

*** Test Cases ***
snmp批量获取SDDU模拟量测试
    写入CSV文档    SDDU模拟量获取测试    信号名称    信号值    结果    备注
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=智能直流配电单元
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SDDU排除模拟量信号}    ${排除列表}
    ${设备名称}    Set Variable    smartDCDistributionUnit
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    ${设备名称}    analogData
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    1    snmp
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    snmp    SDU2    呼叫    ${缺省值列表}    SDDU模拟量获取测试    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    2    snmp
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    snmp    SDU2    呼叫    ${缺省值列表}    SDDU模拟量获取测试    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    0    snmp
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    snmp    SDU2    呼叫    ${缺省值列表}    SDDU模拟量获取测试    null
    断开连接SNMP

snmp获取配电单元负载功率
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    设置子工具值    SDU2    all    呼叫    SDU负载电流1    3000
    设置子工具值    SDU2    all    呼叫    SDU负载电流2    3000
    设置子工具值    SDU2    all    呼叫    SDU电压    60
    FOR    ${直流配电单元序号}    IN RANGE    1    3
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    配电单元负载功率_1-${直流配电单元序号}    180
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    配电单元负载功率_2-${直流配电单元序号}    180
        ${snmp获取值1}    获取SNMP数据_单个    distributionUnitLoadPower${直流配电单元序号}value    1
        should be equal as numbers    ${snmp获取值1}    180
        ${snmp获取值2}    获取SNMP数据_单个    distributionUnitLoadPower${直流配电单元序号}value    2
        should be equal as numbers    ${snmp获取值2}    180
    END
    设置子工具值    SDU2    all    呼叫    SDU负载电流1    0
    设置子工具值    SDU2    all    呼叫    SDU负载电流2    0
    FOR    ${直流配电单元序号}    IN RANGE    1    3
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    配电单元负载功率_1-${直流配电单元序号}    0
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    配电单元负载功率_2-${直流配电单元序号}    0
        ${snmp获取值1}    获取SNMP数据_单个    distributionUnitLoadPower${直流配电单元序号}value    1
        should be equal as numbers    ${snmp获取值1}    0
        ${snmp获取值2}    获取SNMP数据_单个    distributionUnitLoadPower${直流配电单元序号}value    2
        should be equal as numbers    ${snmp获取值2}    0
    END
