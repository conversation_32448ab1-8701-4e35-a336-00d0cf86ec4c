*** Settings ***
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_批量获取交流电表模拟量
    [Documentation]    21min
    写入CSV文档    交流电表模拟量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=交流电表
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${交流电表排除模拟量信号}    ${排除列表}
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    交流电表    analog data    ${交流电表排除模拟量信号}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    ${待测数据长度}    Get Length    ${power_sm待测}
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    1           power_sm
	Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    power_sm    ACmeter       只读    ${缺省值列表}    交流电表模拟量获取测试    获取交流电表模拟量        
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    2           power_sm
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    power_sm    ACmeter       只读    ${缺省值列表}    交流电表模拟量获取测试    获取交流电表模拟量        
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    0           power_sm  
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    power_sm    ACmeter       只读    ${缺省值列表}    交流电表模拟量获取测试    获取交流电表模拟量        
    




power_sm_0002_交流电表回路1总有功功率3
    连接CSU
    设置子工具值    ACmeter    all    只读    1路总有功功率    600
    FOR    ${交流电表序号}    IN RANGE    1    4
        wait until keyword succeeds    5m   5    信号量数据值为(强制获取)    交流电表回路1总有功功率-${交流电表序号}    600
        # ${web获取值1}    获取web实时数据    交流电表回路1总有功功率-${交流电表序号}
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取交流电表模拟量    交流电表回路1总有功功率-${交流电表序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    600
    END
    设置子工具值    ACmeter    all    只读    1路总有功功率    220
    FOR    ${交流电表序号}    IN RANGE    1    4
        wait until keyword succeeds    5m   5    信号量数据值为(强制获取)    交流电表回路1总有功功率-${交流电表序号}    220
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取交流电表模拟量    交流电表回路1总有功功率-${交流电表序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    220
    END
    设置子工具值    ACmeter    all    只读    1路总有功功率    0.1
    
    FOR    ${交流电表序号}    IN RANGE    1    4
        wait until keyword succeeds    5m   5    信号量数据值为(强制获取)    交流电表回路1总有功功率-${交流电表序号}    0.1
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取交流电表模拟量    交流电表回路1总有功功率-${交流电表序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    0.1
    END

power_sm_0003_交流电表回路1电量7
    [Documentation]    SnmpKeyword.py 1257 snmp_value= 32767.0016 web_value= 32767.00
    连接CSU
    设置子工具值    ACmeter    all    只读    1路当前组合有功电能    32767
    FOR    ${交流电表序号}    IN RANGE    1    4
        wait until keyword succeeds    5m   5    信号量数据值为(强制获取)    交流电表回路1电量-${交流电表序号}    32767
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取交流电表模拟量    交流电表回路1电量-${交流电表序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    32767
    END
    设置子工具值    ACmeter    all    只读    1路当前组合有功电能    -32767
    FOR    ${交流电表序号}    IN RANGE    1    4
        wait until keyword succeeds    5m   5    信号量数据值为(强制获取)    交流电表回路1电量-${交流电表序号}    -32767
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取交流电表模拟量    交流电表回路1电量-${交流电表序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    -32767
    END
    设置子工具值    ACmeter    all    只读    1路当前组合有功电能    0
    FOR    ${交流电表序号}    IN RANGE    1    4
        wait until keyword succeeds    5m   5    信号量数据值为(强制获取)    交流电表回路1电量-${交流电表序号}    0
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取交流电表模拟量    交流电表回路1电量-${交流电表序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    0
    END

power_sm_0004_交流电表回路2总有功功率10
    连接CSU
    设置子工具值    ACmeter    all    只读    2路总有功功率    600
    FOR    ${交流电表序号}    IN RANGE    1    4
        wait until keyword succeeds    5m   5    信号量数据值为(强制获取)    交流电表回路2总有功功率-${交流电表序号}    600
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取交流电表模拟量    交流电表回路2总有功功率-${交流电表序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    600
    END
    设置子工具值    ACmeter    all    只读    2路总有功功率    220
    FOR    ${交流电表序号}    IN RANGE    1    4
        wait until keyword succeeds    5m   5    信号量数据值为(强制获取)    交流电表回路2总有功功率-${交流电表序号}    220
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取交流电表模拟量    交流电表回路2总有功功率-${交流电表序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    220
    END
    设置子工具值    ACmeter    all    只读    2路总有功功率    0.1
    FOR    ${交流电表序号}    IN RANGE    1    4
        wait until keyword succeeds    5m   5    信号量数据值为(强制获取)    交流电表回路2总有功功率-${交流电表序号}    0.1
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取交流电表模拟量    交流电表回路2总有功功率-${交流电表序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    0.1
    END

power_sm_0005_交流电表回路2电量14
    [Documentation]    SnmpKeyword.py 1257 snmp_value= 32767.0016 web_value= 32767.00
    连接CSU
    设置子工具值    ACmeter    all    只读    2路当前组合有功电能    32767
    FOR    ${交流电表序号}    IN RANGE    1    4
        wait until keyword succeeds    5m   5    信号量数据值为(强制获取)    交流电表回路2电量-${交流电表序号}    32767
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取交流电表模拟量    交流电表回路2电量-${交流电表序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    32767
    END
    设置子工具值    ACmeter    all    只读    2路当前组合有功电能    -32767
    FOR    ${交流电表序号}    IN RANGE    1    4
        wait until keyword succeeds    5m   5    信号量数据值为(强制获取)    交流电表回路2电量-${交流电表序号}    -32767
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取交流电表模拟量    交流电表回路2电量-${交流电表序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    -32767
    END
    设置子工具值    ACmeter    all    只读    2路当前组合有功电能    0
    FOR    ${交流电表序号}    IN RANGE    1    4
        wait until keyword succeeds    5m   5    信号量数据值为(强制获取)    交流电表回路2电量-${交流电表序号}    0
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取交流电表模拟量    交流电表回路2电量-${交流电表序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    0
    END
