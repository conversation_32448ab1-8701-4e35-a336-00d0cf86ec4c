*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
直流电压高告警
    [Setup]    重置电池模拟器输出
    关闭交流源输出
    实时告警刷新完成
    #获取参数/默认值
    ${干接点上下限}    获取web参数量    直流电压高干接点
    ${告警干接点默认值}    run keyword if    '${干接点上下限}]'=='0'    set variable    无干接点
    ...    ELSE    set variable    A1
    #获取告警级别和输出干接点设置值
    ${级别设置值}    获取web参数量    直流电压高
    ${干接点设置值}    获取web参数量    直流电压高干接点
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    直流电压高    严重
    ${直流电压高告警}    判断告警不存在_带返回值    直流电压高
    run keyword if    '${直流电压高告警}' != '[]'    向下调节电池电压    52
    ${可设置范围}    获取web参数可设置范围    直流电压高阈值
    设置web参数量    直流电压高阈值    ${可设置范围}[0]
    ${ 直流电压高阈值}    获取web参数量    直流电压高阈值
    向上调节电池电压    ${ 直流电压高阈值}+0.5
    wait until keyword succeeds    5m    2    查询指定告警信息    直流电压高
    wait until keyword succeeds    30    1    设置web参数量    直流电压高    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    直流电压高
    ${直流电压高告警}    判断告警存在_带返回值    直流电压高
    should not be true    ${直流电压高告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    直流电压高    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    直流电压高
        sleep    5
        ${直流电压高告警级别}    获取web告警属性    直流电压高    告警级别
        should be equal    '${告警级别}'    '${直流电压高告警级别}'
    END
    
    wait until keyword succeeds    30    1    设置web参数量    直流电压高    主要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    直流电压高    告警干接点
        run keyword if    ${告警干接点获取}!=0    wait until keyword succeeds    30    1    设置web参数量    直流电压高干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        wait until keyword succeeds    30    1    设置web参数量    直流电压高干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    直流电压高    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0
    END
    向下调节电池电压    ${ 直流电压高阈值}-0.5
    wait until keyword succeeds    5m    1    判断告警不存在    直流电压高
    [Teardown]    Run keywords    直流电压参数恢复默认值
    ...    AND    设置web参数量    直流电压高干接点    0
    ...    AND    设置web参数量    直流电压高    主要
    ...    AND    重置电池模拟器输出
    ...    AND    打开交流源输出
    

直流电压过高告警
    [Documentation]    干接点实际是否动作需要将输出干接点连接到输入干接点，输出干接点接${plat.Inrelay1Status}；请在测试前连接好
    [Setup]    重置电池模拟器输出
    关闭交流源输出
    实时告警刷新完成
    #获取参数/默认值
    ${干接点上下限}    获取web参数量    直流电压过高干接点
    ${告警干接点默认值}    run keyword if    '${干接点上下限}]'=='0'    set variable    无干接点
    ...    ELSE    set variable    A1
    #获取告警级别和输出干接点设置值
    ${级别设置值}    获取web参数量    直流电压过高
    ${干接点设置值}    获取web参数量    直流电压过高干接点
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    直流电压过高    严重
    ${直流电压过高告警}    判断告警不存在_带返回值    直流电压过高
    run keyword if    '${直流电压过高告警}' != '[]'    向下调节电池电压    52
    ${可设置范围}    获取web参数可设置范围    直流电压高阈值
    设置web参数量    直流电压高阈值    ${可设置范围}[0]
    ${可设置范围}    获取web参数可设置范围    直流电压过高阈值
    设置web参数量    直流电压过高阈值    ${可设置范围}[0]
    ${ 直流电压过高阈值}    获取web参数量    直流电压过高阈值
    向上调节电池电压    ${ 直流电压过高阈值}+0.5
    wait until keyword succeeds    5m    2    查询指定告警信息    直流电压过高
    wait until keyword succeeds    30    1    设置web参数量    直流电压过高    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    直流电压过高
    ${直流电压过高告警}    判断告警存在_带返回值    直流电压过高
    should not be true    ${直流电压过高告警}
    FOR    ${告警级别}    ${告警级别}    IN    严重    主要    次要    警告
        设置web参数量    直流电压过高    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    直流电压过高
        sleep    3
        ${直流电压过高告警级别}    获取web告警属性    直流电压过高    告警级别
        should be equal    ${告警级别}    ${直流电压过高告警级别}
    END
    
    设置web参数量    直流电压过高    主要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    直流电压过高    告警干接点
        run keyword if    ${告警干接点获取}!=0    设置web参数量    直流电压过高干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        设置web参数量    直流电压过高干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    直流电压过高    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0
    END
    向下调节电池电压    ${ 直流电压过高阈值}-0.5
    wait until keyword succeeds    5m    1    判断告警不存在    直流电压过高
    [Teardown]    Run keywords    直流电压参数恢复默认值
    ...    AND    设置web参数量    直流电压过高干接点    0
    ...    AND    设置web参数量    直流电压过高    主要
    ...    AND    重置电池模拟器输出
    ...    AND    打开交流源输出
    

直流电压低告警
    [Documentation]    直流电压高范围：53-59，默认58.5；>=直流电压低+1，<=直流电压过高。。。
    ...    直流电压低范围：44-55，默认46.5；<=直流电压高-1，>=直流电压过低。。。
    ...    直流电压过高：53-59，默认59。。。
    ...    直流电压过低：44-55，默认46.。。
    [Setup]    重置电池模拟器输出
    关闭交流源输出
    实时告警刷新完成
    #获取参数/默认值
    ${干接点上下限}    获取web参数量    直流电压低干接点
    ${告警干接点默认值}    run keyword if    '${干接点上下限}]'=='0'    set variable    无干接点
    ...    ELSE    set variable    A1
    #获取告警级别和输出干接点设置值
    ${级别设置值}    获取web参数量    直流电压低
    ${干接点设置值}    获取web参数量    直流电压低干接点
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    直流电压低    严重
    ${直流电压低告警}    判断告警不存在_带返回值    直流电压低
    run keyword if    '${直流电压低告警}' != '[]'    向上调节电池电压    53.5
    ${可设置范围}    获取web参数可设置范围    直流电压低阈值
    设置web参数量    直流电压低阈值    ${可设置范围}[1]
    ${ 直流电压低阈值}    获取web参数量    直流电压低阈值
    向下调节电池电压    ${ 直流电压低阈值}-0.5
    wait until keyword succeeds    5m    2    查询指定告警信息    直流电压低
    wait until keyword succeeds    30    1    设置web参数量    直流电压低    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    直流电压低
    ${直流电压低告警}    判断告警存在_带返回值    直流电压低
    should not be true    ${直流电压低告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        设置web参数量    直流电压低    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    直流电压低
        sleep    5
        ${直流电压低告警级别}    获取web告警属性    直流电压低    告警级别
        should be equal    ${告警级别}    ${直流电压低告警级别}
    END
    
    设置web参数量    直流电压低    主要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    直流电压低    告警干接点
        run keyword if    ${告警干接点获取}!=0    设置web参数量    直流电压低干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        设置web参数量    直流电压低干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    直流电压低    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0
    END
    向上调节电池电压    ${ 直流电压低阈值}+1
    wait until keyword succeeds    5m    1    判断告警不存在    直流电压低
    [Teardown]    Run keywords    直流电压参数恢复默认值
    ...    AND    设置web参数量    直流电压低    主要
    ...    AND    设置web参数量    直流电压低干接点    0
    ...    AND    重置电池模拟器输出
    ...    AND    打开交流源输出
    

直流电压过低告警
    [Documentation]    直流电压高范围：53-59，默认58.5；>=直流电压低+1，<=直流电压过高。。。
    ...    直流电压低范围：44-55，默认46.5；<=直流电压高-1，>=直流电压过低。。。
    ...    直流电压过高：53-59，默认59。。。
    ...    直流电压过低：44-55，默认46.。。
    [Setup]    重置电池模拟器输出
    关闭交流源输出
    实时告警刷新完成
    ${干接点上下限}    获取web参数量    直流电压过低干接点
    ${告警干接点默认值}    run keyword if    '${干接点上下限}]'=='0'    set variable    无干接点
    ...    ELSE    set variable    A1
    #获取告警级别和输出干接点设置值
    ${级别设置值}    获取web参数量    直流电压过低
    ${干接点设置值}    获取web参数量    直流电压过低干接点
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    直流电压过低    严重
    Comment    wait until keyword succeeds    5m    1    查询指定告警信息不为    直流电压高
    ${直流电压过高告警}    判断告警不存在_带返回值    直流电压过低
    run keyword if    '${直流电压过高告警}' != '[]'    向上调节电池电压    53.5
    ${可设置范围}    获取web参数可设置范围    直流电压低阈值
    设置web参数量    直流电压低阈值    ${可设置范围}[1]
    ${可设置范围}    获取web参数可设置范围    直流电压过低阈值
    设置web参数量    直流电压过低阈值    ${可设置范围}[1]
    ${ 直流电压过低阈值}    获取web参数量    直流电压过低阈值
    向下调节电池电压    ${ 直流电压过低阈值}-0.5
    wait until keyword succeeds    5m    2    查询指定告警信息    直流电压过低
    wait until keyword succeeds    30    1    设置web参数量    直流电压过低    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    直流电压过低
    ${直流电压过低告警}    判断告警存在_带返回值    直流电压过低
    should not be true    ${直流电压过低告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    直流电压过低    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    直流电压过低
        sleep    3
        ${直流电压过低告警级别}    获取web告警属性    直流电压过低    告警级别
        should be equal    '${告警级别}'    '${直流电压过低告警级别}'
    END
    
    wait until keyword succeeds    30    1    设置web参数量    直流电压过低    主要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    直流电压过低    告警干接点
        run keyword if    ${告警干接点获取}!=0    wait until keyword succeeds    30    1    设置web参数量    直流电压过低干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        ${告警干接点获取1}    获取web告警属性    直流电压过低    告警干接点
        wait until keyword succeeds    30    1    设置web参数量    直流电压过低干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    直流电压过低    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0
    END
    向上调节电池电压    ${ 直流电压过低阈值}+1
    wait until keyword succeeds    5m    1    判断告警不存在    直流电压过低
    [Teardown]    Run keywords    直流电压参数恢复默认值
    ...    AND    设置web参数量    直流电压过低    主要
    ...    AND    设置web参数量    直流电压过低干接点    0
    ...    AND    重置电池模拟器输出
    ...    AND    打开交流源输出
    

直流防雷器异常告警测试
    实时告警刷新完成
    设置web参数量    直流防雷器异常    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    直流防雷器异常
    ${告警}    判断告警存在_带返回值    直流防雷器异常
    should not be true    ${告警}
    模拟数字量告警    直流防雷器异常    ON
    sleep    10
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    10    1    设置web参数量    直流防雷器异常    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    直流防雷器异常
        sleep    3
        ${告警级别获取}    获取web告警属性    直流防雷器异常    告警级别
        should be equal    ${告警级别}    ${告警级别获取}
    END
    
    设置web参数量    直流防雷器异常    次要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    直流防雷器异常    告警干接点
        run keyword if    ${告警干接点获取}!=0    设置web参数量    直流防雷器异常干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        设置web参数量    直流防雷器异常干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    直流防雷器异常    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0
    END
    模拟数字量告警    直流防雷器异常    OFF
    sleep    10
    [Teardown]    run keywords    设置web参数量    直流防雷器异常干接点    0
    ...    AND    模拟数字量告警    直流防雷器异常    OFF
    
