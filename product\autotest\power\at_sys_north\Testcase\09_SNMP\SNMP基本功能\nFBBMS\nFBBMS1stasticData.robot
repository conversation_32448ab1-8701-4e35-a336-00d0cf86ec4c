*** Settings ***
Default Tags      3
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp_test关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/Web关键字_V30.robot
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
snmp_0002_累计放电电量获取测试√
    [Setup]
    连接CSU
    显示属性配置    累计放电电量    统计量    web_attr=On    gui_attr=On
    进行SNMP_V2/V3连接    ${snmp连接方式}
    #子设备工具模拟
    # 设置子工具值    smartli    all    只读    累计放电电量高字节    0
    # 设置子工具值    smartli    all    只读    累计放电电量低字节    0
    ${目标值}    Convert To String    0
    设置子工具值    smartli    all    只读    累计放电电量    ${目标值}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    累计放电电量-${锂电序号}    0
        ${snmp获取值1}    获取SNMP数据_单个    dischargeTotalPower${锂电序号}value
        should be equal as numbers    ${snmp获取值1}    0
    END
    # 设置子工具值    smartli    all    只读    累计放电电量高字节    1
    # 设置子工具值    smartli    all    只读    累计放电电量低字节    0
    ${目标值1}    Convert To String    65536
    设置子工具值    smartli    all    只读    累计放电电量    ${目标值1}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    累计放电电量-${锂电序号}    65536
        ${snmp获取值1}    获取SNMP数据_单个    dischargeTotalPower${锂电序号}value
        should be equal as numbers    ${snmp获取值1}    65536
    END
    # 设置子工具值    smartli    all    只读    累计放电电量高字节    0
    # 设置子工具值    smartli    all    只读    累计放电电量低字节    100
    ${目标值2}    Convert To String    100
    设置子工具值    smartli    all    只读    累计放电电量    ${目标值2}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    累计放电电量-${锂电序号}    100
        ${snmp获取值1}    获取SNMP数据_单个    dischargeTotalPower${锂电序号}value
        should be equal as numbers    ${snmp获取值1}    100
    END
    断开连接SNMP
    显示属性配置    累计放电电量    统计量    web_attr=Off    gui_attr=Off

snmp_0004_累计放电容量获取测试√
    [Setup]
    连接CSU
    显示属性配置    累计放电容量    统计量    web_attr=On    gui_attr=On
    进行SNMP_V2/V3连接    ${snmp连接方式}
    #子设备工具模拟
    # 设置子工具值    smartli    all    只读    累计放电容量高字节    0
    # 设置子工具值    smartli    all    只读    累计放电容量低字节    0
    ${目标值}    Convert To String    0
    设置子工具值    smartli    all    只读    累计放电容量    ${目标值}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    累计放电容量-${锂电序号}    0
        ${snmp获取值1}    获取SNMP数据_单个    dischargeToatalCapacity${锂电序号}value
        should be true    ${snmp获取值1}==0
    END
    # 设置子工具值    smartli    all    只读    累计放电容量高字节    1
    # 设置子工具值    smartli    all    只读    累计放电容量低字节    0
    ${目标值1}    Convert To String    65536
    设置子工具值    smartli    all    只读    累计放电容量    ${目标值1}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    累计放电容量-${锂电序号}    65536
        ${snmp获取值1}    获取SNMP数据_单个    dischargeToatalCapacity${锂电序号}value
        should be true    ${snmp获取值1}==65536
    END
    # 设置子工具值    smartli    all    只读    累计放电容量高字节    0
    # 设置子工具值    smartli    all    只读    累计放电容量低字节    10000
    ${目标值2}    Convert To String    10000
    设置子工具值    smartli    all    只读    累计放电容量    ${目标值2}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    累计放电容量-${锂电序号}    10000
        ${snmp获取值1}    获取SNMP数据_单个    dischargeToatalCapacity${锂电序号}value
        should be true    ${snmp获取值1}==10000
    END
    断开连接SNMP
    显示属性配置    累计放电容量    统计量    web_attr=Off    gui_attr=Off

snmp_0006_fBBMS1stasticData√
    [Documentation]    def,min,max:0,0,65536
    Comment    @{锂电序号lsit}    Create list    1    8    {snmp锂电最大数}
    Comment
    Comment
    Comment    : FOR    ${锂电序号}    IN    @{锂电序号lsit}
    @{锂电单体随机list}    从1-15个数中随机选n个不重复的单体    ${北向协议铁锂最大数}    3
    FOR    ${锂电序号}    IN    @{锂电单体随机list}
        ${比较结果}    对比数据_V2C    fBBMS${锂电序号}stasticData
        should be true    ${比较结果}
    END
