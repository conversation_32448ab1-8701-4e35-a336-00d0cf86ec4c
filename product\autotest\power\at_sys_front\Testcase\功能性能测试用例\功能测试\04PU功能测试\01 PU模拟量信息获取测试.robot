*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
PU输出电压获取测试
    [Tags]    no test
    [Setup]    测试用例前置条件
    Comment    电池管理初始化
    仅有市电条件上电
    连接CSU
    关闭交流源输出
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    10    2    设置web参数量    均充电压    56
    Wait Until Keyword Succeeds    10    2    设置web参数量    浮充电压    56
    Wait Until Keyword Succeeds    15m    2    信号量数据值大于    PU输出电压-1    55
    ${直流电压}    获取web实时数据    直流电压
    ${PU1输出电压}    获取web实时数据    PU输出电压-1
    log    ${PU1输出电压}
    should be true    55.42<=${PU1输出电压}<=56.58
    Wait Until Keyword Succeeds    10    2    设置web参数量    浮充电压    48
    sleep    2
    Wait Until Keyword Succeeds    10    2    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    15m    2    信号量数据值小于    PU输出电压-1    48.2
    ${PU1输出电压}    获取web实时数据    PU输出电压-1
    log    ${PU1输出电压}
    should be true    47.52<=${PU1输出电压}<=48.48

PU输出电流获取测试
    [Tags]    no test
    [Setup]    测试用例前置条件
    Comment    电池管理初始化
    仅有市电条件上电
    连接CSU
    关闭交流源输出
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    10    2    设置web参数量    均充电压    56
    Wait Until Keyword Succeeds    10    2    设置web参数量    浮充电压    56
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    PU输出电压-1    55
    ${直流电压}    获取web实时数据    直流电压
    ${PU1输出电压}    获取web实时数据    PU输出电压-1
    log    ${PU1输出电压}
    should be true    55.42<=${PU1输出电压}<=56.58
    Wait Until Keyword Succeeds    10    2    设置web参数量    浮充电压    48
    sleep    2
    Wait Until Keyword Succeeds    10    2    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    5m    2    信号量数据值小于    PU输出电压-1    48.2
    ${PU1输出电压}    获取web实时数据    PU输出电压-1
    log    ${PU1输出电压}
    should be true    47.52<=${PU1输出电压}<=48.48

PU温度获取测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具模拟量    1    模拟量    PU温度    -38.8
    设置子工具模拟量    10    模拟量    PU温度    -39.9
    设置子工具模拟量    20    模拟量    PU温度    -39.8
    设置子工具模拟量    30    模拟量    PU温度    -39.7
    设置子工具模拟量    40    模拟量    PU温度    -39.6
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU温度-1    -38.8
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU温度-10    -39.9
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU温度-20    -39.8
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU温度-30    -39.7
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU温度-40    -39.6
    设置子工具模拟量    1    模拟量    PU温度    70
    设置子工具模拟量    10    模拟量    PU温度    69
    设置子工具模拟量    20    模拟量    PU温度    69.8
    设置子工具模拟量    30    模拟量    PU温度    69.7
    设置子工具模拟量    40    模拟量    PU温度    69.6
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU温度-1    70
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU温度-10    69
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU温度-20    69.8
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU温度-30    69.7
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU温度-40    69.6
    设置子工具模拟量    1    模拟量    PU温度    25
    设置子工具模拟量    10    模拟量    PU温度    26
    设置子工具模拟量    20    模拟量    PU温度    27
    设置子工具模拟量    30    模拟量    PU温度    24
    设置子工具模拟量    40    模拟量    PU温度    28
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU温度-1    25
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU温度-10    26
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU温度-20    27
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU温度-30    24
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU温度-40    28

PU输入电压测试
    [Setup]
    连接CSU
    设置子工具模拟量    1    模拟量    PU输入电压    150
    设置子工具模拟量    10    模拟量    PU输入电压    149
    设置子工具模拟量    20    模拟量    PU输入电压    148
    设置子工具模拟量    30    模拟量    PU输入电压    147
    设置子工具模拟量    40    模拟量    PU输入电压    146
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入电压-1    150
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入电压-10    149
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入电压-20    148
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入电压-30    147
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入电压-40    146
    设置子工具模拟量    1    模拟量    PU输入电压    0
    设置子工具模拟量    10    模拟量    PU输入电压    1
    设置子工具模拟量    20    模拟量    PU输入电压    2
    设置子工具模拟量    30    模拟量    PU输入电压    3
    设置子工具模拟量    40    模拟量    PU输入电压    4
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入电压-1    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入电压-10    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入电压-20    2
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入电压-30    3
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入电压-40    4
    设置子工具模拟量    1    模拟量    PU输入电压    65
    设置子工具模拟量    10    模拟量    PU输入电压    64
    设置子工具模拟量    20    模拟量    PU输入电压    66
    设置子工具模拟量    30    模拟量    PU输入电压    67
    设置子工具模拟量    40    模拟量    PU输入电压    63
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入电压-1    65
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入电压-10    64
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入电压-20    66
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入电压-30    67
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入电压-40    63

PU输入电流测试
    [Setup]
    连接CSU
    设置子工具模拟量    1    模拟量    PU输入电流    35
    设置子工具模拟量    10    模拟量    PU输入电流    34.9
    设置子工具模拟量    20    模拟量    PU输入电流    34.8
    设置子工具模拟量    30    模拟量    PU输入电流    34.7
    设置子工具模拟量    40    模拟量    PU输入电流    34.6
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入电流-1    35
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入电流-10    34.9
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入电流-20    34.8
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入电流-30    34.7
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入电流-40    34.6
    设置子工具模拟量    1    模拟量    PU输入电流    0
    设置子工具模拟量    10    模拟量    PU输入电流    1
    设置子工具模拟量    20    模拟量    PU输入电流    2
    设置子工具模拟量    30    模拟量    PU输入电流    3
    设置子工具模拟量    40    模拟量    PU输入电流    4
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入电流-1    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入电流-10    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入电流-20    2
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入电流-30    3
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入电流-40    4

PU散热片温度获取测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具模拟量    1    模拟量    PU散热片温度    -40
    设置子工具模拟量    10    模拟量    PU散热片温度    -39.9
    设置子工具模拟量    20    模拟量    PU散热片温度    -39.8
    设置子工具模拟量    30    模拟量    PU散热片温度    -39.7
    设置子工具模拟量    40    模拟量    PU散热片温度    -39.6
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU散热片温度-1    -40
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU散热片温度-10    -39.9
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU散热片温度-20    -39.8
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU散热片温度-30    -39.7
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU散热片温度-40    -39.6
    设置子工具模拟量    1    模拟量    PU散热片温度    150
    设置子工具模拟量    10    模拟量    PU散热片温度    151
    设置子工具模拟量    20    模拟量    PU散热片温度    149
    设置子工具模拟量    30    模拟量    PU散热片温度    148
    设置子工具模拟量    40    模拟量    PU散热片温度    147
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU散热片温度-1    150
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU散热片温度-10    151
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU散热片温度-20    149
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU散热片温度-30    148
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU散热片温度-40    147
    设置子工具模拟量    1    模拟量    PU散热片温度    24
    设置子工具模拟量    10    模拟量    PU散热片温度    25
    设置子工具模拟量    20    模拟量    PU散热片温度    26
    设置子工具模拟量    30    模拟量    PU散热片温度    27
    设置子工具模拟量    40    模拟量    PU散热片温度    28
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU散热片温度-1    24
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU散热片温度-10    25
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU散热片温度-20    26
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU散热片温度-30    27
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU散热片温度-40    28

PU风扇转速获取测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具模拟量    1    模拟量    PU风扇转速    1000
    设置子工具模拟量    10    模拟量    PU风扇转速    999
    设置子工具模拟量    20    模拟量    PU风扇转速    998
    设置子工具模拟量    30    模拟量    PU风扇转速    997
    设置子工具模拟量    40    模拟量    PU风扇转速    996
    Wait Until Keyword Succeeds    15m    5    信号量数据值为    PU风扇转速-1    1000
    Wait Until Keyword Succeeds    15m    5    信号量数据值为    PU风扇转速-10    999
    Wait Until Keyword Succeeds    15m    5    信号量数据值为    PU风扇转速-20    998
    Wait Until Keyword Succeeds    15m    5    信号量数据值为    PU风扇转速-30    997
    Wait Until Keyword Succeeds    15m    5    信号量数据值为    PU风扇转速-40    996
    设置子工具模拟量    1    模拟量    PU风扇转速    0
    设置子工具模拟量    10    模拟量    PU风扇转速    1
    设置子工具模拟量    20    模拟量    PU风扇转速    2
    设置子工具模拟量    30    模拟量    PU风扇转速    3
    设置子工具模拟量    40    模拟量    PU风扇转速    4
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU风扇转速-1    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU风扇转速-10    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU风扇转速-20    2
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU风扇转速-30    3
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU风扇转速-40    4
    设置子工具模拟量    1    模拟量    PU风扇转速    20
    设置子工具模拟量    10    模拟量    PU风扇转速    21
    设置子工具模拟量    20    模拟量    PU风扇转速    22
    设置子工具模拟量    30    模拟量    PU风扇转速    23
    设置子工具模拟量    40    模拟量    PU风扇转速    24
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU风扇转速-1    20
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU风扇转速-10    21
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU风扇转速-20    22
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU风扇转速-30    23
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU风扇转速-40    24

PU槽位地址
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具模拟量    1    模拟量    PU组地址    1
    设置子工具模拟量    1    模拟量    PU组内地址    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU槽位地址-1    1
    设置子工具模拟量    10    模拟量    PU组地址    1
    设置子工具模拟量    10    模拟量    PU组内地址    10
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU槽位地址-10    10
    设置子工具模拟量    20    模拟量    PU组地址    2
    设置子工具模拟量    20    模拟量    PU组内地址    8
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU槽位地址-20    20
    设置子工具模拟量    30    模拟量    PU组地址    3
    设置子工具模拟量    30    模拟量    PU组内地址    6
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU槽位地址-30    30
    设置子工具模拟量    40    模拟量    PU组地址    4
    设置子工具模拟量    40    模拟量    PU组内地址    4
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU槽位地址-40    40
    [Teardown]    Run keywords    设置子工具值    PU    all    模拟量    PU组地址
    ...    1
    ...    AND    设置子工具值    PU    all    模拟量    PU组内地址
    ...    1
