
#-*-coding:utf-8 -*-
#####################################################################################
# File Name:    PM3000_GPIB.py
# Discription:  实现与PM3000的连接和数据读写
# Author:   Pengy；
# DevEnv:   winXP/7x86+python2.7
# Notes:    使用GPIB与PM3000连接，读取和控制PM3000
#           建议使用19200bps的波特率，以提高通讯速率 
# Log:      20180110Pengy 
################################################################################
import sys
import time
import visa
import FLUKE_8808A1
import FLUKE_8808A2

####读取文件地址，使用RIDE时，改为ADD[1]；使用本地编译时改为ADD[0]####
ADD={0:'..\.',
     1:''
    }

add=ADD[1]
#####################################################

ERR_CODE = {1:'1:非法的功能码',
            2:'2:非法的数据地址',
            3:'3:非法的数据值',
            4:'4:服务/从机故障'
            }
########################################
#设置一个时间的格式
ISOTIME = '%Y-%m-%d %X'
########################################
class PM3000_GPIB():
    def __init__(self):     
##        self.bSerialIsOpen = False
##        self.ser = serial.Serial()
        self.rm = visa.ResourceManager()
        print(self.rm.list_resources())
##        self.inst = self.rm.open_resource('GPIB0::2::INSTR')
##        print self.inst.read(':FNC:CH1:VLT?')
##        self.inst.write(':FNC:CH1:VLT?')
##        print self.inst.read()
##打开GPIB====================================================================================================================
    def Open_PM3000_GPIB(self,add):
        """
        根据仪器设置的地址进行设置
        例如：GPIB0::1::INSTR
        """
        flag=1
        add='GPIB0::'+add+'::INSTR'
        self.rm = visa.ResourceManager()
##        print(self.rm.list_resources())
##        self.SetCmd(u'选择ATHD')#选择ATHD
##        self.SetCmd(u'选择VTHD')#选择VTHD
        while(flag!=0 and flag<5):
            try:
                self.inst = self.rm.open_resource(add)
                self.SetCmd(u'选择ATHD','','no')#选择ATHD
                time.sleep(1)
                self.SetCmd(u'选择VTHD','','no')#选择VTHD
                time.sleep(1)
                print self.inst.query('*IDN?').strip('\n')
                print u'打开GPIB：',add
                flag=0
                
            except:
                print u'打开GPIB失败'
                time.sleep(1)
                flag=flag+1

#创建测试文档
##    def CreateTestDoc(self,name):
##        try:
##            savef=open(add+r'.\TestRecord\''+name+'.csv','a')
##            sStr=(u'时间,'+u'输入电压(V),'+u'输出电压(V),'
##                  +u'输出电流（A）,'+u'源效应,'+u'测试结果,'
##                  )
##            print>>savef,sStr.encode('gbk')
##            savef.close()
##        except:
##            print u'打开文件出错！'
            
#创建源效应测试文档
    def CreatePowerEffectDoc(self):
        try:
            savef=open(add+r'.\TestRecord\PowerEffectTest.csv','a')
            sStr=(u'时间,'+u'输入电压(V),'+u'输出电压(V),'
                  +u'输出电流（A）,'+u'源效应,'+u'测试结果,'
                  )
            print>>savef,sStr.encode('gbk')
            savef.close()
        except:
            print u'打开PowerEffectTest文件出错！'

#创建负载效应测试文档
    def CreateLoadEffectDoc(self):
        """""""""""""""""""""""""""""""""""""""
        创建负载效应测试文档或者表头，在每测试完一组数据后，
        需要创建一次表头
        """""""""""""""""""""""""""""""""""""""
        try:
            savef=open(add+r'.\TestRecord\LoadEffectTest.csv','a')
            sStr=(u'时间,'+u'输入电压(V),'+u'输出电压(V),'
                  +u'输出电流（A）,'+u'负载效应,'+u'测试结果,'
                  )
            print>>savef,sStr.encode('gbk')
            savef.close()
        except:
            print u'打开LoadEffectTest文件出错！'

#创建性能指标测试文档
    def CreatePerformanceTestDoc(self):
        try:
            savef=open(add+r'.\TestRecord\PerformanceTest.csv','a')
            sStr=(u'时间,'+u'输入电压(V),'+u'输出电压(V),'
                  +u'输出电流（A）,'+u'输出功率（W）,'+u'输入功率（W）,'+u'效率,'+u'效率测试结果,'
                  +u'A相PF,'+u'B相PF,'+u'C相PF,'+u'PF测试结果,'
                  +u'A相电流THD,'+u'B相电流THD,'+u'C相电流THD,'+u'电流THD测试结果'
                  )
            print>>savef,sStr.encode('gbk')
            savef.close()
        except:
            print u'打开PerformanceTest文件出错！'

#创建稳压精度测试文档
    def CreateVoltagePrecisionDoc(self):
        try:
            savef=open(add+r'.\TestRecord\VoltagePrecisionTest.csv','a')
            sStr=(u'时间,'+u'输出电压设定值(V),'+u'输入电压(V),'
                  +u'输出电流（A）,'+u'输出电压(V),'
                  +u'稳压精度,'+u'测试结果,'
                  )
            print>>savef,sStr.encode('gbk')
            savef.close()
        except:
            print u'打开VoltagePrecisionTest文件出错！'
#创建稳流精度测试文档
    def CreateCurrentPrecisionDoc(self):
        try:
            savef=open(add+r'.\TestRecord\CurrentPrecisionTest.csv','a')
            sStr=(u'时间,'+u'输出电流设定值(V),'+u'输入电压(V),'
                  +u'输出电压(V),'+u'输出电流（A）,'
                  +u'稳流精度,'+u'测试结果,'
                  )
            print>>savef,sStr.encode('gbk')
            savef.close()
        except:
            print u'打开CurrentPrecisionTest文件出错！'
##设置FLUKE8808A电压模式======================================================================
    def SetVolMode(self):
        """""""""""""""""""""""""""""""""""""""
        设置FLUKE8808A电压模式
        """""""""""""""""""""""""""""""""""""""
        FLUKE_1=FLUKE_8808A1.FLUKE_8808A()
        FLUKE_2=FLUKE_8808A2.FLUKE_8808A()
        FLUKE_1.ConnectCom_FLUKE_8808A(1,9600)#测量电压
        FLUKE_2.ConnectCom_FLUKE_8808A(2,9600)#测量电流
        FLUKE_1.SetVolMode('VDC')
        FLUKE_2.SetVolMode('VDC')
        FLUKE_1.CloseCom_FLUKE_8808A()
        FLUKE_2.CloseCom_FLUKE_8808A()
        print u'设置FLUKE8808A电压模式成功！'
##使用FLUKE8808A测量输出电压电流功率=========================
    def ReadOutPut(self):
        """""""""""""""""""""""""""""""""""""""
        COM1:接FLUKE表测量电压
        COM2:接FLUKE表测量电流
        """""""""""""""""""""""""""""""""""""""
        Rlist=[]
        Voltage=''
        Current=''
        FLUKE_1=FLUKE_8808A1.FLUKE_8808A()
        FLUKE_2=FLUKE_8808A2.FLUKE_8808A()
        FLUKE_1.ConnectCom_FLUKE_8808A(1,9600)#测量电压
        FLUKE_2.ConnectCom_FLUKE_8808A(2,9600)#测量电流
        Voltage=round(float(FLUKE_1.ReadVol('VDC')),6)   #单位V
        Current=round(float(FLUKE_2.ReadVol('VDC'))*3*1000,6)   #单位A
        Power=round(float(Voltage*Current),6)   #单位W
        FLUKE_1.CloseCom_FLUKE_8808A()
        FLUKE_2.CloseCom_FLUKE_8808A()
        Rlist=[Voltage,Current,Power]#输出电压、电流、功率
##        print u'输出数据：',Rlist
        return Rlist
#稳压精度测试=============================================
    def VoltagePrecisionTest(self,OutputVol='750',InputVol='380'):
        """""""""""""""""""""""""""""""""""""""
        稳压精度测试
        生成测试文档
        OutputVol:给定的输出电压
        InputVol：给定的输入电压
        COM1:接FLUKE表测量电压
        COM2:接FLUKE表测量电流
        """""""""""""""""""""""""""""""""""""""
        OutPutList=[]
        OutPutList=self.ReadOutPut()
##组包，准备打印：
        OutStr=(time.strftime("%H:%M:%S")+','   #时间
                +OutputVol+','                   #输出电压设定值
                +InputVol+','                   #输入电压                
                +str(round(float(OutPutList[1]),2))+',' #输出电流
                +str(round(float(OutPutList[0]),2))+',' #输出电压
                +'####'+','                     #稳压精度
                +'####'+','                     #通过准则
                )

        print u'稳压精度测试数据：',OutStr

        try:
            savef=open(add+r'.\TestRecord\VoltagePrecisionTest.csv','a')
            print>>savef,OutStr.encode('gbk')
            savef.close()
        except:
            print u'打开VoltagePrecisionTest文件出错！'    
##稳压精度测试结果判断=============================================
    def VoltagePrecisionJudge(self,rule='0.1'):
        """""""""""""""""""""""""""""""""""""""
        读取负载效应数据文档，根据通过准则判断测试结果
        rule：通过准则
        """""""""""""""""""""""""""""""""""""""
        f = open( add+r'.\TestRecord\VoltagePrecisionTest.csv', 'r')
        csvlist = []
        tmplist=[]
        addlist=[]
        savelist=[]
        calclist=[]
        failtag=0
        testtag=0
        outStr=''
        csvlist = f.readlines()
        f.close()

        if len(csvlist)<10:
            print u'测试结果判断数据不全，请重新测试！1'
            return        
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')            
            if tmplist[0].decode('gbk')==u'时间':
                addlist.append(i)
##        print addlist
        for i in addlist:
##            print 't1',addlist,len(csvlist[i+1:i+10])
##            print 't2',csvlist[i+1:i+10]
            if (len(csvlist[i+1:i+10])>8 and
                csvlist[i+1][0:4].decode('gbk')!=u'时间' and
                csvlist[i+2][0:4].decode('gbk')!=u'时间' and
                csvlist[i+3][0:4].decode('gbk')!=u'时间' and
                csvlist[i+4][0:4].decode('gbk')!=u'时间' and
                csvlist[i+5][0:4].decode('gbk')!=u'时间' and
                csvlist[i+6][0:4].decode('gbk')!=u'时间' and
                csvlist[i+7][0:4].decode('gbk')!=u'时间' and
                csvlist[i+8][0:4].decode('gbk')!=u'时间' and
                csvlist[i+9][0:4].decode('gbk')!=u'时间' 
                ):
                calclist=[float(csvlist[i+1].split(',')[4]),float(csvlist[i+2].split(',')[4]),float(csvlist[i+3].split(',')[4]),
                          float(csvlist[i+4].split(',')[4]),float(csvlist[i+5].split(',')[4]),float(csvlist[i+6].split(',')[4]),
                          float(csvlist[i+7].split(',')[4]),float(csvlist[i+8].split(',')[4]),float(csvlist[i+9].split(',')[4])
                          ]
##                print calclist
                #计算稳压精度
                maxvolt=max(calclist)
                minvolt=min(calclist)
                DeltaVolt=max(abs(maxvolt-float(csvlist[i+5].split(',')[4])),abs(minvolt-float(csvlist[i+5].split(',')[4])))
##                print DeltaVolt
                VoltagePrecision=round(abs(DeltaVolt/float(csvlist[i+5].split(',')[4])*100),3)
                print u'稳压精度：',VoltagePrecision
                #判断稳压精度
                if VoltagePrecision>float(rule):
                    testreslut='FAIL'
                else:
                    testreslut='PASS'
                savelist=csvlist[i+9][0:-1].split(',')
                savelist[5]=str(VoltagePrecision)
                savelist[6]=testreslut
                csvlist[i+9]=','.join(savelist)+'\n'
                testtag+=1
        outStr=''.join(csvlist)[:-1]
##        print outStr

        try:
            savef=open(add+r'.\TestRecord\VoltagePrecisionTest.csv','w')
            print>>savef,outStr
            savef.close()
        except:
            print u'打开VoltagePrecisionTest文件出错！'

        ##判断稳压精度测试结果是否通过
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
##            print tmplist,len(tmplist)
            if len(tmplist)>=7 and tmplist[6]=='FAIL':
                print u'测试不通过！'
                failtag=1
                return False
        if failtag==0 and testtag>0:
            print u'测试通过！'
            return True
        else:
            print u'测试结果判断数据不全，请重新测试！2'
            return False
#稳流精度测试=============================================
    def CurrentPrecisionTest(self,OutputCur='750',InputVol='380'):
        """""""""""""""""""""""""""""""""""""""
        稳流精度测试
        生成测试文档
        OutputCur:输出电流给定值
        InputVol：输入电压
        COM1:接FLUKE表测量电压
        COM2:接FLUKE表测量电流
        """""""""""""""""""""""""""""""""""""""
        OutPutList=[]
        OutPutList=self.ReadOutPut()
##组包，准备打印：
        OutStr=(time.strftime("%H:%M:%S")+','   #时间
                +OutputCur+','                   #输出电流设定值
                +InputVol+','                   #输入电压                
                +str(round(float(OutPutList[0]),2))+',' #输出电压
                +str(round(float(OutPutList[1]),2))+',' #输出电流
                +'####'+','                     #稳流精度
                +'####'+','                     #通过准则
                )

        print u'稳流精度测试数据：',OutStr

        try:
            savef=open(add+r'.\TestRecord\CurrentPrecisionTest.csv','a')
            print>>savef,OutStr.encode('gbk')
            savef.close()
        except:
            print u'打开CurrentPrecisionTest文件出错！'    
##稳流精度测试结果判断=============================================
    def CurrentPrecisionJudge(self,rule='0.1'):
        """""""""""""""""""""""""""""""""""""""
        读取稳流精度数据文档，根据通过准则判断测试结果
        rule：通过准则
        """""""""""""""""""""""""""""""""""""""
        f = open( add+r'.\TestRecord\CurrentPrecisionTest.csv', 'r')
        csvlist = []
        tmplist=[]
        addlist=[]
        savelist=[]
        calclist=[]
        failtag=0
        testtag=0
        outStr=''
        csvlist = f.readlines()
        f.close()
        if len(csvlist)<10:
            print u'测试结果判断数据不全，请重新测试！'
            return False
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')            
            if tmplist[0].decode('gbk')==u'时间':
                addlist.append(i)
##        print addlist
        for i in addlist:
##            print len(csvlist[i+1:i+9])
            if (len(csvlist[i+1:i+10])>8 and
                csvlist[i+1][0:4].decode('gbk')!=u'时间' and
                csvlist[i+2][0:4].decode('gbk')!=u'时间' and
                csvlist[i+3][0:4].decode('gbk')!=u'时间' and
                csvlist[i+4][0:4].decode('gbk')!=u'时间' and
                csvlist[i+5][0:4].decode('gbk')!=u'时间' and
                csvlist[i+6][0:4].decode('gbk')!=u'时间' and
                csvlist[i+7][0:4].decode('gbk')!=u'时间' and
                csvlist[i+8][0:4].decode('gbk')!=u'时间' and
                csvlist[i+9][0:4].decode('gbk')!=u'时间' 
                ):
                calclist=[float(csvlist[i+1].split(',')[4]),float(csvlist[i+2].split(',')[4]),float(csvlist[i+3].split(',')[4]),
                          float(csvlist[i+4].split(',')[4]),float(csvlist[i+5].split(',')[4]),float(csvlist[i+6].split(',')[4]),
                          float(csvlist[i+7].split(',')[4]),float(csvlist[i+8].split(',')[4]),float(csvlist[i+9].split(',')[4])
                          ]
##                print calclist
                #计算稳流精度
                maxCurr=max(calclist)
                minCurr=min(calclist)
                DeltaCurr=max(abs(maxCurr-float(csvlist[i+5].split(',')[4])),abs(minCurr-float(csvlist[i+5].split(',')[4])))
##                print DeltaCurr
                CurrentPrecision=round(abs(DeltaCurr/float(csvlist[i+5].split(',')[4])*100),3)
                print u'稳流精度：',CurrentPrecision
                #判断稳流精度
                if CurrentPrecision>float(rule):
                    testreslut='FAIL'
                else:
                    testreslut='PASS'
                savelist=csvlist[i+9][0:-1].split(',')
##                print 't1',savelist
                savelist[5]=str(CurrentPrecision)
                savelist[6]=testreslut
                csvlist[i+9]=','.join(savelist)+'\n'
                testtag+=1

        outStr=''.join(csvlist)[:-1]
##        print testtag

        try:
            savef=open(add+r'.\TestRecord\CurrentPrecisionTest.csv','w')
            print>>savef,outStr
            savef.close()
        except:
            print u'打开CurrentPrecisionTest文件出错！'

        ##判断稳流精度测试结果是否通过
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
##            print tmplist,len(tmplist)
            if len(tmplist)>=7 and tmplist[6]=='FAIL':
                print u'测试不通过！'
                failtag=1
                return False
        if failtag==0 and testtag>0:
            print u'测试通过！'
            return True
        else:
            print u'测试结果判断数据不全，请重新测试！'
            return False

#负载效应测试=============================================
    def LoadEffectTest(self,InputVol='380'):
        """""""""""""""""""""""""""""""""""""""
        负载效应测试
        生成测试文档
        COM1:接FLUKE表测量电压
        COM2:接FLUKE表测量电流
        """""""""""""""""""""""""""""""""""""""
        OutPutList=[]
        OutPutList=self.ReadOutPut()
##组包，准备打印：
        OutStr=(time.strftime("%H:%M:%S")+','   #时间
                +InputVol+','                   #设置的输入电压   
                +str(round(float(OutPutList[0]),2))+',' #输出电压
                +str(round(float(OutPutList[1]),2))+',' #输出电流
                +'####'+','
                +'####'+','
                )

        print u'负载效应测试数据：',OutStr

        try:
            savef=open(add+r'.\TestRecord\LoadEffectTest.csv','a')
            print>>savef,OutStr.encode('gbk')
            savef.close()
        except:
            print u'打开LoadEffectTest文件出错！'        

##负载效应测试结果判断=============================================
    def LoadEffectJudge(self,rule='0.1'):
        """""""""""""""""""""""""""""""""""""""
        读取负载效应数据文档，根据通过准则判断测试结果
        rule：负载效应通过准则
        """""""""""""""""""""""""""""""""""""""
        f = open( add+r'.\TestRecord\LoadEffectTest.csv', 'r')
        csvlist = []
        tmplist=[]
        addlist=[]
        savelist=[]
        calclist=[]
        failtag=0
        outStr=''
        csvlist = f.readlines()
        f.close()
        
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')            
            if tmplist[0].decode('gbk')==u'时间':
                addlist.append(i)
##        print addlist
        for i in addlist:
            if (csvlist[i+1][0:4].decode('gbk')!=u'时间' and
                csvlist[i+2][0:4].decode('gbk')!=u'时间' and
                csvlist[i+3][0:4].decode('gbk')!=u'时间' 
                ):
                calclist=[csvlist[i+1].split(','),csvlist[i+2].split(','),csvlist[i+3].split(',')]
##                print calclist
                voltage1=float(calclist[0][2])
                voltage2=float(calclist[1][2])
                voltage3=float(calclist[2][2])
                maxvolt=max(voltage1,voltage2,voltage3)
                minvolt=min(voltage1,voltage2,voltage3)
                LoadEffect=round(abs(maxvolt-minvolt)/voltage1*100,3)
                if LoadEffect>float(rule):
                    testreslut='FAIL'
                else:
                    testreslut='PASS'
                savelist=csvlist[i+3][0:-1].split(',')
                savelist[4]=str(LoadEffect)
                savelist[5]=testreslut
                csvlist[i+3]=','.join(savelist)+'\n'

        outStr=''.join(csvlist)


        try:
            savef=open(add+r'.\TestRecord\LoadEffectTest.csv','w')
            print>>savef,outStr
            savef.close()
        except:
            print u'打开LoadEffectTest文件出错！'

        ##判断源效应测试结果是否通过
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
##            print tmplist,len(tmplist)
            if len(tmplist)>=7 and tmplist[5]=='FAIL':
                print u'测试不通过！'
                failtag=1
                return False
        if failtag==0:
            print u'测试通过！'
            return True
        
#源效应测试=============================================
    def PowerEffectTest(self,InputVol='380'):
        """""""""""""""""""""""""""""""""""""""
        源效应测试
        生成测试文档
        COM1:接FLUKE表测量电压
        COM2:接FLUKE表测量电流
        """""""""""""""""""""""""""""""""""""""
        OutPutList=[]
        OutPutList=self.ReadOutPut()
##组包，准备打印：
        OutStr=(time.strftime("%H:%M:%S")+','   #时间
                +InputVol+','                   #设置的输入电压   
                +str(round(float(OutPutList[0]),2))+',' #输出电压
                +str(round(float(OutPutList[1]),2))+',' #输出电流
                +'####'+','
                +'####'+','
                )

        print u'源效应测试数据：',OutStr

        try:
            savef=open(add+r'.\TestRecord\PowerEffectTest.csv','a')
            print>>savef,OutStr.encode('gbk')
            savef.close()
        except:
            print u'打开PowerEffectTest文件出错！'        

##源效应测试结果判断=============================================
    def PowerEffectJudge(self,rule='-0.1'):
        """""""""""""""""""""""""""""""""""""""
        读取源效应数据文档，根据通过准则判断测试结果
        rule：源效应通过准则
        """""""""""""""""""""""""""""""""""""""
        f = open( add+r'.\TestRecord\PowerEffectTest.csv', 'r')
        csvlist = []
        tmplist=[]
        addlist=[]
        savelist=[]
        calclist=[]
        outStr=''
        failtag=0
        csvlist = f.readlines()
        f.close()
        
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')            
            if tmplist[0].decode('gbk')==u'时间':
                addlist.append(i)
##        print addlist
        for i in addlist:
            if (csvlist[i+1][0:4].decode('gbk')!=u'时间' and
                csvlist[i+2][0:4].decode('gbk')!=u'时间' and
                csvlist[i+3][0:4].decode('gbk')!=u'时间' 
                ):
                calclist=[csvlist[i+1].split(','),csvlist[i+2].split(','),csvlist[i+3].split(',')]
##                print calclist
                voltage1=float(calclist[0][2])
                voltage2=float(calclist[1][2])
                voltage3=float(calclist[2][2])
                maxvolt=max(voltage1,voltage2,voltage3)
                minvolt=min(voltage1,voltage2,voltage3)
                LoadEffect=round(abs(maxvolt-minvolt)/voltage1*100,3)
                if LoadEffect>float(rule):
                    testreslut='FAIL'
                else:
                    testreslut='PASS'
                savelist=csvlist[i+3][0:-1].split(',')
                savelist[4]=str(LoadEffect)
                savelist[5]=testreslut
                csvlist[i+3]=','.join(savelist)+'\n'

        outStr=''.join(csvlist)
    

        try:
            savef=open(add+r'.\TestRecord\PowerEffectTest.csv','w')
            print>>savef,outStr
            savef.close()
        except:
            print u'打开PowerEffectTest文件出错！'

        ##判断源效应测试结果是否通过
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
##            print tmplist,len(tmplist)
            if len(tmplist)>=7 and tmplist[5]=='FAIL':
                print u'测试不通过！'
                failtag=1
                return False
        if failtag==0:
            print u'测试通过！'
            return True
##性能指标测试=============================================
    def PerformanceTest(self,Efficiency='0.95',PF='0.95',THDA='12'):
        """""""""""""""""""""""""""""""""""""""
        性能指标测试
        生成测试文档
        Efficiency:效率测试通过准则
        PF:PF通过准则
        THDA：电流THD通过准则
        COM1:接FLUKE表测量电压
        COM2:接FLUKE表测量电流
        """""""""""""""""""""""""""""""""""""""
        print u'=========性能指标测试============'
        OutPutList=[]
        eff=0
        PFResult='PASS'
        THDAResult='PASS'
##        self.SetVolMode()
        OutPutList=self.ReadOutPut()
##        print OutPutList
        self.SetCmd(u'设置接线方式','3P4','no')
        time.sleep(1)
        self.SetCmd(u'选择通道','CH1','no')
        time.sleep(2)#等2s再读数据
        ReadStr=self.QueryPM3000Cmd(u'读CH1的数据','','no')
        ReadList1=ReadStr.strip('\n').split(',') #转为列表
        self.SetCmd(u'选择通道','CH2','no')
        time.sleep(2)#等2s再读数据
        ReadStr=self.QueryPM3000Cmd(u'读CH2的数据','','no')
        ReadList2=ReadStr.strip('\n').split(',') #转为列表        
        self.SetCmd(u'选择通道','CH3','no')
        time.sleep(2)#等2s再读数据
        ReadStr=self.QueryPM3000Cmd(u'读CH3的数据','','no')
        ReadList3=ReadStr.strip('\n').split(',') #转为列表
        self.SetCmd(u'选择通道','SUM','no')
        time.sleep(2)#等2s再读数据
        ReadStr=self.QueryPM3000Cmd(u'读SUM的数据','','no')
        ReadList4=ReadStr.strip('\n').split(',') #转为列表
##计算效率：
        if float(ReadList4[0])!=0:
            eff=round(OutPutList[2]/float(ReadList4[0]),4)   #效率
            print u'转换效率为：',eff
        else:
            print u'输入功率为0，请检查是否开机,输入功率为：',float(ReadList4[0])
            return False
##PF判断
        MaxPF=max(round(float(ReadList1[5]),2),round(float(ReadList2[5]),2),round(float(ReadList3[5]),2))
        if MaxPF<float(PF):
            PFResult='FAIL'
        else:
            PFResult='PASS'
##电流THD判断
        MaxTHDA=max(round(float(ReadList1[14]),2),round(float(ReadList2[14]),2),round(float(ReadList3[14]),2))
        if MaxTHDA>float(THDA):
            THDAResult='FAIL'
        else:
            THDAResult='PASS'            
##效率判断
        if eff<float(Efficiency):
            EfficiencyResult='FAIL'
        else:
            EfficiencyResult='PASS' 

##组包，准备打印：
        OutStr=(time.strftime("%H:%M:%S")+','   #时间
                +str(round(float(ReadList1[3]),2))+','               #输入电压
                +str(OutPutList[0])+','         #输出电压
                +str(OutPutList[1])+','         #输出电流
                +str(round(OutPutList[2],2))+','         #输出功率
                +str(round(float(ReadList4[0]),2))+','               #输入功率
                +str(eff)+','                   #效率
                +EfficiencyResult+','           #效率测试结果
                +str(round(float(ReadList1[5]),4))+','               #A相PF
                +str(round(float(ReadList2[5]),4))+','               #B相PF
                +str(round(float(ReadList3[5]),4))+','               #C相PF
                +PFResult+',' 
                +str(round(float(ReadList1[14]),3))+','              #A相电流THD
                +str(round(float(ReadList2[14]),3))+','              #B相电流THD
                +str(round(float(ReadList3[14]),3))+','              #C相电流THD
                +THDAResult+',' 
                )

        print u'性能指标数据：',OutStr

        try:
            savef=open(add+r'.\TestRecord\PerformanceTest.csv','a')
            print>>savef,OutStr.encode('gbk')
            savef.close()
        except:
            print u'打开文件出错！'



##PF测试结果判断=============================================
    def PFTestJudge(self):
        """""""""""""""""""""""""""""""""""""""
        读取性能指标数据文档，根据通过准则判断测试结果
        """""""""""""""""""""""""""""""""""""""
        f = open( add+r'.\TestRecord\PerformanceTest.csv', 'r')
        csvlist = []
        tmplist=[]
        savelist=[]
        csvlist = f.readlines()
        f.close()
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
##            print tmplist
            if tmplist[11]=='FAIL':
                savelist.append(tmplist)

        if savelist!=[]:
            print u'有',len(savelist),u'条测试不通过'
            print savelist
            return False
        else:
            print u'测试通过！'
            return True
##THD测试结果判断=============================================
    def THDTestJudge(self):
        """""""""""""""""""""""""""""""""""""""
        读取性能指标数据文档，根据通过准则判断测试结果
        """""""""""""""""""""""""""""""""""""""
        f = open( add+r'.\TestRecord\PerformanceTest.csv', 'r')
        csvlist = []
        tmplist=[]
        savelist=[]
        csvlist = f.readlines()
        f.close()
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
##            print tmplist[15]
            if tmplist[15].strip('\n')=='FAIL':
                savelist.append(tmplist)

        if savelist!=[]:
            print u'有',len(savelist),u'条测试不通过'
            print savelist
            return False
        else:
            print u'测试通过！'
            return True
##转换效率测试结果判断=============================================
    def EffTestJudge(self):
        """""""""""""""""""""""""""""""""""""""
        读取性能指标数据文档，根据通过准则判断测试结果
        """""""""""""""""""""""""""""""""""""""
        f = open( add+r'.\TestRecord\PerformanceTest.csv', 'r')
        csvlist = []
        tmplist=[]
        savelist=[]
        csvlist = f.readlines()
        f.close()
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
##            print tmplist
            if tmplist[7]=='FAIL':
                savelist.append(tmplist)

        if savelist!=[]:
            print u'有',len(savelist),u'条测试不通过'
            print savelist
            return False
        else:
            print u'测试通过！'
            return True        
##读通道所有数据=============================================
    def ReadCH(self, CH='CH1'):
        """""""""""""""""""""""""""""""""""""""
        CH:选择读取哪个通道数据
        可设置：CH1、CH2、CH3、CHN、SUM
        返回一个列表，内容如下：
        0. Watts 1. VA 2. VAr 3. Vrms 4. Arms 5. PF 6. Vpeak 7. Apeak
        8. Vcf 9.Acf 10.Imp 13.Vthd 14.Athd 15.Frq

        """""""""""""""""""""""""""""""""""""""
        flag=1
        ReadStr=''
        ReadList1=[]
        ReadList=[]
        time.sleep(1)
        self.SetCmd(u'设置接线方式','3P4','no')
        time.sleep(1)
##        print self.inst.write(':WRG:3P4')
        self.SetCmd(u'选择通道',CH,'no')
        time.sleep(2)#等2s再读数据
        while(flag<4): #防止频率读到0值
            ReadStr=self.QueryPM3000Cmd(u'读'+CH+u'的数据','','no')
            ReadList=ReadStr.split(',') #转为列表
            flag=flag+1
            if ReadList[15]!=0:
                flag=0
                break
        for i in ReadList:
##            print str(float(i))
            ReadList1.append(str(float(i)))
##        print 'ReadStr:',ReadList1
##        print 'Watts:',float(ReadList[0])
        return ReadList1


##查询命令=============================================
    def QueryPM3000Cmd(self, sComment=':BSR?',value='',printflag=''):     
        f = open( add+r'.\python\PM3000_GPIB\PM3000_command_list.csv', 'r')
        csvlist = []
        csvlist = f.readlines()
        f.close()
        receiveData=''
        returnStr=''
        tmplist=[]
        flag=1
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
            if tmplist[0] == sComment.encode('gbk'):
                sCmd = tmplist[1].strip('\n')+value   #命令
                if printflag=='':
                    print u'=======',tmplist[0].decode('gbk'),value,'========='
                while(flag!=0 and flag<3):
                    try:
                        returnStr=self.inst.query(sCmd)#查询命令用query
                        flag=0
                    except :
                        flag=flag+1  #出错了重发
                        print u'通讯超时！'
                        time.sleep(1)
##        print returnStr
        if printflag=='':
            print u'收到数据为：',returnStr.strip('\n')
        return returnStr.strip('\n')

##设置命令=============================================
    def SetCmd(self, sComment,value='',printflag=''):     
        f = open( add+r'.\python\PM3000_GPIB\PM3000_command_list.csv', 'r')
        csvlist = []
        csvlist = f.readlines()
        f.close()
        receiveData=''
        returnList=[]
        tmplist=[]
        flag=1
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
            if tmplist[0] == sComment.encode('gbk'):
                sCmd = tmplist[1].strip('\n')+' '+value   #命令
                if printflag=='':
                    print u'=======',tmplist[0].decode('gbk'),value,'========='
                while(flag!=0 and flag<3):
                    try:
                        self.inst.write(sCmd)#设置命令用write
                        flag=0
                    except:
                        flag=flag+1  #出错了重发
                        print u'通讯超时！'




#======================================================================================================    
if __name__ == "__main__":
    myapp = PM3000_GPIB()
    myapp.Open_PM3000_GPIB('2')
    flag=1
##    while(1):
####        while(flag!=0 and flag<3):
##        try:
##            print myapp.inst.write(':SEL:CH1')
##            print myapp.inst.write(':SEL:FRQ')
##            print myapp.inst.write(':BSE 4')
##            print myapp.inst.write('*TRG')
##            
##            print myapp.inst.query(':BRD:CH1?').strip('\n')#查询命令用query
####            print myapp.inst.query('*WAI?').strip('\n')
##            time.sleep(0.5)
##        except :
##            flag=flag+1  #出错了重发
##            print u'通讯超时！'
##            time.sleep(1)
##    while(1):
        
##        myapp.QueryPM3000Cmd(u'读CH1','FRQ?')
##        print myapp.inst.write(':DSE 2')
##        print myapp.inst.query(':DSR?')
##        print myapp.inst.query(':FNC:CH1:FRQ?')
##        time.sleep(1)
    print myapp.ReadCH('CH2')[15]
####        time.sleep(1)
##        myapp.ReadCH('CH3')
####        time.sleep(1)

##    myapp.sendBySerial(':FNC:CH1:VLT?\r')
####    myapp.sendBySerial('\n')
##    myapp.QueryCmd(u'选择CH1')

##    print myapp.inst.write('*RST')
##    for i in range(0,5):
##        myapp.CreatePowerEffectDoc()
##        for i in range(0,3):
##    myapp.PowerEffectTest() 
##    myapp.CreateCurrentPrecisionDoc()
##    for i in range(0,9):
##        time.sleep(0.5)
##        myapp.CurrentPrecisionTest()
##    myapp.PerformanceTest()
##    myapp.THDTestJudge()
##    myapp.CreateTestDoc('test')
