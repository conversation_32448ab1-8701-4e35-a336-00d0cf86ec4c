*** Settings ***
Test Setup        Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接
Default Tags      T1-1
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
uniform_0002_电池浮充√
    [Setup]
    #启动浮充
    Comment    Wait Until Keyword Succeeds    1m    1    统一监控协议_设置数据    直流遥控    命令类型：COMMAND_TYPE    1F 00    ctrl
    Comment    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    浮充
    Comment    @{历史事件内容1}    获取web历史事件内容    ${empty}    ${empty}    所有    1    1
    Comment    should contain    ${历史事件内容1}[-1]    启动浮充
    FOR    ${i}    IN RANGE    0    4
        ${设置结果}    统一监控协议_设置数据    直流遥控    命令类型：COMMAND_TYPE    1F 00    ctrl
        Exit For Loop if    '${设置结果}'=='True'
        sleep    10
    END
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    浮充
    @{历史事件内容1}    获取web历史事件内容    ${empty}    ${empty}    所有    1    1
    should contain    ${历史事件内容1}[-1]    启动浮充
    [Teardown]

uniform_0004_电池均充√
    [Setup]
    #启动浮充
    Comment    Wait Until Keyword Succeeds    1m    1    统一监控协议_设置数据    直流遥控    命令类型：COMMAND_TYPE    10 00    ctrl
    Comment    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    均充
    Comment    @{历史事件内容1}    获取web历史事件内容    ${empty}    ${empty}    所有    1    1
    Comment    should contain    ${历史事件内容1}[-1]    启动均充
    FOR    ${i}    IN RANGE    0    4
        ${设置结果}    统一监控协议_设置数据    直流遥控    命令类型：COMMAND_TYPE    10 00    ctrl
        Exit For Loop if    '${设置结果}'=='True'
        sleep    10
    END
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    均充
    @{历史事件内容1}    获取web历史事件内容    ${empty}    ${empty}    所有    1    1
    should contain    ${历史事件内容1}[-1]    启动均充
    [Teardown]

uniform_0006_电池测试√
    [Setup]
    Comment    电池管理初始化
    #启动浮充
    Comment    Wait Until Keyword Succeeds    1m    1    统一监控协议_设置数据    直流遥控    命令类型：COMMAND_TYPE    11 00    ctrl
    Comment    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    测试
    Comment    @{历史事件内容1}    获取web历史事件内容    ${empty}    ${empty}    所有    1    1
    Comment    should contain    ${历史事件内容1}[-1]    启动测试
    FOR    ${i}    IN RANGE    0    4
        ${设置结果}    统一监控协议_设置数据    直流遥控    命令类型：COMMAND_TYPE    11 00    ctrl
        Exit For Loop if    '${设置结果}'=='True'
        sleep    10
    END
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    测试
    @{历史事件内容1}    获取web历史事件内容    ${empty}    ${empty}    所有    1    1
    should contain    ${历史事件内容1}[-1]    启动测试
    [Teardown]
