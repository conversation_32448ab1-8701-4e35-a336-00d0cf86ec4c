*** Settings ***
Suite Setup
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
1104_0004_V_3.0_1104_直流段_数字量
    [Tags]    1
    ${协议数据}    1104获取数据    ${g_prtcl_1104}    <<获取直流段数字量~CID1=42H>>    <<获取直流段数字量~CID2=43H>>    None    ${g_ver_1104}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${对比结果}    批量对比数据_1104_WEB    ${协议数据}    ${web数据}    1104_直流段_数字量
    should be true    ${对比结果}

1104_0006_V_3.0_1104_直流段_模拟量
    [Tags]    1
    [Setup]    Run keywords    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池组容量_2
    ...    100
    ...    AND    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池组容量_3
    ...    100
    ...    AND    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池组容量_4
    ...    100
    ${协议数据}    1104获取数据    ${g_prtcl_1104}    <<获取直流段模拟量~CID1=42H>>    <<获取直流段模拟量~CID2=42H>>    None    ${g_ver_1104}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${对比结果}    批量对比数据_1104_WEB    ${协议数据}    ${web数据}    1104_直流段_模拟量
    Comment    should be true    ${对比结果}
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池组容量_2
    ...    0
    ...    AND    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池组容量_3
    ...    0
    ...    AND    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池组容量_4
    ...    0

1104_0008_V_3.0_1104_直流段_参数量
    [Tags]    1
    ${协议数据}    1104获取数据    ${g_prtcl_1104}    <<获取直流段参数量~CID1=42H>>    <<获取直流段参数量~CID2=47H>>    None    ${g_ver_1104}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${对比结果}    批量对比数据_1104_WEB    ${协议数据}    ${web数据}    1104_直流段_参数量
    should be true    ${对比结果}

1104_0010_V_3.0_1104_直流段_参数设置
    [Tags]    1
    ${1104数据}    1104批量参数设置测试    ${g_prtcl_1104}    <<交设置交流段参数~CID1=42H>>    <<设置交流段参数~CID2=49H>>    ${g_ver_1104}
    ${对比结果}    批量对比参数设置_1104    ${1104数据}    1104_直流段_参数设置
    should be true    ${对比结果}
    [Teardown]    run keywords    直流自动参数设置后置条件
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    远程一次下电使能
    ...    远程二次下电使能    # run keywords | 直流自动参数设置后置条件 | AND | Run keywords | Wait Until Keyword Succeeds | 10 | 1 | 设置web设备参数量为默认值 | 远程一次下电使能
