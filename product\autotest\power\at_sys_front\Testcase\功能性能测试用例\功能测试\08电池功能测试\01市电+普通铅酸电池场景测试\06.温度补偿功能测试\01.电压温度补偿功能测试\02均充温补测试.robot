*** Settings ***
Force Tags        notest
Resource          ../../../../../../../测试用例关键字.robot

*** Test Cases ***
均充温补正确性测试（默认值）
    
    电池管理初始化
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    交流供电状态    市电1
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    过渡
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动均充
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    均充
    ${电压设置值}    获取web参数量    均充电压
    sleep    5m
    ${电压获取值}    获取web实时数据    直流电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    温度补偿模式    均充
    sleep    5
    设置web设备参数量为默认值    温度补偿电压下限    温度补偿电压上限
    ${参数范围}    获取web参数可设置范围    电池电压温度补偿系数
    设置web参数量    电池电压温度补偿系数    ${参数范围}[1]
    ${电池温度}    获取web实时数据    电池温度-1
    设置web参数量    电池温度补偿基准    ${电池温度}
    sleep    5m
    ${电压获取值new}    获取web实时数据    直流电压
    ${差值}    evaluate    ${电压获取值}-${电压获取值new}
    should be true    -0.2<${差值}<0.2
    [Teardown]    Run keywords    设置web设备参数量为默认值    电池电压温度补偿系数
    ...    AND    设置web参数量    温度补偿模式    禁止

均充温补正确性测试（最小值参数）
    
    仅有市电条件上电
    连接CSU
    ${当前数据值}    Wait Until Keyword Succeeds    5m    2    获取web实时数据    直流电压
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
        设置web参数量    电池组容量_${i}    0
    END
    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    交流供电状态    市电1
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    过渡
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动均充
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    均充
    Wait Until Keyword Succeeds    10    1    设置web参数量    温度补偿模式    均充
    sleep    5
    ${温补下限参数范围}    获取web参数上下限范围    温度补偿电压下限
    设置web参数量    温度补偿电压下限    ${温补下限参数范围}[1]
    ${温补上限参数范围}    获取web参数上下限范围    温度补偿电压上限
    设置web参数量    温度补偿电压上限    ${温补上限参数范围}[2]
    ${参数范围}    获取web参数上下限范围    电池电压温度补偿系数
    设置web参数量    电池电压温度补偿系数    ${参数范围}[0]
    ${温补系数设置值}    获取web参数量    电池电压温度补偿系数
    ${温度基准}    获取web参数上下限范围    电池温度补偿基准
    设置web参数量    电池温度补偿基准    ${温度基准}[0]
    ${电池温度}    获取web实时数据    电池温度-1
    设置web参数量    浮充电压    ${温补下限参数范围}[1]+0.5
    设置web参数量    均充电压    ${温补下限参数范围}[1]+0.5
    ${电压设置值}    获取web参数量    均充电压
    sleep    5m
    ${电压获取值}    获取web实时数据    直流电压
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    0.001    电池_1    电池温度
    sleep    10
    ${电池温度调节值}    获取web实时数据    电池温度-1
    ${电池温度new}    run keyword if    ${电池温度调节值} < 0    evaluate    0
    ...    ELSE    evaluate    ${电池温度调节值}
    ${电池温度差值}    evaluate    ${温度基准}[0]-${电池温度new}
    ${温补电压计算值}    evaluate    ${电池温度差值}*24*${温补系数设置值}*0.001
    sleep    5m
    ${电压获取值new}    获取web实时数据    直流电压
    ${温补电压实际值}    evaluate    ${电压获取值new}-${电压获取值}
    ${差值}    evaluate    ${温补电压实际值}-${温补电压计算值}
    should be true    -0.2<${差值}<0.2
    #改变温补系数为最大值
    设置web参数量    电池电压温度补偿系数    ${参数范围}[2]
    ${温补系数设置值new}    获取web参数量    电池电压温度补偿系数
    ${温补电压计算值new}    evaluate    ${电池温度差值}*24*${温补系数设置值new}*0.001
    sleep    5m
    ${电压获取值new1}    获取web实时数据    直流电压
    ${温补电压实际值new}    evaluate    ${电压获取值new1}-${电压获取值}
    ${差值new}    evaluate    ${温补电压实际值new}-${温补电压计算值new}
    should be true    -0.2<${差值new}<0.2
    [Teardown]    Run keywords    设置web设备参数量为默认值    电池电压温度补偿系数    温度补偿电压上限    温度补偿电压下限    均充电压    浮充电压
    ...    AND    设置web参数量    温度补偿模式    禁止
    ...    AND    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度

均充温补正确性测试（最大值参数）
    
    仅有市电条件上电
    连接CSU
    ${当前数据值}    Wait Until Keyword Succeeds    5m    2    获取web实时数据    直流电压
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
        设置web参数量    电池组容量_${i}    0
    END
    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    交流供电状态    市电1
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    过渡
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动均充
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    均充
    设置web参数量    浮充电压    53.5
    设置web参数量    均充电压    54.5
    ${电压设置值}    获取web参数量    均充电压
    sleep    3m
    ${电压获取值}    获取web实时数据    直流电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    温度补偿模式    均充
    sleep    5
    ${温补下限参数范围}    获取web参数上下限范围    温度补偿电压下限
    设置web参数量    温度补偿电压下限    ${温补下限参数范围}[1]
    ${温补上限参数范围}    获取web参数上下限范围    温度补偿电压上限
    设置web参数量    温度补偿电压上限    ${温补上限参数范围}[2]
    ${参数范围}    获取web参数上下限范围    电池电压温度补偿系数
    设置web参数量    电池电压温度补偿系数    ${参数范围}[0]
    ${温补系数设置值}    获取web参数量    电池电压温度补偿系数
    ${温度基准}    获取web参数上下限范围    电池温度补偿基准
    设置web参数量    电池温度补偿基准    ${温度基准}[0]
    ${温度高阈值}    获取web参数上下限范围    电池温度高阈值
    设置web参数量    电池温度高阈值    ${温度高阈值}[2]
    ${电池温度}    获取web实时数据    电池温度-1
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    5    1.6    电池_1    电池温度
    sleep    10
    ${电池温度调节值}    获取web实时数据    电池温度-1
    ${电池温度new}    run keyword if    ${电池温度调节值} > 45    evaluate    45
    ...    ELSE    evaluate    ${电池温度调节值}
    ${电池温度差值}    evaluate    ${温度基准}[0]-${电池温度new}
    ${温补电压计算值}    evaluate    ${电池温度差值}*24*${温补系数设置值}*0.001
    sleep    3m
    ${电压获取值new}    获取web实时数据    直流电压
    ${温补电压实际值}    evaluate    ${电压获取值new}-${电压获取值}
    ${差值}    evaluate    ${温补电压实际值}-${温补电压计算值}
    should be true    -0.2<${差值}<0.2
    #改变温补系数为最大值
    设置web参数量    电池电压温度补偿系数    ${参数范围}[2]
    ${温补系数设置值new}    获取web参数量    电池电压温度补偿系数
    ${温补电压计算值new}    evaluate    ${电池温度差值}*24*${温补系数设置值new}*0.001
    sleep    3m
    ${电压获取值new1}    获取web实时数据    直流电压
    ${温补电压实际值new}    evaluate    ${电压获取值new1}-${电压获取值}
    ${差值new}    evaluate    ${温补电压实际值new}-${温补电压计算值new}
    should be true    -0.2<${差值new}<0.2
    [Teardown]    Run keywords    设置web设备参数量为默认值    电池电压温度补偿系数    温度补偿电压上限    温度补偿电压下限    均充电压    浮充电压    电池温度高阈值
    ...    AND    设置web参数量    温度补偿模式    禁止
    ...    AND    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度

均充温补正确性测试（温补电压范围外）
    
    仅有市电条件上电
    连接CSU
    ${当前数据值}    Wait Until Keyword Succeeds    10    2    获取web实时数据    直流电压
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
        设置web参数量    电池组容量_${i}    0
    END
    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    交流供电状态    市电1
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    过渡
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动均充
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    均充    #浮充
    Wait Until Keyword Succeeds    10    1    设置web参数量    温度补偿模式    均充
    sleep    5
    ${温补下限参数范围}    获取web参数上下限范围    温度补偿电压下限
    设置web参数量    温度补偿电压下限    ${温补下限参数范围}[0]
    ${温补上限参数范围}    获取web参数上下限范围    温度补偿电压上限
    设置web参数量    温度补偿电压上限    ${温补上限参数范围}[0]
    ${参数范围}    获取web参数上下限范围    电池电压温度补偿系数
    设置web参数量    电池电压温度补偿系数    ${参数范围}[0]
    ${温补系数设置值}    获取web参数量    电池电压温度补偿系数
    ${温度基准}    获取web参数上下限范围    电池温度补偿基准
    设置web参数量    电池温度补偿基准    ${温度基准}[0]
    ${输出高停机设置值}    获取web参数上下限范围    整流器输出高停机电压
    设置web参数量    整流器输出高停机电压    ${输出高停机设置值}[2]
    #设置浮充电压为温补电压下限以下
    ${电压设置值}    获取web参数量    温度补偿电压下限
    ${浮充设置值}    evaluate    ${电压设置值}-1
    设置web参数量    浮充电压    ${浮充设置值}
    设置web参数量    均充电压    ${浮充设置值}
    ${电池温度}    获取web实时数据    电池温度-1
    sleep    3m
    ${电压获取值}    获取web实时数据    直流电压
    ${调节误差}    evaluate    ${电压获取值}-${浮充设置值}
    should be true    -0.2<${调节误差}<0.2
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    10    1.0    电池_1    电池温度
    sleep    3m
    ${电压获取值new}    获取web实时数据    直流电压
    ${差值}    evaluate    ${电压获取值new}-${电压获取值}
    should be true    -0.2<${差值}<0.2
    #设置浮充电压为温补电压上限以上
    ${电压设置值}    获取web参数量    温度补偿电压上限
    设置web参数量    均充电压    58
    ${电池温度}    获取web实时数据    电池温度-1
    sleep    3m
    ${电压获取值new1}    获取web实时数据    直流电压
    ${调节误差1}    evaluate    ${电压获取值new1}-58
    should be true    -0.2<${调节误差1}<0.2
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    0.001    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    sleep    3m
    ${电压获取值new2}    获取web实时数据    直流电压
    ${差值new}    evaluate    ${电压获取值new2}-${电压获取值new1}
    should be true    -0.2<${差值new}<0.2
    [Teardown]    Run keywords    设置web设备参数量为默认值    浮充电压    均充电压    整流器输出高停机电压
    ...    AND    设置web参数量    温度补偿模式    禁止
    ...    AND    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度

均充温补正确性测试（温补温度范围外）
    
    仅有市电条件上电
    连接CSU
    ${当前数据值}    Wait Until Keyword Succeeds    5m    2    获取web实时数据    直流电压
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
        设置web参数量    电池组容量_${i}    0
    END
    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    交流供电状态    市电1
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    过渡
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动均充
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    均充
    设置web参数量    浮充电压    53.5
    设置web参数量    均充电压    54.5
    ${电压设置值}    获取web参数量    均充电压
    sleep    3m
    ${电压获取值}    获取web实时数据    直流电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    温度补偿模式    均充
    sleep    5
    ${温补下限参数范围}    获取web参数上下限范围    温度补偿电压下限
    设置web参数量    温度补偿电压下限    ${温补下限参数范围}[1]
    ${温补上限参数范围}    获取web参数上下限范围    温度补偿电压上限
    设置web参数量    温度补偿电压上限    ${温补上限参数范围}[2]
    ${参数范围}    获取web参数上下限范围    电池电压温度补偿系数
    设置web参数量    电池电压温度补偿系数    ${参数范围}[0]
    ${温补系数设置值}    获取web参数量    电池电压温度补偿系数
    ${温度基准}    获取web参数上下限范围    电池温度补偿基准
    设置web参数量    电池温度补偿基准    ${温度基准}[0]
    ${温度高阈值}    获取web参数上下限范围    电池温度高阈值
    设置web参数量    电池温度高阈值    ${温度高阈值}[2]
    ${电池温度}    获取web实时数据    电池温度-1
    #设置电池温度40度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1.6    电池_1    电池温度
    sleep    10
    ${电池温度new}    获取web实时数据    电池温度-1
    ${电池温度差值}    evaluate    ${温度基准}[0]-${电池温度new}
    ${温补电压计算值}    evaluate    ${电池温度差值}*24*${温补系数设置值}*0.001
    sleep    3m
    ${电压获取值new}    获取web实时数据    直流电压
    ${温补电压实际值}    evaluate    ${电压获取值new}-${电压获取值}
    ${差值}    evaluate    ${温补电压实际值}-${温补电压计算值}
    should be true    -0.2<${差值}<0.2
    #设置电池温度大于45度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    10    1.6    电池_1    电池温度
    sleep    10
    ${电池温度调节值}    获取web实时数据    电池温度-1
    ${电池温度new1}    run keyword if    ${电池温度调节值} > 45    evaluate    45
    ...    ELSE    evaluate    ${电池温度调节值}
    ${电池温度差值1}    evaluate    ${温度基准}[0]-${电池温度new1}
    ${温补电压计算值new}    evaluate    ${电池温度差值1}*24*${温补系数设置值}*0.001
    sleep    3m
    ${电压获取值new1}    获取web实时数据    直流电压
    ${温补电压实际值new}    evaluate    ${电压获取值new1}-${电压获取值}
    ${差值new}    evaluate    ${温补电压实际值new}-${温补电压计算值new}
    should be true    -0.2<${差值new}<0.2
    #恢复温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    ${电池温度恢复}    获取web实时数据    电池温度-1
    #设置温度为0
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    0.001    电池_1    电池温度
    sleep    10
    ${电池温度new2}    获取web实时数据    电池温度-1
    ${电池温度差值2}    evaluate    ${温度基准}[0]-${电池温度new2}
    ${温补电压计算值2}    evaluate    ${电池温度差值2}*24*${温补系数设置值}*0.001
    sleep    3m
    ${电压获取值new2}    获取web实时数据    直流电压
    ${温补电压实际值2}    evaluate    ${电压获取值new2}-${电压获取值}
    ${差值2}    evaluate    ${温补电压实际值2}-${温补电压计算值2}
    should be true    -0.2<${差值2}<0.2
    #设置温度为-10
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    -10    0.001    电池_1    电池温度
    sleep    10
    ${电池温度调节值3}    获取web实时数据    电池温度-1
    ${电池温度new3}    run keyword if    ${电池温度调节值3} < 0    evaluate    0
    ...    ELSE    evaluate    ${电池温度调节值3}
    ${电池温度差值3}    evaluate    ${温度基准}[0]-${电池温度new3}
    ${温补电压计算值3}    evaluate    ${电池温度差值3}*24*${温补系数设置值}*0.001
    sleep    3m
    ${电压获取值new3}    获取web实时数据    直流电压
    ${温补电压实际值3}    evaluate    ${电压获取值new3}-${电压获取值}
    ${差值3}    evaluate    ${温补电压实际值3}-${温补电压计算值3}
    should be true    -0.2<${差值3}<0.2
    [Teardown]    Run keywords    设置web设备参数量为默认值    电池电压温度补偿系数    温度补偿电压上限    温度补偿电压下限    均充电压    浮充电压    电池温度高阈值
    ...    AND    设置web参数量    温度补偿模式    禁止
    ...    AND    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度

电池温度无效时不进行均充温补
    
    仅有市电条件上电
    连接CSU
    ${当前数据值}    Wait Until Keyword Succeeds    10    2    获取web实时数据    直流电压
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
        设置web参数量    电池组容量_${i}    0
    END
    设置web参数量    电池组容量_2    100
    设置web参数量    电池组容量_1    0
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    交流供电状态    市电1
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    过渡
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动均充
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    均充
    Wait Until Keyword Succeeds    10    1    设置web参数量    温度补偿模式    均充
    sleep    5
    ${温补下限参数范围}    获取web参数上下限范围    温度补偿电压下限
    设置web参数量    温度补偿电压下限    ${温补下限参数范围}[0]
    ${温补上限参数范围}    获取web参数上下限范围    温度补偿电压上限
    设置web参数量    温度补偿电压上限    ${温补上限参数范围}[0]
    ${参数范围}    获取web参数上下限范围    电池电压温度补偿系数
    设置web参数量    电池电压温度补偿系数    ${参数范围}[0]
    ${温补系数设置值}    获取web参数量    电池电压温度补偿系数
    ${温度基准}    获取web参数上下限范围    电池温度补偿基准
    设置web参数量    电池温度补偿基准    ${温度基准}[0]
    ${温度高阈值}    获取web参数上下限范围    电池温度高阈值
    设置web参数量    电池温度高阈值    ${温度高阈值}[2]
    设置web参数量    浮充电压    53.5
    设置web参数量    均充电压    54.5
    设置web参数量    电池温度无效-2    主要
    ${电压设置值}    获取web参数量    均充电压
    sleep    2m
    ${电压获取值}    获取web实时数据    直流电压
    wait until keyword succeeds    2m    1    判断告警存在    电池温度无效-2
    #设置电池温度为45
    Wait Until Keyword Succeeds    30    1    设置通道配置    UIB_X5_T2    5    1.6    电池_2    电池温度
    sleep    3m
    ${电压获取值new}    获取web实时数据    直流电压
    ${差值}    evaluate    ${电压获取值new}-${电压获取值}
    should be true    -0.2<${差值}<0.2
    #设置电池温度为0
    Wait Until Keyword Succeeds    30    1    设置通道配置    UIB_X5_T2    0    0.001    电池_2    电池温度
    sleep    3m
    ${电压获取值new1}    获取web实时数据    直流电压
    ${差值1}    evaluate    ${电压获取值new1}-${电压获取值}
    should be true    -0.2<${差值1}<0.2
    [Teardown]    Run keywords    设置web参数量    电池组容量_1    100
    ...    AND    设置web参数量    电池组容量_2    0
    ...    AND    设置web设备参数量为默认值    均充电压    电池温度高阈值
    ...    AND    设置web参数量    温度补偿模式    禁止
    ...    AND    Wait Until Keyword Succeeds    30    1    设置通道配置    UIB_X5_T2    0    1    电池_2    电池温度

电池组无效时不进行均充温补
    
    仅有市电条件上电
    连接CSU
    ${当前数据值}    Wait Until Keyword Succeeds    10    2    获取web实时数据    直流电压
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
        设置web参数量    电池组容量_${i}    0
    END
    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    交流供电状态    市电1
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    过渡
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动均充
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    均充
    Wait Until Keyword Succeeds    10    1    设置web参数量    温度补偿模式    均充
    sleep    5
    ${温补下限参数范围}    获取web参数上下限范围    温度补偿电压下限
    设置web参数量    温度补偿电压下限    ${温补下限参数范围}[0]
    ${温补上限参数范围}    获取web参数上下限范围    温度补偿电压上限
    设置web参数量    温度补偿电压上限    ${温补上限参数范围}[0]
    ${参数范围}    获取web参数上下限范围    电池电压温度补偿系数
    设置web参数量    电池电压温度补偿系数    ${参数范围}[0]
    ${温补系数设置值}    获取web参数量    电池电压温度补偿系数
    ${温度基准}    获取web参数上下限范围    电池温度补偿基准
    设置web参数量    电池温度补偿基准    ${温度基准}[0]
    设置web参数量    浮充电压    53.5
    设置web参数量    均充电压    54.5
    ${电压设置值}    获取web参数量    浮充电压
    设置web参数量    电池组容量_1    0
    sleep    2m
    ${电压获取值}    获取web实时数据    直流电压
    #设置电池温度为45
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    5    1.6    电池_1    电池温度
    sleep    3m
    ${电压获取值new}    获取web实时数据    直流电压
    ${差值}    evaluate    ${电压获取值new}-${电压获取值}
    should be true    -0.2<${差值}<0.2
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    浮充    #浮充
    #设置电池温度为0
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    0.001    电池_1    电池温度
    sleep    3m
    ${电压获取值new1}    获取web实时数据    直流电压
    ${差值1}    evaluate    ${电压获取值new1}-${电压获取值}
    should be true    -0.2<${差值1}<0.2
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    浮充    #浮充
    [Teardown]    Run keywords    设置web参数量    电池组容量_1    100
    ...    AND    设置web设备参数量为默认值    均充电压
    ...    AND    设置web参数量    温度补偿模式    禁止
    ...    AND    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度

均充充温补后电压范围测试
    
    仅有市电条件上电
    连接CSU
    ${当前数据值}    Wait Until Keyword Succeeds    10    2    获取web实时数据    直流电压
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
        设置web参数量    电池组容量_${i}    0
    END
    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    交流供电状态    市电1
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    过渡
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动均充
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    均充
    Wait Until Keyword Succeeds    10    1    设置web参数量    温度补偿模式    均充
    sleep    5
    ${温补下限参数范围}    获取web参数上下限范围    温度补偿电压下限
    设置web参数量    温度补偿电压下限    ${温补下限参数范围}[0]
    ${温补上限参数范围}    获取web参数上下限范围    温度补偿电压上限
    设置web参数量    温度补偿电压上限    ${温补上限参数范围}[0]
    ${参数范围}    获取web参数上下限范围    电池电压温度补偿系数
    设置web参数量    电池电压温度补偿系数    ${参数范围}[2]
    ${温补系数设置值}    获取web参数量    电池电压温度补偿系数
    ${温度基准}    获取web参数上下限范围    电池温度补偿基准
    设置web参数量    电池温度补偿基准    ${温度基准}[0]
    ${温度高阈值}    获取web参数上下限范围    电池温度高阈值
    设置web参数量    电池温度高阈值    ${温度高阈值}[2]
    ${温补下限}    获取web参数量    温度补偿电压下限
    ${温补上限}    获取web参数量    温度补偿电压上限
    #设置浮充电压为温补电压下限以下
    设置web参数量    浮充电压    53.5
    设置web参数量    均充电压    53.5
    ${电池温度}    获取web实时数据    电池温度-1
    sleep    3m
    ${电压获取值}    获取web实时数据    直流电压
    #设置电池温度为45，使得输出电压小于温补下限
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    5    1.6    电池_1    电池温度
    sleep    10
    ${电池温度调节值}    获取web实时数据    电池温度-1
    ${电池温度new}    run keyword if    ${电池温度调节值} > 45    evaluate    45
    ...    ELSE    evaluate    ${电池温度调节值}
    ${电池温度差值}    evaluate    ${温度基准}[0]-${电池温度new}
    ${温补电压计算值}    evaluate    ${电池温度差值}*24*${温补系数设置值}*0.001
    ${调节后电压}    evaluate    ${电压获取值}+${温补电压计算值}
    ${实际输出电压}    run keyword if    ${调节后电压} < ${温补下限}    evaluate    ${温补下限}
    ...    ELSE    evaluate    fail
    sleep    3m
    ${电压获取值new}    获取web实时数据    直流电压
    ${差值}    evaluate    ${电压获取值new}-${实际输出电压}
    should be true    -0.2<${差值}<0.2
    #恢复温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    sleep    10
    ${电池温度恢复}    获取web实时数据    电池温度-1
    Comment    设置web参数量    浮充电压    53.5
    Comment    设置web参数量    均充电压    58
    sleep    3m
    ${输出电压获取值}    获取web实时数据    直流电压
    #设置电池温度为0，使得输出电压大于温补上限
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    0.001    电池_1    电池温度
    sleep    10
    ${电池温度调节值1}    获取web实时数据    电池温度-1
    ${电池温度new1}    run keyword if    ${电池温度调节值1} < 0    evaluate    0
    ...    ELSE    evaluate    ${电池温度调节值1}
    ${电池温度差值1}    evaluate    ${温度基准}[0]-${电池温度new1}
    ${温补电压计算值1}    evaluate    ${电池温度差值1}*24*${温补系数设置值}*0.001
    ${调节后电压1}    evaluate    ${输出电压获取值}+${温补电压计算值1}
    ${实际输出电压1}    run keyword if    ${调节后电压1} > ${温补上限}    evaluate    ${温补上限}
    ...    ELSE    evaluate    fail
    sleep    3m
    ${电压获取值new1}    获取web实时数据    直流电压
    ${差值}    evaluate    ${电压获取值new1}-${实际输出电压1}
    should be true    -0.2<${差值}<0.2
    [Teardown]    Run keywords    设置web设备参数量为默认值    浮充电压    均充电压    电池温度高阈值
    ...    AND    设置web参数量    温度补偿模式    禁止
    ...    AND    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度

浮充模式下禁止均充温补
    
    仅有市电条件上电
    连接CSU
    ${当前数据值}    Wait Until Keyword Succeeds    10    2    获取web实时数据    直流电压
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
        设置web参数量    电池组容量_${i}    0
    END
    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    交流供电状态    市电1
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    过渡
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    浮充
    Wait Until Keyword Succeeds    10    1    设置web参数量    温度补偿模式    均充
    sleep    5
    ${温补下限参数范围}    获取web参数上下限范围    温度补偿电压下限
    设置web参数量    温度补偿电压下限    ${温补下限参数范围}[1]
    ${温补上限参数范围}    获取web参数上下限范围    温度补偿电压上限
    设置web参数量    温度补偿电压上限    ${温补上限参数范围}[2]
    ${参数范围}    获取web参数上下限范围    电池电压温度补偿系数
    设置web参数量    电池电压温度补偿系数    ${参数范围}[0]
    ${温补系数设置值}    获取web参数量    电池电压温度补偿系数
    ${温度基准}    获取web参数上下限范围    电池温度补偿基准
    设置web参数量    电池温度补偿基准    ${温度基准}[0]
    ${温度高阈值}    获取web参数上下限范围    电池温度高阈值
    设置web参数量    电池温度高阈值    ${温度高阈值}[2]
    ${电池温度}    获取web实时数据    电池温度-1
    设置web参数量    均充电压    56.4
    设置web参数量    浮充电压    53.5
    sleep    3m
    ${电压获取值}    获取web实时数据    直流电压
    #设置电池温度为0
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    0.001    电池_1    电池温度
    sleep    10
    ${电池温度调节值}    获取web实时数据    电池温度-1
    sleep    3m
    ${电压获取值new}    获取web实时数据    直流电压
    ${差值}    evaluate    ${电压获取值new}-${电压获取值}
    should be true    -0.2<${差值}<0.2
    #设置电池温度为45
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    5    1.6    电池_1    电池温度
    sleep    10
    ${电池温度调节值}    获取web实时数据    电池温度-1
    sleep    3m
    ${电压获取值new1}    获取web实时数据    直流电压
    ${差值1}    evaluate    ${电压获取值new1}-${电压获取值}
    should be true    -0.2<${差值1}<0.2
    [Teardown]    Run keywords    设置web设备参数量为默认值    电池电压温度补偿系数    温度补偿电压上限    温度补偿电压下限    电池温度高阈值
    ...    AND    设置web参数量    温度补偿模式    禁止
    ...    AND    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
