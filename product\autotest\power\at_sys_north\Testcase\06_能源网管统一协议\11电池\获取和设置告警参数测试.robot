*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_获取电池所有告警级别
    [Tags]    4
    ${能源网管数据}    能源网管协议_获取数据    获取电池告警级别    ${SSH}
    ${web数据}    能源网管协议_获取web对应告警级别    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取电池告警级别
    Should Be True    ${数据对比结果}

power_sm_0002_设置电池告警级别（单个）
    ${sheet_name}    Set Variable    设置电池告警级别
    ${能源网管数据}    能源网管协议_设置单个告警    ${sheet_name}    ${BOARD_IP}    ${SSH}
    ${对比结果}    能源网管协议_批量对比参数设置    ${能源网管数据}    设置电池告警级别
    Should Be True    ${对比结果}

power_sm_0004_获取电池所有告警输出干接点
    [Tags]    4
    ${能源网管数据}    能源网管协议_获取数据    获取电池告警输出干接点    ${SSH}
    ${web数据}    能源网管协议_获取web对应告警输出干接点    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取电池告警输出干接点
    Should Be True    ${数据对比结果}

power_sm_0003_设置电池告警输出干接点（单个）
    ${sheet_name}    Set Variable    设置电池告警输出干接点
    ${能源网管数据}    能源网管协议_设置单个告警输出干接点    ${sheet_name}    ${BOARD_IP}    ${SSH}
    ${对比结果}    能源网管协议_批量对比参数设置    ${能源网管数据}    设置电池告警输出干接点
    Should Be True    ${对比结果}
