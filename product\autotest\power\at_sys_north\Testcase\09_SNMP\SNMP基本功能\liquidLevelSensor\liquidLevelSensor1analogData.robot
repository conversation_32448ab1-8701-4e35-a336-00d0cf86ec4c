*** Settings ***
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp_test关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/Web关键字_V30.robot
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
snmp_0002_液位传感器高度√
    [Documentation]    0、0、100000
    连接CSU
    ###最大值
    连接CSU
    设置子工具值    llSensor    all    只读    液位    131065
    sleep    30
    ${获取值1}    获取web实时数据    液位传感器高度-1
    Comment    ${获取值2}    获取web实时数据    液位传感器高度-2
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${snmp获取值1}    获取SNMP数据_单个    lLSSensorHeight1value
    Comment    ${snmp获取值2}    获取SNMP数据_单个    lLSSensorHeight2value
    should be true    ${获取值1}==2
    Comment    should be true    ${获取值2}==2
    should be true    ${snmp获取值1}==2
    Comment    should be true    ${snmp获取值2}==2
    ###超范围设置
    设置子工具值    llSensor    all    只读    液位    6553595
    sleep    30
    ${获取值1}    获取web实时数据    液位传感器高度-1
    Comment    ${获取值2}    获取web实时数据    液位传感器高度-2
    ${snmp获取值1}    获取SNMP数据_单个    lLSSensorHeight1value
    Comment    ${snmp获取值2}    获取SNMP数据_单个    lLSSensorHeight2value
    should be true    ${获取值1}==100
    Comment    should be true    ${获取值2}==100
    should be true    ${snmp获取值1}==100
    Comment    should be true    ${snmp获取值2}==100
    ###最小值、默认值
    设置子工具值    llSensor    all    只读    液位    655294459
    sleep    30
    ${获取值1}    获取web实时数据    液位传感器高度-1
    Comment    ${获取值2}    获取web实时数据    液位传感器高度-2
    ${snmp获取值1}    获取SNMP数据_单个    lLSSensorHeight1value
    Comment    ${snmp获取值2}    获取SNMP数据_单个    lLSSensorHeight2value
    should be true    ${获取值1}==9999
    Comment    should be true    ${获取值2}==9999
    should be true    ${snmp获取值1}==9999
    Comment    should be true    ${snmp获取值2}==9999
    设置子工具值    llSensor    all    只读    液位    65531
    sleep    30
    ${获取值1}    获取web实时数据    液位传感器高度-1
    Comment    ${获取值2}    获取web实时数据    液位传感器高度-2
    ${snmp获取值1}    获取SNMP数据_单个    lLSSensorHeight1value
    Comment    ${snmp获取值2}    获取SNMP数据_单个    lLSSensorHeight2value
    should be true    ${获取值1}==1
    Comment    should be true    ${获取值2}==1
    should be true    ${snmp获取值1}==1
    Comment    should be true    ${snmp获取值2}==1
    断开连接SNMP
