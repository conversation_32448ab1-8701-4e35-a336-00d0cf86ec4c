*** Settings ***
Resource          ../../../../../../ztepwrlibrary/Resource/协议平台关键字.robot
Resource          ../../A接口关键字_V30.robot

*** Test Cases ***
参数有效，获取交流配电参数量
    ${返回值}    ${A接口获取数据}    协议平台获取北向数据    获取交流段参数量_40H_46H
    should be true    ${返回值}
    log    ${A接口获取数据}
    ${web获取数据}    A接口数据获取web数据    ${A接口获取数据}
    ${对比结果}    批量对比数据_A接口_WEB    ${A接口获取数据}    ${web获取数据}    A_交流段_参数量1    0.2
    should be true    ${对比结果}
    Comment    ${功率因数}    获取web参数量    三相油机功率因数
    Comment    ${因数}    Convert to boolean    ${功率因数}
    Comment    ${油机功率因数}=    set variable if    ${因数}    ${功率因数}    0
    Comment    ${发电机额定功率}    获取web参数量    柴油发电机额定功率_1
    Comment    ${功率}    Convert to boolean    ${发电机额定功率}
    Comment    ${发电机额定功率}=    set variable if    ${功率}    ${发电机额定功率}    0
    Comment    ${油机额定功率}    evaluate    ${油机功率因数}*${发电机额定功率}
    Comment    ${协议平台获取北向数据并判断}    协议平台获取北向数据并判断    获取交流段参数量_40H_46H    油机额定功率==${油机额定功率}
    Comment    should be true    ${协议平台获取北向数据并判断}

参数无效，获取交流配电参数量
    ${返回结果}    A接口生成自定义数据帧    40    46    FF
    ${返回值}    Get From Dictionary    ${返回结果}    result
    should be true    ${返回值}
    ${返回结果}    A接口生成自定义数据帧    40    46    00
    ${返回值}    Get From Dictionary    ${返回结果}    result
    should be true    ${返回值}
    ${返回结果}    A接口生成自定义数据帧    40    46    02
    ${返回值}    Get From Dictionary    ${返回结果}    result
    should be true    ${返回值}
