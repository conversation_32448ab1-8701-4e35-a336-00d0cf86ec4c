#-*-coding:utf-8 -*-
#####################################################################################
# File Name:    Aglient34970a_Gpib.py
# Discription:  实现与Aglient34970a的连接和数据读写
# Author:   刘万仓
# DevEnv:   win7x86  python2.7
# Notes:    使用GPIB与aglient34970a连接，读取和控制aglient34970a
# Log:      20180303
#说明：定义了一个Aglient34970A类，这个类里面包含了两个函数，Connect是连接GPIB的，QueryTemp是查询温度数据的
#参数要求：QueryTemp函数接受1-60或者101-120，201-220,301-320的通道列表
#返回值：以"通道号:温度测量值"的方式返回查询到的温度数据，如果没有查询到数据，则返回999
#备注：列表中的第一个通道号必须是有物理连接的，不然返回的会全部无效
################################################################################

class Aglient34970A():
    import pyvisa
    import time
    mg=pyvisa.ResourceManager()
    connect_flag=True #连接成功标志
    def Connect(self,Address=9):#default address is 9
        self.ag=self.mg.open_resource("GPIB0::"+str(Address)+"::INSTR")     #ag=aglient34970A
        try:
            instr=self.ag.query("*IDN?")
            if "34970A" in instr:
                print ("连接成功")
            else:
                print("仪表型号读取失败！返回的仪表型号信息为： ",idnstr)
        except self.pyvisa.errors.VisaIOError:
            self.connect_flag=False
            print("连接错误或者GPIB地址错误，请检查GPIB线缆和仪表GPIB地址设定！")
        return self.connect_flag
    def QueryTemp(self, ChanList=list(range(1, 21))):#ChanList为1-60或者101-120，201-220,301-320的通道列表
        chanlist=[]#用于存放转化后的通道列表
        chanlist_all=list(range(101,121))+list(range(201,221))+list(range(301,321))#可用通道列表集合
        tempdict={}#返回的测试数据字典
        for i in ChanList:
            if i<21:
                chanlist.append(i + 100)   #还原为101-120
            elif i<41:
                chanlist.append(i - 20 + 200) #还原为201-220
            elif i<61:
                chanlist.append(i - 40 + 300) #还原为301-320
            else:
                chanlist.append(i)
        ChanList=list(set(chanlist).intersection(set(chanlist_all)))#取chanlist与chanlist_all的交集作为待查询数据
        ChanList.sort()#排序
        addstr=str(ChanList).replace(" ", "").replace("[", "").replace("]", "")#addlist为一个形如"101,102,106..."的字符串
        #if __name__ == '__main__': print(addstr,"addstr")
        self.ag.write("*RST")
        self.ag.write("CONF:TEMP TC,K,(@"+addstr+")")#设定热电偶为K型
        self.ag.write("FORM:READ:CHAN ON")#返回的数据包含通道编号
        self.ag.write("ROUTE:SCAN (@"+addstr+")")
        self.ag.write("INIT")
        self.time.sleep(len(ChanList) * 0.2)
        try:
            x=self.ag.query("FETCH?")
            #if __name__ == '__main__': print(x)
            templist=x.split(',')
            #if __name__ == '__main__': print(templist)
            for i in range(len(templist)):
                templist[i]=float(templist[i])
                if i%2!=0:
                    templist[i]=int(templist[i])
            tempdict=dict(zip(templist[1::2],templist[0::2]))
        except self.pyvisa.VisaIOError:
            tempdict=dict(zip(chanlist,[999]*len(chanlist)))
        return tempdict
if __name__ == '__main__':
    AG=Aglient34970A()
    AG.Connect()
    x=AG.QueryTemp(ChanList=list(range(21)))
    print(x)
    # x=AG.QueryTemp(ChanList=[201,202,203])
    # print(x)
    # x=AG.QueryTemp(ChanList=[101,102,103])
    # print(x)
    # x=AG.QueryTemp(ChanList=[301,302,303])
    # print(x)
    x=AG.QueryTemp(ChanList=list(range(301,321)))
    print(x)
