*** Settings ***
Suite Setup
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
1363_0004_V_3.0_1363_锂电段_参数量
    Comment    ${取值约定}    获取web参数的取值约定    电池配置
    Comment    ${val}    Get From Dictionary    ${取值约定}    2
    Comment    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    ${val}
    Comment    run keyword and ignore error    Wait Until Keyword Succeeds    10    1    设置web参数量    智能锂电类型    FB100B3电池
    ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取锂电池段参数量~CID1=4AH>>    <<获取锂电池段参数量~CID2=47H>>    01    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${对比结果}    批量对比数据_1104_WEB    ${协议数据}    ${web数据}    1363_锂电段_参数量
    should be true    ${对比结果}

1363_0006_V_3.0_1363_锂电段_参数设置
    [Documentation]    ${协议数据} | ${表单名称} | ${命令名称} | ${g_ver} | ${屏号}=None
    ...        单板过温告警阈值在数据字典里的attr.show$data值为2
    [Setup]    #run keywords    Wait Until Keyword Succeeds    30    1    设置web参数量    电池配置    #run keywords | Wait Until Keyword Succeeds | 30 | 1 | 设置web参数量 | 电池配置 | # 纯锂电
    Comment    Wait Until Keyword Succeeds    30    1    设置web参数量    电池配置    纯锂电
    ${1363数据}    1104批量参数设置测试    ${g_prtcl_1363}    <<设置锂电池段参数量~CID1=4AH>>    <<设置锂电池段参数量~CID2=49H>>    ${g_ver_1363}    1
    ${对比结果}    批量对比参数设置_1104    ${1363数据}    1363_锂电段_参数设置
    should be true    ${对比结果}
    Wait Until Keyword Succeeds    30    1    设置web参数量    电池配置    纯铅酸
    [Teardown]    #run keywords    Wait Until Keyword Succeeds    30    1    设置web参数量    电池配置    # 纯铅酸
