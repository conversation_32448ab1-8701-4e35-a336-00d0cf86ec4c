*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
批量获取交流电表设备信息测试
    写入CSV文档    交流电表设备信息获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string32    INDEX_DEVICE_CH=交流电表
    ${排除列表}    create list
    @{信号名称列表1}   create_list
	${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${电表排除设备名称信息}    ${排除列表}
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     name     ${信号名称}
		Append To List       ${信号名称列表1}    ${dict}
    END
	Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    ACmeter    ${信号名称列表1}    只读    ZXDU48-FB100B3
        ...    交流电表设备信息获取测试
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    ACmeter    ${信号名称列表1}    只读    ZTE-smartli
        ...    交流电表设备信息获取测试
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    ACmeter    ${信号名称列表1}    只读    ZXDU48 FB100C2
        ...    交流电表设备信息获取测试


    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=float    INDEX_DEVICE_CH=交流电表
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}
    ${缺省值列表}   获取缺省值列表  ${列表1}    1
	Run Keyword And Continue On Failure    南向子设备数值型厂家信息列表封装判断结果    ACmeter    ${缺省值列表}    只读
        ...    交流电表设备信息获取测试
    ${缺省值列表}   获取缺省值列表  ${列表1}    2
    Run Keyword And Continue On Failure    南向子设备数值型厂家信息列表封装判断结果    ACmeter    ${缺省值列表}    只读
        ...    交流电表设备信息获取测试
    ${缺省值列表}   获取缺省值列表  ${列表1}    0
    Run Keyword And Continue On Failure    南向子设备数值型厂家信息列表封装判断结果    ACmeter    ${缺省值列表}    只读
        ...    交流电表设备信息获取测试


    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=int    INDEX_DEVICE_CH=交流电表
    ${排除列表}    create list

    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${电表排除设备名称信息}    ${排除列表}
    ${缺省值列表}   获取缺省值列表  ${列表1}    1
	Run Keyword And Continue On Failure    南向子设备数值型厂家信息列表封装判断结果    ACmeter    ${缺省值列表}    只读
        ...    交流电表设备信息获取测试
    ${缺省值列表}   获取缺省值列表  ${列表1}    2
    Run Keyword And Continue On Failure    南向子设备数值型厂家信息列表封装判断结果    ACmeter    ${缺省值列表}    只读
        ...    交流电表设备信息获取测试
    ${缺省值列表}   获取缺省值列表  ${列表1}    0
    Run Keyword And Continue On Failure    南向子设备数值型厂家信息列表封装判断结果    ACmeter    ${缺省值列表}    只读
        ...    交流电表设备信息获取测试

交流电表软件版本获取测试
    连接CSU
    设置子工具值    ACmeter    all    只读    版本    255
    Wait Until Keyword Succeeds    10m   5    设备信息获取值    交流电表软件版本-1    V2.55
    Wait Until Keyword Succeeds    10m   5    设备信息获取值    交流电表软件版本-2    V2.55
    Wait Until Keyword Succeeds    10m   5    设备信息获取值    交流电表软件版本-3    V2.55
    设置子工具值    ACmeter    all    只读    版本    0
    Wait Until Keyword Succeeds    10m   5    设备信息获取值    交流电表软件版本-1    V0.0
    Wait Until Keyword Succeeds    10m   5    设备信息获取值    交流电表软件版本-2    V0.0
    Wait Until Keyword Succeeds    10m   5    设备信息获取值    交流电表软件版本-3    V0.0
    设置子工具值    ACmeter    all    只读    版本    211
    Wait Until Keyword Succeeds    10m   5    设备信息获取值    交流电表软件版本-1    V2.11
    Wait Until Keyword Succeeds    10m   5    设备信息获取值    交流电表软件版本-2    V2.11
    Wait Until Keyword Succeeds    10m   5    设备信息获取值    交流电表软件版本-3    V2.11
