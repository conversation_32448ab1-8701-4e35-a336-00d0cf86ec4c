*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_多个整流器模块告警
    [Documentation]    主要测试告警类5个级别的设置和8个干接点的设置。干接点实际是否动作需要将输出干接点连接到输入干接点，输出干接点接UIB_X3_DI1；请在测试前连接好
    [Tags]    T1-1
    [Setup]    run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    10    2    设置web参数量    多个整流器模块告警    严重
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    多个整流器模块告警
    ${均充电压可设置范围}    获取web参数可设置范围    均充电压
    设置web参数量    均充电压    ${均充电压可设置范围}[0]
    ${输出高停机电压可设置范围}    获取web参数可设置范围    整流器输出高停机电压
    设置web参数量    整流器输出高停机电压    ${输出高停机电压可设置范围}[0]
    ${整流器输出高电压}    获取web参数量    整流器输出高停机电压
    向上调节电池电压    ${整流器输出高电压} + 0.5
    Wait Until Keyword Succeeds    10m    1    信号量数据值为    工作整流器数量    0
    Wait Until Keyword Succeeds    5m    2    设置web参数量    多个整流器模块告警    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    多个整流器模块告警
    ${告警级别取值约定dict}    获取web参数的取值约定    交流输入限功率告警
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    多个整流器模块告警    ${告警级别设置}
        sleep    2
        wait until keyword succeeds    10m    1    查询指定告警信息    多个整流器模块告警
        ${多个整流器模块告警级别}    获取web告警属性    多个整流器模块告警    告警级别
        should be equal    ${告警级别设置}    ${多个整流器模块告警级别}
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    多个整流器模块告警    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    多个整流器模块告警    主要
    Wait Until Keyword Succeeds    30    1    设置web参数量    多个整流器模块告警干接点    0
    #整流器过压恢复，5min应能恢复
    设置web设备参数量为默认值    整流器输出高停机电压
    设置web设备参数量为默认值    均充电压
    向下调节电池电压    53.5
    ${在线整流器数量}    获取web实时数据    在线整流器数量
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    工作整流器数量    ${在线整流器数量}
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    多个整流器模块告警
    ${power_Sm告警存在}    判断power_sm是否存在单个实时告警    多个整流器模块告警    ${SSH}
    should not be true    ${power_Sm告警存在}[0]
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
    ...    AND    重置电池模拟器输出
    ...    AND    测试用例后置条件
    ...    AND    sleep    10
    ...    AND    整流器测试前置条件
