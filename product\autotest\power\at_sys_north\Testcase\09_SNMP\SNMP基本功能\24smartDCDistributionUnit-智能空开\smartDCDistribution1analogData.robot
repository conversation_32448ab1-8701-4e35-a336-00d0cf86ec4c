*** Settings ***
Resource          ../../../../测试用例关键字.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp_test关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/Web关键字_V30.robot

*** Test Cases ***
snmp批量获取SDDU模拟量测试
    写入CSV文档    SDDU模拟量获取测试    信号名称    信号值    结果    备注
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=智能直流配电单元
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SDDU2排除模拟量信号}    ${排除列表}
    ${设备名称}    Set Variable    smartDCDistributionUnit
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    ${设备名称}    analogData
    @{SDDU2模拟量数据}    create list
    FOR    ${i}    IN    @{snmp待测}
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        ${信号序号}    BuiltIn.Convert To Integer    ${信号序号}
        Run Keyword IF    ${信号序号}==1    Append to List    ${SDDU2模拟量数据}    ${i}
        Run Keyword IF    ${信号序号}==5    Append to List    ${SDDU2模拟量数据}    ${i}
        Run Keyword IF    ${信号序号}==9    Append to List    ${SDDU2模拟量数据}    ${i}
        Run Keyword IF    ${信号序号}==13    Append to List    ${SDDU2模拟量数据}    ${i}
    END
    FOR    ${i}    IN    @{SDDU2模拟量数据}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    snmp_name
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        ${信号序号1}    BuiltIn.Convert To Integer    ${信号序号}
        ${呼叫命令}    Evaluate    (${信号序号1}-1)//4+1
        ${命令名称}    Run Keyword IF    "${呼叫命令}"=="1"    Set Variable    呼叫
        ...    ELSE    Set Variable    呼叫${呼叫命令}
        ${缺省值}    获取web参数上下限范围    ${信号名称}
        Run Keyword And Continue On Failure    南向子设备模拟量获取值封装判断结果    snmp    IntelAirSwitExtend    ${信号名称}    ${命令名称}    ${缺省值}[1]    SDDU模拟量获取测试    null    ${节点名称}    ${信号序号}
        Run Keyword And Continue On Failure    南向子设备模拟量获取值封装判断结果    snmp    IntelAirSwitExtend    ${信号名称}    ${命令名称}    ${缺省值}[2]    SDDU模拟量获取测试    null    ${节点名称}    ${信号序号}
        Run Keyword And Continue On Failure    南向子设备模拟量获取值封装判断结果    snmp    IntelAirSwitExtend    ${信号名称}    ${命令名称}    ${缺省值}[0]    SDDU模拟量获取测试    null    ${节点名称}    ${信号序号}
    END
    断开连接SNMP

snmp获取智能空开电流
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${缺省值}    获取web参数上下限范围    <<智能空开电流~0x22001010080001>>
    设置子工具值    IntelAirSwitExtend    all    呼叫    空开电流    ${缺省值}[1]
    FOR    ${i}    IN RANGE    2    5
        设置子工具值    IntelAirSwitExtend    all    呼叫${i}    空开电流    ${缺省值}[1]
    END
    FOR    ${直流配电单元序号}    IN RANGE    1    3
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    智能空开电流_1-${直流配电单元序号}    ${缺省值}[1]
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    智能空开电流_5-${直流配电单元序号}    ${缺省值}[1]
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    智能空开电流_9-${直流配电单元序号}    ${缺省值}[1]
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    智能空开电流_13-${直流配电单元序号}    ${缺省值}[1]
        ${snmp获取值1}    获取SNMP数据_单个    smartDCDistributionUnitSmartSwitchCurrent${直流配电单元序号}value    1
        should be equal as numbers    ${snmp获取值1}    ${缺省值}[1]
        ${snmp获取值2}    获取SNMP数据_单个    smartDCDistributionUnitSmartSwitchCurrent${直流配电单元序号}value    5
        should be equal as numbers    ${snmp获取值2}    ${缺省值}[1]
        ${snmp获取值3}    获取SNMP数据_单个    smartDCDistributionUnitSmartSwitchCurrent${直流配电单元序号}value    9
        should be equal as numbers    ${snmp获取值3}    ${缺省值}[1]
        ${snmp获取值4}    获取SNMP数据_单个    smartDCDistributionUnitSmartSwitchCurrent${直流配电单元序号}value    13
        should be equal as numbers    ${snmp获取值4}    ${缺省值}[1]
    END
    设置子工具值    IntelAirSwitExtend    all    呼叫    空开电流    ${缺省值}[2]
    FOR    ${i}    IN RANGE    2    5
        设置子工具值    IntelAirSwitExtend    all    呼叫${i}    空开电流    ${缺省值}[2]
    END
    FOR    ${直流配电单元序号}    IN RANGE    1    3
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    智能空开电流_1-${直流配电单元序号}    ${缺省值}[2]
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    智能空开电流_5-${直流配电单元序号}    ${缺省值}[2]
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    智能空开电流_9-${直流配电单元序号}    ${缺省值}[2]
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    智能空开电流_13-${直流配电单元序号}    ${缺省值}[2]
        ${snmp获取值1}    获取SNMP数据_单个    smartDCDistributionUnitSmartSwitchCurrent${直流配电单元序号}value    1
        should be equal as numbers    ${snmp获取值1}    ${缺省值}[2]
        ${snmp获取值2}    获取SNMP数据_单个    smartDCDistributionUnitSmartSwitchCurrent${直流配电单元序号}value    5
        should be equal as numbers    ${snmp获取值2}    ${缺省值}[2]
        ${snmp获取值3}    获取SNMP数据_单个    smartDCDistributionUnitSmartSwitchCurrent${直流配电单元序号}value    9
        should be equal as numbers    ${snmp获取值3}    ${缺省值}[2]
        ${snmp获取值4}    获取SNMP数据_单个    smartDCDistributionUnitSmartSwitchCurrent${直流配电单元序号}value    13
        should be equal as numbers    ${snmp获取值4}    ${缺省值}[2]
    END
