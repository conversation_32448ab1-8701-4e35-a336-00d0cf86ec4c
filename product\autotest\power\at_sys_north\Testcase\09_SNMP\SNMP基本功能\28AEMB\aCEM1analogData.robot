*** Settings ***
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp_test关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/Web关键字_V30.robot
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
snmp批量获取ACEM模拟量
    [Documentation]    1h13min
    写入CSV文档    交流电表模拟量获取测试    信号名称    信号值    结果
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=交流电表
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${AEMB排除模拟量信号}    ${排除列表}
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    aCEM    analogData
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    1    snmp
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    snmp    DMU_aemb    只读    ${缺省值列表}    交流电表模拟量获取测试    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    2    snmp
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    snmp    DMU_aemb    只读    ${缺省值列表}    交流电表模拟量获取测试    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    0    snmp
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    snmp    DMU_aemb    只读    ${缺省值列表}    交流电表模拟量获取测试    null
    断开连接SNMP

snmp_0006_交流电表回路1总有功功率3
    连接CSU
    设置子工具值    DMU_aemb    all    只读    输入总有功功率_P    600000
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表回路1总有功功率-1    600
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${对比结果}    锂电多信号量设置对比    aCEMLoop1TotalActivePower1
    should be true    ${对比结果}
    设置子工具值    DMU_aemb    all    只读    输入总有功功率_P    220000
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表回路1总有功功率-1    220
    ${对比结果}    锂电多信号量设置对比    aCEMLoop1TotalActivePower1
    should be true    ${对比结果}
    设置子工具值    DMU_aemb    all    只读    输入总有功功率_P    0
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表回路1总有功功率-1    0
    ${对比结果}    锂电多信号量设置对比    aCEMLoop1TotalActivePower1
    should be true    ${对比结果}
    断开连接SNMP

snmp_0014_交流电表回路1电量7
    [Documentation]    SnmpKeyword.py 1257 snmp_value= 32767.0016 web_value= 32767.00
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    设置子工具值    DMU_aemb    all    只读    输入总有功电能    600
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表回路1电量-1    600
    ${对比结果}    锂电多信号量设置对比    aCEMLoop1Energy1
    should be true    ${对比结果}
    设置子工具值    DMU_aemb    all    只读    输入总有功电能    0
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表回路1电量-1    0
    ${对比结果}    锂电多信号量设置对比    aCEMLoop1Energy1
    should be true    ${对比结果}
    断开连接SNMP

