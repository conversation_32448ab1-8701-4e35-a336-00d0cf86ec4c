*** Settings ***
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_系统复位
    [Tags]    4
    ${能源网管数据}    能源网管协议_获取数据    系统复位    ${SSH}
    sleep    2m
    连接CSU
    @{历史事件内容}    获取web历史事件内容    ${empty}    ${empty}    所有    1    10
    ${是否复位}    Set Variable    False
    FOR    ${i}    IN    ${历史事件内容}
        ${是否复位}    Evaluate    "远程控制复位" in "${i}"
    END
    Should Be True    ${是否复位}
