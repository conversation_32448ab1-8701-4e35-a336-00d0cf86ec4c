*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
多个整流器模块告警测试
    [Documentation]    主要测试告警类5个级别的设置和8个干接点的设置。干接点实际是否动作需要将输出干接点连接到输入干接点，输出干接点接${plat.Inrelay1Status}；请在测试前连接好
    
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    10    2    设置web参数量    多个整流器模块告警    严重
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    多个整流器模块告警
    #使整流器输出过压来制造多个整流器告警
    ${均充电压可设置范围}    获取web参数可设置范围    均充电压
    设置web参数量    均充电压    ${均充电压可设置范围}[0]
    ${输出高停机电压可设置范围}    获取web参数可设置范围    整流器输出高停机电压
    设置web参数量    整流器输出高停机电压    ${输出高停机电压可设置范围}[0]
    ${整流器输出高电压}    获取web参数量    整流器输出高停机电压
    向上调节电池电压    ${整流器输出高电压} + 0.5
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    工作整流器数量    0
    Wait Until Keyword Succeeds    5m    2    设置web参数量    多个整流器模块告警    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    多个整流器模块告警
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    多个整流器模块告警    ${告警级别设置}
        sleep    2
        wait until keyword succeeds    1m    1    查询指定告警信息    多个整流器模块告警
        ${多个整流器模块告警级别}    获取web告警属性    多个整流器模块告警    告警级别
        should be equal    '${告警级别设置}'    '${多个整流器模块告警级别}'
    END
    
    Wait Until Keyword Succeeds    30    1    设置web参数量    多个整流器模块告警    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    FOR    ${告警干接点设置}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    多个整流器模块告警    告警干接点
        run keyword if    ${告警干接点获取}!=0    Wait Until Keyword Succeeds    30    1    设置web参数量    多个整流器模块告警干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        Wait Until Keyword Succeeds    30    1    设置web参数量    多个整流器模块告警干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    多个整流器模块告警    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    多个整流器模块告警干接点    0
    #整流器过压恢复，5min应能恢复
    设置web设备参数量为默认值    整流器输出高停机电压
    设置web设备参数量为默认值    均充电压
    向下调节电池电压    53.5
    ${在线整流器数量}    获取web实时数据    在线整流器数量
    Wait Until Keyword Succeeds    5m20s    10    信号量数据值为    工作整流器数量    ${在线整流器数量}
    [Teardown]    Run keywords    设置web参数量    多个整流器模块告警干接点    0
    ...    AND    重置电池模拟器输出
    

整流器故障测试
    [Documentation]    主要测试告警类5个级别的设置和8个干接点的设置。干接点实际是否动作需要将输出干接点连接到输入干接点，输出干接点接${plat.Inrelay1Status}；请在测试前连接好
    
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    10    2    设置web参数量    整流器故障    严重
    Wait Until Keyword Succeeds    10    2    设置web参数量    多个整流器模块告警    屏蔽
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    整流器故障
    #使整流器输出过压来制造整流器故障告警
    ${均充电压可设置范围}    获取web参数可设置范围    均充电压
    设置web参数量    均充电压    ${均充电压可设置范围}[0]
    ${输出高停机电压可设置范围}    获取web参数可设置范围    整流器输出高停机电压
    设置web参数量    整流器输出高停机电压    ${输出高停机电压可设置范围}[0]
    ${整流器输出高电压}    获取web参数量    整流器输出高停机电压
    向上调节电池电压    ${整流器输出高电压}+0.5
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    工作整流器数量    0
    Wait Until Keyword Succeeds    5m    2    设置web参数量    整流器故障    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    整流器故障-1
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        设置web参数量    整流器故障-1    ${告警级别设置}
        sleep    2
        wait until keyword succeeds    1m    1    查询指定告警信息    整流器故障-1
        ${整流器故障告警级别}    获取web告警属性    整流器故障-1    告警级别
        should be equal    ${告警级别设置}    ${整流器故障告警级别}
    END
    
    设置web参数量    整流器故障-1    主要
    FOR    ${告警干接点设置}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    整流器故障-1    告警干接点
        run keyword if    ${告警干接点获取}!=0    设置web参数量    整流器故障-1干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        设置web参数量    整流器故障-1干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    整流器故障-1    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
    END
    #整流器过压恢复，5min应能恢复
    设置web设备参数量为默认值    整流器输出高停机电压
    设置web设备参数量为默认值    均充电压
    向下调节电池电压    53.5
    ${在线整流器数量}    获取web实时数据    在线整流器数量
    Wait Until Keyword Succeeds    5m20s    10    信号量数据值为    工作整流器数量    ${在线整流器数量}
    [Teardown]    Run keywords    设置web参数量    整流器故障-1干接点    0
    ...    AND    设置web设备参数量为默认值    整流器输出高停机电压
    ...    AND    设置web设备参数量为默认值    均充电压
    ...    AND    设置web参数量    多个整流器模块告警    主要
    ...    AND    重置电池模拟器输出
    

