*** Settings ***
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
批量获取SDU2告警量测试
    写入CSV文档    SDU2数字量和告警量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=智能直流配电单元
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SDU2排除告警量信号}    ${排除列表}    2
	@{信号量列表}   create list
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
		${dict1}    Create Dictionary
        Set To Dictionary    ${dict1}     name     ${信号名称}
        Append To List       ${信号量列表}    ${dict1}
    END
	Run Keyword And Continue On Failure    南向RS485子设备告警量列表产生封装判断结果    SDU2    ${信号量列表}    呼叫    0    SDU2数字量和告警量获取测试    负载    智能直流配电单元
	Run Keyword And Continue On Failure    南向RS485子设备告警量列表恢复封装判断结果    SDU2    ${信号量列表}    呼叫    1    SDU2数字量和告警量获取测试    负载    智能直流配电单元
 
SDU2通讯中断告警测试
    连接CSU
    控制子工具运行停止    SDU2    关闭
    wait until keyword succeeds    10m    1    判断告警存在    SDDU通讯中断-1
    wait until keyword succeeds    10m    1    判断告警存在    SDDU通讯中断-2
    控制子工具运行停止    SDU2    开启
    wait until keyword succeeds    10m    1    判断告警不存在    SDDU通讯中断-1
    wait until keyword succeeds    10m    1    判断告警不存在    SDDU通讯中断-2
