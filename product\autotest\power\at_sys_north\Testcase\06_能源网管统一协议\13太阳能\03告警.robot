*** Settings ***
Suite Setup       主动告警测试前置条件    ${CSU_role}
Suite Teardown    设置web设备参数量为默认值    CSU主动告警使能
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0002_所有PU模块通讯断告警
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    Comment    显示属性配置    PU通讯状态    数字量    web_attr=On    gui_attr=On
    ${设备名称}    Run Keyword IF    '${CANTOOLS连接方式}'=='TCP'    Set Variable    pu_TCP
    ...    ELSE    Set Variable    pu
    ${级别设置值}    获取web参数量    所有PU模块通讯断告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    所有PU模块通讯断告警    严重
    ${告警级别取值约定dict}    获取web参数的取值约定    所有PU模块通讯断告警
    ${告警级别}    获取web参数量    所有PU模块通讯断告警
    控制子工具运行停止    ${设备名称}    关闭
    wait until keyword succeeds    6m    1    判断告警存在    所有PU模块通讯断告警
    Run Keyword And Continue On Failure    run keyword if    '${CSU_role}'=='Client'    主动告警上送判断2    power_sm    太阳能    所有PU模块通讯断告警
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    所有PU模块通讯断告警    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${告警级别}
    控制子工具运行停止    ${设备名称}    开启
    wait until keyword succeeds    5m    1    判断告警不存在    所有PU模块通讯断告警
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    所有PU模块通讯断告警    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    wait until keyword succeeds    1m    2    查询指定告警信息不为    所有PU模块通讯断告警
    Comment    显示属性配置    PU通讯状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    run keywords    控制子工具运行停止    pu    开启
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
