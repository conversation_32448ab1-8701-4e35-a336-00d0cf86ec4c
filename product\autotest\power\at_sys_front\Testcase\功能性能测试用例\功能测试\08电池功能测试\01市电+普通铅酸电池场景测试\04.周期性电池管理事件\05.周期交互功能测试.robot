*** Settings ***
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
周期时间同时到达测试
    [Documentation]    0:浮充/Float;1:均充/Equal;2:测试/Test;3:放电/Discharge;4:检测/Detect;5:过渡/Transition;6:充电/Charge
    
    电池管理初始化
    设置web参数量    电池均充周期    30    #默认90
    设置web参数量    电池测试周期    30    #默认0天
    设置web参数量    电池检测周期    30    #默认0
    设置web参数量    电池检测持续时间    2
    ${均充末期维持时间范围}    获取web参数上下限范围_有单位    均充末期维持时间
    ${均充最短时间范围}    获取web参数上下限范围_有单位    均充最短时间
    ${均充最长时间范围}    获取web参数上下限范围_有单位    均充最长时间
    ${均充末期维持时间}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间范围}[1]+1
    ...    ELSE    evaluate    ${均充末期维持时间范围}[1]+3
    ${均充最短时间}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间范围}[1]+1
    ...    ELSE    evaluate    ${均充最短时间范围}[1]+3
    ${均充最长时间}    run keyword if    '${均充最长时间范围}[3]'== 'Hour'    evaluate    ${均充最长时间范围}[1]+1
    ...    ELSE    evaluate    ${均充最长时间范围}[1]+5
    ${测试最长时间范围}    获取web参数上下限范围_有单位    测试最长时间
    ${测试最长时间}    run keyword if    '${测试最长时间范围}[3]'== 'Hour'    evaluate    ${测试最长时间范围}[1]+1
    ...    ELSE    evaluate    ${测试最长时间范围}[1]+5
    设置web参数量    测试最长时间    ${测试最长时间}
    设置web参数量    均充末期维持时间    ${均充末期维持时间}
    设置web参数量    均充最短时间    ${均充最短时间}
    设置web参数量    均充最长时间    ${均充最长时间}
    ${均充末期维持时间转换后}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间}*60
    ...    ELSE    set variable    ${均充末期维持时间}
    ${均充最短时间转换后}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间}*60
    ...    ELSE    set variable    ${均充最短时间}
    ${均充最长时间转换后}    run keyword if    '${均充最长时间范围}[3]'== 'Hour'    evaluate    ${均充最长时间}*60
    ...    ELSE    set variable    ${均充最长时间}
    ${测试最长时间转换后}    run keyword if    '${测试最长时间范围}[3]'== 'Hour'    evaluate    ${测试最长时间}*60
    ...    ELSE    set variable    ${测试最长时间}
    log    ===单位换算完成===
    连接CSU
    ${电池均充周期}    获取web参数量    电池均充周期
    ${下次均充时间}    获取web实时数据    下次均充时间
    ${电池测试周期}    获取web参数量    电池测试周期
    ${下次测试时间}    获取web实时数据    下次测试时间
    ${电池检测周期}    获取web参数量    电池检测周期
    ${下次检测时间}    获取web实时数据    下次检测时间
    ${下次时间}    Subtract Time From Date    ${下次均充时间}    5s    #比1:00:00差5s
    Wait Until Keyword Succeeds    10    1    设置系统时间    ${下次时间}
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    测试
    ${下次测试时间_cal}    add time to date    ${下次测试时间}    ${电池测试周期}d    exclude_millis=yes
    sleep    5
    ${下次测试时间_new}    获取web实时数据    下次测试时间
    should be equal    ${下次测试时间_new}    ${下次测试时间_cal}
    ${下次检测时间_cal}    add time to date    ${下次检测时间}    ${电池检测周期}d    exclude_millis=yes
    sleep    5
    ${下次检测时间_new}    获取web实时数据    下次检测时间
    should be equal    ${下次检测时间_new}    ${下次检测时间_cal}
    ${测试等待时间}    evaluate    ${测试最长时间转换后}+2
    ${均充等待时间}    evaluate    ${均充最长时间转换后}+2
    Wait Until Keyword Succeeds    ${测试等待时间}m    2    信号量数据值不为    电池管理状态    测试
    Wait Until Keyword Succeeds    10    1    信号量数据值为    电池管理状态    均充
    Wait Until Keyword Succeeds    ${均充等待时间}m    2    信号量数据值不为    电池管理状态    均充
    ${下次均充时间_cal}    add time to date    ${下次均充时间}    ${电池均充周期}d    exclude_millis=yes
    sleep    5
    连接CSU
    ${下次均充时间_new}    获取web实时数据    下次均充时间
    should be equal    ${下次均充时间_new}    ${下次均充时间_cal}
    Wait Until Keyword Succeeds    1m    2    信号量数据值为    电池管理状态    浮充
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池均充周期    电池测试周期    电池检测周期    均充最长时间
    ...    均充最短时间    均充末期维持时间    电池检测持续时间    测试最长时间
