*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
直流空调型号
    连接CSU
    设置子工具值    DMU_DCAirCondition    all    只读    读空调型号    airCondi1111
    FOR    ${直流空调序号}    IN RANGE    1    ${直流空调最大数}+1
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    直流空调型号-${直流空调序号}    airCondi1111
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取直流空调设备信息    直流空调型号-${直流空调序号}    ${SSH}
        should be equal as strings    ${power_sm获取值1}    airCondi1111
    END
    设置子工具值    DMU_DCAirCondition    all    只读    读空调型号    airCondi2222
    FOR    ${直流空调序号}    IN RANGE    1    ${直流空调最大数}+1
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    直流空调型号-${直流空调序号}    airCondi2222
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取直流空调设备信息    直流空调型号-${直流空调序号}    ${SSH}
        should be equal as strings    ${power_sm获取值1}    airCondi2222
    END
    设置子工具值    DMU_DCAirCondition    all    只读    读空调型号    airCondi
    FOR    ${直流空调序号}    IN RANGE    1    ${直流空调最大数}+1
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    直流空调型号-${直流空调序号}    airCondi
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取直流空调设备信息    直流空调型号-${直流空调序号}    ${SSH}
        should be equal as strings    ${power_sm获取值1}    airCondi
    END

直流空调软件版本
    连接CSU
    设置子工具值    DMU_DCAirCondition    all    只读    读软件版本    V0.0
    FOR    ${直流空调序号}    IN RANGE    1    ${直流空调最大数}+1
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    直流空调软件版本-${直流空调序号}    V0.0
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取直流空调设备信息    直流空调软件版本-${直流空调序号}    ${SSH}
        should be equal as strings    ${power_sm获取值1}    V0.0
    END
    设置子工具值    DMU_DCAirCondition    all    只读    读软件版本    V1.1
    FOR    ${直流空调序号}    IN RANGE    1    ${直流空调最大数}+1
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    直流空调软件版本-${直流空调序号}    V1.1
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取直流空调设备信息    直流空调软件版本-${直流空调序号}    ${SSH}
        should be equal as strings    ${power_sm获取值1}    V1.1
    END
    设置子工具值    DMU_DCAirCondition    all    只读    读软件版本    ZTE
    FOR    ${直流空调序号}    IN RANGE    1    ${直流空调最大数}+1
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    直流空调软件版本-${直流空调序号}    ZTE
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取直流空调设备信息    直流空调软件版本-${直流空调序号}    ${SSH}
        should be equal as strings    ${power_sm获取值1}    ZTE
    END
