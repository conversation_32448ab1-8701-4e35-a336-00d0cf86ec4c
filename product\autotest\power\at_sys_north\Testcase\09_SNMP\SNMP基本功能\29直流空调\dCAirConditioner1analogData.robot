*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
批量获取直流空调模拟量
    写入CSV文档    直流空调模拟量获取测试    信号名称    信号值    结果
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=直流空调
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${直流空调排除模拟量信号}    ${排除列表}    2
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    dCAirConditioner    analogData
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    1    snmp
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    snmp    DMU_DCAirCondition    只读    ${缺省值列表}    直流空调模拟量获取测试    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    2    snmp
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    snmp    DMU_DCAirCondition    只读    ${缺省值列表}    直流空调模拟量获取测试    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    0    snmp
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    snmp    DMU_DCAirCondition    只读    ${缺省值列表}    直流空调模拟量获取测试    null
    断开连接SNMP

设备运行时间
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    设置子工具值    DMU_DCAirCondition    all    只读    设备运行时间    0
    FOR    ${直流空调序号}    IN RANGE    1    ${直流空调最大数}+1
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    设备运行时间-${直流空调序号}    0
        ${snmp获取值1}    获取SNMP数据_单个    deviceRunningTime${直流空调序号}value
        should be equal as strings    ${snmp获取值1}    0
    END
    设置子工具值    DMU_DCAirCondition    all    只读    设备运行时间    2147483647
    FOR    ${直流空调序号}    IN RANGE    1    ${直流空调最大数}+1
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    设备运行时间-${直流空调序号}    2147483647
        ${snmp获取值1}    获取SNMP数据_单个    deviceRunningTime${直流空调序号}value
        should be equal as strings    ${snmp获取值1}    2147483647
    END
    设置子工具值    DMU_DCAirCondition    all    只读    设备运行时间    666
    FOR    ${直流空调序号}    IN RANGE    1    ${直流空调最大数}+1
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    设备运行时间-${直流空调序号}    666
        ${snmp获取值1}    获取SNMP数据_单个    deviceRunningTime${直流空调序号}value
        should be equal as strings    ${snmp获取值1}    666
    END
    [Teardown]    设置子工具值    DMU_DCAirCondition    all    模拟量    设备运行时间    666

内风机运行时间
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    设置子工具值    DMU_DCAirCondition    all    只读    内风机运行时间    0
    FOR    ${直流空调序号}    IN RANGE    1    ${直流空调最大数}+1
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    内风机运行时间-${直流空调序号}    0
        ${snmp获取值1}    获取SNMP数据_单个    indoorFanRunningTime${直流空调序号}value
        should be equal as strings    ${snmp获取值1}    0
    END
    设置子工具值    DMU_DCAirCondition    all    只读    内风机运行时间    2147483647
    FOR    ${直流空调序号}    IN RANGE    1    ${直流空调最大数}+1
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    内风机运行时间-${直流空调序号}    2147483647
        ${snmp获取值1}    获取SNMP数据_单个    indoorFanRunningTime${直流空调序号}value
        should be equal as strings    ${snmp获取值1}    2147483647
    END
    设置子工具值    DMU_DCAirCondition    all    只读    内风机运行时间    666
    FOR    ${直流空调序号}    IN RANGE    1    ${直流空调最大数}+1
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    内风机运行时间-${直流空调序号}    666
        ${snmp获取值1}    获取SNMP数据_单个    indoorFanRunningTime${直流空调序号}value
        should be equal as strings    ${snmp获取值1}    666
    END
    [Teardown]    设置子工具值    DMU_DCAirCondition    all    模拟量    内风机运行时间    666

外风机运行时间
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    设置子工具值    DMU_DCAirCondition    all    只读    外风机运行时间    0
    FOR    ${直流空调序号}    IN RANGE    1    ${直流空调最大数}+1
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    外风机运行时间-${直流空调序号}    0
        ${snmp获取值1}    获取SNMP数据_单个    outdoorFanRunningTime${直流空调序号}value
        should be equal as strings    ${snmp获取值1}    0
    END
    设置子工具值    DMU_DCAirCondition    all    只读    外风机运行时间    2147483647
    FOR    ${直流空调序号}    IN RANGE    1    ${直流空调最大数}+1
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    外风机运行时间-${直流空调序号}    2147483647
        ${snmp获取值1}    获取SNMP数据_单个    outdoorFanRunningTime${直流空调序号}value
        should be equal as strings    ${snmp获取值1}    2147483647
    END
    设置子工具值    DMU_DCAirCondition    all    只读    外风机运行时间    666
    FOR    ${直流空调序号}    IN RANGE    1    ${直流空调最大数}+1
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    外风机运行时间-${直流空调序号}    666
        ${snmp获取值1}    获取SNMP数据_单个    outdoorFanRunningTime${直流空调序号}value
        should be equal as strings    ${snmp获取值1}    666
    END
    [Teardown]    设置子工具值    DMU_DCAirCondition    all    模拟量    外风机运行时间    666
