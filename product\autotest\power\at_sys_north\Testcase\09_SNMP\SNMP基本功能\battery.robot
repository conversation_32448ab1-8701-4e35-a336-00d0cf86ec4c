*** Settings ***
Suite Setup       Run keywords    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池组容量_2    100
...               AND    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池组容量_3    100
...               AND    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池组容量_4    100
...               AND    sleep   5
Suite Teardown    Run keywords    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池组容量_2    0
...               AND    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池组容量_3    0
...               AND    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池组容量_4    0
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
snmp_0002_battery1analogDataX
    Comment    ${比较结果}    对比数据_V2C    battery1analogData
    Comment    should be true    ${比较结果}
    @{电池序号lsit}    evaluate    [i for i in range(1,5 )]
    FOR    ${VAR}    IN    @{电池序号lsit}
        ${比较结果}    对比数据_V2C    battery${VAR}analogData
        should be true    ${比较结果}
    END

snmp_0004_battery1digitalData√
    Comment    ${比较结果}    对比数据_V2C    battery1digitalData
    Comment    should be true    ${比较结果}
    @{电池序号lsit}    evaluate    [i for i in range(1,5 )]
    FOR    ${VAR}    IN    @{电池序号lsit}
        ${比较结果}    对比数据_V2C    battery${VAR}digitalData
        should be true    ${比较结果}
    END

snmp_0006_battery1alarmX
    Comment    ${比较结果}    对比告警_V2C    battery1alarm
    Comment    should be true    ${比较结果}
    @{电池序号lsit}    evaluate    [i for i in range(1,5 )]
    FOR    ${VAR}    IN    @{电池序号lsit}
        ${比较结果}    对比告警_V2C    battery${VAR}alarm
        should be true    ${比较结果}
    END

snmp_0008_battery1alarm_level√
    Comment    ${比较结果}    对比告警级别_V2C    battery1alarm
    Comment    should be true    ${比较结果}
    @{电池序号lsit}    evaluate    [i for i in range(1,5 )]
    FOR    ${VAR}    IN    @{电池序号lsit}
        ${比较结果}    对比告警级别_V2C    battery${VAR}alarm
        should be true    ${比较结果}
    END

snmp_0010_battery1alarm_level_write√
    Comment    ${比较结果}    批量修改告警级别_V2C    battery1alarm
    Comment    should be true    ${比较结果}
    Comment    ${比较结果}    批量设置四种/五种告警级别    battery1alarm
    Comment    should be true    ${比较结果}
    FOR    ${VAR}    IN    battery1alarm    battery2alarm    battery3alarm    battery4alarm
        ${比较结果}    批量设置四种/五种告警级别    ${VAR}
        should be true    ${比较结果}
    END

snmp_0012_battery1alarm_relay√
    Comment    ${比较结果}    对比告警干接点_V2C    battery1alarm
    Comment    should be true    ${比较结果}
    @{电池序号lsit}    evaluate    [i for i in range(1,5 )]
    FOR    ${VAR}    IN    @{电池序号lsit}
        ${比较结果}    对比告警干接点_V2C    battery${VAR}alarm
        should be true    ${比较结果}
    END

snmp_0014_battery1alarm_relay_write√
    Comment    ${比较结果}    批量修改告警干接点_V2C    battery1alarm
    Comment    should be true    ${比较结果}
    @{电池序号lsit}    evaluate    [i for i in range(1,5 )]
    FOR    ${VAR}    IN    @{电池序号lsit}
        ${比较结果}    批量修改告警干接点_V2C    battery${VAR}alarm
        should be true    ${比较结果}
    END

snmp_0016_battery1parameterX
    [Documentation]    batteryBatteryType1value 电池类型 0 -2147483648 1 .1.3.6.1.4.1.3902.2800.4.11.1.5.2.1 F
    [Setup]    run keywords    连接CSU
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    铅酸&锂电混用
    ${比较结果}    对比数据_V2C    battery1parameter
    should be true    ${比较结果}
    ${比较结果}    对比数据_V2C    battery2parameter
    should be true    ${比较结果}
    ${比较结果}    对比数据_V2C    battery3parameter
    should be true    ${比较结果}
    ${比较结果}    对比数据_V2C    battery4parameter
    should be true    ${比较结果}
    [Teardown]    run keywords    连接CSU
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    纯铅酸

snmp_0018_battery1parameterr_writeX
    [Documentation]    电池、参数、电池类型、snmp能设置（0/1）并且web有历史记录，但是snmp获取为无效值
    [Setup]    run keywords    连接CSU
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    铅酸&锂电混用
    Comment    ${比较结果}    批量修改参数_V2C    battery1parameter
    Comment    should be true    ${比较结果}
    ${比较结果}    SNMP批量参数设置    battery1parameter
    should be true    ${比较结果}
    ${比较结果}    SNMP批量参数设置    battery2parameter
    should be true    ${比较结果}
    ${比较结果}    SNMP批量参数设置    battery3parameter
    should be true    ${比较结果}
    ${比较结果}    SNMP批量参数设置    battery4parameter
    should be true    ${比较结果}
    [Teardown]    run keywords    连接CSU
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    纯铅酸
