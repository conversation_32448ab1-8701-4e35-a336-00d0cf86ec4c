*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
snmp批量获取SDDU数字量测试
    写入CSV文档    SDDU数字量获取测试    信号名称    信号值    结果    备注
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=digital data    INDEX_DEVICE_CH=智能直流配电单元
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SDDU排除数字量信号}    ${排除列表}
    ${设备名称}    Set Variable    smartDCDistributionUnit
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    ${设备名称}    digitalData
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    1    snmp
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    snmp    SDU2    呼叫    ${缺省值列表}    SDDU数字量获取测试    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    2    snmp
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    snmp    SDU2    呼叫    ${缺省值列表}    SDDU数字量获取测试    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    0    snmp
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    snmp    SDU2    呼叫    ${缺省值列表}    SDDU数字量获取测试    null
    断开连接SNMP

snmp获取SDDU工作状态
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    #正常
    设置子工具值    SDU2    all    呼叫    SDU负载分路状态1    1
    设置子工具值    SDU2    all    呼叫    SDU负载分路状态2    1
    设置子工具值    SDU2    all    呼叫    SDU负载分路状态3    1
    设置子工具值    SDU2    all    呼叫    SDU负载分路状态4    1
    FOR    ${直流配电单元序号}    IN RANGE    1    ${智能直流配电单元数量}+1
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    SDDU工作状态-${直流配电单元序号}    正常
        ${snmp获取值1}    获取SNMP数据_单个    sDDUWorkState${直流配电单元序号}value
        should be equal    ${snmp获取值1}    1
    END
    设置子工具值    SDU2    all    呼叫    SDU负载分路状态1    0
    设置子工具值    SDU2    all    呼叫    SDU负载分路状态2    0
    设置子工具值    SDU2    all    呼叫    SDU负载分路状态3    0
    设置子工具值    SDU2    all    呼叫    SDU负载分路状态4    0
    FOR    ${直流配电单元序号}    IN RANGE    1    ${智能直流配电单元数量}+1
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    SDDU工作状态-${直流配电单元序号}    告警
        ${snmp获取值1}    获取SNMP数据_单个    sDDUWorkState${直流配电单元序号}value
        should be equal    ${snmp获取值1}    2
    END
    设置子工具值    SDU2    all    呼叫    SDU负载分路状态1    1
    设置子工具值    SDU2    all    呼叫    SDU负载分路状态2    1
    设置子工具值    SDU2    all    呼叫    SDU负载分路状态3    1
    设置子工具值    SDU2    all    呼叫    SDU负载分路状态4    1
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    SDDU工作状态-${直流配电单元序号}    正常
    控制子工具运行停止    SDU2    关闭
    FOR    ${直流配电单元序号}    IN RANGE    1    ${智能直流配电单元数量}+1
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    SDDU工作状态-${直流配电单元序号}    通讯断
        ${snmp获取值1}    获取SNMP数据_单个    sDDUWorkState${直流配电单元序号}value
        should be equal    ${snmp获取值1}    3
    END
    [Teardown]    Run Keywords    控制子工具运行停止    SDU2    启动
    ...    AND    sleep    3min

snmp获取SDDU在位状态
    [Documentation]    无法自动化
    [Tags]    3
