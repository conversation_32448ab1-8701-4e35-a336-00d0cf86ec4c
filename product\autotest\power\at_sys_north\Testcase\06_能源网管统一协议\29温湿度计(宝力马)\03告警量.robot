*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
温湿度传感器通讯断告警
    连接CSU
    ${级别设置值}    获取web参数量    <<温湿度传感器通讯断告警~0x30001030010001>>
    run keyword if    '${级别设置值}'=='屏蔽'    wait until keyword succeeds    10    2    设置web参数量    <<温湿度传感器通讯断告警~0x30001030010001>>    严重
    控制子工具运行停止    DMU_WS312M1    关闭
    ${告警级别取值约定dict}    获取web参数的取值约定    <<温湿度传感器通讯断告警~0x30001030010001>>
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    温湿度传感器通讯断告警    ${告警级别}
        ${级别设置值}    获取web参数量    温湿度传感器通讯断告警
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    温湿度传感器通讯状态    异常
        wait until keyword succeeds    5m    2    查询指定告警信息    温湿度传感器通讯断告警
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    温湿度传感器通讯断告警    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${级别设置值}
    END
    控制子工具运行停止    DMU_WS312M1    开启
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    温湿度传感器通讯状态    正常
    wait until keyword succeeds    5m    1    判断告警不存在    温湿度传感器通讯断告警
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    温湿度传感器通讯断告警    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]
