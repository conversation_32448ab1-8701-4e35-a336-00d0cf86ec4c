*** Settings ***
Test Setup
Test Teardown
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
uniform_0002_交流模拟量√
    ${协议数据}    统一监控协议_获取数据    交流模拟量
    ${web数据}    统一监控协议_获取web对应数据    ${协议数据}
    ${对比结果}    批量对比数据_统一监控协议_WEB    ${协议数据}    ${web数据}    交流模拟量
    should be true    ${对比结果}

uniform_0004_交流开关量√
    ${协议数据}    统一监控协议_获取数据    交流状态量
    ${web数据}    统一监控协议_获取web对应数据    ${协议数据}
    ${对比结果}    批量对比数据_统一监控协议_WEB    ${协议数据}    ${web数据}    交流状态量
    should be true    ${对比结果}

uniform_0006_交流参数量√
    ${协议数据}    统一监控协议_获取数据    交流参数量
    ${web数据}    统一监控协议_获取web对应数据    ${协议数据}
    ${对比结果}    批量对比数据_统一监控协议_WEB    ${协议数据}    ${web数据}    交流参数量
    should be true    ${对比结果}

uniform_0008_交流参数设置√
    [Documentation]    \ # # # 市电额定有功功率 设置完后，将其设置为非零，进行后三个参数设置    # # # 市电降额系数、交流输入限功率预/告警阈值，需要将web中 \ 市电额定有功功率 值设置为非零（默认0）    # # # 远程一次二次下电，需要设置远程一次下电使能为允许才行,1为允许
    ${协议数据}    统一监控协议_批量参数设置测试    交流参数量
    ${对比结果}    批量对比参数设置_统一监控协议_WEB    ${协议数据}    交流参数量设置
    should be true    ${对比结果}
    [Teardown]    run keywords    设置web设备参数量为默认值    市电额定有功功率
    ...    AND    sleep    10
