*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
获取温湿度传感器数字量
    连接CSU
    控制子工具运行停止    DMU_WS312M1    关闭
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    温湿度传感器通讯状态    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    温湿度传感器在位状态    在位
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    温湿度传感器工作状态    通讯断
    ${power_sm获取值}    power_sm_获取一个数据的值    获取温湿度传感器数字量    温湿度传感器通讯状态    ${SSH}
    should be equal as strings    ${power_sm获取值}    1
    ${power_sm获取值}    power_sm_获取一个数据的值    获取温湿度传感器数字量    温湿度传感器在位状态    ${SSH}
    should be equal as strings    ${power_sm获取值}    1
    ${power_sm获取值}    power_sm_获取一个数据的值    获取温湿度传感器数字量    温湿度传感器工作状态    ${SSH}
    should be equal as strings    ${power_sm获取值}    3
    控制子工具运行停止    DMU_WS312M1    开启
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    温湿度传感器通讯状态    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    温湿度传感器在位状态    在位
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    温湿度传感器工作状态    正常
    ${power_sm获取值}    power_sm_获取一个数据的值    获取温湿度传感器数字量    温湿度传感器通讯状态    ${SSH}
    should be equal as strings    ${power_sm获取值}    0
    ${power_sm获取值}    power_sm_获取一个数据的值    获取温湿度传感器数字量    温湿度传感器在位状态    ${SSH}
    should be equal as strings    ${power_sm获取值}    1
    ${power_sm获取值}    power_sm_获取一个数据的值    获取温湿度传感器数字量    温湿度传感器工作状态    ${SSH}
    should be equal as strings    ${power_sm获取值}    1
    [Teardown]    Run Keywords    控制子工具运行停止    DMU_WS312M1    开启
    ...    AND    sleep    3min
