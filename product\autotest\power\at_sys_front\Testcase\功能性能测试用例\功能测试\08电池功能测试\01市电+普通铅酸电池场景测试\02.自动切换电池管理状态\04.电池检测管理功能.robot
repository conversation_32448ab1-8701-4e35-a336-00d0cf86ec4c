*** Settings ***
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
电池检测时交流停电转停电
    [Documentation]    0:浮充/Float;1:均充/Equal;2:测试/Test;3:放电/Discharge;4:检测/Detect;5:过渡/Transition;6:充电/Charge
    
    电池管理初始化
    sleep    5
    设置负载电压电流    53.5    10
    打开负载输出
    Wait Until Keyword Succeeds    5m    2    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    20    2    信号量数据值为    电池管理状态    检测
    关闭交流源输出
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    系统停电
    关闭负载输出
    打开交流源输出
    [Teardown]    run keywords    关闭负载输出
    ...    AND    打开交流源输出

电池检测时无正常工作整流器转停电
    
    电池管理初始化
    Wait Until Keyword Succeeds    3m    2    信号量数据值为    交流供电状态    市电1
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    过渡
    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    检测
    ${均充电压可设置范围}    获取web参数可设置范围    均充电压
    设置web参数量    均充电压    ${均充电压可设置范围}[0]
    ${输出高停机电压可设置范围}    获取web参数可设置范围    整流器输出高停机电压
    设置web参数量    整流器输出高停机电压    ${输出高停机电压可设置范围}[0]
    #使整流器输出过压保证无正常工作的整流器
    ${整流器输出高电压}    获取web参数量    整流器输出高停机电压
    向上调节电池电压    ${整流器输出高电压}+0.5
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    工作整流器数量    0
    ${状态}    获取web实时数据    电池管理状态
    #无整流器放电状态启动浮充，应F
    设置负载电压电流    53.5    10
    打开负载输出
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    系统停电
    #整流器过压恢复，5min应能恢复
    关闭负载输出
    向下调节电池电压    53.5
    ${在线整流器数量}    获取web实时数据    在线整流器数量
    Wait Until Keyword Succeeds    5m10s    1    信号量数据值为    工作整流器数量    ${在线整流器数量}
    [Teardown]    Run keywords    电池管理参数恢复默认值
    ...    AND    重置电池模拟器输出

电池检测时电池组无效转浮充
    
    电池管理初始化
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    检测
    sleep    30s
    设置web参数量    电池组容量_1    0
    sleep    30s
    Wait Until Keyword Succeeds    3m    1    信号量数据值为    电池管理状态    浮充
    [Teardown]    设置web参数量    电池组容量_1    100

检测时间到转浮充
    
    电池管理初始化
    #设置电池检测持续时间
    ${缺省值}    获取web参数上下限范围    电池检测持续时间
    设置web参数量    电池检测持续时间    ${缺省值}[0]
    sleep    5
    ${设置值}    获取web参数量    电池检测持续时间
    ${等待时间}    evaluate    ${设置值} +2
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    ${等待时间}m    1    信号量数据值为    电池管理状态    浮充
    [Teardown]    设置web参数量    电池检测持续时间    ${缺省值}[0]

检测时测试终止电压到转浮充
    
    电池管理初始化
    #设置测试终止电压
    ${测试终止电压可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${测试终止电压可设置范围}[0]
    ${缺省值}    获取web参数上下限范围    电池检测持续时间
    设置web参数量    电池检测持续时间    ${缺省值}[0]
    sleep    5
    设置web参数量    均充使能    允许
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    20    1    信号量数据值为    电池管理状态    检测
    ${设置值}    获取web参数量    测试终止电压
    向下调节电池电压    ${设置值}-0.5
    ${电池测试持续时间}    获取web实时数据    电池状态持续时间
    should be true    ${电池测试持续时间}<= ${缺省值}[0]
    Wait Until Keyword Succeeds    10    1    信号量数据值为    电池管理状态    浮充
    向上调节电池电压    53.5
    [Teardown]    设置web设备参数量为默认值    测试终止电压    电池检测持续时间

电池下电退出检测转浮充
    [Documentation]    0:浮充/Float;1:均充/Equal;2:测试/Test;3:交流停电（以前叫放电）/Discharge;4:检测/Detect;5:过渡/Transition;6:充电/Charge
    ...    ==20190323-f：测试最长时间到仍为2（增加启动测试后的电池状态判断，然后再去判断持续时间ok）
    
    电池管理初始化
    设置web参数量    电池检测持续时间    5
    设置web参数量    均充使能    允许
    wait until keyword succeeds    10    1    设置web参数量    电池高温下电使能    允许
    sleep    3
    ${可设范围}    获取web参数可设置范围    电池高温下电温度
    设置web参数量    电池高温下电温度    ${可设范围}[0]
    run keyword and ignore error    设置web参数量    下电控制延时    0
    显示属性配置    电池高温下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    3
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    检测
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    10    2    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    设置负载电压电流    53.5    10
    打开负载输出
    wait until keyword succeeds    5m    1    判断告警存在    电池高温下电
    Wait Until Keyword Succeeds    1m    2    信号量数据值为    电池高温下电控制状态    动作
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    电池管理状态    浮充
    #恢复
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    -10    1.6    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    电池高温下电控制状态    恢复
    wait until keyword succeeds    4m    2    判断告警不存在    电池高温下电
    显示属性配置    电池高温下电控制状态    数字量    web_attr=Off    gui_attr=Off
    关闭负载输出
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池高温下电温度    电池高温下电使能    电池检测持续时间
    ...    AND    关闭负载输出
