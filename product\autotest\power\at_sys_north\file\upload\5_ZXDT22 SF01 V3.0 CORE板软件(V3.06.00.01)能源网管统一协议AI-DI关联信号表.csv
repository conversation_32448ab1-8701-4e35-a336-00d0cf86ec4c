TD1,TI1,设备对象,device,TD2,TI2,signal_type,full_name,short_name,chinese_full_name,chinese_short_name,unit,data_type,dimension,precision,default_value,min_value,max_value,step,convention,constraints,备注
,,in-relay contract,,,,,,,,,,,,,,,,,,,
0x800201,0x401301,市电1,Mains,0x800a01,0x100101,digital data,MAINS Status,Mns.Status,市电输入状态,市电输入状态,,int,1,0,0,0,1,,0:正常/Normal;1:异常/Fault,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x401302,市电2,Mains,0x800a02,0x100101,digital data,MAINS Status,Mns.Status,市电输入状态,市电输入状态,,int,1,0,0,0,1,,0:正常/Normal;1:异常/Fault,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x401303,油机1,Diesel Generator,0x803201,0x180301,alarm,DG Common Alarm,DG Common Alm.,油机公共告警,油机公共告警,,int,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x401304,油机2,Diesel Generator,0x803202,0x180301,alarm,DG Common Alarm,DG Common Alm.,油机公共告警,油机公共告警,,int,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x401305,油机1,Diesel Generator,0x803201,0x180401,alarm,DG Low Fuel Level,DG Low Fl.Lev.,油机油位低告警,油机油位低告警,,int,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x401306,油机2,Diesel Generator,0x803202,0x180401,alarm,DG Low Fuel Level,DG Low Fl.Lev.,油机油位低告警,油机油位低告警,,int,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x401307,油机1,Diesel Generator,0x803201,0x180101,alarm,DG Start Alarm,DG Start Alm.,油机启动告警,油机启动告警,,int,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x401308,油机2,Diesel Generator,0x803202,0x180101,alarm,DG Start Alarm,DG Start Alm.,油机启动告警,油机启动告警,,int,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x401309,油机1,Diesel Generator,0x803201,0x180901,alarm,DG Manual Mode Alarm,DG Manual Mode,油机手动模式告警,油机手动模式,,int,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x40130a,油机2,Diesel Generator,0x803202,0x180901,alarm,DG Manual Mode Alarm,DG Manual Mode,油机手动模式告警,油机手动模式,,int,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x40130b,油机1,Diesel Generator,0x803201,0x180801,alarm,DG Access Control Alarm,DG Access Ctrl.,油机门磁告警,油机门磁告警,,int,1,0,0,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x40130c,油机2,Diesel Generator,0x803202,0x180801,alarm,DG Access Control Alarm,DG Access Ctrl.,油机门磁告警,油机门磁告警,,int,1,0,0,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x40130d,太阳能,Solar Energy,0x801e01,0x180301,alarm,PV component Missing,PV.Comp.Missing,光伏组件丢失,光伏组件丢失,,int,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x40130e,电池组,Battery Group,0x801801,0x180a01,alarm,Battery Group Missing,Batt.Group Missing,电池组丢失,电池组丢失,,int,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x40130f,电池1,Battery,0x801601,0x180801,alarm,Battery Missing,Batt. Missing,电池丢失,电池丢失,,int,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x401310,电池2,Battery,0x801602,0x180801,alarm,Battery Missing,Batt. Missing,电池丢失,电池丢失,,int,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x401311,电池3,Battery,0x801603,0x180801,alarm,Battery Missing,Batt. Missing,电池丢失,电池丢失,,int,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x401312,电池4,Battery,0x801604,0x180801,alarm,Battery Missing,Batt. Missing,电池丢失,电池丢失,,int,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x401313,系统运行环境,System Running Environment,0x801201,0x180d01,alarm,Temperature Control Unit Alarm,T.Ctrl Unit Alm,温控单元异常,温控单元异常,,int,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x401314,系统运行环境,System Running Environment,0x801201,0x180101,alarm,Smog Alarm,Smog Alarm,烟雾告警,烟雾告警,,int,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x401315,系统运行环境,System Running Environment,0x801201,0x180e01,alarm,DC Air Conditioner Alarm,DC Air Cond.Alm.,直流空调异常,直流空调异常,,int,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x401316,系统运行环境,System Running Environment,0x801201,0x180f01,alarm,Heater Fault Alarm,Heater Fault.Alm.,加热器异常,加热器异常,,int,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x401317,CSU,CSU,0x800201,0x180301,alarm,Input Relay Alarm1,Input Rly.Alm1,输入干接点告警1,输入干接点告警1,,int,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x401318,CSU,CSU,0x800201,0x180302,alarm,Input Relay Alarm2,Input Rly.Alm2,输入干接点告警2,输入干接点告警2,,int,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x401319,CSU,CSU,0x800201,0x180303,alarm,Input Relay Alarm3,Input Rly.Alm3,输入干接点告警3,输入干接点告警3,,int,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x40131a,CSU,CSU,0x800201,0x180304,alarm,Input Relay Alarm4,Input Rly.Alm4,输入干接点告警4,输入干接点告警4,,int,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x40131b,CSU,CSU,0x800201,0x180305,alarm,Input Relay Alarm5,Input Rly.Alm5,输入干接点告警5,输入干接点告警5,,int,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x40131c,CSU,CSU,0x800201,0x180306,alarm,Input Relay Alarm6,Input Rly.Alm6,输入干接点告警6,输入干接点告警6,,int,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x40131d,CSU,CSU,0x800201,0x180307,alarm,Input Relay Alarm7,Input Rly.Alm7,输入干接点告警7,输入干接点告警7,,int,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x40131e,CSU,CSU,0x800201,0x180308,alarm,Input Relay Alarm8,Input Rly.Alm8,输入干接点告警8,输入干接点告警8,,int,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x40131f,能源系统,Power System,0x800401,0x180701,alarm,Inverter Alarm,Inverter Alm,逆变器告警,逆变器告警,,int,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x401320,系统运行环境,System Running Environment,0x801201,0x181001,alarm,External Cabinet Smog Alarm,Ext.Cab.Smog Alm.,扩展柜烟雾告警,扩展柜烟雾告警,,int,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
,,out-relay contract,,,,,,,,,,,,,,,,,,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x401501,能源系统,Power System,0x800401,0x100401,digital data,Emergency Light Status,Emerg.Light Status,应急照明状态,应急照明状态,,int,1,0,0,0,1,,0:关闭/Off;1:开启/On,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x401502,油机1,Diesel Generator,0x803201,0x100401,digital data,DG Control Status,DG Control Status,油机控制状态,油机控制状态,,int,1,0,0,0,1,1,0:关闭/Off;1:开启/On,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x401503,油机2,Diesel Generator,0x803202,0x100401,digital data,DG Control Status,DG Control Status,油机控制状态,油机控制状态,,int,1,0,0,0,1,1,0:关闭/Off;1:开启/On,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
,,temp-ai contract,,,,,,,,,,,,,,,,,,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x401701,电池1,Battery,0x801601,0x080401,analog data,Battery Temperature,Battery Temp,电池温度,电池温度,℃,float,1,0,25,-40,100,,,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x401702,电池2,Battery,0x801602,0x080401,analog data,Battery Temperature,Battery Temp,电池温度,电池温度,℃,float,1,0,25,-40,100,,,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x401703,电池3,Battery,0x801603,0x080401,analog data,Battery Temperature,Battery Temp,电池温度,电池温度,℃,float,1,0,25,-40,100,,,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x401704,电池4,Battery,0x801604,0x080401,analog data,Battery Temperature,Battery Temp,电池温度,电池温度,℃,float,1,0,25,-40,100,,,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
0x800201,0x401705,系统运行环境,System Running Environment,0x801201,0x080101,analog data,Environment Temperature,Environment Temp,环境温度,环境温度,℃,float,1,0,25,-40,100,,,,1.关联信号集合每个软件版本各有不同，但都为总表的子集；2.第一个TD\TI是索引，第二个TD\TI是索引的值
