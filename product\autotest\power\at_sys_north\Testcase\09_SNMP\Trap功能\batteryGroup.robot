*** Settings ***
Suite Setup       测试用例前置条件
Suite Teardown    测试用例后置条件
Default Tags      T1-1
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
snmp_0002_一次下电告警
    [Setup]    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息不为    一次下电告警
    关闭交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    一次下电告警
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
        设置web参数量    电池组容量_${i}    0
    END
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    99
    sleep    2
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    30    1    设置web参数量    下电模式    电池电压    #默认1电池电压
    Wait Until Keyword Succeeds    30    1    设置web参数量    下电控制延时    0    #默认0
    Wait Until Keyword Succeeds    30    1    设置web参数量    负载一次下电使能    允许    #默认1允许
    Wait Until Keyword Succeeds    30    1    设置web参数量    负载二次下电使能    禁止    #默认1允许
    显示属性配置    一次下电控制状态    数字量    web_attr=On    gui_attr=On
    ${恢复时间可设置范围}    获取web参数可设置范围    一次下电恢复时间
    run keyword and ignore error    设置web参数量    一次下电恢复时间    ${恢复时间可设置范围}[0]
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${可设置范围}[1]
    ${电压低可设置范围}    获取web参数可设置范围    电池电压低阈值
    设置web参数量    电池电压低阈值    ${电压低可设置范围}[1]
    ${电池下电电压可设置范围}    获取web参数可设置范围    负载一次下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载一次下电电压    ${电池下电电压可设置范围}[1]
    设置负载电压电流    53.5    10
    打开负载输出
    ${一次下电电压设置值}    获取web参数量    负载一次下电电压
    ${一次下电电压}    evaluate    ${一次下电电压设置值}-0.5
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    向下调节电池电压    ${一次下电电压}
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    一次下电控制状态    动作
    wait until keyword succeeds    1m    1    查询指定告警信息    一次下电告警
    #产生
    ${snmp英文名}    获取snmp单个告警英文名    一次下电告警
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    向上调节电池电压    53.5
    关闭负载输出
    打开交流源输出
    Wait Until Keyword Succeeds    8m    1    信号量数据值为    一次下电控制状态    恢复
    Wait Until Keyword Succeeds    8m    2    查询指定告警信息不为    一次下电告警
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    显示属性配置    一次下电控制状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    负载一次下电电压    测试终止电压    电池电压低阈值    一次下电恢复时间
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出
    ...    AND    重置电池模拟器输出

snmp_0004_二次下电告警
    [Documentation]    二次下电告警不能设置为屏蔽级别，因此注释掉设置告警级别为0的语句。
    [Setup]    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息不为    二次下电告警
    关闭交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    二次下电告警
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
        设置web参数量    电池组容量_${i}    0
    END
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    99
    sleep    2
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    100
    wait until keyword succeeds    30    1    设置web参数量    下电模式    电池电压    #默认1电池电压
    wait until keyword succeeds    30    1    设置web参数量    下电控制延时    0    #默认0
    wait until keyword succeeds    30    1    设置web参数量    负载一次下电使能    禁止    #默认1允许
    wait until keyword succeeds    30    1    设置web参数量    负载二次下电使能    允许    #默认1允许
    显示属性配置    二次下电控制状态    数字量    web_attr=On    gui_attr=On
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${可设置范围}[1]
    ${电压低可设置范围}    获取web参数可设置范围    电池电压低阈值
    设置web参数量    电池电压低阈值    ${电压低可设置范围}[1]
    ${恢复时间可设置范围}    获取web参数可设置范围    二次下电恢复时间
    run keyword and ignore error    设置web参数量    二次下电恢复时间    ${恢复时间可设置范围}[0]
    ${二次下电电压可设置范围}    获取web参数可设置范围    负载二次下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电电压    ${二次下电电压可设置范围}[1]
    设置负载电压电流    53.5    10
    打开负载输出
    ${下电电压设置值}    获取web参数量    负载二次下电电压
    ${下电电压}    evaluate    ${下电电压设置值}-0.5
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    向下调节电池电压    ${下电电压}
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    二次下电控制状态    动作
    wait until keyword succeeds    1m    1    查询指定告警信息    二次下电告警
    #产生
    ${snmp英文名}    获取snmp单个告警英文名    二次下电告警
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    向上调节电池电压    53.5
    关闭负载输出
    打开交流源输出
    Wait Until Keyword Succeeds    8m    1    信号量数据值为    二次下电控制状态    恢复
    Wait Until Keyword Succeeds    8m    2    查询指定告警信息不为    二次下电告警
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    显示属性配置    二次下电控制状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    负载二次下电电压    测试终止电压    电池电压低阈值    负载一次下电使能    二次下电恢复时间
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出
    ...    AND    重置电池模拟器输出

snmp_0006_电池下电告警
    [Documentation]    电池下电告警不能设置为屏蔽级别，因此注释掉设置告警级别为0的语句。
    [Setup]    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    5
    ${电池测试失败级别}    获取web参数量    电池下电告警
    run keyword if    '${电池测试失败级别}'=='屏蔽'    设置web参数量    电池下电告警    严重
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息不为    电池下电告警
    关闭交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    电池下电告警
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
        设置web参数量    电池组容量_${i}    0
    END
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    99
    sleep    2
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电模式    电池电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电控制延时    0
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载一次下电使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池下电使能    允许
    ${可设置范围}    获取web参数可设置范围    电池下电恢复回差
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池下电恢复回差    ${可设置范围}[0]
    显示属性配置    电池下电控制状态    数字量    web_attr=On    gui_attr=On
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${可设置范围}[1]
    ${电压低可设置范围}    获取web参数可设置范围    电池电压低阈值
    设置web参数量    电池电压低阈值    ${电压低可设置范围}[1]
    ${下电电压可设置范围}    获取web参数可设置范围    电池下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池下电电压    ${下电电压可设置范围}[1]
    设置负载电压电流    53.5    10
    打开负载输出
    ${下电电压设置值}    获取web参数量    电池下电电压
    ${下电电压}    evaluate    ${下电电压设置值}-0.5
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    向下调节电池电压    ${下电电压}
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池下电控制状态    动作
    wait until keyword succeeds    1m    1    查询指定告警信息    电池下电告警
    #产生
    ${snmp英文名}    获取snmp单个告警英文名    电池下电告警
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    向上调节电池电压    53.5
    关闭负载输出
    打开交流源输出
    Wait Until Keyword Succeeds    8m    1    信号量数据值为    电池下电控制状态    恢复
    Wait Until Keyword Succeeds    8m    2    查询指定告警信息不为    电池下电告警
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    显示属性配置    电池下电控制状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    电池下电电压    测试终止电压    电池电压低阈值    负载一次下电使能    负载二次下电使能
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出
    ...    AND    重置电池模拟器输出

snmp_0008_电池检测异常
    [Documentation]    电池检测异常后再次执行电池检测以便消除电池检测异常告警。
    [Setup]    重置电池模拟器输出
    电池管理初始化
    #获取告警级别
    ${级别设置值}    获取web参数量    电池检测异常
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池检测异常    严重
    Wait Until Keyword Succeeds    10    2s    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    12m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    15m    2    查询指定告警信息不为    电池检测异常
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    关闭电池模拟器输出
    Wait Until Keyword Succeeds    15m    2    查询指定告警信息    电池检测异常
    #产生
    ${snmp英文名}    获取snmp单个告警英文名    电池检测异常
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    重置电池模拟器输出
    Wait Until Keyword Succeeds    15m    2s    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    15m    2    查询指定告警信息    电池检测异常
    Wait Until Keyword Succeeds    15m    1    判断告警不存在    电池检测异常
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    Wait Until Keyword Succeeds    12m    2    设置web控制量    启动浮充
    [Teardown]    设置web参数量    电池检测异常    严重

snmp_0010_电池测试√
    [Documentation]    告警级别默认屏蔽
    电池管理初始化
    Wait Until Keyword Succeeds    5m    2    设置web参数量    电池测试    次要
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息不为    电池测试
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    Wait Until Keyword Succeeds    5m    2s    设置web控制量    启动测试
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    测试
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    电池测试
    #产生
    ${snmp英文名}    获取snmp单个告警英文名    电池测试
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    Wait Until Keyword Succeeds    2m    2    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池测试
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    设置web参数量    电池测试    屏蔽

snmp_0012_电池均充√
    [Documentation]    ==20190327-F：启动电池均充后因电池组异常退出均充，无电池均充告警
    电池管理初始化
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池均充    次要
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息不为    电池均充
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    Wait Until Keyword Succeeds    10    1    设置web控制量    启动均充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    均充
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    电池均充
    #产生
    ${snmp英文名}    获取snmp单个告警英文名    电池均充
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    Wait Until Keyword Succeeds    2m    2    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池均充
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    设置web参数量    电池均充    次要

snmp_0014_电池组丢失√
    连接CSU
    #获取参数/默认值
    #获取告警级别
    ${级别设置值}    获取web参数量    电池组丢失
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池组丢失    主要
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息不为    电池组丢失
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    ${DI2通道配置}    获取通道配置    ${plat.Inrelay2Status}
    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay2Status}    电池组    电池组丢失    断开
    Wait Until Keyword Succeeds    2m    1    查询指定告警信息    电池组丢失
    #产生
    ${snmp英文名}    获取snmp单个告警英文名    电池组丢失
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay2Status}    ${DI2通道配置}[1]    ${DI2通道配置}[2]    ${DI2通道配置}[3]
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池组丢失
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    Run keywords    设置web参数量    电池组丢失    主要
    ...    AND    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay2Status}    ${DI2通道配置}[1]    ${DI2通道配置}[2]    ${DI2通道配置}[3]

snmp_0016_电池低温下电
    [Documentation]    电池低温下电使能下电
    电池管理初始化
    #获取参数/默认值
    wait until keyword succeeds    10    1    设置web参数量    电池低温下电使能    允许
    ${告警级别}    获取web参数量    电池低温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    电池低温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    电池低温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    sleep    3
    ${可设范围}    获取web参数可设置范围    电池低温下电温度
    设置web参数量    电池低温下电温度    ${可设范围}[1]
    run keyword and ignore error    设置web参数量    下电控制延时    0
    显示属性配置    电池低温下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    3
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    -10    0.001    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    设置负载电压电流    53.5    10
    打开负载输出
    wait until keyword succeeds    6m    1    判断告警存在    电池低温下电
    Wait Until Keyword Succeeds    6m    2    信号量数据值为    电池低温下电控制状态    动作
    #恢复
    设置web参数量    电池低温下电使能    禁止
    wait until keyword succeeds    4m    2    判断告警不存在    电池低温下电
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    设置web参数量    电池低温下电使能    允许
    Wait Until Keyword Succeeds    5    1    显示属性配置    电池低温下电控制状态    数字量    web_attr=Off    gui_attr=Off
    关闭负载输出
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池低温下电温度    电池低温下电使能
    ...    AND    关闭负载输出

snmp_0018_电池高温下电
    [Documentation]    电池高温下电使能下电
    电池管理初始化
    #获取参数/默认值
    wait until keyword succeeds    10    1    设置web参数量    电池高温下电使能    允许
    ${告警级别}    获取web参数量    电池高温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    电池高温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    电池高温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    ${电池电压}    获取web实时数据    电池电压-1
    sleep    3
    ${可设范围}    获取web参数可设置范围    电池高温下电温度
    设置web参数量    电池高温下电温度    ${可设范围}[0]
    run keyword and ignore error    设置web参数量    下电控制延时    0
    显示属性配置    电池高温下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    3
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    10    2    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度
    设置负载电压电流    53.5    10
    打开负载输出
    wait until keyword succeeds    15m    1    判断告警存在    电池高温下电
    Wait Until Keyword Succeeds    3m    2    信号量数据值为    电池高温下电控制状态    动作
    #恢复
    设置web参数量    电池高温下电使能    禁止
    Comment    Wait Until Keyword Succeeds    7m    2    信号量数据值为    电池高温下电控制状态    恢复
    wait until keyword succeeds    15m    2    判断告警不存在    电池高温下电
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    设置web参数量    电池高温下电使能    允许
    Wait Until Keyword Succeeds    5    1    显示属性配置    电池高温下电控制状态    数字量    web_attr=Off    gui_attr=Off
    关闭负载输出
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池高温下电温度    电池高温下电使能
    ...    AND    关闭负载输出
