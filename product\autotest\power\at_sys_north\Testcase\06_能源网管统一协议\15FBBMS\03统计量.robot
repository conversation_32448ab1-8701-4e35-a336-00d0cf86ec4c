*** Settings ***
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_累计放电电量
    连接CSU
    显示属性配置    累计放电电量    统计量    web_attr=On    gui_attr=On
    @{锂电序号随机list}    从1-15个数中随机选n个不重复的单体    ${北向协议铁锂最大数}    3
    ${目标值}    Convert To String    0
    设置子工具值    smartli    all    只读    累计放电电量    ${目标值}
    FOR    ${锂电序号}    IN    @{锂电序号随机list}
        ${锂电序号}    Convert To String    ${锂电序号}
        Wait Until Keyword Succeeds    30m    5    信号量数据值为    累计放电电量-${锂电序号}    0
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取FBBMS统计量    累计放电电量-${锂电序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    0
    END
    ${目标值1}    Convert To String    65536
    设置子工具值    smartli    all    只读    累计放电电量    ${目标值1}
    FOR    ${锂电序号}    IN    @{锂电序号随机list}
        ${锂电序号}    Convert To String    ${锂电序号}
        Wait Until Keyword Succeeds    30m    5    信号量数据值为    累计放电电量-${锂电序号}    65536
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取FBBMS统计量    累计放电电量-${锂电序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    65536
    END
    ${目标值2}    Convert To String    100
    设置子工具值    smartli    all    只读    累计放电电量    ${目标值2}
    FOR    ${锂电序号}    IN    @{锂电序号随机list}
        ${锂电序号}    Convert To String    ${锂电序号}
        Wait Until Keyword Succeeds    30m    5    信号量数据值为    累计放电电量-${锂电序号}    100
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取FBBMS统计量    累计放电电量-${锂电序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    100
    END
    显示属性配置    累计放电电量    统计量    web_attr=Off    gui_attr=Off

power_sm_0002_累计放电容量
    连接CSU
    显示属性配置    累计放电容量    统计量    web_attr=On    gui_attr=On
    @{锂电序号随机list}    从1-15个数中随机选n个不重复的单体    ${北向协议铁锂最大数}    3
    ${目标值}    Convert To String    0
    设置子工具值    smartli    all    只读    累计放电容量    ${目标值}
    FOR    ${锂电序号}    IN    @{锂电序号随机list}
        ${锂电序号}    Convert To String    ${锂电序号}
        Wait Until Keyword Succeeds    30m    5    信号量数据值为    累计放电容量-${锂电序号}    0
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取FBBMS统计量    累计放电容量-${锂电序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    0
    END
    ${目标值1}    Convert To String    65536
    设置子工具值    smartli    all    只读    累计放电容量    ${目标值1}
    FOR    ${锂电序号}    IN    @{锂电序号随机list}
        ${锂电序号}    Convert To String    ${锂电序号}
        Wait Until Keyword Succeeds    30m    5    信号量数据值为    累计放电容量-${锂电序号}    65536
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取FBBMS统计量    累计放电容量-${锂电序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    65536
    END
    ${目标值2}    Convert To String    10000
    设置子工具值    smartli    all    只读    累计放电容量    ${目标值2}
    FOR    ${锂电序号}    IN    @{锂电序号随机list}
        ${锂电序号}    Convert To String    ${锂电序号}
        Wait Until Keyword Succeeds    30m    5    信号量数据值为    累计放电容量-${锂电序号}    10000
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取FBBMS统计量    累计放电容量-${锂电序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    10000
    END
    显示属性配置    累计放电容量    统计量    web_attr=Off    gui_attr=Off
