*** Settings ***
Suite Setup
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
1363_0004_V_3.0_1363_环境段_模拟量
    [Tags]    1
    ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取环境段模拟量~CID1=91H>>    <<获取环境段模拟量~CID2=42H>>    None    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${对比结果}    批量对比数据_1104_WEB    ${协议数据}    ${web数据}    1363_环境段_模拟量
    should be true    ${对比结果}

1363_0006_V_3.0_1363_环境段_参数设置
    [Documentation]    OK
    [Tags]    1
    Comment    仅有市电条件上电
    连接CSU
    Comment    关闭交流源输出
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    环境温度高阈值
    ${缺省值}    获取web参数上下限范围    环境温度高阈值
    ${可设置范围}    获取web参数可设置范围    环境温度高阈值
    ${超下限}    evaluate    ${可设置范围}[0]-1
    ${超上限}    evaluate    ${可设置范围}[1]+1
    #温度传感器上限
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        Comment    ${设置结果}    run keyword and return status    设置web参数量    电池温度高    ${参数设置}
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置环境段参数量~CID1=91H>>    <<设置环境段参数量~CID2=49H>>    温度传感器上限
        ...    ${参数设置}    1    ${g_ver_1363}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    环境温度高阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        Comment    设置web参数量    PU输出过压保护值    ${参数设置}
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置环境段参数量~CID1=91H>>    <<设置环境段参数量~CID2=49H>>    温度传感器上限
        ...    ${参数设置}    1    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    1
        ${参数获取}    获取web参数量    环境温度高阈值
        should be true    ${参数获取}==${参数设置}
    END
    #
    #湿度传感器上限
    #超范围设置不成功
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    环境湿度高阈值
    ${缺省值}    获取web参数上下限范围    环境湿度高阈值
    ${可设置范围}    获取web参数可设置范围    环境湿度高阈值
    ${超下限}    evaluate    ${可设置范围}[0]-1
    ${超上限}    evaluate    ${可设置范围}[1]+1
    #温度传感器上限
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        Comment    ${设置结果}    run keyword and return status    设置web参数量    环境温度低阈值    ${参数设置}
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置环境段参数量~CID1=91H>>    <<设置环境段参数量~CID2=49H>>    湿度传感器上限
        ...    ${参数设置}    1    ${g_ver_1363}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    环境湿度高阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        Comment    设置web参数量    PU输出过压保护值    ${参数设置}
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置环境段参数量~CID1=91H>>    <<设置环境段参数量~CID2=49H>>    湿度传感器上限
        ...    ${参数设置}    1    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    1
        ${参数获取}    获取web参数量    环境湿度高阈值
        should be true    ${参数获取}==${参数设置}
    END
    #
    #温度传感器下限
    #超范围设置不成功
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    环境温度低阈值
    ${缺省值}    获取web参数上下限范围    环境温度低阈值
    ${可设置范围}    获取web参数可设置范围    环境温度低阈值
    ${超下限}    evaluate    ${可设置范围}[0]-1
    ${超上限}    evaluate    ${可设置范围}[1]+1
    #温度传感器上限
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        Comment    ${设置结果}    run keyword and return status    设置web参数量    环境温度低阈值    ${参数设置}
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置环境段参数量~CID1=91H>>    <<设置环境段参数量~CID2=49H>>    温度传感器下限
        ...    ${参数设置}    1    ${g_ver_1363}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    环境温度低阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        Comment    设置web参数量    PU输出过压保护值    ${参数设置}
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置环境段参数量~CID1=91H>>    <<设置环境段参数量~CID2=49H>>    温度传感器下限
        ...    ${参数设置}    1    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    1
        ${参数获取}    获取web参数量    环境温度低阈值
        should be true    ${参数获取}==${参数设置}
    END
    #湿度传感器下限
    #超范围设置不成功
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    环境湿度低阈值
    ${缺省值}    获取web参数上下限范围    环境湿度低阈值
    ${可设置范围}    获取web参数可设置范围    环境湿度低阈值
    ${超下限}    evaluate    ${可设置范围}[0]-1
    ${超上限}    evaluate    ${可设置范围}[1]+1
    #温度传感器上限
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        Comment    ${设置结果}    run keyword and return status    设置web参数量    环境温度低阈值    ${参数设置}
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置环境段参数量~CID1=91H>>    <<设置环境段参数量~CID2=49H>>    湿度传感器下限
        ...    ${参数设置}    1    ${g_ver_1363}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    环境湿度低阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        Comment    设置web参数量    PU输出过压保护值    ${参数设置}
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置环境段参数量~CID1=91H>>    <<设置环境段参数量~CID2=49H>>    湿度传感器下限
        ...    ${参数设置}    1    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    1
        ${参数获取}    获取web参数量    环境湿度低阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    run keywords    连接CSU
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    环境温度高阈值
    ...    环境温度低阈值    环境湿度高阈值    环境湿度低阈值

1363_0008_V_3.0_1363_环境段_参数量
    [Tags]    1
    ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获获取环境段参数量~CID1=91H>>    <<获取环境段参数量~CID2=47H>>    None    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${对比结果}    批量对比数据_1104_WEB    ${协议数据}    ${web数据}    1363_环境段_参数量
    should be true    ${对比结果}

1363_0006_V_3.0_1363_环境温度高阈值_参数设置
    [Documentation]    OK
    [Tags]    1
    Comment    仅有市电条件上电
    连接CSU
    Comment    关闭交流源输出
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    环境温度高阈值
    ${缺省值}    获取web参数上下限范围    环境温度高阈值
    ${可设置范围}    获取web参数可设置范围    环境温度高阈值
    ${超下限}    evaluate    ${可设置范围}[0]-1
    ${超上限}    evaluate    ${可设置范围}[1]+1
    #温度传感器上限
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        Comment    ${设置结果}    run keyword and return status    设置web参数量    电池温度高    ${参数设置}
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置环境段参数量~CID1=91H>>    <<设置环境段参数量~CID2=49H>>    温度传感器上限
        ...    ${参数设置}    1    ${g_ver_1363}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    环境温度高阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        Comment    设置web参数量    PU输出过压保护值    ${参数设置}
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置环境段参数量~CID1=91H>>    <<设置环境段参数量~CID2=49H>>    温度传感器上限
        ...    ${参数设置}    1    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    1
        ${参数获取}    获取web参数量    环境温度高阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    run keywords    连接CSU
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    环境温度高阈值

1363_0006_V_3.0_1363_环境温度低阈值_参数设置
    [Documentation]    OK
    [Tags]    1
    Comment    仅有市电条件上电
    连接CSU
    Comment    关闭交流源输出
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    环境温度低阈值
    ${缺省值}    获取web参数上下限范围    环境温度低阈值
    ${可设置范围}    获取web参数可设置范围    环境温度低阈值
    ${超下限}    evaluate    ${可设置范围}[0]-1
    ${超上限}    evaluate    ${可设置范围}[1]+1
    #温度传感器上限
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        Comment    ${设置结果}    run keyword and return status    设置web参数量    环境温度低阈值    ${参数设置}
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置环境段参数量~CID1=91H>>    <<设置环境段参数量~CID2=49H>>    温度传感器下限
        ...    ${参数设置}    1    ${g_ver_1363}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    环境温度低阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        Comment    设置web参数量    PU输出过压保护值    ${参数设置}
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置环境段参数量~CID1=91H>>    <<设置环境段参数量~CID2=49H>>    温度传感器下限
        ...    ${参数设置}    1    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    1
        ${参数获取}    获取web参数量    环境温度低阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    run keywords    连接CSU
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    环境温度低阈值

1363_0006_V_3.0_1363_环境湿度高阈值_参数设置
    [Documentation]    OK
    [Tags]    1
    Comment    仅有市电条件上电
    连接CSU
    Comment    关闭交流源输出
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    环境湿度高阈值
    ${缺省值}    获取web参数上下限范围    环境湿度高阈值
    ${可设置范围}    获取web参数可设置范围    环境湿度高阈值
    ${超下限}    evaluate    ${可设置范围}[0]-1
    ${超上限}    evaluate    ${可设置范围}[1]+1
    #温度传感器上限
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        Comment    ${设置结果}    run keyword and return status    设置web参数量    环境温度低阈值    ${参数设置}
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置环境段参数量~CID1=91H>>    <<设置环境段参数量~CID2=49H>>    湿度传感器上限
        ...    ${参数设置}    1    ${g_ver_1363}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    环境湿度高阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        Comment    设置web参数量    PU输出过压保护值    ${参数设置}
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置环境段参数量~CID1=91H>>    <<设置环境段参数量~CID2=49H>>    湿度传感器上限
        ...    ${参数设置}    1    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    1
        ${参数获取}    获取web参数量    环境湿度高阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    run keywords    连接CSU
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    环境湿度高阈值

1363_0006_V_3.0_1363_环境湿度低阈值_参数设置
    [Documentation]    OK
    [Tags]    1
    Comment    仅有市电条件上电
    连接CSU
    Comment    关闭交流源输出
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    环境湿度低阈值
    ${缺省值}    获取web参数上下限范围    环境湿度低阈值
    ${可设置范围}    获取web参数可设置范围    环境湿度低阈值
    ${超下限}    evaluate    ${可设置范围}[0]-1
    ${超上限}    evaluate    ${可设置范围}[1]+1
    #温度传感器上限
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        Comment    ${设置结果}    run keyword and return status    设置web参数量    环境温度低阈值    ${参数设置}
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置环境段参数量~CID1=91H>>    <<设置环境段参数量~CID2=49H>>    湿度传感器下限
        ...    ${参数设置}    1    ${g_ver_1363}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    环境湿度低阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        Comment    设置web参数量    PU输出过压保护值    ${参数设置}
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置环境段参数量~CID1=91H>>    <<设置环境段参数量~CID2=49H>>    湿度传感器下限
        ...    ${参数设置}    1    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    1
        ${参数获取}    获取web参数量    环境湿度低阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    run keywords    连接CSU
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    环境湿度低阈值
