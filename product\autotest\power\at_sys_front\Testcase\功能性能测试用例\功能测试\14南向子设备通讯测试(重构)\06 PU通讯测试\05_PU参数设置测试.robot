*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
批量设置参数测试
    [Tags]    PMSA-NTest
    写入CSV文档    整流器参数测试    参数名称    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=parameter    INDEX_DEVICE_CH=太阳能    INDEX_DATA_TYPE=int
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${取值约定}    Get From Dictionary    ${i}    convention
        ${设备名称}    Get From Dictionary    ${i}    device_name
        run keyword if    '${取值约定}'=='False'    Run Keyword And Continue On Failure    南向子设备数值型参数设置封装判断结果    pu    ${信号名称}    参数    整流器参数测试
        run keyword if    '${取值约定}'=='True'    Run Keyword And Continue On Failure    南向子设备有取值约定型参数设置封装判断结果    pu    ${信号名称}    参数    整流器参数测试
    END
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=parameter    INDEX_DEVICE_CH=太阳能    INDEX_DATA_TYPE=float
    ${排除列表}    create list
    ${列表2}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}
    FOR    ${i}    IN    @{列表2}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${取值约定}    Get From Dictionary    ${i}    convention
        ${设备名称}    Get From Dictionary    ${i}    device_name
        run keyword if    '${取值约定}'=='False'    Run Keyword And Continue On Failure    南向子设备数值型参数设置封装判断结果    pu    ${信号名称}    参数    整流器参数测试
        run keyword if    '${取值约定}'=='True'    Run Keyword And Continue On Failure    南向子设备有取值约定型参数设置封装判断结果    pu    ${信号名称}    参数    整流器参数测试
    END
