*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0011_扩展湿度高告警
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    实时告警刷新完成
    ${级别设置值}    获取web参数量    扩展湿度高
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    扩展湿度高    主要
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    扩展湿度高
    ${可设置范围}    获取web参数可设置范围    扩展湿度高阈值
    设置web参数量    扩展湿度高阈值    ${可设置范围}[0]
    ${扩展湿度高阈值}    获取web参数量    扩展湿度高阈值
    FOR    ${i}    IN RANGE    1    10    1
        ${目标值}    Evaluate    int(${扩展湿度高阈值})+int(${i})
        ${目标值}    Convert To String    ${目标值}
        设置子工具值    DMU_YD8779Y    all    只读    湿度    ${目标值}
        sleep    10
        ${WEB扩展湿度}    获取web实时数据    扩展湿度
        exit for loop if    ${WEB扩展湿度}>${扩展湿度高阈值}
    END
    wait until keyword succeeds    5m    1    判断告警存在    扩展湿度高
    ${告警级别取值约定dict}    获取web参数的取值约定    扩展湿度高
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    扩展湿度高    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    扩展湿度高
        ${级别设置值}    获取web参数量    扩展湿度高
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client'    主动告警上送判断2    power_sm    系统运行环境    扩展湿度高
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    扩展湿度高    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${级别设置值}
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    扩展湿度高    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    扩展湿度高
    ${扩展湿度高告警}    判断告警存在_带返回值    扩展湿度高
    should not be true    ${扩展湿度高告警}
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    扩展湿度高    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    Run Keywords    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
    ...    AND    设置web设备参数量为默认值    扩展湿度高阈值
    ...    AND    设置web设备参数量为默认值    扩展湿度高

power_sm_0011_扩展湿度低告警
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    实时告警刷新完成
    ${级别设置值}    获取web参数量    扩展湿度低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    扩展湿度低    主要
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    扩展湿度低
    ${可设置范围}    获取web参数可设置范围    扩展湿度低阈值
    设置web参数量    扩展湿度低阈值    ${可设置范围}[1]
    ${扩展湿度低阈值}    获取web参数量    扩展湿度低阈值
    FOR    ${i}    IN RANGE    1    10    1
        ${目标值}    Evaluate    int(${扩展湿度低阈值})-int(${i})
        ${目标值}    Convert To String    ${目标值}
        设置子工具值    DMU_YD8779Y    all    只读    湿度    ${目标值}
        sleep    10
        ${WEB扩展湿度}    获取web实时数据    扩展湿度
        exit for loop if    ${WEB扩展湿度}<${扩展湿度低阈值}
    END
    wait until keyword succeeds    5m    1    判断告警存在    扩展湿度低
    ${告警级别取值约定dict}    获取web参数的取值约定    扩展湿度低
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    扩展湿度低    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    扩展湿度低
        ${级别设置值}    获取web参数量    扩展湿度低
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client'    主动告警上送判断2    power_sm    系统运行环境    扩展湿度低
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    扩展湿度低    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${级别设置值}
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    扩展湿度低    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    扩展湿度低
    ${扩展湿度高告警}    判断告警存在_带返回值    扩展湿度低
    should not be true    ${扩展湿度高告警}
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    扩展湿度低    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    Run Keywords    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
    ...    AND    设置web设备参数量为默认值    扩展湿度低阈值
    ...    AND    设置web设备参数量为默认值    扩展湿度低

power_sm_0011_扩展湿度无效告警
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    实时告警刷新完成
    ${级别设置值}    获取web参数量    扩展湿度无效
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    扩展湿度无效    主要
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    扩展湿度无效
    设置子工具值    DMU_YD8779Y    all    只读    湿度    101
    wait until keyword succeeds    5m    1    判断告警存在    扩展湿度无效
    ${告警级别取值约定dict}    获取web参数的取值约定    扩展湿度无效
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    扩展湿度无效    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    扩展湿度无效
        ${级别设置值}    获取web参数量    扩展湿度无效
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client'    主动告警上送判断2    power_sm    系统运行环境    扩展湿度无效
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    扩展湿度无效    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${级别设置值}
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    扩展湿度无效    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    扩展湿度无效
    ${扩展湿度高告警}    判断告警存在_带返回值    扩展湿度无效
    should not be true    ${扩展湿度高告警}
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    扩展湿度无效    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    #无告警后查询判断
    wait until keyword succeeds    10    2    设置web设备参数量为默认值    扩展湿度无效
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    扩展湿度无效
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    扩展湿度无效    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    Run Keywords    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
    ...    AND    设置web设备参数量为默认值    扩展湿度无效
    ...    AND    设置子工具值    DMU_YD8779Y    all    只读    湿度    60
    ...    AND    sleep    3min

温湿度传感器通讯断告警
    连接CSU
    ${级别设置值}    获取web参数量    <<温湿度传感器通讯断告警~0x30001030010001>>
    run keyword if    '${级别设置值}'=='屏蔽'    wait until keyword succeeds    10    2    设置web参数量    <<温湿度传感器通讯断告警~0x30001030010001>>    严重
    控制子工具运行停止    DMU_YD8779Y    关闭
    ${告警级别取值约定dict}    获取web参数的取值约定    <<温湿度传感器通讯断告警~0x30001030010001>>
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    温湿度传感器通讯断告警    ${告警级别}
        ${级别设置值}    获取web参数量    温湿度传感器通讯断告警
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    温湿度传感器通讯状态    异常
        wait until keyword succeeds    5m    2    查询指定告警信息    温湿度传感器通讯断告警
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    温湿度传感器通讯断告警    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${级别设置值}
    END
    控制子工具运行停止    DMU_YD8779Y    开启
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    温湿度传感器通讯状态    正常
    wait until keyword succeeds    5m    1    判断告警不存在    温湿度传感器通讯断告警
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    温湿度传感器通讯断告警    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]
