*** Settings ***
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
批量获取数字量测试
    写入CSV文档    FB100B3数字量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=digital data    INDEX_DEVICE_CH=FBBMS
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除数字量信号}    ${排除列表}    1
    ${缺省值列表}   获取缺省值列表  ${列表1}    1
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    smartli       只读    ${缺省值列表}    2    FB100B3数字量获取测试
    ${缺省值列表}   获取缺省值列表  ${列表1}    2
    Run Keyword And Continue On Failure   南向子设备数字量获取值列表封装判断结果    smartli       只读    ${缺省值列表}    2    FB100B3数字量获取测试
    ${缺省值列表}   获取缺省值列表  ${列表1}    0
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    smartli       只读    ${缺省值列表}    2    FB100B3数字量获取测试
	


电池运行状态获取测试
    [Setup]    Run Keywords    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池配置    纯铅酸
    ...        AND    Wait Until Keyword Succeeds    1m    1    设置web参数量    铅酸类型    普通铅酸电池
    连接CSU
    #子设备工具模拟
    设置子工具值    smartli    all    只读    电池运行状态    1
    Wait Until Keyword Succeeds    20m    5    信号量数据值为    电池运行状态-1    放电管理
    Wait Until Keyword Succeeds    20m    5    信号量数据值为    电池运行状态-8    放电管理
    Wait Until Keyword Succeeds    20m    5    信号量数据值为    电池运行状态-${铁锂电池组数}    放电管理
    设置子工具值    smartli    all    只读    电池运行状态    2
    Wait Until Keyword Succeeds    20m    5    信号量数据值为    电池运行状态-1    在线非浮充
    Wait Until Keyword Succeeds    20m    5    信号量数据值为    电池运行状态-8    在线非浮充
    Wait Until Keyword Succeeds    20m    5    信号量数据值为    电池运行状态-${铁锂电池组数}    在线非浮充
    设置子工具值    smartli    all    只读    电池运行状态    3
    Wait Until Keyword Succeeds    20m    5    信号量数据值为    电池运行状态-1    离线
    Wait Until Keyword Succeeds    20m    5    信号量数据值为    电池运行状态-8    离线
    Wait Until Keyword Succeeds    20m    5    信号量数据值为    电池运行状态-${铁锂电池组数}    离线
    设置子工具值    smartli    all    只读    电池运行状态    0
    Wait Until Keyword Succeeds    20m    5    信号量数据值为    电池运行状态-1    充电
    Wait Until Keyword Succeeds    20m    5    信号量数据值为    电池运行状态-8    充电
    Wait Until Keyword Succeeds    20m    5    信号量数据值为    电池运行状态-${铁锂电池组数}    充电
    [Teardown]    配置电池为FB100B3

BMS工作状态获取测试
    连接CSU
    #子设备工具模拟
    控制子工具运行停止    smartli    开启
    Wait Until Keyword Succeeds    20m    5    信号量数据值为    BMS工作状态-1    正常
    Wait Until Keyword Succeeds    20m    5    信号量数据值为    BMS工作状态-8    正常
    Wait Until Keyword Succeeds    20m    5    信号量数据值为    BMS工作状态-${铁锂电池组数}    正常
    控制子工具运行停止    smartli    关闭
    Wait Until Keyword Succeeds    18m    5    信号量数据值为    BMS工作状态-1    通讯断
    Wait Until Keyword Succeeds    18m    5    信号量数据值为    BMS工作状态-8    通讯断
    Wait Until Keyword Succeeds    18m    5    信号量数据值为    BMS工作状态-${铁锂电池组数}    通讯断
    重新启动FB100B3
    设置子工具值    smartli    all    只读    电池充电过流保护    1
    Wait Until Keyword Succeeds    20m    5    信号量数据值为    电池充电过流保护状态-1    异常
    Wait Until Keyword Succeeds    20m    5    信号量数据值为    电池充电过流保护状态-8    异常
    Wait Until Keyword Succeeds    20m    5    信号量数据值为    电池充电过流保护状态-${铁锂电池组数}    异常
    # ${级别设置值}    获取web参数量    电池充电过流保护
    # run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    电池充电过流保护    严重
    # wait until keyword succeeds    20m    5    查询指定告警信息    电池充电过流保护
    # Wait Until Keyword Succeeds    20m    5    信号量数据值为    BMS工作状态-1    告警
    # Wait Until Keyword Succeeds    20m    5    信号量数据值为    BMS工作状态-8    告警
    # Wait Until Keyword Succeeds    20m    5    信号量数据值为    BMS工作状态-${铁锂电池组数}    告警
    # 设置子工具值    smartli    all    只读    电池充电过流保护    0
    # Wait Until Keyword Succeeds    20m    5    信号量数据值为    电池充电过流保护状态-1    正常
    # Wait Until Keyword Succeeds    20m    5    信号量数据值为    电池充电过流保护状态-8    正常
    # Wait Until Keyword Succeeds    20m    5    信号量数据值为    电池充电过流保护状态-${铁锂电池组数}    正常
    # wait until keyword succeeds    20m    5    查询指定告警信息不为    电池充电过流保护
    # Wait Until Keyword Succeeds    20m    5    信号量数据值为    BMS工作状态-1    正常
    # Wait Until Keyword Succeeds    20m    5    信号量数据值为    BMS工作状态-8    正常
    # Wait Until Keyword Succeeds    20m    5    信号量数据值为    BMS工作状态-${铁锂电池组数}    正常
    [Teardown]    重新启动FB100B3
