*** Settings ***
Documentation     统一监控协议 的协议子表，目前只支持传送，温度低/高、湿度低/高，四个告警
...               （其他告警，可选属性软件适用性，要么为'否，要么为' ' ）
Test Setup        Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
uniform_0006_环境温度低√
    [Documentation]    低阈值范围：-27~23，默认-5；过低阈值范围：-30~20，默认-8
    [Setup]    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接    # run keywords | 测试用例前置条件 | AND | Run Keyword if | '${SSH}'=='True' | 统一监控协议_创建加密链路连接
    实时告警刷新完成
    ${可设置范围}    获取web参数可设置范围    环境温度低阈值
    设置web参数量    环境温度低阈值    ${可设置范围}[1]
    ${环境温度}    Wait Until Keyword Succeeds    5m    1    获取web实时数据    环境温度
    FOR    ${调节步长}    IN RANGE    -5    -50    -5
        ${零点}    evaluate    str(${调节步长})
        Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}     ${零点}    1    系统运行环境    环境温度
        sleep    3
        ${环境温度获取}    获取web实时数据    环境温度
        ${环境温度设置值}    获取web参数量    环境温度低阈值
        exit for loop if    ${环境温度获取}<${ 环境温度设置值}
    END
    设置web参数量    环境温度低    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    环境温度低
    ${环境温度低告警}    判断告警存在_带返回值    环境温度低
    should not be true    ${环境温度低告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    10    1    设置web参数量    环境温度低    ${告警级别}
        ${环境温度获取}    获取web实时数据    环境温度
        ${环境温度设置值}    获取web参数量    环境温度低阈值
        连接CSU
        wait until keyword succeeds    2m    1    查询指定告警信息    环境温度低
        sleep    5
    #统一监控协议查询告警存在
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    环境告警量    环境温度低
        should be true    ${统一监控协议告警结果}
    #查询web端级别
        ${环境温度低告警级别}    获取web告警属性    环境温度低    告警级别
        should be equal    ${告警级别}    ${环境温度低告警级别}
    END
    [Teardown]    Run keywords    环境温度参数恢复默认值
    ...    AND    设置通道配置    ${plat.envtemp}     0    1    系统运行环境    环境温度
    ...    AND    设置web参数量    环境温度低    次要
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0008_环境温度过低√
    [Documentation]    低阈值范围：-27~23，默认-5；过低阈值范围：-30~20，默认-8
    [Setup]    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接    # run keywords | 测试用例前置条件 | AND | Run Keyword if | '${SSH}'=='True' | 统一监控协议_创建加密链路连接
    实时告警刷新完成
    ${可设置范围}    获取web参数可设置范围    环境温度低阈值
    设置web参数量    环境温度低阈值    ${可设置范围}[1]
    ${可设置范围}    获取web参数可设置范围    环境温度过低阈值
    设置web参数量    环境温度过低阈值    ${可设置范围}[1]
    ${环境温度}    Wait Until Keyword Succeeds    5m    1    获取web实时数据    环境温度
    FOR    ${调节步长}    IN RANGE    -2    -50    -2
        ${零点}    evaluate    str(${调节步长})
        Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}     ${零点}    1    系统运行环境    环境温度
        sleep    3
        ${环境温度获取}    获取web实时数据    环境温度
        ${环境温度设置值}    获取web参数量    环境温度过低阈值
        exit for loop if    ${环境温度获取}<${ 环境温度设置值}
    END
    设置web参数量    环境温度过低    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    环境温度过低
    ${环境温度过低告警}    判断告警存在_带返回值    环境温度过低
    should not be true    ${环境温度过低告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        设置web参数量    环境温度过低    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    环境温度过低
        sleep    5
    #统一监控协议查询告警存在
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    环境告警量    环境温度过低
        should be true    ${统一监控协议告警结果}
    #查询web端级别
        ${环境温度过低告警级别}    获取web告警属性    环境温度过低    告警级别
        should be equal    ${告警级别}    ${环境温度过低告警级别}
    END
    [Teardown]    Run keywords    环境温度参数恢复默认值
    ...    AND    设置通道配置    ${plat.envtemp}     0    1    系统运行环境    环境温度
    ...    AND    设置web参数量    环境温度过低    次要
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0010_环境温度高√
    [Documentation]    高阈值范围：30-60，默认55；过高阈值范围：33-63，默认58。高比过高低3°
    [Setup]    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接    # run keywords | 测试用例前置条件 | AND | Run Keyword if | '${SSH}'=='True' | 统一监控协议_创建加密链路连接
    实时告警刷新完成
    Comment    Wait Until Keyword Succeeds    5m    1    设置web设备参数量    系统运行环境    温度传感器类型    值=1    #默认1
    Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度过高阈值    58    #默认58，min33
    Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度高阈值    30    #默认55，min30
    ${环境温度}    Wait Until Keyword Succeeds    5m    1    获取web实时数据    环境温度
    FOR    ${调节步长}    IN RANGE    2    40    2
        ${零点}    evaluate    str(${调节步长})
        Wait Until Keyword Succeeds    30    1    通道配置_AI    ${plat.envtemp}     ${零点}    1    系统运行环境    环境温度
        sleep    3
        ${环境温度获取}    获取web实时数据    环境温度
        ${环境温度设置值}    获取web参数量    环境温度高阈值
        exit for loop if    ${环境温度获取}>${ 环境温度设置值}
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度高    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    环境温度高
    ${环境温度高告警}    判断告警存在_带返回值    环境温度高
    should not be true    ${环境温度高告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度高    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    环境温度高
        sleep    5
    #统一监控协议查询告警存在
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    环境告警量    环境温度高
        should be true    ${统一监控协议告警结果}
    #查询web端级别
        ${环境温度高告警级别}    获取web告警属性    环境温度高    告警级别
        should be equal    '${告警级别}'    '${环境温度高告警级别}'
    END
    [Teardown]    Run keywords    环境温度参数恢复默认值
    ...    AND    设置通道配置    ${plat.envtemp}     0    1    系统运行环境    环境温度
    ...    AND    设置web参数量    环境温度高    次要
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0012_环境温度过高√
    [Documentation]    高阈值范围：30-60，默认55；过高阈值范围：33-63，默认58。高比过高低3°
    [Setup]    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接    # run keywords | 测试用例前置条件 | AND | Run Keyword if | '${SSH}'=='True' | 统一监控协议_创建加密链路连接
    实时告警刷新完成
    ${可设置范围}    获取web参数可设置范围    环境温度高阈值
    设置web参数量    环境温度高阈值    ${可设置范围}[0]
    ${可设置范围}    获取web参数可设置范围    环境温度过高阈值
    设置web参数量    环境温度过高阈值    ${可设置范围}[0]
    ${环境温度}    Wait Until Keyword Succeeds    10    1    获取web实时数据    环境温度
    FOR    ${调节步长}    IN RANGE    2    40    2
        ${零点}    evaluate    str(${调节步长})
        Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}     ${零点}    1    系统运行环境    环境温度
        sleep    3
        ${环境温度获取}    获取web实时数据    环境温度
        ${环境温度设置值}    获取web参数量    环境温度过高阈值
        exit for loop if    ${环境温度获取}>${ 环境温度设置值}
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度过高    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    环境温度过高
    ${环境温度高告警}    判断告警存在_带返回值    环境温度过高
    should not be true    ${环境温度高告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        Comment    ${告警级别设置}    evaluate    str(${告警级别})
        Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度过高    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    环境温度过高
        sleep    5
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    环境告警量    环境温度过高
        should be true    ${统一监控协议告警结果}
    #查询web端级别
        ${环境温度高告警级别}    获取web告警属性    环境温度过高    告警级别
        should be equal    '${告警级别}'    '${环境温度高告警级别}'
    END
    [Teardown]    Run keywords    环境温度参数恢复默认值
    ...    AND    设置通道配置    ${plat.envtemp}     0    1    系统运行环境    环境温度
    ...    AND    设置web参数量    环境温度过高    次要
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0014_环境温度无效
    [Documentation]    告警级别默认屏蔽
    ...
    ...    统一监控协议，不支持传送 \ 环境温度无效告警
    设置通道无效值/恢复通道原始值    ${plat.envtemp}    1
    #不接入传感器
    实时告警刷新完成
    设置web参数量    环境温度无效    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    环境温度无效
    ${环境温度无效告警}    判断告警存在_带返回值    环境温度无效
    should not be true    ${环境温度无效告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度无效    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    环境温度无效
        sleep    5
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    环境告警量    环境温度无效
        should be true    ${统一监控协议告警结果}
        ${环境温度无效告警级别}    获取web告警属性    环境温度无效    告警级别
        should be equal    ${告警级别}    ${环境温度无效告警级别}
    END
    设置通道无效值/恢复通道原始值    ${plat.envtemp}    0
    [Teardown]    Run keywords    设置web参数量    环境温度无效    次要
    ...    AND    设置通道无效值/恢复通道原始值    ${plat.envtemp}    0

uniform_0016_环境湿度低
    [Documentation]    目前系统没有接入湿度传感器
    [Setup]    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    实时告警刷新完成
    ${可设置范围}    获取web参数可设置范围    环境湿度低阈值
    设置web参数量    环境湿度低阈值    ${可设置范围}[1]
    ${环境湿度}    Wait Until Keyword Succeeds    5m    1    获取web实时数据    环境湿度
    FOR    ${调节步长}    IN RANGE    -5    -50    -5
        ${零点}    evaluate    str(${调节步长})
        Comment    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    ${零点}    1    系统运行环境    环境温度
        Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.humity}    ${零点}    1    系统运行环境    环境湿度
        Comment    sleep    3
        ${环境湿度获取}    获取web实时数据    环境湿度
        ${环境湿度设置值}    获取web参数量    环境湿度低阈值
        exit for loop if    ${环境湿度获取} <${环境湿度设置值}
    END
    设置web参数量    环境湿度低    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    环境湿度低
    ${环境温度低告警}    判断告警存在_带返回值    环境温度低
    should not be true    ${环境温度低告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    10    1    设置web参数量    环境湿度低    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    环境湿度低
        sleep    5
    #统一监控协议查询告警存在
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    环境告警量    环境湿度低
        should be true    ${统一监控协议告警结果}
    #查询web端级别
        ${环境湿度低告警级别}    获取web告警属性    环境湿度低    告警级别
        should be equal    ${告警级别}    ${环境湿度低告警级别}
    END
    [Teardown]    Run keywords    设置web参数量    环境湿度低阈值    10
    ...    AND    设置通道配置    ${plat.humity}    0    1    系统运行环境    环境湿度
    ...    AND    设置web参数量    环境湿度低    次要
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0018_环境湿度高
    [Documentation]    目前系统没有接入湿度传感器
    [Setup]    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    实时告警刷新完成
    ${可设置范围}    获取web参数可设置范围    环境湿度高阈值
    设置web参数量    环境湿度高阈值    ${可设置范围}[0]
    ${环境湿度}    Wait Until Keyword Succeeds    5m    1    获取web实时数据    环境湿度
    FOR    ${调节步长}    IN RANGE    2    40    2
        ${零点}    evaluate    str(${调节步长})
        Comment    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    ${零点}    1    系统运行环境    环境温度
        Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.humity}    ${零点}    1    系统运行环境    环境湿度
        Comment    sleep    3
        ${环境湿度获取}    获取web实时数据    环境湿度
        ${环境湿度设置值}    获取web参数量    环境湿度高阈值
        exit for loop if    ${环境湿度获取}>${环境湿度设置值}
    END
    设置web参数量    环境湿度高    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    环境湿度高
    ${环境温度低告警}    判断告警存在_带返回值    环境湿度高
    should not be true    ${环境温度低告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    10    1    设置web参数量    环境湿度高    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    环境湿度高
        sleep    5
    #统一监控协议查询告警存在
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    环境告警量    环境湿度高
        should be true    ${统一监控协议告警结果}
    #查询web端级别
        ${环境湿度高告警级别}    获取web告警属性    环境湿度高    告警级别
        should be equal    ${告警级别}    ${环境湿度高告警级别}
    END
    [Teardown]    Run keywords    设置web参数量    环境湿度高阈值    90
    ...    AND    设置通道配置    ${plat.humity}    0    1    系统运行环境    环境湿度
    ...    AND    设置web参数量    环境湿度高    次要
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0020_环境湿度无效
    [Setup]    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    #不接入传感器
    设置通道无效值/恢复通道原始值    ${plat.humity}    1
    实时告警刷新完成
    #获取告警级别
    ${级别设置值}    获取web参数量    环境湿度无效
    run keyword if    '${级别设置值}'!='屏蔽'    设置web参数量    环境湿度无效    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    环境湿度无效
    ${环境湿度无效告警}    判断告警存在_带返回值    环境湿度无效
    should not be true    ${环境湿度无效告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    10    1    设置web参数量    环境湿度无效    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    环境湿度无效
        sleep    5
    #统一监控协议查询告警存在
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    环境告警量    环境湿度无效
        should be true    ${统一监控协议告警结果}
    #查询web端级别
        ${环境湿度告警级别}    获取web告警属性    环境湿度无效    告警级别
        should be equal    ${告警级别}    ${环境湿度告警级别}
    END
    设置通道无效值/恢复通道原始值    ${plat.humity}    0
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接
    ...    AND    设置web参数量    环境湿度无效    屏蔽
    ...    AND    设置通道无效值/恢复通道原始值    ${plat.humity}    0

uniform_0022_烟雾告警
    [Tags]    T1-1
    [Setup]
    实时告警刷新完成
    设置web参数量    烟雾告警    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    烟雾告警
    ${烟雾告警}    判断告警存在_带返回值    烟雾告警
    should not be true    ${烟雾告警}
    模拟数字量告警    烟雾告警    ON
    sleep    10
    ${烟雾状态}    获取干接点状态    ${plat.smoke}
    should be true    ${烟雾状态}==1    \    #0为正常（闭合），1为断开（断开）
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    10    1    设置web参数量    烟雾告警    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    烟雾告警
        sleep    3
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    环境告警量    烟雾告警
        should be true    ${统一监控协议告警结果}
        ${sm告警级别}    获取web告警属性    烟雾告警    告警级别
        should be equal    ${告警级别}    ${sm告警级别}
    END
    模拟数字量告警    烟雾告警    OFF
    wait until keyword succeeds    1m    1    判断告警不存在    烟雾告警
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    环境告警量    烟雾告警
    should not be true    ${统一监控协议告警结果}
    [Teardown]    run keywords    设置web设备参数量为默认值    烟雾告警
    ...    AND    模拟数字量告警    烟雾告警    OFF

uniform_0024_水淹告警
    [Documentation]    将水淹（X9）测试前先短接
    ...    ==20190327-F：水淹状态与数据字典相反，数据字典要求为0为正常，1为异常；实际水淹告警时，获取的状态为0
    ...    xhf0627:水淹状态应从配置文件中读取，默认状态是0，短接后状态为1.
    [Tags]    T1-1
    [Setup]
    实时告警刷新完成
    Wait Until Keyword Succeeds    30    1    设置web参数量    水淹告警    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    水淹告警
    ${水淹告警}    判断告警存在_带返回值    水淹告警
    should not be true    ${水淹告警}
    ${水淹状态}    获取干接点状态    ${plat.Water}
    should be true    ${水淹状态}==0    #0为正常，1为异常
    模拟数字量告警    水淹告警    ON
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    水淹告警    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    水淹告警
        sleep    3
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    环境告警量    水淹告警
        should be true    ${统一监控协议告警结果}
        ${sm告警级别}    获取web告警属性    水淹告警    告警级别
        should be equal    ${告警级别}    ${sm告警级别}
    END
    模拟数字量告警    水淹告警    OFF
    wait until keyword succeeds    1m    1    判断告警不存在    水淹告警
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    环境告警量    水淹告警
    should not be true    ${统一监控协议告警结果}
    [Teardown]    run keywords    设置web设备参数量为默认值    烟雾告警
    ...    AND    模拟数字量告警    水淹告警    OFF

uniform_0026_门磁告警
    [Tags]    T1-1
    [Setup]
    实时告警刷新完成
    设置web参数量    门磁告警    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    门磁告警
    ${门磁告警}    判断告警存在_带返回值    门磁告警
    should not be true    ${门磁告警}
    模拟数字量告警    门磁告警    ON
    sleep    10
    ${门磁状态}    获取干接点状态    ${plat.MagneticDoor}
    should be true    ${门磁状态}==1    \    #0为正常（门磁闭合），1为断开（门磁断开）
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        设置web参数量    门磁告警    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    门磁告警
        sleep    3
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    环境告警量    门磁告警
        should be true    ${统一监控协议告警结果}
        ${sm告警级别}    获取web告警属性    门磁告警    告警级别
        should be equal    ${告警级别}    ${sm告警级别}
    END
    模拟数字量告警    门磁告警    OFF
    wait until keyword succeeds    1m    1    判断告警不存在    门磁告警
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    环境告警量    门磁告警
    should not be true    ${统一监控协议告警结果}
    [Teardown]    run keywords    设置web设备参数量为默认值    烟雾告警
    ...    AND    模拟数字量告警    门磁告警    OFF

uniform_0028_门禁告警
    [Tags]    3
    Log    协议不支持
