*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
web修改校准参数导入测试
    [Tags]    PMSA-NTest
    连接CSU
    强制恢复默认值并重新登录
    ${用户参数文件}    导出参数文件    ${datapassword}
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    10    2    电池_1    电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.battcurr1}    8    3    直流配电    电池分流器电流_1
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.undefined}    9    4    无    无
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.battvolt1}    7    2    电池_1    电池电压
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.voltage}    5    2    直流配电    直流电压
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.humity}    6    2    系统运行环境    环境湿度
    Comment    Wait Until Keyword Succeeds    30    1    设置通道配置    UIB_X3_DI1    无    无    闭合
    Comment    Wait Until Keyword Succeeds    30    1    设置通道配置    DO1    无    无    闭合
    Wait Until Keyword Succeeds    30    2    设置web参数量    CPU利用率高阈值    10    #下限
    ${CPU占用率高阈值获取}    获取web参数量    CPU利用率高阈值
    should be true    ${CPU占用率高阈值获取}==10
    Wait Until Keyword Succeeds    30    2    设置web参数量    系统过载告警阈值    10    #下限
    ${系统过载告警阈值获取}    获取web参数量    系统过载告警阈值
    should be true    ${系统过载告警阈值获取}==10
    压缩文件夹    ${用户参数文件}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${用户参数文件}.zip    ${datapassword}
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    ${CPU利用率高阈值获取}    获取web参数量    CPU利用率高阈值
    ${缺省值}    获取web参数上下限范围    CPU利用率高阈值
    should be true    ${CPU利用率高阈值获取}==${缺省值}[0]
    ${系统过载告警阈值获取}    获取web参数量    系统过载告警阈值
    ${缺省值}    获取web参数上下限范围    系统过载告警阈值
    should be true    ${系统过载告警阈值获取}==${缺省值}[0]
    #需要继承的校准参数
    ${T1通道}    获取通道配置    ${plat.batttemp1}
    should be true    ${T1通道}[1]==10
    should be true    ${T1通道}[2]==2
    ${IB1通道}    获取通道配置    ${plat.battcurr1}
    should be true    ${IB1通道}[1]==8
    should be true    ${IB1通道}[2]==3
    ${IL1通道}    获取通道配置    ${plat.undefined}
    should be true    ${IL1通道}[1]==9
    should be true    ${IL1通道}[2]==4
    ${VB1通道}    获取通道配置    ${plat.battvolt1}
    should be true    ${VB1通道}[1]==7
    should be true    ${VB1通道}[2]==2
    ${VIN通道}    获取通道配置    ${plat.voltage}
    should be true    ${VIN通道}[1]==5
    should be true    ${VIN通道}[2]==2
    ${HUM通道}    获取通道配置    ${plat.humity}
    should be true    ${HUM通道}[1]==6
    should be true    ${HUM通道}[2]==2
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    0    1    电池_1    电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.battcurr1}    0    1    直流配电    电池分流器电流_1
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.undefined}    0    1    无    无
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.battvolt1}    0    1    电池_1    电池电压
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.voltage}    0    1    直流配电    直流电压
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.humity}    0    1    系统运行环境    环境湿度
    删除文件夹以及文件夹下所有文件    download//${用户参数文件}
    删除文件    download//${用户参数文件}.zip
    [Teardown]    强制恢复默认值并重新登录

文件中修改校准参数导入测试
    [Tags]    PMSA-NTest
    连接CSU
    强制恢复默认值并重新登录
    ${T1通道}    获取通道配置    ${plat.batttemp1}
    ${IB1通道}    获取通道配置    ${plat.battcurr1}
    ${IL1通道}    获取通道配置    ${plat.undefined}
    ${VB1通道}    获取通道配置    ${plat.battvolt1}
    ${VIN通道}    获取通道配置    ${plat.voltage}
    ${HUM通道}    获取通道配置    ${plat.humity}
    ${CPU利用率高阈值获取}    获取web参数量    CPU利用率高阈值
    ${系统过载告警阈值获取}    获取web参数量    系统过载告警阈值
    ${用户参数文件}    导出参数文件    ${datapassword}
    &{dict1}    create dictionary    id=plat.batt1temp    para_name=Offset    para_val=4
    &{dict2}    create dictionary    id=plat.batt1temp    para_name=Slope    para_val=2
    &{dict3}    create dictionary    id=plat.battcurr1    para_name=Offset    para_val=1
    &{dict4}    create dictionary    id=plat.battcurr1    para_name=Slope    para_val=3
    &{dict5}    create dictionary    id=plat.loadcurr1    para_name=Offset    para_val=5
    &{dict6}    create dictionary    id=plat.loadcurr1    para_name=Slope    para_val=2.5
    &{dict7}    create dictionary    id=plat.battvolt1    para_name=Offset    para_val=6
    &{dict8}    create dictionary    id=plat.battvolt1    para_name=Slope    para_val=4
    &{dict9}    create dictionary    id=plat.loadvolt    para_name=Offset    para_val=8
    &{dict10}    create dictionary    id=plat.loadvolt    para_name=Slope    para_val=7
    &{dict11}    create dictionary    id=plat.humity    para_name=Offset    para_val=9
    &{dict12}    create dictionary    id=plat.humity    para_name=Slope    para_val=3.5
    @{配置列表}    create list    ${dict1}    ${dict2}    ${dict3}    ${dict4}    ${dict5}    ${dict6}    ${dict7}    ${dict8}    ${dict9}    ${dict10}    ${dict11}    ${dict12}
    修改导出配置文件    ${配置列表}    ${用户参数文件}/config.xml
    修改导出参数值    ${用户参数文件}/parameter.csv    CPU利用率高阈值=84    系统过载告警阈值=90
    压缩文件夹    ${用户参数文件}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${用户参数文件}.zip    ${datapassword}
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    ${CPU利用率高阈值获取}    获取web参数量    CPU利用率高阈值
    should be true    ${CPU利用率高阈值获取}==84
    ${系统过载告警阈值获取}    获取web参数量    系统过载告警阈值
    should be true    ${系统过载告警阈值获取}==90
    #需要继承的参数
    ${T1通道}    获取通道配置    ${plat.batttemp1}
    should be true    ${T1通道}[1]==0
    should be true    ${T1通道}[2]==1
    ${IB1通道}    获取通道配置    ${plat.battcurr1}
    should be true    ${IB1通道}[1]==0
    should be true    ${IB1通道}[2]==1
    ${IL1通道}    获取通道配置    ${plat.undefined}
    should be true    ${IL1通道}[1]==0
    should be true    ${IL1通道}[2]==1
    ${VB1通道}    获取通道配置    ${plat.battvolt1}
    should be true    ${VB1通道}[1]==0
    should be true    ${VB1通道}[2]==1
    ${VIN通道}    获取通道配置    ${plat.voltage}
    should be true    ${VIN通道}[1]==0
    should be true    ${VIN通道}[2]==1
    ${HUM通道}    获取通道配置    ${plat.humity}
    should be true    ${HUM通道}[1]==0
    should be true    ${HUM通道}[2]==1
    删除文件夹以及文件夹下所有文件    download//${用户参数文件}
    删除文件    download//${用户参数文件}.zip
