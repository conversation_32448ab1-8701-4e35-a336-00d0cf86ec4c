*** Settings ***
Suite Setup       主动告警测试前置条件    ${CSU_role}
Suite Teardown    设置web设备参数量为默认值    #run keywords | 设置web设备参数量为默认值 | CSU主动告警使能
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0002_批量获取状态量和SMR告警测试
    [Documentation]    power_sm字典中只显示SMR告警/SMR故障/SMR通讯中断
    ...    所以此处无需从power_sm中再查找数据，以data_dict得到的为主
    写入CSV文档    整流器数字量和告警量获取测试    信号名称    信号值    结果    备注
    连接CSU
    ${级别设置值}    获取web参数量    整流器告警
    run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    整流器告警    严重
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=整流器
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除告警量信号}    ${排除列表}    1    ${模拟整流器地址}    1
    Comment    ${待测数据长度}    Get Length    ${列表1}
    Comment    ${待测}    Evaluate    ${待测数据长度}-1
    Comment    ${待测索引}    Evaluate    random.randint(0,${待测})    random
    #power_sm字典中只显示SMR告警/SMR故障/SMR通讯中断
    #所以此处无需从power_sm中查找数据，以data_dict得到的为主
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${实时告警中的设备名称}    主动告警_组合实时告警中的设备名称    整流器    ${信号名称}
        Run Keyword And Continue On Failure    整流器或PU告警量产生封装判断结果    power_sm    SMR4000    ${信号名称}    数字量    ${告警产生}    ${模拟整流器地址}    整流器告警    整流器数字量和告警量获取测试    ${实时告警中的设备名称}    null    null    null    null    null
        Run Keyword And Continue On Failure    整流器或PU告警量恢复封装判断结果    power_sm    SMR4000    ${信号名称}    数字量    ${告警恢复}    ${模拟整流器地址}    整流器告警    整流器数字量和告警量获取测试    整流器    null    null    null    null    null
    END

power_sm_0003_批量获取状态量和SMR故障测试
    [Documentation]    power_sm字典中只显示SMR告警/SMR故障/SMR通讯中断
    ...    所以此处无需从power_sm中再查找数据，以data_dict得到的为主
    写入CSV文档    整流器数字量和故障量获取测试    信号名称    信号值    结果    备注
    连接CSU
    ${级别设置值}    获取web参数量    整流器故障
    run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    整流器故障    严重
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=整流器
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除故障量信号}    ${排除列表}    1    ${模拟整流器地址}    1
    Comment    ${待测数据长度}    Get Length    ${列表1}
    Comment    ${待测}    Evaluate    ${待测数据长度}-1
    Comment    ${待测索引}    Evaluate    random.randint(0,${待测})    random
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${实时告警中的设备名称}    主动告警_组合实时告警中的设备名称    整流器    ${信号名称}
        Run Keyword And Continue On Failure    整流器或PU故障量产生封装判断结果    power_sm    SMR4000    ${信号名称}    数字量    ${告警产生}    ${模拟整流器地址}    整流器故障    整流器数字量和故障量获取测试    ${实时告警中的设备名称}    null    null    null    null    null
        ...    null
        Run Keyword And Continue On Failure    整流器或PU故障量恢复封装判断结果    power_sm    SMR4000    ${信号名称}    数字量    ${告警恢复}    ${模拟整流器地址}    整流器故障    整流器数字量和故障量获取测试    整流器    null    null    null    null    null
        ...    null
    END

批量获取SMR内部告警
    写入CSV文档    整流器内部告警获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=整流器
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器外部告警}    ${排除列表}    1    ${模拟整流器地址}    1
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    整流器    alarm    ${整流器排除数字量信号}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    ${待测数据长度}    Get Length    ${power_sm待测}
    ${number}    Set Variable    1
    FOR    ${i}    IN    @{power_sm待测}
        Log    正在验证第${number}个数据，一共${待测数据长度}个数据
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${实时告警中的设备名称}    主动告警_组合实时告警中的设备名称    整流器    ${信号名称}
        Run Keyword And Continue On Failure    整流器或PU内部告警产生封装判断结果    power_sm    SMR4000    ${信号名称}    数字量    ${告警产生}    ${模拟整流器地址}    整流器内部告警获取测试    ${实时告警中的设备名称}    null    null    null    null    null
        Run Keyword And Continue On Failure    整流器或PU内部告警恢复封装判断结果    power_sm    SMR4000    ${信号名称}    数字量    ${告警恢复}    ${模拟整流器地址}    整流器内部告警获取测试    ${实时告警中的设备名称}    null    null    null    null    null
        ${number}    Evaluate    ${number}+1
    END

power_sm_0006_整流器风扇故障
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    ${级别设置值}    获取web参数量    整流器风扇故障
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    整流器风扇故障    严重
    ${告警级别取值约定dict}    获取web参数的取值约定    整流器风扇故障
    设置子工具值    SMR4000    all    数字量    整流器风扇故障状态    1
    @{SMR序号随机list}    从1-15个数中随机选n个不重复的单体    ${北向协议SMR最大数}    3    ${模拟整流器地址}
    FOR    ${SMR序号}    IN    @{SMR序号随机list}
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    整流器风扇故障状态-${SMR序号}    异常
        wait until keyword succeeds    2m    1    判断告警存在    整流器风扇故障-${SMR序号}
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    整流器风扇故障-${SMR序号}    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${级别设置值}
    END
    设置子工具值    SMR4000    all    数字量    整流器风扇故障状态    0
    FOR    ${SMR序号}    IN    @{SMR序号随机list}
        Wait Until Keyword Succeeds    5m    1    信号量数据值为    整流器风扇故障状态-${SMR序号}    正常
        wait until keyword succeeds    2m    1    判断告警不存在    整流器风扇故障-${SMR序号}
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    整流器风扇故障-${SMR序号}    ${SSH}
        should not be true    ${power_sm告警存在}[0]
    END
    wait until keyword succeeds    30m    2    查询指定告警信息不为    整流器风扇故障
    Comment    整流器测试结束条件
    [Teardown]    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0004_整流器通讯中断
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    设置子工具个数    SMR4000    3
    ${级别设置值}    获取web参数量    整流器通讯中断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    整流器通讯中断    严重
    ${告警级别取值约定dict}    获取web参数的取值约定    整流器通讯中断
    Log    ${测试环境真实整流个数}
    Comment    ${模拟一个时的起始号}    evaluate    ${模拟整流器开始地址}+1
    ${整流中断个数}    Evaluate    ${北向协议SMR最大数}-3-${测试环境真实整流个数}
    ${中断起始整流器地址}    Evaluate    ${测试环境真实整流个数}+3+1
    @{SMR序号随机list}    从1-15个数中随机选n个不重复的单体    ${北向协议SMR最大数}    3    ${中断起始整流器地址}
    FOR    ${SMR序号}    IN    @{SMR序号随机list}
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    整流器通讯状态-${SMR序号}    异常
        wait until keyword succeeds    2m    1    判断告警存在    整流器通讯中断-${SMR序号}
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    整流器通讯中断-${SMR序号}    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${级别设置值}
    END
    wait until keyword succeeds    5m    2    查询指定告警信息    整流器通讯中断
    wait until keyword succeeds    30m    60    获取指定告警数量大于    整流器通讯中断    ${整流中断个数}
    ${SMR通信恢复}    evaluate    str(${北向协议SMR最大数}-${测试环境真实整流个数})
    设置子工具个数    SMR4000    ${SMR通信恢复}
    FOR    ${SMR序号}    IN    @{SMR序号随机list}
        Wait Until Keyword Succeeds    5m    1    信号量数据值为    整流器通讯状态-${SMR序号}    正常
        wait until keyword succeeds    2m    1    判断告警不存在    整流器通讯中断-${SMR序号}
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    整流器通讯中断-${SMR序号}    ${SSH}
        should not be true    ${power_sm告警存在}[0]
    END
    wait until keyword succeeds    30m    2    查询指定告警信息不为    整流器通讯中断
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
    ...    AND    设置web设备参数量为默认值    整流器通讯中断
