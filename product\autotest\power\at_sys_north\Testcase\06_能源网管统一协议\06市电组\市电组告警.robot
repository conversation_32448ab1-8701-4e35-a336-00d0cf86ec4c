*** Settings ***
Suite Setup       Run Keywords   主动告警测试前置条件    ${CSU_role}
...    AND    测试用例前置条件
Suite Teardown    Run Keywords   设置web设备参数量为默认值    CSU主动告警使能
...    AND    测试用例后置条件
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Default Tags      T1-1
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0002__交流限功率预警
    [Setup]    run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    电池管理初始化
    Wait Until Keyword Succeeds    10    2    设置web参数量    市电额定有功功率    2.1
    Wait Until Keyword Succeeds    10    2    设置web参数量    市电降额系数    85
    ${可设置范围}    获取web参数可设置范围    交流输入限功率预警阈值
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流输入限功率预警阈值    ${可设置范围}[0]
    ${设置值}    获取web参数量    交流输入限功率预警阈值
    ${电压}    获取web实时数据    直流电压
    run keyword if    ${电压}>54    向下调节电池电压    53.5
    run keyword if    ${电压}<52    向上调节电池电压    53.5
    缓慢设置负载电压电流    ${电压}    26
    打开负载输出
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流输入限功率预警    屏蔽
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    交流输入限功率预警
    ${告警级别取值约定dict}    获取web参数的取值约定    交流输入限功率告警
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        wait until keyword succeeds    1m    1    设置web参数量    交流输入限功率预警    ${告警级别设置}
        sleep   5
        wait until keyword succeeds    15m    1    查询指定告警信息    交流输入限功率预警
        ${告警级别}    获取web告警属性    交流输入限功率预警    告警级别
        should be equal    ${告警级别设置}    ${告警级别}
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    交流输入限功率预警    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    设置web参数量    交流输入限功率预警    严重
    关闭负载输出
    Wait Until Keyword Succeeds    10    2    设置web参数量    市电额定有功功率    15
    设置WEB设备参数量为默认值    交流输入限功率预警阈值
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流输入限功率预警
    ${power_Sm告警存在}    判断power_sm是否存在单个实时告警    交流输入限功率预警    ${SSH}
    should not be true    ${power_Sm告警存在}[0]
    [Teardown]    run keywords    设置web参数量    交流输入限功率预警    严重
    ...    AND    关闭负载输出
    ...    AND    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接

power_sm_0003__交流限功率告警
    [Setup]    run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    Comment    电池管理初始化
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    10    2    设置web参数量    市电额定有功功率    2.1
    Wait Until Keyword Succeeds    10    2    设置web参数量    市电降额系数    85
    ${可设置范围}    获取web参数可设置范围    交流输入限功率告警阈值
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流输入限功率告警阈值    ${可设置范围}[0]
    ${设置值}    获取web参数量    交流输入限功率告警阈值
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    80
    打开负载输出
    wait until keyword succeeds    5m    1    查询指定告警信息    交流输入限功率告警
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流输入限功率告警    屏蔽
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    交流输入限功率告警
    ${告警级别取值约定dict}    获取web参数的取值约定    交流输入限功率告警
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        wait until keyword succeeds    1m    1    设置web参数量    交流输入限功率告警    ${告警级别设置}
        sleep   5
        wait until keyword succeeds    1m    1    查询指定告警信息    交流输入限功率告警
        ${交流电压过高告警级别}    获取web告警属性    交流输入限功率告警    告警级别
        should be equal    ${告警级别设置}    ${交流电压过高告警级别}
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    交流输入限功率告警    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    设置WEB设备参数量为默认值    交流输入限功率告警阈值
    Wait Until Keyword Succeeds    10    2    设置web参数量    市电额定有功功率    0
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流输入限功率告警
    ${power_Sm告警存在}    判断power_sm是否存在单个实时告警    交流输入限功率告警    ${SSH}
    should not be true    ${power_Sm告警存在}[0]
    [Teardown]    run keywords    Wait Until Keyword Succeeds    10    2    设置web参数量    市电额定有功功率    2.1
    ...    AND    Wait Until Keyword Succeeds    10    2    设置web参数量    交流输入限功率告警    严重
    ...    AND    Wait Until Keyword Succeeds    10    2    设置web参数量    市电额定有功功率    0
    ...    AND    关闭负载输出
    ...    AND    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
