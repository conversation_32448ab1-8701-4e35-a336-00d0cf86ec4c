*** Settings ***
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
系统停电转放电管理
    
    铁锂电池管理初始化
    sleep    5
    设置负载电压电流    53.5    10
    打开负载输出
    Wait Until Keyword Succeeds    5m    2    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    20    2    信号量数据值为    电池管理状态    检测
    关闭交流源输出
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    系统停电
    关闭负载输出
    打开交流源输出
    [Teardown]    run keywords    关闭负载输出
    ...    AND    打开交流源输出

整流器故障时转放电
    
    铁锂电池管理初始化
    Wait Until Keyword Succeeds    3m    2    信号量数据值为    交流供电状态    市电1
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    过渡
    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    检测
    ${充电电压可设置范围}    获取web参数可设置范围    充电电压
    设置web参数量    充电电压    ${充电电压可设置范围}[0]
    ${输出高停机电压可设置范围}    获取web参数可设置范围    整流器输出高停机电压
    设置web参数量    整流器输出高停机电压    ${输出高停机电压可设置范围}[0]
    #使整流器输出过压保证无正常工作的整流器
    ${整流器输出高电压}    获取web参数量    整流器输出高停机电压
    向上调节电池电压    ${整流器输出高电压}+0.5
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    工作整流器数量    0
    ${状态}    获取web实时数据    电池管理状态
    #无整流器放电状态启动浮充，应F
    设置负载电压电流    53.5    10
    打开负载输出
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    系统停电
    #整流器过压恢复，5min应能恢复
    关闭负载输出
    向下调节电池电压    53.5
    ${在线整流器数量}    获取web实时数据    在线整流器数量
    Wait Until Keyword Succeeds    5m10s    1    信号量数据值为    工作整流器数量    ${在线整流器数量}
    [Teardown]    Run keywords    设置web设备参数量为默认值    充电电压    整流器输出高停机电压
    ...    AND    重置电池模拟器输出

无电池时转充电管理
    
    铁锂电池管理初始化
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    检测
    sleep    30s
    设置web参数量    电池组容量_1    0
    sleep    30s
    Wait Until Keyword Succeeds    3m    1    信号量数据值为    电池管理状态    充电
    [Teardown]    设置web参数量    电池组容量_1    100

检测时间到转充电
    
    铁锂电池管理初始化
    #设置电池检测持续时间
    ${缺省值}    获取web参数上下限范围    电池检测持续时间
    设置web参数量    电池检测持续时间    ${缺省值}[0]
    sleep    5
    ${设置值}    获取web参数量    电池检测持续时间
    ${等待时间}    evaluate    ${设置值} +5
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    ${等待时间}m    1    信号量数据值为    电池管理状态    充电
    [Teardown]    设置web参数量    电池检测持续时间    ${缺省值}[0]

直流电压小于测试终止电压转充电
    
    铁锂电池管理初始化
    #设置测试终止电压
    ${测试终止电压可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${测试终止电压可设置范围}[0]
    ${缺省值}    获取web参数上下限范围    电池检测持续时间
    设置web参数量    电池检测持续时间    ${缺省值}[0]
    sleep    5
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    20    1    信号量数据值为    电池管理状态    检测
    ${设置值}    获取web参数量    测试终止电压
    向下调节电池电压    ${设置值}-0.5
    ${电池测试持续时间}    获取web实时数据    电池状态持续时间
    should be true    ${电池测试持续时间}<= ${缺省值}[0]
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    充电
    向上调节电池电压    52.5
    [Teardown]    设置web设备参数量为默认值    测试终止电压    电池检测持续时间

BMS通讯断转充电
    
    铁锂电池管理初始化
    ${缺省值}    获取web参数上下限范围    电池检测持续时间
    设置web参数量    电池检测持续时间    ${缺省值}[0]
    sleep    5
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    20    1    信号量数据值为    电池管理状态    检测
    显示属性配置    BMS通信断状态    数字量    web_attr=On    gui_attr=On
    #子设备工具模拟
    控制子工具运行停止    smartli    关闭
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    BMS通信断状态-1    异常
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    BMS通信断状态-2    异常
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    BMS通信断状态-3    异常
    ${级别设置值}    获取web参数量    BMS通信断告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    BMS通信断告警    严重
    wait until keyword succeeds    5m    2    查询指定告警信息    BMS通信断告警
    ${电池测试持续时间}    获取web实时数据    电池状态持续时间
    should be true    ${电池测试持续时间}<= ${缺省值}[0]
    Wait Until Keyword Succeeds    10    1    信号量数据值为    电池管理状态    充电
    重新启动FB100B3    3
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    BMS通信断状态-1    正常
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    BMS通信断状态-2    正常
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    BMS通信断状态-3    正常
    wait until keyword succeeds    5m    2    查询指定告警信息不为    BMS通信断告警
    显示属性配置    BMS通信断状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    重新启动FB100B3    3