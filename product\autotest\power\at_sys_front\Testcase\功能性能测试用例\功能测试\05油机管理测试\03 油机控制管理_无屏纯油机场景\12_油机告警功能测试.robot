*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
油机启停过程中交流告警自动屏蔽测试
    [Tags]    notest

油机异常告警
    油机管理初始化
    ${最短运行时间设置值}    获取web参数量    油机最短运行时间
    ${默认值}    获取web参数上下限范围    油机最短运行时间
    run keyword if    ${最短运行时间设置值} != ${默认值}[0]    Wait Until Keyword Succeeds    10    1    设置web参数量    油机最短运行时间
    ...    ${默认值}[0]
    ${最短运行时间设置值}    获取web参数量    油机最短运行时间
    Wait Until Keyword Succeeds    5m    1    设置web控制量    <<油机开启~0x19001040010001>>
    Wait Until Keyword Succeeds    14m    1    获取干接点状态直到变化为    ${油机干接点状态}    0    #干接点动作为1（断开），恢复为0（闭合）
    Comment    同时设置三相电压频率    220    50
    Comment    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    15m    1    判断告警存在    油机异常告警
    Wait Until Keyword Succeeds    15m    1    设置web控制量    <<油机开启~0x19001040010001>>
    Wait Until Keyword Succeeds    14m    1    获取干接点状态直到变化为    ${油机干接点状态}    0    #干接点动作为1（断开），恢复为0（闭合）
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    运行
    Wait Until Keyword Succeeds    15m    1    判断告警不存在    油机异常告警
    Wait Until Keyword Succeeds    15m    1    设置web控制量    <<油机关闭~0x19001040020001>>
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    停止
    Wait Until Keyword Succeeds    14m    1    获取干接点状态直到变化为    ${油机干接点状态}    1    #干接点动作为1（断开），恢复为0（闭合）
    关闭交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    [Teardown]    关闭交流源输出
