*** Settings ***
Suite Setup       Run Keywords   主动告警测试前置条件    ${CSU_role}
...    AND    测试用例前置条件
Suite Teardown    Run Keywords   设置web设备参数量为默认值    CSU主动告警使能
...    AND    测试用例后置条件
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Default Tags      T1-1
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0002_市电停电
    [Documentation]    纯市电场景，交流停电后，CSU告警市电停电，不会告警交流停电
    [Setup]    run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    ${级别设置值}    获取web参数量    市电停电
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    市电停电    主要
    Wait Until Keyword Succeeds    10m    1    查询指定告警信息不为    停电
    Wait Until Keyword Succeeds    10m    1    信号量数据值为    交流供电状态    市电1
    关闭交流源输出
    控制子工具运行停止    oileng    关闭
    Wait Until Keyword Succeeds    5m    2    设置web参数量    市电停电    屏蔽    #默认级别：2
    sleep    3
    Wait Until Keyword Succeeds    10m    1    判断告警不存在    市电停电
    ${告警级别取值约定dict}    获取web参数的取值约定    市电停电
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    3m    1    设置web参数量    市电停电    ${告警级别设置}
        wait until keyword succeeds    10m    1    查询指定告警信息    市电停电
        sleep    5
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    市电停电-1    ${SSH}
        should be true    ${power_sm告警存在}[0]
        Comment    将数字型告警级别转换为中文
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    #消失
    打开交流源输出
    控制子工具运行停止    oileng    启动
    Wait Until Keyword Succeeds    3m    1    判断告警不存在    市电停电
    ${power_Sm告警存在}    判断power_sm是否存在单个实时告警    交流停电    ${SSH}
    should not be true    ${power_Sm告警存在}[0]
    [Teardown]    run keywords    打开交流源输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0003_市电频率高
    [Tags]    3
    连接CSU
    ${级别设置值}    获取web参数量    市电频率高
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    市电频率高    主要
    设置子工具值    oileng    all    告警    备用6    2048
    wait until keyword succeeds    10m    5    查询指定告警信息    市电频率高
    Wait Until Keyword Succeeds    5m    2    设置web参数量    市电频率高    屏蔽
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    市电频率高
    ${告警级别取值约定dict}    获取web参数的取值约定    市电频率高
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    市电频率高    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    市电频率高
        sleep    5
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    市电频率高-1    ${SSH}
        should be true    ${power_sm告警存在}[0]
        Comment    将数字型告警级别转换为中文
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    设置子工具值    oileng    all    告警    备用6    0
    wait until keyword succeeds    10m    5    查询指定告警信息不为    市电频率高
    ${power_Sm告警存在}    判断power_sm是否存在单个实时告警    市电频率高-1    ${SSH}
    should not be true    ${power_Sm告警存在}[0]
    [Teardown]    Run keywords    设置子工具值    oileng    all    告警    备用6    0
    ...    AND    设置web参数量    市电频率高    主要
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0004_市电电压过压
    [Tags]    3
    连接CSU
    ${级别设置值}    获取web参数量    市电电压过压
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    市电电压过压    主要
    设置子工具值    oileng    all    告警    备用6    512
    wait until keyword succeeds    10m    5    查询指定告警信息    市电电压过压
    Wait Until Keyword Succeeds    5m    2    设置web参数量    市电电压过压    屏蔽
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    市电电压过压
    ${告警级别取值约定dict}    获取web参数的取值约定    市电电压过压
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    市电电压过压    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    市电电压过压
        sleep    5
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    市电电压过压-1    ${SSH}
        should be true    ${power_sm告警存在}[0]
        Comment    将数字型告警级别转换为中文
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    设置子工具值    oileng    all    告警    备用6    0
    sleep    20s
    wait until keyword succeeds    10m    5    查询指定告警信息不为    市电电压过压
    ${power_Sm告警存在}    判断power_sm是否存在单个实时告警    市电电压过压-1    ${SSH}
    should not be true    ${power_Sm告警存在}[0]
    [Teardown]    Run keywords    设置子工具值    oileng    all    告警    备用6    0
    ...    AND    设置web参数量    市电电压过压    主要
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0005_市电电压欠压
    [Tags]    3
    连接CSU
    ${级别设置值}    获取web参数量    市电电压欠压
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    市电电压欠压    主要
    设置子工具值    oileng    all    告警    备用6    256
    wait until keyword succeeds    10m    5    查询指定告警信息    市电电压欠压
    Wait Until Keyword Succeeds    5m    2    设置web参数量    市电电压欠压    屏蔽
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    市电电压欠压
    ${告警级别取值约定dict}    获取web参数的取值约定    市电电压欠压
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    市电电压欠压    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    市电电压欠压
        sleep    5
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    市电电压欠压-1    ${SSH}
        should be true    ${power_sm告警存在}[0]
        Comment    将数字型告警级别转换为中文
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    设置子工具值    oileng    all    告警    备用6    0
    wait until keyword succeeds    10m    5    查询指定告警信息不为    市电电压欠压
    ${power_Sm告警存在}    判断power_sm是否存在单个实时告警    市电电压欠压-1    ${SSH}
    should not be true    ${power_Sm告警存在}[0]
    [Teardown]    Run keywords    设置子工具值    oileng    all    告警    备用6    0
    ...    AND    设置web参数量    市电电压欠压    主要
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0006_市电相电流高
    [Tags]    3
    连接CSU
    ${级别设置值}    获取web参数量    市电相电流高
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    市电相电流高    主要
    设置子工具值    oileng    all    告警    备用6    16384
    wait until keyword succeeds    10m    5    查询指定告警信息    市电相电流高
    Wait Until Keyword Succeeds    5m    2    设置web参数量    市电相电流高    屏蔽
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    市电相电流高
    ${告警级别取值约定dict}    获取web参数的取值约定    市电相电流高
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    市电相电流高    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    市电相电流高
        sleep    5
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    市电相电流高-1    ${SSH}
        should be true    ${power_sm告警存在}[0]
        Comment    将数字型告警级别转换为中文
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    设置子工具值    oileng    all    告警    备用6    0
    wait until keyword succeeds    10m    5    查询指定告警信息不为    市电相电流高
    ${power_Sm告警存在}    判断power_sm是否存在单个实时告警    市电相电流高-1    ${SSH}
    should not be true    ${power_Sm告警存在}[0]
    [Teardown]    Run keywords    设置子工具值    oileng    all    告警    备用6    0
    ...    AND    设置web参数量    市电相电流高    主要
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0007_市电频率低
    [Tags]    3
    连接CSU
    ${级别设置值}    获取web参数量    市电频率低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    市电频率低    主要
    设置子工具值    oileng    all    告警    备用6    1024
    wait until keyword succeeds    10m    5    查询指定告警信息    市电频率低
    Wait Until Keyword Succeeds    5m    2    设置web参数量    市电频率低    屏蔽
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    市电频率低
    ${告警级别取值约定dict}    获取web参数的取值约定    市电频率低
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    市电频率低    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    市电频率低
        sleep    5
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    市电频率低-1    ${SSH}
        should be true    ${power_sm告警存在}[0]
        Comment    将数字型告警级别转换为中文
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    设置子工具值    oileng    all    告警    备用6    0
    wait until keyword succeeds    10m    5    查询指定告警信息不为    市电频率低
    ${power_Sm告警存在}    判断power_sm是否存在单个实时告警    市电频率低-1    ${SSH}
    should not be true    ${power_Sm告警存在}[0]
    [Teardown]    Run keywords    设置子工具值    oileng    all    告警    备用6    0
    ...    AND    设置web参数量    市电频率低    主要
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
