*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
批量获取油机模拟量测试
    写入CSV文档    油机模拟量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=油机
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${油机排除模拟量信号}    ${排除列表}
	${缺省值列表}   获取缺省值列表  ${列表1}    1
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    oileng       只读    ${缺省值列表}    2    油机模拟量获取测试
	${缺省值列表}   获取缺省值列表  ${列表1}    2
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    oileng       只读    ${缺省值列表}    2    油机模拟量获取测试
    ${缺省值列表}   获取缺省值列表  ${列表1}    0
	Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    oileng       只读    ${缺省值列表}    2    油机模拟量获取测试
	

批量获取市电模拟量测试
    写入CSV文档    油机模拟量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=市电
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${市电排除模拟量信号}    ${排除列表}
	${缺省值列表}   获取缺省值列表  ${列表1}    1
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    oileng       只读    ${缺省值列表}    2    油机模拟量获取测试
	${缺省值列表}   获取缺省值列表  ${列表1}    2
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    oileng       只读    ${缺省值列表}    2    油机模拟量获取测试
	${缺省值列表}   获取缺省值列表  ${列表1}    0
	Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    oileng       只读    ${缺省值列表}    2    油机模拟量获取测试
	
    

市电有功功率
    连接CSU
    设置子工具值    oileng    all    只读    市电 A 相有功功率    4000
    设置子工具值    oileng    all    只读    市电 B 相有功功率    5000
    设置子工具值    oileng    all    只读    市电 C 相有功功率    6000
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_active_power_L1    6000
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_active_power_L2    5000
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_active_power_L3    4000
    设置子工具值    oileng    all    只读    市电 A 相有功功率    2000
    设置子工具值    oileng    all    只读    市电 B 相有功功率    3000
    设置子工具值    oileng    all    只读    市电 C 相有功功率    3568
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_active_power_L1    3568
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_active_power_L2    3000
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_active_power_L3    2000
    设置子工具值    oileng    all    只读    市电 A 相有功功率    2200
    设置子工具值    oileng    all    只读    市电 B 相有功功率    2234
    设置子工具值    oileng    all    只读    市电 C 相有功功率    2267
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_active_power_L1    2267
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_active_power_L2    2234
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_active_power_L3    2200

市电无功功率
    连接CSU
    设置子工具值    oileng    all    只读    市电 A 相无功功率    98
    设置子工具值    oileng    all    只读    市电 B 相无功功率    88
    设置子工具值    oileng    all    只读    市电 C 相无功功率    68
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_react_power_L1    68
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_react_power_L2    88
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_react_power_L3    98
    设置子工具值    oileng    all    只读    市电 A 相无功功率    1000
    设置子工具值    oileng    all    只读    市电 B 相无功功率    1100
    设置子工具值    oileng    all    只读    市电 C 相无功功率    1468
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_react_power_L1    1468
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_react_power_L2    1100
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_react_power_L3    1000
    设置子工具值    oileng    all    只读    市电 A 相无功功率    200
    设置子工具值    oileng    all    只读    市电 B 相无功功率    234
    设置子工具值    oileng    all    只读    市电 C 相无功功率    267
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_react_power_L1    267
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_react_power_L2    234
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_react_power_L3    200
市电视在功率
    连接CSU
    设置子工具值    oileng    all    只读    市电 A 相视在功率    1001
    设置子工具值    oileng    all    只读    市电 B 相视在功率    2002
    设置子工具值    oileng    all    只读    市电 C 相视在功率    3003
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_appar_power_L1    3003
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_appar_power_L2    2002
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_appar_power_L3    1001
    设置子工具值    oileng    all    只读    市电 A 相视在功率    3010
    设置子工具值    oileng    all    只读    市电 B 相视在功率    3120
    设置子工具值    oileng    all    只读    市电 C 相视在功率    3478
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_appar_power_L1    3478
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_appar_power_L2    3120
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_appar_power_L3    3010
    设置子工具值    oileng    all    只读    市电 A 相视在功率    4300
    设置子工具值    oileng    all    只读    市电 B 相视在功率    4334
    设置子工具值    oileng    all    只读    市电 C 相视在功率    4367
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_appar_power_L1    4367
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_appar_power_L2    4334
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_appar_power_L3    4300

市电相电流获取测试
    连接CSU
    设置子工具值    oileng    all    只读    市电 A 相电流    601
    设置子工具值    oileng    all    只读    市电 B 相电流    602
    设置子工具值    oileng    all    只读    市电 C 相电流    603
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_phase_curr_L1    603
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_phase_curr_L2    602
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_phase_curr_L3    601
    设置子工具值    oileng    all    只读    市电 A 相电流    221
    设置子工具值    oileng    all    只读    市电 B 相电流    220
    设置子工具值    oileng    all    只读    市电 C 相电流    219
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_phase_curr_L1    219
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_phase_curr_L2    220
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_phase_curr_L3    221
    设置子工具值    oileng    all    只读    市电 A 相电流    1
    设置子工具值    oileng    all    只读    市电 B 相电流    0
    设置子工具值    oileng    all    只读    市电 C 相电流    2
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_phase_curr_L1    2
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_phase_curr_L2    0
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_phase_curr_L3    1

市电功率因数获取测试
    连接CSU
    设置子工具值    oileng    all    只读    市电 A 相功率因数    0.98
    设置子工具值    oileng    all    只读    市电 B 相功率因数    0.99
    设置子工具值    oileng    all    只读    市电 C 相功率因数    1
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_power_factor_L1    1
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_power_factor_L2    0.99
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_power_factor_L3    0.98
    设置子工具值    oileng    all    只读    市电 A 相功率因数    99
    设置子工具值    oileng    all    只读    市电 B 相功率因数    98
    设置子工具值    oileng    all    只读    市电 C 相功率因数    97
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_power_factor_L1    97
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_power_factor_L2    98
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_power_factor_L3    99
    设置子工具值    oileng    all    只读    市电 A 相功率因数    0.95
    设置子工具值    oileng    all    只读    市电 B 相功率因数    0.96
    设置子工具值    oileng    all    只读    市电 C 相功率因数    0.97
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_power_factor_L1    0.97
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_power_factor_L2    0.96
    Wait Until Keyword Succeeds    5m   2    获取指定量的调测信息值为    mains_power_factor_L3    0.95
