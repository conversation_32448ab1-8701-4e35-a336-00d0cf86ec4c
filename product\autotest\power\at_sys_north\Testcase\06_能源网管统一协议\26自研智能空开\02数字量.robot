*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
批量获取智能空开数字量
    写入CSV文档    SSW数字量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=digital data    INDEX_DEVICE_CH=智能空开
    @{排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SSW排除数字量信号}    ${排除列表}    1
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    智能空开    digital data    ${SSW排除数字量信号}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    1    power_sm
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    power_sm    DMU_IntelAirSwit     呼叫    ${缺省值列表}    SSW数字量获取测试    获取智能空开数字量   
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    2    power_sm
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    power_sm    DMU_IntelAirSwit     呼叫    ${缺省值列表}    SSW数字量获取测试    获取智能空开数字量    
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    0    power_sm
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    power_sm    DMU_IntelAirSwit     呼叫    ${缺省值列表}    SSW数字量获取测试    获取智能空开数字量   

    