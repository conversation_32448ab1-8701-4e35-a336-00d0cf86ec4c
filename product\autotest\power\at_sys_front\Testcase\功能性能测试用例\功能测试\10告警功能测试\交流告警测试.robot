*** Settings ***
Suite Setup
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
交流停电告警测试
    [Documentation]    纯市电场景，交流停电后，CSU告警市电停电，不会告警交流停电
    
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    停电
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    交流供电状态    市电1
    关闭交流源输出
    Wait Until Keyword Succeeds    5m    2    设置web参数量    市电停电    屏蔽    #默认级别：2
    sleep    3
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    市电停电
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    市电停电    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    市电停电
        sleep    5
        ${交流停电告警级别}    获取web告警属性    市电停电-1    告警级别
        should be equal    '${告警级别设置}'    '${交流停电告警级别}'
    END
    
    Wait Until Keyword Succeeds    30    1    设置web参数量    市电停电    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    FOR    ${告警干接点设置}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    市电停电-1    告警干接点
        run keyword if    ${告警干接点获取}!=0    Wait Until Keyword Succeeds    30    1    设置web参数量    市电停电干接点    ${干接点设置为无}
        sleep    8
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1    #0为恢复，没动作
        Wait Until Keyword Succeeds    30    1    设置web参数量    市电停电干接点    ${告警干接点设置}
        sleep    20
        ${告警干接点获取}    获取web告警属性    市电停电-1    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0    #干接点动作为1（断开），恢复为0（闭合）
    END
    [Teardown]    run keywords    设置web参数量    市电停电干接点    0
    ...    AND    打开交流源输出
    

交流缺相告警测试
    [Setup]    run keywords    测试用例前置条件
    ...    AND    连接CSU
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    交流制式    L1L2L3N-220V
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流缺相
    分别设置各相电压频率    0    220    220    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流缺相_1    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    2m    1    查询指定告警信息不为    交流缺相
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流缺相_1    ${告警级别设置}
        sleep    2
        wait until keyword succeeds    1m    1    查询指定告警信息    交流缺相_1
        ${交流缺相告警级别}    获取web告警属性    交流缺相_1    告警级别
        should be equal    '${告警级别设置}'    '${交流缺相告警级别}'
    END
    
    Wait Until Keyword Succeeds    30    1    设置web参数量    交流缺相_1    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    FOR    ${告警干接点设置}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    交流缺相_1    告警干接点
        run keyword if    ${告警干接点获取}!=0    Wait Until Keyword Succeeds    30    1    设置web参数量    交流缺相_1干接点    ${干接点设置为无}
        sleep    8
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        Wait Until Keyword Succeeds    30    1    设置web参数量    交流缺相_1干接点    ${告警干接点设置}
        sleep    8
        ${告警干接点获取}    获取web告警属性    交流缺相_1    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
    END
    [Teardown]    run keywords    设置web参数量    交流缺相_1干接点    0
    ...    AND    同时设置三相电压频率    220    50
    

交流电压低告警测试
    
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压
    ${可设置范围}    获取web参数可设置范围    交流电压低阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压低阈值    ${可设置范围}[1]
    ${电压低设置值}    获取web参数量    交流电压低阈值
    ${判断点1}    evaluate    ${电压低设置值}-5
    Comment    分别设置各相电压频率    220    220    ${判断点1}    50
    分别设置各相电压频率    ${判断点1}    ${判断点1}    ${判断点1}    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压低_1    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压低
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        wait until keyword succeeds    20    1    设置web参数量    交流电压低_1    ${告警级别设置}
        sleep    2
        wait until keyword succeeds    1m    1    查询指定告警信息    交流电压低_1
        ${交流电压低告警级别}    获取web告警属性    交流电压低_1    告警级别
        should be equal    ${告警级别设置}    ${交流电压低告警级别}
    END
    
    设置web参数量    交流电压低_1    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    FOR    ${告警干接点设置}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    交流电压低_1    告警干接点
        run keyword if    ${告警干接点获取}!=0    设置web参数量    交流电压低_1干接点    ${干接点设置为无}
        sleep    8
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        设置web参数量    交流电压低_1干接点    ${告警干接点设置}
        sleep    8
        ${告警干接点获取}    获取web告警属性    交流电压低_1    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
    END
    [Teardown]    run keywords    设置web参数量    交流电压低_1干接点    0
    ...    AND    交流电压参数恢复默认值
    ...    AND    同时设置三相电压频率    220    50
    

交流电压高告警测试
    
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压
    ${可设置范围}    获取web参数可设置范围    交流电压高阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高阈值    ${可设置范围}[0]
    ${电压高设置值}    获取web参数量    交流电压高阈值
    ${判断点1}    evaluate    ${电压高设置值}+5
    分别设置各相电压频率    ${判断点1}    220    220    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高_1    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    交流电压高
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        wait until keyword succeeds    1m    1    设置web参数量    交流电压高_1    ${告警级别设置}
        sleep    2
        wait until keyword succeeds    1m    1    查询指定告警信息    交流电压高_1
        ${交流电压高告警级别}    获取web告警属性    交流电压高_1    告警级别
        should be equal    ${告警级别设置}    ${交流电压高告警级别}
    END
    
    设置web参数量    交流电压高_1    主要
    FOR    ${告警干接点设置}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    交流电压高_1    告警干接点
        run keyword if    ${告警干接点获取}!=0    设置web参数量    交流电压高_1干接点    ${干接点设置为无}
        sleep    8
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        设置web参数量    交流电压高_1干接点    ${告警干接点设置}
        sleep    8
        ${告警干接点获取}    获取web告警属性    交流电压高_1    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
    END
    [Teardown]    run keywords    设置web参数量    交流电压高_1干接点    0
    ...    AND    交流电压参数恢复默认值
    ...    AND    同时设置三相电压频率    220    50
    

交流电流高告警测试
    
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电流
    ${可设置范围}    获取web参数可设置范围    交流电流高阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电流高阈值    ${可设置范围}[0]
    设置负载电压电流    53.5    80
    打开负载输出
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    系统停电
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电流高_1    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    2    判断告警不存在    交流电流高
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        设置web参数量    交流电流高_1    ${告警级别设置}
        sleep    2
        wait until keyword succeeds    1m    1    查询指定告警信息    交流电流高_1
        ${交流电流高告警级别}    获取web告警属性    交流电流高_1    告警级别
        should be equal    ${告警级别设置}    ${交流电流高告警级别}
    END
    
    设置web参数量    交流电流高_1    主要
    FOR    ${告警干接点设置}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    交流电流高_1    告警干接点
        run keyword if    ${告警干接点获取}!=0    设置web参数量    交流电流高_1干接点    ${干接点设置为无}
        sleep    8
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        设置web参数量    交流电流高_1干接点    ${告警干接点设置}
        sleep    8
        ${告警干接点获取}    获取web告警属性    交流电流高_1    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
    END
    [Teardown]    run keywords    设置web参数量    交流电流高_1干接点    0
    ...    AND    设置web设备参数量为默认值    交流电流高阈值
    ...    AND    关闭负载输出
    

交流电压不平衡告警测试
    [Tags]
    [Setup]    run keywords    测试用例前置条件
    ...    AND    连接CSU
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    交流制式    L1L2L3N-220V
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    设置web所有告警干接点为指定值    0
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压不平衡
    ${可设置范围}    获取web参数可设置范围    交流电压不平衡阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压不平衡阈值    ${可设置范围}[0]
    ${电压低设置值}    获取web参数量    交流电压不平衡阈值
    ${判断点1}    evaluate    220-${电压低设置值}-5
    Comment    分别设置各相电压频率    190    250    220    50
    分别设置各相电压频率    ${判断点1}    250    220    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压不平衡    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    交流电压不平衡
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        设置web参数量    交流电压不平衡    ${告警级别设置}
        sleep    2
        wait until keyword succeeds    1m    1    查询指定告警信息    交流电压不平衡
        ${交流电压不平衡告警级别}    获取web告警属性    交流电压不平衡    告警级别
        should be equal    ${告警级别设置}    ${交流电压不平衡告警级别}
    END
    
    设置web参数量    交流电压不平衡    主要
    FOR    ${告警干接点设置}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    交流电压不平衡    告警干接点
        run keyword if    ${告警干接点获取}!=0    设置web参数量    交流电压不平衡干接点    ${干接点设置为无}
        sleep    8
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        设置web参数量    交流电压不平衡干接点    ${告警干接点设置}
        sleep    8
        ${告警干接点获取}    获取web告警属性    交流电压不平衡    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
        Comment
    END
    [Teardown]    run keywords    设置web参数量    交流电压不平衡干接点    0
    ...    AND    设置web设备参数量为默认值    交流电压不平衡阈值
    ...    AND    同时设置三相电压频率    220    50
    

交流电压过低告警测试
    
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压
    ${可设置范围}    获取web参数可设置范围    交流电压低阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压低阈值    ${可设置范围}[1]
    ${可设置范围}    获取web参数可设置范围    交流电压过低阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压过低阈值    ${可设置范围}[1]
    ${电压低设置值}    获取web参数量    交流电压过低阈值
    ${判断点1}    evaluate    ${电压低设置值}-5
    分别设置各相电压频率    ${判断点1}    ${判断点1}    ${判断点1}    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压过低_1    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    交流电压过低_1
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        wait until keyword succeeds    20    1    设置web参数量    交流电压过低_1    ${告警级别设置}
        sleep    2
        wait until keyword succeeds    1m    1    查询指定告警信息    交流电压过低_1
        ${交流电压过低告警级别}    获取web告警属性    交流电压过低_1    告警级别
        should be equal    ${告警级别设置}    ${交流电压过低告警级别}
    END
    
    设置web参数量    交流电压过低_1    主要
    FOR    ${告警干接点设置}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    交流电压过低_1    告警干接点
        run keyword if    ${告警干接点获取}!=0    设置web参数量    交流电压过低_1干接点    ${干接点设置为无}
        sleep    8
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        设置web参数量    交流电压过低_1干接点    ${告警干接点设置}
        sleep    8
        ${告警干接点获取}    获取web告警属性    交流电压过低_1    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
    END
    [Teardown]    run keywords    设置web参数量    交流电压过低_1干接点    0
    ...    AND    交流电压参数恢复默认值
    ...    AND    同时设置三相电压频率    220    50
    

交流电压过高告警测试
    
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压
    ${可设置范围}    获取web参数可设置范围    交流电压高阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高阈值    ${可设置范围}[0]
    ${电压高设置值}    获取web参数量    交流电压高阈值
    ${可设置范围1}    获取web参数可设置范围    交流电压过高阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压过高阈值    ${可设置范围1}[0]
    ${电压过高设置值}    获取web参数量    交流电压过高阈值
    ${判断点1}    evaluate    ${电压过高设置值}+5
    分别设置各相电压频率    ${判断点1}    220    220    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压过高_1    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    交流电压过高_1
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        wait until keyword succeeds    20    1    设置web参数量    交流电压过高_1    ${告警级别设置}
        sleep    5
        wait until keyword succeeds    5m    1    查询指定告警信息    交流电压过高_1
        ${交流电压过高告警级别}    获取web告警属性    交流电压过高_1    告警级别
        should be equal    ${告警级别设置}    ${交流电压过高告警级别}
    END
    
    设置web参数量    交流电压过高_1    主要
    FOR    ${告警干接点设置}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    交流电压过高_1    告警干接点
        run keyword if    ${告警干接点获取}!=0    设置web参数量    交流电压过高_1干接点    ${干接点设置为无}
        sleep    8
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        设置web参数量    交流电压过高_1干接点    ${告警干接点设置}
        sleep    8
        ${告警干接点获取}    获取web告警属性    交流电压过高_1    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
    END
    [Teardown]    run keywords    设置web参数量    交流电压过高_1干接点    0
    ...    AND    交流电压参数恢复默认值
    ...    AND    同时设置三相电压频率    220    50
    

交流限功率预警测试
    
    电池管理初始化
    设置web所有告警干接点为指定值    0
    Wait Until Keyword Succeeds    10    2    设置web参数量    市电额定有功功率    2.1
    Wait Until Keyword Succeeds    10    2    设置web参数量    市电降额系数    85
    ${可设置范围}    获取web参数可设置范围    交流输入限功率预警阈值
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流输入限功率预警阈值    ${可设置范围}[0]
    ${设置值}    获取web参数量    交流输入限功率预警阈值
    ${电压}    获取web实时数据    直流电压
    run keyword if    ${电压}>54    向下调节电池电压    53.5
    run keyword if    ${电压}<52    向上调节电池电压    53.5
    缓慢设置负载电压电流    ${电压}    28
    打开负载输出
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流输入限功率预警    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    交流输入限功率预警
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        设置web参数量    交流输入限功率预警    ${告警级别设置}
        sleep    5
        wait until keyword succeeds    1m    1    查询指定告警信息    交流输入限功率预警
        ${交流电压过高告警级别}    获取web告警属性    交流输入限功率预警    告警级别
        should be equal    ${告警级别设置}    ${交流电压过高告警级别}
    END
    
    设置web参数量    交流输入限功率预警    严重
    FOR    ${告警干接点设置}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    交流输入限功率预警    告警干接点
        run keyword if    ${告警干接点获取}!=0    设置web参数量    交流输入限功率预警干接点    ${干接点设置为无}
        sleep    8
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        设置web参数量    交流输入限功率预警干接点    ${告警干接点设置}
        sleep    8
        ${告警干接点获取}    获取web告警属性    交流输入限功率预警    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
    END
    关闭负载输出
    Wait Until Keyword Succeeds    10    2    设置web参数量    市电额定有功功率    15
    设置WEB设备参数量为默认值    交流输入限功率预警阈值
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流输入限功率预警
    [Teardown]    run keywords    设置web参数量    交流输入限功率预警    严重
    ...    AND    设置web参数量    交流输入限功率预警干接点    0
    ...    AND    关闭负载输出
    

交流限功率告警测试
    
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    设置web所有告警干接点为指定值    0
    Wait Until Keyword Succeeds    10    2    设置web参数量    市电额定有功功率    2.1
    Wait Until Keyword Succeeds    10    2    设置web参数量    市电降额系数    85
    ${可设置范围}    获取web参数可设置范围    交流输入限功率告警阈值
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流输入限功率告警阈值    ${可设置范围}[0]
    ${设置值}    获取web参数量    交流输入限功率告警阈值
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    80
    打开负载输出
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流输入限功率告警    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    交流输入限功率告警
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        wait until keyword succeeds    1m    1    设置web参数量    交流输入限功率告警    ${告警级别设置}
        sleep    5
        wait until keyword succeeds    1m    1    查询指定告警信息    交流输入限功率告警
        ${交流电压过高告警级别}    获取web告警属性    交流输入限功率告警    告警级别
        should be equal    ${告警级别设置}    ${交流电压过高告警级别}
    END
    
    设置web参数量    交流输入限功率告警    严重
    FOR    ${告警干接点设置}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    交流输入限功率告警    告警干接点
        run keyword if    ${告警干接点获取}!=0    设置web参数量    交流输入限功率告警干接点    ${干接点设置为无}
        sleep    8
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        wait until keyword succeeds    1m    1    设置web参数量    交流输入限功率告警干接点    ${告警干接点设置}
        sleep    8
        ${告警干接点获取}    获取web告警属性    交流输入限功率告警    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
    END
    设置WEB设备参数量为默认值    交流输入限功率告警阈值
    Wait Until Keyword Succeeds    10    2    设置web参数量    市电额定有功功率    0
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流输入限功率告警
    [Teardown]    run keywords    Wait Until Keyword Succeeds    10    2    设置web参数量    市电额定有功功率    2.1
    ...    AND    Wait Until Keyword Succeeds    10    2    设置web参数量    交流输入限功率告警    严重
    ...    AND    Wait Until Keyword Succeeds    10    2    设置web参数量    交流输入限功率告警干接点    0
    ...    AND    Wait Until Keyword Succeeds    10    2    设置web参数量    市电额定有功功率    0
    ...    AND    关闭负载输出
    

交流输入空开断告警测试
    
    实时告警刷新完成
    设置web所有告警干接点为指定值    0
    设置web参数量    交流输入空开断    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    交流输入空开断
    ${告警}    判断告警存在_带返回值    交流输入空开断
    should not be true    ${告警}
    模拟数字量告警    交流输入空开断    ON
    sleep    10
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    10    1    设置web参数量    交流输入空开断    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    交流输入空开断
        sleep    3
        ${告警级别获取}    获取web告警属性    交流输入空开断    告警级别
        should be equal    ${告警级别}    ${告警级别获取}
    END
    
    设置web参数量    交流输入空开断    次要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    交流输入空开断    告警干接点
        run keyword if    ${告警干接点获取}!=0    设置web参数量    交流输入空开断干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        设置web参数量    交流输入空开断干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    交流输入空开断    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0
    END
    模拟数字量告警    交流输入空开断    OFF
    sleep    10
    [Teardown]    run keywords    设置web参数量    交流输入空开断干接点    0
    ...    AND    模拟数字量告警    交流输入空开断    OFF

交流防雷器异常告警测试
    
    实时告警刷新完成
    设置web所有告警干接点为指定值    0
    设置web参数量    交流防雷器异常    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    交流防雷器异常
    ${告警}    判断告警存在_带返回值    交流防雷器异常
    should not be true    ${告警}
    模拟数字量告警    交流防雷器异常    ON
    sleep    10
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    10    1    设置web参数量    交流防雷器异常    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    交流防雷器异常
        sleep    3
        ${告警级别获取}    获取web告警属性    交流防雷器异常    告警级别
        should be equal    ${告警级别}    ${告警级别获取}
    END
    设置web参数量    交流防雷器异常    次要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    交流防雷器异常    告警干接点
        run keyword if    ${告警干接点获取}!=0    设置web参数量    交流防雷器异常干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        设置web参数量    交流防雷器异常干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    交流防雷器异常    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0
    END
    模拟数字量告警    交流防雷器异常    OFF
    sleep    10
    [Teardown]    run keywords    设置web参数量    交流防雷器异常干接点    0
    ...    AND    模拟数字量告警    交流防雷器异常    OFF
    

市电停电告警测试
    [Documentation]    纯市电场景，交流停电后，CSU告警市电停电，不会告警交流停电
    
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    停电
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    交流供电状态    市电1
    关闭交流源输出
    Wait Until Keyword Succeeds    5m    2    设置web参数量    市电停电    屏蔽    #默认级别：2
    sleep    3
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    市电停电
    设置web参数量    电池应用场景    循环场景
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    市电停电    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    市电停电
        sleep    5
        ${交流停电告警级别}    获取web告警属性    市电停电-1    告警级别
        should be equal    '${告警级别设置}'    '${交流停电告警级别}'
    END
    
    [Teardown]    run keywords    设置web参数量    市电停电干接点    0
    ...    AND    打开交流源输出
    ...    AND    设置web参数量    市电停电    主要