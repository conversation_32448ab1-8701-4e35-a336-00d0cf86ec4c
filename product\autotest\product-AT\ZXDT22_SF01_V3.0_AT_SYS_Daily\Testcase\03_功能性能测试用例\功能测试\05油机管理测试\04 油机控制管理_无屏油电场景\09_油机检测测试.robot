*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
油机检测功能测试
    油机管理初始化
    ${缺省值}    获取web参数上下限范围    油机检测周期
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机检测周期    @{缺省值}[0]
    ${缺省值}    获取web参数上下限范围    油机检测时间
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机检测时间    @{缺省值}[0]
    ${缺省值}    获取web参数上下限范围    油机检测时刻
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机检测时刻    @{缺省值}[0]
    ${油机检测时间}    获取web参数量    油机检测时间
    ${油机检测周期}    获取web参数量    油机检测周期
    ${下次检测时间}    获取web实时数据    下次油机检测时间
    ${下次检测时间0}    Subtract Time From Date    ${下次检测时间}    5s    #比1:00:00差5s
    Wait Until Keyword Succeeds    10    1    设置系统时间    ${下次检测时间0}
    sleep    100
    ${干接点实际是否动作状态1}    获取干接点状态    ${油机干接点状态}
    should be true    ${干接点实际是否动作状态1}==0    #干接点动作为1（断开），恢复为0（闭合）
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    油机状态    运行
    ${下次检测时间_cal}    add time to date    ${下次检测时间}    ${油机检测周期}d    exclude_millis=yes
    sleep    5
    ${下次检测时间_new}    获取web实时数据    下次油机检测时间
    should be equal    ${下次检测时间_new}    ${下次检测时间_cal}
    ${运行时间}    evaluate    ${油机检测时间}+2
    ${状态}    Run Keyword And Return Status    Wait Until Keyword Succeeds    ${运行时间}m    2    信号量数据值为    油机状态    停止
    should not be true    ${状态}
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    油机状态    停止
    ${干接点实际是否动作状态1}    获取干接点状态    ${油机干接点状态}
    should be true    ${干接点实际是否动作状态1}==1    #干接点动作为1（断开），恢复为0（闭合）
    关闭交流源输出
    [Teardown]    Run keywords    同步系统时间
    ...    AND    关闭交流源输出
