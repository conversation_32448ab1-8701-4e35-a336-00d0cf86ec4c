*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
温湿度传感器通讯断告警
    [Tags]    trap
    连接CSU
    ${级别设置值}    获取web参数量    <<温湿度传感器通讯断告警~0x30001030010001>>
    run keyword if    '${级别设置值}'=='屏蔽'    wait until keyword succeeds    10    2    设置web参数量    <<温湿度传感器通讯断告警~0x30001030010001>>    严重
    进行SNMP_V2/V3连接    ${snmp连接方式}
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    控制子工具运行停止    DMU_YD8779Y    关闭
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    温湿度传感器通讯状态    异常
    wait until keyword succeeds    5m    2    查询指定告警信息    温湿度传感器通讯断告警
    ${snmp英文名}    获取snmp单个告警英文名    温湿度传感器通讯断告警
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    控制子工具运行停止    DMU_YD8779Y    开启
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    温湿度传感器通讯状态    正常
    wait until keyword succeeds    5m    1    判断告警不存在    温湿度传感器通讯断告警
    ${snmp英文名}    获取snmp单个告警英文名    温湿度传感器通讯断告警
    ${告警消失}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警消失}
    [Teardown]    Run Keywords    控制子工具运行停止    DMU_YD8779Y    开启
    ...    AND    sleep    3min

snmp_扩展湿度高告警
    [Tags]    trap
    [Setup]
    连接CSU
    实时告警刷新完成
    ${级别设置值}    获取web参数量    扩展湿度高
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    扩展湿度高    主要
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    扩展湿度高
    ${可设置范围}    获取web参数可设置范围    扩展湿度高阈值
    设置web参数量    扩展湿度高阈值    ${可设置范围}[0]
    ${扩展湿度高阈值}    获取web参数量    扩展湿度高阈值
    进行SNMP_V2/V3连接    ${snmp连接方式}
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    FOR    ${i}    IN RANGE    1    10    1
        ${目标值}    Evaluate    int(${扩展湿度高阈值})+int(${i})
        ${目标值}    Convert To String    ${目标值}
        设置子工具值    DMU_YD8779Y    all    只读    湿度    ${目标值}
        sleep    10
        ${WEB扩展湿度}    获取web实时数据    扩展湿度
        exit for loop if    ${WEB扩展湿度}>${扩展湿度高阈值}
    END
    wait until keyword succeeds    5m    1    判断告警存在    扩展湿度高
    ${snmp英文名}    获取snmp单个告警英文名    扩展湿度高
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    wait until keyword succeeds    1m    1    查询指定告警信息    扩展湿度高
    Wait Until Keyword Succeeds    30    1    设置web参数量    扩展湿度高    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    扩展湿度高
    ${扩展湿度高告警}    判断告警存在_带返回值    扩展湿度高
    should not be true    ${扩展湿度高告警}
    ${snmp英文名}    获取snmp单个告警英文名    扩展湿度高
    ${告警消失}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警消失}
    [Teardown]    Run Keywords    设置web设备参数量为默认值    扩展湿度高阈值
    ...    AND    设置web设备参数量为默认值    扩展湿度高

snmp_扩展湿度低告警
    [Tags]    trap
    [Setup]
    连接CSU
    实时告警刷新完成
    ${级别设置值}    获取web参数量    扩展湿度低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    扩展湿度低    主要
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    扩展湿度低
    ${可设置范围}    获取web参数可设置范围    扩展湿度低阈值
    设置web参数量    扩展湿度低阈值    ${可设置范围}[1]
    ${扩展湿度低阈值}    获取web参数量    扩展湿度低阈值
    进行SNMP_V2/V3连接    ${snmp连接方式}
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    FOR    ${i}    IN RANGE    1    10    1
        ${目标值}    Evaluate    int(${扩展湿度低阈值})-int(${i})
        ${目标值}    Convert To String    ${目标值}
        设置子工具值    DMU_YD8779Y    all    只读    湿度    ${目标值}
        sleep    10
        ${WEB扩展湿度}    获取web实时数据    扩展湿度
        exit for loop if    ${WEB扩展湿度}<${扩展湿度低阈值}
    END
    wait until keyword succeeds    5m    1    判断告警存在    扩展湿度低
    wait until keyword succeeds    1m    1    查询指定告警信息    扩展湿度低
    ${snmp英文名}    获取snmp单个告警英文名    扩展湿度低
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    Wait Until Keyword Succeeds    30    1    设置web参数量    扩展湿度低    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    扩展湿度低
    ${扩展湿度低告警}    判断告警存在_带返回值    扩展湿度低
    should not be true    ${扩展湿度低告警}
    ${snmp英文名}    获取snmp单个告警英文名    扩展湿度低
    ${告警消失}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警消失}
    [Teardown]    Run Keywords    设置web设备参数量为默认值    扩展湿度低阈值
    ...    AND    设置web设备参数量为默认值    扩展湿度低

snmp_扩展湿度无效告警
    [Tags]    trap
    [Setup]
    连接CSU
    实时告警刷新完成
    ${级别设置值}    获取web参数量    扩展湿度无效
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    扩展湿度无效    主要
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    扩展湿度无效
    进行SNMP_V2/V3连接    ${snmp连接方式}
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    设置子工具值    DMU_YD8779Y    all    只读    湿度    101
    wait until keyword succeeds    5m    1    判断告警存在    扩展湿度无效
    ${snmp英文名}    获取snmp单个告警英文名    扩展湿度无效
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    Wait Until Keyword Succeeds    30    1    设置web参数量    扩展湿度无效    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    扩展湿度无效
    ${扩展湿度高告警}    判断告警存在_带返回值    扩展湿度无效
    should not be true    ${扩展湿度高告警}
    ${snmp英文名}    获取snmp单个告警英文名    扩展湿度无效
    ${告警消失}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警消失}
    [Teardown]    Run Keywords    设置web设备参数量为默认值    扩展湿度无效
    ...    AND    设置子工具值    DMU_YD8779Y    all    只读    湿度    60
    ...    AND    sleep    3min

snmp_扩展温度高告警
    [Tags]    trap
    [Setup]
    连接CSU
    实时告警刷新完成
    Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_1
    sleep    30
    ${级别设置值}    获取web参数量    扩展温度高_1
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    扩展温度高_1    主要
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    扩展温度高
    ${可设置范围}    获取web参数可设置范围    扩展温度高阈值_1
    Wait Until Keyword Succeeds    30    1    设置web参数量    扩展温度高阈值_1    ${可设置范围}[0]
    ${扩展温度高阈值}    获取web参数量    扩展温度高阈值_1
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${缺省值}    获取web参数上下限范围    <<扩展温度~0x9001010030001>>
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    FOR    ${i}    IN RANGE    1    10    1
        ${目标值}    Evaluate    int(${扩展温度高阈值})+int(${i})
        ${目标值}    Convert To String    ${目标值}
        设置子工具值    DMU_YD8779Y    all    只读    温度    ${目标值}
        sleep    10
        ${WEB扩展温度}    获取web实时数据    扩展温度_1
        exit for loop if    ${WEB扩展温度}>${扩展温度高阈值}
    END
    wait until keyword succeeds    5m    1    判断告警存在    扩展温度高_1
    ${snmp英文名}    获取snmp单个告警英文名    扩展温度高_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    Wait Until Keyword Succeeds    30    1    设置web参数量    扩展温度高_1    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    扩展温度高_1
    ${扩展温度高告警}    判断告警存在_带返回值    扩展温度高_1
    should not be true    ${扩展温度高告警}
    ${snmp英文名}    获取snmp单个告警英文名    扩展温度高_1
    ${告警消失}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警消失}
    #4
    Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_4
    sleep    30
    ${级别设置值}    获取web参数量    扩展温度高_4
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    扩展温度高_4    主要
    ${可设置范围}    获取web参数可设置范围    扩展温度高阈值_4
    Wait Until Keyword Succeeds    30    1    设置web参数量    扩展温度高阈值_4    ${可设置范围}[0]
    ${扩展温度高阈值}    获取web参数量    扩展温度高阈值_4
    wait until keyword succeeds    5m    1    判断告警存在    扩展温度高_4
    ${snmp英文名}    获取snmp单个告警英文名    扩展温度高_4
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    Wait Until Keyword Succeeds    30    1    设置web参数量    扩展温度高_4    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    扩展温度高_4
    ${扩展温度高告警}    判断告警存在_带返回值    扩展温度高_4
    should not be true    ${扩展温度高告警}
    ${snmp英文名}    获取snmp单个告警英文名    扩展温度高_4
    ${告警消失}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警消失}
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    扩展温度高_4    主要
    设置子工具值    DMU_YD8779Y    all    只读    温度    ${缺省值}[0]
    wait until keyword succeeds    5m    1    判断告警不存在    扩展温度高_4
    [Teardown]

snmp_扩展温度低告警
    [Tags]    trap
    [Setup]
    连接CSU
    实时告警刷新完成
    Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_1
    sleep    30
    ${级别设置值}    获取web参数量    扩展温度低_1
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    扩展温度低_1    主要
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    扩展温度低_1
    ${可设置范围}    获取web参数可设置范围    扩展温度低阈值_1
    设置web参数量    扩展温度低阈值_1    ${可设置范围}[1]
    ${扩展温度低阈值}    获取web参数量    扩展温度低阈值_1
    ${缺省值}    获取web参数上下限范围    <<扩展温度~0x9001010030001>>
    进行SNMP_V2/V3连接    ${snmp连接方式}
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_1
    FOR    ${i}    IN RANGE    1    10    1
        ${目标值}    Evaluate    int(${扩展温度低阈值})-int(${i})
        ${目标值}    Convert To String    ${目标值}
        设置子工具值    DMU_YD8779Y    all    只读    温度    ${目标值}
        sleep    10
        ${WEB扩展温度}    获取web实时数据    扩展温度_1
        exit for loop if    ${WEB扩展温度}<${扩展温度低阈值}
    END
    wait until keyword succeeds    5m    1    判断告警存在    扩展温度低_1
    wait until keyword succeeds    1m    1    查询指定告警信息    扩展温度低_1
    ${snmp英文名}    获取snmp单个告警英文名    扩展温度低_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    Wait Until Keyword Succeeds    30    1    设置web参数量    扩展温度低_1    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    扩展温度低_1
    ${扩展温度低告警}    判断告警存在_带返回值    扩展温度低_1
    should not be true    ${扩展温度低告警}
    ${snmp英文名}    获取snmp单个告警英文名    扩展温度低_1
    ${告警消失}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警消失}
    Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_4
    sleep    30
    ${可设置范围}    获取web参数可设置范围    扩展温度低阈值_4
    设置web参数量    扩展温度低阈值_4    ${可设置范围}[1]
    ${级别设置值}    获取web参数量    扩展温度低_4
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    扩展温度低_4    主要
    ${扩展温度低阈值}    获取web参数量    扩展温度低阈值_4
    wait until keyword succeeds    5m    1    判断告警存在    扩展温度低_4
    ${snmp英文名}    获取snmp单个告警英文名    扩展温度低_4
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    Wait Until Keyword Succeeds    30    1    设置web参数量    扩展温度低_4    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    扩展温度低_4
    ${扩展温度低告警}    判断告警存在_带返回值    扩展温度低_4
    should not be true    ${扩展温度低告警}
    ${snmp英文名}    获取snmp单个告警英文名    扩展温度低_4
    ${告警消失}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警消失}
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    扩展温度低_4    主要
    设置子工具值    DMU_YD8779Y    all    只读    温度    ${缺省值}[0]
    wait until keyword succeeds    5m    1    判断告警不存在    扩展温度低_4
    [Teardown]

snmp_扩展温度无效告警
    [Tags]    trap
    [Setup]
    连接CSU
    实时告警刷新完成
    Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_1
    sleep    30
    ${缺省值}    获取web参数上下限范围    <<扩展温度~0x9001010030001>>
    ${级别设置值}    获取web参数量    扩展温度无效_1
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    扩展温度无效_1    主要
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    扩展温度无效_1
    进行SNMP_V2/V3连接    ${snmp连接方式}
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    设置子工具值    DMU_YD8779Y    all    只读    温度    101
    wait until keyword succeeds    5m    1    判断告警存在    扩展温度无效_1
    ${snmp英文名}    获取snmp单个告警英文名    扩展温度无效_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    Wait Until Keyword Succeeds    30    1    设置web参数量    扩展温度无效_1    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    扩展温度无效_1
    ${扩展湿度高告警}    判断告警存在_带返回值    扩展温度无效_1
    should not be true    ${扩展湿度高告警}
    ${告警消失}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警消失}
    Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_4
    sleep    30
    ${级别设置值}    获取web参数量    扩展温度无效_4
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    扩展温度无效_4    主要
    设置子工具值    DMU_YD8779Y    all    只读    温度    101
    wait until keyword succeeds    5m    1    判断告警存在    扩展温度无效_4
    ${snmp英文名}    获取snmp单个告警英文名    扩展温度无效_4
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    Wait Until Keyword Succeeds    30    1    设置web参数量    扩展温度无效_4    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    扩展温度无效_4
    ${扩展湿度高告警}    判断告警存在_带返回值    扩展温度无效_4
    should not be true    ${扩展湿度高告警}
    ${告警消失}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警消失}
    设置子工具值    DMU_YD8779Y    all    只读    温度    25
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    扩展温度无效_4    主要
    wait until keyword succeeds    5m    1    判断告警不存在    扩展温度无效_4
    [Teardown]    Run Keywords    设置子工具值    DMU_YD8779Y    all    只读    温度    25
    ...    AND    sleep    3min
