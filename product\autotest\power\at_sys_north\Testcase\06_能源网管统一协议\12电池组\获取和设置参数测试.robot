*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_获取电池组模拟量
    [Tags]    pass   3
    ${能源网管数据}    能源网管协议_获取数据    获取电池组模拟量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取电池组模拟量
    Should Be True    ${数据对比结果}
    ${校验结果}    能源网管协议_校验数据上送完整性    ${能源网管数据}    电池组    analog data
    Comment    Should Be True    ${校验结果}

power_sm_0002_获取电池组数字量
    [Tags]    pass   3
    ${能源网管数据}    能源网管协议_获取数据    获取电池组数字量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取电池组数字量
    Should Be True    ${数据对比结果}
    ${校验结果}    能源网管协议_校验数据上送完整性    ${能源网管数据}    电池组    digital data
    Comment    Should Be True    ${校验结果}

power_sm_0003_获取电池组参数量
    [Tags]    4
    ${能源网管数据}    能源网管协议_获取数据    获取电池组参数量    ${SSH}
    Log Many    ${能源网管数据}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取电池组参数量
    Should Be True    ${数据对比结果}
    ${校验结果}    能源网管协议_校验数据上送完整性    ${能源网管数据}    电池组    parameter
    Comment   Should Be True    ${校验结果}

power_sm_0004_设置电池组参数量
    [Tags]    4
    Comment    ${sheet_name}    Set Variable    设置环境参数
    ${能源网管数据}    能源网管协议_设置单个参数    设置电池组参数    ${BOARD_IP}    ${SSH}
    ${对比结果}    能源网管协议_批量对比参数设置    ${能源网管数据}    设置电池组参数
    Should Be True    ${对比结果}

power_sm_0005_设置电池组控制量
    [Tags]    4    notest
    能源网管协议_设置单个设备单个控制量    4    启动浮充    ${BOARD_IP}    ${SSH}
    ${能源网管数据}    能源网管协议_设置控制量    设置电池组控制量    ${BOARD_IP}    ${SSH}
    ${对比结果}    能源网管协议_批量对比参数设置    ${能源网管数据}    设置电池组控制量
    Comment    能源网管协议_设置单个设备单个控制量    2    整流器唤醒    ${BOARD_IP}    ${SSH}
    Comment    能源网管协议_设置单个设备单个控制量    2    整流器风扇调速允许    ${BOARD_IP}    ${SSH}
    Should Be True    ${对比结果}

power_sm_0006_铅酸电池控制量
    [Tags]    T1-1
    [Setup]    测试用例前置条件
    电池管理初始化
    Comment    能源网管协议_设置单个设备单个控制量    电池组    4    启动浮充    ${SSH}
    Comment    能源网管协议_设置单个设备单个控制量    电池组    4    启动均充    ${SSH}
    Comment    能源网管协议_设置单个设备单个控制量    电池组    4    启动测试    ${SSH}
    #1
    电池管理初始化
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    交流供电状态    市电1
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    过渡
    能源网管协议_设置单个设备单个控制量    电池组    4    启动浮充    ${SSH}
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    浮充
    #2
    能源网管协议_设置单个设备单个控制量    电池组    4    启动均充    ${SSH}
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    均充
    #3
    能源网管协议_设置单个设备单个控制量    电池组    4    启动测试    ${SSH}
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    测试
    [Teardown]    Run Keywords    Wait Until Keyword Succeeds    60    2    设置web控制量    启动浮充
    ...           AND    测试用例后置条件

power_sm_0007_铁锂电池控制量
    [Tags]    T1-1
    [Setup]    Wait Until Keyword Succeeds    60    2    设置web参数量    电池配置    智能锂电
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    智能锂电
    能源网管协议_设置单个设备单个控制量    电池组    4    启动充电    ${SSH}
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    充电
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    60    2    设置web参数量    电池配置    ${电池配置}
    ...    AND    Wait Until Keyword Succeeds    60    2    设置web参数量    铅酸类型    ${铅酸类型}
