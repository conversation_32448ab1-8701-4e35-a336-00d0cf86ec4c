*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
snmp_0002_fBBMS1alarm_level√
    Comment    ${比较结果}    对比告警级别_V2C    fBBMS1alarm
    Comment    should be true    ${比较结果}
    Comment    : FOR    ${锂电序号}    IN RANGE    1    ${snmp锂电最大数}+1
    @{锂电单体随机list}    从1-15个数中随机选n个不重复的单体    ${北向协议铁锂最大数}    2
    FOR    ${锂电序号}    IN    @{锂电单体随机list}
        ${比较结果}    对比告警级别_V2C    fBBMS${锂电序号}alarm
        should be true    ${比较结果}
    END

snmp_0004_fBBMS1alarm_level_writeX
    Comment    ${比较结果}    批量修改告警级别_V2C    cSU1alarm
    Comment    should be true    ${比较结果}
    @{锂电单体随机list}    从1-15个数中随机选n个不重复的单体    ${北向协议铁锂最大数}    2
    FOR    ${锂电序号}    IN    @{锂电单体随机list}
        ${比较结果}    批量设置四种/五种告警级别    fBBMS${锂电序号}alarm
        should be true    ${比较结果}
    END

# snmp批量获取trap告警
#     [Documentation]    6h
#     写入CSV文档    FB100B3数字量和告警量获取测试    信号名称    信号值    结果
#     连接CSU
#     进行SNMP_V2/V3连接    ${snmp连接方式}
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=FBBMS
#     ${排除列表}    create list
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除告警量信号}    ${排除列表}    2    1    0    2
#     ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    fBBMS    alarm    True
#     FOR    ${i}    IN    @{snmp待测}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${节点名称}    Get From Dictionary    ${i}    signal_node
#         ${信号序号}    Get From Dictionary    ${i}    Vindex
#         Run Keyword And Continue On Failure    南向RS485子设备告警量产生封装判断结果    snmp    smartli    ${信号名称}    只读    ${告警产生}    FB100B3数字量和告警量获取测试    FBBMS    ${节点名称}    ${信号序号}    null    null    null    null
#         Run Keyword And Continue On Failure    南向RS485子设备告警量恢复封装判断结果    snmp    smartli    ${信号名称}    只读    ${告警恢复}    FB100B3数字量和告警量获取测试    FBBMS    ${节点名称}    ${信号序号}    null    null    null    null
#     END
#     断开连接SNMP

snmp批量获取trap告警
    [Tags]    trap
    [Documentation]    6h
    写入CSV文档    FB100B3数字量和告警量获取测试    信号名称    信号值    结果
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=FBBMS
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除告警量信号}    ${排除列表}    2    1    0    2
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    fBBMS    alarm    True
    ${信号名称列表1}    Create List
    FOR    ${i}    IN    @{snmp待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    signal_node
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
        Set To Dictionary    ${dict}     snmp_name     ${节点名称}
        Set To Dictionary    ${dict}     Vindex     ${信号序号}
        Set To Dictionary    ${dict}     device_name     FBBMS
        Append To List       ${信号名称列表1}    ${dict}
    END
    Run Keyword And Continue On Failure    南向RS485子设备告警量列表产生封装判断结果    snmp    smartli    ${信号名称列表1}    
    ...    只读    ${告警产生}    FB100B3数字量和告警量获取测试    电池    FBBMS    
    ...    null    null    null    null    null    null
    Run Keyword And Continue On Failure    南向RS485子设备告警量列表恢复封装判断结果    snmp    smartli    ${信号名称列表1}    
    ...    只读    ${告警恢复}    FB100B3数字量和告警量获取测试    电池    FBBMS
    ...    null    null    null    null    null    null
    断开连接SNMP
