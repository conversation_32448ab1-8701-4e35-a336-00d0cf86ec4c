*** Settings ***
Suite Setup       整流器测试前置条件
Suite Teardown    整流器测试结束条件

Resource          ../../../测试用例关键字.robot

*** Test Cases ***
snmp_0004_rectifierGroup1analogData√
    ${比较结果}    对比数据_V2C    rectifierGroup1analogData
    should be true    ${比较结果}

snmp_0006_rectifierGroup1digitalData√
    ${比较结果}    对比数据_V2C    rectifierGroup1digitalData
    should be true    ${比较结果}

snmp_0008_rectifierGroup1alarm√
    ${比较结果}    对比告警_V2C    rectifierGroup1alarm
    should be true    ${比较结果}

snmp_0010_rectifierGroup1alarm_level√
    ${比较结果}    对比告警级别_V2C    rectifierGroup1alarm
    should be true    ${比较结果}

snmp_0012_rectifierGroup1alarm_level_write√
    Comment    ${比较结果}    批量修改告警级别_V2C    powerSystem1alarm
    Comment    should be true    ${比较结果}
    ${比较结果}    批量设置四种/五种告警级别    rectifierGroup1alarm
    should be true    ${比较结果}

snmp_0014_rectifierGroup1alarm_relay√
    ${比较结果}    对比告警干接点_V2C    rectifierGroup1alarm
    should be true    ${比较结果}

snmp_0016_rectifierGroup1alarm_relay_write√
    ${比较结果}    批量修改告警干接点_V2C    rectifierGroup1alarm
    should be true    ${比较结果}

snmp_0018_rectifierGroup1parameterX
    [Documentation]    currentWalk-InTimevalue 电流缓启动时间 0 -2147483648 8 .1.3.6.1.4.1.3902.2800.4.8.1.5.14.1 F
    [Setup]    Run keywords    Wait Until Keyword Succeeds    10    2    设置web参数量    电流缓启动使能    允许
    ...    AND    设置web参数量    交流节能模式    节能
    ${比较结果}    对比数据_V2C    rectifierGroup1parameter
    should be true    ${比较结果}
    [Teardown]    Wait Until Keyword Succeeds    10    2    设置web设备参数量为默认值    电流缓启动使能    交流节能模式

snmp_0020_rectifierGroup1parameter_writeX
    [Documentation]    需要设置 节能模式、电流缓启动使能
    Comment    ${比较结果}    批量修改参数_V2C    powerSystem1parameter
    Comment    should be true    ${比较结果}
    ${比较结果}    SNMP批量参数设置    rectifierGroup1parameter
    should be true    ${比较结果}
    [Teardown]    Run keywords    连接CSU
    ...    AND    Wait Until Keyword Succeeds    30    1    设置web设备参数量为默认值    交流节能模式    电流缓启动使能

snmp_0022_rectifierGroup1recordDataX
    [Setup]    Wait Until Keyword Succeeds    10    2    设置web参数量    交流节能模式    节能
    ${比较结果}    对比数据_V2C    rectifierGroup1recordData
    should be true    ${比较结果}
    [Teardown]    Wait Until Keyword Succeeds    10    2    设置web设备参数量为默认值    交流节能模式

snmp_0024_rectifierGroup1control_autoSaveControl
    [Documentation]    交流节能状态：
    ...
    ...    0:安全/Safe;1:自由/Free Mode;2:自动非节能/Auto NonSave;3:自动节能/Auto Save;4:暂时非节能/Temp NonSave;5:永久非节能/Perm NonSave;6:人工维护检测/Manual
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-2    否    #为保证整流器数据获取正常
    Wait Until Keyword Succeeds    10X    2    设置web参数量    交流节能模式    节能
    设置web参数量    整流器最小开机数量    1
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${设置控制量}    设置SNMP控制量    autoSaveControlvalue
    Comment    ${设置控制量}    设置SNMP控制量    autoSaveControlvalue
    Comment    should be true    ${设置控制量}
    sleep    1m
    ${交流节能状态}    获取web实时数据    交流节能状态
    log    ${交流节能状态}
    Comment    should be equal    ${交流节能状态}    自动非节能
    sleep    20s
    ${snmp该参数值}    获取SNMP数据_单个    rectifierGroupACSaveEnergyStatusvalue
    should be equal    ${snmp该参数值}    2
    断开连接SNMP
    ###增加判断准确性
    ${存在结果}    判断web历史记录存在snmp控制内容    整流器组    自动节能控制    120
    should be true    ${存在结果}
    [Teardown]    Run keywords    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3

snmp_0026_rectifierGroup1control_temporaryNonSaveControl
    [Documentation]    交流节能状态：
    ...
    ...    0:安全/Safe;1:自由/Free Mode;2:自动非节能/Auto NonSave;3:自动节能/Auto Save;4:暂时非节能/Temp NonSave;5:永久非节能/Perm NonSave;6:人工维护检测/Manual
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-2    否    #为保证整流器数据获取正常
    Wait Until Keyword Succeeds    10X    2    设置web参数量    交流节能模式    节能
    sleep    30
    ${交流节能状态}    获取web实时数据    交流节能状态
    log    ${交流节能状态}
    设置web参数量    整流器最小开机数量    1
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${设置控制量}    设置SNMP控制量    temporaryNonSaveControlvalue
    Comment    should be true    ${设置控制量}
    sleep    5
    ${交流节能状态}    获取web实时数据    交流节能状态
    log    ${交流节能状态}
    Comment    should be equal    ${交流节能状态}    自动非节能
    ${snmp该参数值}    获取SNMP数据_单个    rectifierGroupACSaveEnergyStatusvalue
    should be equal    ${snmp该参数值}    4
    断开连接SNMP
    ###增加判断准确性
    ${存在结果}    判断web历史记录存在snmp控制内容    整流器组    暂时非节能控制    60
    should be true    ${存在结果}
    [Teardown]    Run keywords    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3

snmp_0028_rectifierGroup1control_permanentNonSaveControl
    [Documentation]    交流节能状态：
    ...
    ...    0:安全/Safe;1:自由/Free Mode;2:自动非节能/Auto NonSave;3:自动节能/Auto Save;4:暂时非节能/Temp NonSave;5:永久非节能/Perm NonSave;6:人工维护检测/Manual
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-2    否    #为保证整流器数据获取正常
    Wait Until Keyword Succeeds    10X    2    设置web参数量    交流节能模式    节能
    sleep    30
    设置web参数量    整流器最小开机数量    1
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${设置控制量}    设置SNMP控制量    permanentNonSaveControlvalue
    Comment    should be true    ${设置控制量}
    sleep    20s
    ${交流节能状态}    获取web实时数据    交流节能状态
    log    ${交流节能状态}
    Comment    should be equal    ${交流节能状态}    自动非节能
    ${snmp该参数值}    获取SNMP数据_单个    rectifierGroupACSaveEnergyStatusvalue
    should be equal    ${snmp该参数值}    5
    断开连接SNMP
    ###增加判断准确性
    ${存在结果}    判断web历史记录存在snmp控制内容    整流器组    永久非节能控制    120
    should be true    ${存在结果}
    [Teardown]    Run keywords    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3

snmp_0030_rectifierGroup1control_manualDetect
    [Documentation]    交流节能状态：
    ...
    ...    0:安全/Safe;1:自由/Free Mode;2:自动非节能/Auto NonSave;3:自动节能/Auto Save;4:暂时非节能/Temp NonSave;5:永久非节能/Perm NonSave;6:人工维护检测/Manual
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-2    否    #为保证整流器数据获取正常
    Wait Until Keyword Succeeds    10X    2    设置web参数量    交流节能模式    节能
    设置web参数量    整流器最小开机数量    1
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${设置控制量}    设置SNMP控制量    manualDetectvalue
    Comment    should be true    ${设置控制量}
    sleep    20s
    ${交流节能状态}    获取web实时数据    交流节能状态
    log    ${交流节能状态}
    Comment    should be equal    ${交流节能状态}    自动非节能
    ${snmp该参数值}    获取SNMP数据_单个    rectifierGroupACSaveEnergyStatusvalue
    should be equal    ${snmp该参数值}    6
    断开连接SNMP
    ###增加判断准确性
    ${存在结果}    判断web历史记录存在snmp控制内容    整流器组    人工维护检测    120
    should be true    ${存在结果}
    [Teardown]    Run keywords    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3

snmp_0032_rectifierGroup1control_sMRDeviceStatistic√
    [Documentation]    交流节能状态：
    ...
    ...    0:安全/Safe;1:自由/Free Mode;2:自动非节能/Auto NonSave;3:自动节能/Auto Save;4:暂时非节能/Temp NonSave;5:永久非节能/Perm NonSave;6:人工维护检测/Manual
    连接CSU
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流节能模式    自由
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${设置控制量}    设置SNMP控制量    sMRDeviceStatisticvalue
    should be true    ${设置控制量}
    sleep    5
    ###增加判断准确性
    ${存在结果}    判断web历史记录存在snmp控制内容    整流器组    SMR设备统计    60
    should be true    ${存在结果}
    断开连接SNMP
    [Teardown]    Run keywords    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3

snmp_0034_rectifierGroup1control_startAutoSaveModeManually
    [Documentation]    交流节能状态：
    ...
    ...    0:安全/Safe;1:自由/Free Mode;2:自动非节能/Auto NonSave;3:自动节能/Auto Save;4:暂时非节能/Temp NonSave;5:永久非节能/Perm NonSave;6:人工维护检测/Manual
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-2    否    #为保证整流器数据获取正常
    Wait Until Keyword Succeeds    10X    2    设置web参数量    交流节能模式    节能
    设置web参数量    整流器最小开机数量    1
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${设置控制量}    设置SNMP控制量    startAutoSaveModeManuallyvalue
    Comment    should be true    ${设置控制量}
    Comment    sleep    5
    Comment    ###增加判断准确性
    Comment    ${存在结果}    判断web历史记录存在snmp控制内容    整流器组    开启自动节能模式    120
    Comment    should be true    ${存在结果}
    断开连接SNMP
    [Teardown]    Run keywords    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3
