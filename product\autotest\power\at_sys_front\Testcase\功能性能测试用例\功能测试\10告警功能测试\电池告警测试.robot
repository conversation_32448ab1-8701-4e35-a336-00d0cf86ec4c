*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
一次下电告警测试
    [Documentation]    一次下电告警不能设置为屏蔽级别，因此注释掉设置告警级别为0的语句。
    [Setup]    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息不为    一次下电告警
    关闭交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    一次下电告警
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
        设置web参数量    电池组容量_${i}    0
    END
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    99
    sleep    2
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    30    1    设置web参数量    下电模式    电池电压    #默认1电池电压
    Wait Until Keyword Succeeds    30    1    设置web参数量    下电控制延时    0    #默认0
    Wait Until Keyword Succeeds    30    1    设置web参数量    负载一次下电使能    允许    #默认1允许
    Wait Until Keyword Succeeds    30    1    设置web参数量    负载二次下电使能    禁止    #默认1允许
    ${可设置范围}    获取web参数可设置范围    一次下电恢复回差
    Wait Until Keyword Succeeds    10    1    设置web参数量    一次下电恢复回差    ${可设置范围}[0]
    显示属性配置    一次下电控制状态    数字量    web_attr=On    gui_attr=On
    ${恢复时间可设置范围}    获取web参数可设置范围    一次下电恢复时间
    run keyword and ignore error    设置web参数量    一次下电恢复时间    ${恢复时间可设置范围}[0]
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${可设置范围}[1]
    ${电压低可设置范围}    获取web参数可设置范围    电池电压低阈值
    设置web参数量    电池电压低阈值    ${电压低可设置范围}[1]
    ${电池下电电压可设置范围}    获取web参数可设置范围    负载一次下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载一次下电电压    ${电池下电电压可设置范围}[1]
    设置负载电压电流    53.5    10
    打开负载输出
    ${一次下电电压设置值}    获取web参数量    负载一次下电电压
    ${一次下电电压}    evaluate    ${一次下电电压设置值}-0.5
    向下调节电池电压    ${一次下电电压}
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    一次下电控制状态    动作
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    一次下电告警    ${告警级别设置}
        sleep    3
        wait until keyword succeeds    1m    1    查询指定告警信息    一次下电告警
        ${一次下电告警级别}    获取web告警属性    一次下电告警    告警级别
        should be equal    '${告警级别设置}'    '${一次下电告警级别}'
    END
    # @{DOlist}    Create List    1    2    3    4    5    6    7    8
    # 设置8路DODI短接配置    ${DOlist}    ${plat.Inrelay1Status} 
    Wait Until Keyword Succeeds    30    1    设置web参数量    一次下电告警    严重    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    FOR    ${告警干接点设置}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    一次下电告警    告警干接点
        run keyword if    ${告警干接点获取}!=0    Wait Until Keyword Succeeds    30    1    设置web参数量    一次下电告警干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        Wait Until Keyword Succeeds    30    1    设置web参数量    一次下电告警干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    一次下电告警    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
    END
    向上调节电池电压    53.5
    关闭负载输出
    打开交流源输出
    Wait Until Keyword Succeeds    8m    1    信号量数据值为    一次下电控制状态    恢复
    Wait Until Keyword Succeeds    8m    2    查询指定告警信息不为    一次下电告警
    显示属性配置    一次下电控制状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    负载一次下电电压    测试终止电压    电池电压低阈值    一次下电恢复时间
    ...    AND    设置web参数量    一次下电告警干接点    0
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出
    ...    AND    重置电池模拟器输出
    # 

二次下电告警测试
    [Documentation]    二次下电告警不能设置为屏蔽级别，因此注释掉设置告警级别为0的语句。
    [Setup]    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息不为    二次下电告警
    关闭交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    二次下电告警
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
        设置web参数量    电池组容量_${i}    0
    END
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    99
    sleep    2
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    100
    wait until keyword succeeds    30    1    设置web参数量    下电模式    电池电压    #默认1电池电压
    wait until keyword succeeds    30    1    设置web参数量    下电控制延时    0    #默认0
    wait until keyword succeeds    30    1    设置web参数量    负载一次下电使能    禁止    #默认1允许
    wait until keyword succeeds    30    1    设置web参数量    负载二次下电使能    允许    #默认1允许
    ${可设置范围}    获取web参数可设置范围    二次下电恢复回差
    Wait Until Keyword Succeeds    10    1    设置web参数量    二次下电恢复回差    ${可设置范围}[0]
    显示属性配置    二次下电控制状态    数字量    web_attr=On    gui_attr=On
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${可设置范围}[1]
    ${电压低可设置范围}    获取web参数可设置范围    电池电压低阈值
    设置web参数量    电池电压低阈值    ${电压低可设置范围}[1]
    ${恢复时间可设置范围}    获取web参数可设置范围    二次下电恢复时间
    run keyword and ignore error    设置web参数量    二次下电恢复时间    ${恢复时间可设置范围}[0]
    ${二次下电电压可设置范围}    获取web参数可设置范围    负载二次下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电电压    ${二次下电电压可设置范围}[1]
    设置负载电压电流    53.5    10
    打开负载输出
    ${下电电压设置值}    获取web参数量    负载二次下电电压
    ${下电电压}    evaluate    ${下电电压设置值}-0.5
    向下调节电池电压    ${下电电压}
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    二次下电控制状态    动作
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    二次下电告警    ${告警级别设置}
        sleep    3
        wait until keyword succeeds    1m    1    查询指定告警信息    二次下电
        ${二次下电告警级别}    获取web告警属性    二次下电告警    告警级别
        should be equal    '${告警级别设置}'    '${二次下电告警级别}'
    END
    # @{DOlist}    Create List    1    2    3    4    5    6    7    8
    # 设置8路DODI短接配置    ${DOlist}    ${plat.Inrelay1Status} 
    wait until keyword succeeds    30    1    设置web参数量    二次下电告警    严重    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    FOR    ${告警干接点设置}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    二次下电告警    告警干接点
        run keyword if    ${告警干接点获取}!=0    wait until keyword succeeds    30    1    设置web参数量    二次下电告警干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        wait until keyword succeeds    30    1    设置web参数量    二次下电告警干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    二次下电告警    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
    END
    向上调节电池电压    53.5
    关闭负载输出
    打开交流源输出
    Wait Until Keyword Succeeds    8m    1    信号量数据值为    二次下电控制状态    恢复
    Wait Until Keyword Succeeds    8m    2    查询指定告警信息不为    二次下电告警
    显示属性配置    二次下电控制状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    负载二次下电电压    测试终止电压    电池电压低阈值    负载一次下电使能    二次下电恢复时间
    ...    AND    设置web参数量    二次下电告警干接点    0
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出
    ...    AND    重置电池模拟器输出
    # 

电池下电告警测试
    [Documentation]    电池下电告警不能设置为屏蔽级别，因此注释掉设置告警级别为0的语句。
    [Setup]    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    5
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息不为    电池下电告警
    关闭交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    电池下电告警
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
        设置web参数量    电池组容量_${i}    0
    END
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    99
    sleep    2
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电模式    电池电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电控制延时    0
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载一次下电使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池下电使能    允许
    ${可设置范围}    获取web参数可设置范围    电池下电恢复回差
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池下电恢复回差    ${可设置范围}[0]
    显示属性配置    电池下电控制状态    数字量    web_attr=On    gui_attr=On
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${可设置范围}[1]
    ${电压低可设置范围}    获取web参数可设置范围    电池电压低阈值
    设置web参数量    电池电压低阈值    ${电压低可设置范围}[1]
    ${下电电压可设置范围}    获取web参数可设置范围    电池下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池下电电压    ${下电电压可设置范围}[1]
    设置负载电压电流    53.5    10
    打开负载输出
    ${下电电压设置值}    获取web参数量    电池下电电压
    ${下电电压}    evaluate    ${下电电压设置值}-0.5
    向下调节电池电压    ${下电电压}
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池下电控制状态    动作
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    电池下电告警    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池下电告警
        sleep    5
        ${电池下电告警级别}    获取web告警属性    电池下电告警    告警级别
        should be equal    ${告警级别}    ${电池下电告警级别}
    END
    # @{DOlist}    Create List    1    2    3    4    5    6    7    8
    # 设置8路DODI短接配置    ${DOlist}    ${plat.Inrelay1Status} 
    设置web参数量    电池下电告警    严重
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    电池下电告警    告警干接点
        run keyword if    ${告警干接点获取}!=0    设置web参数量    电池下电告警干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        设置web参数量    电池下电告警干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    电池下电告警    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
    END
    向上调节电池电压    53.5
    关闭负载输出
    打开交流源输出
    Wait Until Keyword Succeeds    15m    1    信号量数据值为    电池下电控制状态    恢复
    Wait Until Keyword Succeeds    8m    2    查询指定告警信息不为    电池下电告警
    显示属性配置    电池下电控制状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    电池下电电压    测试终止电压    电池电压低阈值    负载一次下电使能    负载二次下电使能
    ...    AND    设置web参数量    电池下电告警干接点    0
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出
    ...    AND    重置电池模拟器输出

电池检测异常测试
    [Documentation]    电池检测异常后再次执行电池检测以便消除电池检测异常告警。
    [Setup]   重置电池模拟器输出
    电池管理初始化
    Wait Until Keyword Succeeds    10    2s    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池检测异常    次要
    关闭电池模拟器输出
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    电池检测异常
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池检测异常    屏蔽
    sleep    5
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    电池检测异常
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    电池检测异常    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池检测异常
        sleep    3
        ${电池检测异常告警级别}    获取web告警属性    电池检测异常    告警级别
        should be equal    ${告警级别设置}    ${电池检测异常告警级别}
    END
    # @{DOlist}    Create List    1    2    3    4    5    6    7    8
    # 设置8路DODI短接配置    ${DOlist}    ${plat.Inrelay1Status} 
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池检测异常    次要
    FOR    ${告警干接点设置}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    电池检测异常    告警干接点
        run keyword if    ${告警干接点获取}!=0    设置web参数量    电池检测异常干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        设置web参数量    电池检测异常干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    电池检测异常    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
    END
    重置电池模拟器输出
    Wait Until Keyword Succeeds    5m    2s    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    电池检测异常
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    电池检测异常
    Wait Until Keyword Succeeds    2m    2    设置web控制量    启动浮充
    [Teardown]    Run keywords    设置web参数量    电池检测异常    次要
    ...    AND    设置web参数量    电池检测异常干接点    0

电池测试告警测试
    电池管理初始化
    Wait Until Keyword Succeeds    5m    2    设置web参数量    电池测试    严重
    Wait Until Keyword Succeeds    5m    2s    设置web控制量    启动测试
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    测试
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    电池测试
    Wait Until Keyword Succeeds    5m    2    设置web参数量    电池测试    屏蔽
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    电池测试
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    电池测试    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池测试
        sleep    3
        ${电池测试告警级别}    获取web告警属性    电池测试    告警级别
        should be equal    '${告警级别设置}'    '${电池测试告警级别}'
    END
    wait until keyword succeeds    30    1    设置web参数量    电池测试    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    FOR    ${告警干接点设置}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    电池测试    告警干接点
        run keyword if    ${告警干接点获取}!=0    wait until keyword succeeds    30    1    设置web参数量    电池测试干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        wait until keyword succeeds    30    1    设置web参数量    电池测试干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    电池测试    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
    END
    Wait Until Keyword Succeeds    2m    2    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池测试
    [Teardown]    Run keywords    设置web参数量    电池测试    次要
    ...    AND    设置web参数量    电池测试干接点    0

电池均充告警测试
    [Documentation]    ==20190327-F：启动电池均充后因电池组异常退出均充，无电池均充告警
    电池管理初始化
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池均充    次要
    Wait Until Keyword Succeeds    10    1    设置web控制量    启动均充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    均充
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    电池均充
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池均充    屏蔽
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    电池均充
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    电池均充    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池均充
        sleep    3
        ${电池均充告警级别}    获取web告警属性    电池均充    告警级别
        should be equal    ${告警级别设置}    ${电池均充告警级别}
    END
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池均充    次要
    FOR    ${告警干接点设置}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    电池均充    告警干接点
        run keyword if    ${告警干接点获取}!=0    设置web参数量    电池均充干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        设置web参数量    电池均充干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    电池均充    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
    END
    Wait Until Keyword Succeeds    10    2    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池均充
    [Teardown]    Run keywords    设置web参数量    电池均充    次要
    ...    AND    设置web参数量    电池均充干接点    0

电池放电告警测试
    电池管理初始化
    #获取告警级别和输出干接点设置
    ${电池放电级别}    获取web参数量    电池放电
    ${电池放电干接点}    获取web参数量    电池放电干接点
    run keyword if    '${电池放电级别}'=='屏蔽'    设置web参数量    电池放电    严重
    run keyword if    '${电池放电干接点}'=='无干接点'    设置web参数量    电池放电干接点    A1
    sleep    3
    ${电池放电级别}    获取web参数量    电池放电
    ${电池放电干接点}    获取web参数量    电池放电干接点
    ${干接点序号}    strip string    ${电池放电干接点}    characters=A
    ${放电阈值获取范围}    获取web参数上下限范围    电池放电阈值
    设置web参数量    电池放电阈值    ${放电阈值获取范围}[0]
    关闭交流源输出
    设置负载电压电流    53.5    20
    打开负载输出
    #电池放电
    run keyword and ignore error    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    系统停电
    ${电池电流}    获取web实时数据    电池电流-1
    wait until keyword succeeds    3m    2    判断告警存在    电池放电
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    电池放电    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池放电
        sleep    5
        ${获取告警级别}    获取web告警属性    电池放电-1    告警级别
        should be equal    ${告警级别}    ${获取告警级别}
    END
    设置web参数量    电池放电    主要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    电池放电-1    告警干接点
        run keyword if    ${告警干接点获取}!=0    设置web参数量    电池放电干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        设置web参数量    电池放电干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    电池放电-1    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
    END
    #恢复
    打开交流源输出
    关闭负载输出
    Wait Until Keyword Succeeds    30    1    信号量数据值不为    电池管理状态    系统停电
    wait until keyword succeeds    3m    2    判断告警不存在    电池放电
    [Teardown]    Run keywords    设置web设备参数量为默认值    电池放电阈值
    ...    AND    设置web参数量    电池放电    主要
    ...    AND    设置web参数量    电池放电干接点    0
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出

电池电压低告警测试
    关闭交流源输出
    实时告警刷新完成
    #获取参数/默认值
    ${干接点上下限}    获取web参数量    电池电压低干接点
    ${告警干接点默认值}    run keyword if    '${干接点上下限}'=='0'    set variable    无干接点
    ...    ELSE    set variable    A1
    #获取告警级别和输出干接点设置值
    ${级别设置值}    获取web参数量    电池电压低
    ${干接点设置值}    获取web参数量    电池电压低干接点
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池电压低    严重
    ${电压低告警}    判断告警不存在_带返回值    电池电压低
    run keyword if    '${电压低告警}' != '[]'    向上调节电池电压    53.5
    ${可设置范围}    获取web参数可设置范围    电池电压低阈值
    设置web参数量    电池电压低阈值    ${可设置范围}[1]
    ${ 电压低阈值}    获取web参数量    电池电压低阈值
    向下调节电池电压    ${ 电压低阈值}-0.5
    wait until keyword succeeds    5m    2    查询指定告警信息    电池电压低
    wait until keyword succeeds    30    1    设置web参数量    电池电压低    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    电池电压低
    ${电压低告警}    判断告警存在_带返回值    电池电压低
    should not be true    ${电压低告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    电池电压低    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池电压低
        sleep    5
        ${直流电压低告警级别}    获取web告警属性    电池电压低-1    告警级别
        should be equal    ${告警级别}    ${直流电压低告警级别}
    END
    设置web参数量    电池电压低    主要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    电池电压低-1    告警干接点
        run keyword if    ${告警干接点获取}!=0    设置web参数量    电池电压低干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        设置web参数量    电池电压低干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    电池电压低-1    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0
    END
    向上调节电池电压    ${ 电压低阈值}+1
    wait until keyword succeeds    5m    1    判断告警不存在    电池电压低
    [Teardown]    Run keywords    设置web设备参数量为默认值    电池电压低阈值
    ...    AND    设置web参数量    电池电压低    主要
    ...    AND    设置web参数量    电池电压低干接点    0
    ...    AND    打开交流源输出

电池电压过低告警测试
    关闭交流源输出
    实时告警刷新完成
    #获取参数/默认值
    ${干接点上下限}    获取web参数量    电池电压过低干接点
    ${告警干接点默认值}    run keyword if    '${干接点上下限}'=='0'    set variable    无干接点
    ...    ELSE    set variable    A1
    #获取告警级别和输出干接点设置值
    ${级别设置值}    获取web参数量    电池电压过低
    ${干接点设置值}    获取web参数量    电池电压过低干接点
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池电压过低    严重
    ${电压低告警}    判断告警不存在_带返回值    电池电压过低
    run keyword if    '${电压低告警}' != '[]'    向上调节电池电压    53.5
    ${可设置范围}    获取web参数可设置范围    电池电压过低阈值
    设置web参数量    电池电压过低阈值    ${可设置范围}[1]
    ${ 电压低阈值}    获取web参数量    电池电压过低阈值
    向下调节电池电压    ${ 电压低阈值}-0.5
    wait until keyword succeeds    5m    2    查询指定告警信息    电池电压过低
    wait until keyword succeeds    30    1    设置web参数量    电池电压过低    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    电池电压过低
    ${电压低告警}    判断告警存在_带返回值    电池电压过低
    should not be true    ${电压低告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    电池电压过低    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池电压过低
        sleep    5
        ${直流电压低告警级别}    获取web告警属性    电池电压过低-1    告警级别
        should be equal    ${告警级别}    ${直流电压低告警级别}
    END
    设置web参数量    电池电压过低    主要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    电池电压过低-1    告警干接点
        run keyword if    ${告警干接点获取}!=0    设置web参数量    电池电压过低干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        设置web参数量    电池电压过低干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    电池电压过低-1    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0
    END
    向上调节电池电压    ${ 电压低阈值}+1
    wait until keyword succeeds    5m    1    判断告警不存在    电池电压过低
    [Teardown]    Run keywords    设置web设备参数量为默认值    电池电压过低阈值
    ...    AND    设置web参数量    电池电压过低    主要
    ...    AND    设置web参数量    电池电压过低干接点    0
    ...    AND    打开交流源输出

电池测试失败告警测试
    电池管理初始化
    #获取告警级别和输出干接点设置
    ${电池测试失败级别}    获取web参数量    电池测试失败
    ${电池测试失败干接点}    获取web参数量    电池测试失败干接点
    run keyword if    '${电池测试失败级别}'=='屏蔽'    设置web参数量    电池测试失败    严重
    run keyword if    '${电池测试失败干接点}'=='无干接点'    设置web参数量    电池测试失败干接点    A1
    sleep    3
    ${电池测试失败级别}    获取web参数量    电池测试失败
    ${电池测试失败干接点}    获取web参数量    电池测试失败干接点
    ${干接点序号}    strip string    ${电池测试失败干接点}    characters=A
    ${缺省值}    获取web参数上下限范围_有单位    测试最长时间
    ${可设置范围}    获取web参数可设置范围    测试最长时间
    ${测试最长时间设置值}    run keyword if    '${缺省值}[3]'== 'Hour'    evaluate    ${可设置范围}[0]+1
    ...    ELSE    evaluate    ${可设置范围}[0]+5
    ${测试最长时间转换后}    run keyword if    '${缺省值}[3]'== 'Hour'    evaluate    ${测试最长时间设置值}*60
    ...    ELSE    set variable    ${测试最长时间设置值}
    设置web参数量    测试最长时间    ${测试最长时间设置值}
    #断开电池，以便电池检测异常
    设置负载电压电流    53.5    15
    打开负载输出
    断开电池模拟器
    连接电池模拟器
    关闭电池模拟器输出
    sleep    3
    #启动电池测试
    Wait Until Keyword Succeeds    2m    1    设置web控制量    启动测试
    run keyword and ignore error    Wait Until Keyword Succeeds    10    1    信号量数据值为    电池管理状态    测试
    wait until keyword succeeds    3m    2    判断告警存在    电池测试失败
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    10    1    设置web参数量    电池测试失败    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池测试失败
        sleep    5
        ${电池下电告警级别}    获取web告警属性    电池测试失败-1    告警级别
        should be equal    ${告警级别}    ${电池下电告警级别}
    END
    设置web参数量    电池测试失败    主要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    电池测试失败-1    告警干接点
        run keyword if    ${告警干接点获取}!=0    设置web参数量    电池测试失败干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        设置web参数量    电池测试失败干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    电池测试失败-1    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
    END
    #恢复
    关闭负载输出
    仅电池模拟器供电
    同时设置三相电压频率    220    50
    打开交流源输出
    连接CSU
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    交流供电状态    市电1
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    停电
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    0
    设置web参数量    均充使能    允许
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动测试
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    测试
    ${等待时间}    evaluate    ${测试最长时间转换后}+2
    Wait Until Keyword Succeeds    ${等待时间}m    1    信号量数据值为    电池管理状态    均充
    wait until keyword succeeds    3m    2    判断告警不存在    电池测试失败
    Wait Until Keyword Succeeds    2m    1    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    [Teardown]    Run keywords    设置web设备参数量为默认值    测试最长时间
    ...    AND    设置web参数量    电池测试失败    主要
    ...    AND    设置web参数量    电池测试失败干接点    0
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出
    ...    AND    重置电池模拟器输出

电池温度高告警测试
    同时设置三相电压频率    220    50
    打开交流源输出
    连接CSU
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    电池温度高
    #获取告警级别和输出干接点设置值
    ${级别设置值}    获取web参数量    电池温度高
    ${干接点设置值}    获取web参数量    电池温度高干接点
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池温度高    严重
    run keyword if    '${干接点设置值}'=='无干接点'    设置web参数量    电池温度高干接点    A1
    sleep    3
    ${级别设置值}    获取web参数量    电池温度高
    ${干接点设置值}    获取web参数量    电池温度高干接点
    ${干接点序号}    strip string    ${干接点设置值}    characters=A
    #（1）告警产生
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    10    1.6    电池_1    电池温度
    #产生电池温度高告警
    ${电池温度高阈值范围}    获取web参数上下限范围    电池温度高阈值
    ${电池温度高阈值可设置范围}    获取web参数可设置范围    电池温度高阈值
    设置web参数量    电池温度高阈值    ${电池温度高阈值可设置范围}[0]
    ${电池温度}    获取web实时数据    电池温度-1
    Wait Until Keyword Succeeds    10    1    判断告警存在    电池温度高
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    电池温度高    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池温度高
        sleep    3
        ${告警级别}    获取web告警属性    电池温度高-1    告警级别
        should be equal    ${告警级别设置}    ${告警级别}
    END
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池温度高    主要
    FOR    ${告警干接点设置}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    电池温度高-1    告警干接点
        run keyword if    ${告警干接点获取}!=0    设置web参数量    电池温度高干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        设置web参数量    电池温度高干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    电池温度高-1    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
    END
    #（2）告警恢复
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    0    1    电池_1    电池温度
    设置web参数量    电池温度高阈值    ${电池温度高阈值范围}[0]
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池温度高
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池温度高阈值
    ...    AND    设置web参数量    电池温度高    主要
    ...    AND    设置web参数量    电池温度高干接点    0

电池温度低告警测试
    同时设置三相电压频率    220    50
    打开交流源输出
    连接CSU
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    电池温度低
    #获取告警级别和输出干接点设置值
    ${级别设置值}    获取web参数量    电池温度低
    ${干接点设置值}    获取web参数量    电池温度低干接点
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池温度低    严重
    run keyword if    '${干接点设置值}'=='无干接点'    设置web参数量    电池温度低干接点    A1
    sleep    3
    ${级别设置值}    获取web参数量    电池温度低
    ${干接点设置值}    获取web参数量    电池温度低干接点
    ${干接点序号}    strip string    ${干接点设置值}    characters=A
    #（1）告警产生
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    -10    0.001    电池_1    电池温度
    #产生电池温度低告警
    ${电池温度低阈值范围}    获取web参数上下限范围    电池温度低阈值
    ${电池温度低阈值可设置范围}    获取web参数可设置范围    电池温度低阈值
    设置web参数量    电池温度低阈值    ${电池温度低阈值可设置范围}[1]
    ${电池温度}    获取web实时数据    电池温度-1
    Wait Until Keyword Succeeds    10    1    判断告警存在    电池温度低
    sleep    3
    #只是通过调节温度零点来改变温度，如果室温较高时，无法达到告警点，视为正常
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    电池温度低    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池温度低
        sleep    3
        ${告警级别}    获取web告警属性    电池温度低-1    告警级别
        should be equal    ${告警级别设置}    ${告警级别}
    END

    Wait Until Keyword Succeeds    10    2    设置web参数量    电池温度低    主要
    FOR    ${告警干接点设置}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    电池温度低-1    告警干接点
        run keyword if    ${告警干接点获取}!=0    设置web参数量    电池温度低干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        设置web参数量    电池温度低干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    电池温度低-1    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
    END
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    0    1    电池_1    电池温度
    #（2）告警恢复
    设置web参数量    电池温度低阈值    ${电池温度低阈值范围}[0]
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池温度低
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池温度低阈值
    ...    AND    设置web参数量    电池温度低    主要
    ...    AND    设置web参数量    电池温度低干接点    0

电池丢失告警测试

    连接CSU
    #获取参数/默认值
    ${干接点上下限}    获取web参数量    电池丢失干接点
    ${告警干接点默认值}    run keyword if    '${干接点上下限}'=='0'    set variable    无干接点
    ...    ELSE    set variable    A1
    #获取告警级别和输出干接点设置值
    ${级别设置值}    获取web参数量    电池丢失
    ${干接点设置值}    获取web参数量    电池丢失干接点
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池丢失    严重
    ${DI2通道配置}    获取通道配置    ${plat.Inrelay2Status}
    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay2Status}    电池_1    电池丢失    断开
    Comment    Wait Until Keyword Succeeds    5m    1    判断告警不存在    电池丢失
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    电池丢失    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池丢失
        sleep    3
        ${告警级别}    获取web告警属性    电池丢失-1    告警级别
        should be equal    '${告警级别设置}'    '${告警级别}'
    END
    
    wait until keyword succeeds    30    1    设置web参数量    电池丢失    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    FOR    ${告警干接点设置}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    电池丢失-1    告警干接点
        run keyword if    ${告警干接点获取}!=0    wait until keyword succeeds    30    1    设置web参数量    电池丢失干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        wait until keyword succeeds    30    1    设置web参数量    电池丢失干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    电池丢失-1    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
    END
    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay2Status}    ${DI2通道配置}[1]    ${DI2通道配置}[2]    ${DI2通道配置}[3]
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池丢失
    [Teardown]    Run keywords    设置web参数量    电池丢失    主要
    ...    AND    设置web参数量    电池丢失干接点    0
    ...    AND    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay2Status}    ${DI2通道配置}[1]    ${DI2通道配置}[2]    ${DI2通道配置}[3]
    

电池组丢失告警测试
    
    连接CSU
    #获取参数/默认值
    ${干接点上下限}    获取web参数量    电池组丢失干接点
    ${告警干接点默认值}    run keyword if    '${干接点上下限}'=='0'    set variable    无干接点
    ...    ELSE    set variable    A1
    #获取告警级别和输出干接点设置值
    ${级别设置值}    获取web参数量    电池组丢失
    ${干接点设置值}    获取web参数量    电池组丢失干接点
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池组丢失    严重
    ${DI2通道配置}    获取通道配置    ${plat.Inrelay2Status}
    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay2Status}    电池组    电池组丢失    断开
    Comment    Wait Until Keyword Succeeds    5m    1    判断告警不存在    电池组丢失
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    电池组丢失    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池组丢失
        sleep    3
        ${告警级别}    获取web告警属性    电池组丢失    告警级别
        should be equal    '${告警级别设置}'    '${告警级别}'
    END
    
    wait until keyword succeeds    30    1    设置web参数量    电池组丢失    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    FOR    ${告警干接点设置}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    电池组丢失    告警干接点
        run keyword if    ${告警干接点获取}!=0    wait until keyword succeeds    30    1    设置web参数量    电池组丢失干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        wait until keyword succeeds    30    1    设置web参数量    电池组丢失干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    电池组丢失    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
    END
    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay2Status}    ${DI2通道配置}[1]    ${DI2通道配置}[2]    ${DI2通道配置}[3]
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池组丢失
    [Teardown]    Run keywords    设置web参数量    电池组丢失    主要
    ...    AND    设置web参数量    电池组丢失干接点    0
    ...    AND    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay2Status}    ${DI2通道配置}[1]    ${DI2通道配置}[2]    ${DI2通道配置}[3]
    

电池温度无效告警测试
    
    #电池2不接入传感器
    设置通道无效值/恢复通道原始值    ${plat.batttemp2}    1
    实时告警刷新完成
    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池组容量_2    100
    设置web参数量    电池温度无效    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    电池温度无效
    ${电池温度无效告警}    判断告警存在_带返回值    电池温度无效
    should not be true    ${电池温度无效告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    10    1    设置web参数量    电池温度无效    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池温度无效-2
        sleep    5
        ${告警级别}    获取web告警属性    电池温度无效-2    告警级别
        should be equal    ${告警级别}    ${告警级别}
    END
    @{DOlist}    Create List    1    2    3    4    5    6    7    8
    设置8路DODI短接配置    ${DOlist}    ${plat.Inrelay1Status}
    设置web参数量    电池温度无效    主要
    FOR    ${告警干接点设置}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    电池温度无效-2    告警干接点
        run keyword if    ${告警干接点获取}!=0    wait until keyword succeeds    30    1    设置web参数量    电池温度无效-2干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        wait until keyword succeeds    30    1    设置web参数量    电池温度无效-2干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    电池温度无效-2    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0
    END
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置web参数量    电池组容量_2    0
    ...    AND    设置通道无效值/恢复通道原始值    ${plat.batttemp2}    0
    ...    AND    sleep    5
    ...    AND    设置web参数量    电池温度无效    主要
    ...    AND    设置web参数量    电池温度无效干接点    0
    
    

多个下电告警测试
    

    连接CSU
    ${原始用户参数文件}    导出参数文件
    压缩文件夹    ${原始用户参数文件}
    sleep    5
    ${用户参数文件}    导出参数文件
    &{dict1}    create dictionary    id=pdt.power_subrack    para_name=F01 Det Channel App Conf    para_val=1
    &{dict2}    create dictionary    id=pdt.power_subrack    para_name=F02 Det Channel App Conf    para_val=2
    &{dict3}    create dictionary    id=pdt.power_subrack    para_name=F03 Det Channel App Conf    para_val=3
    &{dict4}    create dictionary    id=pdt.power_subrack    para_name=F04 Det Channel App Conf    para_val=4
    &{dict5}    create dictionary    id=pdt.power_subrack    para_name=F05 Det Channel App Conf    para_val=5
    &{dict6}    create dictionary    id=pdt.power_subrack    para_name=F06 Det Channel App Conf    para_val=6
    &{dict7}    create dictionary    id=pdt.power_subrack    para_name=F07 Det Channel App Conf    para_val=7
    &{dict8}    create dictionary    id=pdt.power_subrack    para_name=F08 Det Channel App Conf    para_val=9
    &{dict9}    create dictionary    id=pdt.power_subrack    para_name=F09 Det Channel App Conf    para_val=1
    &{dict10}    create dictionary    id=pdt.power_subrack    para_name=F10 Det Channel App Conf    para_val=1
    &{dict11}    create dictionary    id=pdt.power_subrack    para_name=F11 Det Channel App Conf    para_val=2
    &{dict12}    create dictionary    id=pdt.power_subrack    para_name=F12 Det Channel App Conf    para_val=3
    @{配置列表}    create list    ${dict1}    ${dict2}    ${dict3}    ${dict4}    ${dict5}    ${dict6}    ${dict7}    ${dict8}    ${dict9}    ${dict10}    ${dict11}    ${dict12}
    修改导出配置文件    ${配置列表}    ${用户参数文件}/config.xml
    压缩文件夹    ${用户参数文件}
    ${导入结果}    导入参数文件    ${用户参数文件}.zip
    sleep    4m
    连接CSU
    



    
    实时告警刷新完成
    wait until keyword succeeds    1m    1    判断告警不存在    一次下电分路断
    ${告警}    判断告警存在_带返回值    一次下电分路断
    should not be true    ${告警}
    wait until keyword succeeds    1m    1    判断告警不存在    一次下电扩展分路断
    ${告警}    判断告警存在_带返回值    一次下电扩展分路断
    should not be true    ${告警}
    wait until keyword succeeds    1m    1    判断告警不存在    二次下电分路断
    ${告警}    判断告警存在_带返回值    二次下电分路断
    should not be true    ${告警}
    wait until keyword succeeds    1m    1    判断告警不存在    二次下电扩展分路断
    ${告警}    判断告警存在_带返回值    二次下电扩展分路断
    should not be true    ${告警}
    wait until keyword succeeds    1m    1    判断告警不存在    电池下电分路断
    ${告警}    判断告警存在_带返回值    电池下电分路断
    should not be true    ${告警}
    wait until keyword succeeds    1m    1    判断告警不存在    电池下电扩展分路断
    ${告警}    判断告警存在_带返回值    电池下电扩展分路断
    should not be true    ${告警}
    模拟数字量告警    下电告警    ON
    sleep    10
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    10    1    设置web参数量    一次下电分路断_1    ${告警级别}
        wait until keyword succeeds    10    1    设置web参数量    一次下电扩展分路断    ${告警级别}
        wait until keyword succeeds    10    1    设置web参数量    二次下电分路断_1    ${告警级别}
        wait until keyword succeeds    10    1    设置web参数量    二次下电扩展分路断    ${告警级别}
        wait until keyword succeeds    10    1    设置web参数量    电池下电分路断_1    ${告警级别}
        wait until keyword succeeds    10    1    设置web参数量    电池下电扩展分路断    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    一次下电分路断
        wait until keyword succeeds    1m    1    查询指定告警信息    一次下电扩展分路断
        wait until keyword succeeds    1m    1    查询指定告警信息    二次下电分路断
        wait until keyword succeeds    1m    1    查询指定告警信息    二次下电扩展分路断
        wait until keyword succeeds    1m    1    查询指定告警信息    电池下电分路断
        wait until keyword succeeds    1m    1    查询指定告警信息    电池下电扩展分路断
        sleep    3
        ${告警级别获取}    获取web告警属性    一次下电分路断_1    告警级别
        should be equal    ${告警级别}    ${告警级别获取}
        ${告警级别获取}    获取web告警属性    一次下电扩展分路断    告警级别
        should be equal    ${告警级别}    ${告警级别获取}
        ${告警级别获取}    获取web告警属性    二次下电分路断_1    告警级别
        should be equal    ${告警级别}    ${告警级别获取}
        ${告警级别获取}    获取web告警属性    二次下电扩展分路断    告警级别
        should be equal    ${告警级别}    ${告警级别获取}
        ${告警级别获取}    获取web告警属性    电池下电分路断_1    告警级别
        should be equal    ${告警级别}    ${告警级别获取}
        ${告警级别获取}    获取web告警属性    电池下电扩展分路断    告警级别
        should be equal    ${告警级别}    ${告警级别获取}
    END
    模拟数字量告警    下电告警    OFF
    sleep    10
    wait until keyword succeeds    1m    1    查询指定告警信息不为    一次下电分路断
    wait until keyword succeeds    1m    1    查询指定告警信息不为    一次下电扩展分路断
    wait until keyword succeeds    1m    1    查询指定告警信息不为    二次下电分路断
    wait until keyword succeeds    1m    1    查询指定告警信息不为    二次下电扩展分路断
    wait until keyword succeeds    1m    1    查询指定告警信息不为    电池下电分路断
    wait until keyword succeeds    1m    1    查询指定告警信息不为    电池下电扩展分路断
    [Teardown]    run keywords    设置web设备参数量为默认值    一次下电分路断_1    一次下电扩展分路断    二次下电分路断_1    二次下电扩展分路断    电池下电分路断_1    电池下电扩展分路断
    ...    AND    模拟数字量告警    下电告警    OFF
    
    ...    AND    导入参数文件    ${原始用户参数文件}.zip
    ...    AND    sleep    4m
    ...    AND    系统复位
    ...    AND    sleep    30
    ...    AND    连接CSU

电池回路断告警测试
    [Setup]
    连接CSU
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
        设置web参数量    电池组容量_${i}    0
    END
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    99
    sleep    2
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    100
    #获取告警级别和输出干接点设置
    ${电池回路断级别}    获取web参数量    电池回路断
    ${电池回路断干接点}    获取web参数量    电池回路断干接点
    run keyword if    '${电池回路断级别}'=='屏蔽'    设置web参数量    电池回路断    严重
    run keyword if    '${电池回路断干接点}'=='无干接点'    设置web参数量    电池回路断干接点    A1
    Wait Until Keyword Succeeds    3m    1    判断告警不存在    电池回路断
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_3    100
    wait until keyword succeeds    5m    2    查询指定告警信息    电池回路断
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    电池回路断    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池回路断
        sleep    5
        ${获取告警级别}    获取web告警属性    电池回路断-3    告警级别
        should be equal    ${告警级别}    ${获取告警级别}
    END
    
    设置web参数量    电池回路断    主要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    电池回路断-3    告警干接点
        run keyword if    ${告警干接点获取}!=0    设置web参数量    电池回路断干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        设置web参数量    电池回路断干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    电池回路断-3    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
    END
    
    [Teardown]    Run keywords    设置web参数量    电池回路断    主要
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_3    0
    ...    AND    wait until keyword succeeds    3m    2    判断告警不存在    电池回路断
    ...    AND    设置web参数量    电池回路断干接点    0
    
