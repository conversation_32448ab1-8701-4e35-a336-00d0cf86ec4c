*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
环境温度高阈值设置
    [Documentation]    高阈值范围：30-60；过高阈值范围：33-63。高比过高低3°
    [Tags]    view    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    环境温度高阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    环境温度高阈值
    ${缺省值}    获取web参数上下限范围    环境温度高阈值
    ${可设置范围}    获取web参数可设置范围    环境温度高阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    环境温度高阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    环境温度高阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    环境温度高阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    环境温度高阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    环境温度参数恢复默认值

环境温度过高阈值设置
    [Documentation]    高阈值范围：30-60；过高阈值范围：33-63。高比过高低3°
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    环境温度过高阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    环境温度过高阈值
    ${缺省值}    获取web参数上下限范围    环境温度过高阈值
    ${可设置范围}    获取web参数可设置范围    环境温度过高阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    环境温度过高阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    环境温度过高阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    环境温度过高阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    环境温度过高阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    环境温度参数恢复默认值

环境温度低阈值设置
    [Documentation]    低阈值范围：-30~20，默认-5；过低阈值范围：-33~17，默认-8
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    环境温度低阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    环境温度低阈值
    ${缺省值}    获取web参数上下限范围    环境温度低阈值
    ${可设置范围}    获取web参数可设置范围    环境温度低阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    环境温度低阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    环境温度低阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    环境温度低阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    环境温度低阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    环境温度参数恢复默认值

环境温度过低阈值设置
    [Documentation]    低阈值范围：-30~20，默认-5；过低阈值范围：-33~17，默认-8
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    环境温度过低阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    环境温度过低阈值
    ${缺省值}    获取web参数上下限范围    环境温度过低阈值
    ${可设置范围}    获取web参数可设置范围    环境温度过低阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    环境温度过低阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    环境温度过低阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    环境温度过低阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    环境温度过低阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    环境温度参数恢复默认值

环境湿度高阈值设置
    [Documentation]    湿度高阈值范围：70-100，默认90；
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    环境湿度高阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    环境湿度高阈值
    ${缺省值}    获取web参数上下限范围    环境湿度高阈值
    ${可设置范围}    获取web参数可设置范围    环境湿度高阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    环境湿度高阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    环境湿度高阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    环境湿度高阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    环境湿度高阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    环境湿度低阈值    环境湿度高阈值

环境湿度低阈值设置
    [Documentation]    湿度低阈值范围：10-50，默认20
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    环境湿度低阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    环境湿度低阈值
    ${缺省值}    获取web参数上下限范围    环境湿度低阈值
    ${可设置范围}    获取web参数可设置范围    环境湿度低阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    环境湿度低阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    环境湿度低阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    环境湿度低阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    环境湿度低阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    环境湿度低阈值    环境湿度高阈值
