*** Settings ***
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp_test关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/Web关键字_V30.robot
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
snmp_0002_油机控制屏工作状态√
    [Documentation]    0:自动/Auto;1:手动/Manual;2:其他模式/Other Mode
    ...
    ...    默认为2
    连接CSU
    设置子工具值    oileng    all    只读    灯亮为    4
    sleep    20s
    ${获取值1}    获取web实时数据    油机控制器工作模式
    #
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${snmp获取值1}    获取SNMP数据_单个    dGControlerMode1value
    should be true    '${获取值1}'=='手动'
    should be true    ${snmp获取值1}==1
    设置子工具值    oileng    all    只读    灯亮为    8
    sleep    20s
    ${获取值1}    获取web实时数据    油机控制器工作模式
    ${snmp获取值1}    获取SNMP数据_单个    dGControlerMode1value
    should be true    '${获取值1}'=='自动'
    should be true    ${snmp获取值1}==0
    设置子工具值    oileng    all    只读    灯亮为    0
    sleep    20s
    ${获取值1}    获取web实时数据    油机控制器工作模式
    ${snmp获取值1}    获取SNMP数据_单个    dGControlerMode1value
    断开连接SNMP
    should be true    '${获取值1}'=='其他模式'
    should be true    ${snmp获取值1}==2
    [Teardown]    设置子工具值    oileng    all    只读    灯亮为    8

snmp_0004_油机控制屏通讯状态
    [Documentation]    0:正常/Normal;1:异常/Fault
    连接CSU
    #子设备工具模拟
    控制子工具运行停止    oileng    关闭
    sleep    1m
    ${级别设置值}    获取web参数量    油机控制屏通讯中断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机控制屏通讯中断    主要
    wait until keyword succeeds    5m    2    查询指定告警信息    油机控制屏通讯中断
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${获取值1}    获取web实时数据    油机控制屏通讯状态-1
    Comment    ${获取值2}    获取web实时数据    油机控制屏通讯状态-2
    ${snmp获取值1}    获取SNMP数据_单个    gCPCommunicationState1value
    Comment    ${snmp获取值2}    获取SNMP数据_单个    gCPCommunicationState2value
    should be true    '${获取值1}'=='异常'
    Comment    should be true    '${获取值2}'=='异常'
    should be true    ${snmp获取值1}==1
    Comment    should be true    ${snmp获取值2}==1
    控制子工具运行停止    oileng    开启
    sleep    1m
    wait until keyword succeeds    5m    2    查询指定告警信息不为    油机控制屏通讯中断
    ${获取值1}    获取web实时数据    油机控制屏通讯状态-1
    Comment    ${获取值2}    获取web实时数据    油机控制屏通讯状态-2
    ${snmp获取值1}    获取SNMP数据_单个    gCPCommunicationState1value
    Comment    ${snmp获取值2}    获取SNMP数据_单个    gCPCommunicationState2value
    should be true    '${获取值1}'=='正常'
    Comment    should be true    '${获取值2}'=='正常'
    should be true    ${snmp获取值1}==0
    Comment    should be true    ${snmp获取值2}==0
    [Teardown]    控制子工具运行停止    oileng    开启

snmp_0006_油机控制屏在位状态_无
    [Documentation]    子工具无
    ...    暂时无法自动化
    [Tags]    3
