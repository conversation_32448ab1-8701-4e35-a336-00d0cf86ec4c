*** Settings ***
Force Tags
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
下电模式为禁止时不二次电压下电
    
    铁锂电池管理初始化
    Comment    显示属性配置    二次下电控制状态    数字量    web_attr=On    gui_attr=On
    Comment    sleep    5
    设置web参数量    下电模式    电池电压
    设置web参数量    下电控制延时    0
    设置web参数量    负载二次下电使能    允许
    设置web参数量    负载一次下电使能    禁止
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${可设置范围}[1]
    ${电压低可设置范围}    run keyword and ignore error    获取web参数可设置范围    电池电压低阈值
    run keyword and ignore error    设置web参数量    电池电压低阈值    ${电压低可设置范围}[1]
    ${二次下电电压可设置范围}    获取web参数可设置范围    负载二次下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电电压    ${二次下电电压可设置范围}[1]
    ${负载二次下电电压设置值}    获取web参数量    负载二次下电电压
    设置负载电压电流    53.5    10
    打开负载输出
    设置web参数量    下电模式    禁止
    关闭交流源输出
    sleep    10
    向下调节电池电压    ${负载二次下电电压设置值}-0.5
    sleep    5m
    Comment    ${二次下电控制状态1}    获取web实时数据    二次下电控制状态
    Comment    should be equal    '${二次下电控制状态1}'    '恢复'
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    二次下电告警
    Comment    显示属性配置    二次下电控制状态    数字量    web_attr=Off    gui_attr=Off
    打开交流源输出
    向上调节电池电压    53.5
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    下电模式
    ...    负载二次下电电压    测试终止电压    负载一次下电使能
    ...    AND    run keyword and ignore error    设置web设备参数量为默认值    电池电压低阈值
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出
    ...    AND    重置电池模拟器输出

下电模式为禁止时不二次时间下电
    
    铁锂电池管理初始化
    Comment    显示属性配置    二次下电控制状态    数字量    web_attr=On    gui_attr=On
    Comment    sleep    5
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电模式    停电时间
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电控制延时    0
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电使能    允许
    设置web参数量    负载一次下电使能    禁止
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${可设置范围}[0]
    ${电压低可设置范围}    run keyword and ignore error    获取web参数可设置范围    电池电压低阈值
    run keyword and ignore error    设置web参数量    电池电压低阈值    ${电压低可设置范围}[0]
    ${二次下电电压可设置范围}    获取web参数可设置范围    负载二次下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电电压    ${二次下电电压可设置范围}[0]
    ${下电时间可设置范围}    获取web参数可设置范围    负载二次下电时间
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电时间    ${下电时间可设置范围}[0]
    ${负载二次下电时间设置值}    获取web参数量    负载二次下电时间
    设置负载电压电流    53.5    10
    打开负载输出
    设置web参数量    下电模式    禁止
    关闭交流源输出
    ${停电时间}    evaluate    ${负载二次下电时间设置值}+3
    sleep    ${停电时间}m
    Comment    ${电池下电控制状态1}    获取web实时数据    二次下电控制状态
    Comment    should be equal    '${电池下电控制状态1}'    '恢复'
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    二次下电告警
    Comment    设置web参数量    下电模式    电池电压
    Comment    显示属性配置    二次下电控制状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    Run keywords    设置web参数量    下电模式    停电时间
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    负载二次下电时间
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    下电模式
    ...    负载二次下电电压    测试终止电压    负载一次下电使能
    ...    AND    run keyword and ignore error    设置web设备参数量为默认值    电池电压低阈值
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出

下电模式为禁止时不二次容量下电
    
    铁锂电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    20
    Comment    sleep    5
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电模式    电池剩余容量
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电控制延时    0
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载一次下电使能    禁止
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${可设置范围}[0]
    ${电压低可设置范围}    run keyword and ignore error    获取web参数可设置范围    电池电压低阈值
    run keyword and ignore error    设置web参数量    电池电压低阈值    ${电压低可设置范围}[0]
    ${二次下电电压可设置范围}    获取web参数可设置范围    负载二次下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电电压    ${二次下电电压可设置范围}[0]
    ${测试终止SOC可设置范围}    获取web参数可设置范围    测试终止SOC阈值
    设置web参数量    测试终止SOC阈值    ${测试终止SOC可设置范围}[1]
    sleep    5
    ${下电容量可设置范围}    获取web参数可设置范围    负载二次下电SOC阈值
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电SOC阈值    ${下电容量可设置范围}[1]
    ${负载二次下电SOC设置值}    获取web参数量    负载二次下电SOC阈值
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    90
    打开负载输出
    设置web参数量    下电模式    禁止
    关闭交流源输出
    ${当前容量}    获取web实时数据    电池组当前容量比率-1
    Wait Until Keyword Succeeds    40m    1    信号量数据值小于    电池组当前容量比率-1    ${负载二次下电SOC设置值}
    Comment    ${一次下电控制状态1}    获取web实时数据    二次下电控制状态
    Comment    should be equal    '${一次下电控制状态1}'    '恢复'
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    二次下电告警
    Comment    显示属性配置    二次下电控制状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    Run keywords    设置web参数量    下电模式    电池剩余容量
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    负载二次下电SOC阈值
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    下电模式
    ...    负载二次下电电压    测试终止电压    测试终止SOC阈值    负载一次下电使能
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1
    ...    100
    ...    AND    run keyword and ignore error    设置web设备参数量为默认值    电池电压低阈值
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出

下电使能为禁止时不二次电压下电
    
    铁锂电池管理初始化
    设置web参数量    下电模式    电池电压
    设置web参数量    下电控制延时    0
    设置web参数量    负载二次下电使能    允许
    设置web参数量    负载一次下电使能    禁止
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${可设置范围}[1]
    ${电压低可设置范围}    run keyword and ignore error    获取web参数可设置范围    电池电压低阈值
    run keyword and ignore error    设置web参数量    电池电压低阈值    ${电压低可设置范围}[1]
    ${二次下电电压可设置范围}    获取web参数可设置范围    负载二次下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电电压    ${二次下电电压可设置范围}[1]
    ${负载二次下电电压设置值}    获取web参数量    负载二次下电电压
    设置负载电压电流    53.5    10
    打开负载输出
    设置web参数量    负载二次下电使能    禁止
    关闭交流源输出
    sleep    10
    向下调节电池电压    ${负载二次下电电压设置值}-0.5
    sleep    5m
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    二次下电告警
    向上调节电池电压    53.5
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    负载二次下电使能
    ...    负载二次下电电压    测试终止电压    负载一次下电使能
    ...    AND    run keyword and ignore error    设置web设备参数量为默认值    电池电压低阈值
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出
    ...    AND    重置电池模拟器输出

下电使能为禁止时不二次时间下电
    
    铁锂电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电模式    停电时间
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电控制延时    0
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载一次下电使能    禁止
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${可设置范围}[0]
    ${电压低可设置范围}    run keyword and ignore error    获取web参数可设置范围    电池电压低阈值
    run keyword and ignore error    设置web参数量    电池电压低阈值    ${电压低可设置范围}[0]
    ${二次下电电压可设置范围}    获取web参数可设置范围    负载二次下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电电压    ${二次下电电压可设置范围}[0]
    ${下电时间可设置范围}    获取web参数可设置范围    负载二次下电时间
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电时间    ${下电时间可设置范围}[0]
    ${负载二次下电电压设置值}    获取web参数量    负载二次下电时间
    设置负载电压电流    53.5    10
    打开负载输出
    设置web参数量    负载二次下电使能    禁止
    关闭交流源输出
    ${停电时间}    evaluate    ${负载二次下电电压设置值}+3
    sleep    ${停电时间}m
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    二次下电告警
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    负载二次下电使能
    ...    负载二次下电时间
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    下电模式
    ...    负载二次下电电压    测试终止电压    负载一次下电使能
    ...    AND    run keyword and ignore error    设置web设备参数量为默认值    电池电压低阈值
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出

下电使能为禁止时不二次容量下电
    
    铁锂电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    20
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电模式    电池剩余容量
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电控制延时    0
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载一次下电使能    禁止
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${可设置范围}[0]
    ${电压低可设置范围}    run keyword and ignore error    获取web参数可设置范围    电池电压低阈值
    run keyword and ignore error    设置web参数量    电池电压低阈值    ${电压低可设置范围}[0]
    ${二次下电电压可设置范围}    获取web参数可设置范围    负载二次下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电电压    ${二次下电电压可设置范围}[0]
    ${测试终止SOC可设置范围}    获取web参数可设置范围    测试终止SOC阈值
    设置web参数量    测试终止SOC阈值    ${测试终止SOC可设置范围}[1]
    sleep    5
    ${下电容量可设置范围}    获取web参数可设置范围    负载二次下电SOC阈值
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电SOC阈值    ${下电容量可设置范围}[1]
    ${负载二次下电电压设置值}    获取web参数量    负载二次下电SOC阈值
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    90
    打开负载输出
    设置web参数量    负载二次下电使能    禁止
    关闭交流源输出
    ${当前容量}    获取web实时数据    电池组当前容量比率-1
    Wait Until Keyword Succeeds    40m    1    信号量数据值小于    电池组当前容量比率-1    ${负载二次下电电压设置值}
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    二次下电告警
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    负载二次下电使能
    ...    负载二次下电SOC阈值
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    下电模式
    ...    负载二次下电电压    测试终止电压    测试终止SOC阈值    负载一次下电使能
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1
    ...    100
    ...    AND    run keyword and ignore error    设置web设备参数量为默认值    电池电压低阈值
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出

交流停电的二次电压下电功能
    
    铁锂电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电模式    电池电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电控制延时    0
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载一次下电使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电使能    允许
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${可设置范围}[1]
    ${电压低可设置范围}    run keyword and ignore error    获取web参数可设置范围    电池电压低阈值
    run keyword and ignore error    设置web参数量    电池电压低阈值    ${电压低可设置范围}[1]
    ${恢复时间可设置范围}    获取web参数可设置范围    二次下电恢复时间
    run keyword and ignore error    设置web参数量    二次下电恢复时间    ${恢复时间可设置范围}[0]
    ${二次下电电压可设置范围}    获取web参数可设置范围    负载二次下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电电压    ${二次下电电压可设置范围}[1]
    显示属性配置    二次下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    5
    设置负载电压电流    53.5    5
    打开负载输出
    关闭交流源输出
    sleep    10
    ${二次下电电压设置值}    获取web参数量    负载二次下电电压
    ${二次下电电压0}    evaluate    ${二次下电电压设置值}+1.0
    ${二次下电电压1}    evaluate    ${二次下电电压设置值}-0.5
    向下调节电池电压    ${二次下电电压0}
    ${状态}    Run Keyword And Return Status    Wait Until Keyword Succeeds    3m    2    信号量数据值为    二次下电控制状态
    ...    动作
    should not be true    ${状态}
    向下调节电池电压    ${二次下电电压1}    
    sleep    5m
    Wait Until Keyword Succeeds    3m    1    信号量数据值为    二次下电控制状态    动作
    Wait Until Keyword Succeeds    5m    1    判断告警存在    二次下电告警
    打开交流源输出
    向上调节电池电压    53.5
    Wait Until Keyword Succeeds    8m    1    信号量数据值为    二次下电控制状态    恢复
    Wait Until Keyword Succeeds    8m    1    判断告警不存在    二次下电告警
    关闭负载输出
    显示属性配置    二次下电控制状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    负载二次下电电压
    ...    测试终止电压    负载一次下电使能    二次下电恢复时间
    ...    AND    run keyword and ignore error    设置web设备参数量为默认值    电池电压低阈值
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出
    ...    AND    重置电池模拟器输出

交流停电的二次停电时间下电功能
    
    铁锂电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电模式    停电时间
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电控制延时    0
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载一次下电使能    禁止
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${可设置范围}[0]
    ${电压低可设置范围}    run keyword and ignore error    获取web参数可设置范围    电池电压低阈值
    run keyword and ignore error    设置web参数量    电池电压低阈值    ${电压低可设置范围}[0]
    ${二次下电电压可设置范围}    获取web参数可设置范围    负载二次下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电电压    ${二次下电电压可设置范围}[0]
    ${下电时间可设置范围}    获取web参数可设置范围    负载二次下电时间
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电时间    ${下电时间可设置范围}[0]
    ${负载二次下电时间设置值}    获取web参数量    负载二次下电时间
    ${恢复时间可设置范围}    获取web参数可设置范围    二次下电恢复时间
    run keyword and ignore error    设置web参数量    二次下电恢复时间    ${恢复时间可设置范围}[0]
    显示属性配置    二次下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    5
    设置负载电压电流    53.5    5
    打开负载输出
    关闭交流源输出
    ${停电时间}    evaluate    ${负载二次下电时间设置值}-1
    ${二次下电电压设置值}    获取web参数量    负载二次下电电压
    ${二次下电控制状态1}    获取web实时数据    二次下电控制状态
    should be equal    ${二次下电控制状态1}    恢复
    ${直流电压}    获取web实时数据    直流电压
    sleep    ${停电时间}m
    ${直流电压}    获取web实时数据    直流电压
    should be true    ${直流电压}>${二次下电电压设置值}
    sleep    8m
    Wait Until Keyword Succeeds    2m    2    信号量数据值为    二次下电控制状态    动作
    Wait Until Keyword Succeeds    5m    1    判断告警存在    二次下电告警
    打开交流源输出
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    二次下电控制状态    恢复
    Wait Until Keyword Succeeds    8m    1    判断告警不存在    二次下电告警
    显示属性配置    二次下电控制状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    Run keywords    设置web参数量    下电模式    停电时间
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    负载二次下电时间
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    下电模式
    ...    负载二次下电电压    测试终止电压    负载一次下电使能    二次下电恢复时间
    ...    AND    run keyword and ignore error    设置web设备参数量为默认值    电池电压低阈值
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出

交流停电的二次容量下电功能
    
    铁锂电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    20
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电模式    电池剩余容量
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电控制延时    0
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载一次下电使能    禁止
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${可设置范围}[0]
    ${电压低可设置范围}    run keyword and ignore error    获取web参数可设置范围    电池电压低阈值
    run keyword and ignore error    设置web参数量    电池电压低阈值    ${电压低可设置范围}[0]
    ${二次下电电压可设置范围}    获取web参数可设置范围    负载二次下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电电压    ${二次下电电压可设置范围}[0]
    ${测试终止SOC可设置范围}    获取web参数可设置范围    测试终止SOC阈值
    设置web参数量    测试终止SOC阈值    ${测试终止SOC可设置范围}[1]
    sleep    5
    ${下电容量可设置范围}    获取web参数可设置范围    负载二次下电SOC阈值
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电SOC阈值    ${下电容量可设置范围}[1]
    ${负载二次下电SOC设置值}    获取web参数量    负载二次下电SOC阈值
    ${恢复时间可设置范围}    获取web参数可设置范围    二次下电恢复时间
    run keyword and ignore error    设置web参数量    二次下电恢复时间    ${恢复时间可设置范围}[0]
    显示属性配置    二次下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    5
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    90
    打开负载输出
    关闭交流源输出
    ${当前容量}    获取web实时数据    电池组当前容量比率-1
    Wait Until Keyword Succeeds    40m    1    信号量数据值小于    电池组当前容量比率-1    ${负载二次下电SOC设置值}
    ${直流电压}    获取web实时数据    直流电压
    should be true    ${直流电压}>${二次下电电压可设置范围}[0]
    sleep    5m
    Wait Until Keyword Succeeds    3m    2    信号量数据值为    二次下电控制状态    动作
    Wait Until Keyword Succeeds    5m    1    判断告警存在    二次下电告警
    打开交流源输出
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    二次下电控制状态    恢复
    显示属性配置    二次下电控制状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    Run keywords    设置web参数量    下电模式    电池剩余容量
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    负载二次下电SOC阈值
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    下电模式
    ...    负载二次下电电压    测试终止电压    测试终止SOC阈值    二次下电恢复时间    负载一次下电使能
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1
    ...    100
    ...    AND    run keyword and ignore error    设置web设备参数量为默认值    电池电压低阈值
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出

电池无效时禁止二次电压下电
    
    铁锂电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电模式    电池电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电控制延时    0
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载一次下电使能    禁止
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${可设置范围}[1]
    ${电压低可设置范围}    run keyword and ignore error    获取web参数可设置范围    电池电压低阈值
    run keyword and ignore error    设置web参数量    电池电压低阈值    ${电压低可设置范围}[1]
    ${二次下电电压可设置范围}    获取web参数可设置范围    负载二次下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电电压    ${二次下电电压可设置范围}[1]
    ${负载二次下电电压设置值}    获取web参数量    负载二次下电电压
    显示属性配置    二次下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    5
    设置负载电压电流    53.5    10
    打开负载输出
    关闭交流源输出
    ${二次下电电压1}    evaluate    ${负载二次下电电压设置值}-0.5
    向下调节电池电压    ${二次下电电压1}
    设置web参数量    电池组容量_1    0
    sleep    5m
    ${二次下电控制状态1}    获取web实时数据    二次下电控制状态
    should be equal    ${二次下电控制状态1}    恢复
    打开交流源输出
    sleep    10
    设置web参数量    电池组容量_1    100
    向上调节电池电压    53.5
    关闭负载输出
    显示属性配置    二次下电控制状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    负载二次下电电压
    ...    测试终止电压    负载一次下电使能
    ...    AND    设置web参数量    电池组容量_1    100
    ...    AND    run keyword and ignore error    设置web设备参数量为默认值    电池电压低阈值
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出
    ...    AND    重置电池模拟器输出

电池无效时禁止二次时间下电
    
    铁锂电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电模式    停电时间
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电控制延时    0
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载一次下电使能    禁止
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${可设置范围}[0]
    ${电压低可设置范围}    run keyword and ignore error    获取web参数可设置范围    电池电压低阈值
    run keyword and ignore error    设置web参数量    电池电压低阈值    ${电压低可设置范围}[0]
    ${二次下电电压可设置范围}    获取web参数可设置范围    负载二次下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电电压    ${二次下电电压可设置范围}[0]
    ${下电时间可设置范围}    获取web参数可设置范围    负载二次下电时间
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电时间    ${下电时间可设置范围}[0]
    ${负载二次下电时间设置值}    获取web参数量    负载二次下电时间
    显示属性配置    二次下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    5
    设置负载电压电流    53.5    10
    打开负载输出
    设置web参数量    电池组容量_1    0
    关闭交流源输出
    ${停电时间}    evaluate    ${负载二次下电时间设置值}+3
    sleep    ${停电时间}m
    ${电池下电控制状态1}    获取web实时数据    二次下电控制状态
    sleep    5m
    should be equal    ${电池下电控制状态1}    恢复
    显示属性配置    二次下电控制状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    Run keywords    设置web参数量    电池组容量_1    100
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    负载二次下电时间
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    下电模式
    ...    负载二次下电电压    测试终止电压    负载一次下电使能
    ...    AND    run keyword and ignore error    设置web设备参数量为默认值    电池电压低阈值
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出

电池无效时禁止二次容量下电
    
    铁锂电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    20
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电模式    电池剩余容量
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电控制延时    0
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载一次下电使能    禁止
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${可设置范围}[0]
    ${电压低可设置范围}    run keyword and ignore error    获取web参数可设置范围    电池电压低阈值
    run keyword and ignore error    设置web参数量    电池电压低阈值    ${电压低可设置范围}[0]
    ${二次下电电压可设置范围}    获取web参数可设置范围    负载二次下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电电压    ${二次下电电压可设置范围}[0]
    ${测试终止SOC可设置范围}    获取web参数可设置范围    测试终止SOC阈值
    设置web参数量    测试终止SOC阈值    ${测试终止SOC可设置范围}[1]
    sleep    5
    ${下电容量可设置范围}    获取web参数可设置范围    负载二次下电SOC阈值
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电SOC阈值    ${下电容量可设置范围}[1]
    ${负载二次下电SOC设置值}    获取web参数量    负载二次下电SOC阈值
    显示属性配置    二次下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    5
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    90
    打开负载输出
    设置web参数量    电池组容量_1    0
    关闭交流源输出
    sleep    40m    #计算约12min放电至容量80%
    ${二次下电控制状态1}    获取web实时数据    二次下电控制状态
    should be equal    '${二次下电控制状态1}'    '恢复'
    关闭负载输出
    Wait Until Keyword Succeeds    10m    1    判断告警不存在    二次下电告警
    显示属性配置    二次下电控制状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    Run keywords    设置web参数量    下电模式    电池剩余容量
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    负载二次下电SOC阈值
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    下电模式
    ...    负载二次下电电压    测试终止电压    测试终止SOC阈值    负载一次下电使能
    ...    AND    设置web参数量    电池组容量_1    100
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1
    ...    100
    ...    AND    run keyword and ignore error    设置web设备参数量为默认值    电池电压低阈值
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出

二次电压下电的延时控制功能
    
    铁锂电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电模式    电池电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电控制延时    300    #默认0s
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电使能    允许    #默认1允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载一次下电使能    禁止    #默认1允许
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${可设置范围}[1]
    ${电压低可设置范围}    run keyword and ignore error    获取web参数可设置范围    电池电压低阈值
    run keyword and ignore error    设置web参数量    电池电压低阈值    ${电压低可设置范围}[1]
    ${恢复时间可设置范围}    获取web参数可设置范围    二次下电恢复时间
    run keyword and ignore error    设置web参数量    二次下电恢复时间    ${恢复时间可设置范围}[0]
    ${二次下电电压可设置范围}    获取web参数可设置范围    负载二次下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电电压    ${二次下电电压可设置范围}[1]
    显示属性配置    二次下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    5
    设置负载电压电流    53.5    10
    打开负载输出
    关闭交流源输出
    ${二次下电电压设置值}    获取web参数量    负载二次下电电压
    ${二次下电电压1}    evaluate    ${二次下电电压设置值}-0.5
    向下调节电池电压    ${二次下电电压1}
    ${状态}    Run Keyword And Return Status    Wait Until Keyword Succeeds    5m    2    信号量数据值为    二次下电控制状态
    ...    动作
    should not be true    ${状态}
    Wait Until Keyword Succeeds    10m    1    信号量数据值为    二次下电控制状态    动作
    打开交流源输出
    向下调节电池电压    53.5
    Wait Until Keyword Succeeds    8m    1    信号量数据值为    二次下电控制状态    恢复
    显示属性配置    二次下电控制状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    负载二次下电电压
    ...    测试终止电压    下电控制延时    二次下电恢复时间    负载一次下电使能
    ...    AND    run keyword and ignore error    设置web设备参数量为默认值    电池电压低阈值
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出
    ...    AND    重置电池模拟器输出

二次停电时间下电延时控制功能
    
    铁锂电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电模式    停电时间
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电控制延时    300
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载一次下电使能    禁止
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${可设置范围}[0]
    ${电压低可设置范围}    run keyword and ignore error    获取web参数可设置范围    电池电压低阈值
    run keyword and ignore error    设置web参数量    电池电压低阈值    ${电压低可设置范围}[0]
    ${二次下电电压可设置范围}    获取web参数可设置范围    负载二次下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电电压    ${二次下电电压可设置范围}[0]
    ${下电时间可设置范围}    获取web参数可设置范围    负载二次下电时间
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电时间    ${下电时间可设置范围}[0]
    ${负载二次下电时间设置值}    获取web参数量    负载二次下电时间
    ${恢复时间可设置范围}    获取web参数可设置范围    二次下电恢复时间
    run keyword and ignore error    设置web参数量    二次下电恢复时间    ${恢复时间可设置范围}[0]
    显示属性配置    二次下电控制状态    数字量    web_attr=On    gui_attr=On
    设置负载电压电流    53.5    10
    打开负载输出
    关闭交流源输出
    ${停电时间}    evaluate    ${负载二次下电时间设置值}+2
    ${二次下电电压设置值}    获取web参数量    负载二次下电电压
    ${二次下电控制状态1}    获取web实时数据    二次下电控制状态
    should be equal    ${二次下电控制状态1}    恢复
    Wait Until Keyword Succeeds    ${停电时间}m    1    判断告警存在    二次下电告警
    ${直流电压}    获取web实时数据    直流电压
    should be true    ${直流电压}>${二次下电电压设置值}
    ${状态}    Run Keyword And Return Status    Wait Until Keyword Succeeds    4m30s    2    信号量数据值为    二次下电控制状态
    ...    动作
    should not be true    ${状态}
    Wait Until Keyword Succeeds    10m    1    信号量数据值为    二次下电控制状态    动作
    打开交流源输出
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    二次下电控制状态    恢复
    显示属性配置    二次下电控制状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    Run keywords    设置web参数量    下电模式    停电时间
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    负载二次下电时间
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    下电模式
    ...    负载二次下电电压    测试终止电压    下电控制延时    二次下电恢复时间    负载一次下电使能
    ...    AND    run keyword and ignore error    设置web设备参数量为默认值    电池电压低阈值
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出

二次容量下电延时控制功能
    
    铁锂电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    11
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电模式    电池剩余容量
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电控制延时    300
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载一次下电使能    禁止
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${可设置范围}[0]
    ${电压低可设置范围}    run keyword and ignore error    获取web参数可设置范围    电池电压低阈值
    run keyword and ignore error    设置web参数量    电池电压低阈值    ${电压低可设置范围}[0]
    ${二次下电电压可设置范围}    获取web参数可设置范围    负载二次下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电电压    ${二次下电电压可设置范围}[0]
    ${测试终止SOC可设置范围}    获取web参数可设置范围    测试终止SOC阈值
    设置web参数量    测试终止SOC阈值    ${测试终止SOC可设置范围}[1]
    sleep    5
    ${下电容量可设置范围}    获取web参数可设置范围    负载二次下电SOC阈值
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电SOC阈值    ${下电容量可设置范围}[1]
    ${负载二次下电SOC设置值}    获取web参数量    负载二次下电SOC阈值
    ${恢复时间可设置范围}    获取web参数可设置范围    二次下电恢复时间
    run keyword and ignore error    设置web参数量    二次下电恢复时间    ${恢复时间可设置范围}[0]
    显示属性配置    二次下电控制状态    数字量    web_attr=On    gui_attr=On
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    45
    打开负载输出
    关闭交流源输出
    ${当前容量}    获取web实时数据    电池组当前容量比率-1
    Wait Until Keyword Succeeds    40m    1    信号量数据值小于    电池组当前容量比率-1    ${负载二次下电SOC设置值}
    ${直流电压}    获取web实时数据    直流电压
    should be true    ${直流电压}>${二次下电电压可设置范围}[0]
    ${状态}    Run Keyword And Return Status    Wait Until Keyword Succeeds    5m    2    信号量数据值为    二次下电控制状态
    ...    动作
    should not be true    ${状态}
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    二次下电控制状态    动作
    打开交流源输出
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    二次下电控制状态    恢复
    显示属性配置    二次下电控制状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    Run keywords    设置web参数量    下电模式    电池剩余容量
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    负载二次下电SOC阈值
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    下电模式
    ...    负载二次下电电压    测试终止电压    测试终止SOC阈值    二次下电恢复时间    下电控制延时    负载一次下电使能
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1
    ...    100
    ...    AND    run keyword and ignore error    设置web设备参数量为默认值    电池电压低阈值
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出

容量下电模式下的二次电压下电
    
    铁锂电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电模式    电池剩余容量
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电控制延时    0
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载一次下电使能    禁止
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${可设置范围}[1]
    ${电压低可设置范围}    run keyword and ignore error    获取web参数可设置范围    电池电压低阈值
    run keyword and ignore error    设置web参数量    电池电压低阈值    ${电压低可设置范围}[1]
    ${二次下电电压可设置范围}    获取web参数可设置范围    负载二次下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电电压    ${二次下电电压可设置范围}[1]
    ${测试终止SOC可设置范围}    获取web参数可设置范围    测试终止SOC阈值
    设置web参数量    测试终止SOC阈值    ${测试终止SOC可设置范围}[1]
    sleep    5
    ${下电容量可设置范围}    获取web参数可设置范围    负载二次下电SOC阈值
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电SOC阈值    ${下电容量可设置范围}[0]
    ${二次下电SOC设置值}    获取web参数量    负载二次下电SOC阈值
    ${恢复时间可设置范围}    获取web参数可设置范围    二次下电恢复时间
    run keyword and ignore error    设置web参数量    二次下电恢复时间    ${恢复时间可设置范围}[0]
    显示属性配置    二次下电控制状态    数字量    web_attr=On    gui_attr=On
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    60
    打开负载输出
    ${二次下电电压设置值}    获取web参数量    负载二次下电电压
    ${直流电压}    获取web实时数据    直流电压
    关闭交流源输出
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    电池管理状态    系统停电
    ${二次下电电压}    evaluate    ${二次下电电压设置值}-0.5
    向下调节电池电压    ${二次下电电压}
    ${电池组当前容量比率}    获取web实时数据    电池组当前容量比率-1
    should be true    ${电池组当前容量比率}>${二次下电SOC设置值}
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    二次下电控制状态    动作
    打开交流源输出
    向上调节电池电压    53.5
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    二次下电控制状态    恢复
    显示属性配置    二次下电控制状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    Run keywords    设置web参数量    下电模式    电池剩余容量
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    负载二次下电SOC阈值
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    下电模式
    ...    负载二次下电电压    测试终止电压    测试终止SOC阈值    二次下电恢复时间    负载一次下电使能
    ...    AND    run keyword and ignore error    设置web设备参数量为默认值    电池电压低阈值
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出
    ...    AND    重置电池模拟器输出

停电时间下电模式下的二次电压下电
    
    铁锂电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电模式    停电时间
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电控制延时    0
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载一次下电使能    禁止
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${可设置范围}[1]
    ${电压低可设置范围}    run keyword and ignore error    获取web参数可设置范围    电池电压低阈值
    run keyword and ignore error    设置web参数量    电池电压低阈值    ${电压低可设置范围}[1]
    ${二次下电电压可设置范围}    获取web参数可设置范围    负载二次下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电电压    ${二次下电电压可设置范围}[1]
    ${下电时间可设置范围}    获取web参数可设置范围    负载二次下电时间
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电时间    ${下电时间可设置范围}[1]
    ${负载二次下电时间设置值}    获取web参数量    负载二次下电时间
    ${恢复时间可设置范围}    获取web参数可设置范围    二次下电恢复时间
    run keyword and ignore error    设置web参数量    二次下电恢复时间    ${恢复时间可设置范围}[0]
    显示属性配置    二次下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    5
    设置负载电压电流    53.5    10
    打开负载输出
    ${二次下电电压设置值}    获取web参数量    负载二次下电电压
    ${二次下电电压}    evaluate    ${二次下电电压设置值}-0.5
    关闭交流源输出
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    电池管理状态    系统停电
    向下调节电池电压    ${二次下电电压}
    ${电池放电持续时间}    获取web实时数据    电池状态持续时间
    should be true    ${电池放电持续时间}<${负载二次下电时间设置值}
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    二次下电控制状态    动作
    打开交流源输出
    向上调节电池电压    53.5
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    二次下电控制状态    恢复
    显示属性配置    二次下电控制状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    Run keywords    设置web参数量    下电模式    停电时间
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    负载二次下电时间
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    下电模式
    ...    负载二次下电电压    测试终止电压    二次下电恢复时间    负载一次下电使能
    ...    AND    run keyword and ignore error    设置web设备参数量为默认值    电池电压低阈值
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出
    ...    AND    重置电池模拟器输出

充电电压低于48V时禁止二次电压下电
    [Tags]    notest
    
    铁锂电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电模式    电池电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电控制延时    0
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载一次下电使能    禁止
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${可设置范围}[1]
    ${电压低可设置范围}    run keyword and ignore error    获取web参数可设置范围    电池电压低阈值
    run keyword and ignore error    设置web参数量    电池电压低阈值    ${电压低可设置范围}[1]
    ${恢复时间可设置范围}    获取web参数可设置范围    二次下电恢复时间
    run keyword and ignore error    设置web参数量    二次下电恢复时间    ${恢复时间可设置范围}[0]
    ${二次下电电压可设置范围}    获取web参数可设置范围    负载二次下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电电压    ${二次下电电压可设置范围}[1]
    显示属性配置    二次下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    5
    ${可设置范围}    获取web参数可设置范围    充电电压
    设置web参数量    充电电压    ${可设置范围}[0]
    ${浮充电压设置值}    获取web参数量    充电电压
    run keyword if    ${浮充电压设置值}>= 48    fail
    设置负载电压电流    53.5    10
    打开负载输出
    关闭交流源输出
    ${二次下电电压设置值}    获取web参数量    负载二次下电电压
    ${二次下电电压1}    evaluate    ${二次下电电压设置值}-0.5
    向下调节电池电压    ${二次下电电压1}
    ${状态}    Run Keyword And Return Status    Wait Until Keyword Succeeds    5m    2    信号量数据值为    二次下电控制状态
    ...    动作
    should not be true    ${状态}
    打开交流源输出
    向上调节电池电压    53.5
    设置web参数量    浮充电压    53.5
    显示属性配置    二次下电控制状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    负载二次下电电压
    ...    测试终止电压    充电电压    二次下电恢复时间    负载一次下电使能
    ...    AND    run keyword and ignore error    设置web设备参数量为默认值    电池电压低阈值
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出
    ...    AND    重置电池模拟器输出

充电电压低于48V时禁止二次容量下电
    [Tags]    notest
    
    铁锂电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    20
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电模式    电池剩余容量
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电控制延时    0
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载一次下电使能    禁止
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${可设置范围}[0]
    ${电压低可设置范围}    run keyword and ignore error    获取web参数可设置范围    电池电压低阈值
    run keyword and ignore error    设置web参数量    电池电压低阈值    ${电压低可设置范围}[0]
    ${二次下电电压可设置范围}    获取web参数可设置范围    负载二次下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电电压    ${二次下电电压可设置范围}[0]
    ${测试终止SOC可设置范围}    获取web参数可设置范围    测试终止SOC阈值
    设置web参数量    测试终止SOC阈值    ${测试终止SOC可设置范围}[1]
    sleep    5
    ${下电容量可设置范围}    获取web参数可设置范围    负载二次下电SOC阈值
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电SOC阈值    ${下电容量可设置范围}[1]
    ${二次下电SOC设置值}    获取web参数量    负载二次下电SOC阈值
    ${恢复时间可设置范围}    获取web参数可设置范围    二次下电恢复时间
    run keyword and ignore error    设置web参数量    二次下电恢复时间    ${恢复时间可设置范围}[0]
    ${二次下电电压设置值}    获取web参数量    负载二次下电电压
    显示属性配置    二次下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    5
    ${可设置范围}    获取web参数可设置范围    浮充电压
    设置web参数量    浮充电压    ${可设置范围}[0]
    ${浮充电压设置值}    获取web参数量    浮充电压
    run keyword if    ${浮充电压设置值}>= 48    fail
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    90
    打开负载输出
    关闭交流源输出
    Wait Until Keyword Succeeds    40m    1    信号量数据值小于    电池组当前容量比率-1    ${二次下电SOC设置值}
    ${直流电压}    获取web实时数据    直流电压
    should be true    ${直流电压}>${二次下电电压设置值}
    ${状态}    Run Keyword And Return Status    Wait Until Keyword Succeeds    5m    2    信号量数据值为    二次下电控制状态
    ...    动作
    should not be true    ${状态}
    打开交流源输出
    设置web参数量    浮充电压    53.5
    显示属性配置    二次下电控制状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    Run keywords    设置web参数量    下电模式    电池剩余容量
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    负载二次下电SOC阈值
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    下电模式
    ...    负载二次下电电压    测试终止电压    测试终止SOC阈值    二次下电恢复时间    充电电压    负载一次下电使能
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1
    ...    100
    ...    AND    run keyword and ignore error    设置web设备参数量为默认值    电池电压低阈值
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出

交流有电的二次容量下电功能
    [Setup]    测试用例前置条件
    铁锂电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    20
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电模式    电池剩余容量
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电控制延时    0
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载一次下电使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    市电额定有功功率    2.1
    Wait Until Keyword Succeeds    10    1    设置web参数量    市电降额系数    85
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${可设置范围}[0]
    ${电压低可设置范围}    run keyword and ignore error    获取web参数可设置范围    电池电压低阈值
    run keyword and ignore error    设置web参数量    电池电压低阈值    ${电压低可设置范围}[0]
    ${二次下电电压可设置范围}    获取web参数可设置范围    负载二次下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电电压    ${二次下电电压可设置范围}[0]
    ${测试终止SOC可设置范围}    获取web参数可设置范围    测试终止SOC阈值
    设置web参数量    测试终止SOC阈值    ${测试终止SOC可设置范围}[1]
    sleep    5
    ${下电容量可设置范围}    获取web参数可设置范围    负载二次下电SOC阈值
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电SOC阈值    ${下电容量可设置范围}[1]
    ${恢复时间可设置范围}    获取web参数可设置范围    二次下电恢复时间
    run keyword and ignore error    设置web参数量    二次下电恢复时间    ${恢复时间可设置范围}[0]
    ${二次下电SOC设置值}    获取web参数量    负载二次下电SOC阈值
    ${二次下电恢复容量}    evaluate    ${二次下电SOC设置值}+2
    ${下电恢复回差}    获取web参数可设置范围    二次下电恢复回差
    设置web参数量    二次下电恢复回差    ${下电恢复回差}[0]
    ${二次上电电压}    evaluate    ${二次下电电压可设置范围}[0]+${下电恢复回差}[0]
    显示属性配置    二次下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    5
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    90
    打开负载输出
    wait until keyword succeeds    30    1    设置通道配置    SPB_X2_VB1    -18    1    电池_1    电池电压
    wait until keyword succeeds    30    1    设置通道配置    SPB_X1_VIN    -18    1    直流配电    直流电压
    Wait Until Keyword Succeeds    30m    5    信号量数据值小于    电池组当前容量比率-1    ${二次下电SOC设置值}
    Wait Until Keyword Succeeds    60m    2    信号量数据值为    二次下电控制状态    动作
    关闭负载输出
    wait until keyword succeeds    30    1    设置通道配置    SPB_X2_VB1    0    1    电池_1    电池电压
    wait until keyword succeeds    30    1    设置通道配置    SPB_X1_VIN    0    1    直流配电    直流电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    锂电充电电流系数    0.6
    Wait Until Keyword Succeeds    30m    5    信号量数据值大于    直流电压    ${二次上电电压}
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    二次下电控制状态    恢复
    显示属性配置    二次下电控制状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    Run keywords    设置web参数量    下电模式    电池剩余容量
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    负载二次下电SOC阈值
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    下电模式
    ...    负载二次下电电压    测试终止电压    测试终止SOC阈值    二次下电恢复时间    二次下电恢复回差    负载一次下电使能
    ...    锂电充电电流系数
    ...    AND    设置web设备参数量为默认值    市电降额系数    市电额定有功功率
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1
    ...    100
    ...    AND    run keyword and ignore error    设置web设备参数量为默认值    电池电压低阈值
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出
