*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
从别的模式进入自由模式测试
    
    电池管理初始化
    系统复位
    #安全模式进入自由模式
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-1    是
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    2
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    自由
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自由
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-1    否
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    #节能模式进入自由模式
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能    #王德安
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-1    是
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    自由
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自由
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-1    否
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能    #王德安
    Wait Until Keyword Succeeds    1m    1    设置web控制量    暂时非节能控制
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    自由
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自由
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    1m    1    设置web控制量    永久非节能控制
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    自由
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自由
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式
    ...    安全
    ...    AND    设置web参数量    整流器最小开机数量    3

从别的模式进入安全模式测试
    
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    自由
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自由
    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-1    是
    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-2    是
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    #节能模式进入安全模式
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    电池检测异常
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-1    是
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    2
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    1m    1    设置web控制量    暂时非节能控制
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    1m    1    设置web控制量    永久非节能控制
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式
    ...    安全
    ...    AND    设置web参数量    整流器最小开机数量    3

从自由模式进入节能模式测试
    
    电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    自由
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自由
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    自由
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自由
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    自由
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自由
    ${级别设置值}    获取web参数量    直流电压低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    直流电压低    主要
    ${可设置范围}    获取web参数可设置范围    直流电压低阈值
    设置web参数量    直流电压低阈值    ${可设置范围}[1]
    ${ 直流电压低阈值}    获取web参数量    直流电压低阈值
    向下调节电池电压    ${ 直流电压低阈值}-0.5
    wait until keyword succeeds    5m    2    查询指定告警信息    直流电压低
    # ${告警阈值}    获取web参数量    直流电压过低阈值
    # 向下调节电池电压    ${告警阈值}-0.5
    # Wait Until Keyword Succeeds    5m    1    查询指定告警信息    直流电压过低
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    交流节能状态    自动节能
    向上调节电池电压    ${ 直流电压低阈值}+1
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    直流电压低
    # 向上调节电池电压    53.5
    # Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    直流电压过低
    系统复位
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式
    ...    安全
    ...    AND    设置web参数量    整流器最小开机数量    3
    ...    AND    重置电池模拟器输出
    ...    AND    设置web设备参数量为默认值    直流电压低阈值

从安全模式进入节能模式测试
    
    电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    ${级别设置值}    获取web参数量    直流电压低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    直流电压低    主要
    ${可设置范围}    获取web参数可设置范围    直流电压低阈值
    设置web参数量    直流电压低阈值    ${可设置范围}[1]
    ${ 直流电压低阈值}    获取web参数量    直流电压低阈值
    向下调节电池电压    ${ 直流电压低阈值}-0.5
    wait until keyword succeeds    5m    2    查询指定告警信息    直流电压低
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    交流节能状态    自动节能
    向上调节电池电压    ${ 直流电压低阈值}+1
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    直流电压低
    系统复位
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式
    ...    安全
    ...    AND    设置web参数量    整流器最小开机数量    3
    ...    AND    重置电池模拟器输出
    ...    AND    设置web设备参数量为默认值    直流电压低阈值

从别的模式进入暂时非节能测试
    
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置web控制量    暂时非节能控制
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    暂时非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    1m    1    设置web控制量    自动节能控制
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    Wait Until Keyword Succeeds    1m    1    设置web控制量    暂时非节能控制
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    暂时非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置web控制量    永久非节能控制
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    永久非节能
    Wait Until Keyword Succeeds    1m    1    设置web控制量    暂时非节能控制
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    暂时非节能
    Wait Until Keyword Succeeds    1m    1    设置web控制量    人工维护检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    人工维护检测
    Wait Until Keyword Succeeds    1m    1    设置web控制量    暂时非节能控制
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    暂时非节能
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式
    ...    安全
    ...    AND    设置web参数量    整流器最小开机数量    3

从别的模式进入永久非节能测试
    
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置web控制量    永久非节能控制
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    1m    1    设置web控制量    自动节能控制
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    Wait Until Keyword Succeeds    1m    1    设置web控制量    永久非节能控制
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    永久非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置web控制量    暂时非节能控制
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    暂时非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置web控制量    永久非节能控制
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    永久非节能
    Wait Until Keyword Succeeds    1m    1    设置web控制量    人工维护检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    人工维护检测
    Wait Until Keyword Succeeds    1m    1    设置web控制量    永久非节能控制
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    永久非节能
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式
    ...    安全
    ...    AND    设置web参数量    整流器最小开机数量    3

从别的模式进入人工维护检测
    
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置web控制量    人工维护检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    人工维护检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    Wait Until Keyword Succeeds    1m    1    设置web控制量    人工维护检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    人工维护检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置web控制量    暂时非节能控制
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    暂时非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置web控制量    人工维护检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    人工维护检测
    Wait Until Keyword Succeeds    1m    1    设置web控制量    永久非节能控制
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    永久非节能
    Wait Until Keyword Succeeds    1m    1    设置web控制量    人工维护检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    人工维护检测
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式
    ...    安全
    ...    AND    设置web参数量    整流器最小开机数量    3

从别的模式进入自动节能
    
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置web控制量    自动节能控制
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    1m    1    设置web控制量    自动节能控制
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    Wait Until Keyword Succeeds    1m    1    设置web控制量    暂时非节能控制
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    暂时非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置web控制量    自动节能控制
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    1m    1    设置web控制量    永久非节能控制
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    永久非节能
    Wait Until Keyword Succeeds    1m    1    设置web控制量    自动节能控制
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    1m    1    设置web控制量    人工维护检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    人工维护检测
    Wait Until Keyword Succeeds    1m    1    设置web控制量    自动节能控制
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式
    ...    安全
    ...    AND    设置web参数量    整流器最小开机数量    3
