*** Settings ***
Resource          ../错峰用电场景.robot

*** Test Cases ***
充电系统为0，错峰用电时段跟节假日重合
    错峰用电节假日测试初始化    0    
    设置web参数量    节假日日段_1    01-01~03-01
    ${设置日期}    Create List    2023-03-02    2023-12-31
    FOR    ${日期}    IN    @{设置日期}
        ${时间}    set variable    ${日期} 05:00:00
        设置系统时间    ${时间}
        退出错峰状态
        ${时间}    set variable    ${日期} 06:00:00
        设置系统时间    ${时间}
        错峰放电校验    尖峰
        ${时间}    set variable    ${日期} 08:00:00
        设置系统时间    ${时间}
        错峰放电校验    高峰
        ${时间}    set variable    ${日期} 10:00:00
        设置系统时间    ${时间}
        错峰放电校验    平期
        ${时间}    set variable    ${日期} 12:00:00
        设置系统时间    ${时间}
        错峰谷期校验
        ${时间}    set variable    ${日期} 12:59:50
        设置系统时间    ${时间}
        sleep    15
        退出错峰状态
    END
    ${设置日期}    Create List    2023-01-01    2023-03-01
    FOR    ${日期}    IN    @{设置日期}
        ${时间}    set variable    ${日期} 05:00:00
        设置系统时间    ${时间}
        退出错峰状态
        ${时间}    set variable    ${日期} 06:00:00
        设置系统时间    ${时间}
        退出错峰状态
        ${时间}    set variable    ${日期} 08:00:00
        设置系统时间    ${时间}
        退出错峰状态
        ${时间}    set variable    ${日期} 10:00:00
        设置系统时间    ${时间}
        退出错峰状态
        ${时间}    set variable    ${日期} 12:00:00
        设置系统时间    ${时间}
        退出错峰状态
        ${时间}    set variable    ${日期} 12:59:50
        设置系统时间    ${时间}
        sleep    15
        退出错峰状态
    END
    [Teardown]    错峰用电节假日测试后置条件

充电系统为0，错峰用电时段跟周末重合
    错峰用电节假日测试初始化    0
    设置web参数量    周末错峰用电使能    禁止
    ${设置日期}    Create List    2023-01-02    2023-03-17    2024-02-29
    FOR    ${日期}    IN    @{设置日期}
        ${时间}    set variable    ${日期} 05:00:00
        设置系统时间    ${时间}
        退出错峰状态
        ${时间}    set variable    ${日期} 06:00:00
        设置系统时间    ${时间}
        错峰放电校验    尖峰
        ${时间}    set variable    ${日期} 08:00:00
        设置系统时间    ${时间}
        错峰放电校验    高峰
        ${时间}    set variable    ${日期} 10:00:00
        设置系统时间    ${时间}
        错峰放电校验    平期
        ${时间}    set variable    ${日期} 12:00:00
        设置系统时间    ${时间}
        错峰谷期校验
        ${时间}    set variable    ${日期} 12:59:50
        设置系统时间    ${时间}
        sleep    15
        退出错峰状态
    END
    ${设置日期}    Create List    2023-01-01    2023-03-18    2023-06-18
    FOR    ${日期}    IN    @{设置日期}
        ${时间}    set variable    ${日期} 05:00:00
        设置系统时间    ${时间}
        退出错峰状态
        ${时间}    set variable    ${日期} 06:00:00
        设置系统时间    ${时间}
        退出错峰状态
        ${时间}    set variable    ${日期} 08:00:00
        设置系统时间    ${时间}
        退出错峰状态
        ${时间}    set variable    ${日期} 10:00:00
        设置系统时间    ${时间}
        退出错峰状态
        ${时间}    set variable    ${日期} 12:00:00
        设置系统时间    ${时间}
        退出错峰状态
        ${时间}    set variable    ${日期} 12:59:50
        设置系统时间    ${时间}
        sleep    15
        退出错峰状态
    END
    [Teardown]    错峰用电节假日测试后置条件    True
