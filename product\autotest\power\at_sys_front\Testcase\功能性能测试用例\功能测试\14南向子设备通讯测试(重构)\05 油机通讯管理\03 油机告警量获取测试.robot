*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
油机输出欠压
    连接CSU
    设置子工具值    oileng    all    告警    备用8    256
    comment    sleep    20s
    ${级别设置值}    获取web参数量    油机输出欠压
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机输出欠压    主要
    wait until keyword succeeds    5m    5    查询指定告警信息    油机输出欠压
    设置子工具值    oileng    all    告警    备用8    0
    comment    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机输出欠压
    设置子工具值    oileng    all    告警    备用8    512
    comment    sleep    20s
    ${级别设置值}    获取web参数量    油机输出欠压
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机输出欠压    主要
    wait until keyword succeeds    5m    5    查询指定告警信息    油机输出欠压
    设置子工具值    oileng    all    告警    备用8    0
    comment    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机输出欠压
    [Teardown]    设置子工具值    oileng    all    告警    备用8    0

油机输出过压
    连接CSU
    设置子工具值    oileng    all    告警    备用8    1024
    comment    sleep    20s
    ${级别设置值}    获取web参数量    油机输出过压
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机输出过压    主要
    wait until keyword succeeds    5m    5    查询指定告警信息    油机输出过压
    设置子工具值    oileng    all    告警    备用8    0
    comment    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机输出过压
    设置子工具值    oileng    all    告警    备用8    2048
    comment    sleep    20s
    ${级别设置值}    获取web参数量    油机输出过压
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机输出过压    主要
    wait until keyword succeeds    5m    5    查询指定告警信息    油机输出过压
    设置子工具值    oileng    all    告警    备用8    0
    comment    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机输出过压
    [Teardown]    设置子工具值    oileng    all    告警    备用8    0

油机输出过流
    连接CSU
    设置子工具值    oileng    all    告警    备用7    1
    comment    sleep    20s
    ${级别设置值}    获取web参数量    油机输出过流
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机输出过流    主要
    wait until keyword succeeds    5m    5    查询指定告警信息    油机输出过流
    设置子工具值    oileng    all    告警    备用7    0
    comment    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机输出过流
    设置子工具值    oileng    all    告警    备用7    2
    comment    sleep    20s
    ${级别设置值}    获取web参数量    油机输出过流
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机输出过流    主要
    wait until keyword succeeds    5m    5    查询指定告警信息    油机输出过流
    设置子工具值    oileng    all    告警    备用7    0
    comment    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机输出过流
    [Teardown]    设置子工具值    oileng    all    告警    备用7    0

油机输出过载
    连接CSU
    设置子工具值    oileng    all    告警    备用7    4
    comment    sleep    20s
    ${级别设置值}    获取web参数量    油机输出过载
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机输出过载    主要
    wait until keyword succeeds    5m    5    查询指定告警信息    油机输出过载
    设置子工具值    oileng    all    告警    备用7    0
    comment    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机输出过载
    设置子工具值    oileng    all    告警    备用7    8
    comment    sleep    20s
    ${级别设置值}    获取web参数量    油机输出过载
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机输出过载    主要
    wait until keyword succeeds    5m    5    查询指定告警信息    油机输出过载
    设置子工具值    oileng    all    告警    备用7    0
    comment    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机输出过载
    [Teardown]    设置子工具值    oileng    all    告警    备用7    0

油机输出频率低
    连接CSU
    设置子工具值    oileng    all    告警    备用8    4096
    comment    sleep    20s
    ${级别设置值}    获取web参数量    油机输出频率低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机输出频率低    主要
    wait until keyword succeeds    5m    5    查询指定告警信息    油机输出频率低
    设置子工具值    oileng    all    告警    备用8    0
    comment    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机输出频率低
    设置子工具值    oileng    all    告警    备用8    8192
    comment    sleep    20s
    ${级别设置值}    获取web参数量    油机输出频率低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机输出频率低    主要
    wait until keyword succeeds    5m    5    查询指定告警信息    油机输出频率低
    设置子工具值    oileng    all    告警    备用8    0
    comment    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机输出频率低
    [Teardown]    设置子工具值    oileng    all    告警    备用8    0

油机输出频率高
    连接CSU
    设置子工具值    oileng    all    告警    备用8    16384
    comment    sleep    20s
    ${级别设置值}    获取web参数量    油机输出频率高
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机输出频率高    主要
    wait until keyword succeeds    5m    5    查询指定告警信息    油机输出频率高
    设置子工具值    oileng    all    告警    备用8    0
    comment    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机输出频率高
    设置子工具值    oileng    all    告警    备用8    32768
    comment    sleep    20s
    ${级别设置值}    获取web参数量    油机输出频率高
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机输出频率高    主要
    wait until keyword succeeds    5m    5    查询指定告警信息    油机输出频率高
    设置子工具值    oileng    all    告警    备用8    0
    comment    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机输出频率高
    [Teardown]    设置子工具值    oileng    all    告警    备用8    0

油机低速告警
    连接CSU
    设置子工具值    oileng    all    告警    备用9    16384
    comment    sleep    20s
    ${级别设置值}    获取web参数量    油机低速告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机低速告警    主要
    wait until keyword succeeds    5m    5    查询指定告警信息    油机低速告警
    设置子工具值    oileng    all    告警    备用9    0
    comment    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机低速告警
    连接CSU
    设置子工具值    oileng    all    告警    备用9    32768
    comment    sleep    20s
    ${级别设置值}    获取web参数量    油机低速告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机低速告警    主要
    wait until keyword succeeds    5m    5    查询指定告警信息    油机低速告警
    设置子工具值    oileng    all    告警    备用9    0
    comment    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机低速告警
    [Teardown]    设置子工具值    oileng    all    告警    备用9    0

油机超速告警
    连接CSU
    设置子工具值    oileng    all    告警    备用8    1
    comment    sleep    20s
    ${级别设置值}    获取web参数量    油机超速告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机超速告警    主要
    wait until keyword succeeds    5m    5    查询指定告警信息    油机超速告警
    设置子工具值    oileng    all    告警    备用8    0
    comment    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机超速告警
    设置子工具值    oileng    all    告警    备用8    2
    comment    sleep    20s
    ${级别设置值}    获取web参数量    油机超速告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机超速告警    主要
    wait until keyword succeeds    5m    5    查询指定告警信息    油机超速告警
    设置子工具值    oileng    all    告警    备用8    0
    comment    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机超速告警
    [Teardown]    设置子工具值    oileng    all    告警    备用8    0

油机油压低告警
    连接CSU
    设置子工具值    oileng    all    告警    备用8    4    
    ${级别设置值}    获取web参数量    油机油压低告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机油压低告警    主要
    wait until keyword succeeds    5m    5    查询指定告警信息    油机油压低告警
    设置子工具值    oileng    all    告警    备用8    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机油压低告警
    设置子工具值    oileng    all    告警    备用8    8
    ${级别设置值}    获取web参数量    油机油压低告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机油压低告警    主要
    wait until keyword succeeds    5m    5    查询指定告警信息    油机油压低告警
    设置子工具值    oileng    all    告警    备用8    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机油压低告警
    [Teardown]    设置子工具值    oileng    all    告警    备用8    0

油机电池欠压告警
    连接CSU
    设置子工具值    oileng    all    告警    备用9    8192
    ${级别设置值}    获取web参数量    油机电池欠压告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机电池欠压告警    主要
    wait until keyword succeeds    5m    5    查询指定告警信息    油机电池欠压告警
    设置子工具值    oileng    all    告警    备用9    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机电池欠压告警
    [Teardown]    设置子工具值    oileng    all    告警    备用9    0

油机电池过压告警
    连接CSU
    设置子工具值    oileng    all    告警    备用9    4096
    ${级别设置值}    获取web参数量    油机电池过压告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机电池过压告警    主要
    wait until keyword succeeds    5m    5    查询指定告警信息    油机电池过压告警
    设置子工具值    oileng    all    告警    备用9    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机电池过压告警
    [Teardown]    设置子工具值    oileng    all    告警    备用9    0

油机充电失败告警
    连接CSU
    设置子工具值    oileng    all    告警    备用9    2048
    ${级别设置值}    获取web参数量    油机充电失败告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机充电失败告警    主要
    wait until keyword succeeds    5m    5    查询指定告警信息    油机充电失败告警
    设置子工具值    oileng    all    告警    备用9    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机充电失败告警
    [Teardown]    设置子工具值    oileng    all    告警    备用9    0

油机发动机高温告警
    连接CSU
    设置子工具值    oileng    all    告警    备用8    16
    ${级别设置值}    获取web参数量    油机发动机高温告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机发动机高温告警    主要
    wait until keyword succeeds    5m    5    查询指定告警信息    油机发动机高温告警
    设置子工具值    oileng    all    告警    备用8    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机发动机高温告警
    设置子工具值    oileng    all    告警    备用8    32
    ${级别设置值}    获取web参数量    油机发动机高温告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机发动机高温告警    主要
    wait until keyword succeeds    5m    5    查询指定告警信息    油机发动机高温告警
    设置子工具值    oileng    all    告警    备用8    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机发动机高温告警
    [Teardown]    设置子工具值    oileng    all    告警    备用8    0

油机启动失败告警
    连接CSU
    设置子工具值    oileng    all    告警    备用9    4
    ${级别设置值}    获取web参数量    油机启动失败告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机启动失败告警    主要
    wait until keyword succeeds    5m    5    查询指定告警信息    油机启动失败告警
    设置子工具值    oileng    all    告警    备用9    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机启动失败告警
    [Teardown]    设置子工具值    oileng    all    告警    备用9    0

油机停机失败告警
    连接CSU
    设置子工具值    oileng    all    告警    备用9    8
    ${级别设置值}    获取web参数量    油机停机失败告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机停机失败告警    主要
    wait until keyword succeeds    5m    5    查询指定告警信息    油机停机失败告警
    设置子工具值    oileng    all    告警    备用9    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机停机失败告警
    [Teardown]    设置子工具值    oileng    all    告警    备用9    0

油机油位高告警
    连接CSU
    设置子工具值    oileng    all    告警    备用3    64
    ${级别设置值}    获取web参数量    油机油位高告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机油位高告警    主要
    wait until keyword succeeds    5m    5    查询指定告警信息    油机油位高告警
    设置子工具值    oileng    all    告警    备用3    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机油位高告警
    设置子工具值    oileng    all    告警    备用3    128
    ${级别设置值}    获取web参数量    油机油位高告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机油位高告警    主要
    wait until keyword succeeds    5m    5    查询指定告警信息    油机油位高告警
    设置子工具值    oileng    all    告警    备用3    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机油位高告警
    [Teardown]    设置子工具值    oileng    all    告警    备用3    0

油压传感器开路告警
    连接CSU
    设置子工具值    oileng    all    告警    备用7    4096
    ${级别设置值}    获取web参数量    油压传感器开路告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油压传感器开路告警    主要
    wait until keyword succeeds    5m    5    查询指定告警信息    油压传感器开路告警
    设置子工具值    oileng    all    告警    备用7    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油压传感器开路告警
    [Teardown]    设置子工具值    oileng    all    告警    备用7    0

油机合闸失败
    连接CSU
    设置子工具值    oileng    all    告警    备用9    2
    ${级别设置值}    获取web参数量    油机合闸失败
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机合闸失败    主要
    wait until keyword succeeds    5m    5    查询指定告警信息    油机合闸失败
    设置子工具值    oileng    all    告警    备用9    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机合闸失败
    [Teardown]    设置子工具值    oileng    all    告警    备用9    0

油机分闸失败
    连接CSU
    设置子工具值    oileng    all    告警    预报警代码    28
    设置子工具值    oileng    all    告警    报警代码    28
    ${级别设置值}    获取web参数量    油机分闸失败
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机分闸失败    主要
    wait until keyword succeeds    5m    5    查询指定告警信息    油机分闸失败
    设置子工具值    oileng    all    告警    预报警代码    0
    设置子工具值    oileng    all    告警    报警代码    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机分闸失败
    [Teardown]    run keywords    设置子工具值    oileng    all    告警    预报警代码
    ...    0
    ...    AND    设置子工具值    oileng    all    告警    报警代码
    ...    0

市电合闸失败
    连接CSU
    设置子工具值    oileng    all    告警    备用9    1
    ${级别设置值}    获取web参数量    市电合闸失败
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    市电合闸失败    主要
    wait until keyword succeeds    5m    5    查询指定告警信息    市电合闸失败
    设置子工具值    oileng    all    告警    备用9    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    市电合闸失败
    [Teardown]    设置子工具值    oileng    all    告警    备用9    0

市电分闸失败
    连接CSU
    设置子工具值    oileng    all    告警    预报警代码    30
    设置子工具值    oileng    all    告警    报警代码    30
    ${级别设置值}    获取web参数量    市电分闸失败
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    市电分闸失败    主要
    wait until keyword succeeds    5m    5    查询指定告警信息    市电分闸失败
    设置子工具值    oileng    all    告警    预报警代码    0
    设置子工具值    oileng    all    告警    报警代码    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    市电分闸失败
    [Teardown]    run keywords    设置子工具值    oileng    all    告警    预报警代码
    ...    0
    ...    AND    设置子工具值    oileng    all    告警    报警代码
    ...    0

油机控制屏通讯中断
    连接CSU
    #子设备工具模拟
    控制子工具运行停止    oileng    关闭
    ${级别设置值}    获取web参数量    油机控制屏通讯中断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机控制屏通讯中断    主要
    wait until keyword succeeds    5m    2    查询指定告警信息    油机控制屏通讯中断
    控制子工具运行停止    oileng    开启
    wait until keyword succeeds    5m    2    查询指定告警信息不为    油机控制屏通讯中断
    [Teardown]    控制子工具运行停止    oileng    开启
