*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
123243431055_直流电源插箱(B01.WX)(V3.05.00.03)
    连接CSU
    强制恢复默认值并重新登录
    ${导出参数文件1}    导出参数文件
    ${old_filename}    set variable    ${导出参数文件1}//config.xml
    ${起始时间}    获取系统时间
    ${导入成功}    ${导入配置文件}    导入配置文件    123243431055    power_para.zip
    should be true    ${导入成功}
    ${input_filename}    set variable    //${导入配置文件}//config_123243431055.xml
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    ${导出参数文件2}    导出参数文件
    ${new_filename}    set variable    ${导出参数文件2}//config.xml
    ${配置文件导入结果}    校验导入配置内容是否正确    ${old_filename}    ${new_filename}    ${input_filename}
    should be true    ${配置文件导入结果}
    删除文件夹以及文件夹下所有文件    config//123243431055////${导出参数文件1}
    删除文件    config//123243431055////${导出参数文件1}.zip

123243431055_直流电源插箱(B01.WX)(V3.05.00.03)--加密
    ${密码}    Set Variable    Power123!
    连接CSU
    强制恢复默认值并重新登录
    ${导出参数文件1}    导出参数文件    ${密码}
    ${old_filename}    set variable    ${导出参数文件1}//config.xml
    ${起始时间}    获取系统时间
    解压文件夹到指定文件夹    123243431055    power_para.zip    power_para1
    压缩配置文件夹    123243431055//power_para1
    ${导入成功}    ${导入配置文件}    导入配置文件    123243431055    power_para1.zip    ${密码}
    should be true    ${导入成功}
    ${input_filename}    set variable    //${导入配置文件}//config_123243431055.xml
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    ${导出参数文件2}    导出参数文件    ${密码}
    ${new_filename}    set variable    ${导出参数文件2}//config.xml
    ${配置文件导入结果}    校验导入配置内容是否正确    ${old_filename}    ${new_filename}    ${input_filename}
    should be true    ${配置文件导入结果}
    删除文件夹以及文件夹下所有文件    config//123243431055////${导出参数文件1}
    删除文件    config//123243431055////${导出参数文件1}.zip


部分配置禁止导入测试
    [Documentation]    禁止导入的配置：
    ...    ai通道零点和斜率
    ...    北向网络配置，有线网络配置
    连接CSU
    强制恢复默认值并重新登录
    ${导出参数文件1}    导出参数文件
    ${old_filename}    set variable    ${导出参数文件1}//config.xml
    ${dst_dir}    set variable    power_para
    ${dst_filename}    set variable    ${dst_dir}//config.xml
    复制文件目录    download//${导出参数文件1}    download//${dst_dir}
    #获取ai通道的零点和斜率
    ${通道对象Id}    Create Dictionary    id    plat.ai.factory_config
    ${通道1Id}    Create Dictionary    id    plat.battcurr1
    ${通道2Id}    Create Dictionary    id    plat.battcurr2
    ${零点}    Create Dictionary    name    Offset
    ${斜率}    Create Dictionary    name    Slope
    ${offset1}    获取配置文件属性值    ${old_filename}    ${通道对象Id}    ${零点}    ${通道1Id}
    ${slope1}    获取配置文件属性值    ${old_filename}    ${通道对象Id}    ${斜率}    ${通道1Id}
    ${offset2}    获取配置文件属性值    ${old_filename}    ${通道对象Id}    ${零点}    ${通道2Id}
    ${slope2}    获取配置文件属性值    ${old_filename}    ${通道对象Id}    ${斜率}    ${通道2Id}
    #获取北向网络配置
    ${有线网络IPV4配置id}    Create Dictionary    id    plat.wiredconnection
    ${有线网络IPV6配置id}    Create Dictionary    id    plat.wiredconnectionv6
    ${IPV4地址}    Create Dictionary    name    IP Address
    ${IPV6地址}    Create Dictionary    name    IPv6 Address
    ${IPv4 Address}    获取配置文件属性值    ${old_filename}    ${有线网络IPV4配置id}    ${IPV4地址}
    ${IPv6 Address}    获取配置文件属性值    ${old_filename}    ${有线网络IPV6配置id}    ${IPV6地址}
    ${配置文件1}    修改配置文件参数值    ${dst_filename}    ${通道对象Id}    ${零点}    30    ${通道1Id}
    生成配置文件    ${配置文件1}    ${dst_filename}
    ${配置文件2}    修改配置文件参数值    ${dst_filename}    ${通道对象Id}    ${斜率}    2    ${通道1Id}
    生成配置文件    ${配置文件2}    ${dst_filename}
    ${配置文件3}    修改配置文件参数值    ${dst_filename}    ${通道对象Id}    ${零点}    30    ${通道2Id}
    生成配置文件    ${配置文件3}    ${dst_filename}
    ${配置文件4}    修改配置文件参数值    ${dst_filename}    ${通道对象Id}    ${斜率}    2    ${通道2Id}
    生成配置文件    ${配置文件4}    ${dst_filename}
    ${配置文件5}    修改配置文件参数值    ${dst_filename}    ${有线网络IPV4配置id}    ${IPV4地址}    0.0.0.0
    生成配置文件    ${配置文件5}    ${dst_filename}
    ${配置文件6}    修改配置文件参数值    ${dst_filename}    ${有线网络IPV6配置id}    ${IPV6地址}    0.0.0.0.0.0.0.0
    生成配置文件    ${配置文件6}    ${dst_filename}
    压缩文件夹    ${dst_dir}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${dst_dir}.zip
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    ${导出参数文件2}    导出参数文件
    ${new_filename}    set variable    ${导出参数文件2}//config.xml
    ${offset1_new}    获取配置文件属性值    ${new_filename}    ${通道对象Id}    ${零点}    ${通道1Id}
    ${slope1_new}    获取配置文件属性值    ${new_filename}    ${通道对象Id}    ${斜率}    ${通道1Id}
    ${offset2_new}    获取配置文件属性值    ${new_filename}    ${通道对象Id}    ${零点}    ${通道2Id}
    ${slope2_new}    获取配置文件属性值    ${new_filename}    ${通道对象Id}    ${斜率}    ${通道2Id}
    ${IPv4 Address_new}    获取配置文件属性值    ${new_filename}    ${有线网络IPV4配置id}    ${IPV4地址}
    ${IPv6 Address_new}    获取配置文件属性值    ${new_filename}    ${有线网络IPV6配置id}    ${IPV6地址}
    should be equal    ${offset1}    ${offset1_new}
    should be equal    ${slope1}    ${slope1_new}
    should be equal    ${offset2}    ${offset2_new}
    should be equal    ${slope2}    ${slope2_new}
    should be equal    ${IPv4 Address}    ${IPv4 Address_new}
    should be equal    ${IPv6 Address}    ${IPv6 Address_new}
    删除文件夹以及文件夹下所有文件    download//${导出参数文件1}
    删除文件    download//${导出参数文件1}.zip

部分配置禁止导入测试--加密
    [Documentation]    禁止导入的配置：
    ...    ai通道零点和斜率
    ...    北向网络配置，有线网络配置
    ${密码}    Set Variable    Power123!
    连接CSU
    强制恢复默认值并重新登录
    ${导出参数文件1}    导出参数文件    ${密码}
    ${old_filename}    set variable    ${导出参数文件1}//config.xml
    ${dst_dir}    set variable    power_para
    ${dst_filename}    set variable    ${dst_dir}//config.xml
    复制文件目录    download//${导出参数文件1}    download//${dst_dir}
    #获取ai通道的零点和斜率
    ${通道对象Id}    Create Dictionary    id    plat.ai.factory_config
    ${通道1Id}    Create Dictionary    id    plat.battcurr1
    ${通道2Id}    Create Dictionary    id    plat.battcurr2
    ${零点}    Create Dictionary    name    Offset
    ${斜率}    Create Dictionary    name    Slope
    ${offset1}    获取配置文件属性值    ${old_filename}    ${通道对象Id}    ${零点}    ${通道1Id}
    ${slope1}    获取配置文件属性值    ${old_filename}    ${通道对象Id}    ${斜率}    ${通道1Id}
    ${offset2}    获取配置文件属性值    ${old_filename}    ${通道对象Id}    ${零点}    ${通道2Id}
    ${slope2}    获取配置文件属性值    ${old_filename}    ${通道对象Id}    ${斜率}    ${通道2Id}
    #获取北向网络配置
    ${有线网络IPV4配置id}    Create Dictionary    id    plat.wiredconnection
    ${有线网络IPV6配置id}    Create Dictionary    id    plat.wiredconnectionv6
    ${IPV4地址}    Create Dictionary    name    IP Address
    ${IPV6地址}    Create Dictionary    name    IPv6 Address
    ${IPv4 Address}    获取配置文件属性值    ${old_filename}    ${有线网络IPV4配置id}    ${IPV4地址}
    ${IPv6 Address}    获取配置文件属性值    ${old_filename}    ${有线网络IPV6配置id}    ${IPV6地址}
    ${配置文件1}    修改配置文件参数值    ${dst_filename}    ${通道对象Id}    ${零点}    30    ${通道1Id}
    生成配置文件    ${配置文件1}    ${dst_filename}
    ${配置文件2}    修改配置文件参数值    ${dst_filename}    ${通道对象Id}    ${斜率}    2    ${通道1Id}
    生成配置文件    ${配置文件2}    ${dst_filename}
    ${配置文件3}    修改配置文件参数值    ${dst_filename}    ${通道对象Id}    ${零点}    30    ${通道2Id}
    生成配置文件    ${配置文件3}    ${dst_filename}
    ${配置文件4}    修改配置文件参数值    ${dst_filename}    ${通道对象Id}    ${斜率}    2    ${通道2Id}
    生成配置文件    ${配置文件4}    ${dst_filename}
    ${配置文件5}    修改配置文件参数值    ${dst_filename}    ${有线网络IPV4配置id}    ${IPV4地址}    0.0.0.0
    生成配置文件    ${配置文件5}    ${dst_filename}
    ${配置文件6}    修改配置文件参数值    ${dst_filename}    ${有线网络IPV6配置id}    ${IPV6地址}    0.0.0.0.0.0.0.0
    生成配置文件    ${配置文件6}    ${dst_filename}
    压缩文件夹    ${dst_dir}    ${密码}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${dst_dir}.zip    ${密码}
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    ${导出参数文件2}    导出参数文件    ${密码}
    ${new_filename}    set variable    ${导出参数文件2}//config.xml
    ${offset1_new}    获取配置文件属性值    ${new_filename}    ${通道对象Id}    ${零点}    ${通道1Id}
    ${slope1_new}    获取配置文件属性值    ${new_filename}    ${通道对象Id}    ${斜率}    ${通道1Id}
    ${offset2_new}    获取配置文件属性值    ${new_filename}    ${通道对象Id}    ${零点}    ${通道2Id}
    ${slope2_new}    获取配置文件属性值    ${new_filename}    ${通道对象Id}    ${斜率}    ${通道2Id}
    ${IPv4 Address_new}    获取配置文件属性值    ${new_filename}    ${有线网络IPV4配置id}    ${IPV4地址}
    ${IPv6 Address_new}    获取配置文件属性值    ${new_filename}    ${有线网络IPV6配置id}    ${IPV6地址}
    should be equal    ${offset1}    ${offset1_new}
    should be equal    ${slope1}    ${slope1_new}
    should be equal    ${offset2}    ${offset2_new}
    should be equal    ${slope2}    ${slope2_new}
    should be equal    ${IPv4 Address}    ${IPv4 Address_new}
    should be equal    ${IPv6 Address}    ${IPv6 Address_new}
    删除文件夹以及文件夹下所有文件    download//${导出参数文件1}
    删除文件    download//${导出参数文件1}.zip


新增配置实例后导入测试
    [Documentation]    新增SNMP V3用户
    连接CSU
    强制恢复默认值并重新登录
    ${导出参数文件1}    导出参数文件
    ${old_filename}    set variable    ${导出参数文件1}//config.xml
    ${dst_dir}    set variable    power_para
    ${dst_filename}    set variable    ${dst_dir}//config.xml
    复制文件目录    download//${导出参数文件1}    download//${dst_dir}
    ${SNMP V3用户对象id}    Create Dictionary    id    plat.snmp_v3user
    ${SNMP V3用户1}    Create Dictionary    id    plat.snmp_v3user1
    ${SNMP用户名}    Create Dictionary    name    SNMP User Name
    ${SNMP认证协议}    Create Dictionary    name    SNMP Authentication Protocol
    ${SNMP认证密钥}    Create Dictionary    name    SNMP Authentication Key
    ${SNMP加密协议}    Create Dictionary    name    SNMP Privacy Protocol
    ${SNMP隐私密钥}    Create Dictionary    name    SNMP Privacy Key
    ${SNMP权限范围}    Create Dictionary    name    SNMP Permission Scope
    #新增实例后导入
    ${配置文件1}    新增配置实例    ${dst_filename}    ${SNMP V3用户对象id}    ${SNMP V3用户1}
    生成配置文件    ${配置文件1}    ${dst_filename}
    ${配置文件2}    新增配置属性    ${dst_filename}    ${SNMP V3用户对象id}    ${SNMP V3用户1}    ${SNMP用户名}    zteuser1
    生成配置文件    ${配置文件2}    ${dst_filename}
    ${配置文件3}    新增配置属性    ${dst_filename}    ${SNMP V3用户对象id}    ${SNMP V3用户1}    ${SNMP认证协议}    0
    生成配置文件    ${配置文件3}    ${dst_filename}
    ${配置文件4}    新增配置属性    ${dst_filename}    ${SNMP V3用户对象id}    ${SNMP V3用户1}    ${SNMP认证密钥}    PowerAuth123!
    生成配置文件    ${配置文件4}    ${dst_filename}
    ${配置文件5}    新增配置属性    ${dst_filename}    ${SNMP V3用户对象id}    ${SNMP V3用户1}    ${SNMP加密协议}    0
    生成配置文件    ${配置文件5}    ${dst_filename}
    ${配置文件6}    新增配置属性    ${dst_filename}    ${SNMP V3用户对象id}    ${SNMP V3用户1}    ${SNMP隐私密钥}    PowerPri123!
    生成配置文件    ${配置文件6}    ${dst_filename}
    ${配置文件7}    新增配置属性    ${dst_filename}    ${SNMP V3用户对象id}    ${SNMP V3用户1}    ${SNMP权限范围}    1
    生成配置文件    ${配置文件7}    ${dst_filename}
    压缩文件夹    ${dst_dir}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${dst_dir}.zip
    sleep    15
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    ${导出参数文件2}    导出参数文件
    ${new_filename}    set variable    ${导出参数文件2}//config.xml
    ${配置文件导入结果}    校验导入配置内容是否正确    ${old_filename}    ${new_filename}    //${dst_filename}    download
    should be true    ${配置文件导入结果}
    删除文件夹以及文件夹下所有文件    download//${导出参数文件1}
    删除文件    download//${导出参数文件1}.zip

新增配置实例后导入测试--加密
    [Documentation]    新增SNMP V3用户
    ${密码}    Set Variable    Power123!
    连接CSU
    强制恢复默认值并重新登录
    ${导出参数文件1}    导出参数文件    ${密码}
    ${old_filename}    set variable    ${导出参数文件1}//config.xml
    ${dst_dir}    set variable    power_para
    ${dst_filename}    set variable    ${dst_dir}//config.xml
    复制文件目录    download//${导出参数文件1}    download//${dst_dir}
    ${SNMP V3用户对象id}    Create Dictionary    id    plat.snmp_v3user
    ${SNMP V3用户1}    Create Dictionary    id    plat.snmp_v3user1
    ${SNMP用户名}    Create Dictionary    name    SNMP User Name
    ${SNMP认证协议}    Create Dictionary    name    SNMP Authentication Protocol
    ${SNMP认证密钥}    Create Dictionary    name    SNMP Authentication Key
    ${SNMP加密协议}    Create Dictionary    name    SNMP Privacy Protocol
    ${SNMP隐私密钥}    Create Dictionary    name    SNMP Privacy Key
    ${SNMP权限范围}    Create Dictionary    name    SNMP Permission Scope
    #新增实例后导入
    ${配置文件1}    新增配置实例    ${dst_filename}    ${SNMP V3用户对象id}    ${SNMP V3用户1}
    生成配置文件    ${配置文件1}    ${dst_filename}
    ${配置文件2}    新增配置属性    ${dst_filename}    ${SNMP V3用户对象id}    ${SNMP V3用户1}    ${SNMP用户名}    zteuser1
    生成配置文件    ${配置文件2}    ${dst_filename}
    ${配置文件3}    新增配置属性    ${dst_filename}    ${SNMP V3用户对象id}    ${SNMP V3用户1}    ${SNMP认证协议}    0
    生成配置文件    ${配置文件3}    ${dst_filename}
    ${配置文件4}    新增配置属性    ${dst_filename}    ${SNMP V3用户对象id}    ${SNMP V3用户1}    ${SNMP认证密钥}    PowerAuth123!
    生成配置文件    ${配置文件4}    ${dst_filename}
    ${配置文件5}    新增配置属性    ${dst_filename}    ${SNMP V3用户对象id}    ${SNMP V3用户1}    ${SNMP加密协议}    0
    生成配置文件    ${配置文件5}    ${dst_filename}
    ${配置文件6}    新增配置属性    ${dst_filename}    ${SNMP V3用户对象id}    ${SNMP V3用户1}    ${SNMP隐私密钥}    PowerPri123!
    生成配置文件    ${配置文件6}    ${dst_filename}
    ${配置文件7}    新增配置属性    ${dst_filename}    ${SNMP V3用户对象id}    ${SNMP V3用户1}    ${SNMP权限范围}    1
    生成配置文件    ${配置文件7}    ${dst_filename}
    压缩文件夹    ${dst_dir}    ${密码}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${dst_dir}.zip    ${密码}
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    ${导出参数文件2}    导出参数文件    ${密码}
    ${new_filename}    set variable    ${导出参数文件2}//config.xml
    ${配置文件导入结果}    校验导入配置内容是否正确    ${old_filename}    ${new_filename}    //${dst_filename}    download
    should be true    ${配置文件导入结果}
    删除文件夹以及文件夹下所有文件    download//${导出参数文件1}
    删除文件    download//${导出参数文件1}.zip

配置文件属性值修改后导入测试
    [Documentation]    修改1104、无线统一协议服务端口号
    连接CSU
    强制恢复默认值并重新登录
    ${导出参数文件1}    导出参数文件
    ${old_filename}    set variable    ${导出参数文件1}//config.xml
    ${dst_dir}    set variable    power_para
    ${dst_filename}    set variable    ${dst_dir}//config.xml
    复制文件目录    download//${导出参数文件1}    download//${dst_dir}
    ${1104配置对象id}    Create Dictionary    id    pdt.1104_nms_ip_comm
    ${服务端口}    Create Dictionary    name    listen port
    ${sm配置对象id}    Create Dictionary    id    pdt.sm_nms_ip_comm
    ${1104服务端口}    获取配置文件属性值    ${old_filename}    ${1104配置对象id}    ${服务端口}
    ${sm服务端口}    获取配置文件属性值    ${old_filename}    ${sm配置对象id}    ${服务端口}
    ${配置文件1}    修改配置文件参数值    ${dst_filename}    ${1104配置对象id}    ${服务端口}    4555
    生成配置文件    ${配置文件1}    ${dst_filename}
    ${配置文件2}    修改配置文件参数值    ${dst_filename}    ${sm配置对象id}    ${服务端口}    5555
    生成配置文件    ${配置文件2}    ${dst_filename}
    压缩文件夹    ${dst_dir}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${dst_dir}.zip
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    ${导出参数文件2}    导出参数文件
    ${new_filename}    set variable    ${导出参数文件2}//config.xml
    ${配置文件导入结果}    校验导入配置内容是否正确    ${old_filename}    ${new_filename}    //${dst_filename}    download
    should be true    ${配置文件导入结果}
    删除文件夹以及文件夹下所有文件    download//${导出参数文件1}
    删除文件    download//${导出参数文件1}.zip

配置文件属性值修改后导入测试--加密
    [Documentation]    修改1104、无线统一协议服务端口号
    ${密码}    Set Variable    Power123!
    连接CSU
    强制恢复默认值并重新登录
    ${导出参数文件1}    导出参数文件    ${密码}
    ${old_filename}    set variable    ${导出参数文件1}//config.xml
    ${dst_dir}    set variable    power_para
    ${dst_filename}    set variable    ${dst_dir}//config.xml
    复制文件目录    download//${导出参数文件1}    download//${dst_dir}
    ${1104配置对象id}    Create Dictionary    id    pdt.1104_nms_ip_comm
    ${服务端口}    Create Dictionary    name    listen port
    ${sm配置对象id}    Create Dictionary    id    pdt.sm_nms_ip_comm
    ${1104服务端口}    获取配置文件属性值    ${old_filename}    ${1104配置对象id}    ${服务端口}
    ${sm服务端口}    获取配置文件属性值    ${old_filename}    ${sm配置对象id}    ${服务端口}
    ${配置文件1}    修改配置文件参数值    ${dst_filename}    ${1104配置对象id}    ${服务端口}    4555
    生成配置文件    ${配置文件1}    ${dst_filename}
    ${配置文件2}    修改配置文件参数值    ${dst_filename}    ${sm配置对象id}    ${服务端口}    5555
    生成配置文件    ${配置文件2}    ${dst_filename}
    压缩文件夹    ${dst_dir}    ${密码}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${dst_dir}.zip    ${密码}
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    ${导出参数文件2}    导出参数文件    ${密码}
    ${new_filename}    set variable    ${导出参数文件2}//config.xml
    ${配置文件导入结果}    校验导入配置内容是否正确    ${old_filename}    ${new_filename}    //${dst_filename}    download
    should be true    ${配置文件导入结果}
    删除文件夹以及文件夹下所有文件    download//${导出参数文件1}
    删除文件    download//${导出参数文件1}.zip

配置文件属性值不合法导入测试
    [Documentation]    通道斜率有效范围-60~60
    连接CSU
    Comment    强制恢复默认值并重新登录
    ${导出参数文件1}    导出参数文件
    ${old_filename}    set variable    ${导出参数文件1}//config.xml
    ${dst_dir}    set variable    power_para
    ${dst_filename}    set variable    ${dst_dir}//config.xml
    复制文件目录    download//${导出参数文件1}    download//${dst_dir}
    ${通道对象Id}    Create Dictionary    id    plat.ai.factory_config
    ${通道1Id}    Create Dictionary    id    plat.battcurr1
    ${通道2Id}    Create Dictionary    id    plat.battcurr2
    ${零点}    Create Dictionary    name    Offset
    ${斜率}    Create Dictionary    name    Slope
    ${配置文件1}    修改配置文件参数值    ${dst_filename}    ${通道对象Id}    ${零点}    100    ${通道1Id}
    生成配置文件    ${配置文件1}    ${dst_filename}
    ${配置文件2}    修改配置文件参数值    ${dst_filename}    ${通道对象Id}    ${零点}    100    ${通道2Id}
    生成配置文件    ${配置文件2}    ${dst_filename}
    压缩文件夹    ${dst_dir}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${dst_dir}.zip
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should not be true    ${导入结果}
    should contain match    ${历史事件内容1}    *导入 失败 因为 配置文件错误*
    删除文件夹以及文件夹下所有文件    download//${导出参数文件1}
    删除文件    download//${导出参数文件1}.zip

配置文件属性值不合法导入测试--加密
    [Documentation]    通道斜率有效范围-60~60
    ${密码}    Set Variable    Power123!
    连接CSU
    Comment    强制恢复默认值并重新登录
    ${导出参数文件1}    导出参数文件    ${密码}
    ${old_filename}    set variable    ${导出参数文件1}//config.xml
    ${dst_dir}    set variable    power_para
    ${dst_filename}    set variable    ${dst_dir}//config.xml
    复制文件目录    download//${导出参数文件1}    download//${dst_dir}
    ${通道对象Id}    Create Dictionary    id    plat.ai.factory_config
    ${通道1Id}    Create Dictionary    id    plat.battcurr1
    ${通道2Id}    Create Dictionary    id    plat.battcurr2
    ${零点}    Create Dictionary    name    Offset
    ${斜率}    Create Dictionary    name    Slope
    ${配置文件1}    修改配置文件参数值    ${dst_filename}    ${通道对象Id}    ${零点}    100    ${通道1Id}
    生成配置文件    ${配置文件1}    ${dst_filename}
    ${配置文件2}    修改配置文件参数值    ${dst_filename}    ${通道对象Id}    ${零点}    100    ${通道2Id}
    生成配置文件    ${配置文件2}    ${dst_filename}
    压缩文件夹    ${dst_dir}    ${密码}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${dst_dir}.zip    ${密码}
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should not be true    ${导入结果}
    should contain match    ${历史事件内容1}    *导入 失败 因为 配置文件错误*
    删除文件夹以及文件夹下所有文件    download//${导出参数文件1}
    删除文件    download//${导出参数文件1}.zip


配置文件结构导入不合法测试
    连接CSU
    ${导出参数文件1}    导出参数文件
    ${dst_dir1}    set variable    power_para1
    ${dst_filename1}    set variable    ${dst_dir1}//config.xml
    复制文件目录    download//${导出参数文件1}    download//${dst_dir1}
    #删除<config>，并导入
    删除配置文件结构    ${dst_filename1}    <config>
    压缩文件夹    ${dst_dir1}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${dst_dir1}.zip
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should not be true    ${导入结果}
    should contain match    ${历史事件内容1}    *导入 失败 因为 配置文件错误*
    #删除</row>
    ${导出参数文件2}    导出参数文件
    ${dst_dir2}    set variable    power_para2
    ${dst_filename2}    set variable    ${dst_dir2}//config.xml
    复制文件目录    download//${导出参数文件2}    download//${dst_dir2}
    删除配置文件结构    ${dst_filename2}    </row>
    压缩文件夹    ${dst_dir2}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${dst_dir2}.zip
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容2}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should not be true    ${导入结果}
    should contain match    ${历史事件内容2}    *导入 失败 因为 配置文件错误*
    删除文件夹以及文件夹下所有文件    download//${导出参数文件1}
    删除文件    download//${导出参数文件1}.zip

配置文件结构导入不合法测试--加密
    ${密码}    Set Variable    Power123!
    连接CSU
    ${导出参数文件1}    导出参数文件    ${密码}
    ${dst_dir1}    set variable    power_para1
    ${dst_filename1}    set variable    ${dst_dir1}//config.xml
    复制文件目录    download//${导出参数文件1}    download//${dst_dir1}
    #删除<config>，并导入
    删除配置文件结构    ${dst_filename1}    <config>
    压缩文件夹    ${dst_dir1}    ${密码}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${dst_dir1}.zip    ${密码}
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should not be true    ${导入结果}
    should contain match    ${历史事件内容1}    *导入 失败 因为 配置文件错误*
    #删除</row>
    ${导出参数文件2}    导出参数文件    ${密码}
    ${dst_dir2}    set variable    power_para2
    ${dst_filename2}    set variable    ${dst_dir2}//config.xml
    复制文件目录    download//${导出参数文件2}    download//${dst_dir2}
    删除配置文件结构    ${dst_filename2}    </row>
    压缩文件夹    ${dst_dir2}    ${密码}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${dst_dir2}.zip    ${密码}
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容2}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should not be true    ${导入结果}
    should contain match    ${历史事件内容2}    *导入 失败 因为 配置文件错误*
    删除文件夹以及文件夹下所有文件    download//${导出参数文件1}
    删除文件    download//${导出参数文件1}.zip












