*** Settings ***
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp_test关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/Web关键字_V30.robot
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
snmp_0002_gCP1control_gCPReset
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    FOR    ${i}    IN RANGE    0    4
        ${设置控制量}    设置SNMP控制量    gCPReset1value
        ${存在结果}    判断web历史记录存在snmp控制内容    油机控制屏    油机控制屏复位    360    1
        Exit For Loop if    '${存在结果}'=='True'
        sleep    10
    END
    断开连接SNMP

snmp_0004_gCP1control_gCPCtrlDGEnterManualModet
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    Wait Until Keyword Succeeds    10m    5    设置web参数量    交流输入场景    纯油机
    FOR    ${i}    IN RANGE    0    4
        ${设置控制量}    设置SNMP控制量    gCPCtrlDGEnterManualMode1value
        ${存在结果}    判断web历史记录存在snmp控制内容    油机控制屏    油机进入手动模式    180    1
        Exit For Loop if    '${存在结果}'=='True'
        sleep    10
    END
    设置子工具值    oileng    all    只读    灯亮为    4
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    油机控制器工作模式    手动
    断开连接SNMP

snmp_0006_gCP1control_gCPCtrlDGEnterAutomaticModet
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    Wait Until Keyword Succeeds    10m    5    设置web参数量    交流输入场景    纯油机
    FOR    ${i}    IN RANGE    0    4
        ${设置控制量}    设置SNMP控制量    gCPCtrlDGEnterManualMode1value
        ${存在结果}    判断web历史记录存在snmp控制内容    油机控制屏    油机进入自动模式    180    1
        Exit For Loop if    '${存在结果}'=='True'
        sleep    10
    END
    设置子工具值    oileng    all    只读    灯亮为    8
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    油机控制器工作模式    自动
    断开连接SNMP
