*** Settings ***
Suite Setup       主动告警测试前置条件    ${CSU_role}
Suite Teardown    设置web设备参数量为默认值    CSU主动告警使能
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0002_烟雾告警
    [Tags]    T1-1
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    实时告警刷新完成
    Wait Until Keyword Succeeds    30    1    设置web参数量    烟雾告警    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    烟雾告警
    ${烟雾告警}    判断告警存在_带返回值    烟雾告警
    should not be true    ${烟雾告警}
    模拟数字量告警    烟雾告警    ON
    sleep    10
    ${烟雾状态}    获取干接点状态    ${plat.smoke}
    should be true    ${烟雾状态}==1    \    #0为正常（闭合），1为断开（断开）
    ${告警级别取值约定dict}    获取web参数的取值约定    烟雾告警
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        wait until keyword succeeds    10    1    设置web参数量    烟雾告警    ${告警级别设置}
        sleep   5
        wait until keyword succeeds    2m    1    查询指定告警信息    烟雾告警
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    系统运行环境    烟雾告警
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    烟雾告警    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
        ${烟雾告警级别}    获取web告警属性    烟雾告警    告警级别
        should be equal    ${告警级别设置}    ${烟雾告警级别}
    END
    模拟数字量告警    烟雾告警    OFF
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    烟雾告警
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    烟雾告警    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    设置web设备参数量为默认值    烟雾告警
    ...    AND    模拟数字量告警    烟雾告警    OFF
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0003_水淹告警
    [Tags]    T1-1
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    实时告警刷新完成
    Wait Until Keyword Succeeds    30    1    设置web参数量    水淹告警    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    水淹告警
    ${烟雾告警}    判断告警存在_带返回值    水淹告警
    should not be true    ${烟雾告警}
    模拟数字量告警    水淹告警    ON
    sleep    10
    ${状态}    获取干接点状态    ${plat.Water}
    should be true    ${状态}==1    \    #0为正常（闭合），1为断开（断开）
    ${告警级别取值约定dict}    获取web参数的取值约定    水淹告警
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        wait until keyword succeeds    10    1    设置web参数量    水淹告警    ${告警级别设置}
        sleep   5
        wait until keyword succeeds    2m    1    查询指定告警信息    水淹告警
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    系统运行环境    水淹告警
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    水淹告警    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
        ${烟雾告警级别}    获取web告警属性    水淹告警    告警级别
        should be equal    ${告警级别设置}    ${烟雾告警级别}
    END
    模拟数字量告警    水淹告警    OFF
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    水淹告警
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    水淹告警    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    设置web设备参数量为默认值    水淹告警
    ...    AND    模拟数字量告警    水淹告警    OFF
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0004_门磁告警
    [Tags]    T1-1
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    实时告警刷新完成
    Wait Until Keyword Succeeds    30    1    设置web参数量    门磁告警    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    门磁告警
    ${烟雾告警}    判断告警存在_带返回值    门磁告警
    should not be true    ${烟雾告警}
    模拟数字量告警    门磁告警    ON
    sleep    10
    ${状态}    获取干接点状态   SPB_J3_DOOR
    should be true    ${状态}==1    \    #0为正常（闭合），1为断开（断开）
    ${告警级别取值约定dict}    获取web参数的取值约定    门磁告警
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        wait until keyword succeeds    10    1    设置web参数量    门磁告警    ${告警级别设置}
        sleep   5
        wait until keyword succeeds    2m    1    查询指定告警信息    门磁告警
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    系统运行环境    门磁告警
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    门磁告警    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
        ${烟雾告警级别}    获取web告警属性    门磁告警    告警级别
        should be equal    ${告警级别设置}    ${烟雾告警级别}
    END
    模拟数字量告警    门磁告警    OFF
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    门磁告警
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    门磁告警    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    设置web设备参数量为默认值    门磁告警
    ...    AND    模拟数字量告警    门磁告警    OFF
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0005_环境温度高
    [Documentation]    高阈值范围：30-60，默认55；过高阈值范围：33-63，默认58。高比过高低3°
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    实时告警刷新完成
    ${级别设置值}    获取web参数量    环境温度高
    run keyword if    '${级别设置值}'=='屏蔽'    设置web设备参数量为默认值    环境温度高
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    环境温度高
    Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度过高阈值    58    #默认58，min33
    Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度高阈值    30    #默认55，min30
    ${环境温度}    Wait Until Keyword Succeeds    5m    1    获取web实时数据    环境温度
    FOR    ${调节步长}    IN RANGE    2    40    2
        ${零点}    evaluate    str(${调节步长})
        Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    ${零点}    1    系统运行环境    环境温度
        sleep    3
        ${环境温度获取}    获取web实时数据    环境温度
        ${环境温度设置值}    获取web参数量    环境温度高阈值
        exit for loop if    ${环境温度获取}>${ 环境温度设置值}
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度高    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    环境温度高
    ${环境温度高告警}    判断告警存在_带返回值    环境温度高
    should not be true    ${环境温度高告警}
    ###以上告警已产生，先屏蔽
    wait until keyword succeeds    3m    2    设置web设备参数量为默认值    环境温度高
    ${告警级别取值约定dict}    获取web参数的取值约定    环境温度高
    ${级别设置值}    获取web参数量    环境温度高
    wait until keyword succeeds    1m    1    判断告警存在    环境温度高
    Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client'    主动告警上送判断2    power_sm    系统运行环境    环境温度高
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    环境温度高    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    #无告警后查询判断
    环境温度参数恢复默认值
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    环境温度高
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    环境温度高    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    Run keywords    环境温度参数恢复默认值
    ...    AND    设置通道配置    ${plat.envtemp}    0    1    系统运行环境    环境温度
    ...    AND    设置web设备参数量为默认值    环境温度高
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0006_环境温度低
    [Documentation]    低阈值范围：-27~23，默认-5；过低阈值范围：-30~20，默认-8
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    实时告警刷新完成
    ${级别设置值}    获取web参数量    环境温度低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web设备参数量为默认值    环境温度低
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    环境温度低
    ${可设置范围}    获取web参数可设置范围    环境温度低阈值
    设置web参数量    环境温度低阈值    ${可设置范围}[1]
    ${环境温度}    Wait Until Keyword Succeeds    5m    1    获取web实时数据    环境温度
    FOR    ${调节步长}    IN RANGE    -5    -50    -5
        ${零点}    evaluate    str(${调节步长})
        Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    ${零点}    1    系统运行环境    环境温度
        sleep    3
        ${环境温度获取}    获取web实时数据    环境温度
        ${环境温度设置值}    获取web参数量    环境温度低阈值
        exit for loop if    ${环境温度获取}<${ 环境温度设置值}
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度低    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    环境温度低
    ${环境温度低告警}    判断告警存在_带返回值    环境温度低
    should not be true    ${环境温度低告警}
    ###以上告警已产生，先屏蔽
    wait until keyword succeeds    3m    2    设置web设备参数量为默认值    环境温度低
    ${告警级别取值约定dict}    获取web参数的取值约定    环境温度低
    ${级别设置值}    获取web参数量    环境温度低
    wait until keyword succeeds    1m    1    判断告警存在    环境温度低
    Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client'    主动告警上送判断2    power_sm    系统运行环境    环境温度低
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    环境温度低    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    #无告警后查询判断
    环境温度参数恢复默认值
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    环境温度低
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    环境温度低    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    Run keywords    环境温度参数恢复默认值
    ...    AND    设置通道配置    ${plat.envtemp}    0    1    系统运行环境    环境温度
    ...    AND    设置web设备参数量为默认值    环境温度低
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0007_环境湿度无效
    [Documentation]    默认屏蔽！！！！
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    #不接入传感器
    设置通道无效值/恢复通道原始值    ${plat.humity}    1
    实时告警刷新完成
    设置web参数量    环境湿度无效    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    环境湿度无效
    ${环境湿度无效告警}    判断告警存在_带返回值    环境湿度无效
    should not be true    ${环境湿度无效告警}
    Wait Until Keyword Succeeds    30    1    设置web参数量    环境湿度无效    次要
    ${告警级别取值约定dict}    获取web参数的取值约定    环境湿度无效
    ${级别设置值}    获取web参数量    环境湿度无效
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    环境湿度无效
    Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client'    主动告警上送判断2    power_sm    系统运行环境    环境湿度无效
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    环境湿度无效    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    #消失
    Wait Until Keyword Succeeds    5m    2    设置web参数量    环境湿度无效    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    环境湿度无效
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    环境湿度无效    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    设置通道无效值/恢复通道原始值    ${plat.humity}    0
    [Teardown]    run keywords    设置web设备参数量为默认值    环境湿度无效
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0008_环境温度过高
    [Documentation]    高阈值范围：30-60，默认55；过高阈值范围：33-63，默认58。高比过高低3°
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    实时告警刷新完成
    ${级别设置值}    获取web参数量    环境温度过高
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    环境温度过高    主要
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    环境温度过高
    ${可设置范围}    获取web参数可设置范围    环境温度高阈值
    设置web参数量    环境温度高阈值    ${可设置范围}[0]
    ${可设置范围}    获取web参数可设置范围    环境温度过高阈值
    设置web参数量    环境温度过高阈值    ${可设置范围}[0]
    ${环境温度}    Wait Until Keyword Succeeds    10    1    获取web实时数据    环境温度
    FOR    ${调节步长}    IN RANGE    2    40    2
        ${零点}    evaluate    str(${调节步长})
        Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    ${零点}    1    系统运行环境    环境温度
        sleep    3
        ${环境温度获取}    获取web实时数据    环境温度
        ${环境温度设置值}    获取web参数量    环境温度过高阈值
        exit for loop if    ${环境温度获取}>${ 环境温度设置值}
    END
    wait until keyword succeeds    1m    1    查询指定告警信息    环境温度过高
    Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client'    主动告警上送判断2    power_sm    系统运行环境    环境温度过高
    ${告警级别取值约定dict}    获取web参数的取值约定    环境温度过高
    ${级别设置值}    获取web参数量    环境温度过高
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    环境温度过高    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    #无告警后查询判断
    环境温度参数恢复默认值
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    环境温度过高
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    环境温度过高    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    Run keywords    环境温度参数恢复默认值
    ...    AND    设置通道配置    ${plat.envtemp}    0    1    系统运行环境    环境温度
    ...    AND    设置web设备参数量为默认值    环境温度高
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0009_环境温度过低
    [Documentation]    低阈值范围：-27~23，默认-5；过低阈值范围：-30~20，默认-8
    [Tags]    T1-1
    [Setup]    run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    实时告警刷新完成
    ${级别设置值}    获取web参数量    环境温度过低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    环境温度过低    次要
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    环境温度过低
    ${可设置范围}    获取web参数可设置范围    环境温度低阈值
    设置web参数量    环境温度低阈值    ${可设置范围}[1]
    ${可设置范围}    获取web参数可设置范围    环境温度过低阈值
    设置web参数量    环境温度过低阈值    ${可设置范围}[1]
    ${环境温度}    Wait Until Keyword Succeeds    5m    1    获取web实时数据    环境温度
    FOR    ${调节步长}    IN RANGE    -2    -50    -2
        ${零点}    evaluate    str(${调节步长})
        Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    ${零点}    1    系统运行环境    环境温度
        sleep    3
        ${环境温度获取}    获取web实时数据    环境温度
        ${环境温度设置值}    获取web参数量    环境温度过低阈值
        exit for loop if    ${环境温度获取}<${ 环境温度设置值}
    END
    设置web参数量    环境温度过低    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    环境温度过低
    ${环境温度过低告警}    判断告警存在_带返回值    环境温度过低
    should not be true    ${环境温度过低告警}
    ###以上告警已产生，先屏蔽
    wait until keyword succeeds    3m    2    设置web设备参数量为默认值    环境温度过低
    Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client'    主动告警上送判断2    power_sm    系统运行环境    环境温度过低
    ${告警级别取值约定dict}    获取web参数的取值约定    环境温度过低
    ${级别设置值}    获取web参数量    环境温度过低
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    环境温度过低    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${级别设置值}
    #无告警后查询判断
    环境温度参数恢复默认值
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    环境温度过低
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    环境温度过低    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    Run keywords    环境温度参数恢复默认值
    ...    AND    设置通道配置    ${plat.envtemp}    0    1    系统运行环境    环境温度
    ...    AND    设置web设备参数量为默认值    环境温度过低
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0010_温控单元异常
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay4Status}    系统运行环境    温控单元异常    断开
    Wait Until Keyword Succeeds    10    2    设置web参数量    温控单元异常    屏蔽
    Wait Until Keyword Succeeds    3m    1    判断告警不存在    温控单元异常
    Wait Until Keyword Succeeds    5m    2    设置web参数量    温控单元异常    严重
    wait until keyword succeeds    1m    1    查询指定告警信息    温控单元异常
    ${告警级别取值约定dict}    获取web参数的取值约定    温控单元异常
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    温控单元异常    ${告警级别设置}
        sleep   5
        wait until keyword succeeds    1m    1    查询指定告警信息    温控单元异常
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    系统运行环境    温控单元异常
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    温控单元异常    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
        ${烟雾告警级别}    获取web告警属性    温控单元异常    告警级别
        should be equal    ${告警级别设置}    ${烟雾告警级别}
    END
    wait until keyword succeeds    30    1    设置web参数量    温控单元异常    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay4Status}    系统运行环境    温控单元异常    闭合
    Wait Until Keyword Succeeds    3m    1    判断告警不存在    温控单元异常
    [Teardown]    Run keywords    设置web设备参数量为默认值    温控单元异常
    ...    AND    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay4Status}    系统运行环境    温控单元异常    闭合
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接


