*** Settings ***
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp_test关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/Web关键字_V30.robot
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
snmp_0000_rectifier1deviceInfo
    Comment    ${工作整流器地址}    获取工作整流器地址
    FOR    ${整流器序号}    IN    @{实际工作整流器地址}
        ${比较结果}    对比数据_V2C    rectifier${整流器序号}deviceInfo
        should be true    ${比较结果}
    END

snmp_0001_批量获取SMR设备信息
    [Documentation]    11min
    写入CSV文档    整流器设备信息获取测试    信号名称    信号值    结果    备注
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string32    INDEX_DEVICE_CH=整流器
    ${排除列表}    create list
    @{信号名称列表1}   create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除设备版本信息}    ${排除列表}    2    ${模拟整流器地址}
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    rectifier    deviceInfo
    FOR    ${i}    IN    @{snmp待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    snmp_name
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
        Set To Dictionary    ${dict}     snmp_name     ${节点名称}
        Set To Dictionary    ${dict}     Vindex     ${信号序号}
		Append To List       ${信号名称列表1}    ${dict}
    END
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    SMR4000    ${信号名称列表1}    版本    V99.23    整流器设备信息获取测试    字符    null
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    SMR4000    ${信号名称列表1}    版本    V10.10    整流器设备信息获取测试    字符    null
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    SMR4000    ${信号名称列表1}    版本    V1.81    整流器设备信息获取测试    字符    null
    
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=float    INDEX_DEVICE_CH=整流器
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    2    ${模拟整流器地址}
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    rectifier    deviceInfo
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    1    snmp
    Run Keyword And Continue On Failure    南向子设备厂家信息列表封装判断结果    snmp    SMR4000    ${缺省值列表}    版本    整流器设备信息获取测试    字符    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    2    snmp
    Run Keyword And Continue On Failure    南向子设备厂家信息列表封装判断结果    snmp    SMR4000    ${缺省值列表}    版本    整流器设备信息获取测试    字符    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    0    snmp
    Run Keyword And Continue On Failure    南向子设备厂家信息列表封装判断结果    snmp    SMR4000    ${缺省值列表}    版本    整流器设备信息获取测试    字符    null

    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=int    INDEX_DEVICE_CH=整流器
    ${排除列表}    create list
    @{信号名称列表2}    create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    2    ${模拟整流器地址}
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    rectifier    deviceInfo
    FOR    ${i}    IN    @{snmp待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    snmp_name
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
        Set To Dictionary    ${dict}     snmp_name     ${节点名称}
        Set To Dictionary    ${dict}     Vindex     ${信号序号}
		Append To List       ${信号名称列表2}    ${dict}
    END
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    SMR4000    ${信号名称列表2}    版本    1    整流器设备信息获取测试    数值    null
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    SMR4000    ${信号名称列表2}    版本    1000    整流器设备信息获取测试    数值    null
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    SMR4000    ${信号名称列表2}    版本    1    整流器设备信息获取测试    数值    null

    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=date    INDEX_DEVICE_CH=整流器
    ${排除列表}    create list
    @{信号名称列表3}    create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除设备日期信息}    ${排除列表}    2    ${模拟整流器地址}
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    rectifier    deviceInfo
    FOR    ${i}    IN    @{snmp待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    snmp_name
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
        Set To Dictionary    ${dict}     snmp_name     ${节点名称}
        Set To Dictionary    ${dict}     Vindex     ${信号序号}
		Append To List       ${信号名称列表3}    ${dict}
    END
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    SMR4000    ${信号名称列表3}    版本    2018-11-15    整流器设备信息获取测试    字符    null
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    SMR4000    ${信号名称列表3}    版本    2021-08-23    整流器设备信息获取测试    字符    null
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    SMR4000    ${信号名称列表3}    版本    2018-07-28    整流器设备信息获取测试    字符    null

snmp_0002_整流器条码
    [Tags]    3
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    设置子工具值    SMR    all    资产管理信息    条码3    12522
    设置子工具值    SMR    all    资产管理信息    条码4    50416
    设置子工具值    SMR    all    资产管理信息    条码5g    178
    sleep    1m
    @{SMR随机list}    从1-15个数中随机选n个不重复的单体    ${北向协议SMR最大数}    5    ${模拟整流器开始地址}
    FOR    ${SMR序号}    IN    @{SMR随机list}
        ${web1}    获取web实时数据    整流器条码-${SMR序号}
        ${snmp获取值1}    获取SNMP数据_单个    sMRBarcodes${SMR序号}value
        should be equal as numbers    ${web1}    210097205426
        should be equal as numbers    ${snmp获取值1}    210097205426
    END
    断开连接SNMP

snmp_0003_生产日期
    [Tags]    3
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    设置子工具值    SMR    all    资产管理信息    生产日期年5d6g    2020
    设置子工具值    SMR    all    资产管理信息    生产日期月6d    11
    设置子工具值    SMR    all    资产管理信息    生产日期日7g    19
    sleep    1m
    @{SMR随机list}    从1-15个数中随机选n个不重复的单体    ${北向协议SMR最大数}    5    ${模拟整流器开始地址}
    FOR    ${SMR序号}    IN    @{SMR随机list}
        ${web1}    获取web实时数据    <<生产日期-${SMR序号}~0x70010800c0001>>
        ${snmp获取值1}    获取SNMP数据_单个    sMRManufactureDate${SMR序号}value
        should be equal as strings    ${web1}    2020-11-19
        should be equal as strings    ${snmp获取值1}    2020-11-19
    END
    设置子工具值    SMR    all    资产管理信息    生产日期年5d6g    2021
    设置子工具值    SMR    all    资产管理信息    生产日期月6d    8
    设置子工具值    SMR    all    资产管理信息    生产日期日7g    8
    sleep    1m
    FOR    ${SMR序号}    IN    @{SMR随机list}
        ${web1}    获取web实时数据    <<生产日期-${SMR序号}~0x70010800c0001>>
        ${snmp获取值1}    获取SNMP数据_单个    sMRManufactureDate${SMR序号}value
        should be equal as strings    ${web1}    2021-08-08
        should be equal as strings    ${snmp获取值1}    2021-08-08
    END
    断开连接SNMP

snmp_0003_生产日期_new
    [Tags]    3
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    设置子工具值    SMR    all    资产管理信息    生产日期年5d6g    2020
    设置子工具值    SMR    all    资产管理信息    生产日期月6d    11
    设置子工具值    SMR    all    资产管理信息    生产日期日7g    19
    sleep    20
    @{SMR随机list}    从1-15个数中随机选n个不重复的单体    ${北向协议SMR最大数}    5    ${模拟整流器开始地址}
    FOR    ${SMR序号}    IN    @{SMR随机list}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<生产日期-${SMR序号}~0x70010800c0001>>    2020-11-19
        Wait Until Keyword Succeeds    5m    5    SNMP数据值为    sMRManufactureDate${SMR序号}value    2020-11-19    string
        # ${web1}    获取web实时数据    <<生产日期-${SMR序号}~0x70010800c0001>>
        # ${snmp获取值1}    获取SNMP数据_单个    sMRManufactureDate${SMR序号}value
        # should be equal as strings    ${web1}    2020-11-19
        # should be equal as strings    ${snmp获取值1}    2020-11-19
    END
    设置子工具值    SMR    all    资产管理信息    生产日期年5d6g    2021
    设置子工具值    SMR    all    资产管理信息    生产日期月6d    8
    设置子工具值    SMR    all    资产管理信息    生产日期日7g    8
    sleep    20
    FOR    ${SMR序号}    IN    @{SMR随机list}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<生产日期-${SMR序号}~0x70010800c0001>>    2021-08-08
        Wait Until Keyword Succeeds    5m    5    SNMP数据值为    sMRManufactureDate${SMR序号}value    2021-08-08    string
        # ${web1}    获取web实时数据    <<生产日期-${SMR序号}~0x70010800c0001>>
        # ${snmp获取值1}    获取SNMP数据_单个    sMRManufactureDate${SMR序号}value
        # should be equal as strings    ${web1}    2021-08-08
        # should be equal as strings    ${snmp获取值1}    2021-08-08
    END
    断开连接SNMP