*** Settings ***
Suite Setup       电池管理参数恢复默认值
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
历史告警最大条数测试
    连接CSU
    ${起始时间}    获取系统时间    \    \    \    ${EMPTY}
    #制造多个实时告警（电池温度无效），通过禁止和允许所有告警来增加历史告警记录
    #设置一组电池组
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
    设置web参数量    电池组容量_${i}    100    #设置所有电池组有效
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池温度无效    主要
    ${历史告警数量}    获取web历史告警数量    ${empty}    ${empty}
    #制造10000条历史告警记录
    ${级别设置值}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    <<禁止所有告警~0x1001030010001>>    严重
    FOR    ${k}    IN RANGE    20001
    Wait Until Keyword Succeeds    30    1    设置web控制量    <<禁止所有告警~0x1001040010001>>
    Wait Until Keyword Succeeds    30    1    查询指定告警信息    禁止所有告警
    ${历史告警数量}    获取web历史告警数量    ${empty}    ${empty}
    Wait Until Keyword Succeeds    30    1    设置web控制量    允许所有告警
    exit for loop if    ${历史告警数量}>=20000
    sleep    3
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
    设置web参数量    电池组容量_${i}    0    #设置所有电池组有效
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    100
    ${终止时间1}    获取系统时间
    ${历史告警内容}    获取web历史告警内容    ${起始时间}    ${终止时间2}    1    10
    should be equal    @{历史告警内容}[2]    电池温度无效    #在满10000条历史告警记录后的一条应是“禁止所有告警”

历史告警循环覆盖测试
    [Documentation]    前面一个用例将历史告警产生满。
    连接CSU
    ${起始时间}    获取系统时间
    ${历史告警数量}    获取web历史告警数量    ${empty}    ${empty}
    should be true    ${历史告警数量} == 20000
    #产生环境温度高告警
    ${级别设置值}    获取web参数量    环境温度高
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    环境温度高    次要
    ${告警级别}    获取web参数量    环境温度高
    FOR    ${零点}    IN RANGE    10    40    2
    设置通道配置    UIB_X7_T4    ${零点}    1    系统运行环境    环境温度
    sleep    3
    ${环境温度获取}    获取web实时数据    环境温度
    ${环境温度设置值}    获取web参数量    环境温度高阈值
    exit for loop if    ${环境温度获取}>${ 环境温度设置值}
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    环境温度高
    ${产生时间2}    获取系统时间
    #产生历史告警
    设置通道配置    UIB_X7_T4    0    1    系统运行环境    环境温度
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息不为    环境温度高
    sleep    2m
    ${历史告警数量2}    获取web历史告警数量    ${empty}    ${empty}
    should be true    ${历史告警数量2} == ${历史告警数量}
    @{历史告警内容1}    获取web历史告警内容    ${empty}    ${empty}    1    1
    @{历史告警内容1}    split string    @{历史告警内容1}[-1]    ,
    should \ contain    @{历史告警内容1}[5]    环境温度高
    should be equal    @{历史告警内容1}[3]    ${告警级别}
    ${告警结束时间差1}    subtract date from date    @{历史告警内容1}[1]    ${产生时间2}
    should be true    -10<${告警结束时间差}<10
    #产生电池均充历史告警
    ${级别设置值}    获取web参数量    电池均充
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池均充    次要
    Wait Until Keyword Succeeds    10    1    设置web控制量    启动均充
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息    电池均充
    ${终止时间1}    获取系统时间
    Wait Until Keyword Succeeds    10    1    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    电池均充
    sleep    2m
    ${历史告警数量1}    获取web历史告警数量    ${empty}    ${empty}
    should be true    ${历史告警数量} == ${历史告警数量1}
    @{历史告警内容}    获取web历史告警内容    ${empty}    ${empty}    1    1
    @{历史告警内容}    split string    @{历史告警内容}[-1]    ,
    should contain    @{历史告警内容}[5]    电池均充
    should be equal    @{历史告警内容}[3]    ${告警级别}
    ${告警结束时间差}    subtract date from date    @{历史告警内容}[2]    ${终止时间1}
    should be true    -10<${告警结束时间差}<10
    [Teardown]    设置通道配置    UIB_X7_T4    0    1    系统运行环境    环境温度

单个历史告警产生的新增序号测试
    [Setup]    测试用例前置条件
    电池管理初始化
    sleep    10
    ${历史告警数量}    获取web历史告警数量    ${empty}    ${empty}
    run keyword if    ${历史告警数量}>1900    删除历史记录    历史告警
    ${系统时间1}    获取系统时间
    ${起始时间}    subtract time from date    ${系统时间1}    30d    exclude_millis=yes    #30天以前的时间
    ${终止时间1}    获取系统时间
    ${历史告警数量1}    获取web历史告警数量    ${起始时间}    ${终止时间1}
    log    ${历史告警数量1}
    #产生电池均充历史告警
    Wait Until Keyword Succeeds    5m    2    设置web参数量    电池均充    严重
    Wait Until Keyword Succeeds    10    1    设置web控制量    启动均充
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息    电池均充
    Wait Until Keyword Succeeds    10    1    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    电池均充
    ${终止时间2}    获取系统时间
    ${历史告警数量2}    获取web历史告警数量    ${起始时间}    ${终止时间2}
    should be true    ${历史告警数量2}==${历史告警数量1}+1
    #产生环境温度高历史告警
    FOR    ${调节步长}    IN RANGE    5    40    2
    Comment    ${零点}    evaluate    str(${调节步长})
    设置通道配置    UIB_X7_T4    ${调节步长}    1    系统运行环境    环境温度
    sleep    3
    ${环境温度获取}    获取web实时数据    环境温度
    ${环境温度设置值}    获取web参数量    环境温度高阈值
    exit for loop if    ${环境温度获取}>${ 环境温度设置值}
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    环境温度高
    设置通道配置    UIB_X7_T4    0    1    系统运行环境    环境温度
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息不为    环境温度高
    ${终止时间3}    获取系统时间
    ${历史告警数量3}    获取web历史告警数量    ${起始时间}    ${终止时间3}
    should be true    ${历史告警数量3}==${历史告警数量2}+1
    #产生直流电压高历史告警
    ${直流电压高阈值}    获取web参数量    直流电压高阈值
    向上调节电池电压    ${直流电压高阈值}
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    直流电压高
    向下调节电池电压    53.5
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息不为    直流电压高
    ${终止时间4}    获取系统时间
    ${历史告警数量4}    获取web历史告警数量    ${起始时间}    ${终止时间4}
    should be true    ${历史告警数量4}==${历史告警数量3}+1
    [Teardown]    Run keywords    设置通道配置    UIB_X7_T4    0    1    系统运行环境    环境温度
    ...    AND    设置web参数量    电池均充    屏蔽
    ...    AND    重置电池模拟器输出

多个历史告警产生的新增序号测试
    [Setup]    测试用例前置条件
    同时设置三相电压频率    220    50
    打开交流源输出
    重置电池模拟器输出
    实时告警刷新完成
    sleep    5
    ${历史告警数量}    获取web历史告警数量    ${empty}    ${empty}
    run keyword if    ${历史告警数量}>1900    删除历史记录    历史告警
    ${系统时间1}    获取系统时间
    ${起始时间}    subtract time from date    ${系统时间1}    30d    exclude_millis=yes    #30天以前的时间
    ${终止时间1}    获取系统时间
    ${历史告警数量1}    获取web历史告警数量    ${起始时间}    ${终止时间1}
    log    ${历史告警数量1}
    #产生多个历史告警
    Wait Until Keyword Succeeds    10    2    设置web参数量    环境温度高    严重
    Wait Until Keyword Succeeds    10    2    设置web参数量    直流电压高    严重
    #产生环境温度高历史告警
    FOR    ${零点}    IN RANGE    10    40    2
    设置通道配置    UIB_X7_T4    ${零点}    1    系统运行环境    环境温度
    sleep    3
    ${环境温度获取}    获取web实时数据    环境温度
    ${环境温度设置值}    获取web参数量    环境温度高阈值
    exit for loop if    ${环境温度获取}>${ 环境温度设置值}
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    环境温度高
    设置通道配置    UIB_X7_T4    0    1    系统运行环境    环境温度
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息不为    环境温度高
    sleep    3
    #产生直流电压高历史告警
    ${直流电压高阈值}    获取web参数量    直流电压高阈值
    向上调节电池电压    ${直流电压高阈值}
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    直流电压高
    向下调节电池电压    53.5
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息不为    直流电压高
    sleep    3
    ${终止时间2}    获取系统时间
    ${历史告警数量2}    获取web历史告警数量    ${起始时间}    ${终止时间2}
    should be true    ${历史告警数量2}==${历史告警数量1}+2
    [Teardown]    Run keywords    设置通道配置    UIB_X7_T4    0    1    系统运行环境    环境温度
    ...    AND    设置web参数量    直流电压高    主要
    ...    AND    设置web参数量    环境温度高    次要
    ...    AND    重置电池模拟器输出

获取最新历史告警名称和级别测试（电池均充）
    [Setup]    测试用例前置条件
    电池管理初始化
    sleep    10
    ${历史告警数量}    获取web历史告警数量    ${empty}    ${empty}
    run keyword if    ${历史告警数量}>1900    删除历史记录    历史告警
    Wait Until Keyword Succeeds    2m    2    设置web参数量    均充使能    允许
    ${起始时间}    获取系统时间
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池均充    严重
    ${告警级别}    获取web参数量    电池均充
    Wait Until Keyword Succeeds    2m    2    设置web控制量    启动均充
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    电池均充
    ${终止时间1}    获取系统时间
    ${历史告警数量1}    获取web历史告警数量    ${起始时间}    ${终止时间1}
    log    ${历史告警数量1}
    should be true    ${历史告警数量1}==0
    #产生电池均充历史告警
    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息不为    电池均充
    sleep    3
    ${终止时间2}    获取系统时间
    ${历史告警数量2}    获取web历史告警数量    ${起始时间}    ${终止时间2}
    should be true    ${历史告警数量2}==1
    @{历史告警内容}    获取web历史告警内容    ${起始时间}    ${终止时间2}    1    10
    should contain    @{历史告警内容}[-1]    电池均充
    @{历史告警内容}    split string    @{历史告警内容}[-1]    ,
    should contain    @{历史告警内容}[5]    电池均充
    should be equal    @{历史告警内容}[3]    ${告警级别}
    [Teardown]    设置web参数量    电池均充    屏蔽

获取最新历史告警名称和级别测试（环境温度高）
    [Setup]    测试用例前置条件
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    sleep    5
    ${历史告警数量}    获取web历史告警数量    ${empty}    ${empty}
    run keyword if    ${历史告警数量}>1900    删除历史记录    历史告警
    Wait Until Keyword Succeeds    10    2    设置web参数量    环境温度高    严重
    ${告警级别}    获取web参数量    环境温度高
    ${起始时间}    获取系统时间
    #产生环境温度高历史告警
    FOR    ${零点}    IN RANGE    10    40    2
    设置通道配置    UIB_X7_T4    ${零点}    1    系统运行环境    环境温度
    sleep    3
    ${环境温度获取}    获取web实时数据    环境温度
    ${环境温度设置值}    获取web参数量    环境温度高阈值
    exit for loop if    ${环境温度获取}>${ 环境温度设置值}
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    环境温度高
    ${终止时间1}    获取系统时间
    ${历史告警数量1}    获取web历史告警数量    ${起始时间}    ${终止时间1}
    should be true    ${历史告警数量1}==0
    #产生历史告警
    设置通道配置    UIB_X7_T4    0    1    系统运行环境    环境温度
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息不为    环境温度高
    ${终止时间2}    获取系统时间
    ${历史告警数量2}    获取web历史告警数量    ${起始时间}    ${终止时间2}
    should be true    ${历史告警数量2}==1
    @{历史告警内容}    获取web历史告警内容    ${起始时间}    ${终止时间2}    1    10
    @{历史告警内容}    split string    @{历史告警内容}[-1]    ,
    should contain    @{历史告警内容}[5]    环境温度高
    should be equal    @{历史告警内容}[3]    ${告警级别}
    [Teardown]    设置通道配置    UIB_X7_T4    0    1    系统运行环境    环境温度

获取最新历史告警名称和级别测试（直流电压高）
    [Setup]    测试用例前置条件
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    sleep    5
    ${历史告警数量}    获取web历史告警数量    ${empty}    ${empty}
    run keyword if    ${历史告警数量}>1900    删除历史记录    历史告警
    Wait Until Keyword Succeeds    10    2    设置web参数量    直流电压高    严重
    ${告警级别}    获取web参数量    直流电压高
    ${起始时间}    获取系统时间
    #产生直流电压高告警
    ${直流电压高阈值}    获取web参数量    直流电压高阈值
    向上调节电池电压    ${直流电压高阈值}+0.5
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    直流电压高
    ${终止时间1}    获取系统时间
    ${历史告警数量1}    获取web历史告警数量    ${起始时间}    ${终止时间1}
    should be true    ${历史告警数量1}==0
    #产生直流电压高历史告警
    向下调节电池电压    53.5
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息不为    直流电压高
    sleep    3
    ${终止时间2}    获取系统时间
    ${历史告警数量2}    获取web历史告警数量    ${起始时间}    ${终止时间2}
    should be true    ${历史告警数量2}==1
    @{历史告警内容}    获取web历史告警内容    ${起始时间}    ${终止时间2}    1    10
    @{历史告警内容}    split string    @{历史告警内容}[-1]    ,
    should contain    @{历史告警内容}[5]    直流电压高
    should be equal    @{历史告警内容}[3]    ${告警级别}
    [Teardown]    重置电池模拟器输出

获取最新历史告警名称和级别测试（多个整流器模块告警）
    [Setup]    Run keywords    测试用例前置条件
    ...    AND    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    sleep    40    #王德安
    ${历史告警数量}    获取web历史告警数量    ${empty}    ${empty}
    run keyword if    ${历史告警数量}>1900    删除历史记录    历史告警
    Wait Until Keyword Succeeds    10    2    设置web参数量    多个整流器模块告警    严重
    ${告警级别}    获取web参数量    多个整流器模块告警
    ${工作整流器数量}    获取web实时数据    工作整流器数量
    log    ${工作整流器数量}
    ${状态}    获取web实时数据    电池管理状态
    ${起始时间}    获取系统时间    #王德安
    run keyword if    '${状态}' != '浮充'    设置web控制量    启动浮充
    ${均充电压可设置范围}    获取web参数可设置范围    均充电压
    设置web参数量    均充电压    @{均充电压可设置范围}[0]
    ${输出高停机电压可设置范围}    获取web参数可设置范围    整流器输出高停机电压
    设置web参数量    整流器输出高停机电压    @{输出高停机电压可设置范围}[0]
    #产生多个整流器告警
    ${整流器输出高电压}    获取web参数量    整流器输出高停机电压
    向上调节电池电压    ${整流器输出高电压}+0.5
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    整流器故障
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    多个整流器模块告警
    ${终止时间1}    获取系统时间
    ${历史告警数量1}    获取web历史告警数量    ${起始时间}    ${终止时间1}
    @{历史告警内容1}    获取web历史告警内容    ${起始时间}    ${终止时间1}    1    10
    should be true    ${历史告警数量1}==0
    #产生多个整流器模块历史告警
    设置web设备参数量为默认值    整流器输出高停机电压
    设置web设备参数量为默认值    均充电压
    向下调节电池电压    53.5
    Wait Until Keyword Succeeds    6m    2    查询指定告警信息不为    多个整流器模块告警
    Wait Until Keyword Succeeds    6m    2    查询指定告警信息不为    整流器故障
    ${终止时间2}    获取系统时间
    ${历史告警数量2}    获取web历史告警数量    ${起始时间}    ${终止时间2}
    should be true    ${历史告警数量2}==${工作整流器数量}*2+1    #王德安
    @{历史告警内容}    获取web历史告警内容    ${起始时间}    ${终止时间2}    1    10
    @{多个整流器历史告警}    Get Matches    ${历史告警内容}    *多个整流器模块告警
    @{多个整流器历史告警}    split string    @{多个整流器历史告警}[-1]    ,
    should contain    @{多个整流器历史告警}[5]    多个整流器模块告警
    should be equal    @{多个整流器历史告警}[3]    ${告警级别}
    [Teardown]    Run keywords    设置web设备参数量为默认值    整流器输出高停机电压
    ...    AND    设置web设备参数量为默认值    均充电压
    ...    AND    重置电池模拟器输出

系统断电历史告警保存测试
    [Setup]    测试用例前置条件
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    ${历史告警数量}    获取web历史告警数量    ${empty}    ${empty}
    run keyword if    ${历史告警数量}>1900    删除历史记录    历史告警
    ${系统时间1}    获取系统时间
    ${起始时间}    subtract time from date    ${系统时间1}    30d    exclude_millis=yes    #30天以前的时间
    ${终止时间1}    获取系统时间
    ${历史告警数量1}    获取web历史告警数量    ${起始时间}    ${终止时间1}
    log    ${历史告警数量1}
    ${实时告警数量}    获取web告警数量
    Comment    关闭电池模拟器输出
    Comment    关闭交流源输出
    测试台上下电操作    T1-1    OFF
    sleep    15
    Comment    打开交流源输出
    Comment    打开电池模拟器输出
    Comment    连接CSU
    测试台上下电操作    T1-1    ON
    实时告警刷新完成
    sleep    5
    ${终止时间2}    获取系统时间
    ${历史告警数量2}    获取web历史告警数量    ${起始时间}    ${终止时间2}
    should be true    ${历史告警数量2}==${历史告警数量1}+${实时告警数量}

告警属性变化对历史告警的影响
    [Setup]    测试用例前置条件
    连接CSU
    ${级别设置值}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    <<禁止所有告警~0x1001030010001>>    严重
    Wait Until Keyword Succeeds    30    1    设置web控制量    <<禁止所有告警~0x1001040010001>>
    Wait Until Keyword Succeeds    30    1    查询指定告警信息    禁止所有告警
    Wait Until Keyword Succeeds    30    1    设置web控制量    允许所有告警
    sleep    10
    ${历史告警内容}    获取web历史告警内容_指定名称的最新那条    CSU    禁止所有告警
    should be equal    @{历史告警内容}[3]    严重
    Wait Until Keyword Succeeds    30    1    设置web参数量    <<禁止所有告警~0x1001030010001>>    主要
    Wait Until Keyword Succeeds    30    1    设置web控制量    <<禁止所有告警~0x1001040010001>>
    Wait Until Keyword Succeeds    30    1    查询指定告警信息    禁止所有告警
    sleep    10
    ${历史告警内容1}    获取web历史告警内容_指定名称的最新那条    CSU    禁止所有告警
    should be equal    @{历史告警内容1}[3]    严重
    Wait Until Keyword Succeeds    30    1    设置web控制量    允许所有告警
    [Teardown]    设置web参数量    <<禁止所有告警~0x1001030010001>>    严重

系统时间更改对历史告警的影响
    [Setup]    测试用例前置条件
    连接CSU
    ${级别设置值}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    <<禁止所有告警~0x1001030010001>>    严重
    Wait Until Keyword Succeeds    30    1    设置web控制量    <<禁止所有告警~0x1001040010001>>
    Wait Until Keyword Succeeds    30    1    查询指定告警信息    禁止所有告警
    ${产生时间}    获取系统时间
    ${实时告警告警级别}    获取web告警属性    <<禁止所有告警~0x1001030010001>>    告警级别
    Wait Until Keyword Succeeds    30    1    设置web控制量    允许所有告警
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息不为    禁止所有告警
    sleep    10
    ${恢复时间}    获取系统时间
    @{历史告警内容}    获取web历史告警内容    ${empty}    ${empty}    1    1
    @{历史告警内容}    split string    @{历史告警内容}[-1]    ,
    should contain    @{历史告警内容}[5]    禁止所有告警
    should be equal    @{历史告警内容}[3]    ${级别设置值}
    ${告警产生时间差}    subtract date from date    @{历史告警内容}[1]    ${产生时间}
    should be true    -10<${告警产生时间差}<10
    ${告警结束时间差}    subtract date from date    @{历史告警内容}[2]    ${恢复时间}
    should be true    -20<${告警结束时间差}<2
    ${当前时间}    获取系统时间
    ${调整时间}    add time to date    ${当前时间}    20d    exclude_millis=yes
    sleep    5
    设置系统时间    ${调整时间}
    ${历史告警内容1}    获取web历史告警内容_指定名称的最新那条    CSU    禁止所有告警
    should be equal    @{历史告警内容}[1]    @{历史告警内容1}[1]
    should be equal    @{历史告警内容}[2]    @{历史告警内容1}[2]
    [Teardown]    同步系统时间
