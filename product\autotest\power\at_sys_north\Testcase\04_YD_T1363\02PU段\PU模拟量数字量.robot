*** Settings ***
Suite Setup       #run keywords | 控制子工具运行停止 | pu | 开启
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
# 1363批量获取PU模拟量
#     [Documentation]    21min
#     [Setup]
#     写入CSV文档    PU模拟量获取测试    信号名称    信号值    结果
#     连接CSU
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=太阳能模块
#     ${排除列表}    create list
#     P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除模拟量信号}    ${排除列表}    2    ${模拟PU起始地址}
#     ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取PU段模拟量~CID1=D0H>>    <<获取PU段模拟量~CID2=42H>>    None    ${g_ver_1363}
#     ${web数据}    通过1104_data获取web数据    ${协议数据}
#     ${1104待测}    1104_获取1104/1363指定设备指定信号量    ${协议数据}    ${web数据}    ${列表1}
#     # FOR    ${i}    IN    @{1104待测}
#     #     ${信号名称}    Get From Dictionary    ${i}    signal_name
#     #     ${1104协议名称}    Get From Dictionary    ${i}    1104_name
#     #     ${缺省值}    获取web参数上下限范围    ${信号名称}
#     #     Run Keyword And Continue On Failure    1104南向子设备模拟量获取值封装判断结果    1363    pu    ${信号名称}    模拟量    ${缺省值}[1]    PU模拟量获取测试    null    null    null    <<获取PU段模拟量~CID1=D0H>>    <<获取PU段模拟量~CID2=42H>>    ${1104协议名称}    None
#     #     Run Keyword And Continue On Failure    1104南向子设备模拟量获取值封装判断结果    1363    pu    ${信号名称}    模拟量    ${缺省值}[2]    PU模拟量获取测试    null    null    null    <<获取PU段模拟量~CID1=D0H>>    <<获取PU段模拟量~CID2=42H>>    ${1104协议名称}    None
#     #     Run Keyword IF    ${缺省值}[1]!=${缺省值}[0] and ${缺省值}[2]!=${缺省值}[0]    Run Keyword And Continue On Failure    1104南向子设备模拟量获取值封装判断结果    1363    pu    ${信号名称}    模拟量    ${缺省值}[0]    PU模拟量获取测试    null    null    null    <<获取PU段模拟量~CID1=D0H>>    <<获取PU段模拟量~CID2=42H>>    ${1104协议名称}
#     #     ...    None
#     # END


#     ${缺省值列表1}    获取缺省值列表    ${1104待测}    1    1363
#     Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    1363    pu    模拟量    ${缺省值列表1}    PU模拟量获取测试    null    <<获取整流段模拟量~CID1=D0H>>    <<获取整流段模拟量~CID2=42H>>    None
#     ${缺省值列表2}    获取缺省值列表    ${1104待测}    2    1363
#     Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    1363    pu    模拟量    ${缺省值列表2}    PU模拟量获取测试    null    <<获取整流段模拟量~CID1=D0H>>    <<获取整流段模拟量~CID2=42H>>    None
#     ${缺省值列表0}    获取缺省值列表    ${1104待测}    0    1363
#     # ${缺省值列表0}    1104检查缺省值列表    ${缺省值列表0}    ${缺省值列表1}    ${缺省值列表2} 
#     Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    1363    pu    模拟量    ${缺省值列表0}    PU模拟量获取测试    null    <<获取整流段数字量~CID1=D0H>>    <<获取整流段数字量~CID2=42H>>    None

1363批量获取PU模拟量
    [Documentation]    21min
    [Setup]
    写入CSV文档    PU模拟量获取测试    信号名称    信号值    结果
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=太阳能模块
    ${排除列表}    create list
    P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除模拟量信号}    ${排除列表}    2    ${模拟PU起始地址}
    ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取PU段模拟量~CID1=D0H>>    <<获取PU段模拟量~CID2=42H>>    None    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${1104待测}    1104_获取1104/1363指定设备指定信号量    ${协议数据}    ${web数据}    ${列表1}
    # ${缺省值列表1}    获取缺省值列表    ${1104待测}    1
    # Run Keyword And Continue On Failure    1104南向子设备模拟量列表获取值封装判断结果    1363    pu    模拟量    ${缺省值列表1}    PU模拟量获取测试    null    null    null    <<获取整流段数字量~CID1=D0H>>    <<获取整流段数字量~CID2=42H>>    None
    # ${缺省值列表2}    获取缺省值列表    ${1104待测}    2
    # Run Keyword And Continue On Failure    1104南向子设备模拟量列表获取值封装判断结果    1363    pu    模拟量    ${缺省值列表2}    PU模拟量获取测试    null    null    null    <<获取整流段数字量~CID1=D0H>>    <<获取整流段数字量~CID2=42H>>    None
    # ${缺省值列表0}    获取缺省值列表    ${1104待测}    0
    # ${缺省值列表0}    1104检查缺省值列表    ${缺省值列表0}    ${缺省值列表1}    ${缺省值列表2} 
    # Run Keyword And Continue On Failure    1104南向子设备模拟量列表获取值封装判断结果    1363    pu    模拟量    ${缺省值列表0}    PU模拟量获取测试    null    null    null    <<获取整流段数字量~CID1=D0H>>    <<获取整流段数字量~CID2=42H>>    None

    ${缺省值列表1}    获取缺省值列表    ${1104待测}    1    1363
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    1363    pu    模拟量    ${缺省值列表1}    PU模拟量获取测试    null    <<获取整流段数字量~CID1=D0H>>    <<获取整流段数字量~CID2=42H>>    None
    ${缺省值列表2}    获取缺省值列表    ${1104待测}    2    1363
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    1363    pu    模拟量    ${缺省值列表2}    PU模拟量获取测试    null    <<获取整流段数字量~CID1=D0H>>    <<获取整流段数字量~CID2=42H>>    None
    ${缺省值列表0}    获取缺省值列表    ${1104待测}    0    1363
    # ${缺省值列表0}    1104检查缺省值列表    ${缺省值列表0}    ${缺省值列表1}    ${缺省值列表2} 
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    1363    pu    模拟量    ${缺省值列表0}    PU模拟量获取测试    null    <<获取整流段数字量~CID1=D0H>>    <<获取整流段数字量~CID2=42H>>    None


# 1363批量获取PU数字量
#     [Documentation]    21min
#     写入CSV文档    PU数字量获取测试    信号名称    信号值    结果    备注
#     连接CSU
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=digital data    INDEX_DEVICE_CH=太阳能模块
#     ${排除列表}    create list
#     P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除数字量信号}    ${排除列表}    2    ${模拟PU起始地址}
#     ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取PU段数字量~CID1=D0H>>    <<获取PU段数字量~CID2=43H>>    None    ${g_ver_1363}
#     ${web数据}    通过1104_data获取web数据    ${协议数据}
#     ${1104待测}    1104_获取1104/1363指定设备指定信号量    ${协议数据}    ${web数据}    ${列表1}
#     FOR    ${i}    IN    @{1104待测}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${1104协议名称}    Get From Dictionary    ${i}    1104_name
#         ${缺省值}    获取web参数上下限范围    ${信号名称}
#         Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    1363    pu    ${信号名称}    数字量    ${缺省值}[1]    PU数字量获取测试    null    null    null    <<获取PU段数字量~CID1=D0H>>    <<获取PU段数字量~CID2=43H>>    ${1104协议名称}    None
#         Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    1363    pu    ${信号名称}    数字量    ${缺省值}[2]    PU数字量获取测试    null    null    null    <<获取PU段数字量~CID1=D0H>>    <<获取PU段数字量~CID2=43H>>    ${1104协议名称}    None
#         Run Keyword IF    ${缺省值}[1]!=${缺省值}[0] and ${缺省值}[2]!=${缺省值}[0]    Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    1363    pu    ${信号名称}    数字量    ${缺省值}[0]    PU数字量获取测试    null    null    null    <<获取PU段数字量~CID1=D0H>>    <<获取PU段数字量~CID2=43H>>    ${1104协议名称}
#         ...    None
#     END

# 1363批量获取PU数字量
#     [Documentation]    21min
#     写入CSV文档    PU数字量获取测试    信号名称    信号值    结果    备注
#     连接CSU
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=digital data    INDEX_DEVICE_CH=太阳能模块
#     ${排除列表}    create list
#     P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除数字量信号}    ${排除列表}    2    ${模拟PU起始地址}
#     ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取PU段数字量~CID1=D0H>>    <<获取PU段数字量~CID2=43H>>    None    ${g_ver_1363}
#     ${web数据}    通过1104_data获取web数据    ${协议数据}
#     ${1104待测}    1104_获取1104/1363指定设备指定信号量    ${协议数据}    ${web数据}    ${列表1}
#     FOR    ${i}    IN    @{1104待测}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${1104协议名称}    Get From Dictionary    ${i}    1104_name
#         ${缺省值}    获取web参数上下限范围    ${信号名称}
#         Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    1363    pu    ${信号名称}    数字量    ${缺省值}[1]    PU数字量获取测试    null    null    null    <<获取PU段数字量~CID1=D0H>>    <<获取PU段数字量~CID2=43H>>    ${1104协议名称}    None
#         Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    1363    pu    ${信号名称}    数字量    ${缺省值}[2]    PU数字量获取测试    null    null    null    <<获取PU段数字量~CID1=D0H>>    <<获取PU段数字量~CID2=43H>>    ${1104协议名称}    None
#         Run Keyword IF    ${缺省值}[1]!=${缺省值}[0] and ${缺省值}[2]!=${缺省值}[0]    Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    1363    pu    ${信号名称}    数字量    ${缺省值}[0]    PU数字量获取测试    null    null    null    <<获取PU段数字量~CID1=D0H>>    <<获取PU段数字量~CID2=43H>>    ${1104协议名称}
#         ...    None
#     END

1363批量获取PU数字量
    [Documentation]    21min
    写入CSV文档    PU数字量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=digital data    INDEX_DEVICE_CH=太阳能模块
    ${排除列表}    create list
    P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除数字量信号}    ${排除列表}    2    ${模拟PU起始地址}
    ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取PU段数字量~CID1=D0H>>    <<获取PU段数字量~CID2=43H>>    None    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${1104待测}    1104_获取1104/1363指定设备指定信号量    ${协议数据}    ${web数据}    ${列表1}
    # ${缺省值列表1}    获取缺省值列表    ${1104待测}    1
    # Run Keyword And Continue On Failure    1104南向子设备数字量列表获取值封装判断结果    1363    pu    数字量    ${缺省值列表1}    PU数字量获取测试    null    null    null    <<获取整流段数字量~CID1=41H>>    <<获取整流段数字量~CID2=43H>>    None
    # ${缺省值列表2}    获取缺省值列表    ${1104待测}    2
    # Run Keyword And Continue On Failure    1104南向子设备数字量列表获取值封装判断结果    1363    pu    数字量    ${缺省值列表2}    PU数字量获取测试    null    null    null    <<获取整流段数字量~CID1=41H>>    <<获取整流段数字量~CID2=43H>>    None
    # ${缺省值列表0}    获取缺省值列表    ${1104待测}    0
    # ${缺省值列表0}    1104检查缺省值列表    ${缺省值列表0}    ${缺省值列表1}    ${缺省值列表2} 
    # Run Keyword And Continue On Failure    1104南向子设备数字量列表获取值封装判断结果    1363    pu    数字量    ${缺省值列表0}    PU数字量获取测试    null    null    null    <<获取整流段数字量~CID1=41H>>    <<获取整流段数字量~CID2=43H>>    None

    ${缺省值列表1}    获取缺省值列表    ${1104待测}    1    1363
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    1363    pu    数字量    ${缺省值列表1}    PU数字量获取测试    null    <<获取PU段数字量~CID1=D0H>>    <<获取PU段数字量~CID2=43H>>    None
    ${缺省值列表2}    获取缺省值列表    ${1104待测}    2    1363
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    1363    pu    数字量    ${缺省值列表2}    PU数字量获取测试    ull    <<获取PU段数字量~CID1=D0H>>    <<获取PU段数字量~CID2=43H>>    None
    ${缺省值列表0}    获取缺省值列表    ${1104待测}    0    1363
    # ${缺省值列表0}    1104检查缺省值列表    ${缺省值列表0}    ${缺省值列表1}    ${缺省值列表2} 
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    1363    pu    数字量    ${缺省值列表0}    PU数字量获取测试    null    <<获取PU段数字量~CID1=D0H>>    <<获取PU段数字量~CID2=43H>>    None


# 1363批量获取PU数字量
#     [Documentation]    21min
#     写入CSV文档    PU数字量获取测试    信号名称    信号值    结果    备注
#     连接CSU
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=digital data    INDEX_DEVICE_CH=太阳能模块
#     ${排除列表}    create list
#     P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除数字量信号}    ${排除列表}    2    ${模拟PU起始地址}
#     ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取PU段数字量~CID1=D0H>>    <<获取PU段数字量~CID2=43H>>    None    ${g_ver_1363}
#     ${web数据}    通过1104_data获取web数据    ${协议数据}
#     ${1104待测}    1104_获取1104/1363指定设备指定信号量    ${协议数据}    ${web数据}    ${列表1}
#     FOR    ${i}    IN    @{1104待测}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${1104协议名称}    Get From Dictionary    ${i}    1104_name
#         ${缺省值}    获取web参数上下限范围    ${信号名称}
#         Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    1363    pu    ${信号名称}    数字量    ${缺省值}[1]    PU数字量获取测试    null    null    null    <<获取PU段数字量~CID1=D0H>>    <<获取PU段数字量~CID2=43H>>    ${1104协议名称}    None
#         Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    1363    pu    ${信号名称}    数字量    ${缺省值}[2]    PU数字量获取测试    null    null    null    <<获取PU段数字量~CID1=D0H>>    <<获取PU段数字量~CID2=43H>>    ${1104协议名称}    None
#         Run Keyword IF    ${缺省值}[1]!=${缺省值}[0] and ${缺省值}[2]!=${缺省值}[0]    Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    1363    pu    ${信号名称}    数字量    ${缺省值}[0]    PU数字量获取测试    null    null    null    <<获取PU段数字量~CID1=D0H>>    <<获取PU段数字量~CID2=43H>>    ${1104协议名称}
#         ...    None
#     END

# 1363批量获取PU特殊数字量
#     [Documentation]    ${协议类型} | ${子设备名称} | ${信号名称} | ${命令名称} | ${参数值} | ${模拟起始地址} | ${文档名称} | ${power_sm命令名称} | ${节点名} | ${节点序号} | ${1104/1363表单名称} | ${1104/1363命令名称} | ${1104/1363数据名称} | ${屏号}=None
#     写入CSV文档    PU特殊数字量获取测试    信号名称    信号值    结果    备注
#     连接CSU
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=太阳能模块
#     ${排除列表}    create list
#     P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除特殊数字量信号}    ${排除列表}    1    ${模拟PU起始地址}    1
#     ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取PU段数字量~CID1=D0H>>    <<获取PU段数字量~CID2=43H>>    None    ${g_ver_1363}
#     ${web数据}    通过1104_data获取web数据    ${协议数据}
#     ${1104待测}    1104_获取1104/1363指定设备指定信号量    ${协议数据}    ${web数据}    ${列表1}
#     FOR    ${i}    IN    @{1104待测}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${1104协议名称}    Get From Dictionary    ${i}    1104_name
#         Comment    ${缺省值}    获取web参数上下限范围    ${信号名称}
#         Run Keyword And Continue On Failure    告警量置位状态量封装判断结果    1363    pu    ${信号名称}    数字量    1    ${模拟PU起始地址}    PU特殊数字量获取测试    null    null    null    <<获取PU段数字量~CID1=D0H>>    <<获取PU段数字量~CID2=43H>>    ${1104协议名称}    None
#         Run Keyword And Continue On Failure    告警量置位状态量封装判断结果    1363    pu    ${信号名称}    数字量    0    ${模拟PU起始地址}    PU特殊数字量获取测试    null    null    null    <<获取PU段数字量~CID1=D0H>>    <<获取PU段数字量~CID2=43H>>    ${1104协议名称}    None
#         Comment    Run Keyword IF    ${缺省值}[1]!=${缺省值}[0] and ${缺省值}[2]!=${缺省值}[0]    Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    1363    pu    ${信号名称}    数字量    ${缺省值}[0]    PU特殊数字量获取测试    null    null    null    <<获取PU段数字量~CID1=D0H>>    <<获取PU段数字量~CID2=43H>>
#         ...    ${1104协议名称}    None
#         Comment    ${信号名称}    Get From Dictionary    ${i}    signal_name
#         Comment    Run Keyword And Continue On Failure    告警量置位状态量封装判断结果    power_sm    pu    ${信号名称}    数字量    1    ${模拟PU起始地址}    PU特殊数字量获取测试    获取太阳能模块数字量    null    null    null    null    null
#         ...    null
#         Comment    Run Keyword And Continue On Failure    告警量置位状态量封装判断结果    power_sm    pu    ${信号名称}    数字量    0    ${模拟PU起始地址}    PU特殊数字量获取测试    获取太阳能模块数字量    null    null    null    null    null
#         ...    null
#     END

1363批量获取PU特殊数字量
    [Documentation]    ${协议类型} | ${子设备名称} | ${信号名称} | ${命令名称} | ${参数值} | ${模拟起始地址} | ${文档名称} | ${power_sm命令名称} | ${节点名} | ${节点序号} | ${1104/1363表单名称} | ${1104/1363命令名称} | ${1104/1363数据名称} | ${屏号}=None
    写入CSV文档    PU特殊数字量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=太阳能模块
    ${排除列表}    create list
	@{信号名称列表}   create list
	@{1104协议名称列表}   create list
    P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除特殊数字量信号}    ${排除列表}    1    ${模拟PU起始地址}    1
    ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取PU段数字量~CID1=D0H>>    <<获取PU段数字量~CID2=43H>>    None    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${1104待测}    1104_获取1104/1363指定设备指定信号量    ${协议数据}    ${web数据}    ${列表1}
    FOR    ${i}    IN    @{1104待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${1104协议名称}    Get From Dictionary    ${i}    1104_name
        Set To Dictionary    ${dict1}     name     ${信号名称}
        Append To List       ${信号名称列表}    ${dict1}
        Set To Dictionary    ${dict2}     name     ${1104协议名称}
        Append To List       ${1104协议名称列表}    ${dict2}
        Comment    ${缺省值}    获取web参数上下限范围    ${信号名称}
    END
    Run Keyword And Continue On Failure    告警量置位状态量批量    1363    pu    ${信号名称列表}    数字量    1    ${模拟PU起始地址}
    ...    PU特殊数字量获取测试    null    null    null    null    <<获取PU段数字量~CID1=D0H>>    <<获取PU段数字量~CID2=43H>>    ${1104协议名称列表}    None
    Run Keyword And Continue On Failure    告警量置位状态量批量    1363    pu    ${信号名称列表}    数字量    0    ${模拟PU起始地址}
    ...    PU特殊数字量获取测试    null    null    null    null    <<获取PU段数字量~CID1=D0H>>    <<获取PU段数字量~CID2=43H>>    ${1104协议名称列表}    None

