*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
整流器休眠和唤醒测试
    [Tags]    PMSA-NTest
    连接CSU
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流节能模式    自由
    sleep    5
    ${历史事件数量}    获取web历史事件数量    ${empty}    ${empty}    所有
    run keyword if    ${历史事件数量}>1900    删除历史记录    操作记录
    ${历史事件数量1}    获取web历史事件数量    ${empty}    ${empty}    所有
    ${起始时间}    获取系统时间
    ${操作日志数量1}    获取web历史事件数量    ${empty}    ${empty}    操作日志
    Wait Until Keyword Succeeds    20X    2    设置web控制量    整流器唤醒-1
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    整流器休眠状态-1    否
    ${终止时间}    获取系统时间
    ${历史事件数量2}    获取web历史事件数量    ${empty}    ${empty}    所有
    #should be true    ${历史事件数量2}==${历史事件数量1}+1    #操作记录1次
    ${操作日志数量2}    获取web历史事件数量    ${empty}    ${empty}    操作日志
    #should be true    ${操作日志数量2}==${操作日志数量1}+1	    #操作记录1次
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    操作日志    1    10
    should contain match    ${历史事件内容1}    *整流器唤醒*
    Wait Until Keyword Succeeds    20X    2    设置web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    整流器休眠状态-1    是
    ${终止时间1}    获取系统时间
    ${历史事件数量3}    获取web历史事件数量    ${empty}    ${empty}    所有
    #should be true    ${历史事件数量3}==${历史事件数量2}+1    #操作记录1次
    ${操作日志数量3}    获取web历史事件数量    ${empty}    ${empty}    操作日志
    #should be true    ${操作日志数量3}==${操作日志数量2}+1    \    #操作记录1次
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间1}    操作日志    1    10
    should contain match    ${历史事件内容1}    *整流器休眠*
    ${起始时间1}    获取系统时间
    Wait Until Keyword Succeeds    20X    2    设置web控制量    整流器唤醒-1
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    整流器休眠状态-1    否
    ${终止时间3}    获取系统时间
    ${历史事件数量4}    获取web历史事件数量    ${empty}    ${empty}    所有
    #should be true    ${历史事件数量4}==${历史事件数量3}+1    #操作记录1次
    ${操作日志数量4}    获取web历史事件数量    ${empty}    ${empty}    操作日志
    #should be true    ${操作日志数量4}==${操作日志数量3}+1    \    #操作记录1次
    ${历史事件内容1}    获取web历史事件内容    ${起始时间1}    ${终止时间3}    操作日志    1    10
    should contain match    ${历史事件内容1}    *整流器唤醒*
    ${起始时间2}    获取系统时间
    Wait Until Keyword Succeeds    20X    2    设置web控制量    整流器唤醒-2
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    整流器休眠状态-2    否
    ${终止时间4}    获取系统时间
    ${历史事件数量5}    获取web历史事件数量    ${empty}    ${empty}    所有
    #should be true    ${历史事件数量5}==${历史事件数量4}+1    #操作记录1次
    ${操作日志数量5}    获取web历史事件数量    ${empty}    ${empty}    操作日志
    #should be true    ${操作日志数量5}==${操作日志数量4}+1    \    #操作记录1次
    ${历史事件内容1}    获取web历史事件内容    ${起始时间2}    ${终止时间4}    操作日志    1    10
    should contain match    ${历史事件内容1}    *整流器唤醒*
    Wait Until Keyword Succeeds    20X    2    设置web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    整流器休眠状态-2    是
    ${终止时间5}    获取系统时间
    ${历史事件数量6}    获取web历史事件数量    ${empty}    ${empty}    所有
    #should be true    ${历史事件数量6}==${历史事件数量5}+1    #操作记录1次
    ${操作日志数量6}    获取web历史事件数量    ${empty}    ${empty}    操作日志
    #should be true    ${操作日志数量6}==${操作日志数量5}+1    \    #操作记录1次
    ${历史事件内容1}    获取web历史事件内容    ${起始时间2}    ${终止时间5}    操作日志    1    10
    should contain match    ${历史事件内容1}    *整流器休眠*
    ${起始时间3}    获取系统时间
    Wait Until Keyword Succeeds    20X    2    设置web控制量    整流器唤醒-2
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    整流器休眠状态-2    否
    ${终止时间6}    获取系统时间
    ${历史事件数量7}    获取web历史事件数量    ${empty}    ${empty}    所有
    #should be true    ${历史事件数量7}==${历史事件数量6}+1    #操作记录1次
    ${操作日志数量7}    获取web历史事件数量    ${empty}    ${empty}    操作日志
    #should be true    ${操作日志数量7}==${操作日志数量6}+1    \    #操作记录1次
    ${历史事件内容1}    获取web历史事件内容    ${起始时间3}    ${终止时间6}    操作日志    1    10
    should contain match    ${历史事件内容1}    *整流器唤醒*
    ${起始时间4}    获取系统时间
    Wait Until Keyword Succeeds    20X    2    设置web控制量    整流器唤醒-3
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    整流器休眠状态-3    否
    ${终止时间7}    获取系统时间
    ${历史事件数量8}    获取web历史事件数量    ${empty}    ${empty}    所有
    #should be true    ${历史事件数量8}==${历史事件数量7}+1    #操作记录1次
    ${操作日志数量8}    获取web历史事件数量    ${empty}    ${empty}    操作日志
    #should be true    ${操作日志数量8}==${操作日志数量7}+1    \    #操作记录1次
    ${历史事件内容1}    获取web历史事件内容    ${起始时间4}    ${终止时间7}    操作日志    1    10
    should contain match    ${历史事件内容1}    *整流器唤醒*
    Wait Until Keyword Succeeds    20X    2    设置web控制量    整流器休眠-3
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    整流器休眠状态-3   是
    ${终止时间8}    获取系统时间
    ${历史事件数量9}    获取web历史事件数量    ${empty}    ${empty}    所有
    #should be true    ${历史事件数量9}==${历史事件数量8}+1    #操作记录1次
    ${操作日志数量9}    获取web历史事件数量    ${empty}    ${empty}    操作日志
    #should be true    ${操作日志数量9}==${操作日志数量8}+1    \    #操作记录1次
    ${历史事件内容1}    获取web历史事件内容    ${起始时间4}    ${终止时间8}    操作日志    1    10
    should contain match    ${历史事件内容1}    *整流器休眠*
    ${起始时间5}    获取系统时间
    Wait Until Keyword Succeeds    20X    2    设置web控制量    整流器唤醒-3
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    整流器休眠状态-3    否
    ${终止时间9}    获取系统时间
    ${历史事件数量10}    获取web历史事件数量    ${empty}    ${empty}    所有
    #should be true    ${历史事件数量10}==${历史事件数量9}+1    #操作记录1次
    ${操作日志数量10}    获取web历史事件数量    ${empty}    ${empty}    操作日志
    #should be true    ${操作日志数量10}==${操作日志数量9}+1    \    #操作记录1次
    ${历史事件内容1}    获取web历史事件内容    ${起始时间5}    ${终止时间9}    操作日志    1    10
    should contain match    ${历史事件内容1}    *整流器唤醒*

整流器风扇调速允许和禁止测试
    [Tags]    PMSA-NTest
    连接CSU
    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器风扇调速禁止-1
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    整流器风扇控制状态-1    全速
    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器风扇调速禁止-2
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    整流器风扇控制状态-2    全速
    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器风扇调速禁止-3
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    整流器风扇控制状态-3    全速
    Comment    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器风扇调速禁止-30
    Comment    Wait Until Keyword Succeeds    1m    2    信号量数据值为    整流器风扇控制状态-30    全速
    Comment    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器风扇调速禁止-40
    Comment    Wait Until Keyword Succeeds    1m    2    信号量数据值为    整流器风扇控制状态-40    全速
    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器风扇调速允许-1
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    整流器风扇控制状态-1    自动
    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器风扇调速允许-2
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    整流器风扇控制状态-2    自动
    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器风扇调速允许-3
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    整流器风扇控制状态-3    自动
    Comment    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器风扇调速允许-30
    Comment    Wait Until Keyword Succeeds    1m    2    信号量数据值为    整流器风扇控制状态-30    自动
    Comment    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器风扇调速允许-40
    Comment    Wait Until Keyword Succeeds    1m    2    信号量数据值为    整流器风扇控制状态-40    自动

整流器通讯中断告警清除测试
    [Tags]    PMSA-NTest
    连接CSU
    设置子工具个数    SMR    3
    wait until keyword succeeds    10m    2    查询指定告警信息    整流器通讯中断
    wait until keyword succeeds    30m    2    获取指定告警数量大于    整流器通讯中断    37
    Wait Until Keyword Succeeds    20X    2    设置web控制量    整流器通讯中断告警清除-4
    wait until keyword succeeds    30m    2    精准判断告警不存在    整流器通讯中断-4
    wait until keyword succeeds    30m    2    获取指定告警数量大于    整流器通讯中断    36
    Wait Until Keyword Succeeds    20X    2    设置web控制量    整流器通讯中断告警清除-10
    wait until keyword succeeds    30m    2    精准判断告警不存在    整流器通讯中断-10
    wait until keyword succeeds    30m    2    获取指定告警数量大于    整流器通讯中断    35
    Wait Until Keyword Succeeds    20X    2    设置web控制量    整流器通讯中断告警清除-20
    wait until keyword succeeds    30m    2    精准判断告警不存在    整流器通讯中断-20
    wait until keyword succeeds    30m    2    获取指定告警数量大于    整流器通讯中断    34
    Wait Until Keyword Succeeds    20X    2    设置web控制量    整流器通讯中断告警清除-30
    wait until keyword succeeds    30m    2    精准判断告警不存在    整流器通讯中断-30
    wait until keyword succeeds    30m    2    获取指定告警数量大于    整流器通讯中断    33
    Wait Until Keyword Succeeds    20X    2    设置web控制量    整流器通讯中断告警清除-40
    wait until keyword succeeds    30m    2    精准判断告警不存在    整流器通讯中断-40
    wait until keyword succeeds    30m    2    获取指定告警数量大于    整流器通讯中断    32
    [Teardown]    Run keywords    设置子工具个数    SMR    48
    ...    AND    sleep    3m

整流器设备统计测试
    [Tags]    PMSA-NTest
    连接CSU
    设置子工具个数    SMR    3
    ${级别设置值}    获取web参数量    整流器通讯中断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    整流器通讯中断    严重
    wait until keyword succeeds    10m    2    查询指定告警信息    整流器通讯中断
    wait until keyword succeeds    30m    2    获取指定告警数量大于    整流器通讯中断    44
    wait until keyword succeeds    1m    2    设置web控制量    SMR设备统计
    wait until keyword succeeds    23m    2    查询指定告警信息不为    整流器通讯中断
    [Teardown]    Run keywords    设置子工具个数    SMR    48
    ...    AND    sleep    3m
