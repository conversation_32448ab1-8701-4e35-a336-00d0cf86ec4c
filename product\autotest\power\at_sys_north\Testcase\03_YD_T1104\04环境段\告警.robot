*** Settings ***
Suite Setup       主动告警测试前置条件    ${CSU_role}
Suite Teardown    设置web设备参数量为默认值    CSU主动告警使能
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
1104_0008_V3.0水淹告警
    [Documentation]    将水淹（X9）测试前先短接
    ...    ==20190327-F：水淹状态与数据字典相反，数据字典要求为0为正常，1为异常；实际水淹告警时，获取的状态为0
    ...    xhf0627:水淹状态应从配置文件中读取，默认状态是0，短接后状态为1.
    ...
    ...    获取1104一条告警值并判断告警存在
    ...
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${理应值}= | ${屏号}=None | ${g_ver}=
    [Tags]    T1-1
    [Setup]    #测试用例前置条件
    实时告警刷新完成
    ${级别设置值}    获取web参数量    水淹告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    水淹告警    主要
    wait until keyword succeeds    1m    1    判断告警不存在    水淹告警
    设置web参数量    水淹告警    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    水淹告警
    ${告警}    判断告警存在_带返回值    水淹告警
    should not be true    ${告警}
    模拟数字量告警    水淹告警    ON
    sleep    10
    FOR    ${告警级别设置}    IN    主要    严重    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    水淹告警    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    水淹告警
        sleep    5
    #1363查询告警存在
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1104}    <<获取交流段告警量~CID1=80H>>    <<获取交流段告警量~CID2=44H>>    水浸传感器告警    04    None    ${g_ver_1104}
        should be true    ${1104告警结果}
    #1363查询告警级别与web端级别
        ${交流停电告警级别}    获取web告警属性    水淹告警    告警级别
        should be equal    '${告警级别设置}'    '${交流停电告警级别}'
        Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    1104    系统运行环境    水淹告警
    END
    设置web参数量    水淹告警    次要
    模拟数字量告警    水淹告警    OFF
    [Teardown]    run keywords    设置web参数量    水淹告警    次要
    ...    AND    模拟数字量告警    水淹告警    OFF

1104_0010_V3.0门磁告警
    [Tags]    T1-1
    [Setup]    # 测试用例前置条件
    实时告警刷新完成
    ${级别设置值}    获取web参数量    门磁告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    门磁告警    主要
    wait until keyword succeeds    1m    1    判断告警不存在    门磁告警
    设置web参数量    门磁告警    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    门磁告警
    ${告警}    判断告警存在_带返回值    门磁告警
    should not be true    ${告警}
    模拟数字量告警    门磁告警    ON
    sleep    10
    FOR    ${告警级别设置}    IN    主要    严重    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    门磁告警    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    门磁告警
        sleep    5
    #1363查询告警存在
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1104}    <<获取交流段告警量~CID1=80H>>    <<获取交流段告警量~CID2=44H>>    门窗门磁传感器告警    04    None    ${g_ver_1104}
        should be true    ${1104告警结果}
    #1363查询告警级别与web端级别
        ${交流停电告警级别}    获取web告警属性    门磁告警    告警级别
        should be equal    '${告警级别设置}'    '${交流停电告警级别}'
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    1104    系统运行环境    门磁告警
    END
    设置web参数量    门磁告警    次要
    [Teardown]    run keywords    设置web参数量    门磁告警    次要
    ...    AND    模拟数字量告警    门磁告警    OFF

1104_0012_V3.0烟雾告警
    [Tags]    T1-1
    [Setup]    # 测试用例前置条件
    实时告警刷新完成
    ${级别设置值}    获取web参数量    烟雾告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    烟雾告警    主要
    wait until keyword succeeds    1m    1    判断告警不存在    烟雾告警
    设置web参数量    烟雾告警    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    烟雾告警
    ${告警}    判断告警存在_带返回值    烟雾告警
    should not be true    ${告警}
    模拟数字量告警    烟雾告警    ON
    sleep    10
    FOR    ${告警级别设置}    IN    主要    严重    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    烟雾告警    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    烟雾告警
        sleep    5
    #1363查询告警存在
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1104}    <<获取交流段告警量~CID1=80H>>    <<获取交流段告警量~CID2=44H>>    烟雾传感器告警    04    None    ${g_ver_1104}
        should be true    ${1104告警结果}
    #1363查询告警级别与web端级别
        ${交流停电告警级别}    获取web告警属性    烟雾告警    告警级别
        should be equal    '${告警级别设置}'    '${交流停电告警级别}'
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    1104    系统运行环境    烟雾告警
    END
    设置web参数量    烟雾告警    次要
    [Teardown]    run keywords    设置web参数量    烟雾告警    次要
    ...    AND    模拟数字量告警    烟雾告警    OFF

1104_0014_环境温度低_new
    实时告警刷新完成
    ${可设置范围}    获取web参数可设置范围    环境温度低阈值
    设置web参数量    环境温度低阈值    ${可设置范围}[1]
    ${环境温度}    Wait Until Keyword Succeeds    5m    1    获取web实时数据    环境温度
    FOR    ${调节步长}    IN RANGE    -5    -50    -5
        ${零点}    evaluate    str(${调节步长})
        Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    ${零点}    1    系统运行环境    环境温度
        sleep    3
        ${环境温度获取}    获取web实时数据    环境温度
        ${环境温度设置值}    获取web参数量    环境温度低阈值
        exit for loop if    ${环境温度获取}<${ 环境温度设置值}
    END
    设置web参数量    环境温度低    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    环境温度低
    ${环境温度低告警}    判断告警存在_带返回值    环境温度低
    should not be true    ${环境温度低告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度低    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    环境温度低
        sleep    5
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1104}    <<获取环境段告警量~CID1=80H>>    <<获取环境段告警量~CID2=44H>>    温度传感器告警    1    None    ${g_ver_1104}
        should be true    ${1104告警结果}
        ${环境温度低告警级别}    获取web告警属性    环境温度低    告警级别
        should be equal    ${告警级别}    ${环境温度低告警级别}
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别}'=='主要'    主动告警上送判断2    1104    系统运行环境    环境温度低
    END
    设置web参数量    环境温度低    次要
    Comment    Log    ${可设置范围}[1]

1104_0016_V3.0环境温度过低_new
    [Documentation]    低阈值范围：-27~23，默认-5；过低阈值范围：-30~20，默认-8
    [Setup]    #测试用例前置条件
    实时告警刷新完成
    ${可设置范围}    获取web参数可设置范围    环境温度低阈值
    设置web参数量    环境温度低阈值    ${可设置范围}[1]
    ${可设置范围}    获取web参数可设置范围    环境温度过低阈值
    设置web参数量    环境温度过低阈值    ${可设置范围}[1]
    ${环境温度}    Wait Until Keyword Succeeds    5m    1    获取web实时数据    环境温度
    FOR    ${调节步长}    IN RANGE    -2    -50    -2
        ${零点}    evaluate    str(${调节步长})
        Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    ${零点}    1    系统运行环境    环境温度
        sleep    3
        ${环境温度获取}    获取web实时数据    环境温度
        ${环境温度设置值}    获取web参数量    环境温度过低阈值
        exit for loop if    ${环境温度获取}<${ 环境温度设置值}
    END
    设置web参数量    环境温度过低    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    环境温度过低
    ${环境温度过低告警}    判断告警存在_带返回值    环境温度过低
    should not be true    ${环境温度过低告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度过低    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    环境温度过低
        sleep    5
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1104}    <<获取环境段告警量~CID1=80H>>    <<获取环境段告警量~CID2=44H>>    温度传感器告警    1    None    ${g_ver_1104}
        should be true    ${1104告警结果}
        ${环境温度低告警级别}    获取web告警属性    环境温度过低    告警级别
        should be equal    ${告警级别}    ${环境温度低告警级别}
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别}'=='主要'    主动告警上送判断2    1104    系统运行环境    环境温度过低
    END
    设置web参数量    环境温度过低    次要
    [Teardown]    Run keywords    环境温度参数恢复默认值

1104_0018_V3.0环境温度高_new
    [Documentation]    高阈值范围：30-60，默认55；过高阈值范围：33-63，默认58。高比过高低3°
    [Setup]    #测试用例前置条件
    实时告警刷新完成
    Comment    Wait Until Keyword Succeeds    5m    1    设置web设备参数量    系统运行环境    温度传感器类型    值=1    #默认1
    Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度过高阈值    58    #默认58，min33
    Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度高阈值    30    #默认55，min30
    ${环境温度}    Wait Until Keyword Succeeds    5m    1    获取web实时数据    环境温度
    FOR    ${调节步长}    IN RANGE    2    40    2
        ${零点}    evaluate    str(${调节步长})
        Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    ${零点}    1    系统运行环境    环境温度
        Comment    Wait Until Keyword Succeeds    30    1    通道配置_AI    ${plat.envtemp}    ${零点}    1    系统运行环境    环境温度
        sleep    3
        ${环境温度获取}    获取web实时数据    环境温度
        ${环境温度设置值}    获取web参数量    环境温度高阈值
        exit for loop if    ${环境温度获取}>${ 环境温度设置值}
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度高    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    环境温度高
    ${环境温度高告警}    判断告警存在_带返回值    环境温度高
    should not be true    ${环境温度高告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度高    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    环境温度高
        sleep    5
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1104}    <<获取环境段告警量~CID1=80H>>    <<获取环境段告警量~CID2=44H>>    温度传感器告警    2    None    ${g_ver_1104}
        should be true    ${1104告警结果}
        ${环境温度高告警级别}    获取web告警属性    环境温度高    告警级别
        should be equal    ${告警级别}    ${环境温度高告警级别}
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别}'=='主要'    主动告警上送判断2    1104    系统运行环境    环境温度高
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度高    次要
    [Teardown]    Run keywords    环境温度参数恢复默认值

1104_0020_V3.0环境温度过高_new
    [Documentation]    高阈值范围：30-60，默认55；过高阈值范围：33-63，默认58。高比过高低3°
    ...
    ...    子表无此告警级别
    [Setup]    #测试用例前置条件
    实时告警刷新完成
    ${可设置范围}    获取web参数可设置范围    环境温度高阈值
    设置web参数量    环境温度高阈值    ${可设置范围}[0]
    ${可设置范围}    获取web参数可设置范围    环境温度过高阈值
    设置web参数量    环境温度过高阈值    ${可设置范围}[0]
    ${环境温度}    Wait Until Keyword Succeeds    10    1    获取web实时数据    环境温度
    FOR    ${调节步长}    IN RANGE    2    40    2
        ${零点}    evaluate    str(${调节步长})
        Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    ${零点}    1    系统运行环境    环境温度
        Comment    Wait Until Keyword Succeeds    30    1    通道配置_AI    ${plat.envtemp}    ${零点}    1    系统运行环境    环境温度
        sleep    3
        ${环境温度获取}    获取web实时数据    环境温度
        ${环境温度设置值}    获取web参数量    环境温度过高阈值
        exit for loop if    ${环境温度获取}>${ 环境温度设置值}
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度过高    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    环境温度过高
    ${环境温度高告警}    判断告警存在_带返回值    环境温度过高
    should not be true    ${环境温度高告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度过高    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    环境温度过高
        sleep    5
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1104}    <<获取环境段告警量~CID1=80H>>    <<获取环境段告警量~CID2=44H>>    温度传感器告警    2    None    ${g_ver_1104}
        should be true    ${1104告警结果}
        ${环境温度高告警级别}    获取web告警属性    环境温度过高    告警级别
        should be equal    ${告警级别}    ${环境温度高告警级别}
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别}'=='主要'    主动告警上送判断2    1104    系统运行环境    环境温度过高
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度过高    次要
    [Teardown]    Run keywords    环境温度参数恢复默认值

1104_0022_V3.0环境湿度无效
    [Setup]    #Run keywords | 测试用例前置条件 | AND | 设置web参数量 | 环境湿度无效 | 主要
    #不接入传感器
    设置通道无效值/恢复通道原始值    ${plat.humity}    1
    sleep    10
    实时告警刷新完成
    设置web参数量    环境湿度无效    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    环境湿度无效
    ${环境湿度无效告警}    判断告警存在_带返回值    环境湿度无效
    should not be true    ${环境湿度无效告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    环境湿度无效    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    环境湿度无效
        sleep    5
        Comment    #1363查询告警存在
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1104}    <<获取环境段告警量~CID1=80H>>    <<获取环境段告警量~CID2=44H>>    湿度传感器告警    E3    None    ${g_ver_1104}
        should be true    ${1104告警结果}
        Comment    #1363查询告警级别与web端级别
        ${环境湿度告警级别}    获取web告警属性    环境湿度无效    告警级别
        should be equal    ${告警级别}    ${环境湿度告警级别}
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别}'=='主要'    主动告警上送判断2    1104    系统运行环境    环境湿度无效
    END
    设置通道无效值/恢复通道原始值    UIB_X10_HUM    0
    
    [Teardown]    设置web参数量    环境湿度无效    屏蔽

1104_0024_V3.0门禁告警
    [Documentation]    此告警不支持自动化产生
    [Tags]    3
    [Setup]    测试用例前置条件
    实时告警刷新完成
    设置web参数量    门禁告警    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    门禁告警
    ${门磁告警}    判断告警存在_带返回值    门禁告警
    should not be true    ${门磁告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    门禁告警    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    门禁告警
        sleep    3
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1104}    <<获取环境段告警量~CID1=91H>>    <<获取环境段告警量~CID2=44H>>    门禁传感器告警    4    None    ${g_ver_1104}
        should be true    ${1104告警结果}
        ${门磁告警级别}    获取web告警属性    门禁告警    告警级别
        should be equal    '${告警级别}'    ${门磁告警级别}
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1104}    <<获取环境段告警量~CID1=91H>>    <<获取环境段告警量~CID2=81H>>    门禁告警    None    ${g_ver_1104}
        ${1104告警级别}    获取1104告警级别取值约定    门禁告警    ${1104告警级别}
        should be equal    ${告警级别}    ${1104告警级别}
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别}'=='主要'    主动告警上送判断2    1104    系统运行环境    门禁告警
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    门禁告警    次要
    [Teardown]

1104_0026_V3.0环境湿度低
    [Tags]    3
    [Setup]    测试用例前置条件
    #不接入传感器
    实时告警刷新完成
    设置web参数量    环境湿度低    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    环境湿度低
    ${环境湿度无效告警}    判断告警存在_带返回值    环境湿度低
    should not be true    ${环境湿度无效告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    环境湿度低    ${告警级别}
        ${web告警}    查询指定告警信息    环境湿度低
        sleep    5
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1104}    <<获取环境段告警量~CID1=91H>>    <<获取环境段告警量~CID2=44H>>    湿度传感器告警    1    None    ${g_ver_1104}
        should be equal    ${web告警}    ${1104告警结果}
        ${环境湿度告警级别}    获取web告警属性    环境湿度低    告警级别
        should be equal    ${告警级别}    ${环境湿度告警级别}
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1104}    <<获取环境段告警量~CID1=91H>>    <<获取环境段告警量~CID2=81H>>    环境湿度低    None    ${g_ver_1104}
        should be equal    ${告警级别}    ${1104告警级别}
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别}'=='主要'    主动告警上送判断2    1104    系统运行环境    环境湿度低
    END
    设置web参数量    环境湿度低    次要
    [Teardown]

1104_0028_V3.0环境湿度高
    [Tags]    3
    [Setup]    测试用例前置条件
    #不接入传感器
    实时告警刷新完成
    设置web参数量    环境湿度高    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    环境湿度高
    ${环境湿度无效告警}    判断告警存在_带返回值    环境湿度高
    should not be true    ${环境湿度无效告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    环境湿度高    ${告警级别}
        ${web告警}    查询指定告警信息    环境湿度高
        sleep    5
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1104}    <<获取环境段告警量~CID1=91H>>    <<获取环境段告警量~CID2=44H>>    湿度传感器告警    2    None    ${g_ver_1104}
        should be equal    ${web告警}    ${1104告警结果}
        ${环境湿度告警级别}    获取web告警属性    环境湿度高    告警级别
        should be equal    ${告警级别}    ${环境湿度告警级别}
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1104}    <<获取环境段告警量~CID1=91H>>    <<获取环境段告警量~CID2=81H>>    环境湿度高    None    ${g_ver_1104}
        should be equal    ${告警级别}    ${1104告警级别}
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别}'=='主要'    主动告警上送判断2    1104    系统运行环境    环境湿度高
    END
    设置web参数量    环境湿度高    次要
    [Teardown]

1104_0030_V3.0环境温度高
    [Documentation]    高阈值范围：30-60，默认55；过高阈值范围：33-63，默认58。高比过高低3°
    [Tags]    3
    [Setup]    测试用例前置条件
    实时告警刷新完成
    ${可设置范围}    获取web参数可设置范围    环境温度高阈值
    设置web参数量    环境温度高阈值    ${可设置范围}[0]
    Comment    ${可设置范围}    获取web参数可设置范围    环境温度过高阈值
    Comment    设置web参数量    环境温度过高阈值    ${可设置范围}[0]
    ${环境温度}    Wait Until Keyword Succeeds    10    1    获取web实时数据    环境温度
    Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度高    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    环境温度高
    ${环境温度高告警}    判断告警存在_带返回值    环境温度高
    should not be true    ${环境温度高告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度高    ${告警级别}
        ${web告警}    查询指定告警信息    环境温度高
        sleep    5
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1104}    <<获取环境段告警量~CID1=91H>>    <<获取环境段告警量~CID2=44H>>    温度传感器告警    2    None    ${g_ver_1104}
        should be true    ${1104告警结果}
        ${环境温度高告警级别}    获取web告警属性    环境温度高    告警级别
        should be equal    ${告警级别}    ${环境温度高告警级别}
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1104}    <<获取环境段告警量~CID1=91H>>    <<获取环境段告警量~CID2=81H>>    环境温度高    None    ${g_ver_1104}
        ${1104告警级别}    获取1104告警级别取值约定    环境温度高    ${1104告警级别}
        should be equal    ${告警级别}    ${1104告警级别}
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别}'=='主要'    主动告警上送判断2    1104    系统运行环境    环境温度高
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度高    次要
    [Teardown]    Run keywords    环境温度参数恢复默认值

1104_0032_V3.0环境温度过高
    [Documentation]    高阈值范围：30-60，默认55；过高阈值范围：33-63，默认58。高比过高低3°
    ...
    ...    子表无此告警级别
    [Tags]    3
    [Setup]    测试用例前置条件
    实时告警刷新完成
    ${可设置范围}    获取web参数可设置范围    环境温度高阈值
    设置web参数量    环境温度高阈值    ${可设置范围}[0]
    ${可设置范围}    获取web参数可设置范围    环境温度过高阈值
    设置web参数量    环境温度过高阈值    ${可设置范围}[0]
    ${环境温度}    Wait Until Keyword Succeeds    10    1    获取web实时数据    环境温度
    Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度过高    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    环境温度过高
    ${环境温度高告警}    判断告警存在_带返回值    环境温度过高
    should not be true    ${环境温度高告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度过高    ${告警级别}
        sleep    5
        打印web实时告警信息名称
        ${web告警}    查询指定告警信息    环境温度过高
        sleep    5
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1104}    <<获取环境段告警量~CID1=91H>>    <<获取环境段告警量~CID2=44H>>    温度传感器告警    2    None    ${g_ver_1104}
        should be equal    ${web告警}    ${1104告警结果}
        ${环境温度高告警级别}    获取web告警属性    环境温度过高    告警级别
        should be equal    ${告警级别}    ${环境温度高告警级别}
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别}'=='主要'    主动告警上送判断2    1104    系统运行环境    环境温度过高
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度过高    次要
    [Teardown]    Run keywords    环境温度参数恢复默认值
