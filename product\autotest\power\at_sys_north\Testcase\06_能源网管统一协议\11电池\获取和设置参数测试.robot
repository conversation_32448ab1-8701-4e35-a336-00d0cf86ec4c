*** Settings ***
Suite Setup       Run Keywords    设置web参数量    电池组容量_2    81
...               AND    设置web参数量    电池组容量_3    81
...               AND    设置web参数量    电池组容量_4    81
Suite Teardown    Run Keywords    设置web参数量    电池组容量_2    0
...               AND    设置web参数量    电池组容量_3    0
...               AND    设置web参数量    电池组容量_4    0
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_获取电池模拟量
    [Tags]    pass
    sleep    10s
    ${能源网管数据}    能源网管协议_获取数据    获取电池模拟量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取电池模拟量
    Should Be True    ${数据对比结果}
    ${校验结果}    能源网管协议_校验数据上送完整性    ${能源网管数据}    电池    analog data
    Comment   Should Be True    ${校验结果}

power_sm_0002_获取电池数字量
    [Tags]    pass
    ${能源网管数据}    能源网管协议_获取数据    获取电池数字量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取电池数字量
    Should Be True    ${数据对比结果}
    ${校验结果}    能源网管协议_校验数据上送完整性    ${能源网管数据}    电池    digital data
    Comment    Should Be True    ${校验结果}

power_sm_0003_获取电池参数量
    [Tags]    pass
    ${能源网管数据}    能源网管协议_获取数据    获取电池参数量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取电池参数量
    Should Be True    ${数据对比结果}
    ${校验结果}    能源网管协议_校验数据上送完整性    ${能源网管数据}    电池    parameter
    Comment    Should Be True    ${校验结果}

power_sm_0004_设置电池参数量
    [Tags]    4
    Comment    ${sheet_name}    Set Variable    设置环境参数
    ${能源网管数据}    能源网管协议_设置单个参数    设置电池参数    ${BOARD_IP}    ${SSH}
    ${对比结果}    能源网管协议_批量对比参数设置    ${能源网管数据}    设置电池参数
    Should Be True    ${对比结果}

power_sm_0005_获取电池统计量
    [Tags]    4
    ${能源网管数据}    能源网管协议_获取数据    获取电池统计量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取电池统计量
    Should Be True    ${数据对比结果}
    ${校验结果}    能源网管协议_校验数据上送完整性    ${能源网管数据}    电池    stastic data
    Comment    Should Be True    ${校验结果}
