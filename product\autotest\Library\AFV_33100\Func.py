#-*-coding:utf-8 -*-
#####################################################
#自己定制的函数-判断字符串(无空格)是否是合法的十六进制字符串
#返回值：0-正常 1-空字符串 2-字符个数不是偶数 3-存在非法字符
#####################################################
def isHexString(string):
    #字符串转换为列表
    tempList = list(string)
    #删除字符串中的空格(主要是去除杂乱的空格)
    while (tempList.count(' ')): 
        tempList.remove(' ')
    #检查字符串有效十六进制数据的个数
    if len(tempList) == 0:
        return 1
    #检查字符串中十六进制字符的个数是否是偶数
    if len(tempList) % 2 != 0:             
        return 2
    #检查是否存在非法字符 
    for c in tempList:
        if ord(c) < ord('0'):
            return 3
        elif ord('9') < ord(c) < ord('A'):
            return 3
        elif ord('F') < ord(c) < ord('a'):
            return 3
        elif ord(c) > ord('f'):
            return 3
    return 0
#####################################################
def getHex_XX(intSrc):
    s = hex(intSrc).upper()
    sLen = len(s)
    if sLen == 3:
        return '0'+s[2]
    elif sLen == 4:
        return s[2:]
    else:
        return 'Er'
#==========================
def getHex_sXX(intSrc):
    s = hex(intSrc).upper()
    sLen = len(s)
    if sLen == 3:
        return ' 0'+s[2]
    elif sLen == 4:
        return ' '+s[2:]
    else:
        return ' Er'
#==========================
def getHex_XXXX(intSrc):
    s = hex(intSrc).upper()
    sLen = len(s)
    if sLen == 3:
        return '000'+s[2]
    elif sLen == 4:
        return '00'+s[2:]
    elif sLen == 5:
        return '0'+s[2:]
    elif sLen == 6:
        return s[2:]
    else:
        return 'Err.'
#==========================
def getHex_sXXsXX(intSrc1):
    intSrc = int(intSrc1)
    s = hex(intSrc).upper()
    sLen = len(s)
    if sLen == 3:
        return ' 00 0'+s[2]
    elif sLen == 4:
        return ' 00 '+s[2:]
    elif sLen == 5:
        return (' 0'+s[2]+' '+s[3:])
    elif sLen == 6:
        return (' ' + s[2:4] + ' ' + s[4:])
    else:
        return ' Err..'
#==========================
def getHex_sXXsXXsXXsXX(intSrc1):#高位在前低位在后
    intSrc = int(intSrc1)
    s = hex(intSrc).upper()
    sLen = len(s)
    if sLen == 3:
        return ' 00 00 00 0'+ s[2]
    elif sLen == 4:
        return ' 00 00 00 '+ s[2:]
    elif sLen == 5:
        return (' 00 00 0'+ s[2]+' '+s[3:])
    elif sLen == 6:
        return (' 00 00 ' + s[2:4] + ' ' + s[4:])
    elif sLen == 7:
        return (' 00 0' + s[2] + ' ' + s[3:5] + ' ' + s[5:])
    elif sLen == 8:
        return (' 00 ' + s[2:4] + ' ' + s[4:6] + ' ' + s[6:])
    elif sLen == 9:
        return (' 0' + s[2] + ' ' + s[3:5] + ' ' + s[5:7] + ' ' + s[7:])
    elif sLen == 10:
        return (' ' + s[2:4] + ' ' + s[4:6] + ' ' + s[6:8] + ' ' + s[8:])
    else:
        return ' Err........'
#==========================
def getHex_hXXhXXhXXhXX(intSrc1):#低位在前高位在后
    intSrc = int(intSrc1)
    s = hex(intSrc).upper()
    sLen = len(s)
    if sLen == 3:
        return (' 00 0'+s[2]+' 00 00')
    elif sLen == 4:
        return (' 00 '+ s[2:]+' 00 00')
    elif sLen == 5:
        return(' 0'+s[2]+' '+s[3:]+' 00 00')
    elif sLen == 6:
        return (' ' + s[2:4]+' '+s[4:]+' 00 00')
    elif sLen == 7:
        return (' '+s[3:5] + ' ' + s[5:]+' 00 0' + s[2])
    elif sLen == 8:
        return (' '+s[4:6] + ' ' + s[6:]+' 00 ' + s[2:4])
    elif sLen == 9:
        return (' '+s[5:7] + ' ' + s[7:]+' 0' + s[2] + ' ' + s[3:5])
    elif sLen == 10:
        return ( ' ' + s[6:8] + ' ' + s[8:]+' ' + s[2:4] + ' ' + s[4:6])
    else:
        return ' Err........'
#==========================
def getHex_0xXX(intSrc):
    s = hex(intSrc).upper()
    sLen = len(s)
    if sLen == 3:
        return '0x0'+s[2]
    elif sLen == 4:
        return '0x'+s[2:]
    else:
        return 'Err...'
#==========================
def getHex_0xXXXX(intSrc):
    s = hex(intSrc).upper()
    sLen = len(s)
    if sLen == 3:
        return '0x000'+s[2]
    elif sLen == 4:
        return '0x00'+s[2:]
    elif sLen == 5:
        return '0x0'+s[2:]
    elif sLen == 6:
        return '0x'+s[2:]
    else:
        return 'Err...'
#==========================
def getStrLen(src):
    if src == '':
        return 0
    wRtn = 0
    for i in range(len(src)):
        if ord(src[i]) > 255:
            wRtn += 2
        else:
            wRtn += 1
    return wRtn
#=================================================================================
# 将整数转换为取值约定对应的内容,wData-整型数值,sComment-所有的取值约定内容
def getComment(wData,  sComment):
    if sComment == '':
        return str(wData)
    #split
    strAllComment = sComment.replace(u'；', ';')        
    if strAllComment.find(';') >= 0: #存在';'
        listAllComment = strAllComment.split(';')            
    else:
        listAllComment = [strAllComment] #列表只有一条 元素
    #match        
    for sComment in listAllComment: #遍历每一个 元素
        s = sComment.replace(u'：', ':')           
        if s.find(u':') > 0:
            listComment = s.split(':')               
            if listComment[0] == 'OTHER':
                return str(wData)+listComment[1]
            #考虑到取值约定中有16进制数据
            if listComment[0][:2] == '0x' or listComment[0][:2] == '0X' :
                intValue = int(listComment[0][2:], 16)
            elif listComment[0][-1] == 'H' or listComment[0][-1] == 'h' :
                intValue = int(listComment[0][:-1], 16)
            else:
                intValue = int(listComment[0])
            #
            if intValue == wData:
                return listComment[1]
        else:
            return sComment+' 取值约定字符串中无冒号'
    return str(wData) #没有对应的取值约定,返回原数值
########################################################3
def getCRC16(s): 
    b = 0xA001 
    a = 0xFFFF
    #字符串转序列
    listS = s.split(' ')
    #print('listS=',listS)
    x = []
    for t in listS:
        x.append(int(t,16))
    #print('x=',x)
    #开始计算CRC
    for byte in x:
        #print('getCRC16',byte)
        a = a^byte 
        for i in range(8): 
            last = a%2 
            a = a>>1 
            if last ==1:
                a = a^b 
    aa = ('0'*(6-len(hex(a)))+hex(a)[2:]).upper()
    #ll,hh = int(aa[:2],16),int(aa[2:],16) 
    #return [hh,ll]
    return ' ' + aa[2:] + ' ' + aa[:2]
#####################################################
if __name__ == "__main__":
    data=5
    print(getHex_hXXhXXhXXhXX(data))
    print(getHex_sXXsXXsXXsXX(data))
##    print(getStrLen('1234') == 4)
##    print(getStrLen('啊') == 2)
##    print(getStrLen('，') == 2)
##    print(getComment(1,'0:正常;1:异常') == '异常')
##    print(getComment(0x01,'0:正常;1:异常') == '异常')
##    print(getComment(1,'0:正常;0x01:异常') == '异常')
##    print(getComment(1,'0:正常;1H:异常') == '异常')
##    print(getComment(1,'0:正常;01H:异常') == '异常')
##    print(getComment(2,'0:正常;1:异常;OTHER:超出范围') == '2超出范围')
