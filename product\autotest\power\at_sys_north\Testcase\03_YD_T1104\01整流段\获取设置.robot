*** Settings ***
Resource          ../../../测试用例关键字.robot


*** Test Cases ***
1104_0004_V_3.0_1363_整流段_模拟量
    [Tags]    3
    ${协议数据}    1104获取数据    ${g_prtcl_1104}    <<获取整流段模拟量~CID1=41H>>    <<获取整流段模拟量~CID2=42H>>    None    ${g_ver_1104}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${对比结果}    批量对比数据_1104_WEB    ${协议数据}    ${web数据}    1104_整流段_模拟量
    should be true    ${对比结果}

1104_0006_V_3.0_1363_整流段_遥控
    [Tags]    T1-1
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-1    否    #为保证整流器数据获取正常
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流节能模式    自由
    sleep    5
    ${设置控制量1}    设置1104整流遥控数据    ${g_prtcl_1104}    整流模块均充    255    ${g_ver_1104}
    ${设置控制量2}    设置1104整流遥控数据    ${g_prtcl_1104}    整流模块浮充    255    ${g_ver_1104}
    ${设置控制量3}    设置1104整流遥控数据    ${g_prtcl_1104}    整流模块测试    255    ${g_ver_1104}
    ${设置控制量4}    设置1104整流遥控数据    ${g_prtcl_1104}    整流模块唤醒    1    ${g_ver_1104}
    ${设置控制量5}    设置1104整流遥控数据    ${g_prtcl_1104}    整流模块休眠    1    ${g_ver_1104}
    should be true    ${设置控制量1}
    should be true    ${设置控制量2}
    should be true    ${设置控制量3}
    should be true    ${设置控制量4}
    should be true    ${设置控制量5}
    [Teardown]    run keywords    连接CSU
    ...    AND    Wait Until Keyword Succeeds    30    1    设置web参数量    交流节能模式    安全

1104_0008_V_3.0_1363_整流段_数字量
    [Documentation]    整流器在位状态0:不在位/Not Exist;1:在位/Is Exist
    [Tags]    3
    ${协议数据}    1104获取数据    ${g_prtcl_1104}    <<获取整流段模拟量~CID1=41H>>    <<获取整流段模拟量~CID2=43H>>    None    ${g_ver_1104}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${对比结果}    批量对比数据_1104_WEB    ${协议数据}    ${web数据}    1104_整流段_数字量
    should be true    ${对比结果}

1104_0010_V_3.0_1363_整流段_休眠唤醒
    Comment    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-1    否    #为保证整流器数据获取正常
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流节能模式    自由
    sleep    5
    @{整流器序号随机list}    从1-15个数中随机选n个不重复的单体    ${模拟SMR个数}    4
    FOR    ${i}    IN    @{整流器序号随机list}
        ${整流器地址}    evaluate    str(${i})
        Wait Until Keyword Succeeds    10X    2    设置1104整流遥控数据    ${g_prtcl_1104}    整流模块休眠    ${整流器地址}    ${g_ver_1104}
        Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-${i}    是
        ${整流器1休眠状态}    获取web实时数据    整流器休眠状态-${i}
        should be equal    '${整流器1休眠状态}'    '是'
        Wait Until Keyword Succeeds    10X    2    设置1104整流遥控数据    ${g_prtcl_1104}    整流模块唤醒    ${整流器地址}    ${g_ver_1104}
        Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-${i}    否
        ${整流器1休眠状态}    获取web实时数据    整流器休眠状态-${i}
        should be equal    '${整流器1休眠状态}'    '否'
    END
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流节能模式    安全
    [Teardown]    run keywords    连接CSU
    ...    AND    Wait Until Keyword Succeeds    30    1    设置web参数量    交流节能模式    安全
