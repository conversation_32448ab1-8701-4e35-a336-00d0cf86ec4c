*** Settings ***
Suite Setup
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
# 1104批量获取FBBMS告警量
#     [Setup]    主动告警测试前置条件    ${CSU_role}
#     写入CSV文档    1104锂电池数字量和告警量获取测试    信号名称    信号值    结果    备注
#     连接CSU
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=FBBMS
#     ${排除列表}    create list
#     P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除告警量信号}    ${排除列表}    2    1    0    2
#     ${协议数据}    1104获取数据    ${g_prtcl_1104}    <<获取锂电池段告警量~CID1=4AH>>    <<获取锂电池段告警量~CID2=44H>>    1    ${g_ver_1104}
#     ${web数据}    通过1104_data获取web数据    ${协议数据}    True    1
#     ${1104待测}    1104_获取1104/1363指定设备指定信号量    ${协议数据}    ${web数据}    ${列表1}    1
#     Comment    FOR    ${i}    IN    @{power_sm待测}
#     FOR    ${i}    IN    @{1104待测}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${1104协议名称}    Get From Dictionary    ${i}    1104_name
#         ${实时告警中的设备名称}    主动告警_组合实时告警中的设备名称    FBBMS    ${1104协议名称}
#         Run Keyword And Continue On Failure    南向RS485子设备告警量产生封装判断结果    1104    smartli    ${信号名称}    只读    ${告警产生}    FB100B3数字量和告警量获取测试    ${实时告警中的设备名称}    null    null    <<获取锂电池段告警量~CID1=4AH>>    <<获取锂电池段告警量~CID2=44H>>    ${1104协议名称}    1
#         Run Keyword And Continue On Failure    南向RS485子设备告警量恢复封装判断结果    1104    smartli    ${信号名称}    只读    ${告警恢复}    FB100B3数字量和告警量获取测试    null    null    null    <<获取锂电池段告警量~CID1=4AH>>    <<获取锂电池段告警量~CID2=44H>>    ${1104协议名称}    1
#     END
#     [Teardown]    设置web设备参数量为默认值    CSU主动告警使能

1104批量获取FBBMS告警量
    [Setup]    主动告警测试前置条件    ${CSU_role}
    写入CSV文档    1104锂电池数字量和告警量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=FBBMS
    ${排除列表}    create list
    ${信号量列表}    create list
    P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除告警量信号}    ${排除列表}    2    1    0    2
    ${协议数据}    1104获取数据    ${g_prtcl_1104}    <<获取锂电池段告警量~CID1=4AH>>    <<获取锂电池段告警量~CID2=44H>>    1    ${g_ver_1104}
    ${web数据}    通过1104_data获取web数据    ${协议数据}    True    1
    ${1104待测}    1104_获取1104/1363指定设备指定信号量    ${协议数据}    ${web数据}    ${列表1}    1
    FOR    ${i}    IN    @{1104待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${1104协议名称}    Get From Dictionary    ${i}    1104_name
        ${实时告警中的设备名称}    主动告警_组合实时告警中的设备名称    FBBMS    ${1104协议名称}
    #     Run Keyword And Continue On Failure    南向RS485子设备告警量产生封装判断结果    1104    smartli    ${信号名称}    只读    ${告警产生}    FB100B3数字量和告警量获取测试    ${实时告警中的设备名称}    null    null    <<获取锂电池段告警量~CID1=4AH>>    <<获取锂电池段告警量~CID2=44H>>    ${1104协议名称}    1
    #     Run Keyword And Continue On Failure    南向RS485子设备告警量恢复封装判断结果    1104    smartli    ${信号名称}    只读    ${告警恢复}    FB100B3数字量和告警量获取测试    null    null    null    <<获取锂电池段告警量~CID1=4AH>>    <<获取锂电池段告警量~CID2=44H>>    ${1104协议名称}    1
    # END
	    ${dict1}          Create Dictionary 
        Set To Dictionary    ${dict1}     signal_name     ${信号名称}  
        Set To Dictionary    ${dict1}     alarm_name    ${1104协议名称} 
        Set To Dictionary    ${dict1}     device_name    ${实时告警中的设备名称} 
	    Append To List        ${信号量列表}    ${dict1}
    END
    Run Keyword And Continue On Failure    南向RS485子设备告警量列表产生封装判断结果    1104    smartli    ${信号量列表}    只读    ${告警产生}    
    ...    FB100B3数字量和告警量获取测试    电池    FBBMS    null    null    
    ...    <<获取锂电池段告警量~CID1=4AH>>    <<获取锂电池段告警量~CID2=44H>>    null    1
    Run Keyword And Continue On Failure    南向RS485子设备告警量列表恢复封装判断结果    1104    smartli    ${信号量列表}    只读    ${告警恢复}    
    ...    FB100B3数字量和告警量获取测试    电池    FBBMS    null    null    
    ...    <<获取锂电池段告警量~CID1=4AH>>    <<获取锂电池段告警量~CID2=44H>>    null    1
 
    [Teardown]    设置web设备参数量为默认值    CSU主动告警使能
    