*** Settings ***
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
手动设置测试
    [Documentation]    0:浮充/Float;1:均充/Equal;2:测试/Test;3:放电/Discharge;4:检测/Detect;5:过渡/Transition;6:充电/Charge
    
    电池管理初始化
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动测试
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    测试
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    浮充
    [Teardown]

电池容量无效时设置测试
    [Documentation]    0:浮充/Float;1:均充/Equal;2:测试/Test;3:放电/Discharge;4:检测/Detect;5:过渡/Transition;6:充电/Charge
    
    电池管理初始化
    Wait Until Keyword Succeeds    5m    1s    设置web参数量    电池组容量_1    0
    sleep    3
    Wait Until Keyword Succeeds    5m    1    设置失败的web控制量    启动测试
    [Teardown]    设置web参数量    电池组容量_1    100

交流停电时设置测试
    [Documentation]    0:浮充/Float;1:均充/Equal;2:测试/Test;3:放电/Discharge;4:检测/Detect;5:过渡/Transition;6:充电/Charge
    
    电池管理初始化
    关闭交流源输出
    设置负载电压电流    53.5    10
    打开负载输出
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    系统停电
    Wait Until Keyword Succeeds    5m    1    设置失败的Web控制量    启动测试
    [Teardown]    Run keywords    关闭负载输出
    ...    AND    打开交流源输出

无正常工作整流器时设置测试
    [Documentation]    0:浮充/Float;1:均充/Equal;2:测试/Test;3:放电/Discharge;4:检测/Detect;5:过渡/Transition;6:充电/Charge
    ...    交流节能模式：0:安全/Safe;1:节能/Save;2:自由/Free；
    
    电池管理初始化
    Wait Until Keyword Succeeds    3m    2    信号量数据值为    交流供电状态    市电1
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    过渡
    ${均充电压可设置范围}    获取web参数可设置范围    均充电压
    设置web参数量    均充电压    ${均充电压可设置范围}[0]
    ${输出高停机电压可设置范围}    获取web参数可设置范围    整流器输出高停机电压
    设置web参数量    整流器输出高停机电压    ${输出高停机电压可设置范围}[0]
    #使整流器输出过压保证无正常工作的整流器
    ${整流器输出高电压}    获取web参数量    整流器输出高停机电压
    向上调节电池电压    ${整流器输出高电压}+0.5
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    工作整流器数量    0
    ${状态}    获取web实时数据    电池管理状态
    #无整流器放电状态启动浮充，应F
    设置负载电压电流    53.5    10
    打开负载输出
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    系统停电
    Wait Until Keyword Succeeds    5m    1    设置失败的Web控制量    启动测试
    #整流器过压恢复，5min应能恢复
    关闭负载输出
    向下调节电池电压    53.5
    ${在线整流器数量}    获取web实时数据    在线整流器数量
    Wait Until Keyword Succeeds    5m10s    1    信号量数据值为    工作整流器数量    ${在线整流器数量}
    [Teardown]    Run keywords    电池管理参数恢复默认值
    ...    AND    重置电池模拟器输出

电池容量不足时设置测试
    
    电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    20
    设置web参数量    负载一次下电使能    允许
    设置web参数量    负载二次下电使能    允许
    设置web参数量    下电模式    电池剩余容量
    sleep    5
    ${一次下电可设置范围}    获取web参数上下限范围    负载一次下电SOC阈值
    设置web参数量    负载一次下电SOC阈值    ${一次下电可设置范围}[0]
    ${二次下电可设置范围}    获取web参数上下限范围    负载二次下电SOC阈值
    设置web参数量    负载二次下电SOC阈值    ${二次下电可设置范围}[0]
    ${测试失败可设置范围}    获取web参数可设置范围    测试失败SOC阈值
    设置web参数量    测试失败SOC阈值    ${测试失败可设置范围}[1]
    ${测试终止可设置范围}    获取web参数可设置范围    测试终止SOC阈值
    ${测试终止设置值}    evaluate    ${测试终止可设置范围}[1]-3
    设置web参数量    测试终止SOC阈值    ${测试终止设置值}
    ${测试终止容量}    获取web参数量    测试终止SOC阈值
    Wait Until Keyword Succeeds    10    1    设置web参数量    市电额定有功功率    2.1
    Wait Until Keyword Succeeds    10    1    设置web参数量    市电降额系数    85
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    80
    打开负载输出
    Wait Until Keyword Succeeds    32m    1    信号量数据值小于    电池组当前容量比率-1    ${测试终止容量}
    ${电池当前容量}    获取web实时数据    电池组当前容量比率-1
    Wait Until Keyword Succeeds    5m    1    设置失败的web控制量    启动测试
    关闭负载输出
    [Teardown]    Run keywords    电池管理参数恢复默认值
    ...    AND    设置web设备参数量为默认值    市电降额系数    市电额定有功功率
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    100
    ...    AND    关闭负载输出

直流电压低时设置测试
    [Documentation]    ==20190322-F：直流电压低启动测试成功
    
    电池管理初始化
    ${直流电压高范围}    获取web参数上下限范围    直流电压高阈值
    设置web参数量    直流电压高阈值    ${直流电压高范围}[0]
    ${直流电压过低范围}    获取web参数上下限范围    直流电压过低阈值
    ${电压过低可设置范围}    获取web参数可设置范围    直流电压过低阈值
    设置web参数量    直流电压过低阈值    ${电压过低可设置范围}[0]
    ${直流电压低范围}    获取web参数上下限范围    直流电压低阈值
    ${直流电压低可设范围}    获取web参数可设置范围    直流电压低阈值
    设置web参数量    直流电压低阈值    ${直流电压低可设范围}[1]
    ${直流电压低阈值}    获取web参数量    直流电压低阈值
    ${直流电压目标}    evaluate    ${直流电压低阈值}-0.5
    设置web参数量    浮充电压    ${直流电压目标}
    sleep    2m
    向下调节电池电压    ${直流电压目标}
    sleep    5
    Wait Until Keyword Succeeds    10m    2    判断告警存在    直流电压低
    Wait Until Keyword Succeeds    5m    2s    设置失败的web控制量    启动测试
    ${告警恢复值}    evaluate    ${直流电压低阈值}+1
    设置web参数量    浮充电压    ${告警恢复值}
    向上调节电池电压    ${告警恢复值}
    Wait Until Keyword Succeeds    5m    2    判断告警不存在    直流电压低
    [Teardown]    电池管理参数恢复默认值

直流电压高时设置测试
    [Documentation]    ==20190323-f：启动均充失败（开始增加不为直流电压高的判断）
    
    电池管理初始化
    设置负载电压电流    53.5    10
    打开负载输出
    ${直流电压过高阈值范围}    获取web参数上下限范围    直流电压过高阈值
    设置web参数量    直流电压过高阈值    ${直流电压过高阈值范围}[0]
    ${直流电压高阈值范围}    获取web参数上下限范围    直流电压高阈值
    ${直流电压高阈值可设置范围}    获取web参数可设置范围    直流电压高阈值
    设置web参数量    直流电压高阈值    ${直流电压高阈值可设置范围}[0]
    ${均充电压设置值}    evaluate    ${直流电压高阈值可设置范围}[0]+1
    设置web参数量    均充电压    ${均充电压设置值}    #默认56.4
    设置web参数量    浮充电压    ${直流电压高阈值可设置范围}[0]    #默认53.5
    sleep    3
    向上调节电池电压    ${直流电压高阈值可设置范围}[0]+0.5
    Wait Until Keyword Succeeds    5m    1    判断告警存在    直流电压高
    Wait Until Keyword Succeeds    5m    2s    设置web控制量    启动测试
    Wait Until Keyword Succeeds    20    1    信号量数据值为    电池管理状态    测试
    关闭负载输出
    设置web参数量    浮充电压    53.5    #默认53.5
    设置web参数量    均充电压    56.4    #默认56.4
    设置web参数量    直流电压高阈值    ${直流电压高阈值范围}[0]    #默认58.5
    向下调节电池电压    53.5
    Wait Until Keyword Succeeds    5m    2    判断告警不存在    直流电压高
    [Teardown]    Run keywords    电池管理参数恢复默认值
    ...    AND    关闭负载输出

电池下电时设置测试
    [Documentation]    下电模式：0:禁止；1:电池电压；2:停电时间；3:电池剩余容量
    [Tags]
    
    电池管理初始化
    #获取参数/默认值
    wait until keyword succeeds    10    1    设置web参数量    电池高温下电使能    允许
    ${告警级别}    获取web参数量    电池高温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    电池高温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    电池高温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    sleep    3
    ${可设范围}    获取web参数可设置范围    电池高温下电温度
    设置web参数量    电池高温下电温度    ${可设范围}[0]
    run keyword and ignore error    设置web参数量    下电控制延时    0
    显示属性配置    电池高温下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    3
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    10    2    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    设置负载电压电流    53.5    10
    打开负载输出
    wait until keyword succeeds    10m    1    判断告警存在    电池高温下电
    Wait Until Keyword Succeeds    3m    2    信号量数据值为    电池高温下电控制状态    动作
    Wait Until Keyword Succeeds    5m    1    设置失败的web控制量    启动测试
    #恢复
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    -10    1.6    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    电池高温下电控制状态    恢复
    wait until keyword succeeds    4m    2    判断告警不存在    电池高温下电
    显示属性配置    电池高温下电控制状态    数字量    web_attr=Off    gui_attr=Off
    关闭负载输出
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池高温下电温度    电池高温下电使能
    ...    AND    关闭负载输出

电池温度高时设置测试
    [Documentation]    0:浮充/Float;1:均充/Equal;2:测试/Test;3:交流停电（以前叫放电）/Discharge;4:检测/Detect;5:过渡/Transition;6:充电/Charge
    
    电池管理初始化
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    电池温度高
    #获取告警级别和输出干接点设置值
    ${级别设置值}    获取web参数量    电池温度高
    ${干接点设置值}    获取web参数量    电池温度高干接点
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池温度高    严重
    run keyword if    '${干接点设置值}'=='无干接点'    设置web参数量    电池温度高干接点    A1
    sleep    3
    ${级别设置值}    获取web参数量    电池温度高
    ${干接点设置值}    获取web参数量    电池温度高干接点
    ${干接点序号}    strip string    ${干接点设置值}    characters=A
    #（1）告警产生
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    10    1.6    电池_1    电池温度
    #产生电池温度高告警
    ${电池温度高阈值范围}    获取web参数上下限范围    电池温度高阈值
    ${电池温度高阈值可设置范围}    获取web参数可设置范围    电池温度高阈值
    设置web参数量    电池温度高阈值    ${电池温度高阈值可设置范围}[0]
    ${电池温度}    获取web实时数据    电池温度-1
    Wait Until Keyword Succeeds    10    1    判断告警存在    电池温度高
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动测试
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    测试
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    设置web参数量    电池温度高阈值    ${电池温度高阈值范围}[0]
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池温度高
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    浮充
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池温度高阈值
