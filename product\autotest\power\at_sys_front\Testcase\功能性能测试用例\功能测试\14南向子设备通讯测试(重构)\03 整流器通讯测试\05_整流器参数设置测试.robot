*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
批量设置整流器参数测试
    [Tags]    PMSA-NTest
    写入CSV文档    整流器参数测试    参数名称    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=parameter    INDEX_DEVICE_CH=整流器组    INDEX_DATA_TYPE=int
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除参数信号}    ${排除列表}
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${取值约定}    Get From Dictionary    ${i}    convention
        ${设备名称}    Get From Dictionary    ${i}    device_name
        run keyword if    '${取值约定}'=='False'    Run Keyword And Continue On Failure    南向子设备数值型参数设置封装判断结果    SMR    ${信号名称}    参数    整流器参数测试
        run keyword if    '${取值约定}'=='True'    Run Keyword And Continue On Failure    南向子设备有取值约定型参数设置封装判断结果    SMR    ${信号名称}    参数    整流器参数测试
    END
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=parameter    INDEX_DEVICE_CH=整流器组    INDEX_DATA_TYPE=float
    ${排除列表}    create list
    ${列表2}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除参数信号}    ${排除列表}
    FOR    ${i}    IN    @{列表2}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${取值约定}    Get From Dictionary    ${i}    convention
        ${设备名称}    Get From Dictionary    ${i}    device_name
        run keyword if    '${取值约定}'=='False'    Run Keyword And Continue On Failure    南向子设备数值型参数设置封装判断结果    SMR    ${信号名称}    参数    整流器参数测试
        run keyword if    '${取值约定}'=='True'    Run Keyword And Continue On Failure    南向子设备有取值约定型参数设置封装判断结果    SMR    ${信号名称}    参数    整流器参数测试
    END

设置整流器限流点比率参数测试
    [Tags]    PMSA-NTest
    连接CSU
    判断web带ID的参数是否存在    整流器默认限流点比率
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    整流器默认限流点比率
    ${缺省值}    获取web参数上下限范围    整流器默认限流点比率
    ${可设置范围}    获取web参数可设置范围    整流器默认限流点比率
    ${获取子工具信号名称}    获取子工具信号名称    SMR    0x8001050090001
    Return From Keyword If    '${获取子工具信号名称}'==''    获取子工具信号名称为空
    @{信号名称及关联SID}    split string    ${获取子工具信号名称}    ,
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    整流器默认限流点比率    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    整流器默认限流点比率
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        Wait Until Keyword Succeeds    2m    20    获取子工具特殊参数直到变化为    SMR    参数    ${信号名称及关联SID}[0]    整流器默认限流点比率    ${参数设置}
        ${参数获取}    获取web参数量    整流器默认限流点比率
        should be true    ${参数获取}==${参数设置}
    END
