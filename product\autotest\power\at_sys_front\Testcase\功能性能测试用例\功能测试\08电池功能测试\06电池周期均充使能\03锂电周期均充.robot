*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
智能锂电周期均充
    [Setup]    #测试用例前置条件
    Comment    电池管理初始化
    连接CSU
    设置web参数量    电池配置    常规锂电池
    sleep    3
    ${均充使能}    获取web参数量    均充使能
    ${使能}    Convert To String    ${均充使能}
    Should Be Equal As Strings    ${使能}    None
    ${周期均充使能}    获取web参数量    电池周期均充使能
    ${使能}    Convert To String    ${周期均充使能}
    Should Be Equal As Strings    ${使能}    None
    ${电池均充周期}    获取web参数量    电池均充周期
    Should Be Equal As Strings    ${电池均充周期}    ${EMPTY}
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池配置

常规锂电周期均充
    [Setup]    #测试用例前置条件
    Comment    电池管理初始化
    连接CSU
    设置web参数量    电池配置    智能锂电
    sleep    3
    ${均充使能}    获取web参数量    均充使能
    ${使能}    Convert To String    ${均充使能}
    Should Be Equal As Strings    ${使能}    None
    ${周期均充使能}    获取web参数量    电池周期均充使能
    ${使能}    Convert To String    ${周期均充使能}
    Should Be Equal As Strings    ${使能}    None
    ${电池均充周期}    获取web参数量    电池均充周期
    Should Be Equal As Strings    ${电池均充周期}    ${EMPTY}
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池配置
