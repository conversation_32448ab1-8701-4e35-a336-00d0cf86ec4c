*** Settings ***
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
批量获取实时告警测试
    写入CSV文档    FB100B1数字量和告警量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=FBBMS
    ${排除列表}    create list
    @{告警列表1}   create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B1排除告警量信号}    ${排除列表}    1
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
	${dict1}          Create Dictionary 
        Set To Dictionary    ${dict1}     name     ${信号名称}  
	Append To List       ${告警列表1}    ${dict1}
    END
    Run Keyword And Continue On Failure    南向RS485子设备告警量列表产生封装判断结果    smartli_fb100b1    ${告警列表1}    只读    ${告警产生}    FB100B1数字量和告警量获取测试    电池    FBBMS
    Run Keyword And Continue On Failure    南向RS485子设备告警量列表恢复封装判断结果    smartli_fb100b1    ${告警列表1}    只读    ${告警恢复}    FB100B1数字量和告警量获取测试    电池    FBBMS
    

BMS通信断告警测试
    连接CSU
    #子设备工具模拟
    控制子工具运行停止    smartli_fb100b1    关闭
    Wait Until Keyword Succeeds    18m    5    信号量数据值为    <<BMS通信断状态-1~0x17001020330001>>    异常
    Wait Until Keyword Succeeds    18m    5    信号量数据值为    <<BMS通信断状态-8~0x17001020330001>>    异常
    Wait Until Keyword Succeeds    18m    5    信号量数据值为    <<BMS通信断状态-${铁锂电池组数}~0x17001020330001>>    异常
    ${级别设置值}    获取web参数量    <<BMS通信断告警~0x170010302f0001>>
    run keyword if    '${级别设置值}'=='屏蔽'    wait until keyword succeeds    10    2    设置web参数量    <<BMS通信断告警~0x170010302f0001>>    严重
    wait until keyword succeeds    5m    2    查询指定告警信息    BMS通信断告警
    重新启动FB100B1
    Wait Until Keyword Succeeds    18m    5    信号量数据值为    <<BMS通信断状态-1~0x17001020330001>>    正常
    Wait Until Keyword Succeeds    18m    5    信号量数据值为    <<BMS通信断状态-8~0x17001020330001>>    正常
    Wait Until Keyword Succeeds    18m    5    信号量数据值为    <<BMS通信断状态-${铁锂电池组数}~0x17001020330001>>    正常
    wait until keyword succeeds    5m    2    查询指定告警信息不为    BMS通信断告警
    [Teardown]    重新启动FB100B1

