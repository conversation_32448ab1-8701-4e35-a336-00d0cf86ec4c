*** Settings ***
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot



*** Test Cases ***
power_sm_0001_批量获取FBBMS设备信息
    写入CSV文档    FB100B3设备信息获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string64    INDEX_DEVICE_CH=FBBMS
    ${排除列表}    create list
	@{信号名称列表1}    create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除设备版本信息}    ${排除列表}    1
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    FBBMS    device info    ${FB100B3排除设备版本信息}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    FOR    ${i}    IN    @{power_sm待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
		${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
		Append To List       ${信号名称列表1}    ${dict}
    END
	Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    smartli    ${信号名称列表1}    只读    V99.23    FB100B3设备信息获取测试    字符    获取FBBMS设备信息  
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    smartli    ${信号名称列表1}    只读    V10.10    FB100B3设备信息获取测试    字符    获取FBBMS设备信息   
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    smartli    ${信号名称列表1}    只读    V1.81    FB100B3设备信息获取测试    字符    获取FBBMS设备信息   

    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string64    INDEX_DEVICE_CH=FBBMS
    ${排除列表}    create list
	@{信号名称列表2}    create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除设备名称信息}    ${排除列表}    1
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    FBBMS    device info    ${FB100B3排除设备名称信息}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    FOR    ${i}    IN    @{power_sm待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
		${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
		Append To List       ${信号名称列表2}    ${dict}
    END
	Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    smartli    ${信号名称列表2}    只读    VZXDU48 FB100B3    FB100B3设备信息获取测试    字符    获取FBBMS设备信息    
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    smartli    ${信号名称列表2}    只读    ZTE-smartli        FB100B3设备信息获取测试    字符    获取FBBMS设备信息   
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    smartli    ${信号名称列表2}    只读    VZXDU48 FB100C2    FB100B3设备信息获取测试    字符    获取FBBMS设备信息    
  
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string64    INDEX_DEVICE_CH=FBBMS
    ${排除列表}    create list
	@{信号名称列表3}    create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除设备序列号信息}    ${排除列表}    1
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    FBBMS    device info    ${FB100B3排除设备序列号信息}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    FOR    ${i}    IN    @{power_sm待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
		${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
		Append To List       ${信号名称列表3}    ${dict}
    END
	Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    smartli    ${信号名称列表3}    读写    210097205489    FB100B3设备信息获取测试    字符    获取FBBMS设备信息    
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    smartli    ${信号名称列表3}    读写    210097205673    FB100B3设备信息获取测试    字符    获取FBBMS设备信息    
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    smartli    ${信号名称列表3}    读写    210097205688    FB100B3设备信息获取测试    字符    获取FBBMS设备信息   
 
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=float    INDEX_DEVICE_CH=FBBMS
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    1
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    FBBMS    device info    null
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    1           power_sm
	Run Keyword And Continue On Failure    南向子设备厂家信息列表封装判断结果    power_sm    smartli    ${缺省值列表}    读写      FB100B3设备信息获取测试    数值    获取FBBMS设备信息    
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    2           power_sm
	Run Keyword And Continue On Failure    南向子设备厂家信息列表封装判断结果    power_sm    smartli    ${缺省值列表}    读写      FB100B3设备信息获取测试    数值    获取FBBMS设备信息    
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    0           power_sm
	Run Keyword And Continue On Failure    南向子设备厂家信息列表封装判断结果    power_sm    smartli    ${缺省值列表}   读写       FB100B3设备信息获取测试    数值    获取FBBMS设备信息    
  
	
	   
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=int    INDEX_DEVICE_CH=FBBMS
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    1
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    FBBMS    device info    null
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    1        power_sm
    Run Keyword And Continue On Failure    南向子设备厂家信息列表封装判断结果    power_sm    smartli    ${缺省值列表}    只读       FB100B3设备信息获取测试    数值    获取FBBMS设备信息    
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    2        power_sm
    Run Keyword And Continue On Failure    南向子设备厂家信息列表封装判断结果    power_sm    smartli    ${缺省值列表}    只读       FB100B3设备信息获取测试    数值    获取FBBMS设备信息    
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    0        power_sm
	Run Keyword And Continue On Failure    南向子设备厂家信息列表封装判断结果    power_sm    smartli    ${缺省值列表}    只读       FB100B3设备信息获取测试    数值    获取FBBMS设备信息    
    

power_sm_0002_BMS软件发布日期3
    [Setup]
    连接CSU
    设置子工具值    smartli    all    只读    BMS软件发布日期    2020
    设置子工具值    smartli    all    只读    BMS软件发布日期mouth    12
    设置子工具值    smartli    all    只读    BMS软件发布日期day    5
    @{锂电序号随机list}    从1-15个数中随机选n个不重复的单体    ${北向协议铁锂最大数}    3
    FOR    ${锂电序号}    IN    @{锂电序号随机list}
        Wait Until Keyword Succeeds    20m    5    对比WEB和能源网管单个数据    获取FBBMS设备信息    BMS软件发布日期-${锂电序号}
        ...     2020-12-05
    END
    设置子工具值    smartli    all    只读    BMS软件发布日期    2020
    设置子工具值    smartli    all    只读    BMS软件发布日期mouth    12
    设置子工具值    smartli    all    只读    BMS软件发布日期day    4
    FOR    ${锂电序号}    IN    @{锂电序号随机list}
        Wait Until Keyword Succeeds    20m    5    对比WEB和能源网管单个数据    获取FBBMS设备信息    BMS软件发布日期-${锂电序号}
        ...     2020-12-04
    END

power_sm_0003_BDCU软件发布日期8
    [Setup]
    连接CSU
    设置子工具值    smartli    all    只读    BDU版本日期    2020
    设置子工具值    smartli    all    只读    BDU版本日期mouth    12
    设置子工具值    smartli    all    只读    BDU版本日期day    12
    @{锂电序号随机list}    从1-15个数中随机选n个不重复的单体    ${北向协议铁锂最大数}    3
    FOR    ${锂电序号}    IN    @{锂电序号随机list}
        Wait Until Keyword Succeeds    20m    5    对比WEB和能源网管单个数据    获取FBBMS设备信息    BDCU软件发布日期-${锂电序号}
        ...     2020-12-12
    END
    设置子工具值    smartli    all    只读    BDU版本日期    2020
    设置子工具值    smartli    all    只读    BDU版本日期mouth    11
    设置子工具值    smartli    all    只读    BDU版本日期day    11
    FOR    ${锂电序号}    IN    @{锂电序号随机list}
        Wait Until Keyword Succeeds    20m    5    对比WEB和能源网管单个数据    获取FBBMS设备信息    BDCU软件发布日期-${锂电序号}
        ...     2020-11-11
    END

power_sm_0004_电池启用日期13
    [Tags]    3
    [Setup]
    连接CSU
    设置子工具值    smartli    all    只读    电芯启用日期    2018
    设置子工具值    smartli    all    只读    电芯启用日期mouth    07
    设置子工具值    smartli    all    只读    电芯启用日期day    28
    @{锂电序号随机list}    从1-15个数中随机选n个不重复的单体    ${北向协议铁锂最大数}    3
    FOR    ${锂电序号}    IN    @{锂电序号随机list}
        Wait Until Keyword Succeeds    15m    5    对比WEB和能源网管单个数据    获取FBBMS设备信息    电池启用日期-${锂电序号}
        ...     2018-07-28
    END
    设置子工具值    smartli    all    只读    电芯启用日期    2020
    设置子工具值    smartli    all    只读    电芯启用日期mouth    10
    设置子工具值    smartli    all    只读    电芯启用日期day    1
    FOR    ${锂电序号}    IN    @{锂电序号随机list}
        Wait Until Keyword Succeeds    15m    5    对比WEB和能源网管单个数据    获取FBBMS设备信息    电池启用日期-${锂电序号}
        ...     2020-10-11
    END

power_sm_0005_电池模组序列号_无
    [Tags]    3
    Log    模拟工具暂不支持模拟此信息
    Log    暂不自动化

power_sm_0006_电池生产日期
    [Tags]    3
    连接CSU
    设置子工具值    smartli    all    只读    电芯出厂日期    2020
    设置子工具值    smartli    all    只读    电芯出厂日期mouth    12
    设置子工具值    smartli    all    只读    电芯出厂日期day    12
    sleep    4m
    ${日期1}    获取web实时数据    电池生产日期-1
    ${日期8}    获取web实时数据    电池生产日期-8
    ${日期16}    获取web实时数据    电池生产日期-${铁锂电池组数}
    should be equal    '${日期1}'    '2020-12-12'
    should be equal    '${日期8}'    '2020-12-12'
    should be equal    '${日期16}'    '2020-12-12'
    设置子工具值    smartli    all    只读    电芯出厂日期    2020
    设置子工具值    smartli    all    只读    电芯出厂日期mouth    11
    设置子工具值    smartli    all    只读    电芯出厂日期day    11
    sleep    4m
    ${日期1}    获取web实时数据    电池生产日期-1
    ${日期8}    获取web实时数据    电池生产日期-8
    ${日期16}    获取web实时数据    电池生产日期-${铁锂电池组数}
    should be equal    '${日期1}'    '2020-11-11'
    should be equal    '${日期8}'    '2020-11-11'
    should be equal    '${日期16}'    '2020-11-11'
    sleep    3m
    @{锂电序号随机list}    从1-15个数中随机选n个不重复的单体    ${北向协议铁锂最大数}    3
    FOR    ${锂电序号}    IN    @{锂电序号随机list}
        ${web获取}    获取web实时数据    电池生产日期-${锂电序号}
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取FBBMS设备信息    电池生产日期-${锂电序号}    ${SSH}
        should be equal as strings    ${web获取}    2020-11-11
        should be equal as strings    ${power_sm获取值1}    2020-11-11
    END

power_sm_0007_电池容量_无
    [Tags]    3
    Log    模拟工具暂不支持模拟此信息
    Log    暂不自动化

power_sm_0008_整机序列号_无
    [Tags]    3
    Log    模拟工具暂不支持模拟此信息
    Log    暂不自动化
