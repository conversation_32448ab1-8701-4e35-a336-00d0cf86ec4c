*** Settings ***
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_交流电表系统名称
    连接CSU
    设置子工具值    DMU_aemb    all    只读    软件名称    DTSD36MAbC
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表系统名称-1    DTSD36MAbC
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取交流电表设备信息    交流电表系统名称-1    ${SSH}
    should be equal as strings    ${power_sm获取值1}    DTSD36MAbC
    设置子工具值    DMU_aemb    all    只读    软件名称    DTSD36M
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表系统名称-1    DTSD36M
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取交流电表设备信息    交流电表系统名称-1    ${SSH}
    should be equal as strings    ${power_sm获取值1}    DTSD36M
    [Teardown]    设置子工具值    DMU_aemb    all    只读    软件名称    AEMB

power_sm_0002_交流电表软件版本
    连接CSU
    设置子工具值    DMU_aemb    all    只读    软件版本    V0.0
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表软件版本-1    V0.0
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取交流电表设备信息    交流电表软件版本-1    ${SSH}
    should be equal as strings    ${power_sm获取值1}    V0.0
    设置子工具值    DMU_aemb    all    只读    软件版本    V2.11
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表软件版本-1    V2.11
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取交流电表设备信息    交流电表软件版本-1    ${SSH}
    should be equal as strings    ${power_sm获取值1}    V2.11

power_sm_0003_交流电表软件日期
    连接CSU
    设置子工具值    DMU_aemb    all    只读    软件日期    2020-11-11
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表软件发布日期-1    2020-11-11
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取交流电表设备信息    交流电表软件发布日期-1    ${SSH}
    should be equal as strings    ${power_sm获取值1}    2020-11-11
    设置子工具值    DMU_aemb    all    只读    软件日期    2022-11-11
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表软件发布日期-1    2022-11-11
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取交流电表设备信息    交流电表软件发布日期-1    ${SSH}
    should be equal as strings    ${power_sm获取值1}    2022-11-11
