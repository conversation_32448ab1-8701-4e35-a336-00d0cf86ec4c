*** Settings ***
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp_test关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/Web关键字_V30.robot
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
snmp_0002_powerUnit1alarm_level√
    Comment    :FOR    ${PU序号}    IN RANGE    1    ${北向协议PU最大数}+1
    @{PU随机list}    从1-15个数中随机选n个不重复的单体    ${北向协议PU最大数}    4
    FOR    ${PU序号}    IN    @{PU随机list}
        ${比较结果}    对比告警级别_V2C    powerUnit${PU序号}alarm
        should be true    ${比较结果}
    END

snmp_0004_powerUnit1alarm__level_writeX
    Comment    : FOR    ${VAR}    IN    powerUnit1alarm    powerUnit2alarm    powerUnit3alarm
    Comment    Comment    ${比较结果}    批量修改告警级别_V2C    ${VAR}
    Comment    Comment    should be true    ${比较结果}
    Comment    ${比较结果}    批量设置四种/五种告警级别    ${VAR}
    Comment    should be true    ${比较结果}
    Comment    ${比较结果}    批量设置四种/五种告警级别    powerUnit1alarm
    Comment    should be true    ${比较结果}
    Comment    ${比较结果}    批量设置四种/五种告警级别    powerUnit2alarm
    Comment    should be true    ${比较结果}
    Comment    :FOR    ${PU序号}    IN RANGE    1    ${北向协议PU最大数}+1
    @{PU随机list}    从1-15个数中随机选n个不重复的单体    ${北向协议PU最大数}    4
    FOR    ${PU序号}    IN    @{PU随机list}
        ${比较结果}    批量设置四种/五种告警级别    powerUnit${PU序号}alarm
        should be true    ${比较结果}
    END

snmp_0006_powerUnit1alarm_relayl√
    Comment    : FOR    ${VAR}    IN    powerUnit1alarm    powerUnit2alarm    powerUnit3alarm
    Comment    ${比较结果}    对比告警干接点_V2C    ${VAR}
    Comment    should be true    ${比较结果}
    Comment    :FOR    ${PU序号}    IN RANGE    1    ${北向协议PU最大数}+1
    @{PU随机list}    从1-15个数中随机选n个不重复的单体    ${北向协议PU最大数}    4
    FOR    ${PU序号}    IN    @{PU随机list}
        ${比较结果}    对比告警干接点_V2C    powerUnit${PU序号}alarm
        should be true    ${比较结果}
    END

snmp_0008_powerUnit1alarm_relay_writel√
    Comment    : FOR    ${VAR}    IN    powerUnit1alarm    powerUnit2alarm    powerUnit3alarm
    Comment    ${比较结果}    批量修改告警干接点_V2C    ${VAR}
    Comment    should be true    ${比较结果}
    Comment    :FOR    ${PU序号}    IN RANGE    1    ${北向协议PU最大数}+1
    @{PU随机list}    从1-15个数中随机选n个不重复的单体    ${北向协议PU最大数}    4
    FOR    ${PU序号}    IN    @{PU随机list}
        ${比较结果}    批量修改告警干接点_V2C    powerUnit${PU序号}alarm
        should be true    ${比较结果}
    END
