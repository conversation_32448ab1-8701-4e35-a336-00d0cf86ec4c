*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
油机启停过程中交流告警自动屏蔽测试

油机异常告警
    油机管理初始化
    Wait Until Keyword Succeeds    5m    1    设置web控制量    油机开启
    sleep    20
    ${干接点实际是否动作状态1}    获取干接点状态    ${油机干接点状态}
    should be true    ${干接点实际是否动作状态1}==0    #干接点动作为1（断开），恢复为0（闭合）
    Comment    同时设置三相电压频率    220    50
    Comment    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    5m    1    判断告警存在    油机异常告警
    Wait Until Keyword Succeeds    5m    1    设置web控制量    油机开启
    sleep    20
    ${干接点实际是否动作状态1}    获取干接点状态    ${油机干接点状态}
    should be true    ${干接点实际是否动作状态1}==0    #干接点动作为1（断开），恢复为0（闭合）
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    运行
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    油机异常告警
    Wait Until Keyword Succeeds    5m    1    设置web控制量    油机关闭
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    停止
    Comment    sleep    20
    ${干接点实际是否动作状态1}    获取干接点状态    ${油机干接点状态}
    should be true    ${干接点实际是否动作状态1}==1    #干接点动作为1（断开），恢复为0（闭合）
    关闭交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
