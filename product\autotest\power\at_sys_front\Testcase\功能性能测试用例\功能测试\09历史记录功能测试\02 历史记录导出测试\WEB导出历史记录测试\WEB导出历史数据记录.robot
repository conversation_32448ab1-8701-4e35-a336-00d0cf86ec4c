*** Settings ***
Suite Setup       设置历史记录最大条数    history_data    0    100
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
WEB导出历史数据测试
    连接CSU
    ${数据条数1}    ${第一条数据内容1}    ${最新一条数据内容1}    查询指定历史记录    所有历史数据
    ${导出路径}    导出指定历史记录    所有历史数据    ${datapassword}
    ${数据条数2}    ${第一条数据内容2}    ${最新一条数据内容2}    解析指定历史记录文件    所有历史数据    ${导出路径}
    should be equal    ${第一条数据内容1}[0]    ${第一条数据内容2}[1]
    should be equal    ${数据条数1}    ${数据条数2}
    should be equal    ${最新一条数据内容1}[0]    ${最新一条数据内容2}[1]
