*** Settings ***
Suite Setup
Suite Teardown
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
1363_0004_V3.0_1363_通用命令_时间信息1-6_1
    [Tags]    1
    [Setup]    时间设置前置条件
    ${1363数据}    通用命令_获取时间信息1-6    ${g_prtcl_1363}    <<获取时间1-6~CID1=40H>>    ${g_ver_1363}
    ${web数据}    获取系统时间
    ${对比结果}    对比时间信息    ${1363数据}    ${web数据}    1363_获取时间1
    should be true    ${对比结果}
    ${设置时间}    通用命令_设置时间信息1-6    ${g_prtcl_1363}    <<获取时间1-6~CID1=40H>>    2019,1,2,3,4,5    ${g_ver_1363}
    ${1363数据}    通用命令_获取时间信息1-6    ${g_prtcl_1363}    <<获取时间1-6~CID1=40H>>    ${g_ver_1363}
    ${web数据}    获取系统时间
    ${对比结果}    对比时间信息    ${1363数据}    ${web数据}    1363_设置时间1
    should be true    ${对比结果}
    ${同步时间}    同步系统时间
    [Teardown]    设置web设备参数量为默认值    电池均充周期    电池测试周期    电池检测周期    预约均充使能

1363_0006_V3.0_1363_通用命令_时间信息1-6_2
    [Tags]    1
    [Setup]    时间设置前置条件
    ${1363数据}    通用命令_获取时间信息1-6    ${g_prtcl_1363}    <<获取时间1-6~CID1=41H>>    ${g_ver_1363}
    ${web数据}    获取系统时间
    ${对比结果}    对比时间信息    ${1363数据}    ${web数据}    1363_获取时间2
    should be true    ${对比结果}
    ${设置时间}    通用命令_设置时间信息1-6    ${g_prtcl_1363}    <<获取时间1-6~CID1=41H>>    2019,1,2,3,4,5    ${g_ver_1363}
    ${1363数据}    通用命令_获取时间信息1-6    ${g_prtcl_1363}    <<获取时间1-6~CID1=41H>>    ${g_ver_1363}
    ${web数据}    获取系统时间
    ${对比结果}    对比时间信息    ${1363数据}    ${web数据}    1363_设置时间2
    should be true    ${对比结果}
    ${同步时间}    同步系统时间
    [Teardown]    设置web设备参数量为默认值    电池均充周期    电池测试周期    电池检测周期    预约均充使能

1363_0008_V3.0_1363_通用命令_时间信息1-6_3
    [Tags]    1
    [Setup]    时间设置前置条件
    ${1363数据}    通用命令_获取时间信息1-6    ${g_prtcl_1363}    <<获取时间1-6~CID1=42H>>    ${g_ver_1363}
    ${web数据}    获取系统时间
    ${对比结果}    对比时间信息    ${1363数据}    ${web数据}    1363_获取时间3
    should be true    ${对比结果}
    ${设置时间}    通用命令_设置时间信息1-6    ${g_prtcl_1363}    <<获取时间1-6~CID1=42H>>    2019,1,2,3,4,5    ${g_ver_1363}
    ${1363数据}    通用命令_获取时间信息1-6    ${g_prtcl_1363}    <<获取时间1-6~CID1=42H>>    ${g_ver_1363}
    ${web数据}    获取系统时间
    ${对比结果}    对比时间信息    ${1363数据}    ${web数据}    1363_设置时间3
    should be true    ${对比结果}
    ${同步时间}    同步系统时间
    [Teardown]    设置web设备参数量为默认值    电池均充周期    电池测试周期    电池检测周期    预约均充使能

1363_0010_V3.0_1363_通用命令_时间信息1-6_4
    [Tags]    1
    [Setup]    时间设置前置条件
    ${1363数据}    通用命令_获取时间信息1-6    ${g_prtcl_1363}    <<获取时间1-6~CID1=91H>>    ${g_ver_1363}
    ${web数据}    获取系统时间
    ${对比结果}    对比时间信息    ${1363数据}    ${web数据}    1363_获取时间4
    should be true    ${对比结果}
    ${设置时间}    通用命令_设置时间信息1-6    ${g_prtcl_1363}    <<获取时间1-6~CID1=91H>>    2019,1,2,3,4,5    ${g_ver_1363}
    ${1363数据}    通用命令_获取时间信息1-6    ${g_prtcl_1363}    <<获取时间1-6~CID1=91H>>    ${g_ver_1363}
    ${web数据}    获取系统时间
    ${对比结果}    对比时间信息    ${1363数据}    ${web数据}    1363_设置时间4
    should be true    ${对比结果}
    ${同步时间}    同步系统时间
    [Teardown]    设置web设备参数量为默认值    电池均充周期    电池测试周期    电池检测周期    预约均充使能

1363_0012_V3.0_1363_通用命令_时间信息1-6_5
    [Tags]    1
    [Setup]    时间设置前置条件
    ${1363数据}    通用命令_获取时间信息1-6    ${g_prtcl_1363}    <<获取时间1-6~CID1=D0H>>    ${g_ver_1363}
    ${web数据}    获取系统时间
    ${对比结果}    对比时间信息    ${1363数据}    ${web数据}    1363_获取时间5
    should be true    ${对比结果}
    ${设置时间}    通用命令_设置时间信息1-6    ${g_prtcl_1363}    <<获取时间1-6~CID1=D0H>>    2019,1,2,3,4,5    ${g_ver_1363}
    ${1363数据}    通用命令_获取时间信息1-6    ${g_prtcl_1363}    <<获取时间1-6~CID1=D0H>>    ${g_ver_1363}
    ${web数据}    获取系统时间
    ${对比结果}    对比时间信息    ${1363数据}    ${web数据}    1363_设置时间5
    should be true    ${对比结果}
    ${同步时间}    同步系统时间
    [Teardown]    设置web设备参数量为默认值    电池均充周期    电池测试周期    电池检测周期    预约均充使能

1363_0014_V3.0_1363_通用命令_时间信息1-6_6
    [Tags]    1
    [Setup]    时间设置前置条件
    ${1363数据}    通用命令_获取时间信息1-6    ${g_prtcl_1363}    <<获取时间1-6~CID1=46H>>    ${g_ver_1363}
    ${web数据}    获取系统时间
    ${对比结果}    对比时间信息    ${1363数据}    ${web数据}    1363_获取时间6
    should be true    ${对比结果}
    ${设置时间}    通用命令_设置时间信息1-6    ${g_prtcl_1363}    <<获取时间1-6~CID1=46H>>    2019,1,2,3,4,5    ${g_ver_1363}
    ${1363数据}    通用命令_获取时间信息1-6    ${g_prtcl_1363}    <<获取时间1-6~CID1=46H>>    ${g_ver_1363}
    ${web数据}    获取系统时间
    ${对比结果}    对比时间信息    ${1363数据}    ${web数据}    1363_设置时间6
    should be true    ${对比结果}
    ${同步时间}    同步系统时间
    [Teardown]    设置web设备参数量为默认值    电池均充周期    电池测试周期    电池检测周期    预约均充使能
