*** Settings ***
Test Setup        Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
uniform_0002_整流器休眠唤醒
    [Documentation]    0:否/No;1:是/Yes
    [Setup]    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-1    否    #为保证整流器数据获取正常
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流节能模式    自由
    sleep    5
    ${工作整流器地址}    获取工作整流器地址
    FOR    ${i}    IN    @{工作整流器地址[:-1]}
        ${整流器地址}    evaluate    str(${i})
    #休眠
        ${当前工作整流器地址}    Convert To Hex    ${i}     length=2
        Wait Until Keyword Succeeds    2m    1    统一监控协议_设置数据    整流器遥控    命令类型：COMMAND_TYPE    2F ${当前工作整流器地址}    ctrl
        Wait Until Keyword Succeeds    2m    2    信号量数据值为    整流器休眠状态-${i}    是
    END
    sleep    1m
    FOR    ${i}    IN    @{工作整流器地址[:-1]}
        ${整流器地址}    evaluate    str(${i})
        ${当前工作整流器地址}    Convert To Hex    ${i}     length=2
        ${整流器1休眠状态}    获取web实时数据    整流器休眠状态-${i}
        should be equal    '${整流器1休眠状态}'    '是'
        ${整流器1休眠状态}    统一监控协议_获取一个数据的值    整流状态量    整流器休眠状态-${i}
        should be equal    ${整流器1休眠状态}    1
    #唤醒
        Wait Until Keyword Succeeds    2m    1    统一监控协议_设置数据    整流器遥控    命令类型：COMMAND_TYPE    20 ${当前工作整流器地址}    ctrl
        Wait Until Keyword Succeeds    2m    2    信号量数据值为    整流器休眠状态-${i}    否
    END
    sleep    1m
    FOR    ${i}    IN    @{工作整流器地址[:-1]}
        ${整流器地址}    evaluate    str(${i})
        ${整流器1休眠状态}    获取web实时数据    整流器休眠状态-${i}
        should be equal    '${整流器1休眠状态}'    '否'
        ${整流器1休眠状态}    统一监控协议_获取一个数据的值    整流状态量    整流器休眠状态-${i}
        should be equal    ${整流器1休眠状态}    0
    END
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流节能模式    安全
    [Teardown]    run keywords    连接CSU
    ...    AND    Wait Until Keyword Succeeds    30    1    设置web参数量    交流节能模式    安全

uniform_0004_整流器唤醒√
    [Documentation]    0:否/No;1:是/Yes
    [Setup]    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-1    否    #为保证整流器数据获取正常
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流节能模式    自由
    sleep    5
    ${工作整流器地址}    获取工作整流器地址
    FOR    ${i}    IN    @{工作整流器地址[:-1]}
        ${i}    evaluate    str(${i})
        ${当前工作整流器地址}    Convert To Hex    ${i}     length=2
        Wait Until Keyword Succeeds    1m    1    统一监控协议_设置数据    整流器遥控    命令类型：COMMAND_TYPE    20 ${当前工作整流器地址}    ctrl
        Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-${i}    否
        sleep    1m
        ${整流器1休眠状态}    获取web实时数据    整流器休眠状态-${i}
        should be equal    '${整流器1休眠状态}'    '否'
        ${整流器1休眠状态}    统一监控协议_获取一个数据的值    整流状态量    整流器休眠状态-${i}
        should be equal    ${整流器1休眠状态}    0
    END
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流节能模式    安全
    Comment    ${start}    ${end}    获取起始结束时间段    200
    Comment
    Comment    @{历史事件内容1}    获取web历史事件内容    ${start}    ${end}    所有    1    1
    Comment    should contain    ${历史事件内容1}[-1]    整流器休眠
    [Teardown]    run keywords    连接CSU
    ...    AND    Wait Until Keyword Succeeds    30    1    设置web参数量    交流节能模式    安全
