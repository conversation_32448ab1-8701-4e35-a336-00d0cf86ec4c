*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
交流电表通讯断告警
    [Setup]
    连接CSU
    设置子工具个数    ACmeter    5
    wait until keyword succeeds    2m    1    判断告警存在    交流电表通讯断告警-3
    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流电表告警量~CID1=D2H>>    <<获取交流电表告警量~CID2=44H>>    交流电表通讯断-3    1    FF    ${g_ver_1363}
    Should be true    ${1104告警结果}
    设置子工具个数    ACmeter    1
    wait until keyword succeeds    2m    1    判断告警存在    交流电表通讯断告警-2
    wait until keyword succeeds    2m    1    判断告警存在    交流电表通讯断告警-3
    ${1104告警结果2}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流电表告警量~CID1=D2H>>    <<获取交流电表告警量~CID2=44H>>    交流电表通讯断-2    1    FF    ${g_ver_1363}
    ${1104告警结果3}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流电表告警量~CID1=D2H>>    <<获取交流电表告警量~CID2=44H>>    交流电表通讯断-3    1    FF    ${g_ver_1363}
    Should be true    ${1104告警结果2}
    Should be true    ${1104告警结果3}
    设置子工具个数    ACmeter    9
    wait until keyword succeeds    2m    1    判断告警不存在    交流电表通讯断告警
    FOR    ${交流电表序号}    IN RANGE    1    4
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流电表告警量~CID1=D2H>>    <<获取交流电表告警量~CID2=44H>>    交流电表通讯断-${交流电表序号}    0    FF    ${g_ver_1363}
        Should be true    ${1104告警结果}
    END
    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流电表告警量~CID1=D2H>>    <<获取交流电表告警量~CID2=44H>>    交流电表通讯断-1    0    01    ${g_ver_1363}
    Should be true    ${1104告警结果}
    [Teardown]    Run Keywords    设置子工具个数    ACmeter    9
    ...    AND    sleep    2min
