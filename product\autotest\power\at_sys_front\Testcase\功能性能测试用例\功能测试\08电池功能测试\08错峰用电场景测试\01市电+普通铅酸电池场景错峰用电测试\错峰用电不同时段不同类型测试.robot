*** Settings ***
Resource          ../错峰用电场景.robot


*** Test Cases ***
充电系数为0，错峰用电测试(错峰放电)
    ${日期}    错峰用电不同时段不同类型测试初始化  0
    ${时间}    set variable    ${日期} 05:59:59
    设置系统时间    ${时间}
    错峰放电校验    尖峰
    ${时间}    set variable    ${日期} 08:00:00
    设置系统时间    ${时间}
    错峰放电校验    高峰
    ${时间}    set variable    ${日期} 10:00:00
    设置系统时间    ${时间}
    错峰放电校验    平期
    ${时间}    set variable    ${日期} 12:00:00
    设置系统时间    ${时间}
    错峰谷期校验
    ${时间}    set variable    ${日期} 12:59:50
    设置系统时间    ${时间}
    sleep    15
    退出错峰状态
    [Teardown]    错峰用电不同时段不同类型测试后置条件

充电系数不为0，错峰用电测试(错峰放电)
    ${日期}    错峰用电不同时段不同类型测试初始化  0.1
    ${时间}    set variable    ${日期} 05:59:59
    设置系统时间    ${时间}
    错峰放电校验    尖峰
    ${时间}    set variable    ${日期} 08:00:00
    设置系统时间    ${时间}
    错峰放电校验    高峰
    ${时间}    set variable    ${日期} 10:00:00
    设置系统时间    ${时间}
    错峰放电校验    平期
    ${时间}    set variable    ${日期} 12:00:00
    设置系统时间    ${时间}
    错峰谷期校验
    ${时间}    set variable    ${日期} 13:00:00
    设置系统时间    ${时间}
    sleep    15
    退出错峰状态
    [Teardown]    错峰用电不同时段不同类型测试后置条件

充电系数为0，错峰用电测试(错峰切换)
    ${日期}    错峰用电不同时段不同类型测试初始化  0
    ${时间}    set variable    ${日期} 05:59:59
    设置系统时间    ${时间}
    错峰放电校验    尖峰
    ${时间}    set variable    ${日期} 07:00:00
    设置系统时间    ${时间}
    退出错峰状态
    ${时间}    set variable    ${日期} 08:00:00
    设置系统时间    ${时间}
    错峰放电校验    高峰
    ${时间}    set variable    ${日期} 09:00:00
    设置系统时间    ${时间}
    退出错峰状态
    ${时间}    set variable    ${日期} 10:00:00
    设置系统时间    ${时间}
    错峰放电校验    平期
    ${时间}    set variable    ${日期} 11:00:00
    设置系统时间    ${时间}
    退出错峰状态
    ${时间}    set variable    ${日期} 12:00:00
    设置系统时间    ${时间}
    错峰谷期校验
    ${时间}    set variable    ${日期} 12:59:59
    设置系统时间    ${时间}
    sleep    15
    退出错峰状态
    [Teardown]    错峰用电不同时段不同类型测试后置条件

充电系数为0，错峰用电测试(错峰维持)
    ${日期}    错峰用电不同时段不同类型测试初始化  0
    ${时间}    set variable    ${日期} 05:59:59
    设置系统时间    ${时间}
    错峰放电校验    尖峰
    ${错峰退出电压}    获取错峰退出电压
    向下调节电池电压    ${错峰退出电压}
    错峰维持校验    尖峰
    ${时间}    set variable    ${日期} 08:00:00
    设置系统时间    ${时间}
    错峰维持校验    高峰
    ${时间}    set variable    ${日期} 10:00:00
    设置系统时间    ${时间}
    错峰维持校验    平期
    ${时间}    set variable    ${日期} 12:00:00
    设置系统时间    ${时间}
    错峰谷期校验
    ${时间}    set variable    ${日期} 12:59:59
    设置系统时间    ${时间}
    sleep    15
    退出错峰状态
    [Teardown]    错峰用电不同时段不同类型测试后置条件

充电系数不为0，错峰用电测试(错峰充电)
    ${日期}    错峰用电不同时段不同类型测试初始化  0.1
    ${时间}    set variable    ${日期} 05:59:59
    设置系统时间    ${时间}
    错峰放电校验    尖峰
    ${错峰退出电压}    获取错峰退出电压
    向下调节电池电压    ${错峰退出电压}
    ${直流电压}    获取web实时数据    直流电压
    错峰充电    尖峰
    ${时间}    set variable    ${日期} 08:00:00
    设置系统时间    ${时间}
    错峰充电    高峰
    ${时间}    set variable    ${日期} 10:00:00
    设置系统时间    ${时间}
    错峰充电    平期
    ${时间}    set variable    ${日期} 12:00:00
    设置系统时间    ${时间}
    错峰谷期校验
    ${时间}    set variable    ${日期} 12:59:50
    设置系统时间    ${时间}
    sleep    15
    退出错峰状态
    [Teardown]    错峰用电不同时段不同类型测试后置条件

充电系数不为0，错峰用电测试(错峰充电切换)
    ${日期}    错峰用电不同时段不同类型测试初始化  0.1
    ${时间}    set variable    ${日期} 05:59:59
    设置系统时间    ${时间}
    错峰放电校验    尖峰
    ${错峰启动电压}    ${错峰退出电压}    获取电池调节电压
    向下调节电池电压    ${错峰退出电压}
    错峰充电    尖峰
    ${时间}    set variable    ${日期} 07:00:00
    设置系统时间    ${时间}
    退出错峰状态
    ${时间}    set variable    ${日期} 08:00:00
    设置系统时间    ${时间}
    非错峰状态校验    高峰
    向上调节电池电压    ${错峰启动电压}
    错峰放电校验    高峰
    向下调节电池电压    ${错峰退出电压}
    错峰充电    高峰
    ${时间}    set variable    ${日期} 09:00:00
    设置系统时间    ${时间}
    退出错峰状态
    ${时间}    set variable    ${日期} 10:00:00
    设置系统时间    ${时间}
    非错峰状态校验    平期
    向上调节电池电压    ${错峰启动电压}
    错峰放电校验    平期
    向下调节电池电压    ${错峰退出电压}
    错峰充电    平期
    ${时间}    set variable    ${日期} 11:00:00
    设置系统时间    ${时间}
    退出错峰状态
    ${时间}    set variable    ${日期} 12:00:00
    设置系统时间    ${时间}
    错峰谷期校验
    ${时间}    set variable    ${日期} 12:59:59
    设置系统时间    ${时间}
    sleep    15
    退出错峰状态
    [Teardown]    错峰用电不同时段不同类型测试后置条件

充电系数为0，错峰用电测试(错峰维持切换)
    ${日期}    错峰用电不同时段不同类型测试初始化  0
    ${时间}    set variable    ${日期} 05:59:59
    设置系统时间    ${时间}
    错峰放电校验    尖峰
    ${错峰启动电压}    ${错峰退出电压}    获取电池调节电压
    向下调节电池电压    ${错峰退出电压}
    错峰维持校验    尖峰
    ${时间}    set variable    ${日期} 07:00:00
    设置系统时间    ${时间}
    退出错峰状态
    ${时间}    set variable    ${日期} 08:00:00
    设置系统时间    ${时间}
    非错峰状态校验    高峰
    向上调节电池电压    ${错峰启动电压}
    错峰放电校验    高峰
    向下调节电池电压  ${错峰退出电压}
    错峰维持校验    高峰
    ${时间}    set variable    ${日期} 09:00:00
    设置系统时间    ${时间}
    退出错峰状态
    ${时间}    set variable    ${日期} 10:00:00
    设置系统时间    ${时间}
    非错峰状态校验    平期
    向上调节电池电压    ${错峰启动电压}
    错峰放电校验    平期
    向下调节电池电压  ${错峰退出电压}
    错峰维持校验    平期
    ${时间}    set variable    ${日期} 11:00:00
    设置系统时间    ${时间}
    退出错峰状态
    ${时间}    set variable    ${日期} 12:00:00
    设置系统时间    ${时间}
    错峰谷期校验
    ${时间}    set variable    ${日期} 12:59:59
    设置系统时间    ${时间}
    sleep    15
    退出错峰状态
    [Teardown]    错峰用电不同时段不同类型测试后置条件
