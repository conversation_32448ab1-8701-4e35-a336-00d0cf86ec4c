*** Settings ***
Resource          ../../../../../../../测试用例关键字.robot

*** Test Cases ***
浮充时不进行温补
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    ${当前数据值}    Wait Until Keyword Succeeds    10    2    获取web实时数据    直流电压
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
    设置web参数量    电池组容量_${i}    0
    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    交流供电状态    市电1
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    过渡
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    浮充
    Wait Until Keyword Succeeds    10    1    设置web参数量    温度补偿模式    允许
    sleep    5
    ${温补下限参数范围}    获取web参数上下限范围    温度补偿电压下限
    设置web参数量    温度补偿电压下限    @{温补下限参数范围}[0]
    ${温补上限参数范围}    获取web参数上下限范围    温度补偿电压上限
    设置web参数量    温度补偿电压上限    @{温补上限参数范围}[0]
    ${参数范围}    获取web参数上下限范围    电池电压温度补偿系数
    设置web参数量    电池电压温度补偿系数    @{参数范围}[0]
    ${温补系数设置值}    获取web参数量    电池电压温度补偿系数
    ${温度基准}    获取web参数上下限范围    电池温度补偿基准
    设置web参数量    电池温度补偿基准    @{温度基准}[0]
    ${温度高阈值}    获取web参数上下限范围    电池温度高阈值
    设置web参数量    电池温度高阈值    @{温度高阈值}[2]
    Wait Until Keyword Succeeds    10    1    设置web参数量    温度补偿模式    禁止
    设置web参数量    浮充电压    53.5
    设置web参数量    均充电压    54.5
    ${电压设置值}    获取web参数量    均充电压
    sleep    2m
    ${电压获取值}    获取web实时数据    直流电压
    #设置电池温度为45
    Wait Until Keyword Succeeds    30    1    设置通道配置    UIB_X5_T2    5    1.6    电池_2    电池温度
    sleep    3m
    ${电压获取值new}    获取web实时数据    直流电压
    ${差值}    evaluate    ${电压获取值new}-${电压获取值}
    should be true    -0.2<${差值}<0.2
    #设置电池温度为0
    Wait Until Keyword Succeeds    30    1    设置通道配置    UIB_X5_T2    0    0.001    电池_2    电池温度
    sleep    3m
    ${电压获取值new1}    获取web实时数据    直流电压
    ${差值1}    evaluate    ${电压获取值new1}-${电压获取值}
    should be true    -0.2<${差值1}<0.2
    [Teardown]    Run keywords    设置web参数量    电池组容量_1    100
    ...    AND    设置web参数量    电池组容量_2    0
    ...    AND    设置web设备参数量为默认值    均充电压    电池温度高阈值
    ...    AND    设置web参数量    温度补偿模式    禁止
    ...    AND    Wait Until Keyword Succeeds    30    1    设置通道配置    UIB_X5_T2    0    1    电池_2    电池温度

均充时不进行温补
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    ${当前数据值}    Wait Until Keyword Succeeds    10    2    获取web实时数据    直流电压
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
    设置web参数量    电池组容量_${i}    0
    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    交流供电状态    市电1
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    过渡
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动均充
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    均充
    Wait Until Keyword Succeeds    10    1    设置web参数量    温度补偿模式    允许
    sleep    5
    ${温补下限参数范围}    获取web参数上下限范围    温度补偿电压下限
    设置web参数量    温度补偿电压下限    @{温补下限参数范围}[0]
    ${温补上限参数范围}    获取web参数上下限范围    温度补偿电压上限
    设置web参数量    温度补偿电压上限    @{温补上限参数范围}[0]
    ${参数范围}    获取web参数上下限范围    电池电压温度补偿系数
    设置web参数量    电池电压温度补偿系数    @{参数范围}[0]
    ${温补系数设置值}    获取web参数量    电池电压温度补偿系数
    ${温度基准}    获取web参数上下限范围    电池温度补偿基准
    设置web参数量    电池温度补偿基准    @{温度基准}[0]
    ${温度高阈值}    获取web参数上下限范围    电池温度高阈值
    设置web参数量    电池温度高阈值    @{温度高阈值}[2]
    Wait Until Keyword Succeeds    10    1    设置web参数量    温度补偿模式    禁止
    设置web参数量    浮充电压    53.5
    设置web参数量    均充电压    54.5
    ${电压设置值}    获取web参数量    均充电压
    sleep    2m
    ${电压获取值}    获取web实时数据    直流电压
    #均充模式温补
    #设置电池温度为45
    Wait Until Keyword Succeeds    30    1    设置通道配置    UIB_X5_T2    5    1.6    电池_2    电池温度
    sleep    3m
    ${电压获取值new}    获取web实时数据    直流电压
    ${差值}    evaluate    ${电压获取值new}-${电压获取值}
    should be true    -0.2<${差值}<0.2
    #设置电池温度为0
    Wait Until Keyword Succeeds    30    1    设置通道配置    UIB_X5_T2    0    0.001    电池_2    电池温度
    sleep    3m
    ${电压获取值new1}    获取web实时数据    直流电压
    ${差值1}    evaluate    ${电压获取值new1}-${电压获取值}
    should be true    -0.2<${差值1}<0.2
    [Teardown]    Run keywords    设置web设备参数量为默认值    均充电压    电池温度高阈值
    ...    AND    设置web参数量    温度补偿模式    禁止
    ...    AND    Wait Until Keyword Succeeds    30    1    设置通道配置    UIB_X5_T2    0    1    电池_2    电池温度
