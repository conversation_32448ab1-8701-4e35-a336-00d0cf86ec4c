*** Settings ***
Suite Setup       主动告警测试前置条件    ${CSU_role}
Suite Teardown    设置web设备参数量为默认值    CSU主动告警使能
Test Setup
Test Teardown
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0002_禁止所有告警
    [Documentation]    这个告警没有 屏蔽 这一级别
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    实时告警刷新完成
    wait until keyword succeeds    1m    1    判断告警不存在    禁止所有告警
    Wait Until Keyword Succeeds    10    2    设置web控制量    <<禁止所有告警~0x1001040010001>>
    Wait Until Keyword Succeeds    2m    1    查询指定告警信息    禁止所有告警
    ${告警级别取值约定dict}    获取web参数的取值约定    <<禁止所有告警~0x1001030010001>>
    ${web时间1}    获取系统时间
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    <<禁止所有告警~0x1001030010001>>    ${告警级别设置}
        sleep    5
        wait until keyword succeeds    1m    1    查询指定告警信息    禁止所有告警
        Run Keyword And Continue On Failure    run keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    CSU    禁止所有告警
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    禁止所有告警    ${SSH}
        should be true    ${power_sm告警存在}[0]
        Comment    将数字型告警级别转换为中文
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    Wait Until Keyword Succeeds    30    1    设置web控制量    <<允许所有告警~0x1001040020001>>
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    禁止所有告警
    ${power_Sm告警存在}    判断power_sm是否存在单个实时告警    禁止所有告警    ${SSH}
    should not be true    ${power_Sm告警存在}[0]
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    30    1    设置web控制量    <<允许所有告警~0x1001040020001>>
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0005_输入干接点告警
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    ${告警级别取值约定dict}    获取web参数的取值约定    输入干接点告警
    FOR    ${输入干接点序号}    IN RANGE    1    9
        ${级别设置值}    获取web参数量    输入干接点告警_${输入干接点序号}
        run keyword if    '${级别设置值}'=='屏蔽'    设置web设备参数量为默认值    输入干接点告警_${输入干接点序号}
        Comment    设置${plat.Inrelay2Status}，对应的CSU:输入干接点告警[2]
        wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay2Status}    CSU    输入干接点告警_${输入干接点序号}    断开
        wait until keyword succeeds    1m    1    查询指定告警信息    输入干接点告警_${输入干接点序号}
        ${级别设置值}    获取web参数量    输入干接点告警_${输入干接点序号}
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    输入干接点告警_${输入干接点序号}    ${SSH}
        should be true    ${power_sm告警存在}[0]
        Comment    将数字型告警级别转换为中文
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${级别设置值}
        wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay2Status}    无    无    闭合
        Wait Until Keyword Succeeds    5m    1    判断告警不存在    输入干接点告警_${输入干接点序号}
        ${power_Sm告警存在}    判断power_sm是否存在单个实时告警    输入干接点告警_${输入干接点序号}    ${SSH}
        should not be true    ${power_Sm告警存在}[0]
    END
    [Teardown]    Run keywords    power_sm_输入干接点告警级别恢复
    ...    AND    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay2Status}    无    无    闭合
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0003_CPU利用率高告警
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    显示属性配置    CPU利用率    模拟量    web_attr=On    gui_attr=On
    ${级别设置值}    获取web参数量    CPU利用率高告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web设备参数量为默认值    CPU利用率高告警
    ${可设置范围}    获取web参数可设置范围    CPU利用率高阈值
    设置web参数量    CPU利用率高阈值    ${可设置范围}[1]
    ${获取值}    获取web实时数据    CPU利用率
    ${设置值}    获取web参数量    CPU利用率高阈值
    should be true    ${获取值}<=${设置值}
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    CPU利用率高告警
    ${告警不存在}    判断告警存在_带返回值    CPU利用率高告警
    should not be true    ${告警不存在}
    设置web参数量    CPU利用率高阈值    ${可设置范围}[0]
    ${获取值1}    获取web实时数据    CPU利用率
    ${设置值1}    获取web参数量    CPU利用率高阈值
    should be true    ${获取值1}> ${设置值1}
    Comment    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    CPU利用率高告警
    Comment    ${告警存在}    判断告警存在_带返回值    CPU利用率高告警
    Comment    should be true    ${告警存在}
    ${告警级别取值约定dict}    获取web参数的取值约定    CPU利用率高告警
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        设置web参数量    CPU利用率高告警    ${告警级别设置}
        sleep   5
        Wait Until Keyword Succeeds    3m    1    查询指定告警信息    CPU利用率高告警
        Run Keyword And Continue On Failure    run keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    CSU    CPU利用率高告警
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    CPU利用率高告警    ${SSH}
        should be true    ${power_sm告警存在}[0]
        Comment    将数字型告警级别转换为中文
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    设置web设备参数量为默认值    CPU利用率高告警
    Comment    Wait Until Keyword Succeeds    2m    1    判断告警不存在    CPU利用率高告警
    Comment    ${power_Sm告警存在}    判断power_sm是否存在单个实时告警    内存利用率高告警    ${SSH}
    Comment    should not be true    ${power_Sm告警存在}[0]
    显示属性配置    CPU利用率    模拟量    web_attr=Off    gui_attr=Off
    [Teardown]    Run keywords    设置web参数量    CPU利用率高告警    屏蔽
    ...    AND    设置web设备参数量为默认值    CPU利用率高阈值
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0004_内存利用率高告警
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    显示属性配置    内存利用率    模拟量    web_attr=On    gui_attr=On
    ${级别设置值}    获取web参数量    内存利用率高告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web设备参数量为默认值    内存利用率高告警
    ${可设置范围}    获取web参数可设置范围    内存利用率高阈值
    设置web参数量    内存利用率高阈值    ${可设置范围}[1]
    ${获取值}    获取web实时数据    内存利用率
    ${设置值}    获取web参数量    内存利用率高阈值
    should be true    ${获取值}<=${设置值}
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    内存利用率高告警
    ${告警不存在}    判断告警存在_带返回值    内存利用率高告警
    should not be true    ${告警不存在}
    设置web参数量    内存利用率高阈值    ${可设置范围}[0]
    ${获取值1}    获取web实时数据    内存利用率
    ${设置值1}    获取web参数量    内存利用率高阈值
    should be true    ${获取值1}>${设置值1}
    Comment    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    内存利用率高告警
    Comment    ${告警存在}    判断告警存在_带返回值    内存利用率高告警
    Comment    should be true    ${告警存在}
    ${告警级别取值约定dict}    获取web参数的取值约定    内存利用率高告警
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        设置web参数量    内存利用率高告警    ${告警级别设置}
        sleep   5
        wait until keyword succeeds    3m    1    查询指定告警信息    内存利用率高告警
        Run Keyword And Continue On Failure    run keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    CSU    内存利用率高告警
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    内存利用率高告警    ${SSH}
        should be true    ${power_sm告警存在}[0]
        Comment    将数字型告警级别转换为中文
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    设置web参数量    内存利用率高告警    次要
    设置web设备参数量为默认值    内存利用率高阈值
    Comment    Wait Until Keyword Succeeds    2m    1    判断告警不存在    内存利用率高告警
    Comment    ${power_Sm告警存在}    判断power_sm是否存在单个实时告警    内存利用率高告警    ${SSH}
    Comment    should not be true    ${power_Sm告警存在}[0]
    显示属性配置    内存利用率    模拟量    web_attr=Off    gui_attr=Off
    [Teardown]    Run keywords    设置web参数量    内存利用率高告警    屏蔽
    ...    AND    设置web设备参数量为默认值    内存利用率高阈值
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0005_UIB通讯断
    [Tags]    T1-1
    [Setup]    run keywords    模拟数字量告警    UIB板通讯断    OFF
    ...    AND    sleep    10
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    实时告警刷新完成
    wait until keyword succeeds    1m    1    判断告警不存在    UIB通讯断
    ${告警级别取值约定dict}    获取web参数的取值约定    UIB通讯断
    模拟数字量告警    UIB板通讯断    ON
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    UIB通讯断    ${告警级别设置}
        sleep   5
        wait until keyword succeeds    2m    1    查询指定告警信息    UIB通讯断
        Run Keyword And Continue On Failure    run keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    CSU    UIB通讯断
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    UIB通讯断    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    模拟数字量告警    UIB板通讯断    OFF
    sleep    10
    wait until keyword succeeds    5m    1    判断告警不存在    UIB通讯断
    ${power_Sm告警存在}    判断power_sm是否存在单个实时告警    UIB通讯断    ${SSH}
    should not be true    ${power_Sm告警存在}[0]
    [Teardown]    run keywords    设置web设备参数量为默认值    UIB通讯断
    ...    AND    模拟数字量告警    UIB板通讯断    OFF
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0005_IDDB通讯断
    [Tags]    T1-1
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    实时告警刷新完成
    wait until keyword succeeds    1m    1    判断告警不存在    IDDB通讯断
    ${告警级别取值约定dict}    获取web参数的取值约定    IDDB通讯断
    模拟数字量告警    IDDB板通讯断    ON
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    IDDB通讯断    ${告警级别设置}
        sleep   5
        wait until keyword succeeds    2m    1    查询指定告警信息    IDDB通讯断
        Run Keyword And Continue On Failure    run keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    CSU    IDDB通讯断
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    IDDB通讯断    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    模拟数字量告警    IDDB板通讯断    OFF
    sleep    10
    wait until keyword succeeds    5m    1    判断告警不存在    IDDB通讯断
    ${power_Sm告警存在}    判断power_sm是否存在单个实时告警    IDDB通讯断    ${SSH}
    should not be true    ${power_Sm告警存在}[0]
    [Teardown]    run keywords    设置web设备参数量为默认值    IDDB通讯断
    ...    AND    模拟数字量告警    IDDB板通讯断    OFF
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
