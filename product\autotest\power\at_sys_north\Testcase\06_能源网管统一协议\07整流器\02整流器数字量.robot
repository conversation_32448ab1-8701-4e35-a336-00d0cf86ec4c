*** Settings ***
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_批量获取整流器数字量
    [Setup]    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    自由
    写入CSV文档    整流器数字量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=digital data    INDEX_DEVICE_CH=整流器
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除数字量信号}    ${排除列表}    1    ${模拟整流器地址}
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    整流器    digital data    ${整流器排除数字量信号}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    1    power_sm
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    power_sm    SMR      数字量    ${缺省值列表}    整流器数字量获取测试    获取整流器数字量   
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    2    power_sm
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    power_sm    SMR     数字量    ${缺省值列表}    整流器数字量获取测试    获取整流器数字量    
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    0    power_sm
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    power_sm    SMR       数字量    ${缺省值列表}   整流器数字量获取测试    获取整流器数字量   
    [Teardown]    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全









    
    
