*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
电池测试管理测试（常规锂电池）
    [Setup]    #测试用例前置条件
    Comment    电池管理初始化
    连接CSU
    设置web参数量    电池配置    常规锂电池
    设置web参数量    电池应用场景    备电场景
    sleep    3
    设置web参数量    电池周期测试使能    允许
    sleep    3
    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池测试周期    1
    sleep    3
    ${电池测试周期}    获取web参数量    电池测试周期
    should be equal as numbers    ${电池测试周期}    1
    ${电池测试启动时刻}    获取web参数量    电池测试启动时刻
    ${下次测试时间}    获取web实时数据    下次测试时间
    ${设置时间}    Subtract Time From Date    ${下次测试时间}    5s    #比10:00:00差5s
    设置系统时间    ${设置时间}
    sleep    8
    ${修改时间后的系统时间}    获取系统时间
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    测试
    ${下次测试时间_cal}    add time to date    ${下次测试时间}    ${电池测试周期}d    exclude_millis=yes
    sleep    5
    ${下次测试时间_new}    获取web实时数据    下次测试时间
    should be equal    ${下次测试时间_new}    ${下次测试时间_cal}
    设置web控制量    启动充电
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    充电
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池测试周期    电池周期测试使能    电池配置

电池测试周期为0（常规锂电池）
    [Setup]    #测试用例前置条件
    Comment    电池管理初始化
    连接CSU
    设置web参数量    电池配置    常规锂电池
    设置web参数量    电池应用场景    备电场景
    设置web控制量    启动充电
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    充电
    设置web参数量    电池周期测试使能    允许
    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池测试周期    60
    sleep    5
    ${电池测试周期}    获取web参数量    电池测试周期
    ${下次测试时间}    获取web实时数据    下次测试时间
    ${下次测试时间0}    Subtract Time From Date    ${下次测试时间}    5s    #比10:00:00差5s
    设置web参数量    电池测试周期    0
    sleep    5
    ${下次测试时间_1}    获取web实时数据    下次测试时间
    Should Be Equal As Strings    ${下次测试时间_1}    ${EMPTY}
    设置系统时间    ${下次测试时间0}
    sleep    1m
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    充电
    ${系统时间}    Subtract Time From Date    ${下次测试时间0}    7 days
    设置系统时间    ${系统时间}
    sleep    5
    设置web参数量    电池测试周期    60    #默认0天
    sleep    5
    ${下次测试时间_new}    获取web实时数据    下次测试时间
    should be equal    ${下次测试时间_new}    ${下次测试时间}
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池测试周期    电池周期测试使能    电池配置

电池周期测试使能有效性测试（循环场景）
    [Setup]    #测试用例前置条件
    Comment    电池管理初始化
    连接CSU
    设置web参数量    电池配置    常规锂电池
    设置web参数量    电池应用场景    循环场景
    sleep    3
    ${电池周期测试使能}    获取web参数量    电池周期测试使能
    ${测试使能}    Convert To String    ${电池周期测试使能}
    Should Be Equal    ${测试使能}    None
    ${电池测试周期}    获取web参数量    电池测试周期
    Should Be Equal As Strings    ${电池测试周期}    ${EMPTY}
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池配置

电池周期测试使能有效性测试（禁止）
    [Setup]    #测试用例前置条件
    Comment    电池管理初始化
    连接CSU
    设置web参数量    电池配置    常规锂电池
    设置web参数量    电池应用场景    备电场景
    设置web控制量    启动充电
    sleep    3
    设置web参数量    电池周期测试使能    禁止
    ${电池测试周期}    获取web参数量    电池测试周期
    Should Be Equal As Strings    ${电池测试周期}    ${EMPTY}
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池周期测试使能    电池配置

电池测试周期设置与保存测试（无效值）
    [Setup]    #测试用例前置条件
    Comment    电池管理初始化
    连接CSU
    设置web参数量    电池配置    常规锂电池
    设置web参数量    电池应用场景    备电场景
    sleep    3
    设置web参数量    电池周期测试使能    允许
    Comment    设置web参数量    电池测试周期    0.1
    ${电池周期测试范围}    获取web参数可设置范围    电池测试周期
    ${无效值}    Create List    -0.1
    ${无效值_0}    evaluate    ${电池周期测试范围}[0]-1
    ${无效值_1}    evaluate    ${电池周期测试范围}[1]+1
    ${无效值_2}    evaluate    ${电池周期测试范围}[0]+0.1
    ${无效值_3}    evaluate    ${电池周期测试范围}[1]-0.1
    Append To List    ${无效值}    ${无效值_0}    ${无效值_1}    ${无效值_2}    ${无效值_3}    aaa
    FOR    ${ver}    IN    @{无效值}
        ${返回值}    设置web参数量_带返回值    电池测试周期    ${ver}
        should not be true    ${返回值}
    END
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池测试周期    电池周期测试使能    电池配置

电池测试周期设置与保存测试（有效值）
    [Setup]    #测试用例前置条件
    Comment    电池管理初始化
    连接CSU
    设置web参数量    电池配置    常规锂电池
    设置web参数量    电池应用场景    备电场景
    sleep    3
    设置web参数量    电池周期测试使能    允许
    ${电池周期测试范围}    获取web参数可设置范围    电池测试周期
    Append To List    ${电池周期测试范围}    90
    FOR    ${val}    IN    @{电池周期测试范围}
        设置web参数量    电池测试周期    ${val}
        sleep    2
        ${电池测试周期}    获取web参数量    电池测试周期
        should be equal as numbers    ${电池测试周期}    ${val}
    END
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池测试周期    电池周期测试使能    电池配置
