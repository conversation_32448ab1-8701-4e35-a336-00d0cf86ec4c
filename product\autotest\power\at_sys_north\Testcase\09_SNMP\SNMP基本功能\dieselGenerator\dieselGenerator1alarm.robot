*** Settings ***
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp_test关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/Web关键字_V30.robot
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
snmp_0002_dieselGenerator1alarm_level√
    ${比较结果}    对比告警级别_V2C    dieselGenerator1alarm
    should be true    ${比较结果}

snmp_0004_dieselGenerator1alarm_level_write√
    Comment    ${比较结果}    批量修改告警级别_V2C    cSU1alarm
    Comment    should be true    ${比较结果}
    ${比较结果}    批量设置四种/五种告警级别    dieselGenerator1alarm
    should be true    ${比较结果}

snmp_006_dieselGenerator1alarm_relay
    ${比较结果}    对比告警干接点_V2C    dieselGenerator1alarm
    should be true    ${比较结果}

snmp_0008_dieselGenerator1alarm_relay_write
    ${比较结果}    批量修改告警干接点_V2C    dieselGenerator1alarm
    should be true    ${比较结果}

# snmp_批量获取油机数字量
#     [Documentation]    21min
#     写入CSV文档    油机数字量获取测试    信号名称    信号值    结果    备注
#     连接CSU
#     进行SNMP_V2/V3连接    ${snmp连接方式}
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=digital data    INDEX_DEVICE_CH=油机
#     ${排除列表}    create list
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    2
#     ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    gCP    digitalData
#     FOR    ${i}    IN    @{snmp待测}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${节点名称}    Get From Dictionary    ${i}    snmp_name
#         ${信号序号}    Get From Dictionary    ${i}    Vindex
#         ${缺省值}    获取web参数上下限范围    ${信号名称}
#         Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    snmp    oileng    ${信号名称}    数字量    ${缺省值}[1]    油机数字量获取测试    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    snmp    oileng    ${信号名称}    数字量    ${缺省值}[2]    油机数字量获取测试    null    ${节点名称}    ${信号序号}
#         Run keyword IF    ${缺省值}[1]!=${缺省值}[0] and ${缺省值}[2]!=${缺省值}[0]    Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    snmp    oileng    ${信号名称}    数字量    ${缺省值}[0]    油机数字量获取测试    null    ${节点名称}    ${信号序号}
#     END
#     断开连接SNMP

snmp_批量获取油机数字量
    [Documentation]    21min
    写入CSV文档    油机数字量获取测试    信号名称    信号值    结果    备注
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=digital data    INDEX_DEVICE_CH=油机
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    2
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    gCP    digitalData
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    1    snmp
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    snmp    oileng    数字量    ${缺省值列表}    油机数字量获取测试    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    2    snmp
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    snmp    oileng    数字量    ${缺省值列表}    油机数字量获取测试    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    0    snmp
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    snmp    oileng    数字量    ${缺省值列表}    油机数字量获取测试    null
    断开连接SNMP
