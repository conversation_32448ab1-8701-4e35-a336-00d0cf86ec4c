*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
1363_0002_V3.0_1363_通用命令_获取设备地址_1
    [Tags]    1
    ${1363版本}    通用命令_获取设备地址    ${g_prtcl_1363}    <<获取设备地址~CID1=40H>>    ${g_ver_1363}
    ${对比结果}    对比设备地址信息    ${1363版本}    ${g_addr}    1363设备地址_1
    should be true    ${对比结果}

1363_0004_V3.0_1363_通用命令_获取设备地址_2
    [Tags]    1
    ${1363版本}    通用命令_获取设备地址    ${g_prtcl_1363}    <<获取设备地址~CID1=41H>>    ${g_ver_1363}
    ${对比结果}    对比设备地址信息    ${1363版本}    ${g_addr}    1363设备地址_2
    should be true    ${对比结果}

1363_0006_V3.0_1363_通用命令_获取设备地址_3
    [Tags]    1
    ${1363版本}    通用命令_获取设备地址    ${g_prtcl_1363}    <<获取设备地址~CID1=42H>>    ${g_ver_1363}
    ${对比结果}    对比设备地址信息    ${1363版本}    ${g_addr}    1363设备地址_3
    should be true    ${对比结果}

1363_0008_V3.0_1363_通用命令_获取设备地址_4
    [Documentation]    CID1=91H为环境模块
    [Tags]    1
    [Setup]
    ${1363版本}    通用命令_获取设备地址    ${g_prtcl_1363}    <<获取设备地址~CID1=91H>>    ${g_ver_1363}
    ${对比结果}    对比设备地址信息    ${1363版本}    ${g_addr}    1363设备地址_4
    should be true    ${对比结果}
    [Teardown]

1363_0010_V3.0_1363_通用命令_获取设备地址_5
    [Documentation]    CID1=D0H为PU模块
    [Tags]    1
    [Setup]    #run keywords    控制子工具运行停止    pu    开启
    ...    # AND    sleep    3m
    ${1363版本}    通用命令_获取设备地址    ${g_prtcl_1363}    <<获取设备地址~CID1=D0H>>    ${g_ver_1363}
    ${对比结果}    对比设备地址信息    ${1363版本}    ${g_addr}    1363设备地址_5
    should be true    ${对比结果}
    [Teardown]    #run keywords    控制子工具运行停止    pu    关闭
    ...    # AND    Wait Until Keyword Succeeds    10    2    设置web控制量    CAN总线设备统计

1363_0012_V3.0_1363_通用命令_获取设备地址_6
    [Documentation]    VRLA段
    [Tags]    1
    [Setup]    #run keywords    连接CSU
    ...    # AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置
    ...    # 纯铅酸
    ...    # AND    sleep    1m
    ${1363版本}    通用命令_获取设备地址    ${g_prtcl_1363}    <<获取设备地址~CID1=46H>>    ${g_ver_1363}
    ${对比结果}    对比设备地址信息    ${1363版本}    ${g_addr}    1363设备地址_6
    should be true    ${对比结果}
