*** Settings ***
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_批量获取交流电表模拟量
    [Documentation]    21min
    写入CSV文档    交流电表模拟量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=交流电表
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${AEMB排除模拟量信号}    ${排除列表}    1
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    交流电表    analog data    ${AEMB排除模拟量信号}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    ${待测数据长度}    Get Length    ${power_sm待测}
    ${number}    Set Variable    1
    log    ${power_sm待测}
    sleep    1m
    FOR    ${i}    IN    @{power_sm待测}
        Log    正在验证第${number}个数据，一共${待测数据长度}个数据
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${交流电表设备}    split string    ${信号名称}    ~
        ${交流电表设备1}    split string    ${交流电表设备}[0]    -
        Run keyword if    ${交流电表设备1}[1]!=1    continue for loop
        ${缺省值}    获取web参数上下限范围    ${信号名称}
        Run Keyword And Continue On Failure    南向子设备模拟量获取值封装判断结果    power_sm    DMU_aemb    ${信号名称}    只读    ${缺省值}[1]    交流电表模拟量获取测试    获取交流电表模拟量    null    null
        Run Keyword And Continue On Failure    南向子设备模拟量获取值封装判断结果    power_sm    DMU_aemb    ${信号名称}    只读    ${缺省值}[2]    交流电表模拟量获取测试    获取交流电表模拟量    null    null
        Run Keyword And Continue On Failure    南向子设备模拟量获取值封装判断结果    power_sm    DMU_aemb    ${信号名称}    只读    ${缺省值}[0]    交流电表模拟量获取测试    获取交流电表模拟量    null    null
        ${number}    Evaluate    ${number}+1
    END

power_sm_0002_交流电表回路1总有功功率3
    连接CSU
    设置子工具值    DMU_aemb    all    只读    输入总有功功率_P    600000
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表回路1总有功功率-1    600
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取交流电表模拟量    交流电表回路1总有功功率-1    ${SSH}
    should be equal as numbers    ${power_sm获取值1}    600
    设置子工具值    DMU_aemb    all    只读    输入总有功功率_P    220000
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表回路1总有功功率-1    220
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取交流电表模拟量    交流电表回路1总有功功率-1    ${SSH}
    should be equal as numbers    ${power_sm获取值1}    220
    #################
    设置子工具值    DMU_aemb    all    只读    输入总有功功率_P    0
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表回路1总有功功率-1    0
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取交流电表模拟量    交流电表回路1总有功功率-1    ${SSH}
    should be equal as numbers    ${power_sm获取值1}    0

power_sm_0003_交流电表回路1电量7
    [Documentation]    SnmpKeyword.py 1257 snmp_value= 32767.0016 web_value= 32767.00
    连接CSU
    设置子工具值    DMU_aemb    all    只读    输入总有功电能    600
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表回路1电量-1    600
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取交流电表模拟量    交流电表回路1电量-1    ${SSH}
    should be equal as numbers    ${power_sm获取值1}    600
    设置子工具值    DMU_aemb    all    只读    输入总有功电能    0
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表回路1电量-1    0
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取交流电表模拟量    交流电表回路1电量-1    ${SSH}
    should be equal as numbers    ${power_sm获取值1}    0
