*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
# snmp批量获取PU数字量
#     写入CSV文档    PU数字量获取测试    信号名称    信号值    结果    备注
#     连接CSU
#     进行SNMP_V2/V3连接    ${snmp连接方式}
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=digital data    INDEX_DEVICE_CH=太阳能模块
#     ${排除列表}    create list
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除数字量信号}    ${排除列表}    2    1
#     ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    powerUnit    digitalData
#     FOR    ${i}    IN    @{snmp待测}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${节点名称}    Get From Dictionary    ${i}    snmp_name
#         ${信号序号}    Get From Dictionary    ${i}    Vindex
#         ${缺省值}    获取web参数上下限范围    ${信号名称}
#         Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    snmp    pu    ${信号名称}    数字量    ${缺省值}[1]    PU数字量获取测试    null    ${节点名称}    ${信号序号}    null    null    null    null
#         Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    snmp    pu    ${信号名称}    数字量    ${缺省值}[2]    PU数字量获取测试    null    ${节点名称}    ${信号序号}    null    null    null    null
#         Run keyword IF    ${缺省值}[1]!=${缺省值}[0] and ${缺省值}[2]!=${缺省值}[0]    Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    snmp    pu    ${信号名称}    数字量    ${缺省值}[0]    PU数字量获取测试    null    ${节点名称}    ${信号序号}    null    null    null
#         ...    null
#     END
#     断开连接SNMP

snmp批量获取PU数字量
    写入CSV文档    PU数字量获取测试    信号名称    信号值    结果    备注
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=digital data    INDEX_DEVICE_CH=太阳能模块
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除数字量信号}    ${排除列表}    2    1
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    powerUnit    digitalData
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    1    snmp
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    snmp    pu    数字量    ${缺省值列表}    PU数字量获取测试    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    2    snmp
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    snmp    pu    数字量    ${缺省值列表}    PU数字量获取测试    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    0    snmp
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    snmp    pu    数字量    ${缺省值列表}    PU数字量获取测试    null
    断开连接SNMP

# snmp批量获取PU特殊数字量
#     写入CSV文档    PU特殊数字量获取测试    信号名称    信号值    结果
#     连接CSU
#     进行SNMP_V2/V3连接    ${snmp连接方式}
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=太阳能模块
#     ${排除列表}    create list
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除特殊数字量信号}    ${排除列表}    1    ${模拟PU起始地址}    1
#     ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    powerUnit    alarm
#     FOR    ${i}    IN    @{snmp待测}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${节点名称}    Get From Dictionary    ${i}    snmp_name
#         ${信号序号}    Get From Dictionary    ${i}    Vindex
#         Run Keyword And Continue On Failure    告警量置位状态量封装判断结果    snmp    pu    ${信号名称}    数字量    1    ${模拟PU起始地址}    PU特殊数字量获取测试    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    告警量置位状态量封装判断结果    snmp    pu    ${信号名称}    数字量    0    ${模拟PU起始地址}    PU特殊数字量获取测试    null    ${节点名称}    ${信号序号}
#     END
#     断开连接SNMP

snmp批量获取PU特殊数字量
    写入CSV文档    PU特殊数字量获取测试    信号名称    信号值    结果
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=太阳能模块
    ${排除列表}    create list
    @{信号名称列表}   create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除特殊数字量信号}    ${排除列表}    1    ${模拟PU起始地址}    1
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    powerUnit    alarm
    FOR    ${i}    IN    @{snmp待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    snmp_name
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
        Set To Dictionary    ${dict}     snmp_name     ${节点名称}
        Set To Dictionary    ${dict}     Vindex     ${信号序号}
		Append To List       ${信号名称列表}    ${dict}
    END
    Run Keyword And Continue On Failure    告警量置位状态量列表封装判断结果    snmp    pu    ${信号名称列表}    数字量    1    ${模拟PU起始地址}    PU特殊数字量获取测试    null
    Run Keyword And Continue On Failure    告警量置位状态量列表封装判断结果    snmp    pu    ${信号名称列表}    数字量    0    ${模拟PU起始地址}    PU特殊数字量获取测试    null
    断开连接SNMP


