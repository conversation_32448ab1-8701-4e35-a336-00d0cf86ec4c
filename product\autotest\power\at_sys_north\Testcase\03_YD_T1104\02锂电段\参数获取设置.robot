*** Settings ***
Suite Setup
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
1104_0004_V_3.0_1363_锂电段_参数量
    ${取值约定}    获取web参数的取值约定    电池配置
    ${val}    Get From Dictionary    ${取值约定}    2
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    ${val}
    run keyword and ignore error    Wait Until Keyword Succeeds    10    1    设置web参数量    智能锂电类型    FB100B3
    run keyword and ignore error    Wait Until Keyword Succeeds    10    1    设置web参数量    智能锂电类型    FB100B3电池
    ${协议数据}    1104获取数据    ${g_prtcl_1104}    <<获取锂电池段参数量~CID1=4AH>>    <<获取锂电池段参数量~CID2=47H>>    1    ${g_ver_1104}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${对比结果}    批量对比数据_1104_WEB    ${协议数据}    ${web数据}    1104_锂电段_参数量
    should be true    ${对比结果}

1104_0006_V_3.0_1363_锂电段_参数设置
    [Tags]    notest
    [Documentation]    单板过温告警阈值  参数屏蔽了，无法设置
    [Setup]    #run keywords    Wait Until Keyword Succeeds    30    1    设置web参数量    电池配置    # 纯锂电
    ${取值约定}    获取web参数的取值约定    电池配置
    ${val}    Get From Dictionary    ${取值约定}    2
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    ${val}
    run keyword and ignore error    Wait Until Keyword Succeeds    10    1    设置web参数量    智能锂电类型    FB100B3
    run keyword and ignore error    Wait Until Keyword Succeeds    10    1    设置web参数量    智能锂电类型    FB100B3电池
    ${1363数据}    1104批量参数设置测试    ${g_prtcl_1104}    <<设置锂电池段参数量~CID1=4AH>>    <<设置锂电池段参数量~CID2=49H>>    ${g_ver_1104}    1
    ${对比结果}    批量对比参数设置_1104    ${1363数据}    1104_锂电段_参数设置
    should be true    ${对比结果}
    [Teardown]    #run keywords    Wait Until Keyword Succeeds    30    1    设置web参数量    电池配置    # 纯铅酸
