*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_设置整流控制量
    [Documentation]    1.交流节能模式需为自由
    ...    2.系统时间必须与本地系统时间一致
    ...    3.与"power_sm_0004_整流组节能模式控制"用例一样
    [Tags]    notest
    Comment    能源网管协议_设置单个设备单个控制量    2    整流器唤醒    ${BOARD_IP}    ${SSH}
    Comment    ${sheet_name}    Set Variable    设置整流控制量
    Comment    ${能源网管数据}    能源网管协议_设置控制量    ${sheet_name}    ${BOARD_IP}    ${SSH}
    Comment    ${对比结果}    能源网管协议_批量对比参数设置    ${能源网管数据}    设置整流控制量
    Comment    能源网管协议_设置单个设备单个控制量    2    整流器唤醒    ${BOARD_IP}    ${SSH}
    Comment    能源网管协议_设置单个设备单个控制量    2    整流器风扇调速允许    ${BOARD_IP}    ${SSH}
    Comment    Should Be True    ${对比结果}
    连接CSU
    Wait Until Keyword Succeeds    10m    2    设置web参数量    交流节能模式    节能
    设置web参数量    整流器最小开机数量    1
    # FOR    ${真实整流器地址}    IN    @{实际工作整流器地址}
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-1    否    #为保证整流器数据获取正常
    能源网管协议_设置单个设备单个控制量    整流器组    2    暂时非节能控制    ${SSH}
    Wait Until Keyword Succeeds    30    2    信号量数据值为    交流节能状态    暂时非节能
    Comment    ${交流节能状态}    获取web实时数据    交流节能状态
    Comment    should be equal    ${交流节能状态}    暂时非节能
    能源网管协议_设置单个设备单个控制量    整流器组    2    人工维护检测    ${SSH}
    Wait Until Keyword Succeeds    30    2    信号量数据值为    交流节能状态    人工维护检测
    Comment    ${交流节能状态}    获取web实时数据    交流节能状态
    Comment    should be equal    ${交流节能状态}    人工维护检测
    能源网管协议_设置单个设备单个控制量    整流器组    2    永久非节能控制    ${SSH}
    Wait Until Keyword Succeeds    30    2    信号量数据值为    交流节能状态    永久非节能
    Comment    ${交流节能状态}    获取web实时数据    交流节能状态
    Comment    should be equal    ${交流节能状态}    永久非节能
    能源网管协议_设置单个设备单个控制量    整流器组    2    自动节能控制    ${SSH}
    Wait Until Keyword Succeeds    30    2    信号量数据值为    交流节能状态    自动非节能
    Comment    ${交流节能状态}    获取web实时数据    交流节能状态
    Comment    should be equal    ${交流节能状态}    自动非节能
    # END

power_sm_0002_获取整流器组参数量
    ${能源网管数据}    能源网管协议_获取数据    获取整流器组参数量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取整流器组参数量
    Should Be True    ${数据对比结果}
    ${校验结果}    能源网管协议_校验数据上送完整性    ${能源网管数据}    整流器组    parameter
    Comment   Should Be True    ${校验结果}

power_sm_0003_设置整流组参数量
    ${能源网管数据}    能源网管协议_设置单个参数    设置整流器组参数    ${BOARD_IP}    ${SSH}
    ${对比结果}    能源网管协议_批量对比参数设置    ${能源网管数据}    设置整流器组参数
    Should Be True    ${对比结果}

power_sm_0004_整流组节能模式控制
    [Documentation]    0:安全/Safe;1:自由/Free Mode;2:自动非节能/Auto NonSave;3:自动节能/Auto Save;4:暂时非节能/Temp NonSave;5:永久非节能/Perm NonSave;6:人工维护检测/Manual Detect;
    连接CSU
    Wait Until Keyword Succeeds    10m    2    设置web参数量    交流节能模式    节能
    设置web参数量    整流器最小开机数量    1
    # FOR    ${真实整流器地址}    IN    @{实际工作整流器地址}
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-1    否    #为保证整流器数据获取正常
    能源网管协议_设置单个设备单个控制量    整流器组    2    暂时非节能控制    ${SSH}
    Wait Until Keyword Succeeds    30    2    信号量数据值为    交流节能状态    暂时非节能
    能源网管协议_设置单个设备单个控制量    整流器组    2    人工维护检测    ${SSH}
    Wait Until Keyword Succeeds    30    2    信号量数据值为    交流节能状态    人工维护检测
    能源网管协议_设置单个设备单个控制量    整流器组    2    永久非节能控制    ${SSH}
    Wait Until Keyword Succeeds    30    2    信号量数据值为    交流节能状态    永久非节能
    能源网管协议_设置单个设备单个控制量    整流器组    2    自动节能控制    ${SSH}
    Wait Until Keyword Succeeds    40    2    信号量数据值为    交流节能状态    自动非节能
    # END
    [Teardown]    Run keywords    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3

power_sm_0005_整流组其他控制量
    [Documentation]    交流节能模式需为自由
    连接CSU
    Wait Until Keyword Succeeds    10m    2    设置web参数量    交流节能模式    节能
    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-1    否    #为保证整流器数据获取正常
    能源网管协议_设置单个设备单个控制量    整流器组    2    SMR设备统计    ${SSH}
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    在线整流器数量    48
    # FOR    ${真实整流器地址}    IN    @{实际工作整流器地址}
    #     Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-${真实整流器地址}    否    #为保证整流器数据获取正常
    #     能源网管协议_设置单个设备单个控制量    整流器组    2    SMR设备统计    ${SSH}
    # END
