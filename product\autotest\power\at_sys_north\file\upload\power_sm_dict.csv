设备对象,device,TD,TI,signal_type,full_name,short_name,chinese_full_name,chinese_short_name,unit,data_type,dimension,precision,default_value,min_value,max_value,step,convention,constraint
CSU,CSU,0x800201,0x080101,analog data,CPU Usage Rate,CPU Usage Rate,CPU利用率,CPU利用率,%,sint16,1,0,0,0,100,,,
CSU,CSU,0x800201,0x080201,analog data,Memory Usage Rate,Mem.Usage Rate,内存利用率,内存利用率,%,sint16,1,0,0,0,100,,,
CSU,CSU,0x800201,0x100201,digital data,Input Relay State,Input Rly.State,输入干接点状态,输入干接点状态,,sint8,8,0,0,0,1,,0:正常/Normal;1:异常/Fault;,
CSU,CSU,0x800201,0x100301,digital data,History Data Save Cause,HisData Sav.Cause,历史数据保存原因,历史数据保存原因,,sint8,1,0,0,0,4,,0:时间/Time;1:告警变化/Alarm Change;2:电池状态变化/Batt Stat Change;3:油机启停/Gen Start and Stop;4:日期变化/Date Change;,
CSU,CSU,0x800201,0x180101,alarm,All Alarm Blocked,All Alm.Blocked,禁止所有告警,禁止所有告警,,sint8,1,0,,,,,1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
CSU,CSU,0x800201,0x480101,alarm DO,All Alarm Blocked,All Alm.Blocked,禁止所有告警,禁止所有告警,,sint8,1,0,,,,,,
CSU,CSU,0x800201,0x180201,alarm,MAC Address Not Set,MAC Not Set,MAC地址未设置,MAC地址未设置,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
CSU,CSU,0x800201,0x480201,alarm DO,MAC Address Not Set,MAC Not Set,MAC地址未设置,MAC地址未设置,,sint8,1,0,,,,,,
CSU,CSU,0x800201,0x180301,alarm,Input Relay Alarm,Input Rly.Alm,输入干接点告警,输入干接点告警,,sint8,8,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
CSU,CSU,0x800201,0x480301,alarm DO,Input Relay Alarm,Input Rly.Alm,输入干接点告警,输入干接点告警,,sint8,8,0,,,,,,
CSU,CSU,0x800201,0x180401,alarm,CPU Usage Rate High Alarm,CPU Usage R.H.,CPU利用率高告警,CPU利用率高,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
CSU,CSU,0x800201,0x480401,alarm DO,CPU Usage Rate High Alarm,CPU Usage R.H.,CPU利用率高告警,CPU利用率高,,sint8,1,0,,,,,,
CSU,CSU,0x800201,0x180501,alarm,Memory Usage Rate High Alarm,Mem.Usage R.H.,内存利用率高告警,内存利用率高,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
CSU,CSU,0x800201,0x480501,alarm DO,Memory Usage Rate High Alarm,Mem.Usage R.H.,内存利用率高告警,内存利用率高,,sint8,1,0,,,,,,
CSU,CSU,0x800201,0x180601,alarm,UIB Communication Fail,UIB Comm Fail,UIB通讯断,UIB通讯断,,sint8,1,0,,,,,1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
CSU,CSU,0x800201,0x480601,alarm DO,UIB Communication Fail,UIB Comm Fail,UIB通讯断,UIB通讯断,,sint8,1,0,,,,,,
CSU,CSU,0x800201,0x180701,alarm,IDDB Communication Fail,IDDB Comm Fail,IDDB通讯断,IDDB通讯断,,sint8,1,0,,,,,1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
CSU,CSU,0x800201,0x480701,alarm DO,IDDB Communication Fail,IDDB Comm Fail,IDDB通讯断,IDDB通讯断,,sint8,1,0,,,,,,
CSU,CSU,0x800201,0x180801,alarm,History Alarm Full,HisAlm.Full,历史告警满,历史告警满,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
CSU,CSU,0x800201,0x480801,alarm DO,History Alarm Full,HisAlm.Full,历史告警满,历史告警满,,sint8,1,0,,,,,,
CSU,CSU,0x800201,0x180901,alarm,CSU Fault Alarm,CSU Fault Alm.,CSU故障告警,CSU故障告警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
CSU,CSU,0x800201,0x480901,alarm DO,CSU Fault Alarm,CSU Fault Alm.,CSU故障告警,CSU故障告警,,sint8,1,0,,,,,,
CSU,CSU,0x800201,0x200101,control,Total Alarm Disable,Total Alm. Dis,禁止所有告警,禁止所有告警,,sint8,1,,,,,,,
CSU,CSU,0x800201,0x200201,control,Total Alarm Enable,Total Alm. En,允许所有告警,允许所有告警,,sint8,1,,,,,,,
CSU,CSU,0x800201,0x200501,control,Open SSH,Open SSH,开启SSH,开启SSH,,sint8,1,,,,,,,
CSU,CSU,0x800201,0x200601,control,Close SSH,Close SSH,关闭SSH,关闭SSH,,sint8,1,,,,,,,
CSU,CSU,0x800201,0x200701,control,Open WEB,Open WEB,开启WEB,开启WEB,,sint8,1,,,,,,,
CSU,CSU,0x800201,0x200801,control,Close WEB,Close WEB,关闭WEB,关闭WEB,,sint8,1,,,,,,,
CSU,CSU,0x800201,0x200b01,control,iEnergy SSH Security Code Reset,iEnergy SSH Code Reset,能源网管SSH口令重置,能源网管SSH口令重置,,sint8,1,,,,,,,
CSU,CSU,0x800201,0x280201,parameter,CPU Usage Rate High Threshold,CPU Usage H.Thre.,CPU利用率高阈值,CPU高阈值,%,sint16,1,0,80,10,100,1,,
CSU,CSU,0x800201,0x280301,parameter,Memory Usage Rate High Threshold,Mem.Usage H.Thre.,内存利用率高阈值,内存高阈值,%,sint16,1,0,80,10,100,1,,
CSU,CSU,0x800201,0x280401,parameter,Positive Alarm Enable,Pos Alm Enbale,CSU主动告警使能,主动告警使能,,sint8,1,,0,0,1,1,0:禁止/Disable;1:允许/Enable;,
CSU,CSU,0x800201,0x280501,parameter,Hisdata Save Interval,Hisdata Inter,历史数据保存间隔,历史数据间隔,Min,sint16,1,0,360,1,1440,1,,
CSU,CSU,0x800201,0x280901,parameter,Input Relay Current Relative Signal,Input Rly.Curr.Rela.Signal,输入干接点当前关联信号,输入干接点当前关联信号,,WTDTI,8,0,0,,,,,
CSU,CSU,0x800201,0x280a01,parameter,Input Relay Alias,Input Relay Alias,输入干接点别名,输入干接点别名,,string32,8,,,,,,,
CSU,CSU,0x800201,0x280b01,parameter,Input Relay Alarm Or Abnormal State,Input Rly.Abno.State,输入干接点告警或异常状态,输入干接点告警或异常状态,,sint8,8,0,0,0,1,,0:闭合/Close;1:断开/Break;,
CSU,CSU,0x800201,0x280c01,parameter,Output Relay Current Relative Signal,Output Rly.Curr.Rela.Signal,输出干接点当前关联信号,输出干接点当前关联信号,,WTDTI,14,0,0,,,,,
CSU,CSU,0x800201,0x280d01,parameter,Output Relay Default State,Output Rly.Defa.State,输出干接点默认状态,输出干接点默认状态,,sint8,14,0,0,0,1,,0:闭合/Close;1:断开/Break;,
CSU,CSU,0x800201,0x280e01,parameter,Temperature AI Current Relative Signal,Temp AI.Curr.Rela.Signal,温度AI当前关联信号,温度AI当前关联信号,,WTDTI,8,0,0,,,,,
CSU,CSU,0x800201,0x280f01,parameter,Temperature AI Alias,Temp AI Alias,温度AI别名,温度AI别名,,string32,8,,,,,,,
CSU,CSU,0x800201,0x400101,device info,Serial Number,SN,序列号,序列号,,string16,1,,,,,,,
CSU,CSU,0x800201,0x400201,device info,CSU Software Platform Version,Platform Version,CSU软件所属平台版本,平台版本,,string16,1,,,,,,,
CSU,CSU,0x800201,0x400301,device info,SoftWare Name,SoftWare Name,软件名称,软件名称,,string32,1,,,,,,,
CSU,CSU,0x800201,0x400401,device info,Boot Version,Boot Version,BOOT版本,BOOT版本,,string16,1,,1.3.4,,,,,
CSU,CSU,0x800201,0x400501,device info,Kernel Version,Kernel Version,内核版本,内核版本,,string16,1,,2.6.27,,,,,
CSU,CSU,0x800201,0x400601,device info,Software Version,Software Version,软件版本,软件版本,,string16,1,,,,,,,
CSU,CSU,0x800201,0x400701,device info,Manufactory Name,Manufactory Name,厂家名称,厂家名称,,string32,1,,ZTE,,,,,
CSU,CSU,0x800201,0x400801,device info,Software Release Date,Soft.Release Date,软件发布日期,软件发布日期,,date,1,,,,,,,
CSU,CSU,0x800201,0x400901,device info,Platform Release Date,Plat.Release Date,平台发布日期,平台发布日期,,date,1,,,,,,,
CSU,CSU,0x800201,0x400a01,device info,UIB Version,UIB Version,UIB版本信息,UIB版本信息,,string16,1,,,,,,,
CSU,CSU,0x800201,0x400b01,device info,IDDB Version,IDDB Version,IDDB版本信息,IDDB版本信息,,string16,1,,,,,,,
CSU,CSU,0x800201,0x400c01,device info,MAC Address,MAC addr.,MAC地址,MAC地址,,string32,1,,,,,,,
CSU,CSU,0x800201,0x400d01,device info,UIB Release Date,UIB Release Date,UIB发布日期,UIB发布日期,,date,1,,,,,,,
CSU,CSU,0x800201,0x400e01,device info,IDDB Release Date,IDDB Release Date,IDDB发布日期,IDDB发布日期,,date,1,,,,,,,
CSU,CSU,0x800201,0x400f01,device info,UIB Barcodes,UIB Barcodes,UIB条码,UIB条码,,string16,1,,,,,,,
CSU,CSU,0x800201,0x401001,device info,IDDB Barcodes,IDDB Barcodes,IDDB条码,IDDB条码,,string16,1,,,,,,,
CSU,CSU,0x800201,0x401101,device info,UIB Type,UIB Type,UIB类型,UIB类型,,string16,1,,,,,,,
CSU,CSU,0x800201,0x401201,device info,Input Relay Channel Name,Input Rly.Chan.Name,输入干接点通道名称,输入干接点通道名称,,string32,8,,,,,,,
CSU,CSU,0x800201,0x401301,device info,Input Relay Relative Signal,Input Rly.Rela.Signal,输入干接点关联信号,输入干接点关联信号,,WTDTI,42,0,0,,,,,
CSU,CSU,0x800201,0x401401,device info,Output Relay Channel Name,Output Rly.Chan.Name,输出干接点通道名称,输出干接点通道名称,,string32,14,,,,,,,
CSU,CSU,0x800201,0x401501,device info,Output Relay Relative Signal,Output Rly.Rela.Signal,输出干接点关联信号,输出干接点关联信号,,WTDTI,16,0,0,,,,,
CSU,CSU,0x800201,0x401601,device info,Temperature AI Channel Name,Temp AI.Chan.Name,温度AI通道名称,温度AI通道名称,,string32,8,,,,,,,
CSU,CSU,0x800201,0x401701,device info,Temperature AI Relative Signal,Temp AI.Rela.Signal,温度AI关联信号,温度AI关联信号,,WTDTI,16,0,0,,,,,
能源系统,Power System,0x800401,0x080101,analog data,DC Voltage,DC Voltage,直流电压,直流电压,V,sint16,1,2,4800.0,0.0,6000.0,,,
能源系统,Power System,0x800401,0x080201,analog data,Load Total Current,Load Current,负载总电流,负载电流,A,sint32,1,2,0.0,0.0,300000.0,,,
能源系统,Power System,0x800401,0x080301,analog data,DC Load Total Power,Load Total Pwr.,直流负载总功率,负载总功率,kW,sint32,1,2,0.0,0.0,60000.0,,,
能源系统,Power System,0x800401,0x080401,analog data,Battery Total Current,Batt.Total Curr.,电池组总电流,电池总电流,A,sint32,1,2,0.0,-300000.0,300000.0,10.0,,
能源系统,Power System,0x800401,0x080501,analog data,Battery Status Duration,Batt.Stat.Dura.,电池状态持续时间,电池状态持续时间,Min,sint32,1,0,0,0,65535,,,
能源系统,Power System,0x800401,0x080601,analog data,SMR Total Output Current,Smr Total Curr,整流器总输出电流,整流器总电流,A,sint32,1,2,0.0,0.0,300000.0,,,
能源系统,Power System,0x800401,0x080701,analog data,SMR Setting Voltage,SMR Set Volt,整流器当前输出设定电压,整流器设定电压,V,sint16,1,2,5350.0,4200.0,5800.0,,,
能源系统,Power System,0x800401,0x080801,analog data,SMR Setting Current Limit Rate,SMR CL Rate,整流器设定限流点比率,整流器限流点比率,‰,sint16,1,0,1000,50,1000,,,
能源系统,Power System,0x800401,0x080901,analog data,SMR Working Number,SMR Work Num,工作整流器数量,工作整流器数量,,sint16,1,0,0,0,48,,,
能源系统,Power System,0x800401,0x080a01,analog data,SMR Online Number,SMR Online Num,在线整流器数量,在线整流器数量,,sint16,1,0,0,0,48,,,
能源系统,Power System,0x800401,0x080c01,analog data,System AC Voltage,System AC Volt.,系统交流电压,系统交流电压,V,sint16,3,1,2200.0,0.0,3000.0,,,
能源系统,Power System,0x800401,0x080d01,analog data,System AC Current,System AC Curr.,系统交流电流,系统交流电流,A,sint16,3,1,0.0,0.0,6000.0,,,
能源系统,Power System,0x800401,0x080e01,analog data,DC Reserve Input Current,DC Res.Curr.,直流备用输入电流,直流备用电流,A,sint16,1,1,0.0,0.0,30000.0,,,
能源系统,Power System,0x800401,0x080f01,analog data,PU Total Output Current,PU Total Out.Curr.,PU输出总电流,PU输出总电流,A,sint32,1,2,0.0,0.0,300000.0,,,
能源系统,Power System,0x800401,0x081001,analog data,PU Total Output Power,PU Total Out.Pwr.,PU输出总功率,PU输出总功率,kW,sint32,1,2,0.0,0.0,3276700.0,,,
能源系统,Power System,0x800401,0x081101,analog data,PU Setting Voltage,PU Set.Volt.,PU当前输出设定电压,PU设定电压,V,sint16,1,2,5350.0,4200.0,5800.0,,,
能源系统,Power System,0x800401,0x081201,analog data,DC Output Current,DC Out.Curr.,直流输出电流,直流输出电流,A,sint32,1,2,0.0,0.0,300000.0,,,
能源系统,Power System,0x800401,0x081301,analog data,DC Output Power,DC Out.Power,直流输出功率,直流输出功率,kW,sint32,1,2,0.0,0.0,60000.0,,,
能源系统,Power System,0x800401,0x100101,digital data,Battery Management Status,Batt Manag Status,电池管理状态,电池管理状态,,sint8,1,0,0,0,6,,0:浮充/Float;1:均充/Equal;2:测试/Test;3:系统停电/PowerOff;4:检测/Detect;5:过渡/Transition;6:充电/Charge;,
能源系统,Power System,0x800401,0x100201,digital data,AC Power Source,AC Source,交流供电状态,交流供电状态,,sint8,1,0,0,0,8,,0:无/Null;2:油机1/DG1;3:油机2/DG2;4:市电1/AC1;5:市电2/AC2;,
能源系统,Power System,0x800401,0x100301,digital data,AC Save Energy Status,AC Save Eng.Stu.,交流节能状态,交流节能状态,,sint8,1,0,0,0,6,,0:安全/Safe;1:自由/Free Mode;2:自动非节能/Auto NonSave;3:自动节能/Auto Save;4:暂时非节能/Temp NonSave;5:永久非节能/Perm NonSave;6:人工维护检测/Manual Detect;,
能源系统,Power System,0x800401,0x100401,digital data,Emergency Light Status,Emerg.Light Status,应急照明状态,应急照明状态,,sint8,1,0,0,0,1,,0:关闭/Off;1:开启/On;,
能源系统,Power System,0x800401,0x100501,digital data,PV Power Status,PV Status,光伏供电状态,光伏供电状态,,sint8,1,0,0,0,1,,0:无/Null;1:有/Exist;,
能源系统,Power System,0x800401,0x100601,digital data,PLC Digital Output Status,PLC.DO.Stu,PLC自定义状态,PLC自定义状态,,sint8,8,0,0,0,1,,0:关闭/Off;1:开启/On;,
能源系统,Power System,0x800401,0x180101,alarm,System Expansion Alarm,Sys.Expan.Alm,系统扩容告警,系统扩容告警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
能源系统,Power System,0x800401,0x480101,alarm DO,System Expansion Alarm,Sys.Expan.Alm,系统扩容告警,系统扩容告警,,sint8,1,0,,,,,,
能源系统,Power System,0x800401,0x180201,alarm,System OverLoad Alarm,Sys.OverL.Alm,系统过载告警,系统过载告警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
能源系统,Power System,0x800401,0x480201,alarm DO,System OverLoad Alarm,Sys.OverL.Alm,系统过载告警,系统过载告警,,sint8,1,0,,,,,,
能源系统,Power System,0x800401,0x180301,alarm,Common Alarm,Common Alarm,总告警,总告警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
能源系统,Power System,0x800401,0x480301,alarm DO,Common Alarm,Common Alarm,总告警,总告警,,sint8,1,0,,,,,,
能源系统,Power System,0x800401,0x180401,alarm,Module Slot Fault Alarm,Mod.Slot Fault,模块槽位异常告警,模块槽位异常告警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
能源系统,Power System,0x800401,0x480401,alarm DO,Module Slot Fault Alarm,Mod.Slot Fault,模块槽位异常告警,模块槽位异常告警,,sint8,1,0,,,,,,
能源系统,Power System,0x800401,0x180501,alarm,AC Input Scenario Fault Alarm,Ac.Scen.Fault,交流输入场景配置错误告警,交流场景配置错误,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
能源系统,Power System,0x800401,0x480501,alarm DO,AC Input Scenario Fault Alarm,Ac.Scen.Fault,交流输入场景配置错误告警,交流场景配置错误,,sint8,1,0,,,,,,
能源系统,Power System,0x800401,0x180601,alarm,PLC Custom Alarm,PLC.Cuz.Alm,PLC自定义告警,PLC自定义告警,,sint8,12,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
能源系统,Power System,0x800401,0x480601,alarm DO,PLC Custom Alarm,PLC.Cuz.Alm,PLC自定义告警,PLC自定义告警,,sint8,12,0,,,,,,
能源系统,Power System,0x800401,0x180701,alarm,Inverter Alarm,Inverter Alm,逆变器告警,逆变器告警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
能源系统,Power System,0x800401,0x480701,alarm DO,Inverter Alarm,Inverter Alm,逆变器告警,逆变器告警,,sint8,1,0,,,,,,
能源系统,Power System,0x800401,0x200201,control,SDDU Device Statistic,SDDU Dev.Stat.,SDDU设备统计,SDDU设备统计,,sint8,1,,,,,,,
能源系统,Power System,0x800401,0x280201,parameter,System OverLoad Alarm Threshold,Sys.OverLoad Thre,系统过载告警阈值,系统过载阈值,%,sint16,1,0,80,10,100,1,,
能源系统,Power System,0x800401,0x280401,parameter,AC Input Scenario,AC In.Scenario,交流输入场景,交流输入场景,,sint8,1,0,0,0,3,1,0:市电/Mains;1:油电/Mains and DG;2:纯油机/Only DG;3:无/None;,
能源系统,Power System,0x800401,0x280601,parameter,Site Name,Site Name,站点名称,站点名称,,string32,1,0,Site-X#,,,,,
能源系统,Power System,0x800401,0x280701,parameter,Env Survey Enable,Env Survey Enable,环境监测使能,环境监测使能,,sint8,1,0,1,0,1,1,0:禁止/Disable;1:允许/Enable;,
能源系统,Power System,0x800401,0x280801,parameter,DC Reserve Input Configuration,DC Res.In.Config.,直流备用输入配置,直流备用输入配置,,sint8,1,0,1,0,1,1,0:无/Null;1:有/Exist;,
能源系统,Power System,0x800401,0x280901,parameter,Battery Config,Battery Config,电池配置,电池配置,,sint8,1,0,1,1,4,1,1:纯铅酸/Only VRLA Batt;2:智能锂电/Smart Li Batt;3:铅酸&锂电混用/VRLA&Li Batt;4:常规锂电池/Normal Li Batt;,
能源系统,Power System,0x800401,0x280b01,parameter,Vrla Battery Type,Vrla Batt.Type,铅酸类型,铅酸类型,,sint8,1,0,0,0,3,1,0:普通铅酸电池/VRLA Batt;1:循环电池/Cycling Batt;2:快充电池/Fast Charge Batt;3:储能电池/Store Energy Batt;,
能源系统,Power System,0x800401,0x280c01,parameter,Longitude,Longitude,经度,经度,°,sint32,1,4,0.0,-1800000.0,1800000.0,1.0,,
能源系统,Power System,0x800401,0x280d01,parameter,Latitude,Latitude,纬度,纬度,°,sint32,1,4,0.0,-900000.0,900000.0,1.0,,
能源系统,Power System,0x800401,0x280e01,parameter,Altitude,Altitude,海拔高度,海拔高度,m,sint16,1,0,0,-1000,8848,1,,
能源系统,Power System,0x800401,0x280f01,parameter,Set MAINS and Battery Priority,MAINS Batt.Prior.,市电电池优先级设置,市电电池优先级,,sint8,1,0,0,0,1,1,0:市电优先/MAINS Prior;1:电池优先/Batt.Prior;,
能源系统,Power System,0x800401,0x281001,parameter,SMR Energy Save Off Current Max,Save Off Curr.Max.,整流器节能关闭最大电流,节能关闭最大电流,A,sint16,1,1,400.0,100.0,10000.0,1.0,,
能源系统,Power System,0x800401,0x281101,parameter,Address Street,Addr.Street,街道,街道,,string32,1,0,Site-X#,,,,,
能源系统,Power System,0x800401,0x281201,parameter,Address City,Addr.City,城市,城市,,string32,1,0,Site-X#,,,,,
能源系统,Power System,0x800401,0x281301,parameter,Address Administrative Region,Addr.Admin.Region,行政区,行政区,,string32,1,0,Site-X#,,,,,
能源系统,Power System,0x800401,0x281401,parameter,Normal Li Battery Type,Nor.Li Batt.Type,常规锂电类型,常规锂电类型,,sint8,1,0,0,0,0,1,0:NFB15/NFB15;,
能源系统,Power System,0x800401,0x281501,parameter,Smart Li Battery Type,Smart Li Batt.Type,智能锂电类型,智能锂电类型,,sint8,1,0,1,0,1,1,0:FB15/FB15;1:FB100B3电池/FB100B3;,
能源系统,Power System,0x800401,0x300101,record data,Historical data save Cause,HisData Sav.Cause,历史数据保存原因,历史数据保存原因,,sint8,1,0,0,0,10,,0:自动/Auto;1:参数变化/Para Change;2:告警变化/Alarm Change;3:电池状态变化/Batt Stat Change;4:油机启停/Gen Start and Stop;5:市电启停/Mains Start and Stop;6:油机运行10分钟/Gen Run 10min;7:油机运行30分钟/Gen Run 30min;8:油机运行60分钟/Gen Run 60min;9:电池测试/Batt Testing;10:电池检测/Batt Detect;,
能源系统,Power System,0x800401,0x300201,record data,Real Time Alarm Occurrence Time,Real Alm.Time,实时告警时间,实时告警时间,,time,1,,,,,,,
能源系统,Power System,0x800401,0x300301,record data,History Alarm Time,His.Alm.Time,历史告警时间,历史告警时间,,process_time,1,,,,,,,
能源系统,Power System,0x800401,0x300901,record data,Maximum Peak Time,Max.Peak.Time,最大极值时间,最大极值时间,,time,1,,,,,,,
能源系统,Power System,0x800401,0x300a01,record data,Minimum Peak Time,Min.Peak.Time,最小极值时间,最小极值时间,,time,1,,,,,,,
能源系统,Power System,0x800401,0x300b01,record data,History Data Record Time,Data Rec.Time,历史数据记录时间,数据记录时间,,time,1,,,,,,,
能源系统,Power System,0x800401,0x301401,record data,control Record Time,Ctrl.Rec.Time,操作记录时间,操作记录时间,,time,1,,,,,,,
能源系统,Power System,0x800401,0x301201,record data,Event Duration,Event Duration,事件维持时间,事件维持时间,Min,sint32,1,,,,,,,
能源系统,Power System,0x800401,0x301501,record data,Operator,Operator,操作者,操作者,,string32,1,0,,,,,,
能源系统,Power System,0x800401,0x301601,record data,Operation Content,Operation Content,操作内容,操作内容,,string128,1,0,,,,,,
能源系统,Power System,0x800401,0x380101,stastic data,DC Reserve Input Power,DC Res.Power,直流备用输入发电量,直流备用发电量,kWh,sint32,1,2,,,,,,
能源系统,Power System,0x800401,0x380201,stastic data,DC Reserve Input Work Duration,DC Res.Work Dura.,直流备用输入工作时间,直流备用工作时间,Min,sint32,1,1,,,,,,
能源系统,Power System,0x800401,0x400101,device info,System Name,System Name,系统名称,系统名称,,string32,1,,,,,,,
系统交流输入,System AC Input,0x800601,0x180101,alarm,AC Power Off,AC Power Off,交流停电,交流停电,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
系统交流输入,System AC Input,0x800601,0x480101,alarm DO,AC Power Off,AC Power Off,交流停电,交流停电,,sint8,1,0,,,,,,
系统交流输入,System AC Input,0x800601,0x180201,alarm,AC Phase Lack,AC Phase Lack,交流缺相,交流缺相,,sint8,3,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
系统交流输入,System AC Input,0x800601,0x480201,alarm DO,AC Phase Lack,AC Phase Lack,交流缺相,交流缺相,,sint8,3,0,,,,,,
系统交流输入,System AC Input,0x800601,0x180301,alarm,AC Voltage Low,AC Volt.Low,交流电压低,交流电压低,,sint8,3,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
系统交流输入,System AC Input,0x800601,0x480301,alarm DO,AC Voltage Low,AC Volt.Low,交流电压低,交流电压低,,sint8,3,0,,,,,,
系统交流输入,System AC Input,0x800601,0x180401,alarm,AC Voltage High,AC Volt.High,交流电压高,交流电压高,,sint8,3,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
系统交流输入,System AC Input,0x800601,0x480401,alarm DO,AC Voltage High,AC Volt.High,交流电压高,交流电压高,,sint8,3,0,,,,,,
系统交流输入,System AC Input,0x800601,0x180501,alarm,AC Current High,AC Curr.High,交流电流高,交流电流高,,sint8,3,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
系统交流输入,System AC Input,0x800601,0x480501,alarm DO,AC Current High,AC Curr.High,交流电流高,交流电流高,,sint8,3,0,,,,,,
系统交流输入,System AC Input,0x800601,0x180601,alarm,AC Voltage Imbalance,AC Volt.Imbala,交流电压不平衡,交流电压不平衡,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
系统交流输入,System AC Input,0x800601,0x480601,alarm DO,AC Voltage Imbalance,AC Volt.Imbala,交流电压不平衡,交流电压不平衡,,sint8,1,0,,,,,,
系统交流输入,System AC Input,0x800601,0x180701,alarm,AC Voltage Too Low,AC Volt.T.Low,交流电压过低,交流电压过低,,sint8,3,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
系统交流输入,System AC Input,0x800601,0x480701,alarm DO,AC Voltage Too Low,AC Volt.T.Low,交流电压过低,交流电压过低,,sint8,3,0,,,,,,
系统交流输入,System AC Input,0x800601,0x180801,alarm,AC Voltage Too High,AC Volt.T.High,交流电压过高,交流电压过高,,sint8,3,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
系统交流输入,System AC Input,0x800601,0x480801,alarm DO,AC Voltage Too High,AC Volt.T.High,交流电压过高,交流电压过高,,sint8,3,0,,,,,,
系统交流输入,System AC Input,0x800601,0x180a01,alarm,ACEM Loop Config Abnormal,ACEM Loop Config Abnormal,交流电表回路配置异常,交流电表回路配置异常,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
系统交流输入,System AC Input,0x800601,0x480a01,alarm DO,ACEM Loop Config Abnormal,ACEM Loop Config Abnormal,交流电表回路配置异常,交流电表回路配置异常,,sint8,1,0,,,,,,
系统交流输入,System AC Input,0x800601,0x280101,parameter,AC Current High Threshold,AC Curr.High Thre,交流电流高阈值,交流电流高阈值,A,sint16,1,1,800.0,50.0,6000.0,1.0,,
系统交流输入,System AC Input,0x800601,0x280201,parameter,AC Power Supply,AC Power Supply,交流制式,交流制式,,sint8,1,0,0,0,3,1,0:L1L2L3N-220V/L1L2L3N-220V;1:L1L2L3-110V/L1L2L3-110V;2:L1N-220V/L1N-220V;3:L1L2-110V/L1L2-110V;,
系统交流输入,System AC Input,0x800601,0x280301,parameter,AC Voltage High Threshold,AC Volt.H.Thre,交流电压高阈值,交流电压高阈值,V,sint16,1,1,2860.0,2400.0,2900.0,1.0,,"<=AC Voltage Too High Threshold-10V;
"
系统交流输入,System AC Input,0x800601,0x280401,parameter,AC Voltage Low Threshold,AC Volt.L.Thre,交流电压低阈值,交流电压低阈值,V,sint16,1,1,1540.0,800.0,2000.0,1.0,,">=AC Voltage Too Low Threshold+10V;
"
系统交流输入,System AC Input,0x800601,0x280501,parameter,AC Voltage Imbalance Threshold,AC Volt.Imbala,交流电压不平衡阈值,交流电压不平衡,V,sint16,1,1,550.0,220.0,2200.0,1.0,,
系统交流输入,System AC Input,0x800601,0x280601,parameter,Mains Config,Mains Config,市电配置,市电配置,,sint8,2,0,1,0,1,1,0:无/Null;1:有/Exist;,
系统交流输入,System AC Input,0x800601,0x280701,parameter,AC Voltage Too High Threshold,AC Volt.T.H.Thre,交流电压过高阈值,交流电压过高值,V,sint16,1,1,2960.0,2500.0,3000.0,1.0,,">=AC Voltage High Threshold+10V;
"
系统交流输入,System AC Input,0x800601,0x280801,parameter,AC Voltage Too Low Threshold,AC Volt.T.L.Thre,交流电压过低阈值,交流电压过低值,V,sint16,1,1,1440.0,700.0,1900.0,1.0,,"<=AC Voltage Low Threshold-10V;
"
系统交流输入,System AC Input,0x800601,0x280901,parameter,Diesel Generator Power,DG Power,柴油发电机额定功率,油机额定功率,kVA,sint32,2,1,150.0,0.0,2000.0,1.0,,
系统交流输入,System AC Input,0x800601,0x280c01,parameter,Diesel Generator Application Scenario,DG Apl.Scenario,柴油发电机应用场景,油机应用场景,,sint8,1,0,1,0,1,1,0:非受控/Uncontrolled;1:受控/Controlled;,
交流配电,AC Distribution,0x800801,0x100101,digital data,AC Input Switch Status,AC Input Switch,交流输入空开状态,交流输入空开,,sint8,1,0,0,0,2,,0:闭合/close;1:断开/break;2:无/Null;,
交流配电,AC Distribution,0x800801,0x100201,digital data,AC Output Switch Status,AC Out.Switch,交流输出空开状态,交流输出空开,,sint8,1,0,0,0,2,,0:闭合/close;1:断开/break;2:无/Null;,
交流配电,AC Distribution,0x800801,0x100301,digital data,AC SPD Status,AC SPD,交流防雷器状态,交流防雷器,,sint8,1,0,0,0,2,,0:正常/Normal;1:异常/Fault;2:无/Null;,
交流配电,AC Distribution,0x800801,0x180101,alarm,AC Input Switch Off,AC In.Switch Off,交流输入空开断,交流输入空开断,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
交流配电,AC Distribution,0x800801,0x480101,alarm DO,AC Input Switch Off,AC In.Switch Off,交流输入空开断,交流输入空开断,,sint8,1,0,,,,,,
交流配电,AC Distribution,0x800801,0x180201,alarm,AC SPD Abnormal,AC SPD Abr,交流防雷器异常,交流防雷器异常,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
交流配电,AC Distribution,0x800801,0x480201,alarm DO,AC SPD Abnormal,AC SPD Abr,交流防雷器异常,交流防雷器异常,,sint8,1,0,,,,,,
交流配电,AC Distribution,0x800801,0x180301,alarm,AC Output Switch Off,AC Out.SW Off,交流输出空开断,交流输出空开断,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
交流配电,AC Distribution,0x800801,0x480301,alarm DO,AC Output Switch Off,AC Out.SW Off,交流输出空开断,交流输出空开断,,sint8,1,0,,,,,,
市电,Mains,0x800a01,0x080101,analog data,MAINS Phase Voltage,Mns.Ph.Volt,市电相电压,市电相电压,V,sint16,3,1,2200.0,0.0,3000.0,,,
市电,Mains,0x800a01,0x080201,analog data,MAINS Line Voltage,Mns.Line Volt,市电线电压,市电线电压,V,sint16,3,1,3800.0,0.0,5200.0,,,
市电,Mains,0x800a01,0x080301,analog data,MAINS Phase Current,Mns.Ph.Curr,市电相电流,市电相电流,A,sint16,3,1,0.0,0.0,6000.0,,,
市电,Mains,0x800a01,0x080401,analog data,MAINS Active Power,Mns.Act.Pwr,市电有功功率,市电有功功率,kW,sint32,3,2,0.0,0.0,20000.0,,,
市电,Mains,0x800a01,0x080501,analog data,MAINS Reactive Power,Mns.Rct.Pwr,市电无功功率,市电无功功率,kvar,sint32,3,2,0.0,0.0,20000.0,,,
市电,Mains,0x800a01,0x080601,analog data,MAINS Apparent Power,Mns.App.Pwr,市电视在功率,市电视在功率,kVA,sint32,3,2,0.0,0.0,20000.0,,,
市电,Mains,0x800a01,0x080701,analog data,MAINS Power Factor,Mns.PF,市电功率因数,市电功率因数,,sint16,3,2,100.0,-100.0,100.0,,,
市电,Mains,0x800a01,0x080801,analog data,MAINS Output Frequency,Mns.Out.Freq,市电输出频率,市电输出频率,Hz,sint16,1,0,50,40,75,,,
市电,Mains,0x800a01,0x080901,analog data,MAINS Total Active Power,Mns.Tot.Act.Pwr,市电总有功功率,市电总有功功率,kW,sint32,1,2,0.0,0.0,20000.0,,,
市电,Mains,0x800a01,0x080a01,analog data,MAINS Total Reactive Power,Mns.Tot.Rct.Pwr,市电总无功功率,市电总无功功率,kvar,sint32,1,2,0.0,0.0,20000.0,,,
市电,Mains,0x800a01,0x080b01,analog data,MAINS Total Apparent Power,Mns.Tot.App.Pwr,市电总视在功率,市电总视在功率,kVA,sint32,1,2,0.0,0.0,20000.0,,,
市电,Mains,0x800a01,0x080c01,analog data,MAINS Average Power Factor,Mns.Avr.PF,市电平均功率因数,市电平均功率因数,,sint16,1,2,100.0,-100.0,100.0,,,
市电,Mains,0x800a01,0x100101,digital data,MAINS Status,Mns.Status,市电输入状态,市电输入状态,,sint8,1,0,0,0,1,,0:正常/Normal;1:异常/Fault;,
市电,Mains,0x800a01,0x180101,alarm,MAINS High Frequency,Mns.High Freq,市电频率高,市电频率高,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
市电,Mains,0x800a01,0x480101,alarm DO,MAINS High Frequency,Mns.High Freq,市电频率高,市电频率高,,sint8,1,0,,,,,,
市电,Mains,0x800a01,0x180201,alarm,MAINS Phase Voltage Imbalance,Mns.Volt.Imba,市电相电压不平衡,市电相电压不平衡,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
市电,Mains,0x800a01,0x480201,alarm DO,MAINS Phase Voltage Imbalance,Mns.Volt.Imba,市电相电压不平衡,市电相电压不平衡,,sint8,1,0,,,,,,
市电,Mains,0x800a01,0x180401,alarm,MAINS Failure,Mns.Failure,市电停电,市电停电,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
市电,Mains,0x800a01,0x480401,alarm DO,MAINS Failure,Mns.Failure,市电停电,市电停电,,sint8,1,0,,,,,,
市电,Mains,0x800a01,0x180501,alarm,MAINS Phase Voltage High,Mns.Pha.Volt.H.,市电电压过压,市电电压过压,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
市电,Mains,0x800a01,0x480501,alarm DO,MAINS Phase Voltage High,Mns.Pha.Volt.H.,市电电压过压,市电电压过压,,sint8,1,0,,,,,,
市电,Mains,0x800a01,0x180601,alarm,MAINS Phase Voltage Low,Mns.Pha.Volt.L.,市电电压欠压,市电电压欠压,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
市电,Mains,0x800a01,0x480601,alarm DO,MAINS Phase Voltage Low,Mns.Pha.Volt.L.,市电电压欠压,市电电压欠压,,sint8,1,0,,,,,,
市电,Mains,0x800a01,0x180701,alarm,MAINS Phase Current High,Mns.Pha.Curr.H.,市电相电流高,市电相电流高,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
市电,Mains,0x800a01,0x480701,alarm DO,MAINS Phase Current High,Mns.Pha.Curr.H.,市电相电流高,市电相电流高,,sint8,1,0,,,,,,
市电,Mains,0x800a01,0x180801,alarm,MAINS Low Frequency,Mns.Low Freq,市电频率低,市电频率低,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
市电,Mains,0x800a01,0x480801,alarm DO,MAINS Low Frequency,Mns.Low Freq,市电频率低,市电频率低,,sint8,1,0,,,,,,
市电,Mains,0x800a01,0x300201,record data,MAINS Work Time,Mns.Work Time,市电工作历史时间,市电工作历史时间,,process_time,1,0,,,,,,
市电,Mains,0x800a01,0x300901,record data,MAINS Initial Energy Production,Mns.Init.Eng.Prod,市电起始供电量,市电起始供电量,kWh,sint32,1,2,0.0,0.0,,,,
市电,Mains,0x800a01,0x300a01,record data,MAINS Final Energy Production,Mns.Final Eng.Prod,市电终止供电量,市电终止供电量,kWh,sint32,1,2,0.0,0.0,,,,
市电,Mains,0x800a01,0x380201,stastic data,MAINS Work Duration,Mns.Work Dura,市电工作时间,市电工作时间,Min,sint32,1,0,,,,,,
市电,Mains,0x800a01,0x380301,stastic data,MAINS Energy Production,Mns.Eng.Prod,市电供电量,市电供电量,kWh,sint32,1,2,0.0,0.0,,,,
市电,Mains,0x800a01,0x380501,stastic data,MAINS AC Energy Production,Mns.AC Eng.Prod,市电交流发电量,市电交流发电量,kWh,sint32,1,2,0.0,0.0,,,,
市电组,Mains Group,0x800c01,0x180101,alarm,Acin Power Limit Early Warning,Ac Power Lim.EW.,交流输入限功率预警,交流限功率预警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
市电组,Mains Group,0x800c01,0x480101,alarm DO,Acin Power Limit Early Warning,Ac Power Lim.EW.,交流输入限功率预警,交流限功率预警,,sint8,1,0,,,,,,
市电组,Mains Group,0x800c01,0x180201,alarm,Acin Power Limit alarm,Ac Power Lim.Alm,交流输入限功率告警,交流限功率告警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
市电组,Mains Group,0x800c01,0x480201,alarm DO,Acin Power Limit alarm,Ac Power Lim.Alm,交流输入限功率告警,交流限功率告警,,sint8,1,0,,,,,,
市电组,Mains Group,0x800c01,0x280101,parameter,MAINS Rated Active Power,MAINS Rat.Act.Pwr,市电额定有功功率,市电额定有功功率,kW,sint32,1,2,0.0,0.0,100000.0,10.0,,
市电组,Mains Group,0x800c01,0x280201,parameter,MAINS Derate Factor,MAINS Derate Factor,市电降额系数,市电降额系数,%,sint16,1,0,85,0,100,1,,
市电组,Mains Group,0x800c01,0x280301,parameter,Acin Power Limit Early Warning Threshold,Ac Power Limit EW.Thre.,交流输入限功率预警阈值,交流限功率预警阈值,%,sint16,1,0,85,80,90,1,,"<=Mains Group|Acin Power Limit alm Threshold-10%;
"
市电组,Mains Group,0x800c01,0x280401,parameter,Acin Power Limit alm Threshold,Ac Power Limit alm Threshold,交流输入限功率告警阈值,交流限功率告警阈值,%,sint16,1,0,95,90,100,1,,">=Mains Group|Acin Power Limit Early Warning Threshold+10%;
"
市电组,Mains Group,0x800c01,0x280501,parameter,MAINS Start Voltage Enable,MAINS Start Vol.En.,市电启动电压使能,市电启动电压使能,,sint8,1,0,1,0,1,1,0:禁止/Disable;1:允许/Enable;,
市电组,Mains Group,0x800c01,0x280601,parameter,MAINS Start SOC Enable,MAINS Start SOC En.,市电启动SOC使能,市电启动SOC使能,,sint8,1,0,1,0,1,1,0:禁止/Disable;1:允许/Enable;,
市电组,Mains Group,0x800c01,0x280701,parameter,MAINS Start Time Enable,MAINS Start Time En.,市电启动时间使能,市电启动时间使能,,sint8,1,0,1,0,1,1,0:禁止/Disable;1:允许/Enable;,
市电组,Mains Group,0x800c01,0x280801,parameter,MAINS Start System Abnormal Enable,MAINS Start Sys.Abr.En.,市电启动系统异常使能,市电启动异常使能,,sint8,1,0,1,0,1,1,0:禁止/Disable;1:允许/Enable;,
市电组,Mains Group,0x800c01,0x280901,parameter,MAINS Start Voltage Threshold,Mns.Start Volt.,市电启动电压阈值,市电启动电压,V,sint16,1,1,480.0,400.0,562.0,1.0,,">=Diesel Generator Group|DG Start Voltage Threshold;
<=Mains Group|MAINS Stop Voltage Threshold-1V;
>=Battery Group|LLVD1 Voltage+1V;
>=Battery Group|LLVD2 Voltage+1V;
>=Battery Group|BLVD Voltage+1V;
>=DC Distribution|DC DU LLVD Voltage+1V;
>=Smart DC Distribution Unit|Distribution Unit LLVD Voltage Threshold+1V;
"
市电组,Mains Group,0x800c01,0x280a01,parameter,MAINS Start SOC Threshold,Mns.Start SOC,市电启动SOC阈值,市电启动SOC,%,sint16,1,0,70,5,90,1,,">=Diesel Generator Group|DG Start SOC Threshold;
<=Mains Group|MAINS Stop SOC Threshold-5%;
>=Battery Group|LLVD1 SOC Threshold+5%;
>=Battery Group|LLVD2 SOC Threshold+5%;
>=Battery Group|BLVD SOC Threshold+5%;
>=DC Distribution|DC DU LLVD SOC Threshold+5%;
>=Smart DC Distribution Unit|Distribution Unit LLVD SOC Threshold+5%;
"
市电组,Mains Group,0x800c01,0x280b01,parameter,MAINS Start Discharge Duration Threshold,Mns.St.Dis.Dura.,市电启动放电时间阈值,市电启动放电时间,Min,sint16,1,0,150,0,4320,1,,"<=Diesel Generator Group|DG Start Discharge Duration;
<=Battery Group|LLVD1 Duration-30;
<=Battery Group|LLVD2 Duration-30;
<=Battery Group|BLVD Duration-30;
<=DC Distribution|DC DU LLVD Duration-30;
<=Smart DC Distribution Unit|Distribution Unit LLVD Duration-30;
"
市电组,Mains Group,0x800c01,0x280c01,parameter,MAINS Stop Voltage Enable,MAINS Stop Vol.En.,市电停止电压使能,市电停止电压使能,,sint8,1,0,1,0,1,1,0:禁止/Disable;1:允许/Enable;,
市电组,Mains Group,0x800c01,0x280d01,parameter,MAINS Stop SOC Enable,MAINS Stop SOC En.,市电停止SOC使能,市电停止SOC使能,,sint8,1,0,0,0,1,1,0:禁止/Disable;1:允许/Enable;,
市电组,Mains Group,0x800c01,0x280e01,parameter,MAINS Stop Current Enable,MAINS Stop Cur.En.,市电停止电流使能,市电停止电流使能,,sint8,1,0,1,0,1,1,0:禁止/Disable;1:允许/Enable;,
市电组,Mains Group,0x800c01,0x280f01,parameter,MAINS Stop Voltage Threshold,MAINS Stop Volt.,市电停止电压,市电停止电压,V,sint16,1,1,564.0,420.0,587.0,1.0,,">=Mains Group|MAINS Start Voltage Threshold+1V;
>=Battery Group|LLVD1 Voltage+2V;
>=Battery Group|LLVD2 Voltage+2V;
>=Battery Group|BLVD Voltage+2V;
>=DC Distribution|DC DU LLVD Voltage+2V;
>=Smart DC Distribution Unit|Distribution Unit LLVD Voltage Threshold+2V;
"
市电组,Mains Group,0x800c01,0x281001,parameter,MAINS Stop SOC Threshold,MAINS Stop SOC,市电关闭SOC阈值,市电关闭SOC,%,sint16,1,0,95,50,100,1,,">=Mains Group|MAINS Start SOC Threshold+5%;
>=Diesel Generator Group|DG Stop SOC Threshold;
>=Battery Group|LLVD1 SOC Threshold+10%;
>=Battery Group|LLVD2 SOC Threshold+10%;
>=Battery Group|BLVD SOC Threshold+10%;
>=DC Distribution|DC DU LLVD SOC Threshold+10%;
>=Smart DC Distribution Unit|Distribution Unit LLVD SOC Threshold+10%;
"
市电组,Mains Group,0x800c01,0x281101,parameter,MAINS Stop Battery Current,MAINS Stop Bat.Curr.,市电关闭电池电流,市电关闭电流,C10,sint16,1,3,10.0,0.0,500.0,1.0,,
市电组,Mains Group,0x800c01,0x281201,parameter,Min MAINS Running Duration,Min Mns.Run.Dura.,市电最短运行时间,市电最短运行时间,Min,sint16,1,0,60,0,1440,1,,"<=Mains Group|MAINS Max Running Duration;
<=Battery Group|Full Charge Max Duration;
"
市电组,Mains Group,0x800c01,0x281301,parameter,MAINS Max Running Duration,MAINS Max Run.Dura.,市电最长运行时间,市电最长运行时间,Min,sint16,1,0,480,0,1440,1,,">=Mains Group|Min MAINS Running Duration;
"
市电组,Mains Group,0x800c01,0x281401,parameter,Current Walk-In Enable,Cur Walk-In En,电流缓启动使能,电流缓启动使能,,sint8,1,0,0,0,1,1,0:禁止/Disabled;1:允许/Enabled;,
整流器,Rectifier,0x800e01,0x080101,analog data,SMR Output Voltage,Output Voltage,整流器输出电压,输出电压,V,sint16,48,2,4800.0,0.0,6000.0,,,
整流器,Rectifier,0x800e01,0x080201,analog data,SMR Output Current,Output Current,整流器输出电流,输出电流,A,sint32,48,2,0.0,0.0,11000.0,,,
整流器,Rectifier,0x800e01,0x080301,analog data,SMR Internal Temperature,Internal Temp,整流器机内温度,机内温度,℃,sint16,48,1,250.0,-400.0,1000.0,,,
整流器,Rectifier,0x800e01,0x080401,analog data,SMR Maximum Output Current,Max Out Curr,整流器最大输出电流,最大输出电流,A,sint16,48,1,0.0,0.0,1100.0,,,
整流器,Rectifier,0x800e01,0x080501,analog data,SMR Input Voltage,Input Voltage,整流器输入电压,输入电压,V,sint16,48,1,3800.0,0.0,5200.0,,,
整流器,Rectifier,0x800e01,0x080601,analog data,SMR Input Current,Input Current,整流器输入电流,输入电流,A,sint16,48,2,0.0,0.0,5000.0,,,
整流器,Rectifier,0x800e01,0x080701,analog data,SMR In Freq,In Freq,整流器输入频率,输入频率,Hz,sint16,48,1,500.0,0.0,1000.0,,,
整流器,Rectifier,0x800e01,0x080801,analog data,SMR In Power,In Power,整流器输入功率,输入功率,W,sint32,48,1,0.0,0.0,100000.0,,,
整流器,Rectifier,0x800e01,0x080901,analog data,SMR Output Power,Output Power,整流器输出功率,输出功率,W,sint32,48,1,0.0,0.0,100000.0,,,
整流器,Rectifier,0x800e01,0x080a01,analog data,SMR Fan Speed,Fan Speed,整流器风扇转速,风扇转速,RPM,sint32,48,1,0,0,30000,,,
整流器,Rectifier,0x800e01,0x080b01,analog data,SMR Group Address,SMR Group Address,整流器组地址,整流器组地址,,sint16,48,0,1,1,12,,,
整流器,Rectifier,0x800e01,0x080c01,analog data,SMR Group Inner Address,SMR Group Inner Address,整流器组内地址,整流器组内地址,,sint16,48,0,1,1,12,,,
整流器,Rectifier,0x800e01,0x080d01,analog data,SMR Slot Address,SMR Slot Address,整流器槽位地址,整流器槽位地址,,sint16,48,0,1,1,48,,,
整流器,Rectifier,0x800e01,0x080e01,analog data,SMR Positive Bus Volt,Positive Bus Volt,正bus电压,正bus电压,,sint16,48,1,0.0,0.0,5100.0,,,
整流器,Rectifier,0x800e01,0x080f01,analog data,SMR Negative Bus Volt,Negative Bus Volt,负bus电压,负bus电压,,sint16,48,1,0.0,0.0,5100.0,,,
整流器,Rectifier,0x800e01,0x081001,analog data,SMR Bus1 Volt,Bus1 Volt,Bus1电压,Bus1电压,,sint16,48,1,0.0,0.0,5100.0,,,
整流器,Rectifier,0x800e01,0x081101,analog data,SMR Bus2 Volt,Bus2 Volt,Bus2电压,Bus2电压,,sint16,48,1,0.0,0.0,5100.0,,,
整流器,Rectifier,0x800e01,0x081201,analog data,SMR Bus3 Volt,Bus3 Volt,Bus3电压,Bus3电压,,sint16,48,1,0.0,0.0,5100.0,,,
整流器,Rectifier,0x800e01,0x081301,analog data,SMR Bus4 Volt,Bus4 Volt,Bus4电压,Bus4电压,,sint16,48,1,0.0,0.0,5100.0,,,
整流器,Rectifier,0x800e01,0x081401,analog data,SMR Raw Output Current,Raw Output Current,整流器原始输出电流,原始输出电流,A,sint32,48,2,0.0,0.0,11000.0,,,
整流器,Rectifier,0x800e01,0x100101,digital data,Placeholder,Placeholder,占位符,占位符,,uint32_bit,48,0,0,0,1,,0:否/No;1:是/Yes;,
整流器,Rectifier,0x800e01,0x100101,digital data,SMR Off Status,SMR Off,整流器关机状态,关机,,bit1_2,48,0,0,0,1,,0:否/No;1:是/Yes;,
整流器,Rectifier,0x800e01,0x100201,digital data,SMR Current Limit Status,Current Limit,整流器限流状态,限流,,bit3_2,48,0,0,0,1,,0:否/No;1:是/Yes;,
整流器,Rectifier,0x800e01,0x100301,digital data,SMR Input Power Limit,In Power Limit,整流器输入限功率,输入限功率,,bit5_2,48,0,0,0,1,,0:否/No;1:是/Yes;,
整流器,Rectifier,0x800e01,0x100401,digital data,SMR Temperature Power Limit,TH Power Limit,整流器温度限功率,温度限功率,,bit7_2,48,0,0,0,1,,0:否/No;1:是/Yes;,
整流器,Rectifier,0x800e01,0x100501,digital data,SMR Sleep Status,Sleep,整流器休眠状态,休眠,,bit9_2,48,0,0,0,1,,0:否/No;1:是/Yes;,
整流器,Rectifier,0x800e01,0x100601,digital data,SMR P2P Status,P2P,整流器一键功能状态,一键功能,,bit11_2,48,0,0,0,1,,0:否/No;1:是/Yes;,
整流器,Rectifier,0x800e01,0x100701,digital data,SMR Closedown Status,Closedown,整流器闭锁状态,闭锁,,bit13_2,48,0,0,0,1,,0:否/No;1:是/Yes;,
整流器,Rectifier,0x800e01,0x100801,digital data,SMR DC Power Limit,DC Power Limit,整流器输出限功率状态,输出限功率,,bit15_2,48,0,0,0,1,,0:否/No;1:是/Yes;,
整流器,Rectifier,0x800e01,0x100901,digital data,SMR Input Phase,SMR In.Phase,整流器交流输入相位,输入相位,,bit19_4,48,0,0,0,5,,0:L1/L1;1:L2/L2;2:L3/L3;3:L1-L2/L1-L2;4:L3-L1/L3-L1;5:L2-L3/L2-L3;,
整流器,Rectifier,0x800e01,0x100a01,digital data,SMR Fan Control State,SMR Fan Ctrl,整流器风扇控制状态,整流器风扇控制,,bit21_2,48,0,0,0,1,,0:自动/Auto;1:全速/Full Speed;,
整流器,Rectifier,0x800e01,0x100b01,digital data,SMR Software Update Enable,Update Enable,整流器升级使能状态,升级使能状态,,bit23_2,48,0,0,0,1,,0:禁止/Disabled;1:允许/Enabled;,
整流器,Rectifier,0x800e01,0x100c01,digital data,SMR Work Status,Work Status,整流器工作状态,整流器工作状态,,bit27_4,48,0,0,0,4,,0:无/Null;1:正常/Normal;2:告警/Alarm;3:通讯断/CommFail;4:升级/Update;,
整流器,Rectifier,0x800e01,0x103401,digital data,SMR Current Share Type,SMR Curr Share Type,整流器均流类型,整流器均流类型,,bit29_2,48,0,0,0,1,,0:主从均流/Master Slave Curr Share;1:分组均流/Packet Curr Share;,
整流器,Rectifier,0x800e01,0x180101,alarm,SMR Alarm,SMR Alarm,整流器告警,整流器告警,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x480101,alarm DO,SMR Alarm,SMR Alarm,整流器告警,整流器告警,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x180201,alarm,SMR Fan Fault,SMR Fan Fault,整流器风扇故障,整流器风扇故障,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x480201,alarm DO,SMR Fan Fault,SMR Fan Fault,整流器风扇故障,整流器风扇故障,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x180301,alarm,SMR Radiator Temperature High Off,SMR Ra.T.H.O,整流器散热器过温关机,散热器过温关机,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x480301,alarm DO,SMR Radiator Temperature High Off,SMR Ra.T.H.O,整流器散热器过温关机,散热器过温关机,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x180401,alarm,SMR Input Voltage High Off,SMR In.V.H.O,整流器交流过压关机,整流器输入高关,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x480401,alarm DO,SMR Input Voltage High Off,SMR In.V.H.O,整流器交流过压关机,整流器输入高关,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x180501,alarm,SMR Input Voltage Low Off,SMR In.V.L.O,整流器交流欠压关机,整流器输入低关,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x480501,alarm DO,SMR Input Voltage Low Off,SMR In.V.L.O,整流器交流欠压关机,整流器输入低关,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x180601,alarm,SMR Output Voltage High Off,SMR Out.V.H.O,整流器输出过压关机,整流器输出高关,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x480601,alarm DO,SMR Output Voltage High Off,SMR Out.V.H.O,整流器输出过压关机,整流器输出高关,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x180701,alarm,SMR Output Current High,SMR Out.C.H,整流器输出过流,整流器输出过流,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x480701,alarm DO,SMR Output Current High,SMR Out.C.H,整流器输出过流,整流器输出过流,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x180801,alarm,SMR PFC Fault,SMR PFC.Fault,整流器PFC故障,整流器PFC故障,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x480801,alarm DO,SMR PFC Fault,SMR PFC.Fault,整流器PFC故障,整流器PFC故障,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x180901,alarm,SMR Internal Temperature High,SMR Inter.T.H,整流器机内温度过高,整流器机内过温,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x480901,alarm DO,SMR Internal Temperature High,SMR Inter.T.H,整流器机内温度过高,整流器机内过温,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x180a01,alarm,SMR Output Fuse Broken,SMR Out Fuse,整流器输出熔丝断,整流器输出熔丝,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x480a01,alarm DO,SMR Output Fuse Broken,SMR Out Fuse,整流器输出熔丝断,整流器输出熔丝,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x180b01,alarm,SMR Current Share Alarm,SMR Curr Share,整流器均流不良,整流器均流不良,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x480b01,alarm DO,SMR Current Share Alarm,SMR Curr Share,整流器均流不良,整流器均流不良,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x180c01,alarm,SMR AC Input Power Off,SMR Input Off,整流器交流输入断,整流器输入断,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x480c01,alarm DO,SMR AC Input Power Off,SMR Input Off,整流器交流输入断,整流器输入断,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x180d01,alarm,SMR PFC Output Voltage High,PFC Out.V.H,整流器PFC输出过压告警,PFC输出过压,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x480d01,alarm DO,SMR PFC Output Voltage High,PFC Out.V.H,整流器PFC输出过压告警,PFC输出过压,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x180e01,alarm,SMR PFC Output Voltage Low,PFC Out.V.L,整流器PFC输出欠压告警,PFC输出欠压,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x480e01,alarm DO,SMR PFC Output Voltage Low,PFC Out.V.L,整流器PFC输出欠压告警,PFC输出欠压,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x180f01,alarm,SMR EEPROM Fault,SMR EEPROM,整流器EEPROM故障,整流器EEPROM,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x480f01,alarm DO,SMR EEPROM Fault,SMR EEPROM,整流器EEPROM故障,整流器EEPROM,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x181001,alarm,SMR Internal Communication Fail,SMR In.Comm Fail,整流器机内通讯断,整流器内通讯断,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x481001,alarm DO,SMR Internal Communication Fail,SMR In.Comm Fail,整流器机内通讯断,整流器内通讯断,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x181101,alarm,SMR Primary Current High,SMR Primy.C.H,整流器原边过流,整流器原边过流,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x481101,alarm DO,SMR Primary Current High,SMR Primy.C.H,整流器原边过流,整流器原边过流,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x181201,alarm,SMR PFC Input Current High,PFC Input C.H,整流器PFC输入过流,PFC输入过流,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x481201,alarm DO,SMR PFC Input Current High,PFC Input C.H,整流器PFC输入过流,PFC输入过流,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x181301,alarm,SMR Slow Start Abnormal,SMR Start Abr,整流器缓启动异常,整流器启动异常,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x481301,alarm DO,SMR Slow Start Abnormal,SMR Start Abr,整流器缓启动异常,整流器启动异常,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x181401,alarm,SMR Input Fuse Break or Internal Comm.Broken,SMR In.Fuse,整流器输入熔丝断或机内通讯断,整流器输入熔丝,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x481401,alarm DO,SMR Input Fuse Break or Internal Comm.Broken,SMR In.Fuse,整流器输入熔丝断或机内通讯断,整流器输入熔丝,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x181501,alarm,SMR Input Frequency Abnormal,SMR In.Freq,整流器输入频率异常,整流器输入频率,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x481501,alarm DO,SMR Input Frequency Abnormal,SMR In.Freq,整流器输入频率异常,整流器输入频率,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x181601,alarm,SMR Output Voltage Low,SMR Out.V.L,整流器输出欠压,整流器输出欠压,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x481601,alarm DO,SMR Output Voltage Low,SMR Out.V.L,整流器输出欠压,整流器输出欠压,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x181701,alarm,SMR Output Over Power,SMR Out.O.P,整流器输出过载,整流器输出过载,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x481701,alarm DO,SMR Output Over Power,SMR Out.O.P,整流器输出过载,整流器输出过载,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x181801,alarm,SMR SN Clash,SMR SN Clash,整流器序列号冲突,整流器序列号冲突,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x481801,alarm DO,SMR SN Clash,SMR SN Clash,整流器序列号冲突,整流器序列号冲突,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x181901,alarm,SMR Protocol Error,SMR Pro.Err,整流器协议错误,整流器协议错误,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x481901,alarm DO,SMR Protocol Error,SMR Pro.Err,整流器协议错误,整流器协议错误,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x181a01,alarm,SMR Type No Match,SMR No Match,整流器机型不匹配,机型不匹配,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x481a01,alarm DO,SMR Type No Match,SMR No Match,整流器机型不匹配,机型不匹配,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x181b01,alarm,SMR External Over Temperature,SMR Ext.O.T,整流器机外温度过高,整流器机外过温,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x481b01,alarm DO,SMR External Over Temperature,SMR Ext.O.T,整流器机外温度过高,整流器机外过温,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x181c01,alarm,Starting Resistor Temperature High Alarm,Start.R.T.H,启动电阻过热告警,启动电阻过热,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x481c01,alarm DO,Starting Resistor Temperature High Alarm,Start.R.T.H,启动电阻过热告警,启动电阻过热,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x181d01,alarm,SMR Communication Fail,SMR Comm.Fail,整流器通讯中断,整流器通讯中断,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x481d01,alarm DO,SMR Communication Fail,SMR Comm.Fail,整流器通讯中断,整流器通讯中断,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x181e01,alarm,SMR Bus1 Under Volt,Bus1 Un.Volt,整流器Bus1欠压,整流器Bus1欠压,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x481e01,alarm DO,SMR Bus1 Under Volt,Bus1 Un.Volt,整流器Bus1欠压,整流器Bus1欠压,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x181f01,alarm,SMR Bus2 Under Volt,Bus2 Un.Volt,整流器Bus2欠压,整流器Bus2欠压,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x481f01,alarm DO,SMR Bus2 Under Volt,Bus2 Un.Volt,整流器Bus2欠压,整流器Bus2欠压,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x182001,alarm,SMR Bus3 Under Volt,Bus3 Un.Volt,整流器Bus3欠压,整流器Bus3欠压,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x482001,alarm DO,SMR Bus3 Under Volt,Bus3 Un.Volt,整流器Bus3欠压,整流器Bus3欠压,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x182101,alarm,SMR Bus4 Under Volt,Bus4 Un.Volt,整流器Bus4欠压,整流器Bus4欠压,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x482101,alarm DO,SMR Bus4 Under Volt,Bus4 Un.Volt,整流器Bus4欠压,整流器Bus4欠压,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x182201,alarm,SMR Bus1 Over Volt,Bus1 Ov.Volt,整流器Bus1过压,整流器Bus1过压,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x482201,alarm DO,SMR Bus1 Over Volt,Bus1 Ov.Volt,整流器Bus1过压,整流器Bus1过压,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x182301,alarm,SMR Bus2 Over Volt,Bus2 Ov.Volt,整流器Bus2过压,整流器Bus2过压,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x482301,alarm DO,SMR Bus2 Over Volt,Bus2 Ov.Volt,整流器Bus2过压,整流器Bus2过压,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x182401,alarm,SMR Bus3 Over Volt,Bus3 Ov.Volt,整流器Bus3过压,整流器Bus3过压,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x482401,alarm DO,SMR Bus3 Over Volt,Bus3 Ov.Volt,整流器Bus3过压,整流器Bus3过压,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x182501,alarm,SMR Bus4 Over Volt,Bus4 Ov.Volt,整流器Bus4过压,整流器Bus4过压,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x482501,alarm DO,SMR Bus4 Over Volt,Bus4 Ov.Volt,整流器Bus4过压,整流器Bus4过压,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x182601,alarm,SMR Bus Unbalance,SMR Unbalance,整流器Bus不平衡,整流器Bus不平衡,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x482601,alarm DO,SMR Bus Unbalance,SMR Unbalance,整流器Bus不平衡,整流器Bus不平衡,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x182701,alarm,SMR Fault,SMR Fault,整流器故障,整流器故障,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x482701,alarm DO,SMR Fault,SMR Fault,整流器故障,整流器故障,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x182b01,alarm,SMR Output Voltage Low Early Warning,SMR Out.V.L EW.,整流器输出欠压预警,输出欠压预警,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x482b01,alarm DO,SMR Output Voltage Low Early Warning,SMR Out.V.L EW.,整流器输出欠压预警,输出欠压预警,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x182c01,alarm,SMR Output Voltage High Early Warning,SMR Out.V.H EW.,整流器输出过压预警,输出过压预警,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x482c01,alarm DO,SMR Output Voltage High Early Warning,SMR Out.V.H EW.,整流器输出过压预警,输出过压预警,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x182d01,alarm,SMR Input Voltage Low Early Warning,SMR In.V.L EW.,整流器输入欠压预警,输入欠压预警,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x482d01,alarm DO,SMR Input Voltage Low Early Warning,SMR In.V.L EW.,整流器输入欠压预警,输入欠压预警,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x182e01,alarm,SMR Input Voltage High Early Warning,SMR In.V.H EW.,整流器输入过压预警,输入过压预警,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x482e01,alarm DO,SMR Input Voltage High Early Warning,SMR In.V.H EW.,整流器输入过压预警,输入过压预警,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x182f01,alarm,SMR Input Reserve,SMR In.Reserve,整流器输入反接,整流器输入反接,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x482f01,alarm DO,SMR Input Reserve,SMR In.Reserve,整流器输入反接,整流器输入反接,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x183001,alarm,SMR Output Current High Early Warning,SMR Out.C.H EW.,整流器输出过流预警,输出过流预警,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x483001,alarm DO,SMR Output Current High Early Warning,SMR Out.C.H EW.,整流器输出过流预警,输出过流预警,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x183101,alarm,SMR Internal Temperature High Early Warning,SMR Inter.T.H EW.,整流器机内温度过温预警,机内温度过温预警,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器,Rectifier,0x800e01,0x483101,alarm DO,SMR Internal Temperature High Early Warning,SMR Inter.T.H EW.,整流器机内温度过温预警,机内温度过温预警,,sint8,48,0,,,,,,
整流器,Rectifier,0x800e01,0x200201,control,SMR Sleep,SMR Sleep,整流器休眠,整流器休眠,,sint8,48,,,,,,,
整流器,Rectifier,0x800e01,0x200301,control,SMR Waken,SMR Waken,整流器唤醒,整流器唤醒,,sint8,48,,,,,,,
整流器,Rectifier,0x800e01,0x200601,control,SMR Fan Control Enable,SMR Fan Ctrl.En,整流器风扇调速允许,风扇调速允许,,sint8,48,,,,,,,
整流器,Rectifier,0x800e01,0x200701,control,SMR Fan Control Disable,SMR Fan Ctrl.Dis,整流器风扇调速禁止,风扇调速禁止,,sint8,48,,,,,,,
整流器,Rectifier,0x800e01,0x200901,control,SMR Communication Fail alarm clear,Comm.Fail clear,整流器通讯中断告警清除,通讯中断清除,,sint8,48,,,,,,,
整流器,Rectifier,0x800e01,0x400101,device info,SMR ID,SMR ID,整流器ID,整流器ID,,string32,48,,,,,,,
整流器,Rectifier,0x800e01,0x400201,device info,SMR Rating Maximum Output Current,Rating Out.Curr,整流器额定最大输出电流,额定最大输出电流,A,sint16,48,1,635.0,0.0,1500.0,,,
整流器,Rectifier,0x800e01,0x400301,device info,SMR PFC Software Version,SMR PFC Ver,整流器前级软件版本,整流器前级版本,,string16,48,,,,,,,
整流器,Rectifier,0x800e01,0x400401,device info,SMR DC Software Version,SMR DC Ver,整流器后级软件版本,整流器后级版本,,string16,48,,,,,,,
整流器,Rectifier,0x800e01,0x400501,device info,SMR PFC Software Release Date,SMR PFC Date,整流器前级软件发布日期,整流器前级日期,,date,48,,,,,,,
整流器,Rectifier,0x800e01,0x400601,device info,SMR DC Software Release Date,SMR DC Date,整流器后级软件发布日期,整流器后级日期,,date,48,,,,,,,
整流器,Rectifier,0x800e01,0x400701,device info,SMR Digital Control Platform Version,SMR Platform,整流器数控平台版本,整流器平台,,string16,48,,,,,,,
整流器,Rectifier,0x800e01,0x400801,device info,SMR System Name,SMR Sys Name,整流器系统名称,整流器系统名称,,string32,48,,,,,,,
整流器,Rectifier,0x800e01,0x400901,device info,SMR Manufactory ID,SMR Manufty.ID,制造商ID,制造商ID,,sint16,48,0,1,1,1000,,,
整流器,Rectifier,0x800e01,0x400a01,device info,SMR Manufactory Address,Manufty.Address,制造商地址,制造商地址,,sint16,48,0,1,1,1000,,,
整流器,Rectifier,0x800e01,0x400b01,device info,SMR Barcodes,SMR Barcodes,整流器条码,整流器条码,,string16,48,,,,,,,
整流器,Rectifier,0x800e01,0x400c01,device info,SMR Manufacture Date,Manufacture Date,生产日期,生产日期,,date,48,,,,,,,
整流器组,Rectifier Group,0x801001,0x180101,alarm,Multi-SMR Alarm,Multi-SMR Alm,多个整流器模块告警,多个整流器告警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器组,Rectifier Group,0x801001,0x480101,alarm DO,Multi-SMR Alarm,Multi-SMR Alm,多个整流器模块告警,多个整流器告警,,sint8,1,0,,,,,,
整流器组,Rectifier Group,0x801001,0x180201,alarm,All SMR Commfail Alarm,All SMR Commfail,所有整流器通讯断告警,所有整流器通讯断,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
整流器组,Rectifier Group,0x801001,0x480201,alarm DO,All SMR Commfail Alarm,All SMR Commfail,所有整流器通讯断告警,所有整流器通讯断,,sint8,1,0,,,,,,
整流器组,Rectifier Group,0x801001,0x200101,control,Auto Save Control,Auto Save Ctrl,自动节能控制,自动节能控制,,sint8,1,,,,,,,
整流器组,Rectifier Group,0x801001,0x200201,control,Temporary NonSave Control,Temp NonSave Ctrl,暂时非节能控制,暂时非节能控制,,sint8,1,,,,,,,
整流器组,Rectifier Group,0x801001,0x200301,control,Permanent NonSave Control,Perm NonSave Ctrl,永久非节能控制,永久非节能控制,,sint8,1,,,,,,,
整流器组,Rectifier Group,0x801001,0x200401,control,Manual Detect,Manual Detect,人工维护检测,人工维护检测,,sint8,1,,,,,,,
整流器组,Rectifier Group,0x801001,0x200501,control,SMR Device Statistic,SMR Dev Stat,SMR设备统计,SMR设备统计,,sint8,1,,,,,,,
整流器组,Rectifier Group,0x801001,0x200701,control,Start Auto Save Mode Manually,Start Auto Save,开启自动节能模式,开启自动节能,,sint8,1,,,,,,,
整流器组,Rectifier Group,0x801001,0x280101,parameter,AC Save Energy Mode,AC Save Eng.Mode,交流节能模式,交流节能模式,,sint8,1,0,0,0,2,1,0:安全/Safe;1:节能/Save;2:自由/Free;,
整流器组,Rectifier Group,0x801001,0x280201,parameter,SMR Rotated Period,SMR Rota Period,整流器轮换周期,整流器轮换周期,Day,sint16,1,0,7,0,30,1,,
整流器组,Rectifier Group,0x801001,0x280301,parameter,Minimum Qty of Started SMRs,Min Num Start SMR,整流器最小开机数量,整流器最小开机数,,sint16,1,0,3,1,3,1,,
整流器组,Rectifier Group,0x801001,0x280401,parameter,Temporary NonSave Delay,Temp NonSave Delay,暂时非节能延时时间,暂时非节能延时,Min,sint16,1,0,1440,0,5940,1,,
整流器组,Rectifier Group,0x801001,0x280601,parameter,SMR Soft Start Interval,Soft Start Inter,整流器软启动间隔,软启动间隔,Sec,sint16,1,0,0,0,128,1,,
整流器组,Rectifier Group,0x801001,0x280701,parameter,SMR Output High Off Voltage,Out High Off Volt,整流器输出高停机电压,输出高停机电压,V,sint16,1,1,610.0,565.0,620.0,1.0,,">=Battery Group|Equalized Charge Voltage+2V;
>=Battery Group|Charge Voltage+2V;
>=Battery Group|Float Charge Voltage+2V;
"
整流器组,Rectifier Group,0x801001,0x280801,parameter,SMR Default Output Voltage,Def Out Volt,整流器默认输出电压,默认输出电压,V,sint16,1,2,5350.0,4200.0,5800.0,10.0,,
整流器组,Rectifier Group,0x801001,0x280901,parameter,SMR Default Current Limit Rate,SMR Def CL Rate,整流器默认限流点比率,默认限流点比率,‰,sint16,1,0,1000,80,1000,1,,
整流器组,Rectifier Group,0x801001,0x280b01,parameter,Save Load Rate Maximum,Load Rate Max,节能带载率上限,带载率上限,,sint16,1,2,90.0,20.0,100.0,1.0,,">=Save Load Rate Minimum+0.1;
"
整流器组,Rectifier Group,0x801001,0x280c01,parameter,Save Load Rate Minimum,Load Rate Min,节能带载率下限,带载率下限,,sint16,1,2,40.0,10.0,90.0,1.0,,"<=Save Load Rate Maximum-0.1;
"
整流器组,Rectifier Group,0x801001,0x280d01,parameter,Current Walk-In Enable,Cur Walk-In En,电流缓启动使能,电流缓启动使能,,sint8,1,0,0,0,1,1,0:禁止/Disabled;1:允许/Enabled;,
整流器组,Rectifier Group,0x801001,0x280e01,parameter,Current Walk-In Time,Cur Walk-In Time,电流缓启动时间,电流缓启动时间,Sec,sint16,1,0,8,8,200,1,,
系统运行环境,System Running Environment,0x801201,0x080101,analog data,Environment Temperature,Environment Temp,环境温度,环境温度,℃,sint16,1,0,25,-40,100,,,
系统运行环境,System Running Environment,0x801201,0x080201,analog data,Environment Humidity,Environment Hum,环境湿度,环境湿度,%,sint16,1,0,50,0,100,,,
系统运行环境,System Running Environment,0x801201,0x100501,digital data,Environment Temperature Sensor Status,Env.Temp.Sensor,环境温度传感器状态,环境温度传感器,,sint8,1,0,0,0,1,,0:正常/Normal;1:异常/Fault;2:未配置/No Config;,
系统运行环境,System Running Environment,0x801201,0x100601,digital data,Environment Humidity Sensor Status,Env.Hum.Sensor,环境湿度传感器状态,环境湿度传感器,,sint8,1,0,0,0,1,,0:正常/Normal;1:异常/Fault;,
系统运行环境,System Running Environment,0x801201,0x180101,alarm,Smog Alarm,Smog Alarm,烟雾告警,烟雾告警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
系统运行环境,System Running Environment,0x801201,0x480101,alarm DO,Smog Alarm,Smog Alarm,烟雾告警,烟雾告警,,sint8,1,0,,,,,,
系统运行环境,System Running Environment,0x801201,0x180201,alarm,Flood Alarm,Flood Alarm,水淹告警,水淹告警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
系统运行环境,System Running Environment,0x801201,0x480201,alarm DO,Flood Alarm,Flood Alarm,水淹告警,水淹告警,,sint8,1,0,,,,,,
系统运行环境,System Running Environment,0x801201,0x180301,alarm,Door Alarm,Door Alarm,门磁告警,门磁告警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
系统运行环境,System Running Environment,0x801201,0x480301,alarm DO,Door Alarm,Door Alarm,门磁告警,门磁告警,,sint8,1,0,,,,,,
系统运行环境,System Running Environment,0x801201,0x180401,alarm,Access Control Alarm,Access Ctrl.Alm.,门禁告警,门禁告警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
系统运行环境,System Running Environment,0x801201,0x480401,alarm DO,Access Control Alarm,Access Ctrl.Alm.,门禁告警,门禁告警,,sint8,1,0,,,,,,
系统运行环境,System Running Environment,0x801201,0x180501,alarm,Environment Temperature Invalid,Env.Temp.Invalid,环境温度无效,环境温度无效,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
系统运行环境,System Running Environment,0x801201,0x480501,alarm DO,Environment Temperature Invalid,Env.Temp.Invalid,环境温度无效,环境温度无效,,sint8,1,0,,,,,,
系统运行环境,System Running Environment,0x801201,0x180601,alarm,Environment Temperature High,Env.Temp. High,环境温度高,环境温度高,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
系统运行环境,System Running Environment,0x801201,0x480601,alarm DO,Environment Temperature High,Env.Temp. High,环境温度高,环境温度高,,sint8,1,0,,,,,,
系统运行环境,System Running Environment,0x801201,0x180701,alarm,Environment Temperature Low,Env.Temp. Low,环境温度低,环境温度低,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
系统运行环境,System Running Environment,0x801201,0x480701,alarm DO,Environment Temperature Low,Env.Temp. Low,环境温度低,环境温度低,,sint8,1,0,,,,,,
系统运行环境,System Running Environment,0x801201,0x180801,alarm,Environment Humidity Invalid,Env.Hum.Invalid,环境湿度无效,环境湿度无效,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
系统运行环境,System Running Environment,0x801201,0x480801,alarm DO,Environment Humidity Invalid,Env.Hum.Invalid,环境湿度无效,环境湿度无效,,sint8,1,0,,,,,,
系统运行环境,System Running Environment,0x801201,0x180901,alarm,Environment Humidity High,Env.Hum. High,环境湿度高,环境湿度高,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
系统运行环境,System Running Environment,0x801201,0x480901,alarm DO,Environment Humidity High,Env.Hum. High,环境湿度高,环境湿度高,,sint8,1,0,,,,,,
系统运行环境,System Running Environment,0x801201,0x180a01,alarm,Environment Humidity Low,Env.Hum. Low,环境湿度低,环境湿度低,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
系统运行环境,System Running Environment,0x801201,0x480a01,alarm DO,Environment Humidity Low,Env.Hum. Low,环境湿度低,环境湿度低,,sint8,1,0,,,,,,
系统运行环境,System Running Environment,0x801201,0x180b01,alarm,Environment Temperature Too High,Env.Temp.Too H.,环境温度过高,环境温度过高,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
系统运行环境,System Running Environment,0x801201,0x480b01,alarm DO,Environment Temperature Too High,Env.Temp.Too H.,环境温度过高,环境温度过高,,sint8,1,0,,,,,,
系统运行环境,System Running Environment,0x801201,0x180c01,alarm,Environment Temperature Too Low,Env.Temp.Too L.,环境温度过低,环境温度过低,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
系统运行环境,System Running Environment,0x801201,0x480c01,alarm DO,Environment Temperature Too Low,Env.Temp.Too L.,环境温度过低,环境温度过低,,sint8,1,0,,,,,,
系统运行环境,System Running Environment,0x801201,0x180d01,alarm,Temperature Control Unit Alarm,T.Ctrl Unit Alm,温控单元异常,温控单元异常,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
系统运行环境,System Running Environment,0x801201,0x480d01,alarm DO,Temperature Control Unit Alarm,T.Ctrl Unit Alm,温控单元异常,温控单元异常,,sint8,1,0,,,,,,
系统运行环境,System Running Environment,0x801201,0x180e01,alarm,DC Air Conditioner Alarm,DC Air Cond.Alm.,直流空调异常,直流空调异常,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
系统运行环境,System Running Environment,0x801201,0x480e01,alarm DO,DC Air Conditioner Alarm,DC Air Cond.Alm.,直流空调异常,直流空调异常,,sint8,1,0,,,,,,
系统运行环境,System Running Environment,0x801201,0x180f01,alarm,Heater Fault Alarm,Heater Fault.Alm.,加热器异常,加热器异常,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
系统运行环境,System Running Environment,0x801201,0x480f01,alarm DO,Heater Fault Alarm,Heater Fault.Alm.,加热器异常,加热器异常,,sint8,1,0,,,,,,
系统运行环境,System Running Environment,0x801201,0x181001,alarm,External Cabinet Smog Alarm,Ext.Cab.Smog Alm.,扩展柜烟雾告警,扩展柜烟雾告警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
系统运行环境,System Running Environment,0x801201,0x481001,alarm DO,External Cabinet Smog Alarm,Ext.Cab.Smog Alm.,扩展柜烟雾告警,扩展柜烟雾告警,,sint8,1,0,,,,,,
系统运行环境,System Running Environment,0x801201,0x280201,parameter,Environment Temperature High Threshold,Env.Temp.H.Thre.,环境温度高阈值,环境过温值,℃,sint16,1,0,47,30,60,1,,"<=Environment Temperature Too High Threshold-3℃;
"
系统运行环境,System Running Environment,0x801201,0x280301,parameter,Environment Temperature Low Threshold,Env.Temp.L.Thre.,环境温度低阈值,环境低温值,℃,sint16,1,0,-5,-30,20,1,,">=Environment Temperature Too Low Threshold+3℃;
"
系统运行环境,System Running Environment,0x801201,0x280401,parameter,Environment Humidity High Threshold,Env.Hum.H.Thre.,环境湿度高阈值,环境湿度高阈值,%,sint16,1,0,90,70,100,1,,
系统运行环境,System Running Environment,0x801201,0x280501,parameter,Environment Humidity Low Threshold,Env.Hum.L.Thre.,环境湿度低阈值,环境湿度低阈值,%,sint16,1,0,20,10,50,1,,
系统运行环境,System Running Environment,0x801201,0x280601,parameter,Environment Temperature Too High Threshold,Env.Temp.Too H.Thre.,环境温度过高阈值,环境温度过高阈值,℃,sint16,1,0,50,33,63,1,,">=Environment Temperature High Threshold+3℃;
"
系统运行环境,System Running Environment,0x801201,0x280701,parameter,Environment Temperature Too Low Threshold,Env.Temp.Too L.Thre.,环境温度过低阈值,环境温度过低阈值,℃,sint16,1,0,-8,-33,17,1,,"<=Environment Temperature Low Threshold-3℃;
"
直流配电,DC Distribution,0x801401,0x080701,analog data,LLVD1 Load Current,LLVD1 Load Curr.,一次下电回路负载电流,一次下电回路电流,A,sint32,1,2,0.0,0.0,300000.0,,,
直流配电,DC Distribution,0x801401,0x080801,analog data,LLVD1 Load Power,LLVD1 Load Pwr.,一次下电回路负载功率,一次下电回路功率,kW,sint32,1,2,0.0,0.0,60000.0,,,
直流配电,DC Distribution,0x801401,0x080901,analog data,LLVD2 Load Current,LLVD2 Load Curr.,二次下电回路负载电流,二次下电回路电流,A,sint32,1,2,0.0,0.0,300000.0,,,
直流配电,DC Distribution,0x801401,0x080a01,analog data,LLVD2 Load Power,LLVD2 Load Pwr.,二次下电回路负载功率,二次下电回路功率,kW,sint32,1,2,0.0,0.0,60000.0,,,
直流配电,DC Distribution,0x801401,0x080b01,analog data,BLVD Load Current,BLVD Load Curr.,电池下电回路负载电流,电池下电回路电流,A,sint32,1,2,0.0,0.0,300000.0,,,
直流配电,DC Distribution,0x801401,0x080c01,analog data,BLVD Load Power,BLVD Load Pwr.,电池下电回路负载功率,电池下电回路功率,kW,sint32,1,2,0.0,0.0,60000.0,,,
直流配电,DC Distribution,0x801401,0x080d01,analog data,Tenant Load Current,Ten.Load Curr.,租户负载电流,租户负载电流,A,sint32,8,2,0.0,0.0,300000.0,,,
直流配电,DC Distribution,0x801401,0x080e01,analog data,Public Load Current,Pub.Load Curr.,公用负载电流,公用负载电流,A,sint32,1,2,0.0,0.0,300000.0,,,
直流配电,DC Distribution,0x801401,0x080f01,analog data,Distribution Unit Current,Dist.Unit Curr.,配电单元电流,配电单元电流,A,sint32,4,2,0.0,0.0,300000.0,,,
直流配电,DC Distribution,0x801401,0x081001,analog data,DC Output Voltage,DC Out.Vol.,直流输出电压,直流输出电压,V,sint16,1,2,4800.0,0.0,6000.0,,,
直流配电,DC Distribution,0x801401,0x180101,alarm,DCLP Abnormal,DCLP Abr,直流防雷异常,直流防雷异常,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
直流配电,DC Distribution,0x801401,0x480101,alarm DO,DCLP Abnormal,DCLP Abr,直流防雷异常,直流防雷异常,,sint8,1,0,,,,,,
直流配电,DC Distribution,0x801401,0x180201,alarm,DC Loop Broken,DC.Loop.Brk,直流负载回路断,负载回路断,,sint8,8,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
直流配电,DC Distribution,0x801401,0x480201,alarm DO,DC Loop Broken,DC.Loop.Brk,直流负载回路断,负载回路断,,sint8,8,0,,,,,,
直流配电,DC Distribution,0x801401,0x180301,alarm,Load Extend Loop Broken,Load Ext.Brk.,直流负载扩展分路断,负载扩展分路断,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
直流配电,DC Distribution,0x801401,0x480301,alarm DO,Load Extend Loop Broken,Load Ext.Brk.,直流负载扩展分路断,负载扩展分路断,,sint8,1,0,,,,,,
直流配电,DC Distribution,0x801401,0x180401,alarm,DC Voltage High,DC Volt.High,直流电压高,直流电压高,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
直流配电,DC Distribution,0x801401,0x480401,alarm DO,DC Voltage High,DC Volt.High,直流电压高,直流电压高,,sint8,1,0,,,,,,
直流配电,DC Distribution,0x801401,0x180501,alarm,DC Voltage Low,DC Volt.Low,直流电压低,直流电压低,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
直流配电,DC Distribution,0x801401,0x480501,alarm DO,DC Voltage Low,DC Volt.Low,直流电压低,直流电压低,,sint8,1,0,,,,,,
直流配电,DC Distribution,0x801401,0x180601,alarm,LLVD1 Loop Broken,LLVD1 Loop Brk.,一次下电分路断,一次下电分路断,,sint8,8,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
直流配电,DC Distribution,0x801401,0x480601,alarm DO,LLVD1 Loop Broken,LLVD1 Loop Brk.,一次下电分路断,一次下电分路断,,sint8,8,0,,,,,,
直流配电,DC Distribution,0x801401,0x180701,alarm,LLVD1 Extend Loop Broken,LLVD1 Exten.Brk.,一次下电扩展分路断,一次下电扩展断,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
直流配电,DC Distribution,0x801401,0x480701,alarm DO,LLVD1 Extend Loop Broken,LLVD1 Exten.Brk.,一次下电扩展分路断,一次下电扩展断,,sint8,1,0,,,,,,
直流配电,DC Distribution,0x801401,0x180801,alarm,LLVD2 Loop Broken,LLVD2 Loop Brk.,二次下电分路断,二次下电分路断,,sint8,8,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
直流配电,DC Distribution,0x801401,0x480801,alarm DO,LLVD2 Loop Broken,LLVD2 Loop Brk.,二次下电分路断,二次下电分路断,,sint8,8,0,,,,,,
直流配电,DC Distribution,0x801401,0x180901,alarm,LLVD2 Extend Loop Broken,LLVD2 Exten.Brk.,二次下电扩展分路断,二次下电扩展断,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
直流配电,DC Distribution,0x801401,0x480901,alarm DO,LLVD2 Extend Loop Broken,LLVD2 Exten.Brk.,二次下电扩展分路断,二次下电扩展断,,sint8,1,0,,,,,,
直流配电,DC Distribution,0x801401,0x180a01,alarm,BLVD Loop Broken,BLVD Loop Brk.,电池下电分路断,电池下电分路断,,sint8,8,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
直流配电,DC Distribution,0x801401,0x480a01,alarm DO,BLVD Loop Broken,BLVD Loop Brk.,电池下电分路断,电池下电分路断,,sint8,8,0,,,,,,
直流配电,DC Distribution,0x801401,0x180b01,alarm,BLVD Extend Loop Broken,BLVD Exten.Brk.,电池下电扩展分路断,电池下电扩展断,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
直流配电,DC Distribution,0x801401,0x480b01,alarm DO,BLVD Extend Loop Broken,BLVD Exten.Brk.,电池下电扩展分路断,电池下电扩展断,,sint8,1,0,,,,,,
直流配电,DC Distribution,0x801401,0x180c01,alarm,LLVD1 Load Module Alarm,LLVD1 L.Mod.Alm.,一次下电回路模组告警,一次下电回路模组告警,,sint8,8,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
直流配电,DC Distribution,0x801401,0x480c01,alarm DO,LLVD1 Load Module Alarm,LLVD1 L.Mod.Alm.,一次下电回路模组告警,一次下电回路模组告警,,sint8,8,0,,,,,,
直流配电,DC Distribution,0x801401,0x180d01,alarm,LLVD2 Load Module Alarm,LLVD2 L.Mod.Alm.,二次下电回路模组告警,二次下电回路模组告警,,sint8,8,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
直流配电,DC Distribution,0x801401,0x480d01,alarm DO,LLVD2 Load Module Alarm,LLVD2 L.Mod.Alm.,二次下电回路模组告警,二次下电回路模组告警,,sint8,8,0,,,,,,
直流配电,DC Distribution,0x801401,0x180e01,alarm,DC Voltage Too High,DC Volt.T.High,直流电压过高,直流电压过高,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
直流配电,DC Distribution,0x801401,0x480e01,alarm DO,DC Voltage Too High,DC Volt.T.High,直流电压过高,直流电压过高,,sint8,1,0,,,,,,
直流配电,DC Distribution,0x801401,0x180f01,alarm,DC Voltage Too Low,DC Volt.T.Low,直流电压过低,直流电压过低,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
直流配电,DC Distribution,0x801401,0x480f01,alarm DO,DC Voltage Too Low,DC Volt.T.Low,直流电压过低,直流电压过低,,sint8,1,0,,,,,,
直流配电,DC Distribution,0x801401,0x181001,alarm,BLVD Load Module Alarm,BLVD L.Mod.Alm.,电池下电回路模组告警,电池下电回路模组告警,,sint8,8,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
直流配电,DC Distribution,0x801401,0x481001,alarm DO,BLVD Load Module Alarm,BLVD L.Mod.Alm.,电池下电回路模组告警,电池下电回路模组告警,,sint8,8,0,,,,,,
直流配电,DC Distribution,0x801401,0x181101,alarm,DC DU LLVD1 Loop Broken,DU LLVD1 Loop Brk.,直流配电单元一次下电分路断,配电单元一次下电分路断,,sint8,4,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
直流配电,DC Distribution,0x801401,0x481101,alarm DO,DC DU LLVD1 Loop Broken,DU LLVD1 Loop Brk.,直流配电单元一次下电分路断,配电单元一次下电分路断,,sint8,4,0,,,,,,
直流配电,DC Distribution,0x801401,0x181201,alarm,DC DU LLVD2 Loop Broken,DU LLVD2 Loop Brk.,直流配电单元二次下电分路断,配电单元二次下电分路断,,sint8,4,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
直流配电,DC Distribution,0x801401,0x481201,alarm DO,DC DU LLVD2 Loop Broken,DU LLVD2 Loop Brk.,直流配电单元二次下电分路断,配电单元二次下电分路断,,sint8,4,0,,,,,,
直流配电,DC Distribution,0x801401,0x181301,alarm,DC DU BLVD Loop Broken,DU BLVD Loop Brk.,直流配电单元电池下电分路断,配电单元电池下电分路断,,sint8,4,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
直流配电,DC Distribution,0x801401,0x481301,alarm DO,DC DU BLVD Loop Broken,DU BLVD Loop Brk.,直流配电单元电池下电分路断,配电单元电池下电分路断,,sint8,4,0,,,,,,
直流配电,DC Distribution,0x801401,0x181401,alarm,Common LLVD2 Loop Broken,Comn.LLVD2 L.Brk.,公用二次下电分路断,公用二次下电分路断,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
直流配电,DC Distribution,0x801401,0x481401,alarm DO,Common LLVD2 Loop Broken,Comn.LLVD2 L.Brk.,公用二次下电分路断,公用二次下电分路断,,sint8,1,0,,,,,,
直流配电,DC Distribution,0x801401,0x181501,alarm,Common BLVD Loop Broken,Comn.BLVD L.Brk.,公用电池下电分路断,公用电池下电分路断,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
直流配电,DC Distribution,0x801401,0x481501,alarm DO,Common BLVD Loop Broken,Comn.BLVD L.Brk.,公用电池下电分路断,公用电池下电分路断,,sint8,1,0,,,,,,
直流配电,DC Distribution,0x801401,0x181601,alarm,DC SPD Abnormal,DC SPD Abr.,直流防雷器异常,直流防雷器异常,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
直流配电,DC Distribution,0x801401,0x481601,alarm DO,DC SPD Abnormal,DC SPD Abr.,直流防雷器异常,直流防雷器异常,,sint8,1,0,,,,,,
直流配电,DC Distribution,0x801401,0x181901,alarm,DC DU LLVD Alarm,DU LLVD Alm.,直流配电单元下电告警,配电单元下电告警,,sint8,4,0,,,,,1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
直流配电,DC Distribution,0x801401,0x481901,alarm DO,DC DU LLVD Alarm,DU LLVD Alm.,直流配电单元下电告警,配电单元下电告警,,sint8,4,0,,,,,,
直流配电,DC Distribution,0x801401,0x181a01,alarm,DC Output Loop Broken,DC Out.Loop Brk.,直流输出回路断,直流输出回路断,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
直流配电,DC Distribution,0x801401,0x481a01,alarm DO,DC Output Loop Broken,DC Out.Loop Brk.,直流输出回路断,直流输出回路断,,sint8,1,0,,,,,,
直流配电,DC Distribution,0x801401,0x181b01,alarm,DC Output Over Voltage Protection,DC Out.OVP.,直流输出过压保护,直流输出过压保护,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
直流配电,DC Distribution,0x801401,0x481b01,alarm DO,DC Output Over Voltage Protection,DC Out.OVP.,直流输出过压保护,直流输出过压保护,,sint8,1,0,,,,,,
直流配电,DC Distribution,0x801401,0x181801,alarm,Control CB Alarm,Control CB Alarm,外部控制开关断,外部控制开关断,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
直流配电,DC Distribution,0x801401,0x481801,alarm DO,Control CB Alarm,Control CB Alarm,外部控制开关断,外部控制开关断,,sint8,1,0,,,,,,
直流配电,DC Distribution,0x801401,0x280101,parameter,DC Voltage High Threshold,DC Volt.High Thre.,直流电压高阈值,直流电压高阈值,V,sint16,1,1,585.0,530.0,610.0,1.0,,">=DC Distribution|DC Voltage Low Threshold+1V;
<=DC Distribution|DC Voltage Too High Threshold-1V;
"
直流配电,DC Distribution,0x801401,0x280201,parameter,DC Voltage Low Threshold,DC Volt.Low Thre.,直流电压低阈值,直流电压低阈值,V,sint16,1,1,465.0,440.0,570.0,1.0,,"<=DC Distribution|DC Voltage High Threshold-1V;
>=DC Distribution|DC Voltage Too Low Threshold+1V;
"
直流配电,DC Distribution,0x801401,0x280501,parameter,DC Voltage Too High Threshold,DC V.T.High Thre.,直流电压过高阈值,直流电压过高值,V,sint16,1,1,595.0,530.0,610.0,1.0,,">=DC Distribution|DC Voltage High Threshold+1V;
"
直流配电,DC Distribution,0x801401,0x280601,parameter,DC Voltage Too Low Threshold,DC V.T.Low Thre.,直流电压过低阈值,直流电压过低值,V,sint16,1,1,455.0,440.0,570.0,1.0,,"<=DC Distribution|DC Voltage Low Threshold-1V;
"
直流配电,DC Distribution,0x801401,0x280701,parameter,Tenant Name,Tenant Name,租户名称,租户名称,,string64,8,,Tenant-X#,,,,,
直流配电,DC Distribution,0x801401,0x280901,parameter,DC DU LLVD Enabled,DU LLVD En.,直流配电单元下电使能,配电单元下电使能,,sint8,4,0,1,0,1,1,0:禁止/Disabled;1:允许/Enabled;,
直流配电,DC Distribution,0x801401,0x280a01,parameter,DC DU LLVD Duration,DU LLVD Dura.,直流配电单元下电时间,配电单元下电时间,Min,sint16,4,0,1440,3,7200,1,,">=Diesel Generator Group|DG Start Discharge Duration+30;
>=Mains Group|MAINS Start Discharge Duration Threshold+30;
"
直流配电,DC Distribution,0x801401,0x280b01,parameter,DC DU LLVD Voltage,DU LLVD Volt.,直流配电单元下电电压,配电单元下电电压,V,sint16,4,1,450.0,380.0,580.0,1.0,,"<=Battery Group|Test Stop Voltage-1V;
<=Battery Group|Battery Voltage Low Threshold-1V;
<=Diesel Generator Group|DG Start Voltage Threshold-0.5V;
<=Diesel Generator Group|DG Stop Voltage Threshold-2V;
<=Mains Group|MAINS Start Voltage Threshold-1V;
<=Mains Group|MAINS Stop Voltage Threshold-2V;
"
直流配电,DC Distribution,0x801401,0x280c01,parameter,DC DU LLVD SOC Threshold,DU LLVD SOC,直流配电单元下电SOC阈值,配电单元下电SOC,%,sint16,4,0,20,2,90,1,,"<=Battery Group|Test Stop SOC Threshold-5%;
<=Diesel Generator Group|DG Start SOC Threshold-5%;
<=Diesel Generator Group|DG Stop SOC Threshold-10%;
<=Mains Group|MAINS Start SOC Threshold-5%;
<=Mains Group|MAINS Stop SOC Threshold-10%;
"
直流配电,DC Distribution,0x801401,0x280d01,parameter,DC DU LLVD Remote Enabled,DU LLVD Remote En.,直流配电单元远程下电使能,配电单元远程下电使能,,sint8,4,0,0,0,1,1,0:禁止/Disabled;1:允许/Enabled;,
直流配电,DC Distribution,0x801401,0x280e01,parameter,DC DU LLVD Remote,DU LLVD Remote,直流配电单元远程下电,配电单元远程下电,,sint8,4,0,0,0,1,1,0:上电/Recover;1:下电/Disconnect;,
直流配电,DC Distribution,0x801401,0x280f01,parameter,Distribution Unit Tenant Config,Dist.Unit Tenant Cfg.,配电单元租户设置,配电单元租户设置,,sint8,4,0,0,0,4,1,0:无/None;1:租户1/Tenant1;2:租户2/Tenant2;3:租户3/Tenant3;4:租户4/Tenant4;5:租户5/Tenant5;6:租户6/Tenant6;7:租户7/Tenant7;8:租户8/Tenant8;,
直流配电,DC Distribution,0x801401,0x281001,parameter,Distribution Unit Name,Dist.Unit Name,配电单元名称,配电单元名称,,string64,4,,Dist.Unit-X#,,,,,
直流配电,DC Distribution,0x801401,0x281701,parameter,Distribution Unit Fixed Time Disconnect Enabled,DU FixT.Disc En.,配电单元定时下电使能,配电单元定时下电使能,,sint8,4,0,0,0,1,1,0:禁止/Disabled;1:允许/Enabled;,
直流配电,DC Distribution,0x801401,0x281801,parameter,Distribution Unit Fixed Time Disconnect Start Clock1,DU FixT.Disc St.1,配电单元定时下电起始时刻1,定时下电起始时刻1,,hour_minute,5,0,8:00,0:00,23:59,1,,
直流配电,DC Distribution,0x801401,0x281901,parameter,Distribution Unit Fixed Time Disconnect End Clock1,DU FixT.Disc End.1,配电单元定时下电终止时刻1,定时下电终止时刻1,,hour_minute,5,0,8:00,0:00,23:59,1,,
直流配电,DC Distribution,0x801401,0x281a01,parameter,Distribution Unit Fixed Time Disconnect Start Clock2,DU FixT.Disc St.2,配电单元定时下电起始时刻2,定时下电起始时刻2,,hour_minute,5,0,8:00,0:00,23:59,1,,
直流配电,DC Distribution,0x801401,0x281b01,parameter,Distribution Unit Fixed Time Disconnect End Clock2,DU FixT.Disc End.2,配电单元定时下电终止时刻2,定时下电终止时刻2,,hour_minute,5,0,8:00,0:00,23:59,1,,
直流配电,DC Distribution,0x801401,0x281c01,parameter,Distribution Unit Fixed Time Disconnect Start Clock3,DU FixT.Disc St.3,配电单元定时下电起始时刻3,定时下电起始时刻3,,hour_minute,5,0,8:00,0:00,23:59,1,,
直流配电,DC Distribution,0x801401,0x281d01,parameter,Distribution Unit Fixed Time Disconnect End Clock3,DU FixT.Disc End.3,配电单元定时下电终止时刻3,定时下电终止时刻3,,hour_minute,5,0,8:00,0:00,23:59,1,,
直流配电,DC Distribution,0x801401,0x281e01,parameter,Distribution Unit Fixed Time Disconnect Start Clock4,DU FixT.Disc St.4,配电单元定时下电起始时刻4,定时下电起始时刻4,,hour_minute,5,0,8:00,0:00,23:59,1,,
直流配电,DC Distribution,0x801401,0x281f01,parameter,Distribution Unit Fixed Time Disconnect End Clock4,DU FixT.Disc End.4,配电单元定时下电终止时刻4,定时下电终止时刻4,,hour_minute,5,0,8:00,0:00,23:59,1,,
直流配电,DC Distribution,0x801401,0x281101,parameter,DC Output Loop Broken Threshold,DC Out.Loop Brk.Thres.,直流输出回路断阈值,直流输出回路断阈值,V,sint8,1,1,5.0,1.0,8.0,1.0,,
直流配电,DC Distribution,0x801401,0x281201,parameter,DC Output Over Voltage Protection Threshold,DC Out.OVP.Thres.,直流输出过压保护阈值,直流输出过压保护阈值,V,sint16,1,1,595.0,590.0,600.0,1.0,,
直流配电,DC Distribution,0x801401,0x281301,parameter,DC Voltage Detect Interval,DC Vol.Det.Inter.,直流电压检测间隔,直流电压检测间隔,Min,sint16,1,0,30,10,1440,1,,
直流配电,DC Distribution,0x801401,0x281401,parameter,DC Output Low Off Enable,DC Out.Low Off En.,直流输出低关机使能,直流输出低关机使能,,sint8,1,0,1,0,1,1,0:禁止/Disabled;1:允许/Enabled;,
直流配电,DC Distribution,0x801401,0x281501,parameter,DC Output Voltage Minimum Threshold,DC Out.Vol.Min.Thres.,直流输出最低电压阈值,直流输出最低电压阈值,V,sint16,1,1,470.0,380.0,580.0,1.0,,
直流配电,DC Distribution,0x801401,0x282001,parameter,Busbar Reference Voltage Deviation Threshold,Busbar Ref.Vol.Dev.Thre.,母排基准电压偏差阈值,母排基准电压偏差阈值,V,sint8,1,1,9.0,0.0,15.0,1.0,,
直流配电,DC Distribution,0x801401,0x282101,parameter,DC Output Voltage Maximinum Threshold,DC Out.Vol.Max.Thres.,直流输出最高电压阈值,直流输出最高电压阈值,V,sint16,1,1,580.0,530.0,580.0,1.0,,
直流配电,DC Distribution,0x801401,0x380101,stastic data,LLVD1 Load Energy Consumption,LLVD1 Load NRG Cons.,一次下电回路负载电量,一次下电回路电量,kWh,sint32,1,2,,,,,,
直流配电,DC Distribution,0x801401,0x380201,stastic data,LLVD2 Load Energy Consumption,LLVD2 Load NRG Cons.,二次下电回路负载电量,二次下电回路电量,kWh,sint32,1,2,,,,,,
直流配电,DC Distribution,0x801401,0x380301,stastic data,BLVD Load Energy Consumption,BLVD Load NRG Cons.,电池下电回路负载电量,电池下电回路电量,kWh,sint32,1,2,,,,,,
直流配电,DC Distribution,0x801401,0x380401,stastic data,Tenant Total Energy Consumption,Ten.NRG Cons.,租户总耗电量,租户总电量,kWh,sint32,8,2,,,,,,
直流配电,DC Distribution,0x801401,0x380501,stastic data,Public Total Energy consumption,Pub.NRG Cons.,公用总耗电量,公用总电量,kWh,sint32,1,2,,,,,,
直流配电,DC Distribution,0x801401,0x380601,stastic data,Distribution Unit Total Energy Consumption,Dist.Unit NRG Cons.,配电单元总耗电量,配电单元总电量,kWh,sint32,4,2,,,,,,
电池,Battery,0x801601,0x080101,analog data,Battery Voltage,Battery Volt,电池电压,电池电压,V,sint16,1,2,4800.0,0.0,6000.0,,,
电池,Battery,0x801601,0x080301,analog data,Battery Current,Battery Curr,电池电流,电池电流,A,sint32,1,2,0.0,-300000.0,300000.0,,,
电池,Battery,0x801601,0x080401,analog data,Battery Temperature,Battery Temp,电池温度,电池温度,℃,sint16,1,0,25,-40,100,,,
电池,Battery,0x801601,0x080601,analog data,Battery Present SOC,Batt.Prst.SOC,电池组当前容量比率,电池当前容量比,%,sint16,1,0,100,5,100,,,
电池,Battery,0x801601,0x080801,analog data,Battery SOH,Battery SOH,电池SOH,电池SOH,%,sint16,1,0,0,0,100,,,
电池,Battery,0x801601,0x080901,analog data,Battery Present Cap,Batt.Prst.Cap,电池组当前容量,电池当前容量,Ah,sint16,1,0,200,0,9999,,,
电池,Battery,0x801601,0x080a01,analog data,Battery Block Voltage,Batt.Block Volt.,12V单节电池电压,12V单节电压,V,sint16,4,0,0,0,15,,,
电池,Battery,0x801601,0x080c01,analog data,Battery Middle Voltage 1,Batt.Midd.Volt.1,电池中点电压1,电池中点电压1,V,sint16,1,2,2400.0,0.0,3800.0,,,
电池,Battery,0x801601,0x080d01,analog data,Battery Middle Voltage 2,Batt.Midd.Volt.2,电池中点电压2,电池中点电压2,V,sint16,1,2,2400.0,0.0,3800.0,,,
电池,Battery,0x801601,0x080f01,analog data,Battery Middle Voltage Difference,Batt.Mid.Volt.Dif.,电池中点电压差值,电池中点电压差值,V,sint16,1,2,2400.0,0.0,3800.0,,,
电池,Battery,0x801601,0x100201,digital data,Battery Temperature Sensor Status,Batt.T.Sensor,电池温度传感器状态,电池温度传感器,,sint8,1,0,0,0,1,,0:正常/Normal;1:异常/Fault;2:未配置/No Config;,
电池,Battery,0x801601,0x180101,alarm,Battery Temperature High,Batt.Temp.High,电池温度高,电池温度高,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
电池,Battery,0x801601,0x480101,alarm DO,Battery Temperature High,Batt.Temp.High,电池温度高,电池温度高,,sint8,1,0,,,,,,
电池,Battery,0x801601,0x180201,alarm,Battery Temperature Low,Batt.Temp.Low,电池温度低,电池温度低,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
电池,Battery,0x801601,0x480201,alarm DO,Battery Temperature Low,Batt.Temp.Low,电池温度低,电池温度低,,sint8,1,0,,,,,,
电池,Battery,0x801601,0x180301,alarm,Battery Loop Broken,Batt.Loop Brk,电池回路断,电池回路断,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
电池,Battery,0x801601,0x480301,alarm DO,Battery Loop Broken,Batt.Loop Brk,电池回路断,电池回路断,,sint8,1,0,,,,,,
电池,Battery,0x801601,0x180401,alarm,Battery Temperature Invalid,Batt.T.Invalid,电池温度无效,电池温度无效,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
电池,Battery,0x801601,0x480401,alarm DO,Battery Temperature Invalid,Batt.T.Invalid,电池温度无效,电池温度无效,,sint8,1,0,,,,,,
电池,Battery,0x801601,0x180501,alarm,Battery Voltage Low,Batt.Volt.Low,电池电压低,电池电压低,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
电池,Battery,0x801601,0x480501,alarm DO,Battery Voltage Low,Batt.Volt.Low,电池电压低,电池电压低,,sint8,1,0,,,,,,
电池,Battery,0x801601,0x180601,alarm,Battery Fault,Batt.Fault,电池异常,电池异常,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
电池,Battery,0x801601,0x480601,alarm DO,Battery Fault,Batt.Fault,电池异常,电池异常,,sint8,1,0,,,,,,
电池,Battery,0x801601,0x180701,alarm,Battery Discharge,Batt.Dischg.,电池放电,电池放电,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
电池,Battery,0x801601,0x480701,alarm DO,Battery Discharge,Batt.Dischg.,电池放电,电池放电,,sint8,1,0,,,,,,
电池,Battery,0x801601,0x180801,alarm,Battery Missing,Batt. Missing,电池丢失,电池丢失,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
电池,Battery,0x801601,0x480801,alarm DO,Battery Missing,Batt. Missing,电池丢失,电池丢失,,sint8,1,0,,,,,,
电池,Battery,0x801601,0x180901,alarm,Battery Current Abnormal,Batt.Curr.Abr.,电池电流异常,电池电流异常,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
电池,Battery,0x801601,0x480901,alarm DO,Battery Current Abnormal,Batt.Curr.Abr.,电池电流异常,电池电流异常,,sint8,1,0,,,,,,
电池,Battery,0x801601,0x180a01,alarm,Battery Test Fail,Batt.Test Fail,电池测试失败,电池测试失败,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
电池,Battery,0x801601,0x480a01,alarm DO,Battery Test Fail,Batt.Test Fail,电池测试失败,电池测试失败,,sint8,1,0,,,,,,
电池,Battery,0x801601,0x180b01,alarm,Battery Voltage Too Low,Batt.Volt.Too L.,电池电压过低,电池电压过低,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
电池,Battery,0x801601,0x480b01,alarm DO,Battery Voltage Too Low,Batt.Volt.Too L.,电池电压过低,电池电压过低,,sint8,1,0,,,,,,
电池,Battery,0x801601,0x180c01,alarm,Battery Middle Voltage Imbalance,Batt.Mid.V.Imbal.,电池中点电压不平衡,中点电压不平衡,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
电池,Battery,0x801601,0x480c01,alarm DO,Battery Middle Voltage Imbalance,Batt.Mid.V.Imbal.,电池中点电压不平衡,中点电压不平衡,,sint8,1,0,,,,,,
电池,Battery,0x801601,0x280101,parameter,Battery Start Use Date,Batt.St.Use Date,电池启用日期,电池启用日期,,date,1,0,2037/12/31,2000/1/1,2037/12/31,1,,
电池,Battery,0x801601,0x280201,parameter,Battery Type,Battery Type,电池类型,电池类型,,sint8,1,0,0,0,1,1,0:铅酸/VRLA;1:锂电/Li Batt;,
电池,Battery,0x801601,0x300101,record data,Battery Initial SOC,Batt.Init.SOC,电池组起始容量比率,电池起始容量比,%,sint16,1,0,100,5,100,,,
电池,Battery,0x801601,0x300201,record data,Battery Final SOC,Batt.Final SOC,电池组终止容量比率,电池终止容量比,%,sint16,1,0,100,5,100,,,
电池,Battery,0x801601,0x300301,record data,Battery Changed SOC,Batt.Chg.SOC,电池组变化容量比率,电池变化容量比,%,sint16,1,0,100,0,100,,,
电池,Battery,0x801601,0x300401,record data,Initial Battery Voltage,Init.Batt.Volt.,电池起始电压,电池起始电压,V,sint16,1,2,,,,10.0,,
电池,Battery,0x801601,0x300501,record data,Final Battery Voltage,Fina.Batt.Volt.,电池终止电压,电池终止电压,V,sint16,1,2,,,,10.0,,
电池,Battery,0x801601,0x300801,record data,Battery Initial Charge Power,Batt.Init.Chg.Pwr.,电池起始充电电量,电池起始充电电量,kWh,sint32,1,2,,,,1.0,,
电池,Battery,0x801601,0x300901,record data,Battery Final Charge Power,Batt.Fina.Chg.Pwr.,电池终止充电电量,电池终止充电电量,kWh,sint32,1,2,,,,1.0,,
电池,Battery,0x801601,0x300a01,record data,Battery Initial Discharge Power,Batt.Init.Dis.Pwr.,电池起始放电电量,电池起始放电电量,kWh,sint32,1,2,,,,1.0,,
电池,Battery,0x801601,0x300b01,record data,Battery Final Discharge Power,Batt.Fina.Dis.Pwr.,电池终止放电电量,电池终止放电电量,kWh,sint32,1,2,,,,1.0,,
电池,Battery,0x801601,0x380101,stastic data,Battery Discharge Capacity,Batt.Dischg.Cap.,电池放电容量,电池放电容量,Ah,sint32,1,2,,,,,,
电池,Battery,0x801601,0x380201,stastic data,Battery Discharge Power,Batt.Dis Power,电池放电电量,电池放电电量,kWh,sint32,1,2,,,,1.0,,
电池,Battery,0x801601,0x380301,stastic data,Battery Charge Accumulated Duration,Batt.Chg.Acc.Dura,电池充电累计时间,电池充电累计时间,Min,sint32,1,0,,,,,,
电池,Battery,0x801601,0x380401,stastic data,Battery Charge Capacity,Batt.Chg. Cap.,电池充电容量,电池充电容量,Ah,sint32,1,2,,,,,,
电池,Battery,0x801601,0x380501,stastic data,Battery Charge Power,Batt.Chg.Pwr.,电池充电电量,电池充电电量,kWh,sint32,1,2,,,,1.0,,
电池,Battery,0x801601,0x380601,stastic data,Battery Discharge Accumulated Duration,Batt.Dischg.Accum.Dura.,电池放电累计时间,电池放电时间,Min,sint32,1,0,,,,,,
电池,Battery,0x801601,0x380701,stastic data,Battery Charge Times,Batt.Chg.Times,电池充电次数,电池充电次数,,sint32,1,0,,,,,,
电池,Battery,0x801601,0x380801,stastic data,Battery Equal Times,Batt.Equ.Times,电池均充次数,电池均充次数,,sint32,1,0,,,,,,
电池,Battery,0x801601,0x380901,stastic data,Battery Discharge Times,Batt.Dischg.Times,电池放电次数,电池放电次数,,sint32,1,0,,,,,,
电池,Battery,0x801601,0x380a01,stastic data,Battery Test Times,Batt.Test Times,电池测试次数,电池测试次数,,sint32,1,0,,,,,,
电池组,Battery Group,0x801801,0x080501,analog data,Full Charge Duration,FullChg.Dura.,长充持续时间,长充持续时间,Min,sint16,1,0,0,0,,1,,
电池组,Battery Group,0x801801,0x100a01,digital data,Full Charge State,FullChg.Sta.,长充状态,长充状态,,sint8,1,0,0,0,1,,0:否/No;1:是/Yes;,
电池组,Battery Group,0x801801,0x180101,alarm,LLVD1 Alarm,LLVD1 Alarm,一次下电告警,一次下电告警,,sint8,1,0,,,,,1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
电池组,Battery Group,0x801801,0x480101,alarm DO,LLVD1 Alarm,LLVD1 Alarm,一次下电告警,一次下电告警,,sint8,1,0,,,,,,
电池组,Battery Group,0x801801,0x180201,alarm,LLVD2 Alarm,LLVD2 Alarm,二次下电告警,二次下电告警,,sint8,1,0,,,,,1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
电池组,Battery Group,0x801801,0x480201,alarm DO,LLVD2 Alarm,LLVD2 Alarm,二次下电告警,二次下电告警,,sint8,1,0,,,,,,
电池组,Battery Group,0x801801,0x180301,alarm,BLVD Alarm,BLVD Alarm,电池下电告警,电池下电告警,,sint8,1,0,,,,,1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
电池组,Battery Group,0x801801,0x480301,alarm DO,BLVD Alarm,BLVD Alarm,电池下电告警,电池下电告警,,sint8,1,0,,,,,,
电池组,Battery Group,0x801801,0x180401,alarm,Battery Detection Abnormal,Batt.Det.Abr.,电池检测异常,电池检测异常,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
电池组,Battery Group,0x801801,0x480401,alarm DO,Battery Detection Abnormal,Batt.Det.Abr.,电池检测异常,电池检测异常,,sint8,1,0,,,,,,
电池组,Battery Group,0x801801,0x180501,alarm,Battery Current Imbalance,B.Curr.Imbal.,电池电流不平衡,电池电流不平衡,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
电池组,Battery Group,0x801801,0x480501,alarm DO,Battery Current Imbalance,B.Curr.Imbal.,电池电流不平衡,电池电流不平衡,,sint8,1,0,,,,,,
电池组,Battery Group,0x801801,0x180601,alarm,Battery Testing,Battery Testing,电池测试,电池测试,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
电池组,Battery Group,0x801801,0x480601,alarm DO,Battery Testing,Battery Testing,电池测试,电池测试,,sint8,1,0,,,,,,
电池组,Battery Group,0x801801,0x180701,alarm,Battery Equalized Charge,Batt. Equal,电池均充,电池均充,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
电池组,Battery Group,0x801801,0x480701,alarm DO,Battery Equalized Charge,Batt. Equal,电池均充,电池均充,,sint8,1,0,,,,,,
电池组,Battery Group,0x801801,0x180801,alarm,BHTD Alarm,BHTD Alarm,电池高温下电,电池高温下电,,sint8,1,0,,,,,1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
电池组,Battery Group,0x801801,0x480801,alarm DO,BHTD Alarm,BHTD Alarm,电池高温下电,电池高温下电,,sint8,1,0,,,,,,
电池组,Battery Group,0x801801,0x180901,alarm,BLTD Alarm,BLTD Alarm,电池低温下电,电池低温下电,,sint8,1,0,,,,,1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
电池组,Battery Group,0x801801,0x480901,alarm DO,BLTD Alarm,BLTD Alarm,电池低温下电,电池低温下电,,sint8,1,0,,,,,,
电池组,Battery Group,0x801801,0x180a01,alarm,Battery Group Missing,Batt.Group Miss.,电池组丢失,电池组丢失,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
电池组,Battery Group,0x801801,0x480a01,alarm DO,Battery Group Missing,Batt.Group Miss.,电池组丢失,电池组丢失,,sint8,1,0,,,,,,
电池组,Battery Group,0x801801,0x180b01,alarm,LHTD Alarm,LHTD Alarm,负载高温下电,负载高温下电,,sint8,1,0,,,,,1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
电池组,Battery Group,0x801801,0x480b01,alarm DO,LHTD Alarm,LHTD Alarm,负载高温下电,负载高温下电,,sint8,1,0,,,,,,
电池组,Battery Group,0x801801,0x180c01,alarm,LLTD Alarm,LLTD Alarm,负载低温下电,负载低温下电,,sint8,1,0,,,,,1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
电池组,Battery Group,0x801801,0x480c01,alarm DO,LLTD Alarm,LLTD Alarm,负载低温下电,负载低温下电,,sint8,1,0,,,,,,
电池组,Battery Group,0x801801,0x200101,control,Start Float Charge,Start Float,启动浮充,启动浮充,,sint8,1,,,,,,,
电池组,Battery Group,0x801801,0x200201,control,Start Equalized Charge,Start Equal,启动均充,启动均充,,sint8,1,,,,,,,
电池组,Battery Group,0x801801,0x200301,control,Start Test,Start Test,启动测试,启动测试,,sint8,1,,,,,,,
电池组,Battery Group,0x801801,0x200501,control,Start Battery Detect,Start Batt.Det.,启动电池检测,启动电池检测,,sint8,1,,,,,,,
电池组,Battery Group,0x801801,0x200901,control,BMS Device Statistic,BMS Dev.Stat.,BMS设备统计,BMS设备统计,,sint8,1,,,,,,,
电池组,Battery Group,0x801801,0x200a01,control,Start Charge,Start Charge,启动充电,启动充电,,sint8,1,,,,,,,
电池组,Battery Group,0x801801,0x200b01,control,Start Address Compete,Start Addr Compete,启动地址竞争,启动地址竞争,,sint8,1,,,,,,,
电池组,Battery Group,0x801801,0x280101,parameter,Battery Capacity,Battery Cap,电池组容量,电池组容量,Ah,sint16,4,0,81,0,9990,1,,
电池组,Battery Group,0x801801,0x280201,parameter,Float Charge Voltage,Float Voltage,浮充电压,浮充电压,V,sint16,1,1,535.0,420.0,595.0,1.0,,"<=Battery Group|Equalized Charge Voltage;
<=SMR Output High Off Voltage-2V;
<=PU Output OVP Voltage Threshold-2V;
"
电池组,Battery Group,0x801801,0x280301,parameter,Equalized Charge Voltage,Equalized Voltage,均充电压,均充电压,V,sint16,1,1,564.0,420.0,595.0,1.0,,">=Battery Group|Float Charge Voltage;
<=SMR Output High Off Voltage-2V;
<=PU Output OVP Voltage Threshold-2V;
"
电池组,Battery Group,0x801801,0x280401,parameter,Test Stop Voltage,Test Stop Volt,测试终止电压,测试终止电压,V,sint16,1,1,460.0,400.0,580.0,1.0,,">=Battery Group|LLVD1 Voltage+1V;
>=Battery Group|LLVD2 Voltage+1V;
>=Battery Group|BLVD Voltage+1V;
>=DC Distribution|DC DU LLVD Voltage+1V;
>=Smart DC Distribution Unit|Distribution Unit LLVD Voltage Threshold+1V;
"
电池组,Battery Group,0x801801,0x280501,parameter,Equalized Charge Enabled,Equalized Enabled,均充使能,均充使能,,sint8,1,0,1,0,1,1,0:禁止/Disabled;1:允许/Enabled;,
电池组,Battery Group,0x801801,0x280601,parameter,Start Voltage Deviation in Transition,Start Volt.Dev.,过渡阶段启调电压偏差,启调电压偏差,V,sint16,1,1,-15.0,-20.0,-5.0,1.0,,
电池组,Battery Group,0x801801,0x280701,parameter,Shunt Limit Current Ratio,Shu.Lim.Curr.Rat,分流器限流比率,分流器限流比率,%,sint16,1,0,80,50,100,1,,
电池组,Battery Group,0x801801,0x280801,parameter,Battery Application Scenario,Batt.Apl.Scenario,电池应用场景,电池应用场景,,sint8,1,0,0,0,1,,0:备电场景/Standby Scenario;1:循环场景/Cycle Scenario;,
电池组,Battery Group,0x801801,0x280901,parameter,Battery Temperature High Threshold,Batt.Temp.H.Thre.,电池温度高阈值,电池过温值,℃,sint16,1,0,45,30,60,1,,"<=Battery Group|BHTD Temperature-3℃;
"
电池组,Battery Group,0x801801,0x280a01,parameter,Battery Temperature Low Threshold,Batt.Temp.L.Thre.,电池温度低阈值,电池低温值,℃,sint16,1,0,-5,-30,20,1,,">=Battery Group|BLTD Temperature+3℃;
"
电池组,Battery Group,0x801801,0x280b01,parameter,Battery Detect Period,Batt.Det.Period,电池检测周期,电池检测周期,Day,sint16,1,0,0,0,90,1,,
电池组,Battery Group,0x801801,0x280c01,parameter,Battery Loop Broken Threshold,Batt.Loop B.Thre.,电池回路断阈值电压,电池回路断阈值,V,sint16,1,1,5.0,1.0,8.0,1.0,,
电池组,Battery Group,0x801801,0x280d01,parameter,Battery Discharge Current Threshold,Batt.Dischg Thre.,电池放电阈值,电池放电阈值,A,sint16,1,1,-60.0,-500.0,-30.0,1.0,,
电池组,Battery Group,0x801801,0x280e01,parameter,Battery Voltage Low Threshold,Batt.Volt.L.Thre.,电池电压低阈值,电池电压低值,V,sint16,1,1,460.0,390.0,540.0,1.0,,">=Battery Group|Battery Voltage Too Low Threshold;
>=Battery Group|LLVD1 Voltage+1V;
>=Battery Group|LLVD2 Voltage+1V;
>=Battery Group|BLVD Voltage+1V;
>=DC Distribution|DC DU LLVD Voltage+1V;
>=Smart DC Distribution Unit|Distribution Unit LLVD Voltage Threshold+1V;
"
电池组,Battery Group,0x801801,0x281001,parameter,Battery Charge Current Coefficient,Chg.Curr.Coeff.,电池充电电流系数,充电电流系数,C10,sint16,1,3,150.0,0.0,2000.0,1.0,,
电池组,Battery Group,0x801801,0x281101,parameter,Battery Test Period,Test Period,电池测试周期,测试周期,Day,sint16,1,0,0,0,365,1,,
电池组,Battery Group,0x801801,0x281201,parameter,Test Maximum Duration,Test Max. Dura.,测试最长时间,测试最长时间,Min,sint16,1,0,480,0,1440,1,,
电池组,Battery Group,0x801801,0x281301,parameter,Test Stop SOC Threshold,Test Stop SOC,测试终止SOC阈值,测试终止SOC,%,sint16,1,0,45,10,100,1,,">=Battery Group|LLVD1 SOC Threshold+5%;
>=Battery Group|LLVD2 SOC Threshold+5%;
>=Battery Group|BLVD SOC Threshold+5%;
<=Battery Group|Battery Test Fail SOC Threshold;
>=DC Distribution|DC DU LLVD SOC Threshold+5%;
>=Smart DC Distribution Unit|Distribution Unit LLVD SOC Threshold+5%;
"
电池组,Battery Group,0x801801,0x281401,parameter,Battery Test Fail SOC Threshold,Test Fail SOC,测试失败SOC阈值,测试失败SOC,%,sint16,1,0,85,10,100,1,,">=Battery Group|Test Stop SOC Threshold;
"
电池组,Battery Group,0x801801,0x281501,parameter,Preset Equalized Charge Enabled,Preset Equ.En.,预约均充使能,预约均充使能,,sint8,1,0,0,0,1,1,0:禁止/Disabled;1:允许/Enabled;,
电池组,Battery Group,0x801801,0x281601,parameter,Preset Equalized Charge Date,Preset Equ.Date,预约均充日期,预约均充日期,,date,1,0,2037/12/31,2000/1/1,2037/12/31,1,,
电池组,Battery Group,0x801801,0x281701,parameter,Preset Equalized Charge Duration,Pre.Equ.Chg.Dura.,预约均充时长,预约均充时长,Min,sint16,1,0,1,1,2880,1,,
电池组,Battery Group,0x801801,0x281801,parameter,Disconnect Mode,Disconnect Mode,下电模式,下电模式,,sint8,1,0,1,0,3,1,0:禁止/Disabled;1:电池电压/Batt Volt;2:停电时间/PowerOff Time;3:电池剩余容量/Batt Rem Cap;,
电池组,Battery Group,0x801801,0x281901,parameter,Disconnect Ctrl Delay,Discon. Ctrl Delay,下电控制延时,下电控制延时,sec,sint16,1,0,0,0,900,1,,
电池组,Battery Group,0x801801,0x281b01,parameter,Temperature Compensation Mode,Temp. Comp. Mode,温度补偿模式,温补模式,,sint8,1,0,0,0,3,1,0:允许/Enabled;1:浮充/Float;2:均充/Equal;3:禁止/Disabled;,
电池组,Battery Group,0x801801,0x281c01,parameter,Temperature Compensation Reference,Temp.Comp.Ref.,电池温度补偿基准,温补基准,℃,sint16,1,0,25,20,30,1,,"<=Battery Group|Battery Exempt Temperatrue Complement Max.;
"
电池组,Battery Group,0x801801,0x281d01,parameter,Equalized Charge Period,Equalized Period,电池均充周期,均充周期,Day,sint16,1,0,90,0,365,1,,
电池组,Battery Group,0x801801,0x281e01,parameter,Equalized Charge Maximum Duration,Equ.Max.Dura.,均充最长时间,均充最长时间,Min,sint16,1,0,960,0,2880,1,,">=Equalized Charge Minimum Duration;
"
电池组,Battery Group,0x801801,0x281f01,parameter,Equalized Charge Minimum Duration,Equ.Min.Dura.,均充最短时间,均充最短时间,Min,sint16,1,0,180,0,2880,1,,">=Equalized Charge End Duration;
<=Equalized Charge Maximum Duration;
"
电池组,Battery Group,0x801801,0x282001,parameter,Equalized Charge End Current Coefficient,Equ.End C.Coeff.,均充末期电流系数,均充末期电流系数,C10,sint16,1,3,15.0,1.0,60.0,1.0,,
电池组,Battery Group,0x801801,0x282101,parameter,Equalized Charge End Duration,Equ.End Dura.,均充末期维持时间,均充维持时间,Min,sint16,1,0,180,0,600,1,,"<=Equalized Charge Minimum Duration;
"
电池组,Battery Group,0x801801,0x282201,parameter,Equalized Charge Threshold Duration,Equ.Thre.Dura.,均充阈值放电时间,均充阈值时间,Min,sint16,1,0,300,0,1440,1,,
电池组,Battery Group,0x801801,0x282301,parameter,Equalized Charge Threshold Voltage,Equ.Thre.Volt.,均充阈值电压,均充阈值电压,V,sint16,1,1,485.0,460.0,550.0,1.0,,">=Diesel Generator Group|DG Start Voltage Threshold+0.5V;
"
电池组,Battery Group,0x801801,0x282401,parameter,Equalized Charge Threshold Current,Equ.Thre.Curr.,均充阈值电流,均充阈值电流,C10,sint16,1,3,60.0,30.0,500.0,5.0,,
电池组,Battery Group,0x801801,0x282501,parameter,Equalized Charge Threshold SOC,Equ.Thre.SOC,均充阈值SOC,均充阈值SOC,%,sint16,1,0,90,50,100,1,,
电池组,Battery Group,0x801801,0x282601,parameter,Environment Temperature Contorl Reference,Env. T. Ctrl. Ref,环境温度控制基准值,环境温控基准,℃,sint16,1,0,55,45,80,1,,
电池组,Battery Group,0x801801,0x282701,parameter,Environment Temperature Contorl Compensation Coefficient,Env.T.Ctrl.Coeff,环境温度控制补偿系数,环境温控系数,C10/℃,sint16,1,3,30.0,0.0,60.0,1.0,,
电池组,Battery Group,0x801801,0x282801,parameter,LLVD1 Enabled,LLVD1 Enabled,负载一次下电使能,负载一次下电使能,,sint8,1,0,1,0,1,1,0:禁止/Disabled;1:允许/Enabled;,
电池组,Battery Group,0x801801,0x282901,parameter,LLVD1 Duration,LLVD1 Dura.,负载一次下电时间,一次下电时间,Min,sint16,1,0,1440,3,7200,1,,">=Diesel Generator Group|DG Start Discharge Duration+30;
>=Mains Group|MAINS Start Discharge Duration Threshold+30;
"
电池组,Battery Group,0x801801,0x282a01,parameter,LLVD1 Voltage,LLVD1 Volt.,负载一次下电电压,一次下电电压,V,sint16,1,1,450.0,380.0,580.0,1.0,,"<=Battery Group|Test Stop Voltage-1V;
<=Battery Group|Battery Voltage Low Threshold-1V;
<=Diesel Generator Group|DG Start Voltage Threshold-0.5V;
<=Diesel Generator Group|DG Stop Voltage Threshold-2V;
<=Mains Group|MAINS Start Voltage Threshold-1V;
<=Mains Group|MAINS Stop Voltage Threshold-2V;
"
电池组,Battery Group,0x801801,0x282b01,parameter,LLVD1 SOC Threshold,LLVD1 SOC,负载一次下电SOC阈值,一次下电SOC,%,sint16,1,0,20,2,90,1,,"<=Battery Group|Test Stop SOC Threshold-5%;
<=Diesel Generator Group|DG Start SOC Threshold-5%;
<=Diesel Generator Group|DG Stop SOC Threshold-10%;
<=Mains Group|MAINS Start SOC Threshold-5%;
<=Mains Group|MAINS Stop SOC Threshold-10%;
"
电池组,Battery Group,0x801801,0x282c01,parameter,LLVD2 Enabled,LLVD2 Enabled,负载二次下电使能,负载二次下电使能,,sint8,1,0,1,0,1,1,0:禁止/Disabled;1:允许/Enabled;,
电池组,Battery Group,0x801801,0x282d01,parameter,LLVD2 Duration,LLVD2 Dura.,负载二次下电时间,二次下电时间,Min,sint16,1,0,1680,3,7200,1,,">=Diesel Generator Group|DG Start Discharge Duration+30;
>=Mains Group|MAINS Start Discharge Duration Threshold+30;
"
电池组,Battery Group,0x801801,0x282e01,parameter,LLVD2 Voltage,LLVD2 Volt.,负载二次下电电压,二次下电电压,V,sint16,1,1,440.0,380.0,580.0,1.0,,"<=Battery Group|Test Stop Voltage-1V;
<=Battery Group|Battery Voltage Low Threshold-1V;
<=Diesel Generator Group|DG Start Voltage Threshold-0.5V;
<=Diesel Generator Group|DG Stop Voltage Threshold-2V;
<=Mains Group|MAINS Start Voltage Threshold-1V;
<=Mains Group|MAINS Stop Voltage Threshold-2V;
"
电池组,Battery Group,0x801801,0x282f01,parameter,LLVD2 SOC Threshold,LLVD2 SOC,负载二次下电SOC阈值,二次下电SOC,%,sint16,1,0,10,2,90,1,,"<=Battery Group|Test Stop SOC Threshold-5%;
<=Diesel Generator Group|DG Start SOC Threshold-5%;
<=Diesel Generator Group|DG Stop SOC Threshold-10%;
<=Mains Group|MAINS Start SOC Threshold-5%;
<=Mains Group|MAINS Stop SOC Threshold-10%;
"
电池组,Battery Group,0x801801,0x283001,parameter,BLVD Enabled,BLVD Enabled,电池下电使能,电池下电使能,,sint8,1,0,1,0,1,1,0:禁止/Disabled;1:允许/Enabled;,
电池组,Battery Group,0x801801,0x283101,parameter,BLVD Duration,BLVD Dura.,电池下电时间,电池下电时间,Min,sint16,1,0,1680,3,7200,1,,">=Diesel Generator Group|DG Start Discharge Duration+30;
>=Mains Group|MAINS Start Discharge Duration Threshold+30;
"
电池组,Battery Group,0x801801,0x283201,parameter,BLVD Voltage,BLVD Voltage,电池下电电压,电池下电电压,V,sint16,1,1,440.0,380.0,580.0,1.0,,"<=Battery Group|Test Stop Voltage-1V;
<=Battery Group|Battery Voltage Low Threshold-1V;
<=Diesel Generator Group|DG Start Voltage Threshold-0.5V;
<=Diesel Generator Group|DG Stop Voltage Threshold-2V;
<=Mains Group|MAINS Start Voltage Threshold-1V;
<=Mains Group|MAINS Stop Voltage Threshold-2V;
"
电池组,Battery Group,0x801801,0x283301,parameter,BLVD SOC Threshold,BLVD SOC,电池下电SOC阈值,电池下电SOC,%,sint16,1,0,10,2,90,1,,"<=Battery Group|Test Stop SOC Threshold-5%;
<=Diesel Generator Group|DG Start SOC Threshold-5%;
<=Diesel Generator Group|DG Stop SOC Threshold-10%;
<=Mains Group|MAINS Start SOC Threshold-5%;
<=Mains Group|MAINS Stop SOC Threshold-10%;
"
电池组,Battery Group,0x801801,0x283501,parameter,Battery Detect Duration,Battery Det.Dura.,电池检测持续时间,电池检测时间,Min,sint16,1,0,2,1,5,1,,
电池组,Battery Group,0x801801,0x283601,parameter,BHTD Enabled,BHTD Enabled,电池高温下电使能,电池高温下电使能,,sint8,1,0,0,0,1,1,0:禁止/Disabled;1:允许/Enabled;,
电池组,Battery Group,0x801801,0x283701,parameter,BHTD Temperature,BHTD Temp.,电池高温下电温度,高温下电温度,℃,sint16,1,0,50,30,60,1,,">=Battery Group|Battery Temperature High Threshold+3℃;
"
电池组,Battery Group,0x801801,0x283801,parameter,Battery Voltage Temperature Compensation Coefficient,Volt.Temp.Coeff.,电池电压温度补偿系数,电压温补系数,mV/Cell/℃,sint16,1,1,30.0,0.0,80.0,1.0,,
电池组,Battery Group,0x801801,0x283901,parameter,Battery Current Temperature Compensation Coefficient,Curr.Temp.Coeff.,电池电流温度补偿系数,电流温补系数,C10/℃,sint16,1,3,0.0,0.0,20.0,1.0,,
电池组,Battery Group,0x801801,0x283a01,parameter,BLTD Enabled,BLTD Enabled,电池低温下电使能,电池低温下电使能,,sint8,1,0,0,0,1,1,0:禁止/Disabled;1:允许/Enabled;,
电池组,Battery Group,0x801801,0x283b01,parameter,BLTD Temperature,BLTD Temp.,电池低温下电温度,低温下电温度,℃,sint16,1,0,-15,-40,10,1,,"<=Battery Group|Battery Temperature Low Threshold-3℃;
"
电池组,Battery Group,0x801801,0x283c01,parameter,Battery Test Start Time,Test Start Time,电池测试启动时刻,测试启动时刻,:00,sint16,1,0,10,0,23,1,,
电池组,Battery Group,0x801801,0x283f01,parameter,Temperature Compensation Voltage Maximum,Temp.Volt.Max.,温度补偿电压上限,温补电压上限,V,sint16,1,1,576.0,500.0,595.0,1.0,,">=Battery Group|Temperature Compensation Voltage Minimum;
"
电池组,Battery Group,0x801801,0x284001,parameter,Temperature Compensation Voltage Minimum,Temp.Volt.Min.,温度补偿电压下限,温补电压下限,V,sint16,1,1,528.0,500.0,595.0,1.0,,"<=Battery Group|Temperature Compensation Voltage Maximum;
"
电池组,Battery Group,0x801801,0x284301,parameter,Battery Voltage Too Low Threshold,Batt.V.T.L.Thre.,电池电压过低阈值,电池电压过低值,V,sint16,1,1,450.0,390.0,540.0,1.0,,"<=Battery Group|Battery Voltage Low Threshold;
"
电池组,Battery Group,0x801801,0x284401,parameter,Battery Series,Battery Series,电池系列,电池系列,,sint8,1,0,17,0,17,1,0:ZXDC12 CA/ZXDC12 CA;1:ZXDC12 CG/ZXDC12 CG;2:12V GEL/12V GEL;3:12V FT/12V FT;4:12V AGM/12V AGM;5:2V AGM/2V AGM;6:2V GEL/2V GEL;7:ZXDC12 HP/ZXDC12 HP;8:ZXDC12 HP/ZXDC12 HP;9:ZXDC02 HC/ZXDC02 HC;10:ZXDC02 HL/ZXDC02 HL;11:ZXDC02 HL E/ZXDC02 HL E;12:ZXDC12 HL/ZXDC12 HL;13:ZXDC02 HP/ZXDC02 HP;14:ZXDC02 HP E/ZXDC02 HP E;15:ZXDC12HP A/ZXDC12HP A;16:2V Other/2V Other;17:12V Other/12V Other;,
电池组,Battery Group,0x801801,0x284501,parameter,Battery Exempt Temperatrue Complement Max.,Batt.Exempt Temp.Comp.Max.,电池免温补上限,电池免温补上限,℃,sint16,1,0,35,25,45,1,,">=Battery Group|Temperature Compensation Reference;
"
电池组,Battery Group,0x801801,0x284601,parameter,Battery Current Temperatrue Complement Enabled,Batt.C.T.Comp.En.,电池电流温补使能,电流温补使能,,sint8,1,0,0,0,1,1,0:禁止/Disabled;1:允许/Enabled;,
电池组,Battery Group,0x801801,0x284701,parameter,Battery Middle Voltage Imbalance Threshold,Batt.M.V.Imba.Thre.,电池中点电压不平衡阈值,中点电压不平衡阈值,V,sint16,1,2,200.0,50.0,300.0,1.0,,
电池组,Battery Group,0x801801,0x284801,parameter,LLVD1 Remote Enabled,LLVD1 Remote En.,远程一次下电使能,远程一次下电使能,,sint8,1,0,0,0,1,1,0:禁止/Disabled;1:允许/Enabled;,
电池组,Battery Group,0x801801,0x284901,parameter,LLVD2 Remote Enabled,LLVD2 Remote En.,远程二次下电使能,远程二次下电使能,,sint8,1,0,0,0,1,1,0:禁止/Disabled;1:允许/Enabled;,
电池组,Battery Group,0x801801,0x284b01,parameter,LLVD1 Remote,LLVD1 Remote,远程一次下电,远程一次下电,,sint8,1,0,0,0,1,1,0:上电/Recover;1:下电/Disconnect;,
电池组,Battery Group,0x801801,0x284c01,parameter,LLVD2 Remote,LLVD2 Remote,远程二次下电,远程二次下电,,sint8,1,0,0,0,1,1,0:上电/Recover;1:下电/Disconnect;,
电池组,Battery Group,0x801801,0x284e01,parameter,Battery Charge Over Current Protection Threshold,Batt.Chg.OCP,电池充电过流保护阈值,电池充过流保护值,C3,sint16,1,2,56.00000000000001,30.0,300.0,0.1,,">=Battery Group|Li Battery Charge Current Coefficient+0.04;
>=Battery Group|Battery Charge Over Current Alarm Threshold;
"
电池组,Battery Group,0x801801,0x284f01,parameter,Battery Discharge Over Current Protection Threshold,Batt.Disch.OCP,电池放电过流保护阈值,电池放过流保护,C3,sint16,1,2,57.99999999999999,30.0,300.0,0.1,,">=Battery Group|Battery Discharge Over Current Alarm Threshold;
"
电池组,Battery Group,0x801801,0x285001,parameter,Battery Over Voltage Protection Threhold,Batt.OVP,电池过压保护阈值,电池过压保护,V,sint16,1,2,5480.0,4950.0,6000.0,10.0,,">=Battery Group|Battery Over Voltage Alarm Threhold;
"
电池组,Battery Group,0x801801,0x285101,parameter,Board Over Temperature Protect Threshold,Board OTP.Thrd,单板过温保护阈值,单板过温保护,℃,sint16,1,1,900.0,450.0,1200.0,10.0,,
电池组,Battery Group,0x801801,0x285201,parameter,Battery Charge Over Current Alarm Threshold,Batt.Chg.OCA,电池充电过流告警阈值,电池充过流告警,C3,sint16,1,2,54.0,20.0,300.0,0.1,,">=Battery Group|Li Battery Charge Current Coefficient;
<=Battery Group|Battery Charge Over Current Protection Threshold;
"
电池组,Battery Group,0x801801,0x285301,parameter,Battery Discharge Over Current Alarm Threshold,Batt.Disch.OCA,电池放电过流告警阈值,电池放过流告警,C3,sint16,1,2,56.00000000000001,20.0,300.0,0.1,,"<=Battery Group|Battery Discharge Over Current Protection Threshold;
"
电池组,Battery Group,0x801801,0x285401,parameter,Battery Over Voltage Alarm Threhold,Batt.OVA.Thr,电池过压告警阈值,电池过压阈值,V,sint16,1,2,5400.0,4950.0,6000.0,10.0,,">=Battery Group|Battery Full Voltage+1V;
<=Battery Group|Battery Over Voltage Protection Threhold;
"
电池组,Battery Group,0x801801,0x285601,parameter,Inner Environment Over Temperature Alarm Threshold,Inner Env.OTA,机内环境温度高告警阈值,机内环境过温值,℃,sint16,1,1,600.0,300.0,700.0,10.0,,">=Battery Group|Inner Environment Under Temperature Alarm Threshold;
"
电池组,Battery Group,0x801801,0x285701,parameter,Inner Environment Under Temperature Alarm Threshold,Inner Env.UTA,机内环境温度低告警阈值,机内环境低温值,℃,sint16,1,1,0.0,-200.0,100.0,10.0,,"<=Battery Group|Inner Environment Over Temperature Alarm Threshold;
"
电池组,Battery Group,0x801801,0x285801,parameter,Battery Under Voltage Alarm Threhold,Batt.UVA,电池欠压告警阈值,电池欠压告警,V,sint16,1,2,4350.0,4000.0,5000.0,10.0,,">=Battery Group|Battery Under Voltage Protection Threhold;
<=Battery Group|Battery Self ReCharge Voltage Threhold;
<=Battery Group|Charge Voltage-1V;
"
电池组,Battery Group,0x801801,0x285901,parameter,Battery Under Voltage Protection Threhold,Batt.UVP,电池欠压保护阈值,电池欠压保护,V,sint16,1,2,4000.0,3800.0,5000.0,10.0,,"<=Battery Group|Battery Under Voltage Alarm Threhold;
"
电池组,Battery Group,0x801801,0x285a01,parameter,Cell Over Volatge Alarm Threshold,Cell OVA,单体过压告警阈值,单体过压告警,V,sint16,1,3,3750.0,3200.0,4000.0,10.0,,"<=Battery Group|Cell Charge Over Voltage Protection Threshold;
"
电池组,Battery Group,0x801801,0x285b01,parameter,Cell Charge Over Voltage Protection Threshold,Cell OVP,单体过压保护阈值,单体过压保护值,V,sint16,1,3,3900.0,3200.0,4000.0,10.0,,">=Battery Group|Cell Over Volatge Alarm Threshold;
"
电池组,Battery Group,0x801801,0x285c01,parameter,Cell Under Voltage Alarm Threhold,Cell UVA,单体欠压告警阈值,单体欠压告警,V,sint16,1,3,2800.0,2000.0,3200.0,10.0,,">=Battery Group|Cell Under Voltage Protection Threhold;
"
电池组,Battery Group,0x801801,0x285d01,parameter,Cell Under Voltage Protection Threhold,Cell UVP Thre,单体欠压保护阈值,单体欠压保护值,V,sint16,1,3,2500.0,2000.0,3200.0,10.0,,"<=Battery Group|Cell Under Voltage Alarm Threhold;
>=Battery Group|Cell Damage Protect Threshold;
"
电池组,Battery Group,0x801801,0x285e01,parameter,Cell Charge Over Temperature Alarm Threshold,Cell Chg.OTA,单体充电高温告警阈值,单体充高温告警,℃,sint16,1,1,550.0,350.0,650.0,10.0,,">=Battery Group|Cell Charge Under Temperature Alarm Threshold;
<=Battery Group|Cell Charge Over Temperature Protection Threshold;
<=Battery Group|Cell Discharge Over Temperature Alarm Threshold;
"
电池组,Battery Group,0x801801,0x285f01,parameter,Cell Charge Over Temperature Protection Threshold,Cell Chg.OTP,单体充电高温保护阈值,单体充高温保护,℃,sint16,1,1,600.0,350.0,650.0,10.0,,">=Battery Group|Cell Charge Over Temperature Alarm Threshold;
<=Battery Group|Cell Discharge Over Temperature Protection Threshold;
"
电池组,Battery Group,0x801801,0x286001,parameter,Cell Discharge Over Temperature Alarm Threshold,Cell Disch.OTA,单体放电高温告警阈值,单体放高温告警,℃,sint16,1,1,600.0,350.0,650.0,10.0,,">=Battery Group|Cell Discharge Under Temperature Alarm Threshold;
>=Battery Group|Cell Charge Over Temperature Alarm Threshold;
<=Battery Group|Cell Discharge Over Temperature Protection Threshold;
"
电池组,Battery Group,0x801801,0x286101,parameter,Cell Discharge Over Temperature Protection Threshold,Cell Disch.OTP,单体放电高温保护阈值,单体放高温保护,℃,sint16,1,1,650.0,350.0,650.0,10.0,,">=Battery Group|Cell Discharge Over Temperature Alarm Threshold;
>=Battery Group|Cell Charge Over Temperature Protection Threshold;
"
电池组,Battery Group,0x801801,0x286201,parameter,Cell Charge Under Temperature Alarm Threshold,Cell Chg.UTA,单体充电低温告警阈值,单体充低温告警,℃,sint16,1,1,50.0,-200.0,100.0,10.0,,">=Battery Group|Cell Discharge Under Temperature Alarm Threshold;
>=Battery Group|Cell Charge Under Temperature Protection Threshold;
<=Battery Group|Cell Charge Over Temperature Alarm Threshold;
"
电池组,Battery Group,0x801801,0x286301,parameter,Cell Charge Under Temperature Protection Threshold,Cell Chg.UTP,单体充电低温保护阈值,单体充低温保护,℃,sint16,1,1,0.0,-200.0,100.0,10.0,,">=Battery Group|Cell Discharge Under Temperature Protection Threshold;
<=Battery Group|Cell Charge Under Temperature Alarm Threshold;
"
电池组,Battery Group,0x801801,0x286401,parameter,Cell Discharge Under Temperature Alarm Threshold,Cell Disch.UTA,单体放电低温告警阈值,单体放低温告警,℃,sint16,1,1,-150.0,-200.0,100.0,10.0,,">=Battery Group|Cell Discharge Under Temperature Protection Threshold;
<=Battery Group|Cell Charge Under Temperature Alarm Threshold;
<=Battery Group|Cell Discharge Over Temperature Alarm Threshold;
"
电池组,Battery Group,0x801801,0x286501,parameter,Cell Discharge Under Temperature Protection Threshold,Cell Disch.UTP,单体放电低温保护阈值,单体放低温保护,℃,sint16,1,1,-200.0,-200.0,100.0,10.0,,"<=Battery Group|Cell Discharge Under Temperature Alarm Threshold;
<=Battery Group|Cell Charge Under Temperature Protection Threshold;
"
电池组,Battery Group,0x801801,0x286601,parameter,Cell Voltage Deviation Threhold,Cell Volt.Dev,单体落后电压差阈值,单体落后电压差,V,sint16,1,2,40.0,5.0,150.0,1.0,,"<=Battery Group|Cell Poor Protect Voltage Deviation Threhold;
"
电池组,Battery Group,0x801801,0x286701,parameter,Cell Poor Protect Voltage Deviation Threhold,Cell Poor Prot.,单体落后保护电压差阈值,单体落后保护压差,V,sint16,1,2,50.0,5.0,150.0,1.0,,">=Battery Group|Cell Voltage Deviation Threhold;
"
电池组,Battery Group,0x801801,0x286801,parameter,Battery SOC Low Alarm Threshold,Batt.Soc.Low.A,电池SOC低告警阈值,电池SOC低告警阈值,%,sint16,1,0,20,0,60,1,,">=Battery Group|Battery SOC Low Protect Threshold;
"
电池组,Battery Group,0x801801,0x286901,parameter,Battery SOC Low Protect Threshold,Batt.Soc.Low.P,电池SOC低保护阈值,电池SOC低保护阈值,%,sint16,1,0,15,0,60,1,,"<=Battery Group|Battery SOC Low Alarm Threshold;
"
电池组,Battery Group,0x801801,0x286a01,parameter,Battery SOH Alarm Threshold,Batt.Soh.A,电池SOH告警阈值,电池SOH告警阈值,%,sint16,1,0,50,0,70,1,,">=Battery Group|Battery SOH Protect Threshold;
"
电池组,Battery Group,0x801801,0x286b01,parameter,Battery SOH Protect Threshold,Batt.Soh.P,电池SOH保护阈值,电池SOH保护阈值,%,sint16,1,0,30,0,70,1,,"<=Battery Group|Battery SOH Alarm Threshold;
"
电池组,Battery Group,0x801801,0x286c01,parameter,Cell Damage Protect Threshold,Cell Damage Prt.,单体损坏保护阈值,单体损坏保护阈值,V,sint16,1,2,150.0,100.0,250.0,1.0,,"<=Battery Group|Cell Under Voltage Protection Threhold;
"
电池组,Battery Group,0x801801,0x286d01,parameter,Charge Voltage,Chg.Volt,充电电压,充电电压,V,sint16,1,1,525.0,520.0,580.0,1.0,,
电池组,Battery Group,0x801801,0x287301,parameter,Equalization Start Voltage,Equ.Start Volt,单体均衡启动压差,均衡启动压差,V,sint16,1,2,4.0,1.0,10.0,1.0,,
电池组,Battery Group,0x801801,0x287401,parameter,Li Battery Charge Current Coefficient,Li Batt.Chg.C.Coeff,锂电充电电流系数,锂电充电系数,C3,sint16,1,2,20.0,3.0,200.0,0.1,,"<=Battery Group|Battery Charge Over Current Protection Threshold-0.04;
<=Battery Group|Battery Charge Over Current Alarm Threshold;
"
电池组,Battery Group,0x801801,0x287501,parameter,Software Shutdown Enable,Sft.Shutdown En.,软关机使能,软关机使能,,sint8,1,0,1,0,1,1,0:禁止/Disabled;1:允许/Enabled;,
电池组,Battery Group,0x801801,0x287601,parameter,Bms Buzz Enable,Bms Buzz En.,BMS蜂鸣器使能,BMS蜂鸣器使能,,sint8,1,0,1,0,1,1,0:禁止/Disabled;1:允许/Enabled;,
电池组,Battery Group,0x801801,0x287701,parameter,System Power Off Voltage,Sys.Poweroff Volt,系统停电电压阈值,系统停电电压阈值,V,sint16,1,2,4700.0,4200.0,6000.0,10.0,,
电池组,Battery Group,0x801801,0x287801,parameter,System Power On Voltage,Sys.Poweron Volt,系统来电电压阈值,系统来电电压阈值,V,sint16,1,2,5180.0,4800.0,6000.0,10.0,,
电池组,Battery Group,0x801801,0x287901,parameter,History Data Record Interval,His.Data Inter.,历史数据保存时间间隔,历史数据间隔,Min,sint16,1,0,480,0,1440,1,,
电池组,Battery Group,0x801801,0x287a01,parameter,Out Voltage Deviation in Transition,Out Volt.Dev.,输出电压偏差阈值,输出电压偏差,V,sint16,1,2,90.0,50.0,900.0,1.0,,
电池组,Battery Group,0x801801,0x287c01,parameter,Li Battery Switch SOC,Li Batt.Sw.SOC,锂电切换SOC,锂电切换SOC,%,sint16,1,0,20,0,100,1,,
电池组,Battery Group,0x801801,0x288301,parameter,Li Battery Discharge DOD,Li Batt.Disc.DOD,锂电放电DOD,锂电放电DOD,%,sint16,1,0,70,10,100,1,,
电池组,Battery Group,0x801801,0x288501,parameter,Software Anti-theft Delay,Soft.At.Delay,软件防盗延时,软件防盗延时,Min,sint16,1,0,0,0,4320,1,,
电池组,Battery Group,0x801801,0x288801,parameter,Full Charge Interval,FullChg.Intv.,长充启动周期,长充周期,Day,sint16,1,0,15,0,180,1,,
电池组,Battery Group,0x801801,0x288901,parameter,Full Charge Stop Battery Current,FullChg.Stop Curr,长充关闭电池电流,长充关闭电流,C10,sint16,1,3,5.0,0.0,100.0,1.0,,
电池组,Battery Group,0x801801,0x288a01,parameter,Full Charge Max Duration,FullChg.Max Dura,长充最大时长,长充最大时长,Min,sint16,1,0,720,0,1440,1,,">=Diesel Generator Group|Min DG Running Duration;
>=Mains Group|Min MAINS Running Duration;
"
电池组,Battery Group,0x801801,0x288b01,parameter,LLVD1 Upload Voltage Backlash,LLVD1 Upload Back.,一次下电恢复回差,一次下电恢复回差,V,sint16,1,2,200.0,0.0,500.0,1.0,,
电池组,Battery Group,0x801801,0x288c01,parameter,LLVD2 Upload Voltage Backlash,LLVD2 Upload Back.,二次下电恢复回差,二次下电恢复回差,V,sint16,1,2,200.0,0.0,500.0,1.0,,
电池组,Battery Group,0x801801,0x288d01,parameter,BLVD Upload Voltage Backlash,BLVD Upload Back.,电池下电恢复回差,电池下电恢复回差,V,sint16,1,2,200.0,0.0,500.0,1.0,,
电池组,Battery Group,0x801801,0x288e01,parameter,LLVD1 Upload Time,LLVD1 Upload Time,一次下电恢复时间,一次下电恢复时间,Sec,sint16,1,0,480,300,3600,1,,
电池组,Battery Group,0x801801,0x288f01,parameter,LLVD2 Upload Time,LLVD2 Upload Time,二次下电恢复时间,二次下电恢复时间,Sec,sint16,1,0,300,300,3600,1,,
电池组,Battery Group,0x801801,0x289101,parameter,Vibration Alarm Enable,Vibration Alm. En.,振动告警使能,振动告警使能,,sint8,1,0,0,0,1,1,0:禁止/Disable;1:允许/Enable;,
电池组,Battery Group,0x801801,0x289201,parameter,Gyroscope Sensitivity,Gyroscope Sensitivity,陀螺仪灵敏度,陀螺仪灵敏度,,sint8,1,0,0,0,2,1,0:禁止/Disable;1:低灵敏度/Low;2:高灵敏度/High;,
电池组,Battery Group,0x801801,0x289301,parameter,Heartbeat Cycle,Heartbeat Cycle,心跳周期,心跳周期,min,sint32,1,0,5,3,65535,1,,
电池组,Battery Group,0x801801,0x289401,parameter,UVP Temperature Compensation Enable,UVP Temp. Compensation,整组欠压保护温度补偿,整组欠压保护温度补偿,,sint8,1,0,1,0,1,1,0:禁止/Disable;1:允许/Enable;,
电池组,Battery Group,0x801801,0x289001,parameter,Period Full Charge Start Clock,FullChg.St.Clk.,周期长充启动时刻,长充启动时刻,:00,sint16,1,0,8,0,23,1,,
电池组,Battery Group,0x801801,0x289501,parameter,Temperature Compensation Minimum,Temp.Comp.Min.,电池温度补偿下限,温度补偿下限,℃,sint16,1,0,0,-10,10,1,,
电池组,Battery Group,0x801801,0x289601,parameter,Temperature Compensation Maximum,Temp.Comp.Max.,电池温度补偿上限,温度补偿上限,℃,sint16,1,0,45,35,55,1,,
电池组,Battery Group,0x801801,0x289801,parameter,Li Battery Switch Voltage,Li Batt.Sw.Volt,锂电切换电压,锂电切换电压,V,sint16,1,1,479.0,400.0,530.0,1.0,,
电池组,Battery Group,0x801801,0x289a01,parameter,Li Battery Force Update Enable,Li Batt. Force Update Enable,锂电池强制升级使能,锂电池强制升级使能,,sint8,1,0,0,0,1,1,0:禁止/Disable;1:允许/Enable;,
电池组,Battery Group,0x801801,0x289701,parameter,Battery Full Voltage,Batt. Full Volt.,电池充满电压,电池充满电压,V,sint32,1,3,52500.0,52000.0,56000.0,1.0,,"<=Battery Group|Battery Over Voltage Alarm Threhold-1V;
"
电池组,Battery Group,0x801801,0x28a101,parameter,NFB Cell Under Voltage Alarm Threshold,NFB Cell UVA.Thre.,NFB单体欠压告警阈值,NFB单体欠压告警,V,sint16,1,2,300.0,200.0,400.0,1.0,,">=Battery Group|NFB Cell Under Voltage Protection Threshold+0.01V;
<=Battery Group|NFB Cell Under Voltage Alarm Resume Threshold-0.01V;
"
电池组,Battery Group,0x801801,0x28a201,parameter,NFB Cell Under Voltage Protection Threshold,NFB Cell UVP.Thre.,NFB单体欠压保护阈值,NFB单体欠压保护,V,sint16,1,2,290.0,200.0,400.0,1.0,,"<=Battery Group|NFB Cell Under Voltage Alarm Threshold-0.01V;
<=Battery Group|NFB Cell Under Voltage Protection Resume Threshold-0.01V;
"
电池组,Battery Group,0x801801,0x28a301,parameter,NFB Cell Under Voltage Protection Resume Threshold,NFB Cell UVP.Res.,NFB单体欠压保护恢复阈值,NFB单体UVP恢复,V,sint16,1,2,320.0,200.0,400.0,1.0,,">=Battery Group|NFB Cell Under Voltage Protection Threshold+0.01V;
"
电池组,Battery Group,0x801801,0x28a401,parameter,NFB Cell Under Voltage Alarm Resume Threshold,NFB Cell UVA.Res.,NFB单体欠压告警恢复阈值,NFB单体UVA恢复,V,sint16,1,2,320.0,200.0,400.0,1.0,,">=Battery Group|NFB Cell Under Voltage Alarm Threshold+0.01V;
"
电池组,Battery Group,0x801801,0x28a501,parameter,NFB Battery Under Voltage Alarm Threshold,NFB Batt.UVA.Thre.,NFB电池欠压告警阈值,NFB电池欠压告警,V,sint16,1,1,465.0,400.0,600.0,1.0,,">=Battery Group|NFB Battery Under Voltage Protection Threshold+0.1V;
<=Battery Group|NFB Battery Under Voltage Alarm Resume Threshold-0.1V;
"
电池组,Battery Group,0x801801,0x28a601,parameter,NFB Battery Under Voltage Protection Threshold,NFB Batt.UVP.Thre.,NFB电池欠压保护阈值,NFB电池欠压保护,V,sint16,1,1,450.0,400.0,600.0,1.0,,"<=Battery Group|NFB Battery Under Voltage Alarm Threshold-0.1V;
<=Battery Group|NFB Battery Under Voltage Protection Resume Threshold-0.1V;
"
电池组,Battery Group,0x801801,0x28a701,parameter,NFB Battery Under Voltage Protection Resume Threshold,NFB Batt.UVP.Res.,NFB电池欠压保护恢复阈值,NFB电池UVP恢复,V,sint16,1,1,480.0,400.0,600.0,1.0,,">=Battery Group|NFB Battery Under Voltage Protection Threshold+0.1V;
"
电池组,Battery Group,0x801801,0x28a801,parameter,NFB Battery Under Voltage Alarm Resume Threshold,NFB Batt.UVA.Res.,NFB电池欠压告警恢复阈值,NFB电池UVA恢复,V,sint16,1,1,480.0,400.0,600.0,1.0,,">=Battery Group|NFB Battery Under Voltage Alarm Threshold+0.1V;
"
电池组,Battery Group,0x801801,0x28a901,parameter,Battery Missing Enable,Batt.Missing En.,电池丢失使能,电池丢失使能,,sint8,1,0,1,0,1,1,0:禁止/Disable;1:允许/Enable;,
电池组,Battery Group,0x801801,0x28aa01,parameter,Battery Gyroscope Tilt,Batt.Gyro.Tilt,陀螺仪倾角,陀螺仪倾角,°,sint16,1,0,15,1,90,1,,
电池组,Battery Group,0x801801,0x28ab01,parameter,Anti-theft Line Enable,Anti-theft Line En.,防盗线使能,防盗线使能,,sint8,1,0,1,0,1,1,0:禁止/Disable;1:允许/Enable;,
电池组,Battery Group,0x801801,0x28ac01,parameter,Battery Buzzer Duration,Buzz.Dura.,蜂鸣器持续时间,蜂鸣器时间,Min,sint16,1,0,20,1,2880,1,,
电池组,Battery Group,0x801801,0x28ad01,parameter,Anti-theft CommFail Delay,CommFail Delay,防盗通信断延时,通信断延时,Min,sint16,1,0,2,2,2880,,,
电池组,Battery Group,0x801801,0x28ae01,parameter,Anti-theft GPS Distance,Anti-theft GPS Dista.,GPS防盗距离,GPS防盗距离,m,sint16,1,0,200,10,500,1,,
电池组,Battery Group,0x801801,0x28b001,parameter,LLTD Enabled,LLTD Enabled,负载低温下电使能,负载低温下电使能,,sint8,1,0,0,0,1,1,0:禁止/Disabled;1:允许/Enabled;,
电池组,Battery Group,0x801801,0x28b101,parameter,LLTD Temperature,LLTD Temp.,负载低温下电温度,负载低温下电温度,℃,sint16,1,0,-15,-40,10,1,,
电池组,Battery Group,0x801801,0x28b201,parameter,LHTD Enabled,LHTD Enabled,负载高温下电使能,负载高温下电使能,,sint8,1,0,0,0,1,1,0:禁止/Disabled;1:允许/Enabled;,
电池组,Battery Group,0x801801,0x28b301,parameter,LHTD Temperature,LHTD Temp.,负载高温下电温度,负载高温下电温度,℃,sint16,1,0,50,36,60,1,,">=Battery Group|LHTD Recover Temperature+1℃;
"
电池组,Battery Group,0x801801,0x28b401,parameter,LHTD Recover Temperature,LHTD Re.Temp.,负载高温下电恢复温度,负载高温下电恢复温度,℃,sint16,1,0,44,35,59,1,,"<=Battery Group|LHTD Temperature-1℃;
"
电池组,Battery Group,0x801801,0x28b501,parameter,LHTD Judge Time,LHTD Judge Time,LHTD判断时间,LHTD判断时间,Min,sint16,1,0,3,1,10,1,,
电池组,Battery Group,0x801801,0x28b601,parameter,BMS Remote IP Address,BMS Remote IP,BMS远程IP地址,BMS远程IP,,string32,1,0,0.0.0.0,0.0.0.0,***************,,,
电池组,Battery Group,0x801801,0x28b701,parameter,BMS Remote Port,BMS Remote Port,BMS远程端口,BMS远程端口,,sint16,1,0,0,0,65535,,,
电池组,Battery Group,0x801801,0x28b801,parameter,BMS GPRS User Name,BMS GPRS User Name,BMS GPRS用户名,BMS GPRS用户名,,string32,1,0,smsong,,,,,
电池组,Battery Group,0x801801,0x28b901,parameter,BMS GPRS Password,BMS GPRS Password,BMS GPRS密码,BMS GPRS密码,,string32,1,0,123456,,,,,
电池组,Battery Group,0x801801,0x28ba01,parameter,BMS GPRS Access Point Name,BMS GPRS APN,BMS GPRS接入点名称,BMS GPRS接入点名称,,string32,1,0,CMNET,,,,,
电池组,Battery Group,0x801801,0x28bb01,parameter,BMS SMS Center Number,BMS SMS Center Num,BMS短信中心号码,BMS短信中心号码,,string32,1,0,+8600000000000,,,,,
电池组,Battery Group,0x801801,0x28bc01,parameter,Battery Discharge Maximum Current,Batt.Dischg.Max.Curr.,放电最大电流,放电最大电流,C3,sint16,1,2,105.0,40.0,300.0,,,
电池组,Battery Group,0x801801,0x28bd01,parameter,Power Down Voltage Threshold,Power Down Volt.Ths,掉电电压阈值,掉电电压阈值,V,sint16,1,2,4800.0,4200.0,5800.0,,,
电池组,Battery Group,0x801801,0x28be01,parameter,Cell Dynamic Under Voltage Protect Enable,Cell under volt.prot,单体动态欠压保护,单体动态欠压保护,,sint8,1,0,0,0,1,,0:禁止/Disable;1:允许/Enable;,
电池组,Battery Group,0x801801,0x28c001,parameter,LLVD1 Fixed Time Disconnect Enabled,LLVD1 FixT.Disc En.,定时一次下电使能,定时一次下电使能,,sint8,1,0,0,0,1,1,0:禁止/Disabled;1:允许/Enabled;,
电池组,Battery Group,0x801801,0x28c101,parameter,LLVD1 Fixed Time Disconnect Start Clock,LLVD1 FixT.Disc St.,定时一次下电起始时刻,定时一次下电起始时刻,,hour_minute,5,0,8:00,0:00,23:59,1,,
电池组,Battery Group,0x801801,0x28c201,parameter,LLVD1 Fixed Time Disconnect End Clock,LLVD1 FixT.Disc End.,定时一次下电终止时刻,定时一次下电终止时刻,,hour_minute,5,0,8:00,0:00,23:59,1,,
电池组,Battery Group,0x801801,0x28c301,parameter,LLVD2 Fixed Time Disconnect Enabled,LLVD2 FixT.Disc En.,定时二次下电使能,定时二次下电使能,,sint8,1,0,0,0,1,1,0:禁止/Disabled;1:允许/Enabled;,
电池组,Battery Group,0x801801,0x28c401,parameter,LLVD2 Fixed Time Disconnect Start Clock,LLVD2 FixT.Disc St.,定时二次下电起始时刻,定时二次下电起始时刻,,hour_minute,5,0,8:00,0:00,23:59,1,,
电池组,Battery Group,0x801801,0x28c501,parameter,LLVD2 Fixed Time Disconnect End Clock,LLVD2 FixT.Disc End.,定时二次下电终止时刻,定时二次下电终止时刻,,hour_minute,5,0,8:00,0:00,23:59,1,,
电池组,Battery Group,0x801801,0x300501,record data,Battery Discharge History Time,Dischg.His.Time,电池放电记录时间,放电历史时间,,process_time,1,,,,,,,
电池组,Battery Group,0x801801,0x301801,record data,Battery Charge Record Time,Batt.Chag.Rec.Time,电池充电记录时间,电池充电记录时间,,process_time,1,,,,,,,
电池组,Battery Group,0x801801,0x301901,record data,Battery Equalized Charge Record Time,Batt.Equal.Rec.Time,电池均充记录时间,电池均充记录时间,,process_time,1,,,,,,,
电池组,Battery Group,0x801801,0x301a01,record data,Battery Test Record Time,Batt.Test Rec.Time,电池测试记录时间,电池测试记录时间,,process_time,1,,,,,,,
电池组,Battery Group,0x801801,0x300601,record data,Battery Discharge Duration,Discharge Dura.,电池放电持续时间,放电持续时间,Min,sint32,1,0,0,0,1440,,,
电池组,Battery Group,0x801801,0x300c01,record data,Battery Test Duration,Batt.Test Dura.,电池测试持续时间,测试持续时间,Min,sint32,1,0,0,0,1440,,,
电池组,Battery Group,0x801801,0x301301,record data,Record Duration,Record Duration,记录持续时间,记录持续时间,Min,sint32,1,0,0,0,1440,,,
电池组,Battery Group,0x801801,0x301401,record data,Record Stop Cause,Stop Cause,记录结束原因,结束原因,,sint8,1,0,0,0,20,,0:无/Null;1:手动/Manual;2:系统停电/PowerOFF;3:电池组异常/Batt Fault;4:电池下电/Batt Disconnect;5:定时器异常/TimerFault;6:无输入/Input None;7:电池电流异常/Batt Curr Fault;8:最大时间/Max Time;9:维持时间/Duration;10:电池温度高/Batt Temp High;11:直流电压高/Dc_Volt_High;12:均充禁止/Equal Disabled;13:环境温度高/Env Temp High;14:预约均充时长/Pre.Equ.Chg.Dura.;15:测试最大时间/Test Max Time;16:测试电压/Test Volt;17:测试容量/Test Cap;18:IDDB通讯断/IDDB comm. fail;19:FBBMS通讯断/FBBMS comm. fail;20:循环场景/Cycle Scenario;,
电池组,Battery Group,0x801801,0x301501,record data,Record Start Cause,Start Cause,记录开始原因,开始原因,,sint8,1,0,0,0,12,,0:无/Null;1:手动/Manual;2:定期/Periodic;3:预约/Order;4:测试电压/Test Volt;5:测试容量/Test Cap;6:系统停电/PowerOff;7:均充阈值电流/Equ.Thre.Curr.;8:均充延续/Equal Continue;9:测试时间/Test Time;10:均充阈值电压/Equ.Thre.Volt.;11:均充阈值容量/Equ.Thre.Cap;12:均充阈值时间/Equ.Thre.Dura.;,
电池组,Battery Group,0x801801,0x301601,record data,Record Stop Battery Status,Stop Battery Status,记录结束电池状态,结束电池状态,,sint8,1,0,0,0,6,,0:浮充/Float;1:均充/Equal;2:测试/Test;3:停电/Power Off;4:检测/Detect;5:过渡/Trans;6:充电/Charge;,
电池组,Battery Group,0x801801,0x301701,record data,Record Start Battery Status,Start Battery Status,记录开始电池状态,开始电池状态,,sint8,1,0,0,0,6,,0:浮充/Float;1:均充/Equal;2:测试/Test;3:停电/Power Off;4:检测/Detect;5:过渡/Trans;6:充电/Charge;,
直流负载,DC Load,0x801a01,0x380101,stastic data,Load Power Consumption,Load Power Cons.,负载用电量,负载用电量,kWh,sint32,1,2,,,,,,
太阳能,Solar Energy,0x801e01,0x180101,alarm,PV SPD Abnormal,PV SPD Abr.,光伏防雷器异常,光伏防雷器异常,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
太阳能,Solar Energy,0x801e01,0x480101,alarm DO,PV SPD Abnormal,PV SPD Abr.,光伏防雷器异常,光伏防雷器异常,,sint8,1,0,,,,,,
太阳能,Solar Energy,0x801e01,0x180301,alarm,PV component Missing,PV.Comp.Missing,光伏组件丢失,光伏组件丢失,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
太阳能,Solar Energy,0x801e01,0x480301,alarm DO,PV component Missing,PV.Comp.Missing,光伏组件丢失,光伏组件丢失,,sint8,1,0,,,,,,
太阳能,Solar Energy,0x801e01,0x180401,alarm,All PU Commfail Alarm,All PU Commfail.,所有PU模块通讯断告警,所有PU模块通讯断,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
太阳能,Solar Energy,0x801e01,0x480401,alarm DO,All PU Commfail Alarm,All PU Commfail.,所有PU模块通讯断告警,所有PU模块通讯断,,sint8,1,0,,,,,,
太阳能,Solar Energy,0x801e01,0x200101,control,PU Device Statistic,PU Dev.Stat.,PU设备统计,PU设备统计,,sint8,1,,,,,,,
太阳能,Solar Energy,0x801e01,0x280101,parameter,PU Output OVP Voltage Threshold,PU Out.OVP Volt.,PU输出过压保护值,PU输出过压值,V,sint16,1,1,600.0,570.0,610.0,10.0,,">=Battery Group|Equalized Charge Voltage+2V;
>=Battery Group|Charge Voltage+2V;
>=Battery Group|Float Charge Voltage+2V;
"
太阳能,Solar Energy,0x801e01,0x280201,parameter,PU Default Ouput Voltage,PU.Def.Out.Volt.,PU默认输出电压值,默认输出电压值,V,sint16,1,1,545.0,420.0,580.0,10.0,,
太阳能,Solar Energy,0x801e01,0x280601,parameter,Smart Photovoltaic System Default Output Voltage,SPV Def.Out.Vol.,叠光电源默认输出电压,叠光默认输出电压,V,sint16,1,1,490.0,420.0,580.0,1.0,,
太阳能,Solar Energy,0x801e01,0x300301,record data,PV Initial Power Generation,PV Init.Power Generation,光伏起始发电量,光伏起始发电量,kWh,sint32,1,2,,,,,,
太阳能,Solar Energy,0x801e01,0x300401,record data,PV Final Power Generation,PV final Power Generation,光伏终止发电量,光伏终止发电量,kWh,sint32,1,2,,,,,,
太阳能,Solar Energy,0x801e01,0x380101,stastic data,PV Power Total Generation,PV Total Pwr.Gene.,累计光伏发电量,累计光伏发电量,kWh,sint32,1,2,,,,,,
太阳能,Solar Energy,0x801e01,0x380201,stastic data,PV Work Duration,PV Work Dura.,光伏工作时间,光伏工作时间,Min,sint32,1,0,,,,,,
太阳能,Solar Energy,0x801e01,0x300501,record data,PV Work Record Time,PV.Wrk.Rec.,太阳能工作记录时间,太阳能工作记录时间,,process_time,1,,,,,,,
太阳能模块,Power Unit,0x802001,0x080101,analog data,Power Unit Output Voltage,PU Output Volt.,PU输出电压,PU输出电压,V,sint16,48,2,5350.0,0.0,6000.0,,,
太阳能模块,Power Unit,0x802001,0x080201,analog data,Power Unit Output Current,PU Output Curr.,PU输出电流,PU输出电流,A,sint16,48,2,0.0,0.0,5500.0,,,
太阳能模块,Power Unit,0x802001,0x080301,analog data,Power Unit Input Voltage,PU Input Volt.,PU输入电压,PU输入电压,V,sint16,48,0,65,0,150,,,
太阳能模块,Power Unit,0x802001,0x080401,analog data,Power Unit Temperature,PU Temp.,PU温度,PU温度,℃,sint16,48,1,250.0,-400.0,700.0,,,
太阳能模块,Power Unit,0x802001,0x080501,analog data,Power Unit Input Current,PU Input Curr.,PU输入电流,PU输入电流,A,sint16,48,1,0.0,0.0,350.0,,,
太阳能模块,Power Unit,0x802001,0x080601,analog data,Power Unit Heat Sink Temperature,PU Heat Sink Temp.,PU散热片温度,PU散热片温度,℃,sint16,48,1,250.0,-400.0,1500.0,,,
太阳能模块,Power Unit,0x802001,0x080701,analog data,Power Unit Fan Speed,PU Fan Speed.,PU风扇转速,PU风扇转速,%,sint16,48,0,20,0,100,,,
太阳能模块,Power Unit,0x802001,0x080801,analog data,Power Unit Group Address,PU Group Address,PU组地址,PU组地址,,sint16,48,0,1,1,12,,,
太阳能模块,Power Unit,0x802001,0x080901,analog data,Power Unit Group Inner Address,PU Group Inner Address,PU组内地址,PU组内地址,,sint16,48,0,1,1,12,,,
太阳能模块,Power Unit,0x802001,0x080a01,analog data,Power Unit Output Power,PU Output Power,PU输出功率,PU输出功率,W,sint32,48,1,0.0,0.0,100000.0,,,
太阳能模块,Power Unit,0x802001,0x080b01,analog data,Power Unit Slot Address,PU Slot Address,PU槽位地址,PU槽位地址,,sint16,48,0,1,1,48,,,
太阳能模块,Power Unit,0x802001,0x100101,digital data,Placeholder,Placeholder,占位符,占位符,,uint32_bit,48,0,0,0,1,,0:否/No;1:是/Yes;,
太阳能模块,Power Unit,0x802001,0x100101,digital data,Power Unit Off Status,PU Off,PU关机状态,PU关机,,bit1_2,48,0,0,0,1,,0:否/No;1:是/Yes;,
太阳能模块,Power Unit,0x802001,0x100201,digital data,Power Unit P2P Status,PU P2P,PU一键功能状态,PU一键功能,,bit3_2,48,0,0,0,1,,0:否/No;1:是/Yes;,
太阳能模块,Power Unit,0x802001,0x100301,digital data,Power Unit MPPT Status,MPPT,PU MPPT状态,MPPT状态,,bit5_2,48,0,0,0,1,,0:否/No;1:是/Yes;,
太阳能模块,Power Unit,0x802001,0x100401,digital data,Power Unit Working Status,PU Work Status,PU工作状态,PU工作状态,,bit9_4,48,0,0,0,4,,0:无/Null;1:正常/Normal;2:告警/Alarm;3:通讯断/CommFail;4:升级/Update;,
太阳能模块,Power Unit,0x802001,0x100601,digital data,Power Unit Output Current Limit Status,PU Ouput CL,PU输出限流状态,PU输出限流,,bit11_2,48,0,0,0,1,,0:否/No;1:是/Yes;,
太阳能模块,Power Unit,0x802001,0x100701,digital data,Power Unit Sleep Status,PU Sleep Sts.,PU休眠状态,PU休眠状态,,bit13_2,48,0,0,0,1,,0:否/No;1:是/Yes;,
太阳能模块,Power Unit,0x802001,0x100801,digital data,PU Fan Control State,PU Fan Ctrl.,PU风扇控制状态,PU风扇控制,,bit15_2,48,0,0,0,1,,0:自动/Auto;1:全速/Full Speed;,
太阳能模块,Power Unit,0x802001,0x100901,digital data,PU Software Update Enable,PU Update Enable,PU升级使能状态,PU升级使能状态,,bit17_2,48,0,0,0,1,,0:禁止/Disabled;1:允许/Enabled;,
太阳能模块,Power Unit,0x802001,0x100a01,digital data,PU Input CurrentLimit,PU Input C.Lim.,PU输入限流状态,PU输入限流状态,,bit19_2,48,0,0,0,1,,0:否/No;1:是/Yes;,
太阳能模块,Power Unit,0x802001,0x101901,digital data,Power Unit Input UVP Status,PU Input UVP Stu.,PU输入欠压状态,PU输入欠压状态,,bit21_2,48,0,0,0,1,,0:正常/Normal;1:异常/Fault;,
太阳能模块,Power Unit,0x802001,0x101a01,digital data,Power Unit Input none Status,PU In.none Stu.,PU无输入状态,PU无输入状态,,bit23_2,48,0,0,0,1,,0:否/No;1:是/Yes;,
太阳能模块,Power Unit,0x802001,0x101c01,digital data,Power Unit Input Flow Back Status,PU In.Flw.Bck.Stu,PU输入倒灌状态,PU输入倒灌状态,,bit25_2,48,0,0,0,1,,0:正常/Normal;1:异常/Fault;,
太阳能模块,Power Unit,0x802001,0x180101,alarm,Power Unit Input OVP Alarm,PU In. OVP.,PU输入过压告警,PU输入过压,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
太阳能模块,Power Unit,0x802001,0x480101,alarm DO,Power Unit Input OVP Alarm,PU In. OVP.,PU输入过压告警,PU输入过压,,sint8,48,0,,,,,,
太阳能模块,Power Unit,0x802001,0x180201,alarm,Power Unit Output OVP Alarm,PU Out. OVP,PU输出过压告警,PU输出过压,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
太阳能模块,Power Unit,0x802001,0x480201,alarm DO,Power Unit Output OVP Alarm,PU Out. OVP,PU输出过压告警,PU输出过压,,sint8,48,0,,,,,,
太阳能模块,Power Unit,0x802001,0x180301,alarm,Power Unit OTP Alarm,PU OTP Alm.,PU过温告警,PU过温告警,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
太阳能模块,Power Unit,0x802001,0x480301,alarm DO,Power Unit OTP Alarm,PU OTP Alm.,PU过温告警,PU过温告警,,sint8,48,0,,,,,,
太阳能模块,Power Unit,0x802001,0x180401,alarm,Power Unit Output Over Current Alarm,PU Output OC,PU输出过流告警,PU输出过流,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
太阳能模块,Power Unit,0x802001,0x480401,alarm DO,Power Unit Output Over Current Alarm,PU Output OC,PU输出过流告警,PU输出过流,,sint8,48,0,,,,,,
太阳能模块,Power Unit,0x802001,0x180501,alarm,Power Unit Output Fuse Failure Alarm,PU Out.Fuse,PU输出熔丝断告警,PU输出熔丝断,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
太阳能模块,Power Unit,0x802001,0x480501,alarm DO,Power Unit Output Fuse Failure Alarm,PU Out.Fuse,PU输出熔丝断告警,PU输出熔丝断,,sint8,48,0,,,,,,
太阳能模块,Power Unit,0x802001,0x180601,alarm,Power Unit Radiator OTP Alarm,PU Ra. OTP.,PU散热器过温告警,PU散热器过温,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
太阳能模块,Power Unit,0x802001,0x480601,alarm DO,Power Unit Radiator OTP Alarm,PU Ra. OTP.,PU散热器过温告警,PU散热器过温,,sint8,48,0,,,,,,
太阳能模块,Power Unit,0x802001,0x180701,alarm,Power Unit Fan Fault Alarm,PU Fan Fault,PU风扇故障,PU风扇故障,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
太阳能模块,Power Unit,0x802001,0x480701,alarm DO,Power Unit Fan Fault Alarm,PU Fan Fault,PU风扇故障,PU风扇故障,,sint8,48,0,,,,,,
太阳能模块,Power Unit,0x802001,0x180801,alarm,Power Unit EEPROM Failure Alarm,PU EEPROM,PU EEPROM异常,PU EEPROM异常,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
太阳能模块,Power Unit,0x802001,0x480801,alarm DO,Power Unit EEPROM Failure Alarm,PU EEPROM,PU EEPROM异常,PU EEPROM异常,,sint8,48,0,,,,,,
太阳能模块,Power Unit,0x802001,0x180901,alarm,Power Unit Output UVP Alarm,PU Out.UVP.,PU 输出欠压,PU输出欠压,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
太阳能模块,Power Unit,0x802001,0x480901,alarm DO,Power Unit Output UVP Alarm,PU Out.UVP.,PU 输出欠压,PU输出欠压,,sint8,48,0,,,,,,
太阳能模块,Power Unit,0x802001,0x180a01,alarm,PV Loop Abnormal Alarm,PV Loop Abn.,光伏回路异常告警,光伏回路异常,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
太阳能模块,Power Unit,0x802001,0x480a01,alarm DO,PV Loop Abnormal Alarm,PV Loop Abn.,光伏回路异常告警,光伏回路异常,,sint8,48,0,,,,,,
太阳能模块,Power Unit,0x802001,0x180b01,alarm,PU SN Clash Alarm,PU SN Clash,PU序列号冲突,PU SN冲突,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
太阳能模块,Power Unit,0x802001,0x480b01,alarm DO,PU SN Clash Alarm,PU SN Clash,PU序列号冲突,PU SN冲突,,sint8,48,0,,,,,,
太阳能模块,Power Unit,0x802001,0x180c01,alarm,PU Protocol Error,PU Pro.Err.,PU协议错误,PU协议错误,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
太阳能模块,Power Unit,0x802001,0x480c01,alarm DO,PU Protocol Error,PU Pro.Err.,PU协议错误,PU协议错误,,sint8,48,0,,,,,,
太阳能模块,Power Unit,0x802001,0x180d01,alarm,Power Unit Input Over Current Alarm,PU Input OC.,PU输入过流告警,PU输入过流,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
太阳能模块,Power Unit,0x802001,0x480d01,alarm DO,Power Unit Input Over Current Alarm,PU Input OC.,PU输入过流告警,PU输入过流,,sint8,48,0,,,,,,
太阳能模块,Power Unit,0x802001,0x180e01,alarm,PU Alarm,PU Alarm,PU告警,PU告警,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
太阳能模块,Power Unit,0x802001,0x480e01,alarm DO,PU Alarm,PU Alarm,PU告警,PU告警,,sint8,48,0,,,,,,
太阳能模块,Power Unit,0x802001,0x180f01,alarm,PU Communication Fail,PU Comm.Fail,PU通讯中断,PU通讯中断,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
太阳能模块,Power Unit,0x802001,0x480f01,alarm DO,PU Communication Fail,PU Comm.Fail,PU通讯中断,PU通讯中断,,sint8,48,0,,,,,,
太阳能模块,Power Unit,0x802001,0x181001,alarm,Power Unit Input UVP Alarm,PU Input UVP,PU输入欠压告警,PU输入欠压,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
太阳能模块,Power Unit,0x802001,0x481001,alarm DO,Power Unit Input UVP Alarm,PU Input UVP,PU输入欠压告警,PU输入欠压,,sint8,48,0,,,,,,
太阳能模块,Power Unit,0x802001,0x181101,alarm,Power Unit Input none Alarm,PU Input none,PU无输入告警,PU无输入,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
太阳能模块,Power Unit,0x802001,0x481101,alarm DO,Power Unit Input none Alarm,PU Input none,PU无输入告警,PU无输入,,sint8,48,0,,,,,,
太阳能模块,Power Unit,0x802001,0x181201,alarm,Power Unit Output Shortcut Alarm,PU Shortcut Alm.,PU输出短路告警,PU输出短路,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
太阳能模块,Power Unit,0x802001,0x481201,alarm DO,Power Unit Output Shortcut Alarm,PU Shortcut Alm.,PU输出短路告警,PU输出短路,,sint8,48,0,,,,,,
太阳能模块,Power Unit,0x802001,0x181301,alarm,Power Unit Input Flow Back Alarm,PU Input Flw.Back,PU输入倒灌告警,PU输入倒灌,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
太阳能模块,Power Unit,0x802001,0x481301,alarm DO,Power Unit Input Flow Back Alarm,PU Input Flw.Back,PU输入倒灌告警,PU输入倒灌,,sint8,48,0,,,,,,
太阳能模块,Power Unit,0x802001,0x181401,alarm,PU Fault,PU Fault,PU故障,PU故障,,sint8,48,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
太阳能模块,Power Unit,0x802001,0x481401,alarm DO,PU Fault,PU Fault,PU故障,PU故障,,sint8,48,0,,,,,,
太阳能模块,Power Unit,0x802001,0x200101,control,PU Waken,PU Waken,PU唤醒,PU唤醒,,sint8,48,,,,,,,
太阳能模块,Power Unit,0x802001,0x200201,control,PU Sleep,PU Sleep,PU休眠,PU休眠,,sint8,48,,,,,,,
太阳能模块,Power Unit,0x802001,0x200501,control,PU Fan Control Enable,Fan Ctrl.En.,PU风扇调速允许,PU风扇调速允许,,sint8,48,,,,,,,
太阳能模块,Power Unit,0x802001,0x200601,control,PU Fan Control Disable,Fan Ctrl.Dis.,PU风扇调速禁止,PU风扇调速禁止,,sint8,48,,,,,,,
太阳能模块,Power Unit,0x802001,0x200801,control,PU Communication Fail alarm clear,PU Commfail clear,PU通讯中断告警清除,PU通讯中断清除,,sint8,48,,,,,,,
太阳能模块,Power Unit,0x802001,0x400101,device info,Power Unit Serial Number,PU SN,PU序列号,PU序列号,,string32,48,,,,,,,
太阳能模块,Power Unit,0x802001,0x400201,device info,Power Unit Software Version,PU Software Ver.,PU软件版本,PU软件版本,,string16,48,,,,,,,
太阳能模块,Power Unit,0x802001,0x400301,device info,Power Unit Software Release Date,PU Software Date,PU软件发布日期,PU软件发布日期,,date,48,,,,,,,
太阳能模块,Power Unit,0x802001,0x400401,device info,PU Digital Control Platform Version,PU Platform.,PU数控平台版本,PU数控平台,,string16,48,,,,,,,
太阳能模块,Power Unit,0x802001,0x400501,device info,PU System Name,PU Sys Name,PU系统名称,PU系统名称,,string32,48,,,,,,,
太阳能模块,Power Unit,0x802001,0x400601,device info,PU Manufactory ID,PU Manufactory ID,制造商ID,制造商ID,,sint16,48,0,1,1,1000,,,
太阳能模块,Power Unit,0x802001,0x400701,device info,PU Manufactory Address,Manufty.Address,制造商地址,制造商地址,,sint16,48,0,1,1,1000,,,
太阳能模块,Power Unit,0x802001,0x400801,device info,PU Barcodes,PU Barcodes,PU条码,PU条码,,string16,48,,,,,,,
太阳能模块,Power Unit,0x802001,0x400901,device info,PU Manufacture Date,Manufacture Date,生产日期,生产日期,,date,48,,,,,,,
FBBMS,FBBMS,0x802e01,0x080101,analog data,PACK Voltage,PACK Volt.,PACK电压,PACK电压,V,sint16,1,1,544.0,0.0,600.0,,,
FBBMS,FBBMS,0x802e01,0x080201,analog data,Inner Environment Temperature,Inner Env.Temp.,机内环境温度,机内环境温度,℃,sint16,1,0,50,-30,100,,,
FBBMS,FBBMS,0x802e01,0x080301,analog data,PACK Current,PACK Curr.,PACK电流,PACK电流,A,sint16,1,1,0.0,-3270.0,3270.0,,,
FBBMS,FBBMS,0x802e01,0x080401,analog data,Cell Voltage,Cell Volt,单体电压,单体电压,V,sint16,15,3,3500.0,0.0,4000.0,,,
FBBMS,FBBMS,0x802e01,0x080501,analog data,Cell Temperature,Cell Temp,单体温度,单体温度,℃,sint16,4,0,25,-30,90,,,
FBBMS,FBBMS,0x802e01,0x080601,analog data,Battery SOC,Batt.SOC,电池SOC,电池SOC,%,sint16,1,0,2,0,100,,,
FBBMS,FBBMS,0x802e01,0x080701,analog data,Battery SOH,Battery SOH,电池SOH,电池SOH,%,sint16,1,0,0,0,100,,,
FBBMS,FBBMS,0x802e01,0x080801,analog data,Battery Total Recycle Times,Total Cyc.Times,电池累计循环次数,累计循环次数,,sint32,1,0,0,0,65535,,,
FBBMS,FBBMS,0x802e01,0x080901,analog data,Li Battery Voltage,Li Batt.Volt,锂电电压,锂电电压,V,sint16,1,2,5440.0,0.0,6000.0,,,
FBBMS,FBBMS,0x802e01,0x080a01,analog data,Li Battery Current,Li Batt.Curr,锂电电流,锂电电流,A,sint32,1,2,0.0,-10000.0,10000.0,,,
FBBMS,FBBMS,0x802e01,0x080b01,analog data,Charge Remain Time,Chg.Remain Time,充电剩余时间,充电剩余时间,min,sint32,1,0,0,0,65535,,,
FBBMS,FBBMS,0x802e01,0x080c01,analog data,DisCharge Remain Time,DisChg.Remain Time,放电剩余时间,放电剩余时间,min,sint32,1,0,0,0,65535,,,
FBBMS,FBBMS,0x802e01,0x080d01,analog data,Current Charging Voltage,Curr.Chg.Volt.,当前设定充电电压,当前设定充电电压,V,sint16,1,2,4200.0,4200.0,5800.0,,,
FBBMS,FBBMS,0x802e01,0x080e01,analog data,Current Discharging Voltage,Curr.Dischg.Volt.,当前设定放电电压,当前设定放电电压,V,sint16,1,2,3800.0,3800.0,5800.0,,,
FBBMS,FBBMS,0x802e01,0x080f01,analog data,Current Charging Limit Ratio,Curr.Chg.Lim.Rat.,当前设定充电限电流比例,设定充电限电流比例,‰,sint16,1,0,0,0,1000,,,
FBBMS,FBBMS,0x802e01,0x081001,analog data,Current Discharge Limit Ratio,Curr.Dischg.Lim.Rat.,当前设定放电限电流比例,设定放电限电流比例,‰,sint16,1,0,0,0,1000,,,
FBBMS,FBBMS,0x802e01,0x081101,analog data,Board Temperature,Board Temper.,单板温度,单板温度,℃,sint16,1,1,-400.0,-400.0,1000.0,,,
FBBMS,FBBMS,0x802e01,0x380101,stastic data,Discharge Total Power,Dis.Chg Total Power,累计放电电量,累计放电电量,kWh,sint32,1,2,0.0,0.0,6553600.0,,,
FBBMS,FBBMS,0x802e01,0x380201,stastic data,Discharge Toatal Capacity,Dis.Chg Total Cap.,累计放电容量,累计放电容量,Ah,sint32,1,0,0,0,65536,,,
FBBMS,FBBMS,0x802e01,0x280201,parameter,Battery Enable Time,Battery En.Time,启用日期,启用日期,,date,1,0,2010/1/1,2000/1/1,2037/12/31,,,
FBBMS,FBBMS,0x802e01,0x100101,digital data,Placeholder,Placeholder,占位符,占位符,,uint32_bit,1,0,0,0,1,1,0:关闭/Off;1:启动/Open;,
FBBMS,FBBMS,0x802e01,0x100101,digital data,Cell Equalization Circuit Status,Cell Equ.Stu.,单体均衡启动状态,单体均衡状态,,bit1_2,1,0,0,0,1,1,0:关闭/Off;1:启动/Open;,
FBBMS,FBBMS,0x802e01,0x100201,digital data,Battery Run Status,Batt.Run Stat.,电池运行状态,电池运行状态,,bit5_4,1,0,0,0,2,,0:充电/Charge;1:放电管理/Discharge;2:在线非浮充/On Not Float;3:离线/Off-line;,
FBBMS,FBBMS,0x802e01,0x100301,digital data,Battery Charge Protection,Batt.CP,电池充电保护,电池充电保护,,bit7_2,1,0,0,0,1,,0:否/No;1:是/Yes;,
FBBMS,FBBMS,0x802e01,0x100401,digital data,Battery Discharge Protection,Batt.DP,电池放电保护,电池放电保护,,bit9_2,1,0,0,0,1,,0:否/No;1:是/Yes;,
FBBMS,FBBMS,0x802e01,0x103701,digital data,Charge Input Break State,Charge In.Break.Sta.,充电输入断状态,充电输入断状态,,bit15_2,1,0,0,0,1,,0:正常/Normal;1:断开/Break;,
FBBMS,FBBMS,0x802e01,0x103901,digital data,Charge Prohibit Status,Charge Proh.Sta.,充电禁止状态,充电禁止状态,,bit17_2,1,0,0,0,1,,0:否/No;1:是/Yes;,
FBBMS,FBBMS,0x802e01,0x103a01,digital data,Discharge Prohibit Status,Discharge Proh.Sta.,放电禁止状态,放电禁止状态,,bit19_2,1,0,0,0,1,,0:否/No;1:是/Yes;,
FBBMS,FBBMS,0x802e01,0x103b01,digital data,BDCU Management Status,BDCU Manag.Sta.,BDCU管理状态,BDCU管理状态,,sint8,1,0,0,0,9,,0:常规充电/Common Charge;1:常规放电/Common Disch.;2:升压充电/Set Up Charge;3:升压放电/Set Up Disch.;4:降压充电/Set Down Charge;5:降压放电/Set Down Disch.;6:充放电停止/Charge or Disch.Stop;7:闭锁/Lock;8:直通充电/Through Charge;9:直通放电/Through Disch.;,
FBBMS,FBBMS,0x802e01,0x103c01,digital data,Master Or Slave State,Master.Slave Sta.,主从机状态,主从机状态,,sint8,1,0,0,0,1,,0:主机/Master;1:从机/Slave;,
FBBMS,FBBMS,0x802e01,0x180101,alarm,Battery Charge Over Current Protection Alm,Batt.Chg.OCP.,电池充电过流保护,电池充过流保护,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x480101,alarm DO,Battery Charge Over Current Protection Alm,Batt.Chg.OCP.,电池充电过流保护,电池充过流保护,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x180201,alarm,Battery Discharge Over Current Protection Alm,Batt.Disch.OCP.,电池放电过流保护,电池放过流保护,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x480201,alarm DO,Battery Discharge Over Current Protection Alm,Batt.Disch.OCP.,电池放电过流保护,电池放过流保护,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x180301,alarm,Battery Over Voltage Protection Alm,Batt.OVP Alm,电池过压保护,电池过压保护,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x480301,alarm DO,Battery Over Voltage Protection Alm,Batt.OVP Alm,电池过压保护,电池过压保护,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x180401,alarm,Board Over Temperature Protection Alm,Board OTP.Alm,单板过温保护,单板过温保护,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x480401,alarm DO,Board Over Temperature Protection Alm,Board OTP.Alm,单板过温保护,单板过温保护,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x180501,alarm,Battery Charge Over Current Alm,Batt.Chg.OCA.,电池充电过流告警,电池充过流,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x480501,alarm DO,Battery Charge Over Current Alm,Batt.Chg.OCA.,电池充电过流告警,电池充过流,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x180601,alarm,Battery Discharge Over Current Alm,Batt.Disch.OCA.,电池放电过流告警,电池放过流,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x480601,alarm DO,Battery Discharge Over Current Alm,Batt.Disch.OCA.,电池放电过流告警,电池放过流,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x180701,alarm,Battery Over Voltage Alm,Batt.OVA.Alm,电池过压告警,电池过压,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x480701,alarm DO,Battery Over Voltage Alm,Batt.OVA.Alm,电池过压告警,电池过压,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x180801,alarm,Inner Environment Over Temperature Alm,Inner Env.OTA.,机内环境温度高告警,机内环境过温,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x480801,alarm DO,Inner Environment Over Temperature Alm,Inner Env.OTA.,机内环境温度高告警,机内环境过温,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x180901,alarm,Inner Environment Under Temperature Alm,Inner Env.UTA.,机内环境温度低告警,机内环境低温,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x480901,alarm DO,Inner Environment Under Temperature Alm,Inner Env.UTA.,机内环境温度低告警,机内环境低温,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x180a01,alarm,Battery Under Voltage Alm,Batt.UVA Alm,电池欠压告警,电池欠压,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x480a01,alarm DO,Battery Under Voltage Alm,Batt.UVA Alm,电池欠压告警,电池欠压,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x180b01,alarm,Battery Under Voltage Protection Alm,Batt.UVP Alm,电池欠压保护,电池欠压保护,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x480b01,alarm DO,Battery Under Voltage Protection Alm,Batt.UVP Alm,电池欠压保护,电池欠压保护,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x180c01,alarm,Cell Over Volatge Alm,Cell OVA Alm,单体过压告警,单体过压,,sint8,15,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x480c01,alarm DO,Cell Over Volatge Alm,Cell OVA Alm,单体过压告警,单体过压,,sint8,15,0,,,,,,
FBBMS,FBBMS,0x802e01,0x180d01,alarm,Cell Over Volatge Protection Alm,Cell OVP Alm,单体过压保护,单体过压保护,,sint8,15,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x480d01,alarm DO,Cell Over Volatge Protection Alm,Cell OVP Alm,单体过压保护,单体过压保护,,sint8,15,0,,,,,,
FBBMS,FBBMS,0x802e01,0x180e01,alarm,Cell Under Voltage Alm,Cell UVA Alm,单体欠压告警,单体欠压,,sint8,15,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x480e01,alarm DO,Cell Under Voltage Alm,Cell UVA Alm,单体欠压告警,单体欠压,,sint8,15,0,,,,,,
FBBMS,FBBMS,0x802e01,0x180f01,alarm,Cell Under Voltage Protection Alm,Cell UVP Alm,单体欠压保护,单体欠压保护,,sint8,15,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x480f01,alarm DO,Cell Under Voltage Protection Alm,Cell UVP Alm,单体欠压保护,单体欠压保护,,sint8,15,0,,,,,,
FBBMS,FBBMS,0x802e01,0x181001,alarm,Cell Charge Over Temperature Alm,Cell Chg.OTA.,单体充电高温告警,单体充高温,,sint8,4,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x481001,alarm DO,Cell Charge Over Temperature Alm,Cell Chg.OTA.,单体充电高温告警,单体充高温,,sint8,4,0,,,,,,
FBBMS,FBBMS,0x802e01,0x181101,alarm,Cell Charge Over Temperature Protection Alm,Cell Chg.OTP.,单体充电高温保护告警,单体充高温保护,,sint8,4,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x481101,alarm DO,Cell Charge Over Temperature Protection Alm,Cell Chg.OTP.,单体充电高温保护告警,单体充高温保护,,sint8,4,0,,,,,,
FBBMS,FBBMS,0x802e01,0x181201,alarm,Cell Discharge Over Temperature Alm,Cell Disch.OTA.,单体放电高温告警,单体放高温,,sint8,4,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x481201,alarm DO,Cell Discharge Over Temperature Alm,Cell Disch.OTA.,单体放电高温告警,单体放高温,,sint8,4,0,,,,,,
FBBMS,FBBMS,0x802e01,0x181301,alarm,Cell Discharge Over Temperature Protection Alm,Cell Disch.OTP.,单体放电高温保护告警,单体放高温保护,,sint8,4,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x481301,alarm DO,Cell Discharge Over Temperature Protection Alm,Cell Disch.OTP.,单体放电高温保护告警,单体放高温保护,,sint8,4,0,,,,,,
FBBMS,FBBMS,0x802e01,0x181401,alarm,Cell Charge Under Temperature Alm,Cell Chg.UTA.,单体充电低温告警,单体充低温,,sint8,4,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x481401,alarm DO,Cell Charge Under Temperature Alm,Cell Chg.UTA.,单体充电低温告警,单体充低温,,sint8,4,0,,,,,,
FBBMS,FBBMS,0x802e01,0x181501,alarm,Cell Charge Under Temperature Protection Alm,Cell Chg.UTP.,单体充电低温保护告警,单体充低温保护,,sint8,4,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x481501,alarm DO,Cell Charge Under Temperature Protection Alm,Cell Chg.UTP.,单体充电低温保护告警,单体充低温保护,,sint8,4,0,,,,,,
FBBMS,FBBMS,0x802e01,0x181601,alarm,Cell Discharge Under Temperature Alm,Cell Disch.UTA.,单体放电低温告警,单体放低温,,sint8,4,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x481601,alarm DO,Cell Discharge Under Temperature Alm,Cell Disch.UTA.,单体放电低温告警,单体放低温,,sint8,4,0,,,,,,
FBBMS,FBBMS,0x802e01,0x181701,alarm,Cell Discharge Under Temperature Protection Alm,Cell Disch.UTP.,单体放电低温保护告警,单体放低温保护,,sint8,4,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x481701,alarm DO,Cell Discharge Under Temperature Protection Alm,Cell Disch.UTP.,单体放电低温保护告警,单体放低温保护,,sint8,4,0,,,,,,
FBBMS,FBBMS,0x802e01,0x181801,alarm,Cell Voltage Dev Alm,Cell Volt.Dev.,单体落后电压差,单体落后电压差,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x481801,alarm DO,Cell Voltage Dev Alm,Cell Volt.Dev.,单体落后电压差,单体落后电压差,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x181901,alarm,Cell Poor Protection Alm,Cell Poor Prot.,单体落后保护电压差,单体落后保护压差,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x481901,alarm DO,Cell Poor Protection Alm,Cell Poor Prot.,单体落后保护电压差,单体落后保护压差,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x181a01,alarm,Battery SOC Low Alm,Batt.Soc.L.Alm,电池SOC低告警,电池SOC低,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x481a01,alarm DO,Battery SOC Low Alm,Batt.Soc.L.Alm,电池SOC低告警,电池SOC低,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x181b01,alarm,Battery Soc Low Protect Alm,Batt.Soc.L.Prot.,电池SOC低保护,电池SOC低保护,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x481b01,alarm DO,Battery Soc Low Protect Alm,Batt.Soc.L.Prot.,电池SOC低保护,电池SOC低保护,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x181c01,alarm,Battery SOH Alm,Batt.SOH.Alm,电池SOH告警,电池SOH,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x481c01,alarm DO,Battery SOH Alm,Batt.SOH.Alm,电池SOH告警,电池SOH,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x181d01,alarm,Battery SOH Protect Alm,Batt.SOH Prot.,电池SOH保护,电池SOH保护,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x481d01,alarm DO,Battery SOH Protect Alm,Batt.SOH Prot.,电池SOH保护,电池SOH保护,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x181e01,alarm,Cell Damage Protect Alm,Cell Dmg.Prt.,单体损坏保护,单体损坏保护,,sint8,15,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x481e01,alarm DO,Cell Damage Protect Alm,Cell Dmg.Prt.,单体损坏保护,单体损坏保护,,sint8,15,0,,,,,,
FBBMS,FBBMS,0x802e01,0x181f01,alarm,Battery Missing Alm,Batt.Missing Alm,电池丢失告警,电池丢失,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x481f01,alarm DO,Battery Missing Alm,Batt.Missing Alm,电池丢失告警,电池丢失,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x182001,alarm,Charge Switch Invalid Alm,Chg.Sw.Inv.Alm,充电回路开关失效告警,充开关失效,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x482001,alarm DO,Charge Switch Invalid Alm,Chg.Sw.Inv.Alm,充电回路开关失效告警,充开关失效,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x182101,alarm,Discharge Breaker Invalid Alm,Dch.Sw.Inv.Alm,放电回路开关失效告警,放开关失效,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x482101,alarm DO,Discharge Breaker Invalid Alm,Dch.Sw.Inv.Alm,放电回路开关失效告警,放开关失效,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x182201,alarm,Limit Current Invalid Alm,Lim.Curr.Inv.Alm,限流回路失效告警,限流回路失效,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x482201,alarm DO,Limit Current Invalid Alm,Lim.Curr.Inv.Alm,限流回路失效告警,限流回路失效,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x182301,alarm,Short Cut Protection Alm,Short Cut Prot.,短路保护,短路保护,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x482301,alarm DO,Short Cut Protection Alm,Short Cut Prot.,短路保护,短路保护,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x182401,alarm,Battery Reserve Alm,Batt.Reserve Alm,电池反接告警,电池反接,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x482401,alarm DO,Battery Reserve Alm,Batt.Reserve Alm,电池反接告警,电池反接,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x182501,alarm,Cell Temperature Sensor Invalid Alm,Cell TI Alm,单体温度传感器失效告警,单体温传失效告警,,sint8,4,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x482501,alarm DO,Cell Temperature Sensor Invalid Alm,Cell TI Alm,单体温度传感器失效告警,单体温传失效告警,,sint8,4,0,,,,,,
FBBMS,FBBMS,0x802e01,0x182601,alarm,Inner Over Temperature Protection Alm,Inner OTP Alm,机内过温保护告警,机内过温保护,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x482601,alarm DO,Inner Over Temperature Protection Alm,Inner OTP Alm,机内过温保护告警,机内过温保护,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x182701,alarm,BDCU Under Voltage Protection Alm,BDCU UVP Alm,BDCU电池欠压保护,BDCU欠压保护,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x482701,alarm DO,BDCU Under Voltage Protection Alm,BDCU UVP Alm,BDCU电池欠压保护,BDCU欠压保护,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x182801,alarm,BDCU Bus Under Voltage Protection Alm,BDCU Bus UVP Alm,BDCU母排欠压保护告警,BDCU母排欠压保护,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x482801,alarm DO,BDCU Bus Under Voltage Protection Alm,BDCU Bus UVP Alm,BDCU母排欠压保护告警,BDCU母排欠压保护,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x182901,alarm,BDCU EEPROM Abnormal Alm,BDCU EEPROMAlm,BDCU EEPROM故障告警,BDCU EEPROM故障,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x482901,alarm DO,BDCU EEPROM Abnormal Alm,BDCU EEPROMAlm,BDCU EEPROM故障告警,BDCU EEPROM故障,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x182a01,alarm,Cell Temperature Abnormal Alm,Cell Temp.Abn.,单体温度异常,单体温度异常,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x482a01,alarm DO,Cell Temperature Abnormal Alm,Cell Temp.Abn.,单体温度异常,单体温度异常,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x182b01,alarm,Bms Address Clash Alm,Bms Addr.Clash,地址冲突告警,地址冲突,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x482b01,alarm DO,Bms Address Clash Alm,Bms Addr.Clash,地址冲突告警,地址冲突,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x182c01,alarm,Bms Shake Alm,Bms Shake almus,振动告警,振动告警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x482c01,alarm DO,Bms Shake Alm,Bms Shake almus,振动告警,振动告警,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x182d01,alarm,Cell Volt Sample Abnormal Alm,Cell V.Samp.Abn.,单体电压采样异常告警,单体电压采样异常,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x482d01,alarm DO,Cell Volt Sample Abnormal Alm,Cell V.Samp.Abn.,单体电压采样异常告警,单体电压采样异常,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x182e01,alarm,BDCU Communication Fail Alm,BDCU Comm.Fail.,BDCU通信断告警,BDCU通信断,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x482e01,alarm DO,BDCU Communication Fail Alm,BDCU Comm.Fail.,BDCU通信断告警,BDCU通信断,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x182f01,alarm,BMS Communication Fail Alm,BMS Comm.Fail.,BMS通信断告警,BMS通信断告警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x482f01,alarm DO,BMS Communication Fail Alm,BMS Comm.Fail.,BMS通信断告警,BMS通信断告警,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x183001,alarm,BDCU Bus Over Voltage Protection Alm,BDCU Bus OVP Alm.,BDCU母排过压保护告警,BDCU母排过压保护,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x483001,alarm DO,BDCU Bus Over Voltage Protection Alm,BDCU Bus OVP Alm.,BDCU母排过压保护告警,BDCU母排过压保护,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x183101,alarm,Battery Volt Sample Alarm,Batt. Volt.Sample Alm.,电压采样故障,电压采样故障,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x483101,alarm DO,Battery Volt Sample Alarm,Batt. Volt.Sample Alm.,电压采样故障,电压采样故障,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x183201,alarm,Battery Loop Abnormal Alm,Batt.Loop Abnor.Alm,回路异常,回路异常,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x483201,alarm DO,Battery Loop Abnormal Alm,Batt.Loop Abnor.Alm,回路异常,回路异常,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x183301,alarm,BDCU Charge Under Voltage Protect Alm,BDCU Chg.Under Prot.,BDCU电池充电欠压保护,BDCU充电欠压保护,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x483301,alarm DO,BDCU Charge Under Voltage Protect Alm,BDCU Chg.Under Prot.,BDCU电池充电欠压保护,BDCU充电欠压保护,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x183401,alarm,Environment Temperature High Protect,Env.Temp.High Prot.,环境温度高保护,环境温度高保护,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x483401,alarm DO,Environment Temperature High Protect,Env.Temp.High Prot.,环境温度高保护,环境温度高保护,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x183501,alarm,Environment Temperature Low Protect,Env.Temp.Low Prot.,环境温度低保护,环境温度低保护,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x483501,alarm DO,Environment Temperature Low Protect,Env.Temp.Low Prot.,环境温度低保护,环境温度低保护,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x183601,alarm,Board Temperature High Alarm,Board Temp.High Alm,单板过温告警,单板过温告警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
FBBMS,FBBMS,0x802e01,0x483601,alarm DO,Board Temperature High Alarm,Board Temp.High Alm,单板过温告警,单板过温告警,,sint8,1,0,,,,,,
FBBMS,FBBMS,0x802e01,0x200101,control,Bms Reset,Bms Reset,BMS复位,BMS复位,,sint8,1,,,,,,,
FBBMS,FBBMS,0x802e01,0x200201,control,BMS Communication Fail alarm clear,BMS Comm.Fail clear,BMS通讯中断告警清除,BMS通讯中断清除,,sint8,1,,,,,,,
FBBMS,FBBMS,0x802e01,0x200301,control,Release Lock,Release Lock,解除闭锁,解除闭锁,,sint8,1,,,,,,,
FBBMS,FBBMS,0x802e01,0x400101,device info,BMS System Name,BMS System Name,BMS系统名称,BMS系统名称,,string32,1,,,,,,,
FBBMS,FBBMS,0x802e01,0x400201,device info,BMS Software Version,BMS Software Ver.,BMS软件版本,BMS软件版本,,string16,1,,,,,,,
FBBMS,FBBMS,0x802e01,0x400301,device info,BMS Software Release Date,BMS Software Release Date,BMS软件发布日期,BMS软件发布日期,,date,1,,,,,,,
FBBMS,FBBMS,0x802e01,0x400401,device info,BMS Serial Number,BMS No.,BMS序列号,BMS序列号,,string16,1,,,,,,,
FBBMS,FBBMS,0x802e01,0x400501,device info,BDCU System Name,BDCU System Name,BDCU名称,BDCU名称,,string32,1,,,,,,,
FBBMS,FBBMS,0x802e01,0x400601,device info,BDCU Digital Control Platform Version,BDCU Platform.,BDCU数控平台版本,BDCU数控平台,,string16,1,,,,,,,
FBBMS,FBBMS,0x802e01,0x400701,device info,BDCU Software Version,BDCU Software Version,BDCU软件版本,BDCU软件版本,,string16,1,,,,,,,
FBBMS,FBBMS,0x802e01,0x400801,device info,BDCU Software Release Date,BDCU Software Release Date,BDCU软件发布日期,BDCU软件发布日期,,date,1,,,,,,,
FBBMS,FBBMS,0x802e01,0x400b01,device info,Battery Module Serial Number,Batt.Module SN,电池模组序列号,电池模组序列号,,string32,4,,,,,,,
FBBMS,FBBMS,0x802e01,0x400c01,device info,Battery Manufacture Date,Battery Manufacture Date,电池生产日期,电池生产日期,,date,1,,,,,,,
FBBMS,FBBMS,0x802e01,0x400d01,device info,Li Battery Capacity,Batt.Cap.,电池容量,电池容量,Ah,sint16,1,0,100,0,9990,1,,
FBBMS,FBBMS,0x802e01,0x400e01,device info,Whole Machine Serial Number,Whole Machine No.,整机序列号,整机序列号,,string16,1,,,,,,,
FBBMS,FBBMS,0x802e01,0x400f01,device info,Pack Date of Active,Pack Date of Active,电池启用日期,电池启用日期,,date,1,0,0000/00/00,,,,,
FBBMS,FBBMS,0x802e01,0x401001,device info,BMS Hardware Type Name,BMS Hardware Type,BMS硬件型号,BMS硬件型号,,string32,1,0,'SmartLi100',,,,,
FBBMS,FBBMS,0x802e01,0x401101,device info,PACK Factory Name,PACK Factory Name,PACK厂家名称,PACK厂家名称,,string32,1,0,'ZTE-X',,,,,
FBBMS,FBBMS,0x802e01,0x401201,device info,BOOT Version Identify,BOOT Version Ident.,BOOT版本标识,BOOT版本标识,,string16,1,0,,,,,,
油机组,Diesel Generator Group,0x803001,0x180101,alarm,Mobile DG Work,Mobile DG Work,移动油机供电,移动油机供电,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机组,Diesel Generator Group,0x803001,0x480101,alarm DO,Mobile DG Work,Mobile DG Work,移动油机供电,移动油机供电,,sint8,1,0,,,,,,
油机组,Diesel Generator Group,0x803001,0x280101,parameter,DG Disable Run Enable,DG Disable Run Enable,油机禁止运行使能,油机禁止运行使能,,sint8,1,0,0,0,1,1,0:禁止/Disable;1:允许/Enable;,
油机组,Diesel Generator Group,0x803001,0x280201,parameter,DG Disable Run Start Clock,DG Prd.Stop.St.Clk.,油机禁止运行起始时刻,油机禁止运行起始时刻,,hour_minute,3,0,8:00,0:00,23:59,1,,
油机组,Diesel Generator Group,0x803001,0x280301,parameter,DG Period Stop End Clock,DG Prd.Stop.End.Clk.,油机禁止运行终止时刻,油机禁止运行终止时刻,,hour_minute,3,0,8:00,0:00,23:59,1,,
油机组,Diesel Generator Group,0x803001,0x280401,parameter,DG Period Start Enable,DG Prd.Start En.,油机定时启动使能,油机定时启动使能,,sint8,1,0,0,0,1,1,0:禁止/Disable;1:允许/Enable;,
油机组,Diesel Generator Group,0x803001,0x280501,parameter,DG Period Start Clock,DG Prd.St.Clk.,油机定时启动时刻,油机定时启动时刻,,hour_minute,3,0,8:00,0:00,23:59,1,,
油机组,Diesel Generator Group,0x803001,0x280601,parameter,DG Period End Clock,DG Prd.End Clk.,油机定时关闭时刻,油机定时关闭时刻,,hour_minute,3,0,8:00,0:00,23:59,1,,
油机组,Diesel Generator Group,0x803001,0x280701,parameter,DG Start Voltage Enable,DG Start Vol.En.,油机启动电压使能,油机启动电压使能,,sint8,1,0,1,0,1,1,0:禁止/Disable;1:允许/Enable;,
油机组,Diesel Generator Group,0x803001,0x280801,parameter,DG Start SOC Enable,DG Start SOC En.,油机启动SOC使能,油机启动SOC使能,,sint8,1,0,1,0,1,1,0:禁止/Disable;1:允许/Enable;,
油机组,Diesel Generator Group,0x803001,0x280901,parameter,DG Start Time Enable,DG Start Time En.,油机启动时间使能,油机启动时间使能,,sint8,1,0,1,0,1,1,0:禁止/Disable;1:允许/Enable;,
油机组,Diesel Generator Group,0x803001,0x280a01,parameter,DG Start System Abnormal Enable,DG Start Sys.Abr.En.,油机启动系统异常使能,油机启动异常使能,,sint8,1,0,1,0,1,1,0:禁止/Disable;1:允许/Enable;,
油机组,Diesel Generator Group,0x803001,0x280b01,parameter,DG Start Voltage Threshold,DG Start Volt.,油机启动电压,油机启动电压,V,sint16,1,1,470.0,400.0,542.0,1.0,,">=Battery Group|LLVD1 Voltage+0.5V;
>=Battery Group|LLVD2 Voltage+0.5V;
>=Battery Group|BLVD Voltage+0.5V;
<=Mains Group|MAINS Start Voltage Threshold;
<=Battery Group|Equalized Charge Threshold Voltage-0.5V;
>=DC Distribution|DC DU LLVD Voltage+0.5V;
>=Smart DC Distribution Unit|Distribution Unit LLVD Voltage Threshold+0.5V;
"
油机组,Diesel Generator Group,0x803001,0x280c01,parameter,DG Start SOC Threshold,DG Start SOC,油机启动SOC阈值,油机启动SOC,%,sint16,1,0,50,5,90,1,,">=Battery Group|LLVD1 SOC Threshold+5%;
>=Battery Group|LLVD2 SOC Threshold+5%;
>=Battery Group|BLVD SOC Threshold+5%;
<=Diesel Generator Group|DG Stop SOC Threshold-5%;
<=Mains Group|MAINS Start SOC Threshold;
>=DC Distribution|DC DU LLVD SOC Threshold+5%;
>=Smart DC Distribution Unit|Distribution Unit LLVD SOC Threshold+5%;
"
油机组,Diesel Generator Group,0x803001,0x280d01,parameter,DG Start Discharge Duration,DG St.Dis.Dura.,油机启动放电时间阈值,油机启动放电时间,Min,sint16,1,0,600,3,4320,1,,"<=Battery Group|LLVD1 Duration-30;
<=Battery Group|LLVD2 Duration-30;
<=Battery Group|BLVD Duration-30;
>=Mains Group|MAINS Start Discharge Duration Threshold;
<=DC Distribution|DC DU LLVD Duration-30;
<=Smart DC Distribution Unit|Distribution Unit LLVD Duration-30;
"
油机组,Diesel Generator Group,0x803001,0x280e01,parameter,DG Stop Voltage Enable,DG Stop Vol.En.,油机停止电压使能,油机停止电压使能,,sint8,1,0,1,0,1,1,0:禁止/Disable;1:允许/Enable;,
油机组,Diesel Generator Group,0x803001,0x280f01,parameter,DG Stop SOC Enable,DG Stop SOC En.,油机停止SOC使能,油机停止SOC使能,,sint8,1,0,0,0,1,1,0:禁止/Disable;1:允许/Enable;,
油机组,Diesel Generator Group,0x803001,0x281001,parameter,DG Stop Current Enable,DG Stop Cur.En.,油机停止电流使能,油机停止电流使能,,sint8,1,0,1,0,1,1,0:禁止/Disable;1:允许/Enable;,
油机组,Diesel Generator Group,0x803001,0x281101,parameter,DG Stop Voltage Threshold,DG Stop Volt.,油机停止电压,油机停止电压,V,sint16,1,1,564.0,420.0,587.0,1.0,,">=Diesel Generator Group|DG Start Voltage Threshold+1V;
>=Battery Group|LLVD1 Voltage+2V;
>=Battery Group|LLVD2 Voltage+2V;
>=Battery Group|BLVD Voltage+2V;
>=DC Distribution|DC DU LLVD Voltage+2V;
>=Smart DC Distribution Unit|Distribution Unit LLVD Voltage Threshold+2V;
"
油机组,Diesel Generator Group,0x803001,0x281201,parameter,DG Stop SOC Threshold,DG Stop SOC,油机关闭SOC阈值,油机关闭SOC,%,sint16,1,0,95,50,100,1,,">=Diesel Generator Group|DG Start SOC Threshold+5%;
>=Battery Group|LLVD1 SOC Threshold+10%;
>=Battery Group|LLVD2 SOC Threshold+10%;
>=Battery Group|BLVD SOC Threshold+10%;
>=DC Distribution|DC DU LLVD SOC Threshold+10%;
>=Smart DC Distribution Unit|Distribution Unit LLVD SOC Threshold+10%;
<=Mains Group|MAINS Stop SOC Threshold;
"
油机组,Diesel Generator Group,0x803001,0x281301,parameter,DG Stop Battery Current,DG Stop Bat.Curr.,油机关闭电池电流,油机关闭电流,C10,sint16,1,3,10.0,0.0,500.0,1.0,,
油机组,Diesel Generator Group,0x803001,0x281401,parameter,Min DG Running Duration,Min DG Run.Dura.,油机最短运行时间,油机最短运行时间,Min,sint16,1,0,180,0,1440,1,,"<=Diesel Generator Group|DG Max Running Duration;
<=Battery Group|Full Charge Max Duration;
"
油机组,Diesel Generator Group,0x803001,0x281501,parameter,DG Max Running Duration,DG Max Run.Dura.,油机最长运行时间,油机最长运行时间,Min,sint16,1,0,480,0,1440,1,,">=Diesel Generator Group|Min DG Running Duration;
"
油机组,Diesel Generator Group,0x803001,0x281601,parameter,DG Cool Down Enabled,DG Cool Down En.,油机冷却使能,油机冷却使能,,sint8,1,0,1,0,1,1,0:禁止/Disabled;1:允许/Enabled;,
油机组,Diesel Generator Group,0x803001,0x281701,parameter,DG Cool Down Duration,DG Cool Down Dura.,油机冷却时间,油机冷却时间,Sec,sint16,1,0,30,0,180,1,,
油机组,Diesel Generator Group,0x803001,0x281b01,parameter,DG Three-phase Power Factor,DG Three-phase PF,三相油机功率因数,三相功率因数,,sint16,1,2,80.0,0.0,100.0,1.0,,
油机组,Diesel Generator Group,0x803001,0x281c01,parameter,DG Single-phase Power Factor,DG Single-phase PF,单相油机功率因数,单相功率因数,,sint16,1,2,100.0,0.0,100.0,1.0,,
油机组,Diesel Generator Group,0x803001,0x281d01,parameter,DG Load Rate Threshold,DG Ld.Rate.Thres.,油机带载率阈值,油机带载率阈值,%,sint16,1,0,100,50,100,1,,
油机组,Diesel Generator Group,0x803001,0x282001,parameter,Fuel Level Low Alarm Threshold,Fuel Low Thres.,油机油位低告警阈值,油位低告警阈值,%,sint16,1,0,30,0,50,1,,
油机组,Diesel Generator Group,0x803001,0x282101,parameter,Fuel Level High Alarm Threshold,Fuel High Thres.,油机油位高告警阈值,油位高告警阈值,%,sint16,1,0,95,60,100,1,,
油机,Diesel Generator,0x803201,0x080101,analog data,DG Phase Voltage,DG Phase Volt.,油机相电压,油机相电压,V,sint16,3,0,220,0,300,,,
油机,Diesel Generator,0x803201,0x080201,analog data,DG Line Voltage,DG Line Volt.,油机线电压,油机线电压,V,sint16,3,0,380,0,520,,,
油机,Diesel Generator,0x803201,0x080301,analog data,DG Phase Current,DG Phase Curr.,油机相电流,油机相电流,A,sint16,3,0,0,0,600,,,
油机,Diesel Generator,0x803201,0x080a01,analog data,DG Power Factor,DG.PF.,油机功率因数,油机功率因数,,sint16,3,2,100.0,-100.0,100.0,1.0,,
油机,Diesel Generator,0x803201,0x080401,analog data,DG Output Frequency,DG Out.Freq.,油机输出频率,油机输出频率,Hz,sint16,1,0,50,40,75,1,,
油机,Diesel Generator,0x803201,0x080b01,analog data,DG Apparent Power,DG.App.Pwr.,油机视在功率,油机视在功率,kVA,sint32,3,2,0.0,0.0,,10.0,,
油机,Diesel Generator,0x803201,0x080c01,analog data,DG Active Power,DG.Act.Pwr.,油机有功功率,油机有功功率,kW,sint32,3,2,0.0,0.0,,10.0,,
油机,Diesel Generator,0x803201,0x080d01,analog data,DG Reactive Power,DG.Rct.Pwr.,油机无功功率,油机无功功率,kvar,sint32,3,2,0.0,0.0,,10.0,,
油机,Diesel Generator,0x803201,0x080e01,analog data,DG Speed,DG.Speed,油机转速,油机转速,RPM,sint16,1,0,0,0,5000,,,
油机,Diesel Generator,0x803201,0x080f01,analog data,DG Oil Pressure,DG.Oil Pres.,油机油压,油机油压,BAR,sint16,1,1,0.0,0.0,180.0,1.0,,
油机,Diesel Generator,0x803201,0x081001,analog data,DG Coolant Temperature,DG.Cool.Temp.,油机水温,油机水温,℃,sint16,1,0,0,0,100,1,,
油机,Diesel Generator,0x803201,0x081101,analog data,DG Battery Voltage,DG.Batt.Volt.,油机电池电压,油机电池电压,V,sint16,1,1,120.0,0.0,600.0,1.0,,
油机,Diesel Generator,0x803201,0x081201,analog data,DG Load Rate,DG.Load Rate,油机带载率,油机带载率,%,sint16,1,0,80,0,100,1,,
油机,Diesel Generator,0x803201,0x081301,analog data,DG Oil Temperature,DG.Oil Temp.,油机油温,油机油温,℃,sint16,1,0,0,0,200,1,,
油机,Diesel Generator,0x803201,0x080501,analog data,DG Running Duration,DG Run.Dura.,油机持续运行时间,油机运行时间,Min,sint32,1,0,0,0,,,,
油机,Diesel Generator,0x803201,0x080601,analog data,DG Fuel Level,DG Fuel Level,油机油位,油机油位,%,sint16,1,1,1000.0,0.0,1000.0,1.0,,
油机,Diesel Generator,0x803201,0x080701,analog data,DG Fuel Gauge,DG Fuel Gauge,油机剩余油量,油机剩余油量,L,sint32,1,1,0.0,0.0,80000.0,1.0,,
油机,Diesel Generator,0x803201,0x080801,analog data,Fuel Tank Capacity,Fuel Tank Cap.,油箱容量,油箱容量,L,sint32,1,1,0.0,0.0,100000000.0,1.0,,
油机,Diesel Generator,0x803201,0x100101,digital data,DG Controler Mode,DG Controler Mode,油机控制器工作模式,油机控制器工作模式,,sint8,1,0,0,0,2,1,0:自动/Auto;1:手动/Manual;2:其他模式/Other Mode;,
油机,Diesel Generator,0x803201,0x100201,digital data,DG Status,DG Status,油机状态,油机状态,,sint8,1,0,0,0,4,1,0:未配置/Not Config;1:停止/OFF;2:运行/ON;3:异常/Abnormal;4:过渡/Transition;,
油机,Diesel Generator,0x803201,0x100401,digital data,DG Control Status,DG Control Status,油机控制状态,油机控制状态,,sint8,1,0,0,0,1,1,0:关闭/Off;1:开启/On;,
油机,Diesel Generator,0x803201,0x100501,digital data,DG Application Scenario,DG Apl.Scenario,油机应用场景,油机应用场景,,sint8,1,0,1,0,1,1,0:非受控/Uncontrolled;1:受控/Controlled;,
油机,Diesel Generator,0x803201,0x180101,alarm,DG Start Alarm,DG Start Alm.,油机启动告警,油机启动告警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x480101,alarm DO,DG Start Alarm,DG Start Alm.,油机启动告警,油机启动告警,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x180201,alarm,DG Abnormal Alarm,DG Abnormal Alm.,油机异常告警,油机异常告警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x480201,alarm DO,DG Abnormal Alarm,DG Abnormal Alm.,油机异常告警,油机异常告警,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x180301,alarm,DG Common Alarm,DG Common Alm.,油机公共告警,油机公共告警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x480301,alarm DO,DG Common Alarm,DG Common Alm.,油机公共告警,油机公共告警,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x180401,alarm,DG Low Fuel Level,DG Low Fl.Lev.,油机油位低告警,油机油位低告警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x480401,alarm DO,DG Low Fuel Level,DG Low Fl.Lev.,油机油位低告警,油机油位低告警,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x180501,alarm,DG Output Voltage Imbalance,DG Volt.Imbala.,油机电压不平衡,油机电压不平衡,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x480501,alarm DO,DG Output Voltage Imbalance,DG Volt.Imbala.,油机电压不平衡,油机电压不平衡,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x180601,alarm,Air Filter Block Alarm,A.F.Block Alm.,空气滤清器堵塞告警,空滤堵塞告警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x480601,alarm DO,Air Filter Block Alarm,A.F.Block Alm.,空气滤清器堵塞告警,空滤堵塞告警,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x180701,alarm,Fuel Water Separator High Water Level Alarm,Sep.H.W.Lev.,油水分离器水位高告警,分离器水位高,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x480701,alarm DO,Fuel Water Separator High Water Level Alarm,Sep.H.W.Lev.,油水分离器水位高告警,分离器水位高,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x180801,alarm,DG Access Control Alarm,DG Access Ctrl.,油机门磁告警,油机门磁告警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x480801,alarm DO,DG Access Control Alarm,DG Access Ctrl.,油机门磁告警,油机门磁告警,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x180901,alarm,DG Manual Mode Alarm,DG Manual Mode,油机手动模式告警,油机手动模式,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x480901,alarm DO,DG Manual Mode Alarm,DG Manual Mode,油机手动模式告警,油机手动模式,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x180a01,alarm,DG Fuel Leakage,DG Fuel Leakage,油机燃油泄漏告警,油机燃油泄漏告警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x480a01,alarm DO,DG Fuel Leakage,DG Fuel Leakage,油机燃油泄漏告警,油机燃油泄漏告警,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x180b01,alarm,Tank high water level Alarm,Tank H.W.Lev.,油箱水位高告警,油箱水位高,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x480b01,alarm DO,Tank high water level Alarm,Tank H.W.Lev.,油箱水位高告警,油箱水位高,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x180c01,alarm,Fuel Waterlog Alarm,Fl.Waterlog,燃油水浸告警,燃油水浸告警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x480c01,alarm DO,Fuel Waterlog Alarm,Fl.Waterlog,燃油水浸告警,燃油水浸告警,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x180e01,alarm,DG Output Under Voltage,DG.Out.UVP,油机输出欠压,油机输出欠压,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x480e01,alarm DO,DG Output Under Voltage,DG.Out.UVP,油机输出欠压,油机输出欠压,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x180f01,alarm,DG Output Over Voltage,DG.Out.OVP,油机输出过压,油机输出过压,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x480f01,alarm DO,DG Output Over Voltage,DG.Out.OVP,油机输出过压,油机输出过压,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x181001,alarm,DG Output Over Current,DG.Over Curr.,油机输出过流,油机输出过流,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x481001,alarm DO,DG Output Over Current,DG.Over Curr.,油机输出过流,油机输出过流,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x181101,alarm,DG Output Over Load,DG.Over Load,油机输出过载,油机输出过载,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x481101,alarm DO,DG Output Over Load,DG.Over Load,油机输出过载,油机输出过载,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x181201,alarm,DG Output Under Frequency,DG.Und.Freq.,油机输出频率低,油机输出频率低,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x481201,alarm DO,DG Output Under Frequency,DG.Und.Freq.,油机输出频率低,油机输出频率低,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x181301,alarm,DG Output Over Frequency,DG.Over Freq.,油机输出频率高,油机输出频率高,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x481301,alarm DO,DG Output Over Frequency,DG.Over Freq.,油机输出频率高,油机输出频率高,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x181401,alarm,DG Under Speed,DG.Und.Speed,油机低速告警,油机低速告警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x481401,alarm DO,DG Under Speed,DG.Und.Speed,油机低速告警,油机低速告警,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x181501,alarm,DG Over Speed,DG.Over Speed,油机超速告警,油机超速告警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x481501,alarm DO,DG Over Speed,DG.Over Speed,油机超速告警,油机超速告警,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x181601,alarm,DG Low Oil Pressure,DG.Low Oil Pres.,油机油压低告警,油机油压低告警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x481601,alarm DO,DG Low Oil Pressure,DG.Low Oil Pres.,油机油压低告警,油机油压低告警,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x181701,alarm,DG Coolant Over Temperature,DG.Coolant OTP,油机水温高告警,油机水温高告警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x481701,alarm DO,DG Coolant Over Temperature,DG.Coolant OTP,油机水温高告警,油机水温高告警,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x181801,alarm,DG Oil Over Temperature,DG.Oil OTP,油机油温高告警,油机油温高告警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x481801,alarm DO,DG Oil Over Temperature,DG.Oil OTP,油机油温高告警,油机油温高告警,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x181901,alarm,DG Battery Under Voltage,DG.Bat.UVP,油机电池欠压告警,油机电池欠压,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x481901,alarm DO,DG Battery Under Voltage,DG.Bat.UVP,油机电池欠压告警,油机电池欠压,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x181a01,alarm,DG Battery Over Voltage,DG.Bat.OVP,油机电池过压告警,油机电池过压,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x481a01,alarm DO,DG Battery Over Voltage,DG.Bat.OVP,油机电池过压告警,油机电池过压,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x181b01,alarm,DG Charge Failure,DG.Charge.Fail.,油机充电失败告警,油机充电失败,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x481b01,alarm DO,DG Charge Failure,DG.Charge.Fail.,油机充电失败告警,油机充电失败,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x181c01,alarm,DG Engine High Temperature,DG.Engine OTP,油机发动机高温告警,油机发动机高温,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x481c01,alarm DO,DG Engine High Temperature,DG.Engine OTP,油机发动机高温告警,油机发动机高温,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x181d01,alarm,DG Engine Low Temperature,DG.Engine UTP,油机发动机低温告警,油机发动机低温,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x481d01,alarm DO,DG Engine Low Temperature,DG.Engine UTP,油机发动机低温告警,油机发动机低温,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x181e01,alarm,DG Start Failure,DG.Start Fail.,油机启动失败告警,油机启动失败,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x481e01,alarm DO,DG Start Failure,DG.Start Fail.,油机启动失败告警,油机启动失败,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x181f01,alarm,DG Stop Failure,DG.Stop Fail.,油机停机失败告警,油机停机失败,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x481f01,alarm DO,DG Stop Failure,DG.Stop Fail.,油机停机失败告警,油机停机失败,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x182001,alarm,DG High Fuel level,DG.High Fl Lev.,油机油位高告警,油机油位高告警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x482001,alarm DO,DG High Fuel level,DG.High Fl Lev.,油机油位高告警,油机油位高告警,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x182201,alarm,DG Low Oil Level Alarm,DG.L.Oil Lev.,油机机油液位低告警,机油液位低告警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x482201,alarm DO,DG Low Oil Level Alarm,DG.L.Oil Lev.,油机机油液位低告警,机油液位低告警,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x182301,alarm,DG Emergency Stop Alarm,DG.Emerg.Stop,油机紧急停机告警,紧急停机告警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x482301,alarm DO,DG Emergency Stop Alarm,DG.Emerg.Stop,油机紧急停机告警,紧急停机告警,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x182401,alarm,DG High Alternator Temperature Alarm,H.Alt.Temp.Alm.,电球高温告警,电球高温告警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x482401,alarm DO,DG High Alternator Temperature Alarm,H.Alt.Temp.Alm.,电球高温告警,电球高温告警,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x182501,alarm,DG Water Temperature Sensor Open Alarm,DG.Wa.Te.S.Open,水温传感器开路告警,水温传感器开路,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x482501,alarm DO,DG Water Temperature Sensor Open Alarm,DG.Wa.Te.S.Open,水温传感器开路告警,水温传感器开路,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x182601,alarm,DG Fuel Press Sensor Open Alarm,DG.F.Pre.S.Open,油压传感器开路告警,油压传感器开路,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x482601,alarm DO,DG Fuel Press Sensor Open Alarm,DG.F.Pre.S.Open,油压传感器开路告警,油压传感器开路,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x182701,alarm,DG Fuel Level Sensor Open Alarm,DG.F.Lev.S.Open,油位传感器开路告警,油位传感器开路,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x482701,alarm DO,DG Fuel Level Sensor Open Alarm,DG.F.Lev.S.Open,油位传感器开路告警,油位传感器开路,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x182801,alarm,DG Overlong Running Alarm,DG Ol.Run.Alm,油机超长运行告警,油机超长运行告警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机,Diesel Generator,0x803201,0x482801,alarm DO,DG Overlong Running Alarm,DG Ol.Run.Alm,油机超长运行告警,油机超长运行告警,,sint8,1,0,,,,,,
油机,Diesel Generator,0x803201,0x200101,control,DG Start,DG Start,油机开启,油机开启,,sint8,1,0,,0,1,1,,
油机,Diesel Generator,0x803201,0x200201,control,DG Stop,DG Stop,油机关闭,油机关闭,,sint8,1,0,,0,1,1,,
油机,Diesel Generator,0x803201,0x280101,parameter,Tank Shape,Tank Shape,油箱形状,油箱形状,,sint8,1,0,0,0,3,1,0:未配置/No Config;1:立方体/Cube;2:椭圆形立放/Ellipse Vectical;3:椭圆形横放/Ellipse Horizontal;,
油机,Diesel Generator,0x803201,0x280301,parameter,Fuel Add Record Threshold,Fuel Add Rec.Thre.,燃油加油记录阈值,燃油加油记录阈值,L/10Min,sint16,1,0,50,5,300,1,,
油机,Diesel Generator,0x803201,0x280401,parameter,Fuel Leakage Alarm Threshold,Fuel Lkg.Thre.,燃油泄漏告警阈值,燃油泄漏告警阈值,L/Hour,sint16,1,0,80,10,600,0.1,,
油机,Diesel Generator,0x803201,0x280701,parameter,Tank Length,Tank Length,油箱长度,油箱长度,mm,sint16,1,0,2000,0,9999,1,,
油机,Diesel Generator,0x803201,0x280801,parameter,Tank Width,Tank Width,油箱宽度,油箱宽度,mm,sint16,1,0,1200,0,9999,1,,
油机,Diesel Generator,0x803201,0x280901,parameter,Tank Height,Tank Height,油箱高度,油箱高度,mm,sint16,1,0,850,0,9999,1,,
油机,Diesel Generator,0x803201,0x280a01,parameter,DG Overlong Running Threshold,DG Overlong Run.Thre.,油机超长运行告警阈值,油机超长运行阈值,Min,sint16,1,0,1200,600,1440,1,,
油机,Diesel Generator,0x803201,0x280b01,parameter,ATS Config,ATS Config,ATS配置,ATS配置,,sint8,1,0,1,0,1,1,0:无/Null;1:有/Exist;,
油机,Diesel Generator,0x803201,0x280c01,parameter,Mains Access Config,Mains Access Config,市电接入配置,市电接入配置,,sint8,1,0,1,0,1,1,0:无/Null;1:有/Exist;,
油机,Diesel Generator,0x803201,0x300301,record data,DG Start Fuel Gauge,DG St.Fl.Gug,油机起始油量,油机起始油量,L,sint32,1,1,,,80000.0,1.0,,
油机,Diesel Generator,0x803201,0x300401,record data,DG End Fuel Gauge,DG End Fl.Gug,油机终止油量,油机终止油量,L,sint32,1,1,,,80000.0,1.0,,
油机,Diesel Generator,0x803201,0x300601,record data,DG Run Extra Information,DG Run Ex.Info.,油机运行附加信息,油机运行附加信息,,sint32,1,0,,,,,,
油机,Diesel Generator,0x803201,0x300701,record data,DG Initial Runtime,DG Initial Runtime,油机起始运行时间,油机起始运行时间,Min,sint32,1,0,,,,,,
油机,Diesel Generator,0x803201,0x300801,record data,DG Final Runtime,DG Final Runtime,油机终止运行时间,油机终止运行时间,Min,sint32,1,0,,,,,,
油机,Diesel Generator,0x803201,0x300901,record data,DG Initial Power Generation,DG Init.Power Generation,油机起始发电量,油机起始发电量,kWh,sint32,1,2,,,,,,
油机,Diesel Generator,0x803201,0x300a01,record data,DG Final Power Generation,DG Final Power Generation,油机终止发电量,油机终止发电量,kWh,sint32,1,2,,,,,,
油机,Diesel Generator,0x803201,0x300c01,record data,DG Single Refuel Volume,DG Single Ref.Vol.,油机单次加油量,油机单次加油量,L,sint32,1,1,,,80000.0,1.0,,
油机,Diesel Generator,0x803201,0x300d01,record data,DG Single Leak Volume,DG Single Leak Vol.,油机单次漏油量,油机单次漏油量,L,sint32,1,1,,,80000.0,1.0,,
油机,Diesel Generator,0x803201,0x380201,stastic data,DG Power Total Generation,DG Total Power Gene.,油机累计发电量,油机累计发电量,kWh,sint32,1,2,,,,,,
油机,Diesel Generator,0x803201,0x380301,stastic data,DG Runtime,DG Runtime,油机累计运行时间,油机累计运行时间,Min,sint32,1,0,,,,,,
油机,Diesel Generator,0x803201,0x380401,stastic data,DG Fuel Consumption,DG Fuel Consp.,油机耗油量,油机耗油量,L,sint32,1,1,,,,,,
油机,Diesel Generator,0x803201,0x380501,stastic data,DG Refuel Volume,DG Ref.Vol.,油机加油量,油机加油量,L,sint32,1,1,,,,,,
油机,Diesel Generator,0x803201,0x380601,stastic data,Manual DG Running Duration,Manu.DG Dura.,手动油机运行时长,手动油机时长,Min,sint32,1,0,,,,,,
油机,Diesel Generator,0x803201,0x380801,stastic data,Manual DG Power Generation,Man.DG Pwr.Gene.,手动油机发电量,手动油机发电量,kWh,sint32,1,2,,,,,,
油机,Diesel Generator,0x803201,0x380901,stastic data,Manual DG Fuel Consumption,Man.DG Fuel Cons.,手动油机耗油量,手动油机耗油量,L,sint32,1,1,,,,,,
油机,Diesel Generator,0x803201,0x380a01,stastic data,DG AC Power Total Generation,DG AC Total Power Gene.,油机交流发电量,油机交流发电量,kWh,sint32,1,2,,,,,,
油机,Diesel Generator,0x803201,0x380b01,stastic data,Manual DG AC Power Total Generation,Man.DG AC Total Power Gene.,手动油机交流发电量,手动油机交流发电量,kWh,sint32,1,2,,,,,,
油机,Diesel Generator,0x803201,0x300e01,record data,Generator Run Record Time,DG. Run Rec.Time,油机启停记录时间,油机启停记录时间,,process_time,1,,,,,,,
油机,Diesel Generator,0x803201,0x300f01,record data,Tank Refuel Record Time,Refuel Rec.Time,油箱加油记录时间,油箱加油记录时间,,process_time,1,,,,,,,
油机,Diesel Generator,0x803201,0x301001,record data,DG Fuel Leakage Record Time,Fuel Leak.Rec.Time,燃油泄漏记录时间,燃油泄漏记录时间,,process_time,1,,,,,,,
油机,Diesel Generator,0x803201,0x301101,record data,Manual DG Initial Runtime,Man.DG Initial Runtime,手动油机起始运行时间,手动油机起始运行时间,Min,sint32,1,0,,,,,,
油机,Diesel Generator,0x803201,0x301201,record data,Manual DG Final Runtime,Man.DG Final Runtime,手动油机终止运行时间,手动油机终止运行时间,Min,sint32,1,0,,,,,,
油机,Diesel Generator,0x803201,0x301301,record data,Manual DG Initial Power Generation,Man.DG Init.Power Generation,手动油机起始发电量,手动油机起始发电量,kWh,sint32,1,2,,,,,,
油机,Diesel Generator,0x803201,0x301401,record data,Manual DG Final Power Generation,Man.DG Final Power Generation,手动油机终止发电量,手动油机终止发电量,kWh,sint32,1,2,,,,,,
液位传感器,Liquid Level Sensor,0x803601,0x080101,analog data,LLS Sensor Height,LLS Height,液位传感器高度,液位传感器高度,mm,sint32,1,3,0.0,0.0,65536000.0,,,
液位传感器,Liquid Level Sensor,0x803601,0x180101,alarm,LLS Communication Fail,LLS Comm.Fail,液位传感器通讯中断,液位传感器通讯断,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
液位传感器,Liquid Level Sensor,0x803601,0x480101,alarm DO,LLS Communication Fail,LLS Comm.Fail,液位传感器通讯中断,液位传感器通讯断,,sint8,1,0,,,,,,
液位传感器,Liquid Level Sensor,0x803601,0x280101,parameter,LLS Sensor Offset Adjust,LLS Lev.Adj.,液位传感器校准零点,液位传感器零点,mm,sint32,1,3,0.0,-100000.0,100000.0,1.0,,
液位传感器,Liquid Level Sensor,0x803601,0x280201,parameter,LLS Sensor Slope,LLS Sensor.Slope,液位传感器校准斜率,液位传感器斜率,,sint16,1,3,1000.0,800.0,1500.0,1.0,,
液位传感器,Liquid Level Sensor,0x803601,0x400101,device info,LLS System Name,LLS Sys.Name,液位传感器系统名称,液传系统名称,,string32,1,,,,,,,
液位传感器,Liquid Level Sensor,0x803601,0x400201,device info,LLS Software Version,LLS Soft.Ver.,液位传感器软件版本,液传软件版本,,string16,1,,,,,,,
液位传感器,Liquid Level Sensor,0x803601,0x400301,device info,LLS Software Release Date,LLS Soft.Date,液位传感器软件发布日期,液传软件发布日期,,date,1,,,,,,,
ATS,ATS,0x803801,0x180101,alarm,ATS Fault,ATS Fault,ATS故障,ATS故障,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
ATS,ATS,0x803801,0x480101,alarm DO,ATS Fault,ATS Fault,ATS故障,ATS故障,,sint8,1,0,,,,,,
ATS,ATS,0x803801,0x180201,alarm,ATS SPD Fault,ATS SPD Fault,ATS防雷故障,ATS防雷故障,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
ATS,ATS,0x803801,0x480201,alarm DO,ATS SPD Fault,ATS SPD Fault,ATS防雷故障,ATS防雷故障,,sint8,1,0,,,,,,
油机控制屏,GCP,0x803401,0x081701,analog data,DG Oil Level,DG.Oil Level,油机油位,油机油位,%,sint16,1,1,1000.0,0.0,1000.0,1.0,,
油机控制屏,GCP,0x803401,0x180101,alarm,GCP Communication Fail,GCP Comm.Fail,油机控制屏通讯中断,油机控制屏通讯断,,sint8,1,0,,,,1,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机控制屏,GCP,0x803401,0x480101,alarm DO,GCP Communication Fail,GCP Comm.Fail,油机控制屏通讯中断,油机控制屏通讯断,,sint8,1,0,,,,1,,
油机控制屏,GCP,0x803401,0x180201,alarm,DG Fail to Load,DG.Fail Load,油机合闸失败,油机合闸失败,,sint8,1,0,,,,1,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机控制屏,GCP,0x803401,0x480201,alarm DO,DG Fail to Load,DG.Fail Load,油机合闸失败,油机合闸失败,,sint8,1,0,,,,1,,
油机控制屏,GCP,0x803401,0x180301,alarm,DG Fail to Open,DG.Fail to Open,油机分闸失败,油机分闸失败,,sint8,1,0,,,,1,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机控制屏,GCP,0x803401,0x480301,alarm DO,DG Fail to Open,DG.Fail to Open,油机分闸失败,油机分闸失败,,sint8,1,0,,,,1,,
油机控制屏,GCP,0x803401,0x180401,alarm,Mains GCB Fail to Load,Mns.GCB Fail Load,市电合闸失败,市电合闸失败,,sint8,1,0,,,,1,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机控制屏,GCP,0x803401,0x480401,alarm DO,Mains GCB Fail to Load,Mns.GCB Fail Load,市电合闸失败,市电合闸失败,,sint8,1,0,,,,1,,
油机控制屏,GCP,0x803401,0x180501,alarm,Mains GCB Fail to Open,Mns.GCB Fail Open,市电分闸失败,市电分闸失败,,sint8,1,0,,,,1,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
油机控制屏,GCP,0x803401,0x480501,alarm DO,Mains GCB Fail to Open,Mns.GCB Fail Open,市电分闸失败,市电分闸失败,,sint8,1,0,,,,1,,
油机控制屏,GCP,0x803401,0x200101,control,Ctrl DG Enter Manual Mode,Ctrl DG Manual,油机进入手动模式,油机手动,,sint8,1,,,,,,,
油机控制屏,GCP,0x803401,0x200201,control,Ctrl DG Enter Automatic Mode,Ctrl DG Auto.,油机进入自动模式,油机自动,,sint8,1,,,,,,,
油机控制屏,GCP,0x803401,0x200301,control,GCP Reset,GCP Reset,油机控制屏复位,油机控制屏复位,,sint8,1,,,,,,,
油机控制屏,GCP,0x803401,0x200401,control,GCP Device Statistic,GCP Dev Stat,油机控制屏设备统计,油机控制屏设备统计,,sint8,1,,,,,,,
油机控制屏,GCP,0x803401,0x280101,parameter,Mains Normal Mode,Mains Normal Mode,市电正常模式,市电正常模式,,sint8,1,0,0,0,4,1,0:禁止缺相/L1-3P4W;1:L2L3可缺一相/L1-2P4W;2:L2L3可缺相/L1-1P4W;3:任缺一相/2P4W;4:任缺二相/1P4W;,
油机控制屏,GCP,0x803401,0x400101,device info,GCP System Name,GCP Sys.Name,油机控制屏系统名称,油机屏系统名称,,string32,1,,,,,,,
油机控制屏,GCP,0x803401,0x400201,device info,GCP Software Version,GCP Soft.Ver.,油机控制屏软件版本,油机屏软件版本,,string16,1,,,,,,,
油机控制屏,GCP,0x803401,0x400301,device info,GCP Software Release Date,GCP Soft.Date,油机控制屏软件发布日期,油机屏软件日期,,date,1,,,,,,,
油机控制屏,GCP,0x803401,0x400401,device info,GCP Serial Number,GCP Serial No.,油机控制屏序列号,油机屏序列号,,string16,1,,,,,,,
交流电表,ACEM,0x803c01,0x080101,analog data,ACEM Loop 1 Voltage,ACEM Loop 1 Volt.,交流电表回路1电压,交流电表回路1电压,V,sint16,3,1,2200.0,0.0,3000.0,,,
交流电表,ACEM,0x803c01,0x080201,analog data,ACEM Loop 1 Current,ACEM Loop 1 Curr.,交流电表回路1电流,交流电表回路1电流,A,sint16,3,1,0.0,0.0,6000.0,,,
交流电表,ACEM,0x803c01,0x080301,analog data,ACEM Loop 1 Total Active Power,ACEM Loop 1 Tol.Act.P.,交流电表回路1总有功功率,交流电表回路1总有功,kW,sint32,1,2,0.0,0.0,,,,
交流电表,ACEM,0x803c01,0x080401,analog data,ACEM Loop 1 Total Power Factor,ACEM Loop 1 Tol.Pwr.F.,交流电表回路1总功率因数,交流电表回路1总因数,,sint32,1,2,100.0,-100.0,100.0,,,
交流电表,ACEM,0x803c01,0x080501,analog data,ACEM Loop 1 Phase Power Factor,ACEM Loop 1 Ph.Pwr.F.,交流电表回路1相功率因数,交流电表回路1相因数,,sint32,3,2,100.0,-100.0,100.0,,,
交流电表,ACEM,0x803c01,0x080601,analog data,ACEM Loop 1 Frequency,ACEM Loop 1 Freq.,交流电表回路1频率,交流电表回路1频率,Hz,sint16,1,2,5000.0,4000.0,7500.0,,,
交流电表,ACEM,0x803c01,0x080701,analog data,ACEM Loop 1 Energy,ACEM Loop 1 Eng.,交流电表回路1电量,交流电表回路1电量,kWh,sint32,1,2,0.0,0.0,,,,
交流电表,ACEM,0x803c01,0x080801,analog data,ACEM Loop 2 Voltage,ACEM Loop 2 Volt.,交流电表回路2电压,交流电表回路2电压,V,sint16,3,1,2200.0,0.0,3000.0,,,
交流电表,ACEM,0x803c01,0x080901,analog data,ACEM Loop 2 Current,ACEM Loop 2 Curr.,交流电表回路2电流,交流电表回路2电流,A,sint16,3,1,0.0,0.0,6000.0,,,
交流电表,ACEM,0x803c01,0x080a01,analog data,ACEM Loop 2 Total Active Power,ACEM Loop 2 Tol.Act.P.,交流电表回路2总有功功率,交流电表回路2总有功,kW,sint32,1,2,0.0,0.0,,,,
交流电表,ACEM,0x803c01,0x080b01,analog data,ACEM Loop 2 Total Power Factor,ACEM Loop 2 Tol.Pwr.F.,交流电表回路2总功率因数,交流电表回路2总因数,,sint32,1,2,100.0,-100.0,100.0,,,
交流电表,ACEM,0x803c01,0x080c01,analog data,ACEM Loop 2 Phase Power Factor,ACEM Loop 2 Ph.Pwr.F.,交流电表回路2相功率因数,交流电表回路2相因数,,sint32,3,2,100.0,-100.0,100.0,,,
交流电表,ACEM,0x803c01,0x080d01,analog data,ACEM Loop 2 Frequency,ACEM Loop 2 Freq.,交流电表回路2频率,交流电表回路2频率,Hz,sint16,1,2,5000.0,4000.0,7500.0,,,
交流电表,ACEM,0x803c01,0x080e01,analog data,ACEM Loop 2 Energy,ACEM Loop 2 Eng.,交流电表回路2电量,交流电表回路2电量,kWh,sint32,1,2,0.0,0.0,,,,
交流电表,ACEM,0x803c01,0x080f01,analog data,ACEM Loop 3 Voltage,ACEM Loop 3 Volt.,交流电表回路3电压,交流电表回路3电压,V,sint16,3,1,2200.0,0.0,3000.0,,,
交流电表,ACEM,0x803c01,0x081001,analog data,ACEM Loop 3 Current,ACEM Loop 3 Curr.,交流电表回路3电流,交流电表回路3电流,A,sint16,3,1,0.0,0.0,6000.0,,,
交流电表,ACEM,0x803c01,0x081101,analog data,ACEM Loop 3 Total Active Power,ACEM Loop 3 Tol.Act.P.,交流电表回路3总有功功率,交流电表回路3总有功,kW,sint32,1,2,0.0,0.0,,,,
交流电表,ACEM,0x803c01,0x081201,analog data,ACEM Loop 3 Total Power Factor,ACEM Loop 3 Tol.Pwr.F.,交流电表回路3总功率因数,交流电表回路3总因数,,sint32,1,2,100.0,-100.0,100.0,,,
交流电表,ACEM,0x803c01,0x081301,analog data,ACEM Loop 3 Phase Power Factor,ACEM Loop 3 Ph.Pwr.F.,交流电表回路3相功率因数,交流电表回路3相因数,,sint32,3,2,100.0,-100.0,100.0,,,
交流电表,ACEM,0x803c01,0x081401,analog data,ACEM Loop 3 Frequency,ACEM Loop 3 Freq.,交流电表回路3频率,交流电表回路3频率,Hz,sint16,1,2,5000.0,4000.0,7500.0,,,
交流电表,ACEM,0x803c01,0x081501,analog data,ACEM Loop 3 Energy,ACEM Loop 3 Eng.,交流电表回路3电量,交流电表回路3电量,kWh,sint32,1,2,0.0,0.0,,,,
交流电表,ACEM,0x803c01,0x081601,analog data,ACEM Loop 4 Voltage,ACEM Loop 4 Volt.,交流电表回路4电压,交流电表回路4电压,V,sint16,3,1,2200.0,0.0,3000.0,,,
交流电表,ACEM,0x803c01,0x081701,analog data,ACEM Loop 4 Current,ACEM Loop 4 Curr.,交流电表回路4电流,交流电表回路4电流,A,sint16,3,1,0.0,0.0,6000.0,,,
交流电表,ACEM,0x803c01,0x081801,analog data,ACEM Loop 4 Total Active Power,ACEM Loop 4 Tol.Act.P.,交流电表回路4总有功功率,交流电表回路4总有功,kW,sint32,1,2,0.0,0.0,,,,
交流电表,ACEM,0x803c01,0x081901,analog data,ACEM Loop 4 Total Power Factor,ACEM Loop 4 Tol.Pwr.F.,交流电表回路4总功率因数,交流电表回路4总因数,,sint32,1,2,100.0,-100.0,100.0,,,
交流电表,ACEM,0x803c01,0x081a01,analog data,ACEM Loop 4 Phase Power Factor,ACEM Loop 4 Ph.Pwr.F.,交流电表回路4相功率因数,交流电表回路4相因数,,sint32,3,2,100.0,-100.0,100.0,,,
交流电表,ACEM,0x803c01,0x081b01,analog data,ACEM Loop 4 Frequency,ACEM Loop 4 Freq.,交流电表回路4频率,交流电表回路4频率,Hz,sint16,1,2,5000.0,4000.0,7500.0,,,
交流电表,ACEM,0x803c01,0x081c01,analog data,ACEM Loop 4 Energy,ACEM Loop 4 Eng.,交流电表回路4电量,交流电表回路4电量,kWh,sint32,1,2,0.0,0.0,,,,
交流电表,ACEM,0x803c01,0x180101,alarm,ACEM Communication Fail,ACEM Comm.Fail,交流电表通讯断告警,交流电表通讯断,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
交流电表,ACEM,0x803c01,0x480101,alarm DO,ACEM Communication Fail,ACEM Comm.Fail,交流电表通讯断告警,交流电表通讯断,,sint8,1,0,,,,,,
交流电表,ACEM,0x803c01,0x200101,control,ACEM Energy Reset,ACEM Eng.Reset,交流电表电量清零,交流电表电量清零,,sint8,1,,,,,,,
交流电表,ACEM,0x803c01,0x280101,parameter,ACEM Loop 1 Config,ACEM Loop 1 Config,交流电表回路1配置,交流电表回路1配置,,sint8,1,0,0,0,9,1,0:无/None;1:市电/Mains;2:油机1/DG1;3:油机2/DG2;4:电源交流输入/ACInput;5:交流空调/ACAircd;6:交流负载/ACLoad;7:其他/Other;8:市电2/Mains2;9:ATS输出/ATS Out;,
交流电表,ACEM,0x803c01,0x280201,parameter,ACEM Loop 2 Config,ACEM Loop 2 Config,交流电表回路2配置,交流电表回路2配置,,sint8,1,0,0,0,9,1,0:无/None;1:市电/Mains;2:油机1/DG1;3:油机2/DG2;4:电源交流输入/ACInput;5:交流空调/ACAircd;6:交流负载/ACLoad;7:其他/Other;8:市电2/Mains2;9:ATS输出/ATS Out;,
交流电表,ACEM,0x803c01,0x280301,parameter,ACEM Loop 3 Config,ACEM Loop 3 Config,交流电表回路3配置,交流电表回路3配置,,sint8,1,0,0,0,9,1,0:无/None;1:市电/Mains;2:油机1/DG1;3:油机2/DG2;4:电源交流输入/ACInput;5:交流空调/ACAircd;6:交流负载/ACLoad;7:其他/Other;8:市电2/Mains2;9:ATS输出/ATS Out;,
交流电表,ACEM,0x803c01,0x280401,parameter,ACEM Loop 4 Config,ACEM Loop 4 Config,交流电表回路4配置,交流电表回路4配置,,sint8,1,0,0,0,9,1,0:无/None;1:市电/Mains;2:油机1/DG1;3:油机2/DG2;4:电源交流输入/ACInput;5:交流空调/ACAircd;6:交流负载/ACLoad;7:其他/Other;8:市电2/Mains2;9:ATS输出/ATS Out;,
交流电表,ACEM,0x803c01,0x280501,parameter,ACEM Loop 1 Config Name,ACEM Loop 1 Config Name,交流电表回路1配置名称,交流电表回路1配置名称,,string32,1,0,AC-X#,,,,,
交流电表,ACEM,0x803c01,0x280601,parameter,ACEM Loop 2 Config Name,ACEM Loop 2 Config Name,交流电表回路2配置名称,交流电表回路2配置名称,,string32,1,0,AC-X#,,,,,
交流电表,ACEM,0x803c01,0x280701,parameter,ACEM Loop 3 Config Name,ACEM Loop 3 Config Name,交流电表回路3配置名称,交流电表回路3配置名称,,string32,1,0,AC-X#,,,,,
交流电表,ACEM,0x803c01,0x280801,parameter,ACEM Loop 4 Config Name,ACEM Loop 4 Config Name,交流电表回路4配置名称,交流电表回路4配置名称,,string32,1,0,AC-X#,,,,,
交流电表,ACEM,0x803c01,0x400101,device info,ACEM System Name,ACEM Sys.Name,交流电表系统名称,ACEM系统名称,,string32,1,,,,,,,
交流电表,ACEM,0x803c01,0x400201,device info,ACEM Software Version,ACEM Soft.Ver.,交流电表软件版本,ACEM软件版本,,string16,1,,,,,,,
交流电表,ACEM,0x803c01,0x400301,device info,ACEM Software Release Date,ACEM Soft.Date,交流电表软件发布日期,ACEM软件日期,,date,1,,,,,,,
NFBBMS,NFBBMS,0x804201,0x080101,analog data,Li Battery Voltage,Li Batt.Volt.,锂电电压,锂电电压,V,sint16,1,2,5440.0,0.0,6000.0,,,
NFBBMS,NFBBMS,0x804201,0x080201,analog data,Li Battery Current,Li Batt.Curr.,锂电电流,锂电电流,A,sint32,1,2,0.0,-10000.0,10000.0,,,
NFBBMS,NFBBMS,0x804201,0x080301,analog data,Inner Environment Temperature,Inner Env.Temp.,机内环境温度,机内环境温度,℃,sint16,1,2,5000.0,-3000.0,10000.0,,,
NFBBMS,NFBBMS,0x804201,0x080401,analog data,Cell Voltage,Cell Volt.,单体电压,单体电压,V,sint16,16,3,3500.0,0.0,4000.0,,,
NFBBMS,NFBBMS,0x804201,0x080501,analog data,Cell Temperature,Cell Temp.,单体温度,单体温度,℃,sint16,4,2,2500.0,-3000.0,9000.0,,,
NFBBMS,NFBBMS,0x804201,0x080601,analog data,Battery SOC,Battery SOC,电池SOC,电池SOC,%,sint16,1,2,200.0,0.0,10000.0,,,
NFBBMS,NFBBMS,0x804201,0x080701,analog data,Battery SOH,Battery SOH,电池SOH,电池SOH,%,sint16,1,0,0,0,100,,,
NFBBMS,NFBBMS,0x804201,0x080801,analog data,Battery Total Recycle Times,Total Cyc.Times,电池累计循环次数,累计循环次数,,sint32,1,0,0,0,65535,,,
NFBBMS,NFBBMS,0x804201,0x080901,analog data,Battery Board Temprature,Batt.Board Temp.,单板温度,单板温度,℃,sint16,1,2,2500.0,-3000.0,9000.0,,,
NFBBMS,NFBBMS,0x804201,0x100101,digital data,Placeholder,Placeholder,占位符,占位符,,uint32_bit,1,0,0,0,1,,0:关闭/Off;1:开启/On;,
NFBBMS,NFBBMS,0x804201,0x100101,digital data,Cell Equalization Circuit Status,Cell Equ.Stu.,单体均衡启动状态,单体均衡状态,,bit1_2,1,0,0,0,1,,0:关闭/Off;1:开启/On;,
NFBBMS,NFBBMS,0x804201,0x100201,digital data,Battery Charge Loop Switch Status,Chg.Sw.Stu.,充电回路开关状态,充开关状态,,bit3_2,1,0,0,0,1,,0:断开/Break;1:闭合/Close;,
NFBBMS,NFBBMS,0x804201,0x100301,digital data,Battery Discharge Loop Switch Status,Dch.Sw.Stu.,放电回路开关状态,放开关状态,,bit5_2,1,0,0,0,1,,0:断开/Break;1:闭合/Close;,
NFBBMS,NFBBMS,0x804201,0x100401,digital data,Buzzer Status,Buzz.Stu.,蜂鸣器状态,蜂鸣器状态,,bit7_2,1,0,0,0,1,,0:关闭/Off;1:开启/On;,
NFBBMS,NFBBMS,0x804201,0x100501,digital data,Heater Switch Status,Heater Switch Stu.,加热垫状态,加热垫状态,,bit9_2,1,0,0,0,1,,0:关闭/Off;1:开启/On;,
NFBBMS,NFBBMS,0x804201,0x100601,digital data,Battery Full of Charge,Full of Chg.,电池充满电标志,电池充满电标志,,bit11_2,1,0,0,0,1,,0:否/No;1:是/Yes;,
NFBBMS,NFBBMS,0x804201,0x100701,digital data,Battery Group Status,Batt.Stu.,电池状态,电池状态,,bit15_4,1,0,0,0,1,,0:待机/Standby;1:充电/Charge;2:放电管理/Discharge;3:保护/Protection;4:异常/Fault;,
NFBBMS,NFBBMS,0x804201,0x100801,digital data,Battery Requiring Charge Status,Req.Chg.,电池充电请求标志,电池充电请求标志,,bit17_2,1,0,0,0,1,,0:不需调压/No Adjust;1:需要调压/Adjust;,
NFBBMS,NFBBMS,0x804201,0x100901,digital data,Battery Charge Limit Status,Batt.Chg.Lim.Stu.,电池充电限流状态,电池充电限流状态,,bit19_2,1,0,0,0,1,,0:正常/Normal;1:限流/Limit;,
NFBBMS,NFBBMS,0x804201,0x180101,alarm,Battery Over Voltage Alarm,Batt.OVA.Alm.,电池过压告警,电池过压,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x480101,alarm DO,Battery Over Voltage Alarm,Batt.OVA.Alm.,电池过压告警,电池过压,,sint8,1,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x180201,alarm,Battery Over Voltage Protection Alarm,Batt.OVP.Alm.,电池过压保护,电池过压保护,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x480201,alarm DO,Battery Over Voltage Protection Alarm,Batt.OVP.Alm.,电池过压保护,电池过压保护,,sint8,1,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x180301,alarm,Battery Under Voltage Alarm,Batt.UVA.Alm.,电池欠压告警,电池欠压,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x480301,alarm DO,Battery Under Voltage Alarm,Batt.UVA.Alm.,电池欠压告警,电池欠压,,sint8,1,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x180401,alarm,Battery Under Voltage Protection Alarm,Batt.UVP.Alm.,电池欠压保护,电池欠压保护,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x480401,alarm DO,Battery Under Voltage Protection Alarm,Batt.UVP.Alm.,电池欠压保护,电池欠压保护,,sint8,1,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x180501,alarm,Board Over Temperature Alarm,Board Over Temp.Alm.,单板过温告警,单板过温告警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x480501,alarm DO,Board Over Temperature Alarm,Board Over Temp.Alm.,单板过温告警,单板过温告警,,sint8,1,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x180601,alarm,Board Over Temperature Protection Alarm,Board OTP.Alm.,单板过温保护,单板过温保护,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x480601,alarm DO,Board Over Temperature Protection Alarm,Board OTP.Alm.,单板过温保护,单板过温保护,,sint8,1,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x180701,alarm,Battery Charge Over Current Alarm,Batt.Chg.OCA.,电池充电过流告警,电池充过流,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x480701,alarm DO,Battery Charge Over Current Alarm,Batt.Chg.OCA.,电池充电过流告警,电池充过流,,sint8,1,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x180801,alarm,Battery Charge Over Current Protection Alarm,Batt.Chg.OCP.,电池充电过流保护,电池充过流保护,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x480801,alarm DO,Battery Charge Over Current Protection Alarm,Batt.Chg.OCP.,电池充电过流保护,电池充过流保护,,sint8,1,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x180901,alarm,Battery Discharge Over Current Alarm,Batt.Disch.OCA.,电池放电过流告警,电池放过流,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x480901,alarm DO,Battery Discharge Over Current Alarm,Batt.Disch.OCA.,电池放电过流告警,电池放过流,,sint8,1,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x180a01,alarm,Battery Discharge Over Current Protection Alarm,Batt.Disch.OCP.,电池放电过流保护,电池放过流保护,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x480a01,alarm DO,Battery Discharge Over Current Protection Alarm,Batt.Disch.OCP.,电池放电过流保护,电池放过流保护,,sint8,1,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x180b01,alarm,Inner Environment Over Temperature Alarm,Inner Env.OTA.,机内环境温度高告警,机内环境过温,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x480b01,alarm DO,Inner Environment Over Temperature Alarm,Inner Env.OTA.,机内环境温度高告警,机内环境过温,,sint8,1,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x180c01,alarm,Inner Environment Under Temperature Alarm,Inner Env.UTA.,机内环境温度低告警,机内环境低温,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x480c01,alarm DO,Inner Environment Under Temperature Alarm,Inner Env.UTA.,机内环境温度低告警,机内环境低温,,sint8,1,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x180d01,alarm,Cell Over Volatge Alarm,Cell OVA.Alm.,单体过压告警,单体过压,,sint8,16,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x480d01,alarm DO,Cell Over Volatge Alarm,Cell OVA.Alm.,单体过压告警,单体过压,,sint8,16,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x180e01,alarm,Cell Over Volatge Protection Alarm,Cell OVP.Alm.,单体过压保护,单体过压保护,,sint8,16,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x480e01,alarm DO,Cell Over Volatge Protection Alarm,Cell OVP.Alm.,单体过压保护,单体过压保护,,sint8,16,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x180f01,alarm,Cell Under Voltage Alarm,Cell UVA.Alm.,单体欠压告警,单体欠压,,sint8,16,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x480f01,alarm DO,Cell Under Voltage Alarm,Cell UVA.Alm.,单体欠压告警,单体欠压,,sint8,16,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x181001,alarm,Cell Under Voltage Protection Alarm,Cell UVP.Alm.,单体欠压保护,单体欠压保护,,sint8,16,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x481001,alarm DO,Cell Under Voltage Protection Alarm,Cell UVP.Alm.,单体欠压保护,单体欠压保护,,sint8,16,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x181101,alarm,Cell Charge Over Temperature Alarm,Cell Chg.OTA.,单体充电高温告警,单体充高温,,sint8,4,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x481101,alarm DO,Cell Charge Over Temperature Alarm,Cell Chg.OTA.,单体充电高温告警,单体充高温,,sint8,4,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x181201,alarm,Cell Charge Over Temperature Protection Alarm,Cell Chg.OTP.,单体充电高温保护告警,单体充高温保护,,sint8,4,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x481201,alarm DO,Cell Charge Over Temperature Protection Alarm,Cell Chg.OTP.,单体充电高温保护告警,单体充高温保护,,sint8,4,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x181301,alarm,Cell Discharge Over Temperature Alarm,Cell Disch.OTA.,单体放电高温告警,单体放高温,,sint8,4,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x481301,alarm DO,Cell Discharge Over Temperature Alarm,Cell Disch.OTA.,单体放电高温告警,单体放高温,,sint8,4,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x181401,alarm,Cell Discharge Over Temperature Protection Alarm,Cell Disch.OTP.,单体放电高温保护告警,单体放高温保护,,sint8,4,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x481401,alarm DO,Cell Discharge Over Temperature Protection Alarm,Cell Disch.OTP.,单体放电高温保护告警,单体放高温保护,,sint8,4,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x181501,alarm,Cell Charge Under Temperature Alarm,Cell Chg.UTA.,单体充电低温告警,单体充低温,,sint8,4,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x481501,alarm DO,Cell Charge Under Temperature Alarm,Cell Chg.UTA.,单体充电低温告警,单体充低温,,sint8,4,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x181601,alarm,Cell Charge Under Temperature Protection Alarm,Cell Chg.UTP.,单体充电低温保护告警,单体充低温保护,,sint8,4,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x481601,alarm DO,Cell Charge Under Temperature Protection Alarm,Cell Chg.UTP.,单体充电低温保护告警,单体充低温保护,,sint8,4,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x181701,alarm,Cell Discharge Under Temperature Alarm,Cell Disch.UTA.,单体放电低温告警,单体放低温,,sint8,4,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x481701,alarm DO,Cell Discharge Under Temperature Alarm,Cell Disch.UTA.,单体放电低温告警,单体放低温,,sint8,4,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x181801,alarm,Cell Discharge Under Temperature Protection Alarm,Cell Disch.UTP.,单体放电低温保护告警,单体放低温保护,,sint8,4,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x481801,alarm DO,Cell Discharge Under Temperature Protection Alarm,Cell Disch.UTP.,单体放电低温保护告警,单体放低温保护,,sint8,4,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x181901,alarm,Battery SOC Low Alarm,Batt.Soc.L.Alm.,电池SOC低告警,电池SOC低,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x481901,alarm DO,Battery SOC Low Alarm,Batt.Soc.L.Alm.,电池SOC低告警,电池SOC低,,sint8,1,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x181a01,alarm,Battery Soc Low Protect Alarm,Batt.Soc.L.Prot.,电池SOC低保护,电池SOC低保护,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x481a01,alarm DO,Battery Soc Low Protect Alarm,Batt.Soc.L.Prot.,电池SOC低保护,电池SOC低保护,,sint8,1,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x181b01,alarm,Battery SOH Alarm,Batt.SOH.Alm.,电池SOH告警,电池SOH,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x481b01,alarm DO,Battery SOH Alarm,Batt.SOH.Alm.,电池SOH告警,电池SOH,,sint8,1,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x181c01,alarm,Battery SOH Protect Alarm,Batt.SOH Prot.,电池SOH保护,电池SOH保护,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x481c01,alarm DO,Battery SOH Protect Alarm,Batt.SOH Prot.,电池SOH保护,电池SOH保护,,sint8,1,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x181d01,alarm,Cell Damage Protect Alarm,Cell Dmg.Prt.,单体损坏保护,单体损坏保护,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x481d01,alarm DO,Cell Damage Protect Alarm,Cell Dmg.Prt.,单体损坏保护,单体损坏保护,,sint8,1,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x181e01,alarm,Battery Missing Alarm,Batt.Missing Alm.,电池丢失告警,电池丢失,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x481e01,alarm DO,Battery Missing Alarm,Batt.Missing Alm.,电池丢失告警,电池丢失,,sint8,1,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x181f01,alarm,Charge Switch Invalid Alarm,Chg.Sw.Inv.Alm.,充电回路开关失效告警,充开关失效,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x481f01,alarm DO,Charge Switch Invalid Alarm,Chg.Sw.Inv.Alm.,充电回路开关失效告警,充开关失效,,sint8,1,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x182001,alarm,Discharge Breaker Invalid Alarm,Dch.Sw.Inv.Alm.,放电回路开关失效告警,放开关失效,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x482001,alarm DO,Discharge Breaker Invalid Alarm,Dch.Sw.Inv.Alm.,放电回路开关失效告警,放开关失效,,sint8,1,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x182101,alarm,BMS Communication Fail Alarm,BMS Commfail.Alm.,BMS通信断告警,BMS通信断,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x482101,alarm DO,BMS Communication Fail Alarm,BMS Commfail.Alm.,BMS通信断告警,BMS通信断,,sint8,1,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x182201,alarm,Limit Current Invalid Alarm,Lim.Curr.Inv.Alm.,限流回路失效告警,限流回路失效,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x482201,alarm DO,Limit Current Invalid Alarm,Lim.Curr.Inv.Alm.,限流回路失效告警,限流回路失效,,sint8,1,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x182301,alarm,Short Cut Protection Alarm,Short Cut Prot.,短路保护,短路保护,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x482301,alarm DO,Short Cut Protection Alarm,Short Cut Prot.,短路保护,短路保护,,sint8,1,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x182401,alarm,Battery Reserve Alarm,Batt.Reserve Alm.,电池反接告警,电池反接,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x482401,alarm DO,Battery Reserve Alarm,Batt.Reserve Alm.,电池反接告警,电池反接,,sint8,1,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x182501,alarm,Cell Temperature Sensor Invalid Alarm,Cell TI.Alm.,单体温度传感器失效告警,单体温传失效告警,,sint8,4,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x482501,alarm DO,Cell Temperature Sensor Invalid Alarm,Cell TI.Alm.,单体温度传感器失效告警,单体温传失效告警,,sint8,4,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x182601,alarm,Volt Sample Fault Alarm,Volt.Sample Fault,电压采样故障,电压采样故障,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x482601,alarm DO,Volt Sample Fault Alarm,Volt.Sample Fault,电压采样故障,电压采样故障,,sint8,1,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x182701,alarm,Environment Temperature Invalid Alarm,Env.Temp.Inv.Alm.,环境温度失效,环境温度失效,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x482701,alarm DO,Environment Temperature Invalid Alarm,Env.Temp.Inv.Alm.,环境温度失效,环境温度失效,,sint8,1,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x182801,alarm,Cell Poor Alarm,Cell Poor Alm.,单体落后告警,单体落后告警,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x482801,alarm DO,Cell Poor Alarm,Cell Poor Alm.,单体落后告警,单体落后告警,,sint8,1,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x182901,alarm,Cell Poor Protection Alarm,Cell Poor Prot.Alm.,单体落后保护,单体落后保护,,sint8,1,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
NFBBMS,NFBBMS,0x804201,0x482901,alarm DO,Cell Poor Protection Alarm,Cell Poor Prot.Alm.,单体落后保护,单体落后保护,,sint8,1,0,,,,,,
NFBBMS,NFBBMS,0x804201,0x400101,device info,BMS System Name,BMS Sys.Name,BMS系统名称,BMS系统名称,,string16,1,,,,,,,
NFBBMS,NFBBMS,0x804201,0x400201,device info,BMS Software Version,BMS Software Ver.,BMS软件版本,BMS软件版本,,string16,1,,,,,,,
NFBBMS,NFBBMS,0x804201,0x400301,device info,BMS Manufactory Name,BMS Manufactory Name,BMS厂家名称,BMS厂家名称,,string32,1,,,,,,,
NFBBMS,NFBBMS,0x804201,0x400401,device info,Whole Machine Serial Number,Whole Machine SN.,整机序列号,整机序列号,,string32,1,,,,,,,
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x080101,analog data,DC Voltage,DC Voltage,直流电压,直流电压,V,sint16,1,1,0.0,0.0,600.0,0.1,,
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x080201,analog data,Distribution Unit Current,DU Curr.,配电单元电流,配电单元电流,A,sint32,2,1,0.0,0.0,30000.0,,,
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x080301,analog data,Distribution Unit Energy,DU NRG,配电单元电量,配电单元电量,kWh,sint32,2,2,,,,,,
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x080401,analog data,Distribution Unit Load Power,DU Load Pwr.,配电单元负载功率,配电单元功率,kW,sint32,2,2,0.0,0.0,60000.0,,,
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x080501,analog data,SDDU Total Current,SDDU Total Curr.,SDDU总电流,SDDU总电流,A,sint32,1,1,0.0,0.0,30000.0,,,
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x100201,digital data,Distribution Unit Minor Load Loop Status,DU Minor Load Stu.,配电单元次要负载分路状态,配电单元次要负载,,sint8,2,0,0,0,1,,0:断开/Break;1:闭合/Close;,
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x100301,digital data,Distribution Unit Major Load Loop Status,DU Major Load Stu.,配电单元主要负载分路状态,配电单元主要负载,,sint8,2,0,0,0,1,,0:断开/Break;1:闭合/Close;,
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x100401,digital data,Distribution Unit Minor LLVD Status,Minor LLVD Stu.,配电单元次要负载下电状态,次要负载下电状态,,sint8,2,0,0,0,1,,0:恢复/Reset;1:动作/Set;,
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x100501,digital data,SDDU Work State,SDDU Work Sta.,SDDU工作状态,SDDU工作状态,,sint8,1,0,0,0,4,1,0:无/Null;1:正常/Normal;2:告警/Alarm;3:通讯断/CommFail;4:升级/Update;,
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x180101,alarm,SDDU Communication Fail,SDDU Comm.Fail,SDDU通讯中断,SDDU通讯断,,sint8,1,0,,,,1,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x480101,alarm DO,SDDU Communication Fail,SDDU Comm.Fail,SDDU通讯中断,SDDU通讯断,,sint8,1,0,,,,1,,
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x180201,alarm,Distribution Unit Minor LLVD Alarm,Minor LLVD Alm.,配电单元次要负载下电告警,次要负载下电告警,,sint8,2,0,,,,,1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x480201,alarm DO,Distribution Unit Minor LLVD Alarm,Minor LLVD Alm.,配电单元次要负载下电告警,次要负载下电告警,,sint8,2,0,,,,,,
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x180301,alarm,Distribution Unit Minor Load Loop Broken,DU Minor Load Brk.,配电单元次要负载分路断,配电单元次要负载断,,sint8,2,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x480301,alarm DO,Distribution Unit Minor Load Loop Broken,DU Minor Load Brk.,配电单元次要负载分路断,配电单元次要负载断,,sint8,2,0,,,,,,
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x180401,alarm,Distribution Unit Major Load Loop Broken,DU Major Load Brk.,配电单元主要负载分路断,配电单元主要负载断,,sint8,2,0,,,,,0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning;,
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x480401,alarm DO,Distribution Unit Major Load Loop Broken,DU Major Load Brk.,配电单元主要负载分路断,配电单元主要负载断,,sint8,2,0,,,,,,
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x200301,control,Distribution Unit Energy Reset,DU Eng.Reset,配电单元电量清零,配电单元电量清零,,sint8,2,,,,,,,
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x280101,parameter,Distribution Unit LLVD Enabled,DU LLVD Enabled,配电单元下电使能,配电单元下电使能,,sint8,2,,1,0,1,,0:禁止/Disabled;1:允许/Enabled;,
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x280201,parameter,Distribution Unit LLVD Voltage Threshold,DU LLVD Volt.Thre.,配电单元下电电压阈值,配电单元下电电压,V,sint16,2,1,450.0,380.0,520.0,1.0,,"<=Battery Group|Test Stop Voltage-1V;
<=Battery Group|Battery Voltage Low Threshold-1V;
<=Diesel Generator Group|DG Start Voltage Threshold-0.5V;
<=Diesel Generator Group|DG Stop Voltage Threshold-2V;
<=Mains Group|MAINS Start Voltage Threshold-1V;
<=Mains Group|MAINS Stop Voltage Threshold-2V;
"
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x280301,parameter,Distribution Unit LLVD Duration,DU LLVD Dura.,配电单元下电时间,配电单元下电时间,Min,sint16,2,0,1440,3,7200,1,,">=Diesel Generator Group|DG Start Discharge Duration+30;
>=Mains Group|MAINS Start Discharge Duration Threshold+30;
"
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x280401,parameter,Distribution Unit LLVD SOC Threshold,DU LLVD SOC,配电单元下电SOC阈值,配电单元下电SOC,%,sint16,2,0,20,2,90,1,,"<=Battery Group|Test Stop SOC Threshold-5%;
<=Diesel Generator Group|DG Start SOC Threshold-5%;
<=Diesel Generator Group|DG Stop SOC Threshold-10%;
<=Mains Group|MAINS Start SOC Threshold-5%;
<=Mains Group|MAINS Stop SOC Threshold-10%;
"
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x280501,parameter,Distribution Unit Upload Voltage Threshold,DU Upload Volt.,配电单元上电电压,配电单元上电电压,V,sint16,2,1,500.0,480.0,575.0,1.0,,
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x280601,parameter,Distribution Unit Tenant Config,DU Tenant Cfg.,配电单元租户设置,配电单元租户设置,,sint8,2,0,0,0,4,1,0:无/None;1:租户1/Tenant1;2:租户2/Tenant2;3:租户3/Tenant3;4:租户4/Tenant4;5:租户5/Tenant5;6:租户6/Tenant6;7:租户7/Tenant7;8:租户8/Tenant8;,
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x280701,parameter,Distribution Unit Name,DU Name,配电单元名称,配电单元名称,,string64,2,,Dist.Unit-X#,,,,,
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x280901,parameter,Distribution Unit Fixed Time Disconnect Enabled,DU FixT.Disc En.,配电单元定时下电使能,配电单元定时下电使能,,sint8,2,0,0,0,1,1,0:禁止/Disabled;1:允许/Enabled;,
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x280a01,parameter,Distribution Unit Fixed Time Disconnect Start Clock1,DU FixT.Disc St.1,配电单元定时下电起始时刻1,定时下电起始时刻1,,hour_minute,5,0,8:00,0:00,23:59,1,,
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x280b01,parameter,Distribution Unit Fixed Time Disconnect End Clock1,DU FixT.Disc End.1,配电单元定时下电终止时刻1,定时下电终止时刻1,,hour_minute,5,0,8:00,0:00,23:59,1,,
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x280c01,parameter,Distribution Unit Fixed Time Disconnect Start Clock2,DU FixT.Disc St.2,配电单元定时下电起始时刻2,定时下电起始时刻2,,hour_minute,5,0,8:00,0:00,23:59,1,,
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x280d01,parameter,Distribution Unit Fixed Time Disconnect End Clock2,DU FixT.Disc End.2,配电单元定时下电终止时刻2,定时下电终止时刻2,,hour_minute,5,0,8:00,0:00,23:59,1,,
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x280e01,parameter,Distribution Unit Remote Disconnect Enabled,DU Remote Disc.En.,配电单元远程下电使能,配电单元远程下电使能,,sint8,2,0,0,0,1,1,0:禁止/Disabled;1:允许/Enabled;,
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x280f01,parameter,Distribution Unit Remote Disconnect,DU Remote Disc.,配电单元远程下电,配电单元远程下电,,sint8,2,0,0,0,1,1,0:上电/Recover;1:下电/Disconnect;,
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x281001,parameter,SDDU Access Configuration,SDDU Access Config.,SDDU接入配置,SDDU接入配置,,sint8,1,0,0,0,1,1,0:母排/Busbar;1:负载/Load;,
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x380101,stastic data,Distribution Unit Total Energy Consumption,DU Total NRG Cons.,配电单元总耗电量,配电单元总电量,kWh,sint32,2,2,,,,,,
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x380201,stastic data,SDDU Total Energy Consumption,SDDU NRG Cons.,SDDU总耗电量,SDDU总电量,kWh,sint32,1,2,,,,,,
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x400101,device info,SDDU System Name,SDDU Sys.Name,SDDU系统名称,SDDU系统名称,,string32,1,,,,,,,
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x400201,device info,SDDU Software Version,SDDU Soft.Ver.,SDDU软件版本,SDDU软件版本,,string32,1,,,,,,,
智能直流配电单元,Smart DC Distribution Unit,0x804401,0x400301,device info,SDDU Software Release Date,SDDU Soft.Date,SDDU软件发布日期,SDDU软件发布日期,,date,1,,,,,,,
