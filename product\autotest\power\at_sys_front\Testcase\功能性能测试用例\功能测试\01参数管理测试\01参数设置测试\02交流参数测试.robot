*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
交流电流高阈值参数测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    交流电流高阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    交流电流高阈值
    ${缺省值}    获取web参数上下限范围    交流电流高阈值
    ${可设置范围}    获取web参数可设置范围    交流电流高阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    交流电流高阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    交流电流高阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    交流电流高阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    交流电流高阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    交流电流高阈值

交流制式参数测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    交流制式
    显示属性配置    交流接线方式_1    数字量    web_attr=ON    gui_attr=ON
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    交流制式
    ${参数设置范围dict}    获取web参数的取值约定    交流制式
    ${参数设置范围values}    get dictionary values    ${参数设置范围dict}
    ${结果判定范围}    set variable    L1    L1-L2    L1    L1-L2
    FOR    ${参数设置}    ${结果判定}    IN ZIP    ${参数设置范围values}    ${结果判定范围}
        run keyword if    '${参数设置}'=='L-N(220V)' or '${参数设置}'=='L-N(110V)' or '${参数设置}'=='L1N(220V)' or '${参数设置}'=='L1L2(110V)'    run keyword and ignore error    设置web参数量    空调接入相位    L1
        设置web参数量    交流制式    ${参数设置}
        sleep    3
        ${结果获取}    获取web实时数据    交流接线方式_1
        should be equal    ${结果获取}    ${结果判定}
    END
    显示属性配置    交流接线方式_1    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    设置web设备参数量为默认值    交流制式

交流电压高阈值参数测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    交流电压高阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    交流电压高阈值
    ${缺省值}    获取web参数上下限范围    交流电压高阈值
    ${可设置范围}    获取web参数可设置范围    交流电压高阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    交流电压高阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    交流电压高阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    交流电压高阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    交流电压高阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    run keywords    设置web设备参数量为默认值    交流电压高阈值
    ...    AND    设置web设备参数量为默认值    交流电压过高阈值

交流电压低阈值参数测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    交流电压低阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    交流电压低阈值
    ${缺省值}    获取web参数上下限范围    交流电压低阈值
    ${可设置范围}    获取web参数可设置范围    交流电压低阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    交流电压低阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    交流电压低阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    交流电压低阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    交流电压低阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    run keywords    设置web设备参数量为默认值    交流电压过低阈值
    ...    AND    设置web设备参数量为默认值    交流电压低阈值

交流电压不平衡阈值参数测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    交流电压不平衡阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    交流电压不平衡阈值
    ${缺省值}    获取web参数上下限范围    交流电压不平衡阈值
    ${可设置范围}    获取web参数可设置范围    交流电压不平衡阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    交流电压不平衡阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    交流电压不平衡阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    交流电压不平衡阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    交流电压不平衡阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    交流电压不平衡阈值

市电配置参数测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    市电配置
    ${取值约定dict}    获取web参数的取值约定    市电配置
    ${取值约定values}    get dictionary values    ${取值约定dict}
    ${市电配置数量}    获取web参数的数量    市电配置
    FOR    ${i}    IN RANGE    ${市电配置数量}    0    -1
        嵌入for循环    市电配置_${i}    ${取值约定values}
    END
    [Teardown]    run keywords    设置web参数量    市电配置_1    有
    ...    AND    设置web参数量    市电配置_2    无

交流电压过高阈值参数测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    交流电压过高阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    交流电压过高阈值
    ${缺省值}    获取web参数上下限范围    交流电压过高阈值
    ${可设置范围}    获取web参数可设置范围    交流电压过高阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    交流电压过高阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    交流电压过高阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    交流电压过高阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    交流电压过高阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    交流电压过高阈值

交流电压过低阈值参数测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    交流电压过低阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    交流电压过低阈值
    ${缺省值}    获取web参数上下限范围    交流电压过低阈值
    ${可设置范围}    获取web参数可设置范围    交流电压过低阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    交流电压过低阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    交流电压过低阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    交流电压过低阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    交流电压过低阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    交流电压过低阈值
