*** Settings ***
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
批量获取SDU2设备信息测试
    写入CSV文档    SDU2设备信息获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string32    INDEX_DEVICE_CH=智能直流配电单元
    ${排除列表}    create list
	@{信号名称列表1}   create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SDU2排除设备版本信息}    ${排除列表}
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     name     ${信号名称}
		Append To List       ${信号名称列表1}    ${dict}
    END
	Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    SDU2    ${信号名称列表1}    建链    V99.23    SDU2设备信息获取测试
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    SDU2    ${信号名称列表1}    建链    V10.10    SDU2设备信息获取测试
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    SDU2    ${信号名称列表1}    建链    V1.81    SDU2设备信息获取测试


    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string32    INDEX_DEVICE_CH=智能直流配电单元
    ${排除列表}    create list
	@{信号名称列表2}    create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SDU2排除设备名称信息}    ${排除列表}
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     name     ${信号名称}
		Append To List       ${信号名称列表2}    ${dict}
    END
	Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    SDU2    ${信号名称列表2}    建链    VZXDU48 FB100B3    SDU2设备信息获取测试
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    SDU2    ${信号名称列表2}    建链    ZTE-smartli    SDU2设备信息获取测试
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    SDU2    ${信号名称列表2}    建链    VZXDU48 FB100C2    SDU2设备信息获取测试


    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=date    INDEX_DEVICE_CH=智能直流配电单元
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SDU2排除设备日期信息}    ${排除列表}
	@{信号名称列表3}   create_list
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     name     ${信号名称}
		Append To List       ${信号名称列表3}    ${dict}
    END
	Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    SDU2    ${信号名称列表3}    建链    2018-11-15    SDU2设备信息获取测试
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    SDU2    ${信号名称列表3}    建链    2021-08-23    SDU2设备信息获取测试
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    SDU2    ${信号名称列表3}    建链    2018-07-28    SDU2设备信息获取测试