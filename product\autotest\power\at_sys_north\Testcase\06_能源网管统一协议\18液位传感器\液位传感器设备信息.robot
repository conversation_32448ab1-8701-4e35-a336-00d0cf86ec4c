*** Settings ***
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_液位传感器设备信息
    连接CSU
    设置子工具值    llSensor    all    只读    厂家信息    0000000KAN1100000000V1.00
    sleep    30s
    ${获取值1}    获取web实时数据    液位传感器系统名称-1
    ${获取值2}    获取web实时数据    液位传感器软件版本-1
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取液位传感器设备信息    液位传感器系统名称    ${SSH}
    ${power_sm获取值2}    power_sm_获取一个数据的值    获取液位传感器设备信息    液位传感器软件版本    ${SSH}
    should be equal as strings    ${获取值1}    KAN110
    should be equal as strings    ${获取值2}    V1.00
    should be equal as strings    ${power_sm获取值1}    KAN110
    should be equal as strings    ${power_sm获取值2}    V1.00
    设置子工具值    llSensor    all    只读    厂家信息    0000000ZHANGH0000000V7.28
    sleep    30s
    ${获取值1}    获取web实时数据    液位传感器系统名称-1
    ${获取值2}    获取web实时数据    液位传感器软件版本-1
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取液位传感器设备信息    液位传感器系统名称    ${SSH}
    ${power_sm获取值2}    power_sm_获取一个数据的值    获取液位传感器设备信息    液位传感器软件版本    ${SSH}
    should be equal as strings    ${获取值1}    ZHANGH
    should be equal as strings    ${获取值2}    V7.28
    should be equal as strings    ${power_sm获取值1}    ZHANGH
    should be equal as strings    ${power_sm获取值2}    V7.28
    设置子工具值    llSensor    all    只读    厂家信息    0000000KANGYU0000000V1.01
    sleep    30s
    ${获取值1}    获取web实时数据    液位传感器系统名称-1
    ${获取值2}    获取web实时数据    液位传感器软件版本-1
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取液位传感器设备信息    液位传感器系统名称    ${SSH}
    ${power_sm获取值2}    power_sm_获取一个数据的值    获取液位传感器设备信息    液位传感器软件版本    ${SSH}
    should be equal as strings    ${获取值1}    KANGYU
    should be equal as strings    ${获取值2}    V1.01
    should be equal as strings    ${power_sm获取值1}    KANGYU
    should be equal as strings    ${power_sm获取值2}    V1.01
