*** Settings ***
Suite Setup
Suite Teardown
Test Setup        Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
uniform_0002_直流模拟量
    [Documentation]    单组电池SOC-1 电池SOC-1 0x17001010060001 100 val_invalid F
    ...    单组电池电压-2 电池电压-2 0xb002010010001 无效 53.49 F
    ...    单组电池电流-2 电池电流-2 0xb002010030001 无效 0 F
    ...    单组电池温度-2 电池温度-2 0xb002010040001 无效 30 F
    ...    单组电池均充次数-2 电池均充次数-2 0xb002070080001 无效 1 F
    ...    单组电池放电次数-2 电池放电次数-2 0xb002070090001 无效 0 T
    ...    单组电池SOC-2 电池SOC-2 0x17002010060001 无效 val_invalid T
    ...    单组电池电压-3 电池电压-3 0xb003010010001 无效 53.51 F
    ...    单组电池电流-3 电池电流-3 0xb003010030001 无效 0 F
    ...    单组电池温度-3 电池温度-3 0xb003010040001 无效 val_invalid T
    ...    单组电池均充次数-3 电池均充次数-3 0xb003070080001 无效 1 F
    ...    单组电池放电次数-3 电池放电次数-3 0xb003070090001 无效 0 T
    ...    单组电池SOC-3 电池SOC-3 0x17003010060001 无效 val_invalid T
    ...    单组电池电压-4 电池电压-4 0xb004010010001 无效 53.49 F
    ...    单组电池电流-4 电池电流-4 0xb004010030001 无效 0 F
    ...    单组电池温度-4 电池温度-4 0xb004010040001 无效 val_invalid T
    ...    单组电池均充次数-4 电池均充次数-4 0xb004070080001 无效 1 F
    ...    单组电池放电次数-4 电池放电次数-4 0xb004070090001 无效 0 T
    ...    单组电池SOC-4 电池SOC-4 0x17004010060001 无效 val_invalid T
    [Setup]    Run keywords    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池组容量_2    100
    ...    AND    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池组容量_3    100
    ...    AND    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池组容量_4    100
    ...    AND    设置web参数量    均充使能    允许
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    ${协议数据}    统一监控协议_获取数据    直流模拟量
    ${web数据}    统一监控协议_获取web对应数据    ${协议数据}
    ${对比结果}    批量对比数据_统一监控协议_WEB    ${协议数据}    ${web数据}    直流模拟量
    should be true    ${对比结果}
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池组容量_2    0
    ...    AND    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池组容量_3    0
    ...    AND    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池组容量_4    0
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0004_直流状态量√
    [Setup]    run keywords    显示属性配置    电池组在位状态    数字量    web_attr=On    gui_attr=On
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    ${协议数据}    统一监控协议_获取数据    直流状态量
    ${web数据}    统一监控协议_获取web对应数据    ${协议数据}
    ${对比结果}    批量对比数据_统一监控协议_WEB    ${协议数据}    ${web数据}    直流状态量
    should be true    ${对比结果}
    [Teardown]    run keywords    显示属性配置    电池组在位状态    数字量    web_attr=Off    gui_attr=Off
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0006_直流参数量√
    [Setup]
    ${协议数据}    统一监控协议_获取数据    直流参数量
    ${web数据}    统一监控协议_获取web对应数据    ${协议数据}
    ${对比结果}    批量对比数据_统一监控协议_WEB    ${协议数据}    ${web数据}    直流参数量
    should be true    ${对比结果}

uniform_0008_直流参数设置
    ${取值约定}    获取web参数的取值约定    电池配置
    ${val}    Get From Dictionary    ${取值约定}    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    ${val}
    ${协议数据}    统一监控协议_批量参数设置测试    直流参数量
    ${对比结果}    批量对比参数设置_统一监控协议_WEB    ${协议数据}    直流参数量设置
    should be true    ${对比结果}
