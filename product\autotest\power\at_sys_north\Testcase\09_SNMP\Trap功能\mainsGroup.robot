*** Settings ***
Suite Setup       run keywords    连接CSU
...               AND    Wait Until Keyword Succeeds    10    1    设置web参数量    交流制式    L1L2L3N-220V
...               AND    测试用例前置条件
Suite Teardown    测试用例后置条件
Default Tags      T1-1
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
snmp_0004_交流输入限功率预警
    电池管理初始化
    #获取告警级别
    ${级别设置值}    获取web参数量    交流输入限功率预警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    交流输入限功率预警    严重
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息不为    交流输入限功率预警
    Wait Until Keyword Succeeds    10    2    设置web参数量    市电额定有功功率    2.1
    Wait Until Keyword Succeeds    10    2    设置web参数量    市电降额系数    85
    ${可设置范围}    获取web参数可设置范围    交流输入限功率预警阈值
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流输入限功率预警阈值    ${可设置范围}[0]
    ${设置值}    获取web参数量    交流输入限功率预警阈值
    ${电压}    获取web实时数据    直流电压
    run keyword if    ${电压}>54    向下调节电池电压    53.5
    run keyword if    ${电压}<52    向上调节电池电压    53.5
    缓慢设置负载电压电流    ${电压}    26
    打开负载输出
    ###以上告警已产生
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流输入限功率预警    屏蔽
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    交流输入限功率预警
    ${直流电压高告警}    判断告警存在_带返回值    交流输入限功率预警
    should not be true    ${直流电压高告警}
    #已产生，先屏蔽
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    wait until keyword succeeds    30    1    设置web参数量    交流输入限功率预警    严重
    wait until keyword succeeds    3m    2    判断告警存在    交流输入限功率预警
    #产生
    ${snmp英文名}    获取snmp单个告警英文名    交流输入限功率预警
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    #无告警后查询判断
    关闭负载输出
    Wait Until Keyword Succeeds    10    2    设置web参数量    市电额定有功功率    15
    设置WEB设备参数量为默认值    交流输入限功率预警阈值
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流输入限功率预警
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    run keywords    设置web参数量    交流输入限功率预警    严重
    ...    AND    关闭负载输出

snmp_0006_交流输入限功率告警
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    #获取告警级别
    ${级别设置值}    获取web参数量    交流输入限功率告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    交流输入限功率告警    严重
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息不为    交流输入限功率告警
    Wait Until Keyword Succeeds    10    2    设置web参数量    市电额定有功功率    2.1
    Wait Until Keyword Succeeds    10    2    设置web参数量    市电降额系数    85
    ${可设置范围}    获取web参数可设置范围    交流输入限功率告警阈值
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流输入限功率告警阈值    ${可设置范围}[0]
    ${设置值}    获取web参数量    交流输入限功率告警阈值
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    80
    打开负载输出
    ###以上告警已产生
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流输入限功率告警    屏蔽
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    交流输入限功率告警
    ${直流电压高告警}    判断告警存在_带返回值    交流输入限功率告警
    should not be true    ${直流电压高告警}
    #已产生，先屏蔽
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    wait until keyword succeeds    30    1    设置web参数量    交流输入限功率告警    严重
    wait until keyword succeeds    3m    2    判断告警存在    交流输入限功率告警
    #产生
    ${snmp英文名}    获取snmp单个告警英文名    交流输入限功率告警
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    #无告警后查询判断
    设置WEB设备参数量为默认值    交流输入限功率告警阈值
    Wait Until Keyword Succeeds    10    2    设置web参数量    市电额定有功功率    0
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流输入限功率告警
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    run keywords    Wait Until Keyword Succeeds    10    2    设置web参数量    市电额定有功功率    2.1
    ...    AND    Wait Until Keyword Succeeds    10    2    设置web参数量    交流输入限功率告警    严重
    ...    AND    Wait Until Keyword Succeeds    10    2    设置web参数量    市电额定有功功率    0
    ...    AND    关闭负载输出
