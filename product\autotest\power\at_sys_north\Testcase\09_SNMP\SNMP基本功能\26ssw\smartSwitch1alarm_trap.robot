*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
snmp批量获取SSW告警量trap测试
    写入CSV文档    SSW数字量和告警量获取测试    信号名称    信号值    结果    备注
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=智能空开
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SSW排除告警量信号}    ${排除列表}    1
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    smartSwitch    alarm    True
    ${信号名称列表1}    Create List
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    signal_node
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
        Set To Dictionary    ${dict}     snmp_name     ${节点名称}
        Set To Dictionary    ${dict}     Vindex     ${信号序号}
        Set To Dictionary    ${dict}     device_name     智能空开
        Append To List       ${信号名称列表1}    ${dict}
    END
    Run Keyword And Continue On Failure    南向RS485子设备告警量列表产生封装判断结果    snmp    DMU_DCAirCondition    ${信号名称列表1}    
    ...    呼叫    ${告警产生}    SSW数字量和告警量获取测试    负载    智能空开    
    ...    null    null    null    null    null    null
    Run Keyword And Continue On Failure    南向RS485子设备告警量列表恢复封装判断结果    snmp    DMU_DCAirCondition    ${信号名称列表1}    
    ...    呼叫    ${告警恢复}    SSW数字量和告警量获取测试    负载    智能空开
    ...    null    null    null    null    null    null

智能空开通讯断
    连接CSU
    ${级别设置值}    获取web参数量    智能空开通讯断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    智能空开通讯断    严重
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    智能空开通讯断-1
    设置子工具个数    DMU_IntelAirSwit    39
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    智能空开工作状态-40    通讯断
    wait until keyword succeeds    10m    1    判断告警存在    智能空开通讯断-40
    ${snmp英文名}    获取snmp单个告警英文名    智能空开通讯断_40
    ${snmp英文名}    Set Variable    smartSwitch${snmp英文名}
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    设置子工具个数    DMU_IntelAirSwit    40
    Wait Until Keyword Succeeds    20m    5    信号量数据值为    智能空开工作状态-40    正常
    wait until keyword succeeds    10m    1    判断告警不存在    智能空开通讯断-40
    ${告警消失}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警消失}
    [Teardown]    Run Keywords    设置子工具个数    DMU_IntelAirSwit    40
    ...    AND    sleep    5min
