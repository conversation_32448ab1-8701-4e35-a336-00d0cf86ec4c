*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
负载一次下电时间设置测试
    [Documentation]    默认45V；小于等于电池电压低阈值-1、测试终止电压-1
    [Tags]    PMSA-NTest    Restr
    [Setup]    判断web参数是否存在    下电模式
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    纯铅酸
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    铅酸类型
    ${缺省值}    获取web参数上下限范围    铅酸类型
    ${取值约定dict}    获取web参数的取值约定    铅酸类型
    ${取值约定values}    get dictionary values    ${取值约定dict}
    ${val}    Get From Dictionary    ${取值约定dict}    ${缺省值}[0]
    FOR    ${参数设置}    IN    @{取值约定values}
        Wait Until Keyword Succeeds    10    2    设置web参数量    铅酸类型    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    铅酸类型
        should be equal    ${参数获取}    ${参数设置}
        设置下电模式/使能    停电时间    负载一次下电使能    负载一次下电时间
        数值类参数设置最大值最小值默认值    负载一次下电时间
    END
    [Teardown]    设置web设备参数量为默认值    负载一次下电时间    负载一次下电使能    下电模式    铅酸类型    电池配置

负载一次下电SOC阈值设置测试
    [Documentation]    铅酸：10~80，默认20；铁锂：10~90，默认15；负载一次下电SOC阈值<=测试终止SOC阈值-1；测试终止SOC阈值<=测试失败SOC阈值；
    [Tags]    Restr    PMSA-NTest
    [Setup]    判断web参数是否存在    下电模式
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流输入场景    油电    #油电场景测试
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    铅酸&锂电混用
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    铅酸类型
    ${缺省值}    获取web参数上下限范围    铅酸类型
    ${取值约定dict}    获取web参数的取值约定    铅酸类型
    ${取值约定values}    get dictionary values    ${取值约定dict}
    ${val}    Get From Dictionary    ${取值约定dict}    ${缺省值}[0]
    FOR    ${参数设置}    IN    @{取值约定values}    ${val}
        Wait Until Keyword Succeeds    10    2    设置web参数量    铅酸类型    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    铅酸类型
        should be equal    ${参数获取}    ${参数设置}
        设置下电模式/使能    电池剩余容量    负载一次下电使能    负载一次下电SOC阈值
        数值类参数设置最大值最小值默认值    负载一次下电SOC阈值
    END
    [Teardown]    设置web设备参数量为默认值    负载一次下电SOC阈值    负载二次下电SOC阈值    电池下电SOC阈值    测试终止SOC阈值    测试失败SOC阈值    铅酸类型    电池配置

负载二次下电SOC阈值设置测试
    [Documentation]    普通铅酸：10~80，默认10；
    ...    铁锂：5~90，默认5；
    ...    负载二次下电SOC阈值<=测试终止SOC阈值-1；测试终止SOC阈值<=测试失败SOC阈值；
    [Tags]    PMSA-NTest    Restr
    [Setup]    判断web参数是否存在    下电模式
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流输入场景    油电    #油电场景测试
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    铅酸&锂电混用
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    铅酸类型
    ${缺省值}    获取web参数上下限范围    铅酸类型
    ${取值约定dict}    获取web参数的取值约定    铅酸类型
    ${取值约定values}    get dictionary values    ${取值约定dict}
    ${val}    Get From Dictionary    ${取值约定dict}    ${缺省值}[0]
    FOR    ${参数设置}    IN    @{取值约定values}    ${val}
        Wait Until Keyword Succeeds    10    2    设置web参数量    铅酸类型    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    铅酸类型
        should be equal    ${参数获取}    ${参数设置}
        设置下电模式/使能    电池剩余容量    负载二次下电使能    负载二次下电SOC阈值
        数值类参数设置最大值最小值默认值    负载二次下电SOC阈值
    END
    [Teardown]    设置web设备参数量为默认值    负载一次下电SOC阈值    负载二次下电SOC阈值    电池下电SOC阈值    测试终止SOC阈值    测试失败SOC阈值    铅酸类型    电池配置

负载二次下电时间设置测试
    [Documentation]    普通铅酸：38~49V，默认44V；
    ...    铅酸25: 38~51V，默认45.8V
    ...    铁锂：42~58V，默认46V；
    ...    小于等于电池电压低阈值-1、测试终止电压-1
    [Tags]    PMSA-NTest    Restr
    [Setup]    判断web参数是否存在    下电模式
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    纯铅酸
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    铅酸类型
    ${缺省值}    获取web参数上下限范围    铅酸类型
    ${取值约定dict}    获取web参数的取值约定    铅酸类型
    ${取值约定values}    get dictionary values    ${取值约定dict}
    ${val}    Get From Dictionary    ${取值约定dict}    ${缺省值}[0]
    FOR    ${参数设置}    IN    @{取值约定values}
        Wait Until Keyword Succeeds    10    2    设置web参数量    铅酸类型    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    铅酸类型
        should be equal    ${参数获取}    ${参数设置}
        设置下电模式/使能    停电时间    负载二次下电使能    负载二次下电时间
        数值类参数设置最大值最小值默认值    负载二次下电时间
    END
    [Teardown]    设置web设备参数量为默认值    负载二次下电时间    负载二次下电使能    下电模式    铅酸类型    电池配置

电池下电SOC阈值设置测试
    [Documentation]    10~80，默认10；
    ...    小于测试终止SOC阈值-1
    [Tags]    PMSA-NTest    Restr
    [Setup]    判断web参数是否存在    下电模式
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流输入场景    油电    #油电场景测试
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    铅酸&锂电混用
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    铅酸类型
    ${缺省值}    获取web参数上下限范围    铅酸类型
    ${取值约定dict}    获取web参数的取值约定    铅酸类型
    ${取值约定values}    get dictionary values    ${取值约定dict}
    ${val}    Get From Dictionary    ${取值约定dict}    ${缺省值}[0]
    FOR    ${参数设置}    IN    @{取值约定values}    ${val}
        Wait Until Keyword Succeeds    10    2    设置web参数量    铅酸类型    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    铅酸类型
        should be equal    ${参数获取}    ${参数设置}
        设置下电模式/使能    电池剩余容量    电池下电使能    电池下电SOC阈值
        数值类参数设置最大值最小值默认值    电池下电SOC阈值
    END
    [Teardown]    设置web设备参数量为默认值    负载一次下电SOC阈值    负载二次下电SOC阈值    电池下电SOC阈值    测试终止SOC阈值    测试失败SOC阈值    铅酸类型    电池配置

电池下电时间设置测试
    [Tags]    PMSA-NTest    Restr
    [Setup]    判断web参数是否存在    下电模式
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    纯铅酸
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    铅酸类型
    ${缺省值}    获取web参数上下限范围    铅酸类型
    ${取值约定dict}    获取web参数的取值约定    铅酸类型
    ${取值约定values}    get dictionary values    ${取值约定dict}
    ${val}    Get From Dictionary    ${取值约定dict}    ${缺省值}[0]
    FOR    ${参数设置}    IN    @{取值约定values}
        Wait Until Keyword Succeeds    10    2    设置web参数量    铅酸类型    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    铅酸类型
        should be equal    ${参数获取}    ${参数设置}
        设置下电模式/使能    停电时间    电池下电使能    电池下电时间
        数值类参数设置最大值最小值默认值    电池下电时间
    END
    [Teardown]    设置web设备参数量为默认值    电池下电时间    电池下电使能    下电模式    铅酸类型    电池配置

整流器轮换周期设置测试
    [Tags]    PMSA-NTest    Restr
    [Setup]    run keywords    测试用例前置条件
    ...    AND    判断web参数是否存在    交流节能模式
    ...    AND    Wait Until Keyword Succeeds    10    2    设置web参数量    交流节能模式    节能
    ...    AND    判断web参数是否存在    整流器轮换周期
    数值类参数设置最大值最小值默认值    整流器轮换周期
    [Teardown]    设置web设备参数量为默认值    整流器轮换周期    交流节能模式

暂时非节能延时时间设置测试
    [Tags]    PMSA-NTest    Restr
    [Setup]    run keywords    测试用例前置条件
    ...    AND    判断web参数是否存在    交流节能模式
    ...    AND    Wait Until Keyword Succeeds    10    2    设置web参数量    交流节能模式    节能
    ...    AND    判断web参数是否存在    暂时非节能延时时间
    数值类参数设置最大值最小值默认值    暂时非节能延时时间
    [Teardown]    设置web设备参数量为默认值    暂时非节能延时时间    交流节能模式

充电电压设置测试
    [Tags]    PMSA-NTest    Restr
    [Setup]    Run keywords    判断web参数是否存在    电池配置
    ...    AND    设置web参数量    电池配置    智能锂电
    ...    AND    判断web参数是否存在    充电电压
    数值类参数设置最大值最小值默认值    充电电压
    [Teardown]    设置web设备参数量为默认值    充电电压    电池配置


*** Keywords ***
 
设置下电模式/使能
    [Arguments]    ${下电模式}    ${下电使能名称}    ${下电阈值名称}
    设置web参数量    下电模式    ${下电模式}
    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    负载一次下电使能
    设置web参数量    负载一次下电使能    允许
    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    ${下电阈值名称}