*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
整流器1关机状态获取测试
    [Setup]    测试用例前置条件
    关闭交流源输出
    设置电池模拟器模式    铅酸电池    24    5    50
    电池模拟器接触器分合    ON
    打开电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器关机状态-1    否    #为保证整流器数据获取正常
    ${整流器1关机状态}    获取web实时数据    整流器关机状态-1
    log    ${整流器1关机状态}
    should be equal    '${整流器1关机状态}'    '否'
    关闭交流源输出
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器关机状态-1    是    #为保证整流器数据获取正常
    ${整流器1关机状态}    获取web实时数据    整流器关机状态-1
    log    ${整流器1关机状态}
    should be equal    '${整流器1关机状态}'    '是'
    关闭电池模拟器输出

整流器2关机状态获取测试
    [Setup]    测试用例前置条件
    关闭交流源输出
    设置电池模拟器模式    铅酸电池    24    5    50
    电池模拟器接触器分合    ON
    打开电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器关机状态-2    否    #为保证整流器数据获取正常
    ${整流器2关机状态}    获取web实时数据    整流器关机状态-2
    log    ${整流器2关机状态}
    should be equal    '${整流器2关机状态}'    '否'
    关闭交流源输出
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器关机状态-2    是    #为保证整流器数据获取正常
    ${整流器2关机状态}    获取web实时数据    整流器关机状态-2
    log    ${整流器2关机状态}
    should be equal    '${整流器2关机状态}'    '是'
    关闭电池模拟器输出

整流器1限流状态获取测试
    [Documentation]    测试台空开太小，电流大了，空开保护，待问题解决后测试
    [Tags]
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    过渡
    设置web参数量    浮充电压    48
    设置web控制量    启动浮充
    设置web参数量    电池组容量_1    0
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    整流器设定限流点比率    960
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    150    5
    打开负载输出
    sleep    5m
    #增大负载到限流
    FOR    ${i}    IN RANGE    1    100
        ${电流1}    evaluate    50+3*${i}
        ${当前负载电压电流}    查询负载电压电流
        ${电压1}    获取web实时数据    直流电压
        Comment    exit for loop if    ${电流1}>299    #电子负载电流不能超过300A
        sleep    30
        ${状态}    获取web实时数据    整流器限流状态-1
        exit for loop if    '${状态}'=='是'
        设置负载电压电流    ${电压1}    ${电流1}
    END
    sleep    5
    FOR    ${i}    IN    1    2
        ${状态}    获取web实时数据    整流器输出限功率状态-${i}
        should be equal    '${状态}'    '是'
    END
    ${整流器输出电压}    获取web实时数据    整流器输出电压-1
    [Teardown]    关闭负载输出

整流器2限流状态获取测试
    [Documentation]    测试台空开太小，电流大了，空开保护，待问题解决后测试
    [Tags]
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    过渡
    设置web参数量    浮充电压    48
    设置web控制量    启动浮充
    设置web参数量    电池组容量_1    0
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    整流器设定限流点比率    960
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    150    5
    打开负载输出
    sleep    10
    #增大负载到限流
    FOR    ${i}    IN RANGE    1    100
        ${电流1}    evaluate    50+3*${i}
        ${当前负载电压电流}    查询负载电压电流
        ${电压1}    获取web实时数据    直流电压
        Comment    exit for loop if    ${电流1}>299    #电子负载电流不能超过300A
        sleep    8
        ${状态}    获取web实时数据    整流器限流状态-2
        exit for loop if    '${状态}'=='是'
        设置负载电压电流    ${电压1}    ${电流1}
    END
    sleep    5
    FOR    ${i}    IN    1    2
        ${状态}    获取web实时数据    整流器输出限功率状态-${i}
        should be equal    '${状态}'    '是'
    END
    ${整流器输出电压}    获取web实时数据    整流器输出电压-2
    [Teardown]    关闭负载输出

# 整流器2限流状态获取测试
#     [Documentation]    测试台空开太小，电流大了，空开保护，待问题解决后测试
#     [Tags]
#     [Setup]    测试用例前置条件
#     仅有市电条件上电
#     连接CSU
#     Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
#     Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    过渡
#     设置web参数量    浮充电压    48
#     设置web控制量    启动浮充
#     设置web参数量    电池组容量_1    0
#     Wait Until Keyword Succeeds    5m    2    信号量数据值大于    整流器设定限流点比率    960
#     设置负载电压电流    48    10
#     打开负载输出
#     sleep    10
#     ${整流器2限流状态}    获取web实时数据    整流器限流状态-2
#     log    ${整流器2限流状态}
#     # should be equal    ${整流器2限流状态}    0
#     should be equal    '${整流器2限流状态}'    '否'
#     ${工作整流器数量}    获取web实时数据    工作整流器数量
#     设置负载电压电流    48    80
#     sleep    5
#     设置负载电压电流    48    150
#     sleep    5
#     限制输出电压    47
#     sleep    5m
#     ${整流器2限流状态}    获取web实时数据    整流器限流状态-2
#     log    ${整流器2限流状态}
#     设置web参数量    电池组容量_1    100
#     should be equal    '${整流器2限流状态}'    '是'
#     [Teardown]    关闭负载输出

整流器输入限功率状态获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    ${整流器1输入限功率}    获取web实时数据    整流器输入限功率-1
    log    ${整流器1输入限功率}
    should be equal    '${整流器1输入限功率}'    '否'
    ${整流器2输入限功率}    获取web实时数据    整流器输入限功率-2
    log    ${整流器2输入限功率}
    should be equal    '${整流器2输入限功率}'    '否'

整流器温度限功率获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    ${整流器1温度限功率}    获取web实时数据    整流器温度限功率-1
    log    ${整流器1温度限功率}
    should be equal    '${整流器1温度限功率}'    '否'
    ${整流器2温度限功率}    获取web实时数据    整流器温度限功率-2
    log    ${整流器2温度限功率}
    should be equal    '${整流器2温度限功率}'    '否'

整流器1休眠状态获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    设置web参数量    交流节能模式    自由
    sleep    5
    设置web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-1    是
    ${整流器1休眠状态}    获取web实时数据    整流器休眠状态-1
    log    ${整流器1休眠状态}
    should be equal    '${整流器1休眠状态}'    '是'
    设置web控制量    整流器唤醒-1
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-1    否
    ${整流器1休眠状态}    获取web实时数据    整流器休眠状态-1
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    2    #为保证整流器数据获取正常
    should be equal    '${整流器1休眠状态}'    '否'
    [Teardown]    设置web参数量    交流节能模式    安全

整流器2休眠状态获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    设置web参数量    交流节能模式    自由
    sleep    5
    设置web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-2    是
    ${整流器2休眠状态}    获取web实时数据    整流器休眠状态-2
    log    ${整流器2休眠状态}
    should be equal    '${整流器2休眠状态}'    '是'
    设置web控制量    整流器唤醒-2
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-2    否
    ${整流器2休眠状态}    获取web实时数据    整流器休眠状态-2
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    2    #为保证整流器数据获取正常
    should be equal    '${整流器2休眠状态}'    '否'
    [Teardown]    设置web参数量    交流节能模式    安全

整流器1一键功能状态获取测试
    [Documentation]    控制量中无整流器进入一键功能控制项
    [Tags]    no test
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    设置web控制量    整流器进入一键功能-1
    sleep    5
    ${整流器1一键功能状态}    获取web实时数据    整流器一键功能状态-1
    log    ${整流器1一键功能状态}
    should be equal    '${整流器1一键功能状态}'    '是'
    设置web控制量    整流器退出一键功能-1
    sleep    5
    ${整流器1一键功能状态}    获取web实时数据    整流器一键功能状态-1
    log    ${整流器1一键功能状态}
    should be equal    '${整流器1一键功能状态}'    '否'

整流器2一键功能状态获取测试
    [Tags]    no test
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    设置web控制量    整流器进入一键功能-2
    sleep    5
    ${整流器2一键功能状态}    获取web实时数据    整流器一键功能状态-2
    log    ${整流器2一键功能状态}
    should be equal    '${整流器2一键功能状态}'    '是'
    设置web控制量    整流器退出一键功能-2
    sleep    5
    ${整流器2一键功能状态}    获取web实时数据    整流器一键功能状态-2
    log    ${整流器2一键功能状态}
    should be equal    '${整流器2一键功能状态}'    '否'

整流器闭锁状态获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    ${整流器1闭锁状态}    获取web实时数据    整流器闭锁状态-1
    log    ${整流器1闭锁状态}
    should be equal    '${整流器1闭锁状态}'    '否'
    ${整流器2闭锁状态}    获取web实时数据    整流器闭锁状态-2
    log    ${整流器2闭锁状态}
    should be equal    '${整流器2闭锁状态}'    '否'

整流器1输出限功率状态获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    过渡
    设置web参数量    浮充电压    53.5
    Wait Until Keyword Succeeds    20    2    设置web控制量    启动浮充
    设置web参数量    电池组容量_1    1000
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    电池电压-1    53
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    整流器设定限流点比率    960
    ${电压}    获取web实时数据    直流电压
    ${电流}    evaluate    2700/${电压}*3    #一个单体2880w，输出限功率状态：最大功率，48V以上
    缓慢设置负载电压电流    ${电压}    ${电流}    5
    sleep    10
    #增大负载到限功率
    FOR    ${i}    IN RANGE    1    100
        ${电流1}    evaluate    ${电流}+3*${i}
        ${当前负载电压电流}    查询负载电压电流
        ${电压1}    获取web实时数据    直流电压
        exit for loop if    ${电流1}>299    #电子负载电流不能超过300A
        sleep    8
        ${状态}    获取web实时数据    整流器输出限功率状态-1
        exit for loop if    '${状态}'=='是'
        设置负载电压电流    ${电压1}    ${电流1}
    END
    sleep    5
    FOR    ${i}    IN    1    2
        ${状态}    获取web实时数据    整流器输出限功率状态-${i}
        should be equal    '${状态}'    '是'
    END
    ${整流器输出电压}    获取web实时数据    整流器输出电压-1
    should be true    ${整流器输出电压}>=48
    [Teardown]    设置web参数量    电池组容量_1    1000

整流器2输出限功率状态获取测试
    [Documentation]    限功率状态时有时无改为循环判断
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    过渡
    设置web参数量    浮充电压    53.5
    Wait Until Keyword Succeeds    20    2    设置web控制量    启动浮充
    设置web参数量    电池组容量_1    1000
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    电池电压-1    53
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    整流器设定限流点比率    960
    ${电压}    获取web实时数据    直流电压
    ${电流}    evaluate    2700/${电压}*3    #一个单体2880w，输出限功率状态：最大功率，48V以上
    缓慢设置负载电压电流    ${电压}    ${电流}    5
    sleep    10
    #增大负载到限功率
    FOR    ${i}    IN RANGE    1    100
        ${电流1}    evaluate    ${电流}+3*${i}
        ${当前负载电压电流}    查询负载电压电流
        ${电压1}    获取web实时数据    直流电压
        exit for loop if    ${电流1}>299    #电子负载电流不能超过300A
        sleep    8
        ${状态}    获取web实时数据    整流器输出限功率状态-2
        exit for loop if    '${状态}'=='是'
        设置负载电压电流    ${电压1}    ${电流1}
    END
    sleep    5
    FOR    ${i}    IN    1    2
        ${状态}    获取web实时数据    整流器输出限功率状态-${i}
        should be equal    '${状态}'    '是'
    END
    ${整流器输出电压}    获取web实时数据    整流器输出电压-2
    should be true    ${整流器输出电压}>=48
    [Teardown]    设置web参数量    电池组容量_1    1000

整流器交流输入相位获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    ${整流器1交流输入相位}    获取web实时数据    整流器交流输入相位-1
    log    ${整流器1交流输入相位}
    ${整流器2交流输入相位}    获取web实时数据    整流器交流输入相位-2
    log    ${整流器2交流输入相位}
    ${整流器3交流输入相位}    获取web实时数据    整流器交流输入相位-3
    log    ${整流器3交流输入相位}
    should be equal    '${整流器1交流输入相位}'    'L1'
    should be equal    '${整流器2交流输入相位}'    'L2'
    should be equal    '${整流器3交流输入相位}'    'L3'

整流器1风扇控制状态获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    设置web控制量    整流器风扇调速禁止-1
    sleep    5
    ${整流器1风扇控制状态}    获取web实时数据    整流器风扇控制状态-1
    log    ${整流器1风扇控制状态}
    should be equal    '${整流器1风扇控制状态}'    '全速'
    设置web控制量    整流器风扇调速允许-1
    sleep    5
    ${整流器1风扇控制状态}    获取web实时数据    整流器风扇控制状态-1
    log    ${整流器1风扇控制状态}
    should be equal    '${整流器1风扇控制状态}'    '自动'

整流器2风扇控制状态获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    设置web控制量    整流器风扇调速禁止-2
    sleep    5
    ${整流器2风扇控制状态}    获取web实时数据    整流器风扇控制状态-2
    log    ${整流器2风扇控制状态}
    should be equal    '${整流器2风扇控制状态}'    '全速'
    设置web控制量    整流器风扇调速允许-2
    sleep    5
    ${整流器2风扇控制状态}    获取web实时数据    整流器风扇控制状态-2
    log    ${整流器2风扇控制状态}
    should be equal    '${整流器2风扇控制状态}'    '自动'

整流器升级使能状态获取测试
    [Tags]    notest
    [Documentation]    模拟子工具没有“整流器升级使能状态”数据
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    设置子工具值    SMR    1    数字量    整流器升级使能状态    1
    Wait Until Keyword Succeeds    10m   5    设备信息获取值    整流器升级使能状态-1        允许
    # ${整流器1升级使能状态}    获取web实时数据    整流器升级使能状态-1
    # log    ${整流器1升级使能状态}
    # should be equal    '${整流器1升级使能状态}'    '允许'
    设置子工具值    SMR    2    数字量    整流器升级使能状态    1
    Wait Until Keyword Succeeds    10m   5    设备信息获取值    整流器升级使能状态-2        允许
    # ${整流器2升级使能状态}    获取web实时数据    整流器升级使能状态-2
    # log    ${整流器2升级使能状态}
    # should be equal    '${整流器2升级使能状态}'    '允许'

整流器1工作状态获取测试
    [Setup]    测试用例前置条件
    关闭交流源输出
    设置电池模拟器模式    铅酸电池    24    5    50
    电池模拟器接触器分合    ON
    打开电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    ${整流器1工作状态}    获取web实时数据    整流器工作状态-1
    log    ${整流器1工作状态}
    should be equal    '${整流器1工作状态}'    '正常'
    同时设置三相电压频率    65    50
    Wait Until Keyword Succeeds    15m    2    信号量数据值为    整流器工作状态-1    告警
    ${整流器1工作状态}    获取web实时数据    整流器工作状态-1
    log    ${整流器1工作状态}
    should be equal    '${整流器1工作状态}'    '告警'
    关闭电池模拟器输出
    [Teardown]    同时设置三相电压频率    220    50

整流器2工作状态获取测试
    [Setup]    测试用例前置条件
    关闭交流源输出
    设置电池模拟器模式    铅酸电池    24    5    50
    电池模拟器接触器分合    ON
    打开电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    ${整流器2工作状态}    获取web实时数据    整流器工作状态-2
    log    ${整流器2工作状态}
    should be equal    '${整流器2工作状态}'    '正常'
    同时设置三相电压频率    65    50
    Wait Until Keyword Succeeds    20m    2    信号量数据值为    整流器工作状态-2    告警
    ${整流器2工作状态}    获取web实时数据    整流器工作状态-2
    log    ${整流器2工作状态}
    should be equal    '${整流器2工作状态}'    '告警'
    关闭电池模拟器输出
    [Teardown]    同时设置三相电压频率    220    50

整流器风扇故障状态获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    ${整流器1风扇故障状态}    获取web实时数据    整流器风扇故障状态-1
    log    ${整流器1风扇故障状态}
    should be equal    '${整流器1风扇故障状态}'    '正常'
    ${整流器2风扇故障状态}    获取web实时数据    整流器风扇故障状态-2
    log    ${整流器2风扇故障状态}
    should be equal    '${整流器2风扇故障状态}'    '正常'

整流器通讯状态获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    ${整流器1通讯状态}    获取web实时数据    整流器通讯状态-1
    log    ${整流器1通讯状态}
    should be equal    '${整流器1通讯状态}'    '正常'
    ${整流器2通讯状态}    获取web实时数据    整流器通讯状态-2
    log    ${整流器2通讯状态}
    should be equal    '${整流器2通讯状态}'    '正常'

整流器散热器过温关机状态获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    ${整流器1散热器过温关机状态}    获取web实时数据    整流器散热器过温关机状态-1
    log    ${整流器1散热器过温关机状态}
    should be equal    '${整流器1散热器过温关机状态}'    '正常'
    ${整流器2散热器过温关机状态}    获取web实时数据    整流器散热器过温关机状态-2
    log    ${整流器2散热器过温关机状态}
    should be equal    '${整流器2散热器过温关机状态}'    '正常'

整流器交流过压关机状态获取测试
    [Documentation]    交流源最高电压300V，整流器过压保护点大于300V，没法产生告警，无法测试
    [Tags]    no test
    [Setup]    测试用例前置条件
    关闭交流源输出
    设置电池模拟器模式    铅酸电池    24    5    50
    电池模拟器接触器分合    ON
    打开电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    ${整流器1交流过压关机状态 }    获取web实时数据    整流器交流过压关机状态-1
    log    ${整流器1交流过压关机状态 }
    should be equal    '${整流器1交流过压关机状态 }'    '正常'
    ${整流器2交流过压关机状态 }    获取web实时数据    整流器交流过压关机状态-2
    log    ${整流器2交流过压关机状态 }
    should be equal    '${整流器2交流过压关机状态 }'    '正常'
    关闭电池模拟器输出

整流器1交流欠压关机状态获取测试
    [Setup]    测试用例前置条件
    关闭交流源输出
    设置电池模拟器模式    铅酸电池    24    5    50
    电池模拟器接触器分合    ON
    打开电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    连接CSU
    Wait Until Keyword Succeeds    15m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    Wait Until Keyword Succeeds    15m    2    信号量数据值为    整流器交流欠压关机状态-1    正常    #为保证整流器数据获取正常
    ${整流器1交流欠压关机状态 }    获取web实时数据    整流器交流欠压关机状态-1
    log    ${整流器1交流欠压关机状态 }
    should be equal    '${整流器1交流欠压关机状态 }'    '正常'
    同时设置三相电压频率    65    50
    Wait Until Keyword Succeeds    15m    2    信号量数据值为    整流器交流欠压关机状态-1    异常
    ${整流器1交流欠压关机状态 }    获取web实时数据    整流器交流欠压关机状态-1
    log    ${整流器1交流欠压关机状态 }
    should be equal    '${整流器1交流欠压关机状态 }'    '异常'
    关闭电池模拟器输出
    [Teardown]    同时设置三相电压频率    220    50

整流器2交流欠压关机状态获取测试
    [Setup]    测试用例前置条件
    关闭交流源输出
    设置电池模拟器模式    铅酸电池    24    5    50
    电池模拟器接触器分合    ON
    打开电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    连接CSU
    Wait Until Keyword Succeeds    15m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    Wait Until Keyword Succeeds    15m    2    信号量数据值为    整流器交流欠压关机状态-2    正常    #为保证整流器数据获取正常
    ${整流器1工作状态}    获取web实时数据    整流器交流欠压关机状态-2
    log    ${整流器1工作状态}
    should be equal    '${整流器1工作状态}'    '正常'
    同时设置三相电压频率    65    50
    Wait Until Keyword Succeeds    15m    2    信号量数据值为    整流器交流欠压关机状态-2    异常
    ${整流器1工作状态}    获取web实时数据    整流器交流欠压关机状态-2
    log    ${整流器1工作状态}
    should be equal    '${整流器1工作状态}'    '异常'
    关闭电池模拟器输出
    [Teardown]    同时设置三相电压频率    220    50

整流器输出过流状态获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    ${整流器1输出过流状态}    获取web实时数据    整流器输出过流状态-1
    log    ${整流器1输出过流状态}
    should be equal    '${整流器1输出过流状态}'    '正常'
    ${整流器2输出过流状态}    获取web实时数据    整流器输出过流状态-2
    log    ${整流器2输出过流状态}
    should be equal    '${整流器2输出过流状态}'    '正常'

整流器PFC故障状态获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    ${整流器1PFC故障状态}    获取web实时数据    整流器PFC故障状态-1
    log    ${整流器1PFC故障状态}
    should be equal    '${整流器1PFC故障状态}'    '正常'
    ${整流器2PFC故障状态}    获取web实时数据    整流器PFC故障状态-2
    log    ${整流器2PFC故障状态}
    should be equal    '${整流器2PFC故障状态}'    '正常'

整流器机内温度过高状态获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    ${整流器1机内温度过高状态}    获取web实时数据    整流器机内温度过高状态-1
    log    ${整流器1机内温度过高状态}
    should be equal    '${整流器1机内温度过高状态}'    '正常'
    ${整流器2机内温度过高状态}    获取web实时数据    整流器机内温度过高状态-2
    log    ${整流器2机内温度过高状态}
    should be equal    '${整流器2机内温度过高状态}'    '正常'

整流器输出熔丝断状态获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    ${整流器1输出熔丝断状态}    获取web实时数据    整流器输出熔丝断状态-1
    log    ${整流器1输出熔丝断状态}
    should be equal    '${整流器1输出熔丝断状态}'    '正常'
    ${整流器2输出熔丝断状态}    获取web实时数据    整流器输出熔丝断状态-2
    log    ${整流器2输出熔丝断状态}
    should be equal    '${整流器2输出熔丝断状态}'    '正常'

整流器均流不良状态获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-2    否    #为保证整流器数据获取正常
    ${整流器1均流不良状态}    获取web实时数据    整流器均流不良状态-1
    log    ${整流器1均流不良状态}
    should be equal    '${整流器1均流不良状态}'    '正常'
    ${整流器2均流不良状态}    获取web实时数据    整流器均流不良状态-2
    log    ${整流器2均流不良状态}
    should be equal    '${整流器2均流不良状态}'    '正常'

整流器交流输入断状态获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    ${整流器1交流输入断状态}    获取web实时数据    整流器交流输入断状态-1
    log    ${整流器1交流输入断状态}
    should be equal    '${整流器1交流输入断状态}'    '正常'
    ${整流器2交流输入断状态}    获取web实时数据    整流器交流输入断状态-2
    log    ${整流器2交流输入断状态}
    should be equal    '${整流器2交流输入断状态}'    '正常'

整流器PFC输出过压告警状态获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    ${整流器1PFC输出过压告警状态}    获取web实时数据    整流器PFC输出过压告警状态-1
    log    ${整流器1PFC输出过压告警状态}
    should be equal    '${整流器1PFC输出过压告警状态}'    '正常'
    ${整流器2PFC输出过压告警状态}    获取web实时数据    整流器PFC输出过压告警状态-2
    log    ${整流器2PFC输出过压告警状态}
    should be equal    '${整流器2PFC输出过压告警状态}'    '正常'

整流器PFC输出欠压告警状态获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    ${整流器1PFC输出欠压告警状态}    获取web实时数据    整流器PFC输出欠压告警状态-1
    log    ${整流器1PFC输出欠压告警状态}
    should be equal    '${整流器1PFC输出欠压告警状态}'    '正常'
    ${整流器2PFC输出欠压告警状态}    获取web实时数据    整流器PFC输出欠压告警状态-2
    log    ${整流器2PFC输出欠压告警状态}
    should be equal    '${整流器2PFC输出欠压告警状态}'    '正常'

整流器EEPROM故障状态获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    ${整流器1EEPROM故障状态}    获取web实时数据    整流器EEPROM故障状态-1
    log    ${整流器1EEPROM故障状态}
    should be equal    '${整流器1EEPROM故障状态}'    '正常'
    ${整流器2EEPROM故障状态}    获取web实时数据    整流器EEPROM故障状态-2
    log    ${整流器2EEPROM故障状态}
    should be equal    '${整流器2EEPROM故障状态}'    '正常'

整流器机内通讯断状态获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    ${整流器1机内通讯断状态}    获取web实时数据    整流器机内通讯断状态-1
    log    ${整流器1机内通讯断状态}
    should be equal    '${整流器1机内通讯断状态}'    '正常'
    ${整流器2机内通讯断状态}    获取web实时数据    整流器机内通讯断状态-2
    log    ${整流器2机内通讯断状态}
    should be equal    '${整流器2机内通讯断状态}'    '正常'

整流器原边过流状态获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    ${整流器1原边过流状态}    获取web实时数据    整流器原边过流状态-1
    log    ${整流器1原边过流状态}
    should be equal    '${整流器1原边过流状态}'    '正常'
    ${整流器2原边过流状态}    获取web实时数据    整流器原边过流状态-2
    log    ${整流器2原边过流状态}
    should be equal    '${整流器2原边过流状态}'    '正常'

整流器PFC输入过流状态获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    ${整流器1PFC输入过流状态}    获取web实时数据    整流器PFC输入过流状态-1
    log    ${整流器1PFC输入过流状态}
    should be equal    '${整流器1PFC输入过流状态}'    '正常'
    ${整流器2PFC输入过流状态}    获取web实时数据    整流器PFC输入过流状态-2
    log    ${整流器2PFC输入过流状态}
    should be equal    '${整流器2PFC输入过流状态}'    '正常'

整流器缓启动异常状态获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    ${整流器1缓启动异常状态}    获取web实时数据    整流器缓启动异常状态-1
    log    ${整流器1缓启动异常状态}
    should be equal    '${整流器1缓启动异常状态}'    '正常'
    ${整流器2缓启动异常状态}    获取web实时数据    整流器缓启动异常状态-2
    log    ${整流器2缓启动异常状态}
    should be equal    '${整流器2缓启动异常状态}'    '正常'

整流器输入熔丝断状态获取测试
    [Tags]    view
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    ${整流器1输入熔丝断状态}    获取web实时数据    整流器输入熔丝断状态-1
    log    ${整流器1输入熔丝断状态}
    should be equal    '${整流器1输入熔丝断状态}'    '正常'
    ${整流器2输入熔丝断状态}    获取web实时数据    整流器输入熔丝断状态-2
    log    ${整流器2输入熔丝断状态}
    should be equal    '${整流器2输入熔丝断状态}'    '正常'

整流器输入频率异常状态获取测试
    [Documentation]    修改告警判断值为77
    [Setup]    测试用例前置条件
    关闭交流源输出
    设置电池模拟器模式    铅酸电池    24    5    50
    电池模拟器接触器分合    ON
    打开电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器交流欠压关机状态-1    正常    #为保证整流器数据获取正常
    ${整流器1输入频率异常状态}    获取web实时数据    整流器输入频率异常状态-1
    log    ${整流器1输入频率异常状态}
    should be equal    '${整流器1输入频率异常状态}'    '正常'
    ${整流器2输入频率异常状态}    获取web实时数据    整流器输入频率异常状态-2
    log    ${整流器2输入频率异常状态}
    should be equal    '${整流器2输入频率异常状态}'    '正常'
    同时设置三相电压频率    220    77
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入频率异常状态-1    异常    #为保证整流器数据获取正常
    ${整流器1输入频率异常状态}    获取web实时数据    整流器输入频率异常状态-1
    log    ${整流器1输入频率异常状态}
    should be equal    '${整流器1输入频率异常状态}'    '异常'
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入频率异常状态-2    异常    #为保证整流器数据获取正常
    ${整流器2输入频率异常状态}    获取web实时数据    整流器输入频率异常状态-2
    log    ${整流器2输入频率异常状态}
    should be equal    '${整流器2输入频率异常状态}'    '异常'
    同时设置三相电压频率    220    50
    关闭电池模拟器输出
    [Teardown]    同时设置三相电压频率    220    50

整流器输出欠压状态获取测试
    [Documentation]    无法让整流器输出保护
    [Tags]
    [Setup]    测试用例前置条件
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    15
    关闭电池模拟器输出
    设置web控制量    启动浮充
    sleep    5
    打开负载输出
    设置负载电压电流    53.5    100
    sleep    5
    ${整流器1输出欠压状态}    获取web实时数据    整流器输出欠压状态-1
    log    ${整流器1输出欠压状态}
    should be equal    '${整流器1输出欠压状态}'    '正常'
    ${整流器2输出欠压状态}    获取web实时数据    整流器输出欠压状态-2
    log    ${整流器2输出欠压状态}
    should be equal    '${整流器2输出欠压状态}'    '正常'

整流器输出过载状态获取测试
    [Tags]    no test
    [Setup]    测试用例前置条件
    打开交流源输出
    关闭电池模拟器输出
    设置web控制量    启动浮充
    sleep    5
    打开负载输出
    设置负载电压电流    53.5    100
    sleep    5
    ${整流器1输出过载状态}    获取web实时数据    整流器输出过载状态-1
    log    ${整流器1输出过载状态}
    should be equal    '${整流器1输出过载状态}'    '正常'
    ${整流器2输出过载状态}    获取web实时数据    整流器输出过载状态-2
    log    ${整流器2输出过载状态}
    should be equal    '${整流器2输出过载状态}'    '正常'

整流器序列号冲突状态获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    ${整流器1序列号冲突状态}    获取web实时数据    整流器序列号冲突状态-1
    log    ${整流器1序列号冲突状态}
    should be equal    '${整流器1序列号冲突状态}'    '正常'
    ${整流器2序列号冲突状态}    获取web实时数据    整流器序列号冲突状态-2
    log    ${整流器2序列号冲突状态}
    should be equal    '${整流器2序列号冲突状态}'    '正常'

整流器协议错误状态获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    ${整流器1协议错误状态}    获取web实时数据    整流器协议错误状态-1
    log    ${整流器1协议错误状态}
    should be equal    '${整流器1协议错误状态}'    '正常'
    ${整流器2协议错误状态}    获取web实时数据    整流器协议错误状态-2
    log    ${整流器2协议错误状态}
    should be equal    '${整流器2协议错误状态}'    '正常'

整流器机型不匹配状态获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    ${整流器1机型不匹配状态}    获取web实时数据    整流器机型不匹配状态-1
    log    ${整流器1机型不匹配状态}
    should be equal    '${整流器1机型不匹配状态}'    '正常'
    ${整流器2机型不匹配状态}    获取web实时数据    整流器机型不匹配状态-2
    log    ${整流器2机型不匹配状态}
    should be equal    '${整流器2机型不匹配状态}'    '正常'

整流器机外温度过高状态获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    ${整流器1机外温度过高状态}    获取web实时数据    整流器机外温度过高状态-1
    log    ${整流器1机外温度过高状态}
    should be equal    '${整流器1机外温度过高状态}'    '正常'
    ${整流器2机外温度过高状态}    获取web实时数据    整流器机外温度过高状态-2
    log    ${整流器2机外温度过高状态}
    should be equal    '${整流器2机外温度过高状态}'    '正常'

启动电阻过热告警状态获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    ${整流器1启动电阻过热告警状态}    获取web实时数据    启动电阻过热告警状态-1
    log    ${整流器1启动电阻过热告警状态}
    should be equal    '${整流器1启动电阻过热告警状态}'    '正常'
    ${整流器2启动电阻过热告警状态}    获取web实时数据    启动电阻过热告警状态-2
    log    ${整流器2启动电阻过热告警状态}
    should be equal    '${整流器2启动电阻过热告警状态}'    '正常'

整流器均流类型测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1    #为保证整流器数据获取正常
    ${整流器1均流类型}    获取web实时数据    整流器均流类型-1
    log    ${整流器1均流类型}
    should be equal    '${整流器1均流类型}'    '主从均流'
    ${整流器2均流类型}    获取web实时数据    整流器均流类型-2
    log    ${整流器2均流类型}
    should be equal    '${整流器2均流类型}'    '主从均流'
