*** Settings ***
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_获取PU模块模拟量
    [Tags]    3
    ${能源网管数据}    能源网管协议_获取数据    获取太阳能模块模拟量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取太阳能模块模拟量
    Should Be True    ${数据对比结果}
    ${校验结果}    能源网管协议_校验数据上送完整性    ${能源网管数据}    太阳能模块    analog data
    Comment     Should Be True    ${校验结果}

power_sm_0002_获取PU模块数字量
    [Documentation]    按位传输
    [Tags]    3
    ${能源网管数据}    能源网管协议_获取数据    获取太阳能模块数字量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取太阳能模块数字量
    Should Be True    ${数据对比结果}
    ${校验结果}    能源网管协议_校验数据上送完整性    ${能源网管数据}    太阳能模块    digital data
    Comment   Should Be True    ${校验结果}

power_sm_0003_获取PU模块设备信息
    ${能源网管数据}    能源网管协议_获取数据    获取太阳能模块设备信息    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取太阳能模块设备信息
    Should Be True    ${数据对比结果}
    ${校验结果}    能源网管协议_校验数据上送完整性    ${能源网管数据}    太阳能模块    device info
    Comment   Should Be True    ${校验结果}

power_sm_0004_设置PU模块控制量
    [Tags]    4
    Comment    ${能源网管数据}    能源网管协议_设置控制量    设置太阳能模块控制量    ${BOARD_IP}    ${SSH}
    Comment    ${对比结果}    能源网管协议_批量对比参数设置    ${能源网管数据}    设置太阳能模块控制量
    Comment    能源网管协议_设置单个设备单个控制量    3    PU风扇调速允许    ${BOARD_IP}    ${SSH}
    Comment    Should Be True    ${对比结果}
    @{PU随机list}    从1-15个数中随机选n个不重复的单体    ${北向协议PU最大数}    4
    FOR    ${PU序号}    IN    @{PU随机list}
        Wait Until Keyword Succeeds    2m    4    能源网管协议_设置单个设备单个控制量    太阳能模块_${PU序号}    3    PU休眠    ${SSH}
        Wait Until Keyword Succeeds    2m    2    信号量数据值为    PU休眠状态-${PU序号}    是
        Wait Until Keyword Succeeds    2m    4    能源网管协议_设置单个设备单个控制量    太阳能模块_${PU序号}    3    PU唤醒    ${SSH}
        Wait Until Keyword Succeeds    2m    2    信号量数据值为    PU休眠状态-${PU序号}    否
        Wait Until Keyword Succeeds    2m    4    能源网管协议_设置单个设备单个控制量    太阳能模块_${PU序号}    3    PU风扇调速禁止    ${SSH}
        Wait Until Keyword Succeeds    2m    2    信号量数据值为    PU风扇控制状态-${PU序号}    全速
        Wait Until Keyword Succeeds    2m    4    能源网管协议_设置单个设备单个控制量    太阳能模块_${PU序号}    3    PU风扇调速允许    ${SSH}
        Wait Until Keyword Succeeds    2m    2    信号量数据值为    PU风扇控制状态-${PU序号}    自动
        Wait Until Keyword Succeeds    2m    4    能源网管协议_设置单个设备单个控制量    太阳能模块_${PU序号}    3    PU通讯中断告警清除    ${SSH}
    END
