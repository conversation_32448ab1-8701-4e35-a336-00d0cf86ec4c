*** Settings ***
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
BMS设备统计测试
    连接CSU
    #子设备工具模拟
    控制子工具运行停止    smartli_fb100b1    关闭
    Wait Until Keyword Succeeds    18m    5    信号量数据值为    <<BMS通信断状态-1~0x17001020330001>>    异常
    Wait Until Keyword Succeeds    18m    5    信号量数据值为    <<BMS通信断状态-8~0x17001020330001>>    异常
    Wait Until Keyword Succeeds    18m    5    信号量数据值为    <<BMS通信断状态-${铁锂电池组数}~0x17001020330001>>    异常
    ${级别设置值}    获取web参数量    <<BMS通信断告警~0x170010302f0001>>
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    <<BMS通信断告警~0x170010302f0001>>    严重
    wait until keyword succeeds    5m    2    查询指定告警信息    BMS通信断告警
    wait until keyword succeeds    30m    60    获取指定告警数量大于    BMS通信断告警    30
    wait until keyword succeeds    1m    2    设置web控制量    RS485总线设备统计
    wait until keyword succeeds    23m    2    查询指定告警信息不为    BMS通信断告警
    [Teardown]    重新启动FB100B1
