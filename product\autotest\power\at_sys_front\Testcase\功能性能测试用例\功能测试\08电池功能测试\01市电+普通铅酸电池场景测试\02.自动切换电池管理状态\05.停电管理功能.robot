*** Settings ***
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
停电来电进入浮充
    
    电池管理初始化
    sleep    5
    ${均充阈值电压缺省值}    获取web参数上下限范围    均充阈值电压
    ${均充阈值电压可设置范围}    获取web参数可设置范围    均充阈值电压
    设置web参数量    均充阈值电压    ${均充阈值电压可设置范围}[0]
    ${均充阈值SOC缺省值}    获取web参数上下限范围    均充阈值SOC
    ${均充阈值SOC可设置范围}    获取web参数可设置范围    均充阈值SOC
    设置web参数量    均充阈值SOC    ${均充阈值SOC可设置范围}[0]
    ${均充阈值放电时间缺省值}    获取web参数上下限范围    均充阈值放电时间
    ${均充阈值放电时间可设置范围}    获取web参数可设置范围    均充阈值放电时间
    设置web参数量    均充阈值放电时间    ${均充阈值放电时间可设置范围}[1]
    设置web参数量    均充使能    允许
    #确定交流停电前为浮充状态
    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    浮充
    设置负载电压电流    53.5    10
    打开负载输出
    sleep    3
    关闭交流源输出
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    系统停电
    ${电池组当前容量比率}    获取web实时数据    电池组当前容量比率-1
    ${电池当前电压}    获取web实时数据    电池电压-1
    ${均充阈值SOC设置值}    获取web参数量    均充阈值SOC
    ${均充阈值电压设置值}    获取web参数量    均充阈值电压
    should be true    ${电池组当前容量比率}>${均充阈值SOC设置值}
    should be true    ${电池当前电压}>${均充阈值电压设置值}
    #判断电池电压、容量是否满足均充条件
    打开交流源输出
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    系统停电
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    过渡
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    浮充
    关闭负载输出
    [Teardown]    设置web设备参数量为默认值    均充阈值SOC    均充阈值电压    均充阈值放电时间

停电来电进入均充（电压）
    
    电池管理初始化
    ${均充阈值电压缺省值}    获取web参数上下限范围    均充阈值电压
    ${均充阈值电压可设置范围}    获取web参数可设置范围    均充阈值电压
    设置web参数量    均充阈值电压    ${均充阈值电压可设置范围}[1]
    ${均充阈值SOC缺省值}    获取web参数上下限范围    均充阈值SOC
    ${均充阈值SOC可设置范围}    获取web参数可设置范围    均充阈值SOC
    设置web参数量    均充阈值SOC    ${均充阈值SOC可设置范围}[0]
    ${均充阈值放电时间缺省值}    获取web参数上下限范围    均充阈值放电时间
    ${均充阈值放电时间可设置范围}    获取web参数可设置范围    均充阈值放电时间
    设置web参数量    均充阈值放电时间    ${均充阈值放电时间可设置范围}[1]
    设置web参数量    均充使能    允许
    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    浮充
    设置负载电压电流    53.5    10
    打开负载输出
    关闭交流源输出
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    系统停电
    #使电池电压满足均充阈值电压
    ${阈值电压设置值}    获取web参数量    均充阈值电压
    向下调节电池电压    ${阈值电压设置值}-0.5
    #确定电池容量不满足均充条件
    ${均充阈值SOC设置值}    获取web参数量    均充阈值SOC
    ${电池组当前容量比率}    获取web实时数据    电池组当前容量比率-1
    should be true    ${电池组当前容量比率}>${均充阈值SOC设置值}
    打开交流源输出
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    系统停电
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    过渡
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    均充
    关闭负载输出
    [Teardown]    Run keywords    设置web设备参数量为默认值    均充阈值SOC    均充阈值电压    均充阈值放电时间
    ...    AND    打开交流源输出
    ...    AND    关闭负载输出
    ...    AND    重置电池模拟器输出

停电来电进入均充（容量）
    
    电池管理初始化
    ${均充阈值电压缺省值}    获取web参数上下限范围    均充阈值电压
    ${均充阈值电压可设置范围}    获取web参数可设置范围    均充阈值电压
    设置web参数量    均充阈值电压    ${均充阈值电压可设置范围}[0]
    ${均充阈值SOC缺省值}    获取web参数上下限范围    均充阈值SOC
    ${均充阈值SOC可设置范围}    获取web参数可设置范围    均充阈值SOC
    设置web参数量    均充阈值SOC    ${均充阈值SOC可设置范围}[1]
    ${均充阈值放电时间缺省值}    获取web参数上下限范围    均充阈值放电时间
    ${均充阈值放电时间可设置范围}    获取web参数可设置范围    均充阈值放电时间
    设置web参数量    均充阈值放电时间    ${均充阈值放电时间可设置范围}[1]
    设置web参数量    均充使能    允许
    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    浮充
    设置负载电压电流    53.5    30
    打开负载输出
    关闭交流源输出
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    系统停电
    #使电池容量满足均充阈值容量
    ${均充阈值SOC设置值}    获取web参数量    均充阈值SOC
    FOR    ${i}    IN RANGE    50
        sleep    5
        ${电池组当前容量比率}    获取web实时数据    电池组当前容量比率-1
        exit for loop if    ${电池组当前容量比率}<${均充阈值SOC设置值}
    END
    #确定电池电压不满足均充条件
    ${均充阈值电压设置值}    获取web参数量    均充阈值电压
    ${电池当前电压}    获取web实时数据    电池电压-1
    should be true    ${电池当前电压}>${均充阈值电压设置值}
    打开交流源输出
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    系统停电
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    过渡
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    均充
    关闭负载输出
    [Teardown]    Run keywords    设置web设备参数量为默认值    均充阈值SOC    均充阈值电压    均充阈值放电时间
    ...    AND    打开交流源输出
    ...    AND    关闭负载输出

停电来电进入均充（时间）
    [Documentation]    失败，计时问题
    
    电池管理初始化
    ${均充阈值电压缺省值}    获取web参数上下限范围    均充阈值电压
    ${均充阈值电压可设置范围}    获取web参数可设置范围    均充阈值电压
    设置web参数量    均充阈值电压    ${均充阈值电压可设置范围}[0]
    ${均充阈值SOC缺省值}    获取web参数上下限范围    均充阈值SOC
    ${均充阈值SOC可设置范围}    获取web参数可设置范围    均充阈值SOC
    设置web参数量    均充阈值SOC    ${均充阈值SOC可设置范围}[0]
    ${均充阈值放电时间缺省值}    获取web参数上下限范围_有单位    均充阈值放电时间
    ${可设置范围}    获取web参数可设置范围    均充阈值放电时间
    ${均充阈值放电时间设置值}    run keyword if    '${均充阈值放电时间缺省值}[3]'== 'Hour'    evaluate    ${可设置范围}[0]+1
    ...    ELSE    evaluate    ${可设置范围}[0]+5
    ${均充阈值放电时间转换后}    run keyword if    '${均充阈值放电时间缺省值}[3]'== 'Hour'    evaluate    ${均充阈值放电时间设置值}*60
    ...    ELSE    set variable    ${均充阈值放电时间设置值}
    设置web参数量    均充阈值放电时间    ${均充阈值放电时间设置值}
    设置web参数量    均充使能    允许
    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    浮充
    设置负载电压电流    53.5    10
    打开负载输出
    sleep    3
    关闭交流源输出
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    系统停电
    sleep    ${均充阈值放电时间转换后}m
    ${电池组当前容量比率}    获取web实时数据    电池组当前容量比率-1
    ${电池当前电压}    获取web实时数据    电池电压-1
    ${均充阈值SOC设置值}    获取web参数量    均充阈值SOC
    ${均充阈值电压设置值}    获取web参数量    均充阈值电压
    #确定电池电压、容量不满足均充条件
    should be true    ${电池组当前容量比率}>${均充阈值SOC设置值}
    should be true    ${电池当前电压}>${均充阈值电压设置值}
    打开交流源输出
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    系统停电
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    均充
    关闭负载输出
    [Teardown]    Run keywords    设置web设备参数量为默认值    均充阈值SOC    均充阈值电压    均充阈值放电时间
    ...    AND    打开交流源输出
    ...    AND    关闭负载输出

停电前均充的来电管理
    [Documentation]    ==20190321-F：启动均充成功，但获取的电池状态仍然为浮充
    
    电池管理初始化
    ${均充阈值电压缺省值}    获取web参数上下限范围    均充阈值电压
    ${均充阈值电压可设置范围}    获取web参数可设置范围    均充阈值电压
    设置web参数量    均充阈值电压    ${均充阈值电压可设置范围}[0]
    ${均充阈值SOC缺省值}    获取web参数上下限范围    均充阈值SOC
    ${均充阈值SOC可设置范围}    获取web参数可设置范围    均充阈值SOC
    设置web参数量    均充阈值SOC    ${均充阈值SOC可设置范围}[0]
    ${均充阈值放电时间缺省值}    获取web参数上下限范围    均充阈值放电时间
    ${均充阈值放电时间可设置范围}    获取web参数可设置范围    均充阈值放电时间
    设置web参数量    均充阈值放电时间    ${均充阈值放电时间可设置范围}[1]
    设置web参数量    均充使能    允许
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动均充
    Wait Until Keyword Succeeds    2m    2    信号量数据值为    电池管理状态    均充
    #确定交流停电前为均充状态
    关闭交流源输出
    设置负载电压电流    53.5    10
    打开负载输出
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    系统停电
    ${电池组当前容量比率}    获取web实时数据    电池组当前容量比率-1
    ${电池当前电压}    获取web实时数据    电池电压-1
    ${均充阈值SOC设置值}    获取web参数量    均充阈值SOC
    ${均充阈值电压设置值}    获取web参数量    均充阈值电压
    #确定电池电压、容量不满足均充条件
    should be true    ${电池组当前容量比率}>${均充阈值SOC设置值}
    should be true    ${电池当前电压}>${均充阈值电压设置值}
    打开交流源输出
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    系统停电
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    过渡
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    电池管理状态    均充
    关闭负载输出
    [Teardown]    Run keywords    设置web设备参数量为默认值    均充阈值SOC    均充阈值电压    均充阈值放电时间
    ...    AND    打开交流源输出
    ...    AND    关闭负载输出

停电来电的过渡阶段测试
    
    电池管理初始化
    #确定交流停电前为浮充状态
    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    2m    2    信号量数据值为    电池管理状态    浮充
    设置负载电压电流    53.5    10
    打开负载输出
    关闭交流源输出
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    系统停电
    打开交流源输出
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    过渡
    ${过渡开始时间}    get time
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    过渡
    ${过渡结束时间}    get time
    ${过渡时长}    Subtract Date From Date    ${过渡结束时间}    ${过渡开始时间}
    should be true    15<=${过渡时长}<=40
    关闭负载输出
    [Teardown]    run keywords    关闭负载输出
    ...    AND    打开交流源输出

放电状态下的设定输出电压
    [Documentation]    ==20190321-F：电压偏差不等
    
    电池管理初始化
    #确定交流停电前为浮充状态
    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    2m    2    信号量数据值为    电池管理状态    浮充
    设置负载电压电流    53.5    10
    打开负载输出
    关闭交流源输出
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    系统停电
    sleep    5m
    ${当前整流器输出设定电压}    获取web实时数据    整流器当前输出设定电压
    ${当前电池电压}    获取web实时数据    电池电压-1
    ${整流器设定电压}    Convert to Number    ${当前整流器输出设定电压}    1
    ${启调电压偏差}    获取web参数量    过渡阶段启调电压偏差
    ${电压偏差}    evaluate    ${整流器设定电压}-${当前电池电压}
    ${误差}    evaluate    ${启调电压偏差}-${电压偏差}
    should be true    -0.2<=${误差}<=0.2
    [Teardown]    run keywords    关闭负载输出
    ...    AND    打开交流源输出
