*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
交流电表在位状态
    连接CSU
    P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
    显示属性配置    交流电表在位状态    数字量    web_attr=On    gui_attr=On
    控制子工具运行停止    DMU_aemb    启动
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表在位状态-1    在位
    1104/1363_南向子设备模拟量/数字量获取    1    1363    <<获取交流电表数字量~CID1=D2H>>    <<获取交流电表数字量~CID2=43H>>    交流电表在位状态-1    FF
    1104/1363_南向子设备模拟量/数字量获取    1    1363    <<获取交流电表数字量~CID1=D2H>>    <<获取交流电表数字量~CID2=43H>>    交流电表在位状态-1    01
    控制子工具运行停止    DMU_aemb    关闭
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表在位状态-1    在位
    1104/1363_南向子设备模拟量/数字量获取    1    1363    <<获取交流电表数字量~CID1=D2H>>    <<获取交流电表数字量~CID2=43H>>    交流电表在位状态-1    FF
    1104/1363_南向子设备模拟量/数字量获取    1    1363    <<获取交流电表数字量~CID1=D2H>>    <<获取交流电表数字量~CID2=43H>>    交流电表在位状态-1    01
    [Teardown]    Run Keywords    控制子工具运行停止    DMU_aemb    启动
    ...    AND    sleep    3min

交流电表通讯状态
    [Setup]
    连接CSU
    P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
    显示属性配置    交流电表在位状态    数字量    web_attr=On    gui_attr=On
    控制子工具运行停止    DMU_aemb    启动
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表通讯状态-1    正常
    1104/1363_南向子设备模拟量/数字量获取    0    1363    <<获取交流电表数字量~CID1=D2H>>    <<获取交流电表数字量~CID2=43H>>    交流电表通讯状态-1    FF
    1104/1363_南向子设备模拟量/数字量获取    0    1363    <<获取交流电表数字量~CID1=D2H>>    <<获取交流电表数字量~CID2=43H>>    交流电表通讯状态-1    01
    控制子工具运行停止    DMU_aemb    关闭
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表通讯状态-1    异常
    1104/1363_南向子设备模拟量/数字量获取    1    1363    <<获取交流电表数字量~CID1=D2H>>    <<获取交流电表数字量~CID2=43H>>    交流电表通讯状态-1    FF
    1104/1363_南向子设备模拟量/数字量获取    1    1363    <<获取交流电表数字量~CID1=D2H>>    <<获取交流电表数字量~CID2=43H>>    交流电表通讯状态-1    FF
    [Teardown]    Run Keywords    控制子工具运行停止    DMU_aemb    启动
    ...    AND    sleep    3min

交流电表工作状态
    连接CSU
    P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
    显示属性配置    交流电表在位状态    数字量    web_attr=On    gui_attr=On
    控制子工具运行停止    DMU_aemb    启动
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表工作状态-1    正常
    1104/1363_南向子设备模拟量/数字量获取    1    1363    <<获取交流电表数字量~CID1=D2H>>    <<获取交流电表数字量~CID2=43H>>    交流电表工作状态-1    FF
    1104/1363_南向子设备模拟量/数字量获取    1    1363    <<获取交流电表数字量~CID1=D2H>>    <<获取交流电表数字量~CID2=43H>>    交流电表工作状态-1    01
    控制子工具运行停止    DMU_aemb    关闭
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表工作状态-1    通讯断
    1104/1363_南向子设备模拟量/数字量获取    3    1363    <<获取交流电表数字量~CID1=D2H>>    <<获取交流电表数字量~CID2=43H>>    交流电表工作状态-1    FF
    1104/1363_南向子设备模拟量/数字量获取    3    1363    <<获取交流电表数字量~CID1=D2H>>    <<获取交流电表数字量~CID2=43H>>    交流电表工作状态-1    01
    [Teardown]    Run Keywords    控制子工具运行停止    DMU_aemb    启动
    ...    AND    sleep    3min
