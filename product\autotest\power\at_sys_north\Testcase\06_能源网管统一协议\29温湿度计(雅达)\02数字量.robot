*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
获取温湿度传感器数字量
    连接CSU
    控制子工具运行停止    DMU_YD8779Y    关闭
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    温湿度传感器通讯状态    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    温湿度传感器在位状态    在位
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    温湿度传感器工作状态    通讯断
    ${power_sm获取值}    power_sm_获取一个数据的值    获取温湿度传感器数字量    温湿度传感器通讯状态    ${SSH}
    should be equal as strings    ${power_sm获取值}    1
    ${power_sm获取值}    power_sm_获取一个数据的值    获取温湿度传感器数字量    温湿度传感器在位状态    ${SSH}
    should be equal as strings    ${power_sm获取值}    1
    ${power_sm获取值}    power_sm_获取一个数据的值    获取温湿度传感器数字量    温湿度传感器工作状态    ${SSH}
    should be equal as strings    ${power_sm获取值}    3
    控制子工具运行停止    DMU_YD8779Y    开启
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    温湿度传感器通讯状态    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    温湿度传感器在位状态    在位
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    温湿度传感器工作状态    正常
    ${power_sm获取值}    power_sm_获取一个数据的值    获取温湿度传感器数字量    温湿度传感器通讯状态    ${SSH}
    should be equal as strings    ${power_sm获取值}    0
    ${power_sm获取值}    power_sm_获取一个数据的值    获取温湿度传感器数字量    温湿度传感器在位状态    ${SSH}
    should be equal as strings    ${power_sm获取值}    1
    ${power_sm获取值}    power_sm_获取一个数据的值    获取温湿度传感器数字量    温湿度传感器工作状态    ${SSH}
    should be equal as strings    ${power_sm获取值}    1
    [Teardown]    Run Keywords    控制子工具运行停止    DMU_YD8779Y    开启
    ...    AND    sleep    3min

获取扩展湿度传感器状态
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    扩展湿度传感器状态    正常
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取系统运行环境数字量    扩展湿度传感器状态    ${SSH}
    should be equal as strings    ${power_sm获取值1}    0
    设置子工具值    DMU_YD8779Y    all    只读    湿度    101
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    扩展湿度传感器状态    异常
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取系统运行环境数字量    扩展湿度传感器状态    ${SSH}
    should be equal as strings    ${power_sm获取值1}    1
    设置子工具值    DMU_YD8779Y    all    只读    湿度    60
    Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    湿度传感器    系统运行环境    无
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    扩展湿度传感器状态    未配置
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取系统运行环境数字量    扩展湿度传感器状态    ${SSH}
    should be equal as strings    ${power_sm获取值1}    2
    [Teardown]    Run Keywords    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
    ...    AND    Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    湿度传感器    系统运行环境    扩展湿度

获取扩展温度传感器状态
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    FOR    ${i}    IN RANGE    1    9
        Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_${i}
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    扩展温度传感器状态_${i}    正常
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取系统运行环境数字量    扩展温度传感器状态_${i}    ${SSH}
        should be equal as strings    ${power_sm获取值1}    0
        设置子工具值    DMU_YD8779Y    all    只读    温度    101
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    扩展温度传感器状态_${i}    异常
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取系统运行环境数字量    扩展温度传感器状态_${i}    ${SSH}
        should be equal as strings    ${power_sm获取值1}    1
        设置子工具值    DMU_YD8779Y    all    只读    温度    24
    END
    FOR    ${i}    IN RANGE    1    9
        Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    无
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    扩展温度传感器状态_${i}    未配置
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取系统运行环境数字量    扩展温度传感器状态_${i}    ${SSH}
        should be equal as strings    ${power_sm获取值1}    2
    END
    [Teardown]    Run Keywords    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
    ...    AND    Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_4
