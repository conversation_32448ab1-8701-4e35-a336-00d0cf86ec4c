*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
遥控复位_1
    ${设置协议数据}    设置1363通用控制数据    ${g_prtcl_1363}    <<遥控复位1-6~CID1=40H>>     <<遥控复位1-6~CID2=E2H>>    远程复位CSU    00    ${g_ver_1363}
    sleep    2m
    连接CSU
    @{历史事件内容}    获取web历史事件内容    ${empty}    ${empty}    所有    1    10
    ${是否复位}    Set Variable    False
    FOR    ${i}    IN    @{历史事件内容}
        ${是否复位}    Evaluate    "远程控制复位" in "${i}"
        ${操作记录}    Set Variable    ${i}
        Exit For Loop If    ${是否复位}==True
    END
    Should Contain    ${操作记录}    north 1363
    Should Be True    ${是否复位}

遥控复位_2
    ${设置协议数据}    设置1363通用控制数据    ${g_prtcl_1363}    <<遥控复位1-6 (2)~CID1=41H>>     <<遥控复位1-6~CID2=E2H>>    远程复位CSU    00    ${g_ver_1363}
    sleep    2m
    连接CSU
    @{历史事件内容}    获取web历史事件内容    ${empty}    ${empty}    所有    1    10
    ${是否复位}    Set Variable    False
    FOR    ${i}    IN    @{历史事件内容}
        ${是否复位}    Evaluate    "远程控制复位" in "${i}"
        ${操作记录}    Set Variable    ${i}
        Exit For Loop If    ${是否复位}==True
    END
    Should Contain    ${操作记录}    north 1363
    Should Be True    ${是否复位}

遥控复位_3
    ${设置协议数据}    设置1363通用控制数据    ${g_prtcl_1363}    <<遥控复位1-6 (3)~CID1=42H>>     <<遥控复位1-6~CID2=E2H>>    远程复位CSU    00    ${g_ver_1363}
    sleep    2m
    连接CSU
    @{历史事件内容}    获取web历史事件内容    ${empty}    ${empty}    所有    1    10
    ${是否复位}    Set Variable    False
    FOR    ${i}    IN    @{历史事件内容}
        ${是否复位}    Evaluate    "远程控制复位" in "${i}"
        ${操作记录}    Set Variable    ${i}
        Exit For Loop If    ${是否复位}==True
    END
    Should Contain    ${操作记录}    north 1363
    Should Be True    ${是否复位}

遥控复位_4
    ${设置协议数据}    设置1363通用控制数据    ${g_prtcl_1363}    <<遥控复位1-6 (4)~CID1=91H>>     <<遥控复位1-6~CID2=E2H>>    远程复位CSU    00    ${g_ver_1363}
    sleep    2m
    连接CSU
    @{历史事件内容}    获取web历史事件内容    ${empty}    ${empty}    所有    1    10
    ${是否复位}    Set Variable    False
    FOR    ${i}    IN    @{历史事件内容}
        ${是否复位}    Evaluate    "远程控制复位" in "${i}"
        ${操作记录}    Set Variable    ${i}
        Exit For Loop If    ${是否复位}==True
    END
    Should Contain    ${操作记录}    north 1363
    Should Be True    ${是否复位}

遥控复位_5
    ${设置协议数据}    设置1363通用控制数据    ${g_prtcl_1363}    <<遥控复位1-6 (5)~CID1=D0H>>     <<遥控复位1-6~CID2=E2H>>    远程复位CSU    00    ${g_ver_1363}
    sleep    2m
    连接CSU
    @{历史事件内容}    获取web历史事件内容    ${empty}    ${empty}    所有    1    10
    ${是否复位}    Set Variable    False
    FOR    ${i}    IN    @{历史事件内容}
        ${是否复位}    Evaluate    "远程控制复位" in "${i}"
        ${操作记录}    Set Variable    ${i}
        Exit For Loop If    ${是否复位}==True
    END
    Should Contain    ${操作记录}    north 1363
    Should Be True    ${是否复位}

遥控复位_6
    ${设置协议数据}    设置1363通用控制数据    ${g_prtcl_1363}    <<遥控复位1-6 (6)~CID1=46H>>     <<遥控复位1-6~CID2=E2H>>    远程复位CSU    00    ${g_ver_1363}
    sleep    2m
    连接CSU
    @{历史事件内容}    获取web历史事件内容    ${empty}    ${empty}    所有    1    10
    ${是否复位}    Set Variable    False
    FOR    ${i}    IN    @{历史事件内容}
        ${是否复位}    Evaluate    "远程控制复位" in "${i}"
        ${操作记录}    Set Variable    ${i}
        Exit For Loop If    ${是否复位}==True
    END
    Should Contain    ${操作记录}    north 1363
    Should Be True    ${是否复位}
