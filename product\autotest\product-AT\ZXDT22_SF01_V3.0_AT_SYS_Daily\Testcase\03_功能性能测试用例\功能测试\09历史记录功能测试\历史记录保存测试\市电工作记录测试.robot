*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
市电工作记录保存内容（纯市电场景）
    连接交流源
    连接电池模拟器
    关闭电池模拟器输出
    关闭交流源输出
    设置电池模拟器模式    铅酸电池    24    5    50
    电池模拟器接触器分合    ON
    打开电池模拟器输出
    连接CSU
    ${开始数量}    获取web事件记录数量    市电工作记录
    run keyword if    ${开始数量}>998    删除历史记录    市电工作记录
    sleep    30
    ${开始数量}    获取web事件记录数量    市电工作记录
    #保证有1个整流器在线，再开始测试
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    在线整流器数量    1
    同时设置三相电压频率    220    50
    打开交流源输出
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流供电状态    市电1
    ${起始时间}    获取系统时间
    ${起始电量}    获取web实时数据    市电供电量
    ${起始工作时间}    获取web实时数据    市电工作时间
    sleep    3m
    关闭交流源输出
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    交流供电状态    市电1
    sleep    3m
    ${结束时间}    获取系统时间
    ${结束电量}    获取web实时数据    市电供电量
    ${结束工作时间}    获取web实时数据    市电工作时间
    ${工作时间}    evaluate    ${结束工作时间}-${起始工作时间}
    ${结束数量}    获取web事件记录数量    市电工作记录
    @{记录内容}    获取web事件记录最新一条    市电工作记录
    should be true    @{记录内容}[0] == ${结束数量}
    should be true    ${结束数量} == ${开始数量} + 1
    ${起始时间差}    subtract date from date    @{记录内容}[1]    ${起始时间}
    should be true    -180<=${起始时间差}<=180
    ${结束时间差}    subtract date from date    @{记录内容}[2]    ${结束时间}
    should be true    -180<=${结束时间差}<=180
    should be true    ${工作时间}-1<=@{记录内容}[3]<=${工作时间}+1
    should be true    ${起始电量}<=@{记录内容}[4]<=${起始电量}+1
    should be true    ${结束电量}<=@{记录内容}[5]<=${结束电量}+1

市电工作记录保存内容（不满足保存条件）
    连接交流源
    连接电池模拟器
    关闭电池模拟器输出
    关闭交流源输出
    设置电池模拟器模式    铅酸电池    24    5    50
    电池模拟器接触器分合    ON
    打开电池模拟器输出
    连接CSU
    ${开始数量}    获取web事件记录数量    市电工作记录
    run keyword if    ${开始数量}>998    删除历史记录    市电工作记录
    sleep    30
    ${开始数量}    获取web事件记录数量    市电工作记录
    @{记录内容}    获取web事件记录最新一条    市电工作记录
    #保证有1个整流器在线，再开始测试
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    在线整流器数量    1
    同时设置三相电压频率    220    50
    打开交流源输出
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流供电状态    市电1
    ${起始时间}    获取系统时间
    ${起始电量}    获取web实时数据    市电供电量
    ${起始工作时间}    获取web实时数据    市电工作时间
    sleep    90S
    关闭交流源输出
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    交流供电状态    市电1
    sleep    90S
    ${结束时间}    获取系统时间
    ${结束电量}    获取web实时数据    市电供电量
    ${结束工作时间}    获取web实时数据    市电工作时间
    ${工作时间}    evaluate    ${结束工作时间}-${起始工作时间}
    ${结束数量}    获取web事件记录数量    市电工作记录
    @{记录内容1}    获取web事件记录最新一条    市电工作记录
    should be true    @{记录内容}[0] == ${结束数量}
    should be true    ${结束数量} == ${开始数量}
    ${time}    Subtract Date From Date    @{记录内容1}[1]    @{记录内容}[1]
    should be equal    ${time}    ${0}
    ${time}    Subtract Date From Date    @{记录内容1}[2]    @{记录内容}[2]
    should be equal    ${time}    ${0}
    should be true    @{记录内容1}[3]==@{记录内容}[3]
    should be true    @{记录内容1}[4]==@{记录内容}[4]
    should be true    @{记录内容1}[5]==@{记录内容}[5]

市电工作记录掉电保存
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    ${记录数量}    获取web事件记录数量    市电工作记录
    测试台上下电操作    T1-1    OFF
    sleep    15
    测试台上下电操作    T1-1    ON
    实时告警刷新完成
    sleep    5
    ${记录数量1}    获取web事件记录数量    市电工作记录
    should be true    ${记录数量}==${记录数量1}

市电工作记录最大条数测试
    连接CSU
    sleep    20
    #产生市电工作记录
    FOR    ${k}    IN RANGE    1002
    ${开始数量}    获取web事件记录数量    市电工作记录
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    在线整流器数量    1
    同时设置三相电压频率    220    50
    打开交流源输出
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流供电状态    市电1
    ${起始时间}    获取系统时间
    ${起始电量}    获取web实时数据    市电供电量
    ${起始工作时间}    获取web实时数据    市电工作时间
    sleep    3m
    关闭交流源输出
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    交流供电状态    市电1
    sleep    3m
    ${结束时间}    获取系统时间
    ${结束电量}    获取web实时数据    市电供电量
    ${结束工作时间}    获取web实时数据    市电工作时间
    should be true    ${结束数量} == ${开始数量} + 1
    sleep    10
    ${市电工作记录数量}    获取web事件记录数量    市电工作记录
    exit for loop if    ${市电工作记录数量}>=1002
    sleep    2m
    ${市电工作记录数量}    获取web事件记录数量    市电工作记录
    should be true    ${市电工作记录数量}== 1000
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    在线整流器数量    1
    同时设置三相电压频率    220    50
    打开交流源输出
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流供电状态    市电1
    ${起始时间}    获取系统时间
    ${起始电量}    获取web实时数据    市电供电量
    ${起始工作时间}    获取web实时数据    市电工作时间
    sleep    3m
    关闭交流源输出
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    交流供电状态    市电1
    sleep    8m
    ${市电工作记录数量}    获取web事件记录数量    市电工作记录
    should be true    ${市电工作记录数量}== 1000
    [Teardown]    打开交流源输出
