*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
安全模式手动控制整流器
    
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-3
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    2
    ${整流器1休眠状态}    获取web实时数据    整流器休眠状态-1
    run keyword if    '${整流器1休眠状态}'=='是'    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器唤醒-1
    ${整流器2休眠状态}    获取web实时数据    整流器休眠状态-2
    run keyword if    '${整流器2休眠状态}'=='是'    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器唤醒-2
    ${整流器3休眠状态}    获取web实时数据    整流器休眠状态-3
    run keyword if    '${整流器3休眠状态}'=='是'    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器唤醒-3
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    ${直流电压}    获取web实时数据    直流电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    2
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    ${直流电压}    获取web实时数据    直流电压
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-3
    ${整流器1休眠状态}    获取web实时数据    整流器休眠状态-1
    run keyword if    '${整流器1休眠状态}'=='是'    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器唤醒-1
    ${整流器2休眠状态}    获取web实时数据    整流器休眠状态-2
    run keyword if    '${整流器2休眠状态}'=='是'    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器唤醒-2
    ${整流器3休眠状态}    获取web实时数据    整流器休眠状态-3
    run keyword if    '${整流器3休眠状态}'=='是'    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器唤醒-3
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    [Teardown]    设置web参数量    整流器最小开机数量    3

安全模式不自动关整流器测试
    
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    ${状态}    Run Keyword And Return Status    Wait Until Keyword Succeeds    8m    2    信号量数据值不为    工作整流器数量    3
    should not be true    ${状态}
    [Teardown]    设置web参数量    整流器最小开机数量    3

安全模式增加负载开启整流器测试
    
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-3
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    2
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    80    2
    打开负载输出
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    [Teardown]    Run keywords    设置web参数量    整流器最小开机数量    3
    ...    AND    关闭负载输出

安全模式减小负载关闭整流器测试
    
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-3
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    2
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    80    2
    打开负载输出
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    关闭负载输出
    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-3
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    2
    [Teardown]    Run keywords    设置web参数量    整流器最小开机数量    3
    ...    AND    关闭负载输出

安全模式电池容量变化
    
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-3
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    2
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    500
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    [Teardown]    Run keywords    设置web参数量    整流器最小开机数量    3
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    100

安全模式电池充电系数变化
    
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-3
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    2
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池充电电流系数    0.6
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    [Teardown]    Run keywords    设置web参数量    整流器最小开机数量    3
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池充电电流系数    0.15

安全模式节能带载率下限变化
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-3
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    2
    ${可设范围}    获取web参数可设置范围    节能带载率上限
    Wait Until Keyword Succeeds    10    1    设置web参数量    节能带载率上限    ${可设范围}[0]
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    2
    ${下限可设范围}    获取web参数可设置范围    节能带载率下限
    Wait Until Keyword Succeeds    10    1    设置web参数量    节能带载率下限    ${下限可设范围}[0]
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    [Teardown]    Run keywords    设置web参数量    整流器最小开机数量    3
    ...    AND    设置web设备参数量为默认值    节能带载率上限    节能带载率下限

安全模式不轮换整流器测试
    
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器轮换周期    7
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    ${下次轮换时间}    获取web实时数据    下次轮换时间
    ${起始时间}    获取系统时间
    ${设置轮换时间}    Subtract Time From Date    ${起始时间}    50s    #比16:00:00差5s
    设置系统时间    ${设置轮换时间}
    sleep    5m
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    ${下次轮换时间1}    获取web实时数据    下次轮换时间
    should be equal    ${下次轮换时间}    ${下次轮换时间1}
    [Teardown]    Run keywords    同步系统时间
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3
