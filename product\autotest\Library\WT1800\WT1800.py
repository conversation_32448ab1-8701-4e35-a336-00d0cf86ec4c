#!/usr/bin/env python2
# -*- coding: UTF-8 -*-
#####################################################################################
# File Name:    WT1800.py
# Discription:  实现与功率分析仪的连接和数据读写
# Author:   Pengy；
# DevEnv:   winXP/7x86+python2.7
# Notes:    用于充电模块自动化测试
# Log:      20180319Pengy 
#####################################################################################

import struct  # pack unpack
import time    # 时间库
import os
from struct import *
import vxi11 #WT1800的以太网使用的是WXI-11协议，不能用socket

####读取文件地址，使用RIDE时，改为ADD[1]；使用本地编译时改为ADD[0]####
ADD={0:'..\.',
     1:''
    }
add=ADD[1]

class WT1800(): 
    def __init__(self):
        self.sCmdName = ''
        self.disWT1800 = []  # 显示精度数据存储使用

    ##############################网络连接##############################    
    # 连接WT1800
    def connect_WT1800_Ethernet(self, IP):
        """连接功率分析仪WT1800
        IP：IP地址; 
        """
        try:
            self.instr=vxi11.Instrument(IP)
            print self.instr.ask('*IDN?')
            self.SetCmd(u'电压档自动','no')
            self.SetCmd(u'电流档自动','no')
            print(u'连接WT1800成功...')
        except:
            print u'连接WT1800失败'

       

    #初始化测试，配置各通道为何测试项目
    def configItem(self):
        print u'==========配置WT1800============'
        f = open( add+r'.\python\WT1800\WT1800_command_list.csv', 'r')
        csvlist = []
        tmplist1=[]
        csvlist = f.readlines()
        f.close()
        
        tmplist=[]
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
            tmplist1.append(tmplist)
        
        self.SetCmd(u'设置更新时间500ms','no') #设置更新时间500ms
        self.SetCmd(u'设置项目数量','no') #设置项目数量为38
        #取CSV文件中的设置项目，如果有增减，需要修改循环范围：
        for i in range(4,45):
            sCmd=(tmplist1[i][3]+','+tmplist1[i][4]+','+tmplist1[i][5]).decode('gbk')
            sCmd=sCmd[1:-1]
            sName=tmplist1[i][2]
            self.instr.write(sCmd)

        #取CSV文件中的批量谐波设置项目，如果有增减，需要修改循环范围：
##        self.SetCmd(u'设置谐波列表数为6')
##        self.SetCmd(u'设置谐波次数100')
##
##        for i in range(39,44):#41
##            sCmd=(tmplist1[i][3]+','+tmplist1[i][4]).decode('gbk')
##            sCmd=sCmd[1:-1]
##            sName=tmplist1[i][2]
##            self.instr.write(sCmd)


        self.SetCmd(u'电压档自动','no')
        self.SetCmd(u'电流档自动','no')
        self.SetCmd(u'复位积分','no')
##        print self.queryNumeric(u'查询积分设置')
##        print self.queryNumeric(u'查询积分状态')
##        print self.instr.ask(':NUMERIC:NORMAL:ITEM1?')

##############################创建文档############################## 
#创建源效应测试文档
    def CreatePowerEffectDoc(self):
        try:
            savef=open(add+r'.\TestRecord'+u'\源效应测试.csv','a')
            sStr=(u'时间,'+u'输入电压(V),'+u'输出电压(V),'
                  +u'输出电流（A）,'+u'源效应,'+u'测试结果,'
                  )
            print>>savef,sStr.encode('gbk')
            savef.close()
        except:
            print u'打开源效应测试文件出错！'

#创建负载效应测试文档
    def CreateLoadEffectDoc(self):
        """""""""""""""""""""""""""""""""""""""
        创建负载效应测试文档或者表头，在每测试完一组数据后，
        需要创建一次表头
        """""""""""""""""""""""""""""""""""""""
        try:
            savef=open(add+r'.\TestRecord'+u'\负载效应测试.csv','a')
            sStr=(u'时间,'+u'输入电压(V),'+u'输出电压(V),'
                  +u'输出电流（A）,'+u'负载效应,'+u'测试结果,'
                  )
            print>>savef,sStr.encode('gbk')
            savef.close()
        except:
            print u'打开负载效应测试文件出错！'

#创建性能指标测试文档
    def CreatePerformanceTestDoc(self):
        try:
            savef=open(add+r'.\TestRecord'+u'\性能指标测试.csv','a')
            sStr=(u'时间,'+u'输入电压(V),'+u'输出电压(V),'
                  +u'输出电流（A）,'+u'输出功率（W）,'+u'输入功率（W）,'+u'效率,'+u'效率测试结果,'
                  +u'A相PF,'+u'B相PF,'+u'C相PF,'+u'PF测试结果,'
                  +u'A相电流THD,'+u'B相电流THD,'+u'C相电流THD,'+u'电流THD测试结果'
                  )
            print>>savef,sStr.encode('gbk')
            savef.close()
        except:
            print u'打开性能指标测试文件出错！'

#创建稳压精度测试文档
    def CreateVoltagePrecisionDoc(self):
        try:
            savef=open(add+r'.\TestRecord'+u'\稳压精度测试.csv','a')
            sStr=(u'时间,'+u'输出电压设定值(V),'+u'输入电压(V),'
                  +u'输出电流（A）,'+u'输出电压(V),'
                  +u'稳压精度,'+u'测试结果,'
                  )
            print>>savef,sStr.encode('gbk')
            savef.close()
        except:
            print u'打开稳压精度测试文件出错！'
#创建稳流精度测试文档
    def CreateCurrentPrecisionDoc(self):
        try:
            savef=open(add+r'.\TestRecord'+u'\稳流精度测试.csv','a')
            sStr=(u'时间,'+u'输出电流设定值(V),'+u'输入电压(V),'
                  +u'输出电压(V),'+u'输出电流（A）,'
                  +u'稳流精度,'+u'测试结果,'
                  )
            print>>savef,sStr.encode('gbk')
            savef.close()
        except:
            print u'打开稳流精度测试文件出错！'

##############################关键字##############################
##查询WT1800数据=============================================
    def ReadWT1800Data(self):
        """""""""""""""""""""""""""""""""""""""
        查询WT1800数据，能返回如下信息：
        交流电压1、交流电压2、交流电压3、交流总电压、直流电压、交流电流1、交流电流2、交流电流3、
        交流总电流、直流电流、有功功率1、有功功率2、有功功率3、交流总有功功率、直流总功率、
        功率因数1、功率因数2、功率因数3、总功率因数、电压THD1、电压THD2、电压THD3、电流THD1、
        电流THD2、电流THD3、频率、输出电量、输入电量、交流视在功率1、交流视在功率2、交流视在功率3、
        交流总视在功率、直流视在功率、交流无功功率1、交流无功功率2、交流无功功率3、交流总无功功率、
        直流无功功率、R相电压、S相电压、T相电压
        """""""""""""""""""""""""""""""""""""""
        returnDataList=[]
        ReadList=[]
        while(1):
            receiveDataStr=self.queryNumeric(u'查询数据','no display')
            receiveData=receiveDataStr.split(',') #转为列表
            if receiveData[0]!='NAN':
                break
        for i in receiveData:#转换为浮点数的字符
            ReadList.append(str(float(i)))            
        return ReadList


##读通道所有数据=============================================
    def ReadCH(self, CH='CH1'):
        """""""""""""""""""""""""""""""""""""""
        该函数与PM3000读数据匹配；
        CH:选择读取哪个通道数据
        可设置：CH1、CH2、CH3、SUM
        返回一个列表，内容如下：
        0. Watts 1. VA 2. VAr
        3. Vrms 4. Arms 5. PF
        6. NAN 7. NAN  8. NAN
        9.NAN 10.NAN 11.NAN
        12.相电压 13.Vthd 14.Athd
        15.Frq

        """""""""""""""""""""""""""""""""""""""
        returnDataList=[]
        ReadList=[]
        while(1):
            receiveDataStr=self.queryNumeric(u'查询数据','no display')
            receiveData=receiveDataStr.split(',') #转为列表
            if receiveData[0]!='NAN':
                break
        """""""""""""""""""""""""""""""""""""""
        查询WT1800数据，能返回如下信息：
        交流电压1、交流电压2、交流电压3、交流总电压、直流电压、交流电流1、交流电流2、交流电流3、
        交流总电流、直流电流、有功功率1、有功功率2、有功功率3、交流总有功功率、直流总功率、
        功率因数1、功率因数2、功率因数3、总功率因数、电压THD1、电压THD2、电压THD3、电流THD1、
        电流THD2、电流THD3、频率、输出电量、输入电量、交流视在功率1、交流视在功率2、交流视在功率3、
        交流总视在功率、直流视在功率、交流无功功率1、交流无功功率2、交流无功功率3、交流总无功功率、
        直流无功功率、R相电压、S相电压、T相电压
        """""""""""""""""""""""""""""""""""""""

        if CH=='CH1':
            returnDataList=[receiveData[10],receiveData[28],receiveData[33],
                            receiveData[0],receiveData[5],receiveData[15],
                            'NAN','NAN','NAN',
                            'NAN','NAN','NAN',
                            receiveData[38],receiveData[19],receiveData[22],
                            receiveData[25],
                            ]
        elif CH=='CH2':
            returnDataList=[receiveData[11],receiveData[29],receiveData[34],
                            receiveData[1],receiveData[6],receiveData[16],
                            'NAN','NAN','NAN',
                            'NAN','NAN','NAN',
                            receiveData[39],receiveData[20],receiveData[23],
                            receiveData[25],
                            ]            
        elif CH=='CH3':
            returnDataList=[receiveData[12],receiveData[30],receiveData[35],
                            receiveData[2],receiveData[7],receiveData[17],
                            'NAN','NAN','NAN',
                            'NAN','NAN','NAN',
                            receiveData[40],receiveData[21],receiveData[24],
                            receiveData[25],
                            ]
        elif CH=='SUM':
            returnDataList=[receiveData[13],receiveData[31],receiveData[36],
                            receiveData[3],receiveData[8],receiveData[18],
                            'NAN','NAN','NAN',
                            'NAN','NAN','NAN',
                            receiveData[38],receiveData[19],receiveData[22],
                            receiveData[25],
                            ]
        else:
            print u'输入错误！'
            return False
##        print 't1',returnDataList
        return returnDataList

##等待电压达到指定值=============================================
    def WaitVolReachValue(self,Vol='700',waittime='10'):
        """""""""""""""""""""""""""""""""""""""
        等待电压达到指定值
        入参：指定电压
        返回结果
        """""""""""""""""""""""""""""""""""""""
        OutPutList=[]        
        cnt=0
        while(1):
            receiveDataStr=self.queryNumeric(u'查询数据','no display')
            receiveData=receiveDataStr.split(',') #转为列表
            realVol=receiveData[4]
            time.sleep(1)
            cnt=cnt+1
            if realVol>=float(Vol):
##                print '电压：',realVol
                return True
            elif cnt>=float(waittime):
                return False

#稳压精度测试=============================================
    def VoltagePrecisionTest(self,OutputVol='750',InputVol='380'):
        """""""""""""""""""""""""""""""""""""""
        稳压精度测试
        生成测试文档
        OutputVol:给定的输出电压
        InputVol：给定的输入电压
        """""""""""""""""""""""""""""""""""""""
        OutPutList=[]
        receiveDataStr=''
        while(1):
            receiveDataStr=self.queryNumeric(u'查询数据','no display')
            OutPutList=receiveDataStr.split(',') #转为列表
            if OutPutList[0]!='NAN':
                break
##组包，准备打印：        
        OutStr=(time.strftime("%H:%M:%S")+','   #时间
                +OutputVol+','                   #输出电压设定值
                +InputVol+','                   #输入电压                
                +str(round(float(OutPutList[9]),2))+',' #输出电流
                +str(round(float(OutPutList[4]),2))+',' #输出电压
                +'####'+','                     #稳压精度
                +'####'+','                     #通过准则
                )

        print u'稳压精度测试数据：',OutStr

        try:
            savef=open(add+r'.\TestRecord'+u'\稳压精度测试.csv','a')
            print>>savef,OutStr.encode('gbk')
            savef.close()
        except:
            print u'打开稳压精度测试文件出错！'    
##稳压精度测试结果判断=============================================
    def VoltagePrecisionJudge(self,rule='0.1'):
        """""""""""""""""""""""""""""""""""""""
        读取负载效应数据文档，根据通过准则判断测试结果
        rule：通过准则
        """""""""""""""""""""""""""""""""""""""
        f = open( add+r'.\TestRecord'+u'\稳压精度测试.csv', 'r')
        csvlist = []
        tmplist=[]
        addlist=[]
        savelist=[]
        calclist=[]
        failtag=0
        testtag=0
        outStr=''
        csvlist = f.readlines()
        f.close()

        if len(csvlist)<10:
            print u'测试结果判断数据不全，请重新测试！1'
            return        
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')            
            if tmplist[0].decode('gbk')==u'时间':
                addlist.append(i)
##        print addlist
        for i in addlist:
##            print 't1',addlist,len(csvlist[i+1:i+10])
##            print 't2',csvlist[i+1:i+10]
            if (len(csvlist[i+1:i+10])>8 and
                csvlist[i+1][0:4].decode('gbk')!=u'时间' and
                csvlist[i+2][0:4].decode('gbk')!=u'时间' and
                csvlist[i+3][0:4].decode('gbk')!=u'时间' and
                csvlist[i+4][0:4].decode('gbk')!=u'时间' and
                csvlist[i+5][0:4].decode('gbk')!=u'时间' and
                csvlist[i+6][0:4].decode('gbk')!=u'时间' and
                csvlist[i+7][0:4].decode('gbk')!=u'时间' and
                csvlist[i+8][0:4].decode('gbk')!=u'时间' and
                csvlist[i+9][0:4].decode('gbk')!=u'时间' 
                ):
                calclist=[float(csvlist[i+1].split(',')[4]),float(csvlist[i+2].split(',')[4]),float(csvlist[i+3].split(',')[4]),
                          float(csvlist[i+4].split(',')[4]),float(csvlist[i+5].split(',')[4]),float(csvlist[i+6].split(',')[4]),
                          float(csvlist[i+7].split(',')[4]),float(csvlist[i+8].split(',')[4]),float(csvlist[i+9].split(',')[4])
                          ]
##                print calclist
                #计算稳压精度
                maxvolt=max(calclist)
                minvolt=min(calclist)
                DeltaVolt=max(abs(maxvolt-float(csvlist[i+5].split(',')[4])),abs(minvolt-float(csvlist[i+5].split(',')[4])))
##                print DeltaVolt
                VoltagePrecision=round(abs(DeltaVolt/float(csvlist[i+5].split(',')[4])*100),3)
                print u'稳压精度：',VoltagePrecision
                #判断稳压精度
                if VoltagePrecision>float(rule):
                    testreslut='FAIL'
                else:
                    testreslut='PASS'
                savelist=csvlist[i+9][0:-1].split(',')
                savelist[5]=str(VoltagePrecision)
                savelist[6]=testreslut
                csvlist[i+9]=','.join(savelist)+'\n'
                testtag+=1
        outStr=''.join(csvlist)[:-1]
##        print outStr

        try:
            savef=open(add+r'.\TestRecord'+u'\稳压精度测试.csv','w')
            print>>savef,outStr
            savef.close()
        except:
            print u'打开稳压精度测试文件出错！'

        ##判断稳压精度测试结果是否通过
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
##            print tmplist,len(tmplist)
            if len(tmplist)>=7 and tmplist[6]=='FAIL':
                print u'测试不通过！'
                failtag=1
                return False
        if failtag==0 and testtag>0:
            print u'测试通过！'
            return True
        else:
            print u'测试结果判断数据不全，请重新测试！2'
            return False
#稳流精度测试=============================================
    def CurrentPrecisionTest(self,OutputCur='750',InputVol='380'):
        """""""""""""""""""""""""""""""""""""""
        稳流精度测试
        生成测试文档
        OutputCur:输出电流给定值
        InputVol：输入电压
        COM1:接FLUKE表测量电压
        COM2:接FLUKE表测量电流
        """""""""""""""""""""""""""""""""""""""
        OutPutList=[]
        receiveDataStr=''
        while(1):
            receiveDataStr=self.queryNumeric(u'查询数据','no display')
            OutPutList=receiveDataStr.split(',') #转为列表
            if OutPutList[0]!='NAN':
                break
##组包，准备打印：
        OutStr=(time.strftime("%H:%M:%S")+','   #时间
                +OutputCur+','                   #输出电流设定值
                +InputVol+','                   #输入电压                
                +str(round(float(OutPutList[4]),2))+',' #输出电压
                +str(round(float(OutPutList[9]),2))+',' #输出电流
                +'####'+','                     #稳流精度
                +'####'+','                     #通过准则
                )

        print u'稳流精度测试数据：',OutStr

        try:
            savef=open(add+r'.\TestRecord'+u'\稳流精度测试.csv','a')
            print>>savef,OutStr.encode('gbk')
            savef.close()
        except:
            print u'打开稳流精度测试文件出错！'    
##稳流精度测试结果判断=============================================
    def CurrentPrecisionJudge(self,rule='0.1'):
        """""""""""""""""""""""""""""""""""""""
        读取稳流精度数据文档，根据通过准则判断测试结果
        rule：通过准则
        """""""""""""""""""""""""""""""""""""""
        f = open( add+r'.\TestRecord'+u'\稳流精度测试.csv', 'r')
        csvlist = []
        tmplist=[]
        addlist=[]
        savelist=[]
        calclist=[]
        failtag=0
        testtag=0
        outStr=''
        csvlist = f.readlines()
        f.close()
        if len(csvlist)<10:
            print u'测试结果判断数据不全，请重新测试！'
            return False
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')            
            if tmplist[0].decode('gbk')==u'时间':
                addlist.append(i)
##        print addlist
        for i in addlist:
##            print len(csvlist[i+1:i+9])
            if (len(csvlist[i+1:i+10])>8 and
                csvlist[i+1][0:4].decode('gbk')!=u'时间' and
                csvlist[i+2][0:4].decode('gbk')!=u'时间' and
                csvlist[i+3][0:4].decode('gbk')!=u'时间' and
                csvlist[i+4][0:4].decode('gbk')!=u'时间' and
                csvlist[i+5][0:4].decode('gbk')!=u'时间' and
                csvlist[i+6][0:4].decode('gbk')!=u'时间' and
                csvlist[i+7][0:4].decode('gbk')!=u'时间' and
                csvlist[i+8][0:4].decode('gbk')!=u'时间' and
                csvlist[i+9][0:4].decode('gbk')!=u'时间' 
                ):
                calclist=[float(csvlist[i+1].split(',')[4]),float(csvlist[i+2].split(',')[4]),float(csvlist[i+3].split(',')[4]),
                          float(csvlist[i+4].split(',')[4]),float(csvlist[i+5].split(',')[4]),float(csvlist[i+6].split(',')[4]),
                          float(csvlist[i+7].split(',')[4]),float(csvlist[i+8].split(',')[4]),float(csvlist[i+9].split(',')[4])
                          ]
##                print calclist
                #计算稳流精度
                maxCurr=max(calclist)
                minCurr=min(calclist)
                DeltaCurr=max(abs(maxCurr-float(csvlist[i+5].split(',')[4])),abs(minCurr-float(csvlist[i+5].split(',')[4])))
##                print DeltaCurr
                CurrentPrecision=round(abs(DeltaCurr/float(csvlist[i+5].split(',')[4])*100),3)
                print u'稳流精度：',CurrentPrecision
                #判断稳流精度
                if CurrentPrecision>float(rule):
                    testreslut='FAIL'
                else:
                    testreslut='PASS'
                savelist=csvlist[i+9][0:-1].split(',')
##                print 't1',savelist
                savelist[5]=str(CurrentPrecision)
                savelist[6]=testreslut
                csvlist[i+9]=','.join(savelist)+'\n'
                testtag+=1

        outStr=''.join(csvlist)[:-1]
##        print testtag

        try:
            savef=open(add+r'.\TestRecord'+u'\稳流精度测试.csv','w')
            print>>savef,outStr
            savef.close()
        except:
            print u'打开稳流精度测试文件出错！'

        ##判断稳流精度测试结果是否通过
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
##            print tmplist,len(tmplist)
            if len(tmplist)>=7 and tmplist[6]=='FAIL':
                print u'测试不通过！'
                failtag=1
                return False
        if failtag==0 and testtag>0:
            print u'测试通过！'
            return True
        else:
            print u'测试结果判断数据不全，请重新测试！'
            return False

#负载效应测试=============================================
    def LoadEffectTest(self,InputVol='380'):
        """""""""""""""""""""""""""""""""""""""
        负载效应测试
        生成测试文档
        COM1:接FLUKE表测量电压
        COM2:接FLUKE表测量电流
        """""""""""""""""""""""""""""""""""""""
        OutPutList=[]
        receiveDataStr=''
        while(1):
            receiveDataStr=self.queryNumeric(u'查询数据','no display')
            OutPutList=receiveDataStr.split(',') #转为列表
            if OutPutList[0]!='NAN':
                break
##组包，准备打印：
        OutStr=(time.strftime("%H:%M:%S")+','   #时间
                +InputVol+','                   #设置的输入电压   
                +str(round(float(OutPutList[4]),2))+',' #输出电压
                +str(round(float(OutPutList[9]),2))+',' #输出电流
                +'####'+','
                +'####'+','
                )

        print u'负载效应测试数据：',OutStr

        try:
            savef=open(add+r'.\TestRecord'+u'\负载效应测试.csv','a')
            print>>savef,OutStr.encode('gbk')
            savef.close()
        except:
            print u'打开负载效应测试文件出错！'        

##负载效应测试结果判断=============================================
    def LoadEffectJudge(self,rule='0.1'):
        """""""""""""""""""""""""""""""""""""""
        读取负载效应数据文档，根据通过准则判断测试结果
        rule：负载效应通过准则
        """""""""""""""""""""""""""""""""""""""
        f = open( add+r'.\TestRecord'+u'\负载效应测试.csv', 'r')
        csvlist = []
        tmplist=[]
        addlist=[]
        savelist=[]
        calclist=[]
        failtag=0
        outStr=''
        csvlist = f.readlines()
        f.close()
        
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')            
            if tmplist[0].decode('gbk')==u'时间':
                addlist.append(i)
##        print addlist
        for i in addlist:
            if (csvlist[i+1][0:4].decode('gbk')!=u'时间' and
                csvlist[i+2][0:4].decode('gbk')!=u'时间' and
                csvlist[i+3][0:4].decode('gbk')!=u'时间' 
                ):
                calclist=[csvlist[i+1].split(','),csvlist[i+2].split(','),csvlist[i+3].split(',')]
##                print calclist
                voltage1=float(calclist[0][2])
                voltage2=float(calclist[1][2])
                voltage3=float(calclist[2][2])
                maxvolt=max(voltage1,voltage2,voltage3)
                minvolt=min(voltage1,voltage2,voltage3)
                LoadEffect=round(abs(maxvolt-minvolt)/voltage1*100,3)
                if LoadEffect>float(rule):
                    testreslut='FAIL'
                else:
                    testreslut='PASS'
                savelist=csvlist[i+3][0:-1].split(',')
                savelist[4]=str(LoadEffect)
                savelist[5]=testreslut
                csvlist[i+3]=','.join(savelist)+'\n'

        outStr=''.join(csvlist)


        try:
            savef=open(add+r'.\TestRecord'+u'\负载效应测试.csv','w')
            print>>savef,outStr
            savef.close()
        except:
            print u'打开负载效应测试文件出错！'

        ##判断负载效应结果是否通过
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
##            print tmplist,len(tmplist)
            if len(tmplist)>=7 and tmplist[5]=='FAIL':
                print u'测试不通过！'
                failtag=1
                return False
        if failtag==0:
            print u'测试通过！'
            return True
        
#源效应测试=============================================
    def PowerEffectTest(self,InputVol='380'):
        """""""""""""""""""""""""""""""""""""""
        源效应测试
        生成测试文档
        COM1:接FLUKE表测量电压
        COM2:接FLUKE表测量电流
        """""""""""""""""""""""""""""""""""""""
        OutPutList=[]
        receiveDataStr=''
        while(1):
            receiveDataStr=self.queryNumeric(u'查询数据','no display')
            OutPutList=receiveDataStr.split(',') #转为列表
            if OutPutList[0]!='NAN':
                break
##组包，准备打印：
        OutStr=(time.strftime("%H:%M:%S")+','   #时间
                +InputVol+','                   #设置的输入电压   
                +str(round(float(OutPutList[4]),2))+',' #输出电压
                +str(round(float(OutPutList[9]),2))+',' #输出电流
                +'####'+','
                +'####'+','
                )

        print u'源效应测试数据：',OutStr

        try:
            savef=open(add+r'.\TestRecord'+u'\源效应测试.csv','a')
            print>>savef,OutStr.encode('gbk')
            savef.close()
        except:
            print u'打开源效应测试文件出错！'        

##源效应测试结果判断=============================================
    def PowerEffectJudge(self,rule='-0.1'):
        """""""""""""""""""""""""""""""""""""""
        读取源效应数据文档，根据通过准则判断测试结果
        rule：源效应通过准则
        """""""""""""""""""""""""""""""""""""""
        f = open( add+r'.\TestRecord'+u'\源效应测试.csv', 'r')
        csvlist = []
        tmplist=[]
        addlist=[]
        savelist=[]
        calclist=[]
        outStr=''
        failtag=0
        csvlist = f.readlines()
        f.close()
        
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')            
            if tmplist[0].decode('gbk')==u'时间':
                addlist.append(i)
##        print addlist
        for i in addlist:
            if (csvlist[i+1][0:4].decode('gbk')!=u'时间' and
                csvlist[i+2][0:4].decode('gbk')!=u'时间' and
                csvlist[i+3][0:4].decode('gbk')!=u'时间' 
                ):
                calclist=[csvlist[i+1].split(','),csvlist[i+2].split(','),csvlist[i+3].split(',')]
##                print calclist
                voltage1=float(calclist[0][2])
                voltage2=float(calclist[1][2])
                voltage3=float(calclist[2][2])
                maxvolt=max(voltage1,voltage2,voltage3)
                minvolt=min(voltage1,voltage2,voltage3)
                LoadEffect=round(abs(maxvolt-minvolt)/voltage1*100,3)
                if LoadEffect>float(rule):
                    testreslut='FAIL'
                else:
                    testreslut='PASS'
                savelist=csvlist[i+3][0:-1].split(',')
                savelist[4]=str(LoadEffect)
                savelist[5]=testreslut
                csvlist[i+3]=','.join(savelist)+'\n'

        outStr=''.join(csvlist)
    

        try:
            savef=open(add+r'.\TestRecord'+u'\源效应测试.csv','w')
            print>>savef,outStr
            savef.close()
        except:
            print u'打开源效应测试文件出错！'

        ##判断源效应测试结果是否通过
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
##            print tmplist,len(tmplist)
            if len(tmplist)>=7 and tmplist[5]=='FAIL':
                print u'测试不通过！'
                failtag=1
                return False
        if failtag==0:
            print u'测试通过！'
            return True
##性能指标测试=============================================
    def PerformanceTest(self,Efficiency='0.95',PF='0.95',THDA='12'):
        """""""""""""""""""""""""""""""""""""""
        性能指标测试
        生成测试文档
        Efficiency:效率测试通过准则
        PF:PF通过准则
        THDA：电流THD通过准则
        COM1:接FLUKE表测量电压
        COM2:接FLUKE表测量电流
        """""""""""""""""""""""""""""""""""""""
        print u'=========性能指标测试============'
        OutPutList=[]
        eff=0
        PFResult='PASS'
        THDAResult='PASS'
        OutPutList=[]
        receiveDataStr=''
        while(1):
            receiveDataStr=self.queryNumeric(u'查询数据','no display')
            OutPutList=receiveDataStr.split(',') #转为列表
            if OutPutList[0]!='NAN':
                break
##计算效率：
        if float(OutPutList[13])!=0:
            eff=round(float(OutPutList[14])/float(OutPutList[13]),4)   #效率
            print u'转换效率为：',eff
        else:
            print u'输入功率为0，请检查是否开机,输入功率为：',float(OutPutList[13])
            return False
##PF判断
        MaxPF=max(round(float(OutPutList[18]),4),round(float(OutPutList[18]),4),round(float(OutPutList[18]),4))
        if MaxPF<float(PF):
            PFResult='FAIL'
        else:
            PFResult='PASS'
##电流THD判断
        MaxTHDA=max(round(float(OutPutList[22]),2),round(float(OutPutList[23]),2),round(float(OutPutList[24]),2))
        if MaxTHDA>float(THDA):
            THDAResult='FAIL'
        else:
            THDAResult='PASS'            
##效率判断
        if eff<float(Efficiency):
            EfficiencyResult='FAIL'
        else:
            EfficiencyResult='PASS' 

##组包，准备打印：
        OutStr=(time.strftime("%H:%M:%S")+','   #时间
                +str(round(float(OutPutList[0]),2))+','         #输入电压
                +str(round(float(OutPutList[4]),2))+','         #输出电压
                +str(round(float(OutPutList[9]),2))+','         #输出电流
                +str(round(float(OutPutList[14]),2))+','        #输出功率
                +str(round(float(OutPutList[13]),2))+','        #输入功率
                +str(eff)+','                   #效率
                +EfficiencyResult+','           #效率测试结果
                +str(round(float(OutPutList[18]),4))+','               #A相PF
                +str(round(float(OutPutList[18]),4))+','               #B相PF
                +str(round(float(OutPutList[18]),4))+','               #C相PF
                +PFResult+',' 
                +str(round(float(OutPutList[22]),3))+','              #A相电流THD
                +str(round(float(OutPutList[23]),3))+','              #B相电流THD
                +str(round(float(OutPutList[24]),3))+','              #C相电流THD
                +THDAResult+',' 
                )

        print u'性能指标数据：',OutStr

        try:
            savef=open(add+r'.\TestRecord'+u'\性能指标测试.csv','a')
            print>>savef,OutStr.encode('gbk')
            savef.close()
        except:
            print u'打开性能指标测试文件出错！'



##PF测试结果判断=============================================
    def PFTestJudge(self):
        """""""""""""""""""""""""""""""""""""""
        读取性能指标数据文档，根据通过准则判断测试结果
        """""""""""""""""""""""""""""""""""""""
        f = open( add+r'.\TestRecord'+u'\性能指标测试.csv', 'r')
        csvlist = []
        tmplist=[]
        savelist=[]
        csvlist = f.readlines()
        f.close()
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
##            print tmplist
            if tmplist[11]=='FAIL':
                savelist.append(tmplist)

        if savelist!=[]:
            print u'有',len(savelist),u'条测试不通过'
            print savelist
            return False
        else:
            print u'测试通过！'
            return True
##THD测试结果判断=============================================
    def THDTestJudge(self):
        """""""""""""""""""""""""""""""""""""""
        读取性能指标数据文档，根据通过准则判断测试结果
        """""""""""""""""""""""""""""""""""""""
        f = open( add+r'.\TestRecord'+u'\性能指标测试.csv', 'r')
        csvlist = []
        tmplist=[]
        savelist=[]
        csvlist = f.readlines()
        f.close()
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
##            print tmplist[15]
            if tmplist[15].strip('\n')=='FAIL':
                savelist.append(tmplist)

        if savelist!=[]:
            print u'有',len(savelist),u'条测试不通过'
            print savelist
            return False
        else:
            print u'测试通过！'
            return True
##转换效率测试结果判断=============================================
    def EffTestJudge(self):
        """""""""""""""""""""""""""""""""""""""
        读取性能指标数据文档，根据通过准则判断测试结果
        """""""""""""""""""""""""""""""""""""""
        f = open( add+r'.\TestRecord'+u'\性能指标测试.csv', 'r')
        csvlist = []
        tmplist=[]
        savelist=[]
        csvlist = f.readlines()
        f.close()
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
##            print tmplist
            if tmplist[7]=='FAIL':
                savelist.append(tmplist)

        if savelist!=[]:
            print u'有',len(savelist),u'条测试不通过'
            print savelist
            return False
        else:
            print u'测试通过！'
            return True        
##最大效率测试结果判断=============================================
    def MaxEffTestJudge(self,MaxEff='0.96'):
        """""""""""""""""""""""""""""""""""""""
        读取性能指标数据文档，根据通过准则判断测试结果
        """""""""""""""""""""""""""""""""""""""
        f = open( add+r'.\TestRecord'+u'\性能指标测试.csv', 'r')
        csvlist = []
        tmplist=[]
        savelist=[]
        csvlist = f.readlines()
        f.close()
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
##            print tmplist
            if tmplist[6].decode('gbk')!=u'效率':
                savelist.append(tmplist[6])

        print u'最大转换效率为：',max(savelist)
        if max(savelist)<float(MaxEff):
            print u'测试不通过'
            return False
        else:
            print u'测试通过！'
            return True 


##############################数据处理#############################               
    # 查询命令
    def queryNumeric(self, sComment,printflag=''):     
        f = open( add+r'.\python\WT1800\WT1800_command_list.csv', 'r')
        csvlist = []
        csvlist = f.readlines()
        f.close()       
        tmplist=[]

        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
            if tmplist[2] == sComment.encode('gbk'):
                sCmd = tmplist[3]   #命令
                sName= tmplist[2]   #名称
                if printflag=='':
                    print u'=======',sName.decode('gbk'),sCmd,'========='
                re=self.instr.ask(sCmd)
        return re
 
    # 设置命令
    def SetCmd(self, sComment,printflag=''):     
        f = open( add+r'.\python\WT1800\WT1800_command_list.csv', 'r')
        csvlist = []
        csvlist = f.readlines()
        f.close()
        
        tmplist=[]
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
            if tmplist[2] == sComment.encode('gbk'):
                sCmd = tmplist[3]   #命令
                sName= tmplist[2]   #名称
                if printflag=='':
                    print u'=======',sName.decode('gbk'),sCmd,'========='
                re=self.instr.write(sCmd)
        return re            

if __name__ == "__main__":
    test = WT1800()
    test.connect_WT1800_Ethernet('10.9.86.149')
    test.configItem()
##    print test.ReadWT1800Data()[25]
##    print test.ReadWT1800Data()
##    time.sleep(2)
##    test.ReadCH('SUM')
##    print test.queryNumeric(u'查询项目设置')
##    print test.queryNumeric(u'查询数据')

##    test.VoltagePrecisionTest()
##    test.CurrentPrecisionTest()
##    test.LoadEffectTest()
##    test.PowerEffectTest()
##    test.PerformanceTest()
