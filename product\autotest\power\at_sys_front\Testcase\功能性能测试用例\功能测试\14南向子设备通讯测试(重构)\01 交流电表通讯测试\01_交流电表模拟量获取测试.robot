*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
批量获取交流电表模拟量测试
    写入CSV文档    交流电表模拟量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=交流电表
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${交流电表排除模拟量信号}    ${排除列表}
    ${缺省值列表}   获取缺省值列表  ${列表1}    1
	Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    ACmeter    只读    ${缺省值列表}    2    交流电表模拟量获取测试
    ${缺省值列表}   获取缺省值列表  ${列表1}    2
	Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    ACmeter    只读    ${缺省值列表}    2    交流电表模拟量获取测试
    ${缺省值列表}   获取缺省值列表  ${列表1}    0
	Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    ACmeter    只读    ${缺省值列表}    2    交流电表模拟量获取测试
    
交流电表回路1总有功功率获取测试
    连接CSU
    设置子工具值    ACmeter    all    只读    1路总有功功率    600
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表回路1总有功功率-1     600
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表回路1总有功功率-2     600
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表回路1总有功功率-3     600
    设置子工具值    ACmeter    all    只读    1路总有功功率    220
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表回路1总有功功率-1     220
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表回路1总有功功率-2     220
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表回路1总有功功率-3     220
    设置子工具值    ACmeter    all    只读    1路总有功功率    0.1
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表回路1总有功功率-1     0.1
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表回路1总有功功率-2     0.1
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表回路1总有功功率-3     0.1

交流电表回路1电量测试
    连接CSU
    设置子工具值    ACmeter    all    只读    1路当前组合有功电能    32767
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表回路1电量-1     32767
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表回路1电量-2     32767
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表回路1电量-3     32767
    设置子工具值    ACmeter    all    只读    1路当前组合有功电能    -32767
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表回路1电量-1     -32767
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表回路1电量-2     -32767
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表回路1电量-3     -32767
    设置子工具值    ACmeter    all    只读    1路当前组合有功电能    0
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表回路1电量-1     0
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表回路1电量-2     0
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表回路1电量-3     0

交流电表回路2总有功功率获取测试
    连接CSU
    设置子工具值    ACmeter    all    只读    2路总有功功率    600
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表回路2总有功功率-1     600
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表回路2总有功功率-2     600
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表回路2总有功功率-3     600
    设置子工具值    ACmeter    all    只读    2路总有功功率    220
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表回路2总有功功率-1     220
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表回路2总有功功率-2     220
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表回路2总有功功率-3     220
    设置子工具值    ACmeter    all    只读    2路总有功功率    0.1
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表回路2总有功功率-1     0.1
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表回路2总有功功率-2     0.1
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表回路2总有功功率-3     0.1

交流电表回路2电量
    连接CSU
    设置子工具值    ACmeter    all    只读    2路当前组合有功电能    32767
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表回路2电量-1     32767
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表回路2电量-2     32767
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表回路2电量-3     32767
    设置子工具值    ACmeter    all    只读    2路当前组合有功电能    -32767
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表回路2电量-1     -32767
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表回路2电量-2     -32767
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表回路2电量-3     -32767
    设置子工具值    ACmeter    all    只读    2路当前组合有功电能    0
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表回路2电量-1     0
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表回路2电量-2     0
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表回路2电量-3     0
