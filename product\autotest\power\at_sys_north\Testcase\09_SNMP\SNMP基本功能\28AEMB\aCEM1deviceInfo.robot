*** Settings ***
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp_test关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/Web关键字_V30.robot
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
snmp_0004_交流电表软件版本X
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    设置子工具值    DMU_aemb    all    只读    软件版本    V2.55
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表软件版本-1    V2.55
    ${snmp获取值1}    获取SNMP数据_单个    aCEMSoftwareVersion1value
    should be equal    '${snmp获取值1}'    'V2.55'
    设置子工具值    DMU_aemb    all    只读    软件版本    V0.0
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表软件版本-1    V0.0
    ${snmp获取值1}    获取SNMP数据_单个    aCEMSoftwareVersion1value
    should be equal    '${snmp获取值1}'    'V0.0'
    设置子工具值    DMU_aemb    all    只读    软件版本    V1.2
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表软件版本-1    V1.2
    ${snmp获取值1}    获取SNMP数据_单个    aCEMSoftwareVersion1value
    should be equal    '${snmp获取值1}'    'V1.2'
    断开连接SNMP

snmp_0002_交流电表系统名称X
    连接CSU
    设置子工具值    DMU_aemb    all    只读    软件名称    DTSD36MAbC
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表系统名称-1    DTSD36MAbC
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${snmp获取值1}    获取SNMP数据_单个    aCEMSystemName1value
    should be equal    '${snmp获取值1}'    'DTSD36MAbC'
    设置子工具值    DMU_aemb    all    只读    软件名称    DTSD36M
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表系统名称-1    DTSD36M
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${snmp获取值1}    获取SNMP数据_单个    aCEMSystemName1value
    should be equal    '${snmp获取值1}'    'DTSD36M'
    断开连接SNMP

snmp_0004_交流电表软件日期
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    设置子工具值    DMU_aemb    all    只读    软件日期    2020-11-11
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表软件发布日期-1    2020-11-11
    ${snmp获取值1}    获取SNMP数据_单个    aCEMSoftwareReleaseDate1value
    should be equal    '${snmp获取值1}'    '2020-11-11'
    设置子工具值    DMU_aemb    all    只读    软件日期    2022-11-11
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表软件发布日期-1    2022-11-11
    ${snmp获取值1}    获取SNMP数据_单个    aCEMSoftwareReleaseDate1value
    should be equal    '${snmp获取值1}'    '2022-11-11'
    断开连接SNMP
