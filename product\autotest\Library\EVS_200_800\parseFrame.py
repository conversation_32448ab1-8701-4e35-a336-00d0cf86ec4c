#-*-coding:utf-8 -*-
#from xml.etree import ElementTree
#import os
import const
import Func
#import sys
import dealSQL

class ParseFrame():
    sendStr  = ''
    sendData = []
    recData  = []
    outStr   = []
    myDB     = None

    #---------
    def __init__(self, fileName=None):        
        if fileName:
            self.myDB = dealSQL.ConnectSql( fileName )
        else:
            print('Err, parseFrame.py, __init__')

##    def dealsql(self, fileName=None):        
##        if fileName:
##            self.myDB = dealSQL.ConnectSql( fileName )
##        else:
##            print('Err, parseFrame.py, __init__')

    def isHexString(self, string):
        newString = string.replace( ' ', '' ) #删除字符串中的空格
        charCnt = len( newString )
        if charCnt == 0:
            print(u'没有可以发送的数据')
            return False, ''
        if ( charCnt % 2 ) != 0:
            print(u'有效字符个数非偶数')
            return False, ''
        for c in newString:
            if ord( c ) < ord( '0' ) or ord( c ) > ord( 'f' ):
                print(u'存在非十六进制字符')
                return False, ''
            if ord( '9' ) < ord( c ) < ord( 'A' ):
                print(u'存在非十六进制字符')
                return False, ''
            if ord( 'F' ) < ord( c ) < ord( 'a' ):
                print(u'存在非十六进制字符')
                return False, ''
            return True, newString
    #---------
    def  sendStrToSendData(self):
        del self.sendData[:] #功能同 self.SendData.clear()
        chk,newStr = self.isHexString( self.sendStr )
        if not chk:
            return
        for i in range( 0, len(newStr), 2 ): # range(start, end, scan)
            self.sendData.append( int( newStr[i] + newStr[i+1], 16 ) )
    #---------   
##    def  calcSendData(self):        
##        self.sendStrToSendData() 
   #---------
    #数据解析:将数值val转换成 该数值对应的Name+该数值(经过取值约定的处理)
    #listFrXml-xml文件中的一条<TEXT>...</TEXT>中的内容'...'转换得到的列表
    def valToStr(self, val, varInfo):
        sDataType = varInfo[ const.lstTitleOfAllInfo.index('[dataType]') ]
        sName     = varInfo[ const.lstTitleOfAllInfo.index('[nameCn]')]
        sComment  = varInfo[ const.lstTitleOfAllInfo.index('[comment]')]
        sPre      = varInfo[ const.lstTitleOfAllInfo.index('[precision]')]
        sUnit     = varInfo[ const.lstTitleOfAllInfo.index('[unit]')]
        #sName等长度处理
##        wLenName = Func.getStrLen(sName)
##        if wLenName < 20:
##            cnt = 20-wLenName
##        else:
##            cnt = 1
##        sName = sName + '-'*cnt
        #无效值判断
        if sDataType == 'INT8U' and val == 0x8000: # 0x8000
            return sName + '无效值'
        
        if sDataType == 'INT8S' and val == -1: # 0xFF
            return sName + '无效值'
        
        if sDataType == 'INT16S' and val == -32768: # 0x8000
            return sName + '无效值'
        
        if sDataType == 'INT32U' and val == 0x80000000: #2147483648
            return sName + '无效值'                
        
##        #根据 取值约定 处理数值        
##        if sComment != '': #取值约定
##            return sName + Func.getComment(val, sComment)
##        
##        #根据 数据精度 处理数值       
##        elif int(sPre) != 0:
##            val = val / pow(10, int(sPre))
##            return sName + str(val) + ' ' + sUnit #单位
##        else:
##            return sName + str(val) + ' ' + sUnit #单位
                #根据 取值约定 处理数值
        
##        if sComment != '': #状态量显示取值约定
##            return sName + ','+Func.getComment(val, sComment)
        
        #根据 数据精度 处理数值       
        elif int(sPre) != 0:
            val = val / pow(10, float(sPre))  #处理精度
            return sName + ','+str(val) + ',' + sUnit #单位
        else:
            return sName + ','+str(val) + ',' + sUnit #单位

#=================================================================================
    #输出的字符串: outStr=[]    
    def dealFrame(self):
        del self.outStr[:]
        func = self.recData[1]
        if self.sendData[1] != func:
            print(u'parseFrame.py - dealFrame : 应答帧 & 发送帧 功能码不同')
            return
        conn = self.myDB.get_conn( self.myDB.sDbFilePath )
        sql  = 'SELECT * FROM AllInfo WHERE modbusAddr = ?'
            
        #读数字量/读线圈
        if func == 1 or func == 2:
            print(u'parseFrame.py - dealFrame : 读线圈')
            wRegAddr = self.sendData[2]*256 + self.sendData[3]        
            wRegCnt  = self.sendData[4]*256 + self.sendData[5]            
            wDataCnt = self.recData[2] #接收数据区的字节数
            if wRegCnt > wDataCnt * 8:
                print(u'parseFrame.py - dealFrame : 接收到的字节数不正确,wRegCnt != wDataCnt*8')
                return
            index = 3 #寄存器数据开始的地址
            wStartAddr = wRegAddr
            while wRegAddr < (wStartAddr + wRegCnt):                
                dat    = self.recData[index]
                index += 1
                for i in range(8):
                    val = (dat >> i) & 0x01
                    varInfo = self.myDB.requreRec( conn, sql, ( Func.getHex_0xXXXX( wRegAddr ), ) )[0]
                    self.outStr.append( self.valToStr( val, varInfo ) )
                    wRegAddr += 1
                    if wRegAddr >= (wStartAddr + wRegCnt):
                        break            
        #读模拟量/寄存器
        elif func == 3 or func == 4:
##            print(u'parseFrame.py - dealFrame : 读寄存器')
            wRegAddr = self.sendData[2]*256 + self.sendData[3]
            wRegCnt  = self.sendData[4]*256 + self.sendData[5]            
            wDataCnt = self.recData[2] #接收数据区的字节数
            if wRegCnt * 2 != wDataCnt:
                print(u'parseFrame.py - dealFrame : 接收到的字节数不正确')
                return
            index = 3 #寄存器数据开始的地址
            wStartAddr = wRegAddr
            while wRegAddr < (wStartAddr + wRegCnt):
                varInfo   = self.myDB.requreRec( conn, sql, ( Func.getHex_0xXXXX( wRegAddr ), ) )[0]
                wRegAddr += int( varInfo[ const.lstTitleOfAllInfo.index('[modbusByte]') ] / 2 )
                ucDtTp = varInfo[const.lstTitleOfAllInfo.index('[dataType]')]
                if ucDtTp == 'IPV4_ADDR' or ucDtTp == 'CHAR' or ucDtTp == 'PASSWORD' or ucDtTp == 'PHONE' or ucDtTp == 'EMAIL_ADDR' :
                    tmpStr = ''
                    for i in range( varInfo[const.lstTitleOfAllInfo.index('[modbusByte]')] ):
                        tmpStr += chr( self.recData[index] )
                        index += 1
                    self.outStr.append( varInfo[const.lstTitleOfAllInfo.index('[nameCn]')] + ',' + tmpStr )
                    
                elif ucDtTp == 'INT8U' or ucDtTp == 'INT16U' or ucDtTp == 'BOOLEAN' :
                    if index+2 > len(self.recData):
                        print(u'parseFrame.py - dealFrame : 数据好像受到干扰了1')
                        break
                    val = (self.recData[index] << 8) + self.recData[index+1]
                    index += 2
                    self.outStr.append(self.valToStr(val, varInfo) )
                    
                elif ucDtTp == 'INT8S' or ucDtTp == 'INT16S': #Modbus协议,把INT8S转换成INT16S处理
                    if index+2 > len(self.recData):
                        print(u'parseFrame.py - dealFrame : 数据好像受到干扰了2')
                        break
                    val = (self.recData[index] << 8) + self.recData[index+1]
                    index += 2
                    if val > 32767:
                        val -= 65536
                    self.outStr.append(self.valToStr(val, varInfo) )
                    
                elif ucDtTp == 'INT32U':
                    if index+4 > len(self.recData):
                        print(u'parseFrame.py - dealFrame : 数据好像受到干扰了3')
                        break
                    val = (self.recData[index] << 24) + (self.recData[index+1] << 16) + (self.recData[index+2] << 8) + self.recData[index+3]
                    index += 4
                    self.outStr.append(self.valToStr(val, varInfo) )
                    
                elif ucDtTp == 'T_DateStruct':
                    if index+6 > len(self.recData):
                        print(u'parseFrame.py - dealFrame : 数据好像受到干扰了4')
                        break
                    strTime = ''
                    strTime += (str((self.recData[index] << 8) + self.recData[index+1]) + '-' ) #year
                    index += 2
                    strTime += (str((self.recData[index] << 8) + self.recData[index+1]) + '-' ) #month
                    index += 2
                    strTime +=  str((self.recData[index] << 8) + self.recData[index+1])  #day
                    index += 2                    
                    self.outStr.append( varInfo[const.lstTitleOfAllInfo.index('[nameCn]')] + ',' + strTime)
                    
                elif ucDtTp == 'T_TimeStruct':
                    if index+12 > len(self.recData):
                        print(u'parseFrame.py - dealFrame : 数据好像受到干扰了5')
                        break
                    strTime = ''
                    strTime += (str((self.recData[index] << 8) + self.recData[index+1]) + '-' ) #year
                    index += 2
                    strTime += (str((self.recData[index] << 8) + self.recData[index+1]) + '-' ) #month
                    index += 2
                    strTime += (str((self.recData[index] << 8) + self.recData[index+1]) + ' ' ) #day
                    index += 2
                    strTime += (str((self.recData[index] << 8) + self.recData[index+1]) + ':' ) #hour
                    index += 2
                    strTime += (str((self.recData[index] << 8) + self.recData[index+1]) + ':' ) #minute
                    index += 2
                    strTime +=  str((self.recData[index] << 8) + self.recData[index+1])  #second
                    index += 2                
                    self.outStr.append( varInfo[const.lstTitleOfAllInfo.index('[nameCn]')] + ',' + strTime)

                else:
                    print(u'dealFrame.py - dealFrame 还没有处理数据类型：', ucDtTp)
        elif func == 5: #写线圈状态
##            print(u'parseFrame.py - dealFrame : 写线圈状态')
            wRegAddr = self.recData[2]*256 + self.recData[3]
            varInfo  = self.myDB.requreRec( conn, sql, ( Func.getHex_0xXXXX( wRegAddr ), ) )[0]
            val      = self.recData[4]*256 + self.recData[5]
            #处理有符号数的特殊情况
            ucDtTp = varInfo[const.lstTitleOfAllInfo.index('[dataType]')]
            if ucDtTp == 'INT8S' or ucDtTp == 'INT16S': #Modbus协议,把INT8S转换成INT16S处理
                if val > 32767:
                    val -= 65536
            #
            self.outStr.append(self.valToStr(val, varInfo) )    
        # 写一个寄存器
        # Addr Func RegAddrH RegAddrL DataH DataL CRC_L CRC_H
        # Addr Func RegAddrH RegAddrL DataH DataL CRC_L CRC_H
        elif func == 6: #写一个寄存器
##            print(u'parseFrame.py - dealFrame : 写一个寄存器')
            wRegAddr = self.recData[2]*256+self.recData[3]
            varInfo   = self.myDB.requreRec( conn, sql, ( Func.getHex_0xXXXX( wRegAddr ), ) )[0]
            val = self.recData[4]*256 + self.recData[5]
            #处理有符号数的特殊情况
            ucDtTp = varInfo[const.lstTitleOfAllInfo.index('[dataType]')]
            if ucDtTp == 'INT8S' or ucDtTp == 'INT16S': #Modbus协议,把INT8S转换成INT16S处理
                if val > 32767:
                    val -= 65536
            #
            self.outStr.append(self.valToStr(val, varInfo) )    
        #写多个寄存器
        elif func == 16: #写多个寄存器
##            print(u'parseFrame.py - dealFrame : 写多个寄存器')
            wRegAddr = self.sendData[2]*256 + self.sendData[3]
            wRegCnt  = self.sendData[4]*256 + self.sendData[5]            
            #
            index = 3 #寄存器数据开始的地址
            wStartAddr = wRegAddr
            while wRegAddr < (wStartAddr + wRegCnt):
                #print('wRegAddr 1',wRegAddr)
                varInfo   = self.myDB.requreRec( conn, sql, ( Func.getHex_0xXXXX( wRegAddr ), ) )[0]
                wRegAddr += int( int(tmpList[Const.xmlIndex.get('ModbusByteCnt')]) / 2 )
                
                self.outStr.append( varInfo[const.lstTitleOfAllInfo.index('[nameCn]')] + ' OK')
        else:
            self.outString.append(u'没有对应的解析内容,功能码:' + str(self.sendData[1]))
        return    
    #------------------
    def main(self):
        self.sendStr = '0103000000021112'
        self.sendStrToSendData()
        print(self.sendData)
        self.recData = [1,3,4,5,6,7,8,9,10]
        self.dealFrame()
        print(self.outStr)
        
#=================================================================================    
if __name__ == "__main__":
    #发送数据示例
    sample = ParseFrame( const.fileDB )
    sample.main()
      
