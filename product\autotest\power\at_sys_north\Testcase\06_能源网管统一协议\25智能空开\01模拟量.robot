*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
批量获取智能空开模拟量
    写入CSV文档    IntelAirSwitExtend模拟量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=智能直流配电单元
    @{排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SDDU2排除模拟量信号}    ${排除列表}
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    智能直流配电单元    analog data    ${SDDU2排除模拟量信号}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    @{SDDU2模拟量数据1}    create list
    @{SDDU2模拟量数据5}    create list
    @{SDDU2模拟量数据9}    create list
    @{SDDU2模拟量数据13}    create list
    FOR    ${i}    IN    @{power_sm待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        @{信号名称和序号}    split string    ${信号名称}    _
        @{信号序号和设备序号}    split string    ${信号名称和序号}[1]    -
        ${信号序号}    Set Variable    ${信号序号和设备序号}[0]
        ${信号序号}    BuiltIn.Convert To Integer    ${信号序号}
        Run Keyword IF    ${信号序号}==1    Append to List    ${SDDU2模拟量数据1}    ${i}
        Run Keyword IF    ${信号序号}==5    Append to List    ${SDDU2模拟量数据5}    ${i}
        Run Keyword IF    ${信号序号}==9    Append to List    ${SDDU2模拟量数据9}    ${i}
        Run Keyword IF    ${信号序号}==13    Append to List    ${SDDU2模拟量数据13}    ${i}
    END

    FOR    ${i}    IN   1  2  0
        FOR    ${j}    IN    1  5  9  13
            ${缺省值列表}    获取缺省值列表  ${SDDU2模拟量数据${j}}    ${i}    power_sm
            ${呼叫命令序号}    Evaluate    (${j}-1)//4+1
            ${工具命令}    Run Keyword IF    "${呼叫命令序号}"=="1"    Set Variable    呼叫
            ...    ELSE    Set Variable    呼叫${呼叫命令序号}
            Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    power_sm    IntelAirSwitExtend
            ...    ${工具命令}    ${缺省值列表}    IntelAirSwitExtend模拟量获取测试    获取SDDU模拟量
        END
    END