*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
下电配置修改测试
    连接CSU
    ${用户参数文件}    导出参数文件
    &{dict1}    create dictionary    id=pdt.power_subrack    para_name=LLVD1 Config    para_val=0
    &{dict2}    create dictionary    id=pdt.power_subrack    para_name=LLVD2 Config    para_val=0
    &{dict3}    create dictionary    id=pdt.power_subrack    para_name=BLVD Config    para_val=0
    @{配置列表}    create list    ${dict1}    ${dict2}    ${dict3}
    修改导出配置文件    ${配置列表}    ${用户参数文件}/config.xml
    压缩文件夹    ${用户参数文件}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${用户参数文件}.zip
    sleep    6m
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    0    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    ${一次下电}    获取web参数量    负载一次下电使能
    ${二次下电}    获取web参数量    负载二次下电使能
    ${电池下电}    获取web参数量    电池下电使能
    should be equal    '${一次下电}'    'None'
    should be equal    '${二次下电}'    'None'
    should be equal    '${电池下电}'    'None'
    ${用户参数文件}    导出参数文件
    &{dict1}    create dictionary    id=pdt.power_subrack    para_name=LLVD1 Config    para_val=1
    &{dict2}    create dictionary    id=pdt.power_subrack    para_name=LLVD2 Config     para_val=1
    &{dict3}    create dictionary    id=pdt.power_subrack    para_name=BLVD Config    para_val=1
    @{配置列表}    create list    ${dict1}    ${dict2}    ${dict3}
    修改导出配置文件    ${配置列表}    ${用户参数文件}/config.xml
    压缩文件夹    ${用户参数文件}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${用户参数文件}.zip
    sleep    6m
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    0    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    ${一次下电}    获取web参数量    负载一次下电使能
    ${二次下电}    获取web参数量    负载二次下电使能
    ${电池下电}    获取web参数量    电池下电使能
    should not be equal    '${一次下电}'    'None'
    should not be equal    '${二次下电}'    'None'
    should not be equal    '${电池下电}'    'None'
