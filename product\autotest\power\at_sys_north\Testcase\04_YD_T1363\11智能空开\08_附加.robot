*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
空开显示属性配置操作记录

    @{配置列表}    create list    智能空开电压    智能空开电流    智能空开功率
    FOR    ${配置参数}    IN    @{配置列表}
        显示属性配置    ${配置参数}    模拟量    web_attr=Off    gui_attr=Off
    END   
    sleep    10
    ${start_time}    ${end_time}    获取起始结束时间段    30
    @{历史事件内容}    获取web历史事件内容    ${start_time}    ${end_time}    200
    FOR    ${配置参数}    IN    @{配置列表}
        显示属性配置    ${配置参数}    模拟量    web_attr=On    gui_attr=On
    END   
    log    ${历史事件内容}

    FOR    ${配置参数}    IN    @{配置列表}
        ${操作历史flag}    set variable    0
        FOR    ${i}    IN RANGE    0    len(@{历史事件内容})
            @{操作内容}    split string    ${历史事件内容}[${i}]    ,
            ${value_str}    evaluate    '${操作内容}[3]'.replace(' ',"")
            ${是否为智能空开gui操作历史}    run keyword and return status    should contain    ${value_str}    ${配置参数}gui0->1
            ${是否为智能空开web操作历史}    run keyword and return status    should contain    ${value_str}    ${配置参数}web0->1
            ${操作历史flag}    run keyword if    ${是否为智能空开gui操作历史}    evaluate    ${操作历史flag}+1
                ...    ELSE    set variable    ${操作历史flag}
            ${操作历史flag}    run keyword if    ${是否为智能空开web操作历史}    evaluate    ${操作历史flag}+1
                ...    ELSE    set variable    ${操作历史flag}        
        END
        should be equal as numbers    ${操作历史flag}    2
    END


空开复位检测
    FOR    ${智能空开序号}    IN RANGE    1    11
        Wait Until Keyword Succeeds    10m    5    查询web信息直到值为    <<智能空开自定义容量-${智能空开序号}~0x2c001050050001>>    125
        1104/1363_南向子设备字符型/数值型厂家信息    数值    125    1363    <<获取智能空开参数 (1-10)~CID1=D3H>>    <<获取智能空开参数(1-10)~CID2=A1H>>    智能空开自定义容量-${智能空开序号}
    END
    FOR    ${智能空开序号}    IN RANGE    11    21
        Wait Until Keyword Succeeds    10m    5    查询web信息直到值为    <<智能空开自定义容量-${智能空开序号}~0x2c001050050001>>    125
        1104/1363_南向子设备字符型/数值型厂家信息    数值    125    1363    <<获取智能空开参数 (1-10)~CID1=D3H>>    <<获取智能空开参数(1-10)~CID2=A2H>>    智能空开自定义容量-${智能空开序号}
    END
    FOR    ${智能空开序号}    IN RANGE    21    31
        Wait Until Keyword Succeeds    10m    5    查询web信息直到值为    <<智能空开自定义容量-${智能空开序号}~0x2c001050050001>>    125
        1104/1363_南向子设备字符型/数值型厂家信息    数值    125    1363    <<获取智能空开参数 (1-10)~CID1=D3H>>    <<获取智能空开参数(1-10)~CID2=A6H>>    智能空开自定义容量-${智能空开序号}
    END
    FOR    ${智能空开序号}    IN RANGE    31    41
        Wait Until Keyword Succeeds    10m    5    查询web信息直到值为    <<智能空开自定义容量-${智能空开序号}~0x2c001050050001>>    125
        1104/1363_南向子设备字符型/数值型厂家信息    数值    125    1363    <<获取智能空开参数 (1-10)~CID1=D3H>>    <<获取智能空开参数(1-10)~CID2=A7H>>    智能空开自定义容量-${智能空开序号}
    END
    系统复位
    sleep    4m
    FOR    ${智能空开序号}    IN RANGE    1    11
        Wait Until Keyword Succeeds    10m    5    查询web信息直到值为    <<智能空开自定义容量-${智能空开序号}~0x2c001050050001>>    125
        1104/1363_南向子设备字符型/数值型厂家信息    数值    125    1363    <<获取智能空开参数 (1-10)~CID1=D3H>>    <<获取智能空开参数(1-10)~CID2=A1H>>    智能空开自定义容量-${智能空开序号}
    END
    FOR    ${智能空开序号}    IN RANGE    11    21
        Wait Until Keyword Succeeds    10m    5    查询web信息直到值为    <<智能空开自定义容量-${智能空开序号}~0x2c001050050001>>    125
        1104/1363_南向子设备字符型/数值型厂家信息    数值    125    1363    <<获取智能空开参数 (1-10)~CID1=D3H>>    <<获取智能空开参数(1-10)~CID2=A2H>>    智能空开自定义容量-${智能空开序号}
    END
    FOR    ${智能空开序号}    IN RANGE    21    31
        Wait Until Keyword Succeeds    10m    5    查询web信息直到值为    <<智能空开自定义容量-${智能空开序号}~0x2c001050050001>>    125
        1104/1363_南向子设备字符型/数值型厂家信息    数值    125    1363    <<获取智能空开参数 (1-10)~CID1=D3H>>    <<获取智能空开参数(1-10)~CID2=A6H>>    智能空开自定义容量-${智能空开序号}
    END
    FOR    ${智能空开序号}    IN RANGE    31    41
        Wait Until Keyword Succeeds    10m    5    查询web信息直到值为    <<智能空开自定义容量-${智能空开序号}~0x2c001050050001>>    125
        1104/1363_南向子设备字符型/数值型厂家信息    数值    125    1363    <<获取智能空开参数 (1-10)~CID1=D3H>>    <<获取智能空开参数(1-10)~CID2=A7H>>    智能空开自定义容量-${智能空开序号}
    END

空开自定义容量检测
    FOR    ${智能空开序号}    IN RANGE    1    11
        Wait Until Keyword Succeeds    10m    5    查询web信息直到值为    <<智能空开自定义容量-${智能空开序号}~0x2c001050050001>>    125
        1104/1363_南向子设备字符型/数值型厂家信息    数值    125    1363    <<获取智能空开参数 (1-10)~CID1=D3H>>    <<获取智能空开参数(1-10)~CID2=A1H>>    智能空开自定义容量-${智能空开序号}
    END
    FOR    ${智能空开序号}    IN RANGE    11    21
        Wait Until Keyword Succeeds    10m    5    查询web信息直到值为    <<智能空开自定义容量-${智能空开序号}~0x2c001050050001>>    125
        1104/1363_南向子设备字符型/数值型厂家信息    数值    125    1363    <<获取智能空开参数 (1-10)~CID1=D3H>>    <<获取智能空开参数(1-10)~CID2=A2H>>    智能空开自定义容量-${智能空开序号}
    END
    FOR    ${智能空开序号}    IN RANGE    21    31
        Wait Until Keyword Succeeds    10m    5    查询web信息直到值为    <<智能空开自定义容量-${智能空开序号}~0x2c001050050001>>    125
        1104/1363_南向子设备字符型/数值型厂家信息    数值    125    1363    <<获取智能空开参数 (1-10)~CID1=D3H>>    <<获取智能空开参数(1-10)~CID2=A6H>>    智能空开自定义容量-${智能空开序号}
    END
    FOR    ${智能空开序号}    IN RANGE    31    41
        Wait Until Keyword Succeeds    10m    5    查询web信息直到值为    <<智能空开自定义容量-${智能空开序号}~0x2c001050050001>>    125
        1104/1363_南向子设备字符型/数值型厂家信息    数值    125    1363    <<获取智能空开参数 (1-10)~CID1=D3H>>    <<获取智能空开参数(1-10)~CID2=A7H>>    智能空开自定义容量-${智能空开序号}
    END
    
    FOR    ${智能空开序号}    IN RANGE    1    21
        Wait Until Keyword Succeeds    10m    5    查询web信息直到值为    <<智能空开额定电流-${智能空开序号}~0x2c001080050001>>    125
        1104/1363_南向子设备字符型/数值型厂家信息    数值    125    1363    <<获取智能空开版本信息 (1-20)~CID1=D3H>>    <<获取智能空开版本信息(1-20)~CID2=A3H>>    智能空开额定电流-${智能空开序号}
    END
    FOR    ${智能空开序号}    IN RANGE    21    41
        Wait Until Keyword Succeeds    10m    5    查询web信息直到值为    <<智能空开额定电流-${智能空开序号}~0x2c001080050001>>    125
        1104/1363_南向子设备字符型/数值型厂家信息    数值    125    1363    <<获取智能空开版本信息 (21-40)~CID1=D3H>>    <<获取智能空开版本信息(21-40)~CID2=A4H>>    智能空开额定电流-${智能空开序号}
    END