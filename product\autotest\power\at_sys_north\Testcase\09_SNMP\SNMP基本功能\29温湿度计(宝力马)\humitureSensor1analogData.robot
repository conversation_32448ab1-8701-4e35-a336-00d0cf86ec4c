*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
批量获取温湿度传感器模拟量
    [Setup]
    写入CSV文档    温湿度传感器模拟量获取测试    信号名称    信号值    结果    备注
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=温湿度传感器
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${排除列表}    ${排除列表}
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    humitureSensor    analogData
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    1    snmp
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    snmp    DMU_WS312M1    只读    ${缺省值列表}    温湿度传感器模拟量获取测试    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    2    snmp
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    snmp    DMU_WS312M1    只读    ${缺省值列表}    温湿度传感器模拟量获取测试    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    0    snmp
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    snmp    DMU_WS312M1    只读    ${缺省值列表}    温湿度传感器模拟量获取测试    null
    [Teardown]

扩展温度（温湿度传感器）
    [Documentation]    1、通过通道复用关联扩展温度；
    ...    2、通过温湿度计关联扩展温度；
    [Setup]
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${缺省值}    获取web参数上下限范围    <<扩展温度~0x9001010030001>>
    设置子工具值    DMU_WS312M1    all    只读    温度    ${缺省值}[1]
    FOR    ${i}    IN RANGE    1    9
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    温湿度传感器环境温度    ${缺省值}[1]
        Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_${i}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    扩展温度_${i}    ${缺省值}[1]
        sleep    15
        ${snmp获取值1}    获取SNMP数据_单个    extendTemperaturevalue    ${i}
        should be equal as numbers    ${snmp获取值1}    ${缺省值}[1]
    END
    设置子工具值    DMU_WS312M1    all    只读    温度    ${缺省值}[2]
    FOR    ${i}    IN RANGE    1    9
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    温湿度传感器环境温度    ${缺省值}[2]
        Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_${i}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    扩展温度_${i}    ${缺省值}[2]
        sleep    15
        ${snmp获取值1}    获取SNMP数据_单个    extendTemperaturevalue    ${i}
        should be equal as numbers    ${snmp获取值1}    ${缺省值}[2]
    END
    设置子工具值    DMU_WS312M1    all    只读    温度    ${缺省值}[0]
    FOR    ${i}    IN RANGE    1    9
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    温湿度传感器环境温度    ${缺省值}[0]
        Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_${i}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    扩展温度_${i}    ${缺省值}[0]
        sleep    15
        ${snmp获取值1}    获取SNMP数据_单个    extendTemperaturevalue    ${i}
        should be equal as numbers    ${snmp获取值1}    ${缺省值}[0]
    END
    [Teardown]

扩展湿度（温湿度传感器)
    [Documentation]    1、通过通道复用关联扩展温度；
    ...    2、通过温湿度计关联扩展温度；
    [Setup]
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${缺省值}    获取web参数上下限范围    <<扩展湿度~0x30001010020001>>
    设置子工具值    DMU_WS312M1    all    只读    湿度    ${缺省值}[1]
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    温湿度传感器环境湿度    ${缺省值}[1]
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    扩展湿度    ${缺省值}[1]
    sleep    15
    ${WEB扩展湿度}    获取web实时数据    扩展湿度
    ${snmp获取值1}    获取SNMP数据_单个    extendHumidityvalue
    should be equal as numbers    ${WEB扩展湿度}    ${缺省值}[1]
    should be equal as numbers    ${snmp获取值1}    ${WEB扩展湿度}
    设置子工具值    DMU_WS312M1    all    只读    湿度    ${缺省值}[2]
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    温湿度传感器环境湿度    ${缺省值}[2]
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    扩展湿度    ${缺省值}[2]
    sleep    15
    ${WEB扩展湿度}    获取web实时数据    扩展湿度
    ${snmp获取值1}    获取SNMP数据_单个    extendHumidityvalue
    should be equal as numbers    ${WEB扩展湿度}    ${缺省值}[2]
    should be equal as numbers    ${snmp获取值1}    ${WEB扩展湿度}
    设置子工具值    DMU_WS312M1    all    只读    湿度    ${缺省值}[0]
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    温湿度传感器环境湿度    ${缺省值}[0]
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    扩展湿度    ${缺省值}[0]
    sleep    15
    ${WEB扩展湿度}    获取web实时数据    扩展湿度
    ${snmp获取值1}    获取SNMP数据_单个    extendHumidityvalue
    should be equal as numbers    ${WEB扩展湿度}    ${缺省值}[0]
    should be equal as numbers    ${snmp获取值1}    ${WEB扩展湿度}
    [Teardown]

