*** Settings ***
Documentation     对于输出干接点实际是否动作的判断，通过输入干接点来判断：
...               1）将输出干接点接入到输入干接点，通过判断输入干接点状态的变化来判断输出干接点是否动作； ............................................. 
...               2）8个输出干接点都并接到输入干接点1上；
...               3）必须保证每一次动作只能有一个输出干接点动作，所以每次输出干接点动作确认后，需要对此输出干接点进行恢复，才能进行下一个输出干接点的测试
...               2019053101：修改输入干接点初始状态
Suite Setup       run keywords    连接CSU
...               AND    Wait Until Keyword Succeeds    10    1    设置web所有告警干接点为指定值    0
...               AND    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay1Status}    无    无    断开
...               AND    干接点强制控制关闭
...               AND    测试用例前置条件
...               AND    短接所有do至di  ${plat.Inrelay1Status}
Suite Teardown    run keywords    连接CSU
...               AND    Wait Until Keyword Succeeds    10    1    设置web所有告警干接点为默认值
...               AND    测试用例后置条件
...               AND    重置DODI短接配置
Resource          ../../../../测试用例关键字.robot
