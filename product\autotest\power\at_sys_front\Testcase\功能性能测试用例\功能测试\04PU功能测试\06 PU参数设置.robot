*** Settings ***
Suite Setup       #Run keywords    测试用例前置条件    # AND    仅有市电条件上电
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
PU默认输出电压值设置测试
    [Setup]
    连接CSU
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    PU默认输出电压值
    ${缺省值}    获取web参数上下限范围    PU默认输出电压值
    ${可设置范围}    获取web参数可设置范围    PU默认输出电压值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    PU默认输出电压值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    PU默认输出电压值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    PU默认输出电压值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    PU默认输出电压值
        should be true    ${参数获取}==${参数设置}
    END

PU输出过压保护值设置测试
    [Setup]
    连接CSU
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    PU输出过压保护值
    ${缺省值}    获取web参数上下限范围    PU输出过压保护值
    ${可设置范围}    获取web参数可设置范围    PU输出过压保护值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    PU输出过压保护值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    PU输出过压保护值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    PU输出过压保护值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    PU输出过压保护值
        should be true    ${参数获取}==${参数设置}
    END
