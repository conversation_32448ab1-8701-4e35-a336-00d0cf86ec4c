#-*-coding:utf-8 -*-
import socket	#for sockets
import sys
from struct import *
import time
import serial
import os
import pickle #用来 保存/读取 列表的内容(腌制数据)
import Func
import re

import parseFrame
import binascii

####读取文件地址，使用RIDE时，改为ADD[1]；使用本地编译时改为ADD[0]####
ADD={0:'..\.',
     1:''
    }
add=ADD[0]
#####################################################

ERR_CODE = {1:'1:非法的功能码',
            2:'2:非法的数据地址',
            3:'3:非法的数据值',
            4:'4:服务/从机故障'
            }
########################################
#设置一个时间的格式
ISOTIME = '%Y-%m-%d %X'
########################################
class AFV_33100():
##    #声明类成员
    bNetState = False
    frame = None
    cntFramesSend = 0
    cntFramesRec  = 0
    cntBytesSend  = 0
    cntBytesRec   = 0

    listOutCsv = []
    bIsWritingFile = False
    bSendNotRec = False #解决通讯断,只发送,没有接收时的保存问题
    sock = None # telnet通讯用的变量

#=======================================================================================   
    def __init__(self, parent = None):
############################################
#全局变量
############################################        
        self.bSerialIsOpen = False
        self.receiveFrame = []
        self.frame = parseFrame.ParseFrame(add+r'.\table\modbusDB')
        self.ser = serial.Serial()
        self.bInitSocket = False #socket初始化完成标志
        self.ONOFFMode=0
##延时============================================================
    def timesleep(self,time_delay):
        time.sleep(time_delay)
        print 'time delay:',time_delay
##打开串口============================================================================================= 
    def Connect_AFV_33100_Com(self,destPort='1',destBaud='9600'):
        destPort=int(destPort)
        destBaud=int(destBaud)
        if self.bSerialIsOpen: #在线切换串口,关闭已经打开的串口
            self.bSerialIsOpen = False
            self.ser.close()
            print u'AFV_33100串口已打开'
        else:
            #初始化串口        
            try:
                self.ser = serial.Serial(port=destPort-1, baudrate=destBaud)
                self.bSerialIsOpen = True
                self.QueryCmd('02 06 00 02 00 01 E9 F9','no')#远控
                print u'打开AFV_33100串口成功！'                
            except serial.SerialException:
                print u'打开AFV_33100串口失败！'
                self.bSerialIsOpen = False

##关闭串口============================================================================================= 
    def Close_AFV_33100_Com(self):
        self.QueryCmd('02 06 00 02 00 00 28 39','no')#本地
        self.bSerialIsOpen = False
        self.ser.close()
        print u'关闭AFV_33100串口！'
#读三相电压频率===================================================================
    def QueryThreePhaseVoltage(self):
        """
        读三相电压频率;
        
        返回列表：A相电压、B相电压、C相电压、频率、输出V12线电压、输出V23线电压、输出V31线电压
        """        
        print u'查询AFV_33100的模拟量:'
        
        sendChars='02 03 02 00 00 19'#查询模拟量数据
        sendChars=sendChars+Func.getCRC16(sendChars)
        recList=self.QueryCmd(sendChars,'no')
        recList=self.ResolutionProtocol(recList)     #解析16进制模拟量数据
        print recList
        return recList 

#系统开关机###########################################################
    def OutPut_ON_OFF(self,state):
        """
        控制设备开关机输出，可选参数如下：
        ON：开机输出
        OFF：关输出断开继电器
        """
        sendChars='02 03 02 00 00 01'#查询输出运行状态
        sendChars=sendChars+Func.getCRC16(sendChars)
        ONOFF_State_List=self.QueryCmd(sendChars,'no')
        if state!='ON' and state!='OFF' :
            print u'输入命令错误，请检查！'
            return False
        if state=='ON':
            if ONOFF_State_List[0]>0:
                print u'系统已开机！'
                return
            if self.ONOFFMode==1:
                sendChars='02 06 00 01 00 01'
                sendChars=sendChars+Func.getCRC16(sendChars)            
                self.QueryCmd(sendChars,'no')#通用运行开机命令
                print u'AFV_33100交直流源开机输出，通用运行模式！'
            elif self.ONOFFMode==2:
                self.QueryCmd('02 06 00 01 00 04 D9 FA')#三相独立运行开机命令
                print u'AFV_33100交直流源开机输出，三相独立运行模式！'

        elif state=='OFF':
            if ONOFF_State_List[0]==0:
                print u'系统已关机！'
                return
            self.QueryCmd('02 06 00 01 00 00 D8 39','no')#停止命令
            print u'AFV_33100交直流源关机！'

#设置三相电压频率###########################################################
    def SetVoltage(self,volt='255.5',freq='52.1'):
        """
        设置三相模式的电压、频率
        volt:设置电压，范围：0.0-300.0V
        freq:设置频率，范围：15.00-100.00Hz
        """
        self.ONOFFMode=1#通用模式，该标志位设置为1
        volt=round(float(volt),1)
        freq=round(float(freq),1)
        print u'设置AFV_33100的三相电压:',volt,'V/',freq,'Hz'
        volt=int(volt*10)
        freq=int(freq*10)        
        Setvolt=self.IntToHexStr(volt)
        Setfreq=self.IntToHexStr(freq)      
        sendChars='02 10 01 00 00 02 04'#同时设置三相电压频率        
        sendChars=sendChars+' '+Setvolt+' '+Setfreq
        sendChars=sendChars+Func.getCRC16(sendChars)
        self.QueryCmd(sendChars,'no')

        sendChars='02 03 02 00 00 01'#查询输出运行状态
        sendChars=sendChars+Func.getCRC16(sendChars)
        ONOFF_State_List=self.QueryCmd(sendChars,'no')
        if ONOFF_State_List[0]!=0:
            sendChars='02 06 00 01 00 01'
            sendChars=sendChars+Func.getCRC16(sendChars)            
            self.QueryCmd(sendChars,'no')#设置为通用模式
            print u'通用模式运行！'
#设置三相线电压频率###########################################################
    def SetLineVoltage(self,Linevolt='380',freq='50.0'):
        """
        设置三相模式的线电压、频率
        volt:设置电压，范围：0.0-520.0V
        freq:设置频率，范围：15.00-100.00Hz
        """
        self.ONOFFMode=1#通用模式，该标志位设置为1
        Linevolt=round(float(Linevolt),1)
        freq=round(float(freq),1)
        print u'设置AFV_33100源的三相线电压:',Linevolt,'V/',freq,'Hz'
        volt=round(float(Linevolt)/1.732,1)
        volt=int(volt*10)
        freq=int(freq*10)
##        print volt,freq
        Setvolt=self.IntToHexStr(volt)
        Setfreq=self.IntToHexStr(freq)
        sendChars='02 10 01 00 00 02 04'#同时设置三相电压频率        
        sendChars=sendChars+' '+Setvolt+' '+Setfreq
        sendChars=sendChars+Func.getCRC16(sendChars)
##        print sendChars
        self.QueryCmd(sendChars,'no')        

        sendChars='02 03 02 00 00 01'#查询输出运行状态
        sendChars=sendChars+Func.getCRC16(sendChars)
        ONOFF_State_List=self.QueryCmd(sendChars,'no')
        if ONOFF_State_List[0]!=0:
            sendChars='02 06 00 01 00 01'
            sendChars=sendChars+Func.getCRC16(sendChars)            
            self.QueryCmd(sendChars,'no')#设置为通用模式
            print u'通用模式运行！'
#设置各相电压###########################################################
    def SetThreePhaseVoltage(self,volt1='221',volt2='222',volt3='223',freq='50.00'):
        """
        设置三相模式的三相电压
        volt1:设置A相电压
        volt2:设置B相电压
        volt3:设置C相电压
        freq:设置频率
        """
        self.ONOFFMode=2#三相独立设定模式，该标志位设置为2
        volt1=round(float(volt1),1)
        volt2=round(float(volt2),1)
        volt3=round(float(volt3),1)
        freq=round(float(freq),1)
        print u'设置AFV_33100源的各相电压:',volt1,'V/',volt2,'V/',volt3,'V/',freq,'Hz'
        volt1=int(volt1*10)
        volt2=int(volt2*10)
        volt3=int(volt3*10)
        freq=int(freq*10)        
        Setvolt1=self.IntToHexStr(volt1)
        Setvolt2=self.IntToHexStr(volt2)
        Setvolt3=self.IntToHexStr(volt3)
        Setfreq=self.IntToHexStr(freq)
        sendChars='02 10 01 01 00 04 08'#同时设置三相电压频率        
        sendChars=sendChars+' '+Setfreq+' '+Setvolt1+' '+Setvolt2+' '+Setvolt3
        sendChars=sendChars+Func.getCRC16(sendChars)
##        print sendChars
        self.QueryCmd(sendChars,'no')
        sendChars='02 03 02 00 00 01'#查询输出运行状态
        sendChars=sendChars+Func.getCRC16(sendChars)
        ONOFF_State_List=self.QueryCmd(sendChars,'no')
        if ONOFF_State_List[0]!=0:          
            self.QueryCmd('02 06 00 01 00 04 D9 FA','no')#设置为三相独立设定模式
            print u'三相独立设定模式运行！'
#整型数转换为16进制字符串###########################################################
    def IntToHexStr(self,tmp):
        if int(tmp)<0:
            Settmp=str(hex(65536+int(tmp))[2:].upper())
        else:
            Settmp=str(hex(int(tmp))[2:].upper())
        if len(Settmp)==1:
            Settmp='00 0'+Settmp
        elif len(Settmp)==2:
            Settmp='00 '+Settmp
        elif len(Settmp)==3:
            Settmp='0'+Settmp[0:1]+' '+Settmp[1:]
        elif len(Settmp)==4:
            Settmp=Settmp[0:2]+' '+Settmp[2:]
        else:
            print u'设置超出设置范围！'
            return False
        return Settmp
#=====================================================================
#处理发送数据：
#=====================================================================
    def sendBySerial(self, lstSendData):
        wSendLen = len( lstSendData )
        if wSendLen == 0:
            return
        bSendData = b''
        for d in lstSendData:
            bSendData += pack( 'B', d )        
        sCommType = 'Send By Serial:'        
        try:
            self.ser.write( bSendData )                            
        except:
            print u'串口发送数据失败'                
            return
        #发送成功后续处理
        self.cntBytesSend  += wSendLen
        self.cntFramesSend += 1
        return

#============================================================================
    def SendData(self,sendcomm):        
        #处理数据
        self.frame.sendStr = sendcomm
        if self.frame.sendStr == None:            
            return
        self.frame.sendStrToSendData()      
        #根据不同接口进行发送数据
        if self.bSerialIsOpen == True:
            self.sendBySerial( self.frame.sendData )
        else:
            print u'发送数据失败,没有找到通讯通道'
        return       
#============================================================================
# 处理接收数据：
#=============================================================================
    def ResolutionProtocol(self,recList,sendcmd=''):
        ResolutionList=[]
        ResolutionList.append(recList[10]/10.0)#输出A相电压
        ResolutionList.append(recList[11]/10.0)#输出B相电压
        ResolutionList.append(recList[12]/10.0)#输出C相电压
        ResolutionList.append(recList[9]/100.0)#输出频率
        ResolutionList.append(round(recList[10]/10.0*1.732,2))#输出AB线电压
        ResolutionList.append(round(recList[11]/10.0*1.732,2))#输出BC线电压
        ResolutionList.append(round(recList[12]/10.0*1.732,2))#输出AC线电压        
        return ResolutionList

#将16进制数转换为16进制字符串=============================================================================================                
    def dealRecData(self,listByteData,isSerial,sendcmd):
        if len(listByteData) == 0:
            return
        tempStr = ''
        #显示接收到的原始数据(十六进制)  
        for d in listByteData:    
            tempStr += Func.getHex_sXX(d)
##        print 'Receive:',tempStr
        return tempStr              
            
#===================================================================================================        
    def receiveData(self,sleep,sendcmd=''):
##        self.s.settimeout(5)
        receiveList =[]
        listByteData = b''
        gaga=b''
        buffer = []       
        time.sleep(sleep)#延时1s收数据
        ##接收串口数据
        if self.bSerialIsOpen:
            i = self.ser.inWaiting()
            if i != 0: #有数据
                listByteData = self.ser.read(i)
                for d in listByteData:
                    buffer.append(ord(d))
                tempList=self.dealRecData(buffer,True,sendcmd)  #第二个参数表示是否是串口数据
##                print tempList
                listByteData = b''
                buffer=[]
            else:#没有数据
                print u'没有收到数据！'
                tempList=self.dealRecData(buffer,True)
        
        return tempList
#===================================================================================================        
    def QueryCmd(self,sendcmd='',pirntflag=''):
        flag=1
        while(flag!=0):
            try:
                self.SendData(sendcmd)
                returnStr=self.receiveData(0.5,sendcmd)
                if pirntflag=='':
                    print '收到数据：',returnStr
                flag=0
            except :
                flag=flag+1  #出错了重发
                time.sleep(1)
            if flag>4:
                print u'通讯异常，请检查设备连接',sendcmd
                return False
        #将收到的16进制数据转换为10进制整型列表数据
        recStr=returnStr[10:-6].replace(' ','')#去掉帧头帧尾和空格
        recList=re.findall(r'.{4}',recStr)#使用正则表达式将字符串按长度为4分开为列表
        for i in range(len(recList)):
            recList[i]=int(recList[i],16)
        return recList

#==============================================================================    
if __name__ == "__main__":
    myapp = AFV_33100()
    myapp.Connect_AFV_33100_Com('3')
    myapp.QueryThreePhaseVoltage()
####    myapp.OutPut_ON_OFF('OFF')
##    time.sleep(1)
##    myapp.QueryThreePhaseVoltage()
##    myapp.SetVoltage('256','51')
##    myapp.SetLineVoltage('360','52.5')
##    myapp.OutPut_ON_OFF('ON')
##    time.sleep(5)
##    myapp.QueryThreePhaseVoltage()
