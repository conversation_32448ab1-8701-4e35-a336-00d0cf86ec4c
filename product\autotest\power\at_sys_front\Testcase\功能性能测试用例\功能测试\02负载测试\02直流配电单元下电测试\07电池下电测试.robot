*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
禁止下电模式下电测试(参数设置)
    设置web参数量    下电模式    禁止
    ${下电参数}    Create List    一次下电恢复回差    一次下电恢复时间    负载二次下电使能    负载二次下电电压    二次下电恢复回差    二次下电恢复时间    电池下电使能    电池下电电压    电池下电恢复回差    电池下电时间    负载二次下电SOC阈值    电池下电SOC阈值
    FOR    ${val}    IN    @{下电参数}
        ${参数值}    获取web参数量    ${val}
        ${转换值}    Convert To Boolean    ${参数值}
        should not be true    ${转换值}
    END
    ${取值范围}    获取web参数的取值约定    电池高温下电使能
    should be equal    ${取值范围}[0]    禁止
    should be equal    ${取值范围}[1]    允许
    ${取值范围}    获取web参数的取值约定    电池低温下电使能
    should be equal    ${取值范围}[0]    禁止
    should be equal    ${取值范围}[1]    允许
    [Teardown]    设置web设备参数量为默认值    下电模式

禁止下电模式下电测试(高温下电)
    设置web参数量    下电模式    禁止
    设置通道配置    SPB_J4_T1    0    1.000000    电池_1    电池温度
    设置web参数量    电池高温下电使能    允许
    ${电池高温取值范围}    获取web参数可设置范围    电池高温下电温度
    设置web参数量    电池高温下电温度    ${电池高温取值范围}[0]
    ${电池温度}    获取web实时数据    电池温度
    ${offset}    evaluate    ${电池高温取值范围}[0]-${电池温度}+1
    设置通道配置    SPB_J4_T1    ${offset}    1.000000    电池_1    电池温度
    ${开始时间}    获取系统时间
    Wait Until Keyword Succeeds    2m    10    判断告警存在    电池_1:电池温度高
    Wait Until Keyword Succeeds    6m    10    判断告警存在    电池组:电池高温下电
    ${电池控制状态}    获取web实时数据    电池高温下电控制状态
    should be equal    ${电池控制状态}    动作
    设置通道配置    SPB_J4_T1    0.000000    1.000000    电池_1    电池温度
    Wait Until Keyword Succeeds    2m    10    判断告警不存在    电池_1:电池温度高
    Wait Until Keyword Succeeds    6m    10    判断告警不存在    电池组:电池高温下电
    ${电池控制状态}    获取web实时数据    电池高温下电控制状态
    should be equal    ${电池控制状态}    恢复
    ${结束时间}    获取系统时间
    ${告警内容}    获取web历史告警内容    ${开始时间}    ${结束时间}    1    10
    ${value}    Evaluate    False
    FOR    ${val}    IN    @{告警内容}
        ${array}    Split String    ${val}    ,
        ${value}=    set variable if    '电池组:电池高温下电' in '${array}[5]'    True    False
        Run keyword if    ${value}    Exit For Loop
    END
    should be true    ${value}
    [Teardown]    设置web设备参数量为默认值    下电模式    电池高温下电使能

禁止下电模式下电测试(低温下电)
    ${电池下电模式}    获取web参数量    下电模式
    设置web参数量    下电模式    禁止
    设置通道配置    SPB_J4_T1    0    1.000000    电池_1    电池温度
    设置web参数量    电池低温下电使能    允许
    ${电池低温取值范围}    获取web参数可设置范围    电池低温下电温度
    设置web参数量    电池低温下电温度    ${电池低温取值范围}[1]
    ${电池温度}    获取web实时数据    电池温度
    ${offset}    evaluate    ${电池低温取值范围}[1]-${电池温度}-1
    设置通道配置    SPB_J4_T1    ${offset}    1.000000    电池_1    电池温度
    ${开始时间}    获取系统时间
    Wait Until Keyword Succeeds    2m    10    判断告警存在    电池_1:电池温度低
    Wait Until Keyword Succeeds    6m    10    判断告警存在    电池组:电池低温下电
    ${电池控制状态}    获取web实时数据    电池低温下电控制状态
    should be equal    ${电池控制状态}    动作
    设置通道配置    SPB_J4_T1    0.000000    1.000000    电池_1    电池温度
    Wait Until Keyword Succeeds    2m    10    判断告警不存在    电池_1:电池温度低
    Wait Until Keyword Succeeds    6m    10    判断告警不存在    电池组:电池低温下电
    ${电池控制状态}    获取web实时数据    电池低温下电控制状态
    should be equal    ${电池控制状态}    恢复
    ${结束时间}    获取系统时间
    ${告警内容}    获取web历史告警内容    ${开始时间}    ${结束时间}    1    10
    ${value}    Evaluate    False
    FOR    ${val}    IN    @{告警内容}
        ${array}    Split String    ${val}    ,
        ${value}=    set variable if    '电池组:电池低温下电' in '${array}[5]'    True    False
        Run keyword if    ${value}    Exit For Loop
    END
    should be true    ${value}
    [Teardown]    设置web设备参数量为默认值    下电模式    电池低温下电使能
