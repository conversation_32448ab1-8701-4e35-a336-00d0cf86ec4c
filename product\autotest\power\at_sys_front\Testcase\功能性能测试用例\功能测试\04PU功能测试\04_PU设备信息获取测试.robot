*** Settings ***
Suite Setup       #Run keywords    测试用例前置条件    # AND    仅有市电条件上电
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
# PU序列号获取测试
#     [Tags]    no test
#     [Setup]
#     连接CSU
#     #子设备工具模拟
#     设置子工具参数量    1    厂家信息    PU序列号    210097205436
#     设置子工具参数量    10    厂家信息    PU序列号    210097205417
#     设置子工具参数量    20    厂家信息    PU序列号    210097205423
#     设置子工具参数量    30    厂家信息    PU序列号    210097205428
#     设置子工具参数量    40    厂家信息    PU序列号    210097205438
#     sleep    5m
#     ${PU1序列号}    获取web实时数据    PU序列号-1
#     ${PU2序列号}    获取web实时数据    PU序列号-10
#     ${PU3序列号}    获取web实时数据    PU序列号-20
#     ${PU4序列号}    获取web实时数据    PU序列号-30
#     ${PU5序列号}    获取web实时数据    PU序列号-40
#     should be true    '${PU1序列号}' == '210097205436'
#     should be true    '${PU2序列号}' == '210097205417'
#     should be true    '${PU3序列号}' == '210097205423'
#     should be true    '${PU4序列号}' == '210097205428'
#     should be true    '${PU5序列号}' == '210097205438'
#     设置子工具参数量    1    厂家信息    PU序列号    210097205434
#     设置子工具参数量    10    厂家信息    PU序列号    210097205418
#     设置子工具参数量    20    厂家信息    PU序列号    210097205437
#     设置子工具参数量    30    厂家信息    PU序列号    210097205435
#     设置子工具参数量    40    厂家信息    PU序列号    210097205433
#     sleep    5m
#     ${PU1序列号}    获取web实时数据    PU序列号-1
#     ${PU2序列号}    获取web实时数据    PU序列号-10
#     ${PU3序列号}    获取web实时数据    PU序列号-20
#     ${PU4序列号}    获取web实时数据    PU序列号-30
#     ${PU5序列号}    获取web实时数据    PU序列号-40
#     should be true    '${PU1序列号}' == '210097205434'
#     should be true    '${PU2序列号}' == '210097205418'
#     should be true    '${PU3序列号}' == '210097205437'
#     should be true    '${PU4序列号}' == '210097205435'
#     should be true    '${PU5序列号}' == '210097205433'

PU软件版本获取测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具值    PU    all    版本    PU软件版本    V1.80
    sleep    2m
    ${PU1软件版本}    获取web实时数据    PU软件版本-1
    ${PU2软件版本}    获取web实时数据    PU软件版本-10
    ${PU3软件版本}    获取web实时数据    PU软件版本-20
    ${PU4软件版本}    获取web实时数据    PU软件版本-30
    ${PU5软件版本}    获取web实时数据    PU软件版本-40
    should be equal    ${PU1软件版本}    V1.80
    should be equal    ${PU2软件版本}    V1.80
    should be equal    ${PU3软件版本}    V1.80
    should be equal    ${PU4软件版本}    V1.80
    should be equal    ${PU5软件版本}    V1.80
    设置子工具值    PU    all    版本    PU软件版本    V12345
    sleep    2m
    ${PU1软件版本}    获取web实时数据    PU软件版本-1
    ${PU2软件版本}    获取web实时数据    PU软件版本-10
    ${PU3软件版本}    获取web实时数据    PU软件版本-20
    ${PU4软件版本}    获取web实时数据    PU软件版本-30
    ${PU5软件版本}    获取web实时数据    PU软件版本-40
    should be equal    ${PU1软件版本}    V12345
    should be equal    ${PU2软件版本}    V12345
    should be equal    ${PU3软件版本}    V12345
    should be equal    ${PU4软件版本}    V12345
    should be equal    ${PU5软件版本}    V12345

PU软件发布日期获取测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具值    PU    all    版本    PU软件发布日期    2018-01-02
    sleep    2m
    ${PU1软件发布日期}    获取web实时数据    PU软件发布日期-1
    ${PU2软件发布日期}    获取web实时数据    PU软件发布日期-10
    ${PU3软件发布日期}    获取web实时数据    PU软件发布日期-20
    ${PU4软件发布日期}    获取web实时数据    PU软件发布日期-30
    ${PU5软件发布日期}    获取web实时数据    PU软件发布日期-40
    should be true    '${PU1软件发布日期}' == '2018-01-02'
    should be true    '${PU2软件发布日期}' == '2018-01-02'
    should be true    '${PU3软件发布日期}' =='2018-01-02'
    should be true    '${PU4软件发布日期}' =='2018-01-02'
    should be true    '${PU5软件发布日期}' =='2018-01-02'
    设置子工具值    PU    all    版本    PU软件发布日期    2020-11-14
    sleep    2m
    ${PU1软件发布日期}    获取web实时数据    PU软件发布日期-1
    ${PU2软件发布日期}    获取web实时数据    PU软件发布日期-10
    ${PU3软件发布日期}    获取web实时数据    PU软件发布日期-20
    ${PU4软件发布日期}    获取web实时数据    PU软件发布日期-30
    ${PU5软件发布日期}    获取web实时数据    PU软件发布日期-40
    should be true    '${PU1软件发布日期}' == '2020-11-14'
    should be true    '${PU2软件发布日期}' == '2020-11-14'
    should be true    '${PU3软件发布日期}' =='2020-11-14'
    should be true    '${PU4软件发布日期}' =='2020-11-14'
    should be true    '${PU5软件发布日期}' =='2020-11-14'

PU数控平台版本获取测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具值    PU    all    版本    数控平台版本    V1234567
    sleep    2m
    ${PU1数控平台版本}    获取web实时数据    PU数控平台版本-1
    ${PU2数控平台版本}    获取web实时数据    PU数控平台版本-10
    ${PU3数控平台版本}    获取web实时数据    PU数控平台版本-20
    ${PU4数控平台版本}    获取web实时数据    PU数控平台版本-30
    ${PU5数控平台版本}    获取web实时数据    PU数控平台版本-40
    should be true    '${PU1数控平台版本}' == 'V1234567'
    should be true    '${PU2数控平台版本}' == 'V1234567'
    should be true    '${PU3数控平台版本}' == 'V1234567'
    should be true    '${PU4数控平台版本}' == 'V1234567'
    should be true    '${PU5数控平台版本}' == 'V1234567'
    设置子工具值    PU    all    版本    数控平台版本    V1.83
    sleep    2m
    ${PU1数控平台版本}    获取web实时数据    PU数控平台版本-1
    ${PU2数控平台版本}    获取web实时数据    PU数控平台版本-10
    ${PU3数控平台版本}    获取web实时数据    PU数控平台版本-20
    ${PU4数控平台版本}    获取web实时数据    PU数控平台版本-30
    ${PU5数控平台版本}    获取web实时数据    PU数控平台版本-40
    should be true    '${PU1数控平台版本}' == 'V1.83'
    should be true    '${PU2数控平台版本}' == 'V1.83'
    should be true    '${PU3数控平台版本}' == 'V1.83'
    should be true    '${PU4数控平台版本}' == 'V1.83'
    should be true    '${PU5数控平台版本}' == 'V1.83'

PU系统名称获取测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具值    PU    all    厂家信息    PU名称    ZXDT02_PU3000_V30
    sleep    2m
    ${PU1系统名称}    获取web实时数据    PU系统名称-1
    ${PU2系统名称}    获取web实时数据    PU系统名称-10
    ${PU3系统名称}    获取web实时数据    PU系统名称-20
    ${PU4系统名称}    获取web实时数据    PU系统名称-30
    ${PU5系统名称}    获取web实时数据    PU系统名称-40
    should be true    '${PU1系统名称}' == 'ZXDT02_PU3000_V30'
    should be true    '${PU2系统名称}' == 'ZXDT02_PU3000_V30'
    should be true    '${PU3系统名称}' == 'ZXDT02_PU3000_V30'
    should be true    '${PU4系统名称}' == 'ZXDT02_PU3000_V30'
    should be true    '${PU5系统名称}' == 'ZXDT02_PU3000_V30'
    设置子工具值    PU    all    厂家信息    PU名称    ZXDT02_PU3000_V31
    sleep    2m
    ${PU1系统名称}    获取web实时数据    PU系统名称-1
    ${PU2系统名称}    获取web实时数据    PU系统名称-10
    ${PU3系统名称}    获取web实时数据    PU系统名称-20
    ${PU4系统名称}    获取web实时数据    PU系统名称-30
    ${PU5系统名称}    获取web实时数据    PU系统名称-40
    should be true    '${PU1系统名称}' == 'ZXDT02_PU3000_V31'
    should be true    '${PU2系统名称}' == 'ZXDT02_PU3000_V31'
    should be true    '${PU3系统名称}' == 'ZXDT02_PU3000_V31'
    should be true    '${PU4系统名称}' == 'ZXDT02_PU3000_V31'
    should be true    '${PU5系统名称}' == 'ZXDT02_PU3000_V31'

制造商ID获取测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具值    PU    all    资产管理信息    制造商ID    1
    sleep    2m
    ${PU1制造商ID}    获取web实时数据    <<制造商ID-1~0x10001080060001>>
    ${PU2制造商ID}    获取web实时数据    <<制造商ID-10~0x10001080060001>>
    ${PU3制造商ID}    获取web实时数据    <<制造商ID-20~0x10001080060001>>
    ${PU4制造商ID}    获取web实时数据    <<制造商ID-30~0x10001080060001>>
    ${PU5制造商ID}    获取web实时数据    <<制造商ID-40~0x10001080060001>>
    should be true    ${PU1制造商ID} == 1
    should be true    ${PU2制造商ID} == 1
    should be true    ${PU3制造商ID} == 1
    should be true    ${PU4制造商ID} == 1
    should be true    ${PU5制造商ID} == 1
    设置子工具值    PU    all    资产管理信息    制造商ID    2
    sleep    2m
    ${PU1制造商ID}    获取web实时数据    <<制造商ID-1~0x10001080060001>>
    ${PU2制造商ID}    获取web实时数据    <<制造商ID-10~0x10001080060001>>
    ${PU3制造商ID}    获取web实时数据    <<制造商ID-20~0x10001080060001>>
    ${PU4制造商ID}    获取web实时数据    <<制造商ID-30~0x10001080060001>>
    ${PU5制造商ID}    获取web实时数据    <<制造商ID-40~0x10001080060001>>
    should be true    ${PU1制造商ID} == 2
    should be true    ${PU2制造商ID} == 2
    should be true    ${PU3制造商ID} == 2
    should be true    ${PU4制造商ID} == 2
    should be true    ${PU5制造商ID} == 2

制造商地址获取测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具值    PU    all    资产管理信息    制造商地址    8
    sleep    2m
    ${PU1制造商地址}    获取web实时数据    <<制造商地址-1~0x10001080070001>>
    ${PU2制造商地址}    获取web实时数据    <<制造商地址-10~0x10001080070001>>
    ${PU3制造商地址}    获取web实时数据    <<制造商地址-20~0x10001080070001>>
    ${PU4制造商地址}    获取web实时数据    <<制造商地址-30~0x10001080070001>>
    ${PU5制造商地址}    获取web实时数据    <<制造商地址-40~0x10001080070001>>
    should be true    ${PU1制造商地址} == 8
    should be true    ${PU2制造商地址} == 8
    should be true    ${PU3制造商地址} == 8
    should be true    ${PU4制造商地址} == 8
    should be true    ${PU5制造商地址} == 8
    设置子工具值    PU    all    资产管理信息    制造商地址    9
    sleep    2m
    ${PU1制造商地址}    获取web实时数据    <<制造商地址-1~0x10001080070001>>
    ${PU2制造商地址}    获取web实时数据    <<制造商地址-10~0x10001080070001>>
    ${PU3制造商地址}    获取web实时数据    <<制造商地址-20~0x10001080070001>>
    ${PU4制造商地址}    获取web实时数据    <<制造商地址-30~0x10001080070001>>
    ${PU5制造商地址}    获取web实时数据    <<制造商地址-40~0x10001080070001>>
    should be true    ${PU1制造商地址} == 9
    should be true    ${PU2制造商地址} == 9
    should be true    ${PU3制造商地址} == 9
    should be true    ${PU4制造商地址} == 9
    should be true    ${PU5制造商地址} == 9

PU条码获取测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具值    PU    all    资产管理信息    PU条码3    12522
    设置子工具值    PU    all    资产管理信息    PU条码4    50416
    设置子工具值    PU    all    资产管理信息    PU条码5g    178
    sleep    3m
    ${PU1条码}    获取web实时数据    <<PU条码-1~0x10001080080001>>
    ${PU2条码}    获取web实时数据    <<PU条码-10~0x10001080080001>>
    ${PU3条码}    获取web实时数据    <<PU条码-20~0x10001080080001>>
    ${PU4条码}    获取web实时数据    <<PU条码-30~0x10001080080001>>
    ${PU5条码}    获取web实时数据    <<PU条码-40~0x10001080080001>>
    ${PU1条码}    evaluate    str(${PU1条码})
    ${PU2条码}    evaluate    str(${PU2条码})
    ${PU3条码}    evaluate    str(${PU3条码})
    ${PU4条码}    evaluate    str(${PU4条码})
    ${PU5条码}    evaluate    str(${PU5条码})
    should be true    ${PU1条码}==210097205426
    should be true    ${PU2条码}==210097205426
    should be true    ${PU3条码}==210097205426
    should be true    ${PU4条码}==210097205426
    should be true    ${PU5条码}==210097205426

生产日期获取测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具值    PU    all    资产管理信息    生产日期年5d6g    2020
    设置子工具值    PU    all    资产管理信息    生产日期月6d    11
    设置子工具值    PU    all    资产管理信息    生产日期日7g    19
    sleep    2m
    ${PU1生产日期}    获取web实时数据    <<生产日期-1~0x10001080090001>>
    ${PU2生产日期}    获取web实时数据    <<生产日期-10~0x10001080090001>>
    ${PU3生产日期}    获取web实时数据    <<生产日期-20~0x10001080090001>>
    ${PU4生产日期}    获取web实时数据    <<生产日期-30~0x10001080090001>>
    ${PU5生产日期}    获取web实时数据    <<生产日期-40~0x10001080090001>>
    should be equal    '${PU1生产日期}'    '2020-11-19'
    should be equal    '${PU2生产日期}'    '2020-11-19'
    should be equal    '${PU3生产日期}'    '2020-11-19'
    should be equal    '${PU4生产日期}'    '2020-11-19'
    should be equal    '${PU5生产日期}'    '2020-11-19'
