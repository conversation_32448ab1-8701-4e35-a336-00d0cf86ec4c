*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
定时下电使能设置测试
    [Setup]    设置web设备参数量为默认值    定时一次下电使能    定时二次下电使能
    ${取值范围}    获取web参数的取值约定    定时一次下电使能
    should be equal    ${取值范围}[0]    禁止
    should be equal    ${取值范围}[1]    允许
    设置web参数量    定时一次下电使能    禁止
    FOR    ${val}    IN RANGE    1    6
        ${起始时刻值}    获取web参数量    定时一次下电起始时刻${val}
        ${终止时刻值}    获取web参数量    定时一次下电终止时刻${val}
        ${转换值}    Convert To Boolean    ${起始时刻值}
        should not be true    ${转换值}
        ${转换值}    Convert To Boolean    ${终止时刻值}
        should not be true    ${转换值}
    END
    设置web参数量    定时一次下电使能    允许
    FOR    ${val}    IN RANGE    1    6
        ${起始时刻范围}    获取web参数可设置范围    定时一次下电起始时刻${val}
        should be equal as strings    ${起始时刻范围}[0]    00:00
        should be equal as strings    ${起始时刻范围}[1]    23:59
        ${起始时刻范围}    获取web参数可设置范围    定时一次下电终止时刻${val}
        should be equal as strings    ${起始时刻范围}[0]    00:00
        should be equal as strings    ${起始时刻范围}[1]    23:59
    END
    ${取值范围}    获取web参数的取值约定    定时二次下电使能
    should be equal    ${取值范围}[0]    禁止
    should be equal    ${取值范围}[1]    允许
    设置web参数量    定时二次下电使能    禁止
    FOR    ${val}    IN RANGE    1    6
        ${起始时刻值}    获取web参数量    定时二次下电起始时刻${val}
        ${终止时刻值}    获取web参数量    定时二次下电终止时刻${val}
        ${转换值}    Convert To Boolean    ${起始时刻值}
        should not be true    ${转换值}
        ${转换值}    Convert To Boolean    ${终止时刻值}
        should not be true    ${转换值}
    END
    设置web参数量    定时二次下电使能    允许
    FOR    ${val}    IN RANGE    1    6
        ${起始时刻范围}    获取web参数可设置范围    定时二次下电起始时刻${val}
        should be equal as strings    ${起始时刻范围}[0]    00:00
        should be equal as strings    ${起始时刻范围}[1]    23:59
        ${起始时刻范围}    获取web参数可设置范围    定时二次下电终止时刻${val}
        should be equal as strings    ${起始时刻范围}[0]    00:00
        should be equal as strings    ${起始时刻范围}[1]    23:59
    END
    [Teardown]    设置web设备参数量为默认值    定时一次下电使能    定时二次下电使能

定时下电时段设置测试
    [Setup]    设置web设备参数量为默认值    定时一次下电使能    定时二次下电使能
    设置web参数量    定时一次下电使能    允许
    FOR    ${val}    IN RANGE    1    6
        ${起始时刻范围}    获取web参数可设置范围    定时一次下电起始时刻${val}
        should be equal as strings    ${起始时刻范围}[0]    00:00
        should be equal as strings    ${起始时刻范围}[1]    23:59
        ${终止时刻范围}    获取web参数可设置范围    定时一次下电终止时刻${val}
        should be equal as strings    ${终止时刻范围}[0]    00:00
        should be equal as strings    ${终止时刻范围}[1]    23:59
        设置web参数的范围并校验设置成功    定时一次下电起始时刻${val}
        设置web参数的范围并校验设置成功    定时一次下电终止时刻${val}
    END
    设置web参数量    定时二次下电使能    允许
    FOR    ${val}    IN RANGE    1    6
        ${起始时刻范围}    获取web参数可设置范围    定时二次下电起始时刻${val}
        should be equal as strings    ${起始时刻范围}[0]    00:00
        should be equal as strings    ${起始时刻范围}[1]    23:59
        ${终止时刻范围}    获取web参数可设置范围    定时二次下电终止时刻${val}
        should be equal as strings    ${终止时刻范围}[0]    00:00
        should be equal as strings    ${终止时刻范围}[1]    23:59
        设置web参数的范围并校验设置成功    定时二次下电起始时刻${val}
        设置web参数的范围并校验设置成功    定时二次下电终止时刻${val}
    END
    [Teardown]    设置web设备参数量为默认值    定时一次下电使能    定时二次下电使能
