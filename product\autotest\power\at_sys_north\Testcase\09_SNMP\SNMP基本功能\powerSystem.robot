*** Settings ***
Suite Setup       Run Keywords    PU测试前置条件
...               AND    整流器测试前置条件
Suite Teardown    Run Keywords    PU测试结束条件
...               AND    整流器测试结束条件

Resource          ../../../测试用例关键字.robot

*** Test Cases ***
snmp_0004_powerSystem1analogDataX
    ${比较结果}    对比数据_V2C    powerSystem1analogData
    should be true    ${比较结果}

snmp_0006_powerSystem1digitalData√
    ${比较结果}    对比数据_V2C    powerSystem1digitalData
    should be true    ${比较结果}

snmp_0008_powerSystem1alarm√
    ${比较结果}    对比告警_V2C    powerSystem1alarm
    should be true    ${比较结果}

snmp_0010_powerSystem1alarm_level√
    ${比较结果}    对比告警级别_V2C    powerSystem1alarm
    should be true    ${比较结果}

snmp_0012_powerSystem1alarm_level_write√
    Comment    ${比较结果}    批量修改告警级别_V2C    powerSystem1alarm
    Comment    should be true    ${比较结果}
    ${比较结果}    批量设置四种/五种告警级别    powerSystem1alarm
    should be true    ${比较结果}

snmp_0014_powerSystem1alarm_relay√
    ${比较结果}    对比告警干接点_V2C    powerSystem1alarm
    should be true    ${比较结果}

snmp_0016_powerSystem1alarm_relay_write√
    ${比较结果}    批量修改告警干接点_V2C    powerSystem1alarm
    should be true    ${比较结果}

snmp_0018_powerSystem1parameterX
    ${取值约定}    获取web参数的取值约定    电池配置
    ${val}    Get From Dictionary    ${取值约定}    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    ${val}
    run keyword and ignore error    Wait Until Keyword Succeeds    10    1    设置web参数量    电池应用场景    循环场景
    ${比较结果}    对比数据_V2C    powerSystem1parameter
    should be true    ${比较结果}

snmp_0020_powerSystem1parameter_writeX
    Comment    ${比较结果}    批量修改参数_V2C    powerSystem1parameter
    Comment    should be true    ${比较结果}
    ${比较结果}    SNMP批量参数设置    powerSystem1parameter
    should be true    ${比较结果}

snmp_0022_powerSystem1stasticData√
    ${比较结果}    对比数据_V2C    powerSystem1stasticData
    should be true    ${比较结果}

snmp_0024_powerSystem1deviceInfo√
    ${比较结果}    对比数据_V2C    powerSystem1deviceInfo
    should be true    ${比较结果}
