*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
批量获取PU数字量测试
    [Tags]    PMSA-NTest
    写入CSV文档    PU数字量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{缺省值0列表}    create list
    @{缺省值1列表}    create list
    @{缺省值2列表}    create list
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=digital data    INDEX_DEVICE_CH=太阳能模块
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除数字量信号}    ${排除列表}    1
    ...    ${模拟PU起始地址}
    ${缺省值列表}   获取缺省值列表  ${列表1}    1
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    pu    数字量    ${缺省值列表}    2    PU数字量获取测试
    ${缺省值列表}   获取缺省值列表  ${列表1}    2
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    pu    数字量    ${缺省值列表}    2    PU数字量获取测试
    ${缺省值列表}   获取缺省值列表  ${列表1}    0
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    pu    数字量    ${缺省值列表}    2    PU数字量获取测试

批量获取PU特殊数字量测试
    [Documentation]    通过告警量标志位置位数字量状态值。
    [Tags]    PMSA-NTest
    写入CSV文档    PU特殊数字量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=太阳能模块
    ${排除列表}    create list
	@{信号名称列表}   create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除特殊数字量信号}    ${排除列表}    1
    ...    ${模拟PU起始地址}    1
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${dict1}    Create Dictionary
        Set To Dictionary    ${dict1}     name     ${信号名称}
        Append To List       ${信号名称列表}    ${dict1}
    END
	Run Keyword And Continue On Failure    告警量置位状态量列表封装判断结果    pu    ${信号名称列表}    数字量    1    ${模拟PU起始地址}
    ...    PU特殊数字量获取测试
    Run Keyword And Continue On Failure    告警量置位状态量列表封装判断结果    pu    ${信号名称列表}    数字量    0    ${模拟PU起始地址}
    ...    PU特殊数字量获取测试
