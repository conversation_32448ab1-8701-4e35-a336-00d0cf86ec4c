*** Settings ***
Documentation     电池充电模式：0:普通/Common;1:智能/Smart
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
普通充电最大电流限制
    
    电池管理初始化
    设置电池模拟器电压模式    铅酸电池    24    5    48
    设置web参数量    电池充电电流系数    0.125    #默认0.15
    sleep    3
    ${电池组容量设置}    获取web参数量    电池组容量_1
    ${充电电流系数设置}    获取web参数量    电池充电电流系数
    ${充电电流设置}    evaluate    ${电池组容量设置}*${充电电流系数设置}
    设置web参数量    均充电压    56.4
    ${均充电压设置值}    获取web参数量    均充电压
    设置web参数量    浮充电压    53.5
    Wait Until Keyword Succeeds    2m    1    设置web控制量    启动均充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    均充
    sleep    1m
    ${当前电池电流_1}    获取web实时数据    电池电流-1
    should be true    ${当前电池电流_1}<=1.1*${充电电流设置}
    Wait Until Keyword Succeeds    2m    1    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    设置电池模拟器电压模式    铅酸电池    24    5    52.5
    [Teardown]    Run keywords    设置web参数量    电池充电电流系数    0.15
    ...    AND    设置电池模拟器电压模式    铅酸电池    24    5    52.5

普通充电时的容量变化
    
    电池管理初始化
    设置电池模拟器电压模式    铅酸电池    24    5    48
    设置web参数量    电池充电电流系数    0.125    #默认0.15
    sleep    3
    ${电池组容量设置}    获取web参数量    电池组容量_1
    ${充电电流系数设置}    获取web参数量    电池充电电流系数
    ${充电电流设置}    evaluate    ${电池组容量设置}*${充电电流系数设置}
    设置web参数量    均充电压    56.4
    ${均充电压设置值}    获取web参数量    均充电压
    设置web参数量    浮充电压    53.5
    Wait Until Keyword Succeeds    2m    1    设置web控制量    启动均充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    均充
    sleep    1m
    ${当前电池电流_1}    获取web实时数据    电池电流-1
    should be true    ${当前电池电流_1}<=1.1*${充电电流设置}
    设置web参数量    电池组容量_1    200
    sleep    5
    ${电池组容量设置}    获取web参数量    电池组容量_1
    ${充电电流设置}    evaluate    ${电池组容量设置}*${充电电流系数设置}
    sleep    1m
    ${当前电池电流_1}    获取web实时数据    电池电流-1
    should be true    ${当前电池电流_1}<=1.1*${充电电流设置}
    设置web参数量    电池组容量_1    50
    sleep    5
    ${电池组容量设置}    获取web参数量    电池组容量_1
    ${充电电流设置}    evaluate    ${电池组容量设置}*${充电电流系数设置}
    sleep    1m
    ${当前电池电流_1}    获取web实时数据    电池电流-1
    should be true    ${当前电池电流_1}<=1.1*${充电电流设置}
    Wait Until Keyword Succeeds    2m    1    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    设置电池模拟器电压模式    铅酸电池    24    5    52.5
    [Teardown]    Run keywords    设置web参数量    电池充电电流系数    0.15
    ...    AND    设置web参数量    电池组容量_1    100
    ...    AND    设置电池模拟器电压模式    铅酸电池    24    5    52.5

普通充电时的充电系数变化
    
    电池管理初始化
    设置电池模拟器电压模式    铅酸电池    24    5    48
    设置web参数量    电池充电电流系数    0.125    #默认0.15
    sleep    3
    ${电池组容量设置}    获取web参数量    电池组容量_1
    ${充电电流系数设置}    获取web参数量    电池充电电流系数
    ${充电电流设置}    evaluate    ${电池组容量设置}*${充电电流系数设置}
    设置web参数量    均充电压    56.4
    ${均充电压设置值}    获取web参数量    均充电压
    设置web参数量    浮充电压    53.5
    Wait Until Keyword Succeeds    2m    1    设置web控制量    启动均充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    均充
    sleep    1m
    ${当前电池电流_1}    获取web实时数据    电池电流-1
    should be true    ${当前电池电流_1}<=1.1*${充电电流设置}
    设置web参数量    电池充电电流系数    0.06    #默认0.15
    sleep    3
    ${充电电流系数设置}    获取web参数量    电池充电电流系数
    ${充电电流设置}    evaluate    ${电池组容量设置}*${充电电流系数设置}
    sleep    1m
    ${当前电池电流_1}    获取web实时数据    电池电流-1
    should be true    ${当前电池电流_1}<=1.1*${充电电流设置}
    设置web参数量    电池充电电流系数    0.3    #默认0.15
    sleep    3
    ${充电电流系数设置}    获取web参数量    电池充电电流系数
    ${充电电流设置}    evaluate    ${电池组容量设置}*${充电电流系数设置}
    sleep    1m
    ${当前电池电流_1}    获取web实时数据    电池电流-1
    should be true    ${当前电池电流_1}<=1.1*${充电电流设置}
    Wait Until Keyword Succeeds    2m    1    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    设置电池模拟器电压模式    铅酸电池    24    5    52.5
    [Teardown]    Run keywords    设置web参数量    电池充电电流系数    0.15
    ...    AND    设置电池模拟器电压模式    铅酸电池    24    5    52.5
