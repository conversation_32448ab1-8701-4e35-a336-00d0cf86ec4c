*** Settings ***
Suite Setup       #Run keywords    测试用例前置条件    # AND    仅有市电条件上电
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
PU休眠和唤醒测试
    [Setup]
    连接CSU
    #子设备工具模拟PU
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU休眠-1
    Wait Until Keyword Succeeds    1m    2    信号量数据值为    PU休眠状态-1    是
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU休眠-10
    Wait Until Keyword Succeeds    1m    2    信号量数据值为    PU休眠状态-10    是
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU休眠-20
    Wait Until Keyword Succeeds    1m    2    信号量数据值为    PU休眠状态-20    是
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU休眠-30
    Wait Until Keyword Succeeds    1m    2    信号量数据值为    PU休眠状态-30    是
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU休眠-40
    Wait Until Keyword Succeeds    1m    2    信号量数据值为    PU休眠状态-40    是
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU唤醒-1
    Wait Until Keyword Succeeds    1m    2    信号量数据值为    PU休眠状态-1    否
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU唤醒-10
    Wait Until Keyword Succeeds    1m    2    信号量数据值为    PU休眠状态-10    否
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU唤醒-20
    Wait Until Keyword Succeeds    1m    2    信号量数据值为    PU休眠状态-20    否
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU唤醒-30
    Wait Until Keyword Succeeds    1m    2    信号量数据值为    PU休眠状态-30    否
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU唤醒-40
    Wait Until Keyword Succeeds    1m    2    信号量数据值为    PU休眠状态-40    否

PU风扇调速允许和禁止测试
    [Setup]
    连接CSU
    #子设备工具模拟PU
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU风扇调速允许-1
    Wait Until Keyword Succeeds    1m    2    信号量数据值为    PU风扇控制状态-1    自动
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU风扇调速允许-10
    Wait Until Keyword Succeeds    1m    2    信号量数据值为    PU风扇控制状态-10    自动
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU风扇调速允许-20
    Wait Until Keyword Succeeds    1m    2    信号量数据值为    PU风扇控制状态-20    自动
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU风扇调速允许-30
    Wait Until Keyword Succeeds    1m    2    信号量数据值为    PU风扇控制状态-30    自动
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU风扇调速允许-40
    Wait Until Keyword Succeeds    1m    2    信号量数据值为    PU风扇控制状态-40    自动
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU风扇调速禁止-1
    Wait Until Keyword Succeeds    1m    2    信号量数据值为    PU风扇控制状态-1    全速
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU风扇调速禁止-10
    Wait Until Keyword Succeeds    1m    2    信号量数据值为    PU风扇控制状态-10    全速
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU风扇调速禁止-20
    Wait Until Keyword Succeeds    1m    2    信号量数据值为    PU风扇控制状态-20    全速
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU风扇调速禁止-30
    Wait Until Keyword Succeeds    1m    2    信号量数据值为    PU风扇控制状态-30    全速
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU风扇调速禁止-40
    Wait Until Keyword Succeeds    1m    2    信号量数据值为    PU风扇控制状态-40    全速
