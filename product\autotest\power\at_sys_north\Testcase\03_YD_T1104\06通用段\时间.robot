*** Settings ***
Documentation     时间设置会影响
...               电池均充周期 | 电池测试周期 | 电池检测周期 | 预约均充使能
...               需要提前预处理 将这些值全部设置为0
...               时间设置后 这些值需要恢复为默认值
...               还有个大前提:
...               - - 这些参数设置的场景 是铅酸
Suite Setup       时间设置前置条件
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
1104_0016_V3.0_1104_通用命令_时间信息1-6_1
    [Tags]    1
    [Setup]    时间设置前置条件
    ${1363数据}    通用命令_获取时间信息1-6    ${g_prtcl_1104}    <<获取时间1-6~CID1=40H>>    ${g_ver_1104}
    ${web数据}    获取系统时间
    ${对比结果}    对比时间信息    ${1363数据}    ${web数据}    1104_获取时间_1
    should be true    ${对比结果}
    ${设置时间}    通用命令_设置时间信息1-6    ${g_prtcl_1104}    <<获取时间1-6~CID1=40H>>    2019,1,2,3,4,5    ${g_ver_1104}
    ${1363数据}    通用命令_获取时间信息1-6    ${g_prtcl_1104}    <<获取时间1-6~CID1=40H>>    ${g_ver_1104}
    ${web数据}    获取系统时间
    ${对比结果}    对比时间信息    ${1363数据}    ${web数据}    1104_设置时间_1
    should be true    ${对比结果}
    ${同步时间}    同步系统时间
    [Teardown]    设置web设备参数量为默认值    电池均充周期    电池测试周期    电池检测周期    预约均充使能

1104_0018_V3.0_1104_通用命令_时间信息1-6_2
    [Tags]    1
    [Setup]    时间设置前置条件
    ${1363数据}    通用命令_获取时间信息1-6    ${g_prtcl_1104}    <<获取时间1-6~CID1=41H>>    ${g_ver_1104}
    ${web数据}    获取系统时间
    ${对比结果}    对比时间信息    ${1363数据}    ${web数据}    1104_获取时间_2
    should be true    ${对比结果}
    ${设置时间}    通用命令_设置时间信息1-6    ${g_prtcl_1104}    <<获取时间1-6~CID1=41H>>    2019,1,2,3,4,5    ${g_ver_1104}
    ${1363数据}    通用命令_获取时间信息1-6    ${g_prtcl_1104}    <<获取时间1-6~CID1=41H>>    ${g_ver_1104}
    ${web数据}    获取系统时间
    ${对比结果}    对比时间信息    ${1363数据}    ${web数据}    1104_设置时间_2
    should be true    ${对比结果}
    ${同步时间}    同步系统时间
    [Teardown]    设置web设备参数量为默认值    电池均充周期    电池测试周期    电池检测周期    预约均充使能

1104_0020_V3.0_1104_通用命令_时间信息1-6_3
    [Tags]    1
    [Setup]    时间设置前置条件
    ${1363数据}    通用命令_获取时间信息1-6    ${g_prtcl_1104}    <<获取时间1-6~CID1=42H>>    ${g_ver_1104}
    ${web数据}    获取系统时间
    ${对比结果}    对比时间信息    ${1363数据}    ${web数据}    1104_获取时间_3
    should be true    ${对比结果}
    ${设置时间}    通用命令_设置时间信息1-6    ${g_prtcl_1104}    <<获取时间1-6~CID1=42H>>    2019,1,2,3,4,5    ${g_ver_1104}
    ${1363数据}    通用命令_获取时间信息1-6    ${g_prtcl_1104}    <<获取时间1-6~CID1=42H>>    ${g_ver_1104}
    ${web数据}    获取系统时间
    ${对比结果}    对比时间信息    ${1363数据}    ${web数据}    1104_设置时间_3
    should be true    ${对比结果}
    ${同步时间}    同步系统时间
    [Teardown]    设置web设备参数量为默认值    电池均充周期    电池测试周期    电池检测周期    预约均充使能

1104_0022_V3.0_1104_通用命令_时间信息1-6_4
    [Tags]    1
    [Setup]    时间设置前置条件
    ${1363数据}    通用命令_获取时间信息1-6    ${g_prtcl_1104}    <<获取时间1-6~CID1=80H>>    ${g_ver_1104}
    ${web数据}    获取系统时间
    ${对比结果}    对比时间信息    ${1363数据}    ${web数据}    1104_获取时间_4
    should be true    ${对比结果}
    ${设置时间}    通用命令_设置时间信息1-6    ${g_prtcl_1104}    <<获取时间1-6~CID1=80H>>    2019,1,2,3,4,5    ${g_ver_1104}
    ${1363数据}    通用命令_获取时间信息1-6    ${g_prtcl_1104}    <<获取时间1-6~CID1=80H>>    ${g_ver_1104}
    ${web数据}    获取系统时间
    ${对比结果}    对比时间信息    ${1363数据}    ${web数据}    1104_设置时间_4
    should be true    ${对比结果}
    ${同步时间}    同步系统时间
    [Teardown]    设置web设备参数量为默认值    电池均充周期    电池测试周期    电池检测周期    预约均充使能
