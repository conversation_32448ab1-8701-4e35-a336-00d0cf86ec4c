*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
设置系统时间测试
    [Tags]    view
    [Setup]    测试用例前置条件
    连接CSU
    设置系统时间    2011-08-19 20:06:00
    sleep    3
    ${获取的时间}    获取系统时间
    Log    ${获取的时间}
    ${设置的时间}    Convert Date    2011-08-19 20:06:00    epoch    vi
    log    ${设置的时间}
    ${获取的时间}    Convert Date    ${获取的时间}    epoch
    log    ${获取的时间}
    should be true    ${设置的时间}<=${获取的时间}<=${设置的时间}+5
    同步系统时间

设置日期格式测试
    [Documentation]    0:年月日,1:月日年,2:日月年
    [Tags]    no test
    [Setup]    Run keywords    测试用例前置条件
    ...    AND    判断web参数是否存在    日期格式
    Wait Until Keyword Succeeds    5m    2    设置web参数量    日期格式    日月年
    ${获取日期格式}    获取web参数量    日期格式
    should be equal    ${获取日期格式}    日月年
    设置web参数量    日期格式    月日年
    ${获取日期格式}    获取web参数量    日期格式
    should be equal    ${获取日期格式}    月日年
    设置web参数量    日期格式    年月日
    ${获取日期格式}    获取web参数量    日期格式
    should be equal    ${获取日期格式}    年月日

日志保存间隔测试
    [Documentation]    此参数去掉
    [Tags]    no test
    [Setup]    Run keywords    测试用例前置条件
    ...    AND    判断web参数是否存在    日志保存间隔
    Wait Until Keyword Succeeds    5m    2    设置web参数量    历史数据保存间隔    720
    ${日志保存间隔}    获取web参数量    历史数据保存间隔
    should be true    ${日志保存间隔}==720
    设置web参数量    历史数据保存间隔    5
    ${日志保存间隔}    获取web参数量    历史数据保存间隔
    should be true    ${日志保存间隔}==5
    设置web参数量    历史数据保存间隔    1440
    ${日志保存间隔}    获取web参数量    历史数据保存间隔
    should be true    ${日志保存间隔}==1440

电池类型测试
    [Documentation]    0:普通铅酸电池/VRLA Batt;
    ...    1:快充电池/Fast Charge Batt;
    ...    2:深循环电池/Deep Cycling Batt;
    ...    3:铁锂电池/FeLiPO4 Batt;
    ...    4:高温电池/High Temp Batt;
    ...    5:TB电池/TB Batt;
    ...    6:FB电池/FB Batt
    ...    默认：0
    [Tags]    no test
    [Setup]    Run keywords    测试用例前置条件
    ...    AND    判断web参数是否存在    电池类型
    连接CSU
    ${原参数}    Wait Until Keyword Succeeds    5m    2    获取web参数量    电池类型
    ${取值约定dict}    获取web参数的取值约定    电池类型
    ${取值约定values}    Get Dictionary Values    ${取值约定dict}
    FOR    ${参数设置}    IN    @{取值约定values}
        设置web参数量    电池类型    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    电池类型
        should be equal    ${参数获取}    ${参数设置}
    END
    [Teardown]    设置web参数量    电池类型    ${原参数}

交流输入场景测试
    [Documentation]    0:市电/Mains;1:油电/Mains and DG;2:油机/DG;3:无/None
    [Setup]    测试用例前置条件
    连接CSU
    ${原参数}    Wait Until Keyword Succeeds    5m    2    获取web参数量    交流输入场景
    ${取值约定dict}    获取web参数的取值约定    交流输入场景
    ${取值约定values}    Get Dictionary Values    ${取值约定dict}
    FOR    ${参数设置}    IN    @{取值约定values}
        设置web参数量    交流输入场景    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    交流输入场景
        should be equal    ${参数获取}    ${参数设置}
    END
    [Teardown]    设置web参数量    交流输入场景    ${原参数}

