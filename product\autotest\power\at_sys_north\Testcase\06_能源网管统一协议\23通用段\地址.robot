*** Settings ***
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_获取地址
    [Tags]    pass
    ${能源网管数据}    能源网管协议_获取地址
    ${对比结果}    能源网管协议_对比地址    ${能源网管数据}    ${g_energySC_addr}    获取地址
    should be true    ${对比结果}

power_sm_0002_设置地址
    [Tags]    pass
    ${init_addr}    能源网管协议_获取地址
    ${new_addr}    Set Variable    00 03
    ${能源网管数据}    能源网管协议_设置地址    更新的设备地址    ${new_addr}    设置地址    ${init_addr}    ${SSH}
    should be true    ${能源网管数据}
    ${addr}    能源网管协议_获取地址
    ${对比结果}    能源网管协议_对比地址    ${addr}    ${new_addr}    获取地址
    should be true    ${对比结果}
    #恢复原始地址
    ${能源网管数据}    能源网管协议_设置地址    更新的设备地址    ${init_addr}    设置地址    ${new_addr}    ${SSH}
    should be true    ${能源网管数据}
    ${addr}    能源网管协议_获取地址
    ${对比结果}    能源网管协议_对比地址    ${addr}    ${init_addr}    获取地址
    should be true    ${对比结果}
