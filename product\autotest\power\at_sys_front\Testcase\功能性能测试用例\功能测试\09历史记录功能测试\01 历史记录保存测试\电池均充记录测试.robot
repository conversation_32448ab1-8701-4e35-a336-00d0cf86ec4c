*** Settings ***
Suite Setup       测试用例前置条件
Suite Teardown    测试用例后置条件
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
电池均充记录保存内容（手动产生手动退出）
    电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    市电额定有功功率    2.1
    Wait Until Keyword Succeeds    10    1    设置web参数量    市电降额系数    85
    ${开始数量}    获取web事件记录数量    电池均充记录
    run keyword if    ${开始数量}>990    删除历史记录    电池均充记录
    sleep    30
    ${开始数量}    获取web事件记录数量    电池均充记录
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动均充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    均充
    ${起始时间}    获取系统时间
    ${起始SOC}    获取web实时数据    电池组当前容量比率-1
    ${起始电池电压}    获取web实时数据    电池电压-1
    ${起始电量}    获取web实时数据    电池充电电量-1
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    80
    打开负载输出
    Wait Until Keyword Succeeds    30m    5    信号量数据值小于    电池组当前容量比率-1    100
    ${持续时间}    获取web实时数据    电池状态持续时间
    关闭负载输出
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    ${结束时间}    获取系统时间
    ${结束SOC}    获取web实时数据    电池组当前容量比率-1
    ${结束电池电压}    获取web实时数据    电池电压-1
    ${结束电量}    获取web实时数据    电池充电电量-1
    sleep    30
    ${结束数量}    获取web事件记录数量    电池均充记录
    @{均充记录内容}    获取web事件记录最新一条    电池均充记录
    Comment    @{均充记录内容}    split string    ${记录内容}    ,
    should be true    ${均充记录内容}[0] == ${结束数量}
    should be true    ${结束数量} == ${开始数量} + 1
    ${起始时间差}    subtract date from date    ${均充记录内容}[1]    ${起始时间}
    should be true    -10<${起始时间差}<10
    ${结束时间差}    subtract date from date    ${均充记录内容}[2]    ${结束时间}
    should be true    -10<${结束时间差}<10
    should be true    ${均充记录内容}[3] == ${持续时间}
    should be equal    '${均充记录内容}[4]'    '手动'
    should be equal    '${均充记录内容}[5]'    '手动'
    should be true    ${均充记录内容}[6] == ${起始SOC}
    should be true    ${均充记录内容}[7] == ${结束SOC}
    should be true    ${起始电池电压}-3 <= ${均充记录内容}[8] <= ${起始电池电压}+3
    should be true    ${结束电池电压}-3 <= ${均充记录内容}[9] <= ${结束电池电压}+3
    should be true    ${起始电量}-0.2 <= ${均充记录内容}[10] <= ${起始电量} +0.2
    should be true    ${结束电量}-0.2<= ${均充记录内容}[11] <= ${结束电量}+0.2
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    市电额定有功功率    0
    ...    AND    关闭负载输出

电池均充记录保存内容（周期均充触发且均充完成退出）
    电池管理初始化
    设置web参数量    电池均充周期    180
    设置web参数量    电池测试周期    0
    设置web参数量    电池检测周期    0
    设置web参数量    均充使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    市电额定有功功率    2.1
    Wait Until Keyword Succeeds    10    1    设置web参数量    市电降额系数    85
    sleep    5
    ${均充末期维持时间范围}    获取web参数上下限范围_有单位    均充末期维持时间
    ${均充最短时间范围}    获取web参数上下限范围_有单位    均充最短时间
    ${均充最长时间范围}    获取web参数上下限范围_有单位    均充最长时间
    ${均充末期维持时间}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间范围}[1]+1
    ...    ELSE    evaluate    ${均充末期维持时间范围}[1]+1
    ${均充最短时间}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间范围}[1]+1
    ...    ELSE    evaluate    ${均充最短时间范围}[1]+1
    ${均充最长时间}    run keyword if    '${均充最长时间范围}[3]'== 'Hour'    evaluate    ${均充最长时间范围}[1]+1
    ...    ELSE    evaluate    ${均充最长时间范围}[1]+2
    设置web参数量    均充末期维持时间    ${均充末期维持时间}
    设置web参数量    均充最短时间    ${均充最短时间}
    设置web参数量    均充最长时间    ${均充最长时间}
    ${均充末期维持时间转换后}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间}*60
    ...    ELSE    set variable    ${均充末期维持时间}
    ${均充最短时间转换后}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间}*60
    ...    ELSE    set variable    ${均充最短时间}
    ${均充最长时间转换后}    run keyword if    '${均充最长时间范围}[3]'== 'Hour'    evaluate    ${均充最长时间}*60
    ...    ELSE    set variable    ${均充最长时间}
    log    ===单位换算完成===
    ${电池均充周期}    获取web参数量    电池均充周期
    ${下次均充时间}    获取web实时数据    下次均充时间
    ${下次均充时间0}    Subtract Time From Date    ${下次均充时间}    5s    #比16:00:00差5s
    设置系统时间    ${下次均充时间0}
    sleep    8
    ${开始数量}    获取web事件记录数量    电池均充记录
    run keyword if    ${开始数量}>990    删除历史记录    电池均充记录
    sleep    30
    ${开始数量}    获取web事件记录数量    电池均充记录
    ${修改后的系统时间}    获取系统时间
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    均充
    ${下次均充时间1}    获取web实时数据    下次均充时间
    ${起始时间}    获取系统时间
    ${起始SOC}    获取web实时数据    电池组当前容量比率-1
    ${起始电池电压}    获取web实时数据    电池电压-1
    ${起始电量}    获取web实时数据    电池充电电量-1
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    80
    打开负载输出
    Wait Until Keyword Succeeds    30m    5    信号量数据值小于    电池组当前容量比率-1    100
    关闭负载输出
    ${等待时间}    evaluate    ${均充最长时间转换后}+3
    Wait Until Keyword Succeeds    ${等待时间}m    2    信号量数据值为    电池管理状态    浮充
    ${结束时间}    获取系统时间
    ${结束SOC}    获取web实时数据    电池组当前容量比率-1
    ${结束电池电压}    获取web实时数据    电池电压-1
    ${结束电量}    获取web实时数据    电池充电电量-1
    sleep    30
    ${结束数量}    获取web事件记录数量    电池均充记录
    @{均充记录内容}    获取web事件记录最新一条    电池均充记录
    Comment    @{均充记录内容}    split string    ${记录内容}    ,
    should be true    ${均充记录内容}[0] == ${结束数量}
    should be true    ${结束数量} == ${开始数量} + 1
    ${起始时间差}    subtract date from date    ${均充记录内容}[1]    ${起始时间}
    should be true    -4000<${起始时间差}<4000
    ${结束时间差}    subtract date from date    ${均充记录内容}[2]    ${结束时间}
    should be true    -4000<${结束时间差}<4000
    should be true    ${均充记录内容}[3] == ${均充最长时间}
    should be equal    '${均充记录内容}[4]'    '定期'
    should be equal    '${均充记录内容}[5]'    '最大时间'
    should be true    ${均充记录内容}[6] == ${起始SOC}
    should be true    ${均充记录内容}[7] == ${结束SOC}
    should be true    ${起始电池电压}-3 <= ${均充记录内容}[8] <= ${起始电池电压}+3
    should be true    ${结束电池电压}-3 <= ${均充记录内容}[9] <= ${结束电池电压}+3
    should be true    ${起始电量}-0.2 <= ${均充记录内容}[10] <= ${起始电量} +0.2
    should be true    ${结束电量}-0.2<= ${均充记录内容}[11] <= ${结束电量}+0.2
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池测试周期    电池均充周期    电池检测周期    均充最长时间    均充最短时间    均充末期维持时间
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    市电额定有功功率    0
    ...    AND    关闭负载输出

电池均充记录保存内容（周期均充触发且异常退出）
    电池管理初始化
    设置web参数量    电池均充周期    180
    设置web参数量    电池测试周期    0
    设置web参数量    电池检测周期    0
    设置web参数量    均充使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    市电额定有功功率    2.1
    Wait Until Keyword Succeeds    10    1    设置web参数量    市电降额系数    85
    sleep    5
    ${均充末期维持时间范围}    获取web参数上下限范围_有单位    均充末期维持时间
    ${均充最短时间范围}    获取web参数上下限范围_有单位    均充最短时间
    ${均充最长时间范围}    获取web参数上下限范围_有单位    均充最长时间
    ${均充末期维持时间}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间范围}[1]+1
    ...    ELSE    evaluate    ${均充末期维持时间范围}[1]+1
    ${均充最短时间}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间范围}[1]+1
    ...    ELSE    evaluate    ${均充最短时间范围}[1]+1
    ${均充最长时间}    run keyword if    '${均充最长时间范围}[3]'== 'Hour'    evaluate    ${均充最长时间范围}[1]+1
    ...    ELSE    evaluate    ${均充最长时间范围}[1]+2
    设置web参数量    均充末期维持时间    ${均充末期维持时间}
    设置web参数量    均充最短时间    ${均充最短时间}
    设置web参数量    均充最长时间    ${均充最长时间}
    ${均充末期维持时间转换后}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间}*60
    ...    ELSE    set variable    ${均充末期维持时间}
    ${均充最短时间转换后}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间}*60
    ...    ELSE    set variable    ${均充最短时间}
    ${均充最长时间转换后}    run keyword if    '${均充最长时间范围}[3]'== 'Hour'    evaluate    ${均充最长时间}*60
    ...    ELSE    set variable    ${均充最长时间}
    log    ===单位换算完成===
    ${电池均充周期}    获取web参数量    电池均充周期
    ${下次均充时间}    获取web实时数据    下次均充时间
    ${下次均充时间0}    Subtract Time From Date    ${下次均充时间}    5s    #比16:00:00差5s
    设置系统时间    ${下次均充时间0}
    sleep    8
    ${开始数量}    获取web事件记录数量    电池均充记录
    run keyword if    ${开始数量}>990    删除历史记录    电池均充记录
    sleep    30
    ${开始数量}    获取web事件记录数量    电池均充记录
    ${修改后的系统时间}    获取系统时间
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    均充
    ${下次均充时间1}    获取web实时数据    下次均充时间
    ${起始时间}    获取系统时间
    ${起始SOC}    获取web实时数据    电池组当前容量比率-1
    ${起始电池电压}    获取web实时数据    电池电压-1
    ${起始电量}    获取web实时数据    电池充电电量-1
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    80
    打开负载输出
    Wait Until Keyword Succeeds    30m    5    信号量数据值小于    电池组当前容量比率-1    100
    关闭负载输出
    ${等待时间}    evaluate    ${均充最长时间转换后}+3
    ${持续时间}    获取web实时数据    电池状态持续时间
    关闭负载输出
    #获取告警级别
    ${级别设置值}    获取web参数量    电池温度高
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池温度高    严重
    sleep    3
    ${级别设置值}    获取web参数量    电池温度高
    #（1）告警产生
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    10    1.6    电池_1    电池温度
    #产生电池温度高告警
    ${电池温度高阈值范围}    获取web参数上下限范围    电池温度高阈值
    ${电池温度高阈值可设置范围}    获取web参数可设置范围    电池温度高阈值
    设置web参数量    电池温度高阈值    ${电池温度高阈值可设置范围}[0]
    ${电池温度}    获取web实时数据    电池温度-1
    Wait Until Keyword Succeeds    10    1    判断告警存在    电池温度高
    ${持续时间}    获取web实时数据    电池状态持续时间
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    ${结束时间}    获取系统时间
    ${结束SOC}    获取web实时数据    电池组当前容量比率-1
    ${结束电池电压}    获取web实时数据    电池电压-1
    ${结束电量}    获取web实时数据    电池充电电量-1
    sleep    30
    ${结束数量}    获取web事件记录数量    电池均充记录
    @{均充记录内容}    获取web事件记录最新一条    电池均充记录
    Comment    @{均充记录内容}    split string    ${记录内容}    ,
    should be true    ${均充记录内容}[0] == ${结束数量}
    should be true    ${结束数量} == ${开始数量} + 1
    ${起始时间差}    subtract date from date    ${均充记录内容}[1]    ${起始时间}
    should be true    -400<${起始时间差}<400
    ${结束时间差}    subtract date from date    ${均充记录内容}[2]    ${结束时间}
    should be true    -400<${结束时间差}<400
    should be true    ${均充记录内容}[3] == ${持续时间}
    should be equal    '${均充记录内容}[4]'    '定期'
    should be equal    '${均充记录内容}[5]'    '电池温度高'
    should be true    ${均充记录内容}[6] == ${起始SOC}
    should be true    ${均充记录内容}[7] == ${结束SOC}
    should be true    ${起始电池电压}-3<= ${均充记录内容}[8] <= ${起始电池电压}+3
    should be true    ${结束电池电压}-3<= ${均充记录内容}[9] <= ${结束电池电压}+3
    should be true    ${起始电量}-0.2 <= ${均充记录内容}[10] <= ${起始电量} +0.2
    should be true    ${结束电量}-0.2<= ${均充记录内容}[11] <= ${结束电量}+0.2
    #（2）告警恢复
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    设置web参数量    电池温度高阈值    ${电池温度高阈值范围}[0]
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池温度高
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池测试周期    电池均充周期    电池检测周期    均充最长时间    均充最短时间    均充末期维持时间
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    市电额定有功功率    0
    ...    AND    设置web设备参数量为默认值    电池温度高阈值
    ...    AND    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    ...    AND    设置web参数量    电池温度高    主要
    ...    AND    关闭负载输出

电池均充记录保存内容（预约均充触发且均充完成退出）
    电池管理初始化
    设置web参数量    电池均充周期    180    #默认90
    设置web参数量    电池测试周期    0    #默认0天
    设置web参数量    电池检测周期    0    #默认0天
    设置web参数量    预约均充使能    允许    #默认0禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    市电额定有功功率    2.1
    Wait Until Keyword Succeeds    10    1    设置web参数量    市电降额系数    85
    Sleep    5
    ${预约均充时长范围}    获取web参数上下限范围_有单位    预约均充时长
    ${预约均充时长设置值}    run keyword if    '${预约均充时长范围}[3]'== 'Hour'    evaluate    ${预约均充时长范围}[1]+1
    ...    ELSE    evaluate    ${预约均充时长范围}[1]+1
    Wait Until Keyword Succeeds    10    1    设置web参数量    预约均充时长    ${预约均充时长设置值}    #默认0
    ${预约均充时长设置值转换后}    run keyword if    '${预约均充时长范围}[3]'== 'Hour'    evaluate    ${预约均充时长设置值}*60
    ...    ELSE    set variable    ${预约均充时长设置值}
    sleep    5
    ${预约均充时长}    获取web参数量    预约均充时长
    ${电池均充周期}    获取web参数量    电池均充周期
    ${下次均充日期}    获取web实时数据    下次均充时间    #比下次周期均充时间要提前，否则会进入周期均充，而无法判断是否没进预约均充
    ${设置预约均充时间}    Subtract Time From Date    ${下次均充日期}    50s    #比16:00:00差50s
    设置web参数量    预约均充日期    ${设置预约均充时间}    #默认2037-12-31
    设置系统时间    ${设置预约均充时间}
    sleep    10
    ${等待时间}    evaluate    ${预约均充时长设置值转换后}+3
    ${开始数量}    获取web事件记录数量    电池均充记录
    run keyword if    ${开始数量}>990    删除历史记录    电池均充记录
    sleep    30
    ${开始数量}    获取web事件记录数量    电池均充记录
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    均充
    ${起始时间}    获取系统时间
    ${起始SOC}    获取web实时数据    电池组当前容量比率-1
    ${起始电池电压}    获取web实时数据    电池电压-1
    ${起始电量}    获取web实时数据    电池充电电量-1
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    80
    打开负载输出
    Wait Until Keyword Succeeds    30m    5    信号量数据值小于    电池组当前容量比率-1    100
    关闭负载输出
    ${预约均充时间开始}    get time
    Wait Until Keyword Succeeds    ${等待时间}m    1    信号量数据值为    电池管理状态    浮充
    ${预约均充时间结束}    get time
    ${实际预约均充时间}    Subtract Date From Date    ${预约均充时间结束}    ${预约均充时间开始}
    ${结束时间}    获取系统时间
    ${结束SOC}    获取web实时数据    电池组当前容量比率-1
    ${结束电池电压}    获取web实时数据    电池电压-1
    ${结束电量}    获取web实时数据    电池充电电量-1
    sleep    30
    ${结束数量}    获取web事件记录数量    电池均充记录
    @{均充记录内容}    获取web事件记录最新一条    电池均充记录
    Comment    @{均充记录内容}    split string    ${记录内容}    ,
    should be true    ${均充记录内容}[0] == ${结束数量}
    should be true    ${结束数量} == ${开始数量} + 1
    ${起始时间差}    subtract date from date    ${均充记录内容}[1]    ${起始时间}
    should be true    -100<${起始时间差}<100
    ${结束时间差}    subtract date from date    ${均充记录内容}[2]    ${结束时间}
    should be true    -100<${结束时间差}<100
    should be true    ${均充记录内容}[3] == ${预约均充时长}
    should be equal    '${均充记录内容}[4]'    '预约'
    should be equal    '${均充记录内容}[5]'    '预约均充时长'
    should be true    ${均充记录内容}[6] == ${起始SOC}
    should be true    ${均充记录内容}[7] == ${结束SOC}
    should be true    ${起始电池电压}-3 <= ${均充记录内容}[8] <= ${起始电池电压}+3
    should be true    ${结束电池电压}-3 <= ${均充记录内容}[9] <= ${结束电池电压}+3
    should be true    ${起始电量}-0.2 <= ${均充记录内容}[10] <= ${起始电量} +0.2
    should be true    ${结束电量}-0.2<= ${均充记录内容}[11] <= ${结束电量}+0.2
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池测试周期    电池均充周期    电池检测周期    预约均充使能
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    市电额定有功功率    0
    ...    AND    关闭负载输出

电池均充记录保存内容（手动进入交流停电退出    
    电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    市电额定有功功率    2.1
    Wait Until Keyword Succeeds    10    1    设置web参数量    市电降额系数    85
    ${开始数量}    获取web事件记录数量    电池均充记录
    run keyword if    ${开始数量}>990    删除历史记录    电池均充记录
    sleep    30
    ${开始数量}    获取web事件记录数量    电池均充记录
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动均充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    均充
    ${起始时间}    获取系统时间
    ${起始SOC}    获取web实时数据    电池组当前容量比率-1
    ${起始电池电压}    获取web实时数据    电池电压-1
    ${起始电量}    获取web实时数据    电池充电电量-1
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    80
    打开负载输出
    Wait Until Keyword Succeeds    30m    5    信号量数据值小于    电池组当前容量比率-1    100
    关闭负载输出
    关闭交流源输出
    ${持续时间}    获取web实时数据    电池状态持续时间
    Wait Until Keyword Succeeds    3m    1    信号量数据值为    电池管理状态    系统停电
    ${结束时间}    获取系统时间
    ${结束SOC}    获取web实时数据    电池组当前容量比率-1
    ${结束电池电压}    获取web实时数据    电池电压-1
    ${结束电量}    获取web实时数据    电池充电电量-1
    sleep    30
    ${结束数量}    获取web事件记录数量    电池均充记录
    @{均充记录内容}    获取web事件记录最新一条    电池均充记录
    should be true    ${均充记录内容}[0] == ${结束数量}
    should be true    ${结束数量} == ${开始数量} + 1
    ${起始时间差}    subtract date from date    ${均充记录内容}[1]    ${起始时间}
    should be true    -10<${起始时间差}<10
    ${结束时间差}    subtract date from date    ${均充记录内容}[2]    ${结束时间}
    should be true    -10<${结束时间差}<10
    should be true    ${均充记录内容}[3] >= ${持续时间}
    should be equal    '${均充记录内容}[4]'    '手动'
    should be equal    '${均充记录内容}[5]'    '系统停电'
    should be true    ${均充记录内容}[6] == ${起始SOC}
    should be true    ${均充记录内容}[7] == ${结束SOC}
    should be true    ${起始电池电压}-3 <= ${均充记录内容}[8] <= ${起始电池电压}+3
    should be true    ${结束电池电压}-3<= ${均充记录内容}[9] <= ${结束电池电压}+3
    should be true    ${起始电量}-0.2 <= ${均充记录内容}[10] <= ${起始电量} +0.2
    should be true    ${结束电量}-0.2<= ${均充记录内容}[11] <= ${结束电量}+0.2
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    市电额定有功功率    0
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出

电池均充记录保存内容（手动进入电池组无效退出）
    电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    市电额定有功功率    2.1
    Wait Until Keyword Succeeds    10    1    设置web参数量    市电降额系数    85
    ${开始数量}    获取web事件记录数量    电池均充记录
    run keyword if    ${开始数量}>990    删除历史记录    电池均充记录
    sleep    30
    ${开始数量}    获取web事件记录数量    电池均充记录
    Wait Until Keyword Succeeds    10    1    设置web控制量    启动均充
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    均充
    ${起始时间}    获取系统时间
    ${起始SOC}    获取web实时数据    电池组当前容量比率-1
    ${起始电池电压}    获取web实时数据    电池电压-1
    ${起始电量}    获取web实时数据    电池充电电量-1
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    80
    打开负载输出
    Wait Until Keyword Succeeds    30m    5    信号量数据值小于    电池组当前容量比率-1    100
    关闭负载输出
    sleep    5
    ${持续时间}    获取web实时数据    电池状态持续时间
    ${结束时间}    获取系统时间
    ${结束SOC}    获取web实时数据    电池组当前容量比率-1
    ${结束电池电压}    获取web实时数据    电池电压-1
    ${结束电量}    获取web实时数据    电池充电电量-1
    Wait Until Keyword Succeeds    30    1s    设置web参数量    电池组容量_1    0
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    浮充
    sleep    10
    ${结束数量}    获取web事件记录数量    电池均充记录
    @{均充记录内容}    获取web事件记录最新一条    电池均充记录
    should be true    ${均充记录内容}[0] == ${结束数量}
    should be true    ${结束数量} == ${开始数量} + 1
    ${起始时间差}    subtract date from date    ${均充记录内容}[1]    ${起始时间}
    should be true    -100<${起始时间差}<100
    ${结束时间差}    subtract date from date    ${均充记录内容}[2]    ${结束时间}
    should be true    -100<${结束时间差}<100
    should be true    ${均充记录内容}[3] >= ${持续时间}
    should be equal    '${均充记录内容}[4]'    '手动'
    should be equal    '${均充记录内容}[5]'    '电池组异常'
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    市电额定有功功率    0
    ...    AND    设置web参数量    电池组容量_1    100
    ...    AND    关闭负载输出

电池均充记录保存内容（手动进入直流电压高退出）
    电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    市电额定有功功率    2.1
    Wait Until Keyword Succeeds    10    1    设置web参数量    市电降额系数    85
    ${开始数量}    获取web事件记录数量    电池均充记录
    run keyword if    ${开始数量}>990    删除历史记录    电池均充记录
    sleep    30
    ${开始数量}    获取web事件记录数量    电池均充记录
    Wait Until Keyword Succeeds    30    1    设置web控制量    启动均充
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    均充
    ${起始时间}    获取系统时间
    ${起始SOC}    获取web实时数据    电池组当前容量比率-1
    ${起始电池电压}    获取web实时数据    电池电压-1
    ${起始电量}    获取web实时数据    电池充电电量-1
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    80
    打开负载输出
    Wait Until Keyword Succeeds    30m    5    信号量数据值小于    电池组当前容量比率-1    100
    关闭负载输出
    #制造直流电压高告警，应转浮充
    ${直流电压高阈值}    获取web参数量    直流电压高阈值
    ${直流电压高阈值范围}    获取web参数上下限范围    直流电压高阈值
    ${直流电压高阈值可设置范围}    获取web参数可设置范围    直流电压高阈值
    设置web参数量    直流电压高阈值    ${直流电压高阈值可设置范围}[0]
    ${是否告警存在}    run keyword and return status    Wait Until Keyword Succeeds    2m    1    判断告警存在    直流电压高
    #若直流电压高没有出现，则调节电池电压超过告警阈值
    run keyword if    '${是否告警存在}'=='False'    向上调节电池电压    ${直流电压高阈值可设置范围}[0]
    run keyword if    '${是否告警存在}'=='False'    Wait Until Keyword Succeeds    2m    1    判断告警存在    直流电压高
    ${持续时间}    获取web实时数据    电池状态持续时间
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    浮充
    ${结束时间}    获取系统时间
    ${结束SOC}    获取web实时数据    电池组当前容量比率-1
    ${结束电池电压}    获取web实时数据    电池电压-1
    ${结束电量}    获取web实时数据    电池充电电量-1
    sleep    30
    ${结束数量}    获取web事件记录数量    电池均充记录
    @{均充记录内容}    获取web事件记录最新一条    电池均充记录
    should be true    ${均充记录内容}[0] == ${结束数量}
    should be true    ${结束数量} == ${开始数量} + 1
    ${起始时间差}    subtract date from date    ${均充记录内容}[1]    ${起始时间}
    should be true    -10<${起始时间差}<10
    ${结束时间差}    subtract date from date    ${均充记录内容}[2]    ${结束时间}
    should be true    -10<${结束时间差}<10
    should be true    ${均充记录内容}[3] >= ${持续时间}
    should be equal    '${均充记录内容}[4]'    '手动'
    should be equal    '${均充记录内容}[5]'    '直流电压高'
    should be true    ${均充记录内容}[6] == ${起始SOC}
    should be true    ${均充记录内容}[7] == ${结束SOC}
    should be true    ${起始电池电压}-3 <= ${均充记录内容}[8] <= ${起始电池电压}+3
    should be true    ${结束电池电压}-3 <= ${均充记录内容}[9] <= ${结束电池电压}+3
    should be true    ${起始电量}-0.2 <= ${均充记录内容}[10] <= ${起始电量} +0.2
    should be true    ${结束电量}-0.2<= ${均充记录内容}[11] <= ${结束电量}+0.2
    #恢复
    设置web参数量    直流电压高阈值    ${直流电压高阈值范围}[0]
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    直流电压高
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    市电额定有功功率    0
    ...    AND    设置web参数量    直流电压高阈值    ${直流电压高阈值范围}[0]
    ...    AND    关闭负载输出
    ...    AND    重置电池模拟器输出

电池均充记录保存内容（手动进入电池下电退出）
    电池管理初始化
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    交流供电状态    市电1
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    过渡
    设置web设备参数量为默认值    均充最长时间    均充最短时间    均充末期维持时间
    设置web参数量    负载一次下电使能    禁止
    设置web参数量    负载二次下电使能    禁止
    设置web参数量    电池下电使能    禁止    #默认0：禁止
    wait until keyword succeeds    10    1    设置web参数量    电池低温下电使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    市电额定有功功率    2.1
    Wait Until Keyword Succeeds    10    1    设置web参数量    市电降额系数    85
    ${开始数量}    获取web事件记录数量    电池均充记录
    run keyword if    ${开始数量}>990    删除历史记录    电池均充记录
    sleep    30
    ${开始数量}    获取web事件记录数量    电池均充记录
    ${告警级别}    获取web参数量    电池低温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    电池低温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    电池低温下电温度
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    sleep    3
    ${可设范围}    获取web参数可设置范围    电池低温下电温度
    设置web参数量    电池低温下电温度    ${可设范围}[1]
    run keyword and ignore error    设置web参数量    下电控制延时    0
    显示属性配置    电池低温下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    3
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动均充
    Wait Until Keyword Succeeds    20    1    信号量数据值为    电池管理状态    均充
    ${起始时间}    获取系统时间
    ${起始SOC}    获取web实时数据    电池组当前容量比率-1
    ${起始电池电压}    获取web实时数据    电池电压-1
    ${起始电量}    获取web实时数据    电池充电电量-1
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    80
    打开负载输出
    Wait Until Keyword Succeeds    30m    5    信号量数据值小于    电池组当前容量比率-1    100
    关闭负载输出
    ${直流电压}    获取web实时数据    直流电压
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    -10    0.001    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    ${持续时间}     获取web实时数据    电池状态持续时间
    wait until keyword succeeds    13m    1    判断告警存在    电池低温下电
    #${持续时间}    获取web实时数据    电池状态持续时间
    ${结束时间}    获取系统时间
    ${结束SOC}    获取web实时数据    电池组当前容量比率-1
    ${结束电池电压}    获取web实时数据    电池电压-1
    ${结束电量}    获取web实时数据    电池充电电量-1
    Wait Until Keyword Succeeds    13m    2    信号量数据值为    电池低温下电控制状态    动作
    Wait Until Keyword Succeeds    20    1    信号量数据值为    电池管理状态    浮充
    sleep    30
    ${结束数量}    获取web事件记录数量    电池均充记录
    @{均充记录内容}    获取web事件记录最新一条    电池均充记录
    should be true    ${均充记录内容}[0] == ${结束数量}
    should be true    ${结束数量} == ${开始数量} + 1
    ${起始时间差}    subtract date from date    ${均充记录内容}[1]    ${起始时间}
    should be true    -100<${起始时间差}<100
    ${结束时间差}    subtract date from date    ${均充记录内容}[2]    ${结束时间}
    should be true    -600<${结束时间差}<600
    should be true    ${均充记录内容}[3] >= ${持续时间}
    should be equal    '${均充记录内容}[4]'    '手动'
    should be equal    '${均充记录内容}[5]'    '电池下电'
    should be true    ${均充记录内容}[6] == ${起始SOC}
    should be true    ${均充记录内容}[7] == ${结束SOC}
    should be true    ${起始电池电压}-3 <= ${均充记录内容}[8] <= ${起始电池电压}+3
    should be true    ${结束电池电压}-3 <= ${均充记录内容}[9] <= ${结束电池电压}+3
    should be true    ${起始电量}-0.2 <= ${均充记录内容}[10] <= ${起始电量} +0.2
    should be true    ${结束电量}-0.2<= ${均充记录内容}[11] <= ${结束电量}+0.2
    #恢复
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    Wait Until Keyword Succeeds    13m    2    信号量数据值为    电池低温下电控制状态    恢复
    wait until keyword succeeds    4m    2    判断告警不存在    电池低温下电
    显示属性配置    电池低温下电控制状态    数字量    web_attr=Off    gui_attr=Off
    关闭负载输出
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池低温下电温度    电池低温下电使能    负载一次下电使能    负载二次下电使能    电池下电使能
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    市电额定有功功率    0
    ...    AND    关闭负载输出

电池均充记录保存内容（测试电压进入电池温度高退出）
    电池管理初始化
    #设置测试终止电压
    ${测试终止电压缺省值}    获取web参数上下限范围    测试终止电压
    ${测试终止电压可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${测试终止电压可设置范围}[1]
    #设置测试最大时间
    ${测试最长时间缺省值}    获取web参数上下限范围    测试最长时间
    ${测试最长时间可设置范围}    获取web参数可设置范围    测试最长时间
    设置web参数量    测试最长时间    ${测试最长时间可设置范围}[1]
    #设置测试终止容量
    ${测试终止容量缺省值}    获取web参数上下限范围_有单位    测试终止SOC阈值
    ${测试终止容量可设置范围}    获取web参数可设置范围    测试终止SOC阈值
    设置web参数量    测试终止SOC阈值    ${测试终止容量可设置范围}[0]
    ${测试失败容量阈值缺省值}    获取web参数上下限范围_有单位    测试失败SOC阈值
    ${测试失败容量阈值可设置范围}    获取web参数可设置范围    测试失败SOC阈值
    设置web参数量    测试失败SOC阈值    ${测试失败容量阈值可设置范围}[1]
    sleep    5
    Wait Until Keyword Succeeds    10    1    设置web参数量    市电额定有功功率    2.1
    Wait Until Keyword Succeeds    10    1    设置web参数量    市电降额系数    85
    ${开始数量}    获取web事件记录数量    电池均充记录
    run keyword if    ${开始数量}>990    删除历史记录    电池均充记录
    sleep    30
    ${开始数量}    获取web事件记录数量    电池均充记录
    #设置均充相关
    设置web参数量    均充使能    允许
    设置负载电压电流    53.5    30
    打开负载输出
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动测试
    Wait Until Keyword Succeeds    20    1    信号量数据值为    电池管理状态    测试
    ${测试终止SOC设置值}    获取web参数量    测试终止SOC阈值
    ${测试失败SOC设置值}    获取web参数量    测试失败SOC阈值
    ${测试终止容量转换}    run keyword if    '${测试终止容量缺省值}[3]'== 'C10'    evaluate    ${测试终止SOC设置值}*100
    ...    ELSE    evaluate    ${测试终止SOC设置值}
    ${测试失败SOC转换}    run keyword if    '${测试失败容量阈值缺省值}[3]'== 'C10'    evaluate    ${测试失败SOC设置值}*100
    ...    ELSE    evaluate    ${测试失败SOC设置值}
    ${测试终止电压}    获取web参数量    测试终止电压
    ${测试最长时间}    获取web参数量    测试最长时间
    向下调节电池电压    ${测试终止电压}
    ${起始时间}    获取系统时间
    ${起始SOC}    获取web实时数据    电池组当前容量比率-1
    ${起始电池电压}    获取web实时数据    电池电压-1
    ${起始电量}    获取web实时数据    电池充电电量-1
    ${电池测试持续时间}    获取web实时数据    电池状态持续时间
    should be true    ${测试终止容量转换}<${起始SOC}<=${测试失败SOC转换}
    should be true    ${电池测试持续时间}<${测试最长时间}
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    均充
    关闭负载输出
    向上调节电池电压    53.5
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    80
    打开负载输出
    Wait Until Keyword Succeeds    30m    5    信号量数据值小于    电池组当前容量比率-1    100
    关闭负载输出
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    电池温度高
    #获取告警级别和输出干接点设置值
    ${级别设置值}    获取web参数量    电池温度高
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池温度高    严重
    sleep    3
    ${级别设置值}    获取web参数量    电池温度高
    #（1）告警产生
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    10    1.6    电池_1    电池温度
    #产生电池温度高告警
    ${电池温度高阈值范围}    获取web参数上下限范围    电池温度高阈值
    ${电池温度高阈值可设置范围}    获取web参数可设置范围    电池温度高阈值
    设置web参数量    电池温度高阈值    ${电池温度高阈值可设置范围}[0]
    ${电池温度}    获取web实时数据    电池温度-1
    Wait Until Keyword Succeeds    10    1    判断告警存在    电池温度高
    ${持续时间}    获取web实时数据    电池状态持续时间
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    ${结束时间}    获取系统时间
    ${结束SOC}    获取web实时数据    电池组当前容量比率-1
    ${结束电池电压}    获取web实时数据    电池电压-1
    ${结束电量}    获取web实时数据    电池充电电量-1
    sleep    30
    ${结束数量}    获取web事件记录数量    电池均充记录
    @{均充记录内容}    获取web事件记录最新一条    电池均充记录
    should be true    ${均充记录内容}[0] == ${结束数量}
    should be true    ${结束数量} == ${开始数量} + 1
    ${起始时间差}    subtract date from date    ${均充记录内容}[1]    ${起始时间}
    should be true    -200<=${起始时间差}<=200
    ${结束时间差}    subtract date from date    ${均充记录内容}[2]    ${结束时间}
    should be true    -200<=${结束时间差}<=200
    should be true    ${均充记录内容}[3] >= ${持续时间}
    should be equal    '${均充记录内容}[4]'    '测试电压'
    should be equal    '${均充记录内容}[5]'    '电池温度高'
    should be true    ${均充记录内容}[6] == ${起始SOC}
    should be true    ${均充记录内容}[7] == ${结束SOC}
    should be true    ${起始电池电压}-3 <= ${均充记录内容}[8] <= ${起始电池电压}+3
    should be true    ${结束电池电压}-3 <= ${均充记录内容}[9] <= ${结束电池电压}+3
    should be true    ${起始电量}-0.2 <= ${均充记录内容}[10] <= ${起始电量} +0.2
    should be true    ${结束电量}-0.2<= ${均充记录内容}[11] <= ${结束电量}+0.2
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    设置web参数量    电池温度高阈值    ${电池温度高阈值范围}[0]
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池温度高
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    市电额定有功功率    0
    ...    AND    设置web设备参数量为默认值    测试终止电压    测试最长时间    测试终止SOC阈值    测试失败SOC阈值
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    市电额定有功功率    0
    ...    AND    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    ...    AND    重置电池模拟器输出
    ...    AND    关闭负载输出

电池均充记录保存内容(测试容量进入电池温度高退出）
    电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    20
    #设置测试终止电压
    ${测试终止电压缺省值}    获取web参数上下限范围    测试终止电压
    ${测试终止电压可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${测试终止电压可设置范围}[0]
    #设置测试最大时间
    ${测试最长时间缺省值}    获取web参数上下限范围    测试最长时间
    ${测试最长时间可设置范围}    获取web参数可设置范围    测试最长时间
    设置web参数量    测试最长时间    ${测试最长时间可设置范围}[1]
    #设置测试终止容量
    ${测试终止容量缺省值}    获取web参数上下限范围_有单位    测试终止SOC阈值
    ${测试终止容量可设置范围}    获取web参数可设置范围    测试终止SOC阈值
    设置web参数量    测试终止SOC阈值    ${测试终止容量可设置范围}[1]
    ${测试失败容量阈值缺省值}    获取web参数上下限范围_有单位    测试失败SOC阈值
    ${测试失败容量阈值可设置范围}    获取web参数可设置范围    测试失败SOC阈值
    设置web参数量    测试失败SOC阈值    ${测试失败容量阈值可设置范围}[1]
    Wait Until Keyword Succeeds    10    1    设置web参数量    市电额定有功功率    2.1
    Wait Until Keyword Succeeds    10    1    设置web参数量    市电降额系数    85
    ${开始数量}    获取web事件记录数量    电池均充记录
    run keyword if    ${开始数量}>990    删除历史记录    电池均充记录
    sleep    30
    ${开始数量}    获取web事件记录数量    电池均充记录
    sleep    5
    #设置均充相关
    设置web参数量    均充使能    允许
    缓慢设置负载电压电流    53.5    50
    打开负载输出
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动测试
    Wait Until Keyword Succeeds    20    1    信号量数据值为    电池管理状态    测试
    ${测试终止SOC设置值}    获取web参数量    测试终止SOC阈值
    ${测试失败SOC设置值}    获取web参数量    测试失败SOC阈值
    ${测试终止容量转换}    run keyword if    '${测试终止容量缺省值}[3]'== 'C10'    evaluate    ${测试终止SOC设置值}*100
    ...    ELSE    evaluate    ${测试终止SOC设置值}
    ${测试失败SOC转换}    run keyword if    '${测试失败容量阈值缺省值}[3]'== 'C10'    evaluate    ${测试失败SOC设置值}*100
    ...    ELSE    evaluate    ${测试失败SOC设置值}
    ${测试终止电压}    获取web参数量    测试终止电压
    ${测试最长时间}    获取web参数量    测试最长时间
    Wait Until Keyword Succeeds    20m    1    信号量数据值为    电池组当前容量比率-1    ${测试终止容量转换}
    ${电池测试持续时间}    获取web实时数据    电池状态持续时间
    ${起始时间}    获取系统时间
    ${起始SOC}    获取web实时数据    电池组当前容量比率-1
    ${起始电池电压}    获取web实时数据    电池电压-1
    ${起始电量}    获取web实时数据    电池充电电量-1
    should be true    ${起始电池电压}>${测试终止电压}
    should be true    ${电池测试持续时间}<${测试最长时间}
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    均充
    关闭负载输出
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    80
    打开负载输出
    Wait Until Keyword Succeeds    30m    5    信号量数据值小于    电池组当前容量比率-1    100
    关闭负载输出
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    电池温度高
    #获取告警级别和输出干接点设置值
    ${级别设置值}    获取web参数量    电池温度高
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池温度高    严重
    sleep    3
    ${级别设置值}    获取web参数量    电池温度高
    #（1）告警产生
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    10    1.6    电池_1    电池温度
    #产生电池温度高告警
    ${电池温度高阈值范围}    获取web参数上下限范围    电池温度高阈值
    ${电池温度高阈值可设置范围}    获取web参数可设置范围    电池温度高阈值
    设置web参数量    电池温度高阈值    ${电池温度高阈值可设置范围}[0]
    ${电池温度}    获取web实时数据    电池温度-1
    Wait Until Keyword Succeeds    10    1    判断告警存在    电池温度高
    ${持续时间}    获取web实时数据    电池状态持续时间
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    ${结束时间}    获取系统时间
    ${结束SOC}    获取web实时数据    电池组当前容量比率-1
    ${结束电池电压}    获取web实时数据    电池电压-1
    ${结束电量}    获取web实时数据    电池充电电量-1
    sleep    30
    ${结束数量}    获取web事件记录数量    电池均充记录
    @{均充记录内容}    获取web事件记录最新一条    电池均充记录
    should be true    ${均充记录内容}[0] == ${结束数量}
    should be true    ${结束数量} == ${开始数量} + 1
    ${起始时间差}    subtract date from date    ${均充记录内容}[1]    ${起始时间}
    should be true    -20<=${起始时间差}<=20
    ${结束时间差}    subtract date from date    ${均充记录内容}[2]    ${结束时间}
    should be true    -20<=${结束时间差}<=20
    should be true    ${均充记录内容}[3] >= ${持续时间}
    should be equal    '${均充记录内容}[4]'    '测试容量'
    should be equal    '${均充记录内容}[5]'    '电池温度高'
    should be true    ${均充记录内容}[6] == ${起始SOC}
    should be true    ${均充记录内容}[7] == ${结束SOC}
    should be true    ${起始电池电压}-5 <= ${均充记录内容}[8] <= ${起始电池电压}+5
    should be true    ${结束电池电压}-5 <= ${均充记录内容}[9] <= ${结束电池电压}+5
    should be true    ${起始电量}-0.2 <= ${均充记录内容}[10] <= ${起始电量} +0.2
    should be true    ${结束电量}-0.2<= ${均充记录内容}[11] <= ${结束电量}+0.2
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    设置web参数量    电池温度高阈值    ${电池温度高阈值范围}[0]
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池温度高
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    市电额定有功功率    0
    ...    AND    设置web设备参数量为默认值    测试终止电压    测试最长时间    测试终止SOC阈值    测试失败SOC阈值
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    100
    ...    AND    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    ...    AND    关闭负载输出

电池均充记录保存内容(测试时间进入电池温度高退出）
    电池管理初始化
    ${缺省值}    获取web参数上下限范围_有单位    测试最长时间
    ${可设置范围}    获取web参数可设置范围    测试最长时间
    ${测试最长时间设置值}    run keyword if    '${缺省值}[3]'== 'Hour'    evaluate    ${可设置范围}[0]+1
    ...    ELSE    evaluate    ${可设置范围}[0]+5
    ${测试最长时间转换后}    run keyword if    '${缺省值}[3]'== 'Hour'    evaluate    ${测试最长时间设置值}*60
    ...    ELSE    set variable    ${测试最长时间设置值}
    Comment    ${设置值}    evaluate    ${可设置范围}[0]+5
    设置web参数量    测试最长时间    ${测试最长时间设置值}
    设置web参数量    均充使能    允许
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动测试
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    测试
    Comment    sleep    5m15s
    ${等待时间}    evaluate    ${测试最长时间转换后}+2
    Wait Until Keyword Succeeds    10    1    设置web参数量    市电额定有功功率    2.1
    Wait Until Keyword Succeeds    10    1    设置web参数量    市电降额系数    85
    ${开始数量}    获取web事件记录数量    电池均充记录
    run keyword if    ${开始数量}>990    删除历史记录    电池均充记录
    sleep    30
    ${开始数量}    获取web事件记录数量    电池均充记录
    Wait Until Keyword Succeeds    ${等待时间}m    1    信号量数据值为    电池管理状态    均充
    ${起始时间}    获取系统时间
    ${起始SOC}    获取web实时数据    电池组当前容量比率-1
    ${起始电池电压}    获取web实时数据    电池电压-1
    ${起始电量}    获取web实时数据    电池充电电量-1
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    80
    打开负载输出
    Wait Until Keyword Succeeds    30m    5    信号量数据值小于    电池组当前容量比率-1    100
    关闭负载输出
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    电池温度高
    #获取告警级别和输出干接点设置值
    ${级别设置值}    获取web参数量    电池温度高
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池温度高    严重
    sleep    3
    ${级别设置值}    获取web参数量    电池温度高
    #（1）告警产生
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    10    1.6    电池_1    电池温度
    #产生电池温度高告警
    ${电池温度高阈值范围}    获取web参数上下限范围    电池温度高阈值
    ${电池温度高阈值可设置范围}    获取web参数可设置范围    电池温度高阈值
    设置web参数量    电池温度高阈值    ${电池温度高阈值可设置范围}[0]
    ${电池温度}    获取web实时数据    电池温度-1
    Wait Until Keyword Succeeds    10    1    判断告警存在    电池温度高
    ${持续时间}    获取web实时数据    电池状态持续时间
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    ${结束时间}    获取系统时间
    ${结束SOC}    获取web实时数据    电池组当前容量比率-1
    ${结束电池电压}    获取web实时数据    电池电压-1
    ${结束电量}    获取web实时数据    电池充电电量-1
    sleep    30
    ${结束数量}    获取web事件记录数量    电池均充记录
    @{均充记录内容}    获取web事件记录最新一条    电池均充记录
    should be true    ${均充记录内容}[0] == ${结束数量}
    should be true    ${结束数量} == ${开始数量} + 1
    ${起始时间差}    subtract date from date    ${均充记录内容}[1]    ${起始时间}
    should be true    -10<${起始时间差}<10
    ${结束时间差}    subtract date from date    ${均充记录内容}[2]    ${结束时间}
    should be true    -10<${结束时间差}<10
    should be true    ${均充记录内容}[3] >= ${持续时间}
    should be equal    '${均充记录内容}[4]'    '测试时间'
    should be equal    '${均充记录内容}[5]'    '电池温度高'
    should be true    ${均充记录内容}[6] == ${起始SOC}
    should be true    ${均充记录内容}[7] == ${结束SOC}
    should be true    ${起始电池电压}-5 <= ${均充记录内容}[8] <= ${起始电池电压}+5
    should be true    ${结束电池电压}-5 <= ${均充记录内容}[9] <= ${结束电池电压}+5
    should be true    ${起始电量}-0.2 <= ${均充记录内容}[10] <= ${起始电量} +0.2
    should be true    ${结束电量}-0.2<= ${均充记录内容}[11] <= ${结束电量}+0.2
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    设置web参数量    电池温度高阈值    ${电池温度高阈值范围}[0]
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池温度高
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    市电额定有功功率    0
    ...    AND    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池温度高阈值
    ...    AND    设置web参数量    测试最长时间    ${缺省值}[0]
    ...    AND    关闭负载输出

电池均充记录保存内容(系统停电来电进入手动退出）
    电池管理初始化
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动均充
    设置负载电压电流    53.5    10
    打开负载输出
    Wait Until Keyword Succeeds    20    1    信号量数据值为    电池管理状态    均充
    关闭交流源输出
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    系统停电
    关闭负载输出
    打开交流源输出
    ${开始数量}    获取web事件记录数量    电池均充记录
    run keyword if    ${开始数量}>990    删除历史记录    电池均充记录
    sleep    30
    ${开始数量}    获取web事件记录数量    电池均充记录
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    均充
    ${起始时间}    获取系统时间
    ${起始SOC}    获取web实时数据    电池组当前容量比率-1
    ${起始电池电压}    获取web实时数据    电池电压-1
    ${起始电量}    获取web实时数据    电池充电电量-1
    sleep    1m
    ${持续时间}    获取web实时数据    电池状态持续时间
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    ${结束时间}    获取系统时间
    ${结束SOC}    获取web实时数据    电池组当前容量比率-1
    ${结束电池电压}    获取web实时数据    电池电压-1
    ${结束电量}    获取web实时数据    电池充电电量-1
    sleep    30
    ${结束数量}    获取web事件记录数量    电池均充记录
    @{均充记录内容}    获取web事件记录最新一条    电池均充记录
    Comment    @{均充记录内容}    split string    ${记录内容}    ,
    should be true    ${均充记录内容}[0] == ${结束数量}
    should be true    ${结束数量} == ${开始数量} + 1
    ${起始时间差}    subtract date from date    ${均充记录内容}[1]    ${起始时间}
    should be true    -10<${起始时间差}<10
    ${结束时间差}    subtract date from date    ${均充记录内容}[2]    ${结束时间}
    should be true    -10<${结束时间差}<10
    should be true    ${均充记录内容}[3] == ${持续时间}
    should be equal    '${均充记录内容}[4]'    '均充延续'
    should be equal    '${均充记录内容}[5]'    '手动'
    should be true    ${均充记录内容}[6] == ${起始SOC}
    should be true    ${均充记录内容}[7] == ${结束SOC}
    should be true    ${起始电池电压}-3 <= ${均充记录内容}[8] <= ${起始电池电压}+3
    should be true    ${结束电池电压}-3 <= ${均充记录内容}[9] <= ${结束电池电压}+3
    should be true    ${起始电量}-0.2 <= ${均充记录内容}[10] <= ${起始电量} +0.2
    should be true    ${结束电量}-0.2<= ${均充记录内容}[11] <= ${结束电量}+0.2
    [Teardown]    Run keywords    关闭负载输出
    ...    AND    打开交流源输出

电池均充记录掉电保存
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    ${均充记录数量}    获取web事件记录数量    电池均充记录
    run keyword if    ${均充记录数量}>990    删除历史记录    电池均充记录
    测试台上下电操作    ${测试台编号}    OFF
    sleep    15
    测试台上下电操作    ${测试台编号}    ON
    实时告警刷新完成
    sleep    5
    ${均充记录数量1}    获取web事件记录数量    电池均充记录
    should be true    ${均充记录数量}==${均充记录数量1}

