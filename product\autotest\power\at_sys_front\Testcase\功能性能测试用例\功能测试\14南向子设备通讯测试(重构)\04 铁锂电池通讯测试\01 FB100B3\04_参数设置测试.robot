*** Settings ***
Suite Setup       Run keywords    设置子工具个数    smartli    1
...               AND    sleep    3m
Suite Teardown    Run keywords    设置子工具个数    smartli    ${铁锂电池组数}
...                AND    sleep    3m
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
批量设置参数测试
    写入CSV文档    FB100B3锂电池参数测试    参数名称    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=parameter    INDEX_DEVICE_CH=电池组    INDEX_OPTIONAL_ATTRIBUTE=li_batt    INDEX_DATA_TYPE=int
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除参数量信号}    ${排除列表}
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${取值约定}    Get From Dictionary    ${i}    convention
        ${设备名称}    Get From Dictionary    ${i}    device_name
        run keyword if    '${取值约定}'=='False'    Run Keyword And Continue On Failure    南向子设备数值型参数设置封装判断结果    smartli    ${信号名称}    读写    FB100B3锂电池参数测试
        run keyword if    '${取值约定}'=='True'    Run Keyword And Continue On Failure    南向子设备有取值约定型参数设置封装判断结果    smartli    ${信号名称}    读写    FB100B3锂电池参数测试
    END
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=parameter    INDEX_DEVICE_CH=电池组    INDEX_OPTIONAL_ATTRIBUTE=smart_li_batt    INDEX_DATA_TYPE=int
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除参数量信号}    ${排除列表}
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${取值约定}    Get From Dictionary    ${i}    convention
        ${设备名称}    Get From Dictionary    ${i}    device_name
        run keyword if    '${取值约定}'=='False'    Run Keyword And Continue On Failure    南向子设备数值型参数设置封装判断结果    smartli    ${信号名称}    读写    FB100B3锂电池参数测试
        run keyword if    '${取值约定}'=='True'    Run Keyword And Continue On Failure    南向子设备有取值约定型参数设置封装判断结果    smartli    ${信号名称}    读写    FB100B3锂电池参数测试
    END
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=parameter    INDEX_DEVICE_CH=电池组    INDEX_OPTIONAL_ATTRIBUTE=smart_li_batt_fb100b3    INDEX_DATA_TYPE=int
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除参数量信号}    ${排除列表}
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${取值约定}    Get From Dictionary    ${i}    convention
        ${设备名称}    Get From Dictionary    ${i}    device_name
        run keyword if    '${取值约定}'=='False'    Run Keyword And Continue On Failure    南向子设备数值型参数设置封装判断结果    smartli    ${信号名称}    读写    FB100B3锂电池参数测试
        run keyword if    '${取值约定}'=='True'    Run Keyword And Continue On Failure    南向子设备有取值约定型参数设置封装判断结果    smartli    ${信号名称}    读写    FB100B3锂电池参数测试
    END
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=parameter    INDEX_DEVICE_CH=电池组    INDEX_OPTIONAL_ATTRIBUTE=li_batt    INDEX_DATA_TYPE=float
    ${排除列表}    create list
    ${列表2}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除参数量信号}    ${排除列表}
    FOR    ${i}    IN    @{列表2}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${取值约定}    Get From Dictionary    ${i}    convention
        ${设备名称}    Get From Dictionary    ${i}    device_name
        run keyword if    '${取值约定}'=='False'    Run Keyword And Continue On Failure    南向子设备数值型参数设置封装判断结果    smartli    ${信号名称}    读写    FB100B3锂电池参数测试
        run keyword if    '${取值约定}'=='True'    Run Keyword And Continue On Failure    南向子设备有取值约定型参数设置封装判断结果    smartli    ${信号名称}    读写    FB100B3锂电池参数测试
    END
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=parameter    INDEX_DEVICE_CH=电池组    INDEX_OPTIONAL_ATTRIBUTE=smart_li_batt    INDEX_DATA_TYPE=float
    ${排除列表}    create list
    ${列表2}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除参数量信号}    ${排除列表}
    FOR    ${i}    IN    @{列表2}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${取值约定}    Get From Dictionary    ${i}    convention
        ${设备名称}    Get From Dictionary    ${i}    device_name
        run keyword if    '${取值约定}'=='False'    Run Keyword And Continue On Failure    南向子设备数值型参数设置封装判断结果    smartli    ${信号名称}    读写    FB100B3锂电池参数测试
        run keyword if    '${取值约定}'=='True'    Run Keyword And Continue On Failure    南向子设备有取值约定型参数设置封装判断结果    smartli    ${信号名称}    读写    FB100B3锂电池参数测试
    END
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=parameter    INDEX_DEVICE_CH=电池组    INDEX_OPTIONAL_ATTRIBUTE=smart_li_batt_fb100b3    INDEX_DATA_TYPE=float
    ${排除列表}    create list
    ${列表2}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除参数量信号}    ${排除列表}
    FOR    ${i}    IN    @{列表2}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${取值约定}    Get From Dictionary    ${i}    convention
        ${设备名称}    Get From Dictionary    ${i}    device_name
        run keyword if    '${取值约定}'=='False'    Run Keyword And Continue On Failure    南向子设备数值型参数设置封装判断结果    smartli    ${信号名称}    读写    FB100B3锂电池参数测试
        run keyword if    '${取值约定}'=='True'    Run Keyword And Continue On Failure    南向子设备有取值约定型参数设置封装判断结果    smartli    ${信号名称}    读写    FB100B3锂电池参数测试
    END
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=parameter    INDEX_DEVICE_CH=电池组    INDEX_OPTIONAL_ATTRIBUTE=fb100b3_batt_type    INDEX_DATA_TYPE=int
    ${排除列表}    create list
    ${列表2}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除参数量信号}    ${排除列表}
    FOR    ${i}    IN    @{列表2}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${取值约定}    Get From Dictionary    ${i}    convention
        ${设备名称}    Get From Dictionary    ${i}    device_name
        run keyword if    '${取值约定}'=='False'    Run Keyword And Continue On Failure    南向子设备数值型参数设置封装判断结果    smartli    ${信号名称}    读写    FB100B3锂电池参数测试
        run keyword if    '${取值约定}'=='True'    Run Keyword And Continue On Failure    南向子设备有取值约定型参数设置封装判断结果    smartli    ${信号名称}    读写    FB100B3锂电池参数测试
    END
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=parameter    INDEX_DEVICE_CH=电池组    INDEX_OPTIONAL_ATTRIBUTE=smart_li_batt    INDEX_DATA_TYPE=string32
    ${排除列表}    create list
    ${列表2}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除str32参数量信号}    ${排除列表}
    FOR    ${i}    IN    @{列表2}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${设备名称}    Get From Dictionary    ${i}    device_name
        Run Keyword And Continue On Failure    南向子设备32位字符串型参数设置封装判断结果    smartli    ${信号名称}    读写    FB100B3锂电池参数测试
    END

远程IP地址设置测试
    连接CSU
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    BMS远程IP地址
    Wait Until Keyword Succeeds    20    2    设置web参数量    BMS远程IP地址    ************
    Wait Until Keyword Succeeds    6m    20    获取子工具字符串直到变化为    smartli    读写    后台ip地址    BMS远程IP地址    ************    ************
    
    Wait Until Keyword Succeeds    20    2    设置web参数量    BMS远程IP地址    *************
    Wait Until Keyword Succeeds    6m    20    获取子工具字符串直到变化为    smartli    读写    后台ip地址    BMS远程IP地址    *************   *************
    
    Wait Until Keyword Succeeds    20    2    设置web参数量    BMS远程IP地址    *************
    Wait Until Keyword Succeeds    6m    20    获取子工具字符串直到变化为    smartli    读写    后台ip地址    BMS远程IP地址    *************   *************
    
