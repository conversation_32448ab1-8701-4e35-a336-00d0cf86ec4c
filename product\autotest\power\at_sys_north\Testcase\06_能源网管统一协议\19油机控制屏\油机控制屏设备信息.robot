*** Settings ***
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot


*** Test Cases ***
power_sm_0001_批量获取油机设备信息
    [Documentation]    11min
    写入CSV文档    油机控制屏设备信息获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string64    INDEX_DEVICE_CH=油机控制屏
    ${排除列表}    create list
	@{信号名称列表1}    create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${油机排除设备名称信息}    ${排除列表}    1
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    油机控制屏    device info    ${油机排除设备名称信息}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    FOR    ${i}    IN    @{power_sm待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
		${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
		Append To List       ${信号名称列表1}    ${dict}
    END
	Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    oileng    ${信号名称列表1}    只读    65535    油机控制屏设备信息获取测试    数值    获取油机控制屏设备信息    
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    oileng    ${信号名称列表1}    只读    45576    油机控制屏设备信息获取测试    数值    获取油机控制屏设备信息    
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    oileng    ${信号名称列表1}    只读    12312    油机控制屏设备信息获取测试    数值    获取油机控制屏设备信息    
 
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=float    INDEX_DEVICE_CH=油机控制屏
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    1
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    油机控制屏    device info    null
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    1            power_sm
	Run Keyword And Continue On Failure    南向子设备厂家信息列表封装判断结果    power_sm    oileng    ${缺省值列表}    只读       油机控制屏设备信息获取测试    数值    获取油机控制屏设备信息   
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    2            power_sm
	Run Keyword And Continue On Failure    南向子设备厂家信息列表封装判断结果    power_sm    oileng    ${缺省值列表}    只读       油机控制屏设备信息获取测试    数值    获取油机控制屏设备信息    
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    0            power_sm
    Run Keyword And Continue On Failure    南向子设备厂家信息列表封装判断结果    power_sm    oileng    ${缺省值列表}    只读       油机控制屏设备信息获取测试    数值    获取油机控制屏设备信息    
    
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=int    INDEX_DEVICE_CH=油机控制屏
    ${排除列表}    create list
	@{信号名称列表2}    create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    1
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    油机控制屏    device info    null
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    FOR    ${i}    IN    @{power_sm待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        Comment    ${缺省值}    获取web参数上下限范围    ${信号名称}
		${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
		Append To List       ${信号名称列表2}    ${dict}
    END
	Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    oileng    ${信号名称列表2}    只读    0    油机控制屏设备信息获取测试    数值    获取油机控制屏设备信息    
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    oileng    ${信号名称列表2}    只读    255    油机控制屏设备信息获取测试    数值    获取油机控制屏设备信息    
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    oileng    ${信号名称列表2}    只读    0    油机控制屏设备信息获取测试    数值    获取油机控制屏设备信息    
 

power_sm_0002_油机控制屏系统名称
    连接CSU
    设置子工具值    oileng    all    只读    控制器类型    GM
    sleep    5m
    ${web获取值1}    获取web实时数据    油机控制屏系统名称
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取油机控制屏设备信息    油机控制屏系统名称    ${SSH}
    should be equal as strings    ${web获取值1}    GM631
    should be equal as strings    ${power_sm获取值1}    GM631
    设置子工具值    oileng    all    只读    控制器类型   TM
    sleep    5m
    ${web获取值1}    获取web实时数据    油机控制屏系统名称
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取油机控制屏设备信息    油机控制屏系统名称    ${SSH}
    should be equal as strings    ${web获取值1}    TM631
    should be equal as strings    ${power_sm获取值1}    TM631

power_sm_0003_油机控制屏软件版本
    连接CSU
    设置子工具值    oileng    all    只读    软件版本号    303
    sleep    20s
    ${web获取值1}    获取web实时数据    油机控制屏软件版本
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取油机控制屏设备信息    油机控制屏软件版本    ${SSH}
    should be equal as strings    ${web获取值1}    V3.03
    should be equal as strings    ${power_sm获取值1}    V3.03
    设置子工具值    oileng    all    只读    软件版本号    304
    sleep    20s
    ${web获取值1}    获取web实时数据    油机控制屏软件版本
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取油机控制屏设备信息    油机控制屏软件版本    ${SSH}
    should be equal as strings    ${web获取值1}    V3.04
    should be equal as strings    ${power_sm获取值1}    V3.04
    设置子工具值    oileng    all    只读    软件版本号    305
    sleep    20s
    ${web获取值1}    获取web实时数据    油机控制屏软件版本
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取油机控制屏设备信息    油机控制屏软件版本    ${SSH}
    should be equal as strings    ${web获取值1}    V3.05
    should be equal as strings    ${power_sm获取值1}    V3.05

power_sm_0004_油机控制屏软件发布日期
    连接CSU
    设置子工具值    oileng    all    只读    软件发布日期year    5139
    设置子工具值    oileng    all    只读    软件发布日期month    3095
    sleep    20s
    ${web获取值1}    获取web实时数据    油机控制屏软件发布日期
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取油机控制屏设备信息    油机控制屏软件发布日期    ${SSH}
    should be equal as strings    ${web获取值1}    2019-12-23
    should be equal as strings    ${power_sm获取值1}    2019-12-23
    设置子工具值    oileng    all    只读    软件发布日期year    5140
    设置子工具值    oileng    all    只读    软件发布日期month    3097
    sleep    1m
    ${web获取值1}    获取web实时数据    油机控制屏软件发布日期
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取油机控制屏设备信息    油机控制屏软件发布日期    ${SSH}
    should be equal as strings    ${web获取值1}    2020-12-25
    should be equal as strings    ${power_sm获取值1}    2020-12-25
