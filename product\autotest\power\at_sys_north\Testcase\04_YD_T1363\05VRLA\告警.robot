*** Settings ***
Suite Setup       Run Keywords    主动告警测试前置条件    ${CSU_role}
...               AND    测试用例前置条件
Suite Teardown    Run Keywords    设置web设备参数量为默认值    CSU主动告警使能
...               AND    测试用例后置条件
Default Tags      T1-1
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
1363_0002_1电池温度低告警-1
    同时设置三相电压频率    220    50
    打开交流源输出
    连接CSU
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    电池温度低
    #获取告警级别和输出干接点设置值
    ${级别设置值}    获取web参数量    电池温度低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池温度低    严重
    sleep    3
    ${级别设置值}    获取web参数量    电池温度低
    #（1）告警产生
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    -10    0.001    电池_1    电池温度
    #产生电池温度低告警
    ${电池温度低阈值范围}    获取web参数上下限范围    电池温度低阈值
    ${电池温度低阈值可设置范围}    获取web参数可设置范围    电池温度低阈值
    设置web参数量    电池温度低阈值    ${电池温度低阈值可设置范围}[1]
    ${电池温度}    获取web实时数据    电池温度-1
    Wait Until Keyword Succeeds    10    1    判断告警存在    电池温度低
    sleep    3
    #只是通过调节温度零点来改变温度，如果室温较高时，无法达到告警点，视为正常
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    电池温度低    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池温度低
        sleep    5
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取VRLA段告警量~CID1=46H>>    <<获取VRLA段告警量~CID2=44H>>    电池温度告警-1    1    None    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${电池温度低告警级别}    获取web告警属性    电池温度低-1    告警级别
        should be equal    ${告警级别设置}    ${电池温度低告警级别}
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取VRLA段告警级别~CID1=46H>>    <<获取VRLA段告警级别~CID2=81H>>    电池温度低    None    ${g_ver_1363}
        ${1104告警级别}    获取1104告警级别取值约定    电池温度低-1    ${1104告警级别}
        should be equal    ${告警级别设置}    ${1104告警级别}
        ${web实时告警名}    由子工具获取web实时告警名称    电池温度低-1
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    1363    电池_1    ${web实时告警名}
    END
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池温度低    主要
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    #（2）告警恢复
    设置web参数量    电池温度低阈值    ${电池温度低阈值范围}[0]
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池温度低
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池温度低阈值
    ...    AND    设置web参数量    电池温度低    主要

1363_0004_2电池温度高告警-1
    同时设置三相电压频率    220    50
    打开交流源输出
    连接CSU
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    电池温度高
    #获取告警级别和输出干接点设置值
    ${级别设置值}    获取web参数量    电池温度高
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池温度高    严重
    sleep    3
    ${级别设置值}    获取web参数量    电池温度高
    #（1）告警产生
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    10    1.6    电池_1    电池温度
    #产生电池温度高告警
    ${电池温度高阈值范围}    获取web参数上下限范围    电池温度高阈值
    ${电池温度高阈值可设置范围}    获取web参数可设置范围    电池温度高阈值
    设置web参数量    电池温度高阈值    ${电池温度高阈值可设置范围}[0]
    ${电池温度}    获取web实时数据    电池温度-1
    Wait Until Keyword Succeeds    10    1    判断告警存在    电池温度高
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    电池温度高    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池温度高
        sleep    3
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取VRLA段告警量~CID1=46H>>    <<获取VRLA段告警量~CID2=44H>>    电池温度告警-1    2    None    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${电池温度高告警级别}    获取web告警属性    电池温度高-1    告警级别
        should be equal    ${告警级别设置}    ${电池温度高告警级别}
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取VRLA段告警级别~CID1=46H>>    <<获取VRLA段告警级别~CID2=81H>>    电池温度高    None    ${g_ver_1363}
        ${1104告警级别}    获取1104告警级别取值约定    电池温度高-1    ${1104告警级别}
        should be equal    ${电池温度高告警级别}    ${1104告警级别}
        ${web实时告警名}    由子工具获取web实时告警名称    电池温度高-1
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    1363    电池_1    ${web实时告警名}
    END
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池温度高    主要
    #（2）告警恢复
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    设置web参数量    电池温度高阈值    ${电池温度高阈值范围}[0]
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池温度高
    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取VRLA段告警量~CID1=46H>>    <<获取VRLA段告警量~CID2=44H>>    电池温度告警-1    2    None    ${g_ver_1363}
    should not be true    ${1104告警结果}
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池温度高阈值
    ...    AND    设置web参数量    电池温度高    主要

1363_0006_3电池温度无效告警
    #电池2不接入传感器
    设置通道无效值/恢复通道原始值    ${plat.batttemps}[0]    1
    实时告警刷新完成
    设置web参数量    电池温度无效    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    电池温度无效
    ${电池温度无效告警}    判断告警存在_带返回值    电池温度无效
    should not be true    ${电池温度无效告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        设置web参数量    电池温度无效    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池温度无效
        sleep    5
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取VRLA段告警量~CID1=46H>>    <<获取VRLA段告警量~CID2=44H>>    电池温度告警-1    80    None    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${电池温度无效告警级别}    获取web告警属性    电池温度无效-1    告警级别
        should be equal    ${告警级别}    ${电池温度无效告警级别}
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取VRLA段告警级别~CID1=46H>>    <<获取VRLA段告警级别~CID2=81H>>    电池温度无效    None    ${g_ver_1363}
        ${1104告警级别}    获取1104告警级别取值约定    电池温度无效    ${1104告警级别}
        should be equal    ${电池温度无效告警级别}    ${1104告警级别}
        ${web实时告警名}    由子工具获取web实时告警名称    电池温度无效
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别}'=='主要'    主动告警上送判断2    1363    电池_1    ${web实时告警名}
    END
    设置web参数量    电池温度无效    主要
    #无告警后查询判断
    Comment    Wait Until Keyword Succeeds    30    1    设置web参数量    电池组容量_2    0
    Comment    wait until keyword succeeds    5m    1    判断告警不存在    电池温度无效
    Comment    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=44H>>    电池温度告警-2    E3    None    ${g_ver_1363}
    Comment    should not be true    ${1104告警结果}
    [Teardown]    Run Keywords    设置web参数量    电池温度无效    主要
    ...           AND     设置通道无效值/恢复通道原始值    ${plat.batttemps}[0]    0


1363_0008_4电池电压低
    连接交流源
    连接电池模拟器
    重置电池模拟器输出
    打开电池模拟器输出
    sleep    10
    关闭交流源输出
    实时告警刷新完成
    #获取参数/默认值
    ${级别设置值}    获取web参数量    电池电压低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池电压低    严重
    ${电压低告警}    判断告警不存在_带返回值    电池电压低
    run keyword if    '${电压低告警}' != '[]'    向上调节电池电压    53.5
    ${可设置范围}    获取web参数可设置范围    电池电压低阈值
    设置web参数量    电池电压低阈值    ${可设置范围}[1]
    ${ 电压低阈值}    获取web参数量    电池电压低阈值
    向下调节电池电压    ${ 电压低阈值}-0.5
    wait until keyword succeeds    5m    2    查询指定告警信息    电池电压低
    wait until keyword succeeds    30    1    设置web参数量    电池电压低    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    电池电压低
    ${电压低告警}    判断告警存在_带返回值    电池电压低
    should not be true    ${电压低告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        设置web参数量    电池电压低    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池电压低
        sleep    5
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取VRLA段告警量~CID1=46H>>    <<获取VRLA段告警量~CID2=44H>>    电池电压告警-1    1    None    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${直流电压低告警级别}    获取web告警属性    电池电压低-1    告警级别
        should be equal    ${告警级别}    ${直流电压低告警级别}
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取VRLA段告警级别~CID1=46H>>    <<获取VRLA段告警级别~CID2=81H>>    电池电压低    None    ${g_ver_1363}
        ${1104告警级别}    获取1104告警级别取值约定    电池电压低-1    ${1104告警级别}
        should be equal    ${告警级别}    ${1104告警级别}
        ${web实时告警名}    由子工具获取web实时告警名称    电池电压低-1
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别}'=='主要'    主动告警上送判断2    1363    电池_1    ${web实时告警名}
    END
    设置web参数量    电池电压低    主要
    #无告警后查询判断
    向上调节电池电压    ${ 电压低阈值}+1
    wait until keyword succeeds    5m    1    判断告警不存在    电池电压低
    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取VRLA段告警量~CID1=46H>>    <<获取VRLA段告警量~CID2=44H>>    电池电压告警-1    1    None    ${g_ver_1363}
    should not be true    ${1104告警结果}
    [Teardown]    Run keywords    设置web设备参数量为默认值    电池电压低阈值
    ...    AND    设置web参数量    电池电压低    主要
    ...    AND    打开交流源输出
    ...    AND    重置电池模拟器输出

1363_0010_电池电压过低-1
    [Documentation]    0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning
    ...
    ...    1363无此告警级别
    连接交流源
    连接电池模拟器
    重置电池模拟器输出
    打开电池模拟器输出
    sleep    10
    关闭交流源输出
    实时告警刷新完成
    #获取参数/默认值
    ${级别设置值}    获取web参数量    电池电压过低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池电压过低    严重
    ${电压低告警}    判断告警不存在_带返回值    电池电压过低
    run keyword if    '${电压低告警}' != '[]'    向上调节电池电压    53.5
    ${可设置范围}    获取web参数可设置范围    电池电压过低阈值
    设置web参数量    电池电压过低阈值    ${可设置范围}[1]
    ${ 电压低阈值}    获取web参数量    电池电压过低阈值
    向下调节电池电压    ${ 电压低阈值}-0.5
    wait until keyword succeeds    5m    2    查询指定告警信息    电池电压过低
    wait until keyword succeeds    30    1    设置web参数量    电池电压过低    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    电池电压过低
    ${电压低告警}    判断告警存在_带返回值    电池电压过低
    should not be true    ${电压低告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    1m    1    设置web参数量    电池电压过低    ${告警级别}
        Comment    设置web参数量    电池电压过低    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池电压过低
        sleep    1m
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取VRLA段告警量~CID1=46H>>    <<获取VRLA段告警量~CID2=44H>>    电池电压告警-1    1    None    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${直流电压低告警级别}    获取web告警属性    电池电压过低-1    告警级别
        should be equal    ${告警级别}    ${直流电压低告警级别}
        ${web实时告警名}    由子工具获取web实时告警名称    电池电压过低-1
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别}'=='主要'    主动告警上送判断2    1363    电池_1    ${web实时告警名}
    END
    设置web参数量    电池电压过低    主要
    #无告警后查询判断
    向上调节电池电压    ${ 电压低阈值}+1
    wait until keyword succeeds    5m    1    判断告警不存在    电池电压过低
    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取VRLA段告警量~CID1=46H>>    <<获取VRLA段告警量~CID2=44H>>    电池电压告警-1    1    None    ${g_ver_1363}
    should not be true    ${1104告警结果}
    [Teardown]    Run keywords    设置web设备参数量为默认值    电池电压过低阈值
    ...    AND    设置web参数量    电池电压过低    主要
    ...    AND    打开交流源输出
    ...    AND    重置电池模拟器输出
