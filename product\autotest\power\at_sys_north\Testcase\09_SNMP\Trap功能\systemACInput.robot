*** Settings ***
Default Tags      T1-1
Suite Setup       测试用例前置条件
Suite Teardown    测试用例后置条件
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
snmp_0002_交流停电告警
    [Documentation]    纯市电场景，交流停电后，CSU告警市电停电，不会告警交流停电
    ...
    ...
    ...    222-------------- [u'mAINSFailure1'] [u'aCPowerOff']
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    ${级别设置值}    获取web参数量    市电停电
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    市电停电    主要
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    停电
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    交流供电状态    市电1
    关闭交流源输出
    Wait Until Keyword Succeeds    5m    2    设置web参数量    市电停电    屏蔽    #默认级别：2
    sleep    3
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    市电停电
    ###以上告警已产生，先屏蔽
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    Wait Until Keyword Succeeds    30    1    设置web参数量    市电停电    主要
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    市电停电
    #产生
    ${snmp英文名}    获取snmp单个告警英文名    市电停电_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    打开交流源输出
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    市电停电
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    打开交流源输出

snmp_0004_交流缺相-1
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    ${级别设置值}    获取web参数量    交流缺相_1
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    交流缺相_1    主要
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流缺相
    分别设置各相电压频率    0    220    220    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流缺相_1    屏蔽
    Wait Until Keyword Succeeds    2m    1    查询指定告警信息不为    交流缺相
    ###以上告警已产生，先屏蔽
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    Wait Until Keyword Succeeds    30    1    设置web参数量    交流缺相_1    主要
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    交流缺相
    #产生
    ${snmp英文名}    获取单体类型的snmp告警英文名    交流缺相    0    1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    #消失
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流缺相_1
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    同时设置三相电压频率    220    50

snmp_0006_交流缺相-2
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    ${级别设置值}    获取web参数量    交流缺相_2
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    交流缺相_2    主要
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流缺相
    分别设置各相电压频率    220    0    220    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流缺相_2    屏蔽
    Wait Until Keyword Succeeds    2m    1    查询指定告警信息不为    交流缺相
    ###以上告警已产生，先屏蔽
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    Wait Until Keyword Succeeds    30    1    设置web参数量    交流缺相_2    主要
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    交流缺相
    #产生
    ${snmp英文名}    获取单体类型的snmp告警英文名    交流缺相    0    2
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    #消失
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流缺相_2
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    同时设置三相电压频率    220    50

snmp_0008_交流缺相-3
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    ${级别设置值}    获取web参数量    交流缺相_3
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    交流缺相_3    主要
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流缺相
    分别设置各相电压频率    220    220    0    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流缺相_3    屏蔽
    Wait Until Keyword Succeeds    2m    1    查询指定告警信息不为    交流缺相
    ###以上告警已产生，先屏蔽
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    Wait Until Keyword Succeeds    30    1    设置web参数量    交流缺相_3    主要
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    交流缺相
    #产生
    ${snmp英文名}    获取单体类型的snmp告警英文名    交流缺相    0    3
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    #消失
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流缺相_3
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    同时设置三相电压频率    220    50

snmp_0010_交流电压低-1
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压
    ${可设置范围}    获取web参数可设置范围    交流电压低阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压低阈值    ${可设置范围}[1]
    ${电压低设置值}    获取web参数量    交流电压低阈值
    ${判断点1}    evaluate    ${电压低设置值}-5
    分别设置各相电压频率    ${判断点1}    220    220    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压低_1    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压低
    ###以上告警已产生，先屏蔽
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    Wait Until Keyword Succeeds    30    1    设置web参数量    交流电压低_1    主要
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    交流电压低
    #产生
    ${snmp英文名}    获取单体类型的snmp告警英文名    交流电压低    0    1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流电压低_1
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    run keywords    交流电压参数恢复默认值
    ...    AND    同时设置三相电压频率    220    50

snmp_0012_交流电压低-2
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压
    ${可设置范围}    获取web参数可设置范围    交流电压低阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压低阈值    ${可设置范围}[1]
    ${电压低设置值}    获取web参数量    交流电压低阈值
    ${判断点1}    evaluate    ${电压低设置值}-5
    分别设置各相电压频率    220    ${判断点1}    220    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压低_2    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压低
    ###以上告警已产生，先屏蔽
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    Wait Until Keyword Succeeds    30    1    设置web参数量    交流电压低_2    主要
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    交流电压低
    #产生
    ${snmp英文名}    获取单体类型的snmp告警英文名    交流电压低    0    2
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流电压低_2
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    run keywords    交流电压参数恢复默认值
    ...    AND    同时设置三相电压频率    220    50

snmp_0014_交流电压低-3
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压
    ${可设置范围}    获取web参数可设置范围    交流电压低阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压低阈值    ${可设置范围}[1]
    ${电压低设置值}    获取web参数量    交流电压低阈值
    ${判断点1}    evaluate    ${电压低设置值}-5
    分别设置各相电压频率    220    220    ${判断点1}    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压低_3    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压低
    ###以上告警已产生，先屏蔽
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    Wait Until Keyword Succeeds    30    1    设置web参数量    交流电压低_3    主要
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    交流电压低
    #产生
    ${snmp英文名}    获取单体类型的snmp告警英文名    交流电压低    0    3
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流电压低_3
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    run keywords    交流电压参数恢复默认值
    ...    AND    同时设置三相电压频率    220    50

snmp_0016_交流电压高-1
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压
    ${可设置范围}    获取web参数可设置范围    交流电压高阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高阈值    ${可设置范围}[0]
    ${电压高设置值}    获取web参数量    交流电压高阈值
    ${判断点1}    evaluate    ${电压高设置值}+5
    分别设置各相电压频率    ${判断点1}    220    220    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高_1    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    交流电压高
    ###以上告警已产生，先屏蔽
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    Wait Until Keyword Succeeds    30    1    设置web参数量    交流电压高_1    主要
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    交流电压高
    #产生
    ${snmp英文名}    获取单体类型的snmp告警英文名    交流电压高    0    1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流电压高_1
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    run keywords    交流电压参数恢复默认值
    ...    AND    同时设置三相电压频率    220    50

snmp_0018_交流电压高-2
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压
    ${可设置范围}    获取web参数可设置范围    交流电压高阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高阈值    ${可设置范围}[0]
    ${电压高设置值}    获取web参数量    交流电压高阈值
    ${判断点1}    evaluate    ${电压高设置值}+5
    分别设置各相电压频率    220    ${判断点1}    220    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高_2    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    交流电压高
    ###以上告警已产生，先屏蔽
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    Wait Until Keyword Succeeds    30    1    设置web参数量    交流电压高_2    主要
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    交流电压高
    #产生
    ${snmp英文名}    获取单体类型的snmp告警英文名    交流电压高    0    2
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流电压高_2
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    run keywords    交流电压参数恢复默认值
    ...    AND    同时设置三相电压频率    220    50

snmp_0020_交流电压高-3
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压
    ${可设置范围}    获取web参数可设置范围    交流电压高阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高阈值    ${可设置范围}[0]
    ${电压高设置值}    获取web参数量    交流电压高阈值
    ${判断点1}    evaluate    ${电压高设置值}+5
    分别设置各相电压频率    220    220    ${判断点1}    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高_3    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    交流电压高
    ###以上告警已产生，先屏蔽
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    Wait Until Keyword Succeeds    30    1    设置web参数量    交流电压高_3    主要
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    交流电压高
    #产生
    ${snmp英文名}    获取单体类型的snmp告警英文名    交流电压高    0    3
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流电压高_3
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    run keywords    交流电压参数恢复默认值
    ...    AND    同时设置三相电压频率    220    50

snmp_0022_交流电流高
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电流
    ${可设置范围}    获取web参数可设置范围    交流电流高阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电流高阈值    ${可设置范围}[0]
    设置负载电压电流    53.5    80
    打开负载输出
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    系统停电
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电流高_1    屏蔽
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电流高_2    屏蔽
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电流高_3    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    5m    2    判断告警不存在    交流电流高
    ###以上告警已产生，先屏蔽
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    Wait Until Keyword Succeeds    30    1    设置web参数量    交流电流高_1    主要
    Wait Until Keyword Succeeds    30    1    设置web参数量    交流电流高_2    主要
    Wait Until Keyword Succeeds    30    1    设置web参数量    交流电流高_3    主要
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    交流电流高_1
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    交流电流高_2
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    交流电流高_3
    #产生
    ${snmp英文名1}    获取单体类型的snmp告警英文名    交流电流高    0    1
    ${snmp英文名2}    获取单体类型的snmp告警英文名    交流电流高    0    2
    ${snmp英文名3}    获取单体类型的snmp告警英文名    交流电流高    0    3
    ${告警产生1}    判断Trap告警产生    ${snmp英文名1}
    ${告警产生2}    判断Trap告警产生    ${snmp英文名2}
    ${告警产生3}    判断Trap告警产生    ${snmp英文名3}
    should be true    ${告警产生1}
    should be true    ${告警产生2}
    should be true    ${告警产生3}
    #消失
    Wait Until Keyword Succeeds    2m    1    设置web设备参数量为默认值    交流电流高阈值
    同时设置三相电压频率    220    50
    关闭负载输出
    Wait Until Keyword Succeeds    4m    1    判断告警不存在    交流电流高_1
    Wait Until Keyword Succeeds    4m    1    判断告警不存在    交流电流高_2
    Wait Until Keyword Succeeds    4m    1    判断告警不存在    交流电流高_3
    ${告警产生1}    判断Trap告警消失    ${snmp英文名1}
    ${告警产生2}    判断Trap告警消失    ${snmp英文名2}
    ${告警产生3}    判断Trap告警消失    ${snmp英文名3}
    should be true    ${告警产生1}
    should be true    ${告警产生2}
    should be true    ${告警产生3}
    [Teardown]    run keywords    设置web设备参数量为默认值    交流电流高阈值
    ...    AND    关闭负载输出
    ...    AND    设置web设备参数量为默认值    交流电流高_1    交流电流高_2    交流电流高_3

snmp_0024_交流电压不平衡√
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    #获取告警级别
    ${电池放电级别}    获取web参数量    交流电压不平衡
    run keyword if    '${电池放电级别}'=='屏蔽'    设置web参数量    交流电压不平衡    主要
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压不平衡
    ${可设置范围}    获取web参数可设置范围    交流电压不平衡阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压不平衡阈值    ${可设置范围}[0]
    ${电压低设置值}    获取web参数量    交流电压不平衡阈值
    ${判断点1}    evaluate    220-${电压低设置值}-5
    分别设置各相电压频率    ${判断点1}    250    220    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压不平衡    屏蔽
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    交流电压不平衡
    ###以上告警已测试，先屏蔽
    ###产生告警，并判断
    Wait Until Keyword Succeeds    30    1    设置web参数量    交流电压不平衡    主要
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    交流电压不平衡
    #产生
    ${snmp英文名}    获取snmp单个告警英文名    交流电压不平衡
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    交流电压不平衡
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    run keywords    设置web参数量    交流电压不平衡干接点    0
    ...    AND    设置web设备参数量为默认值    交流电压不平衡阈值
    ...    AND    同时设置三相电压频率    220    50

snmp_0026_交流电压过低-1
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压
    ${可设置范围}    获取web参数可设置范围    交流电压过低阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压过低阈值    ${可设置范围}[1]
    ${电压低设置值}    获取web参数量    交流电压过低阈值
    ${判断点1}    evaluate    ${电压低设置值}-5
    分别设置各相电压频率    ${判断点1}    220    220    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压过低_1    屏蔽
    #调试发现，有的时候会报交流电压不平衡告警，
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压不平衡    屏蔽
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    交流电压过低_1
    ###以上告警已产生，先屏蔽
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    Wait Until Keyword Succeeds    30    1    设置web参数量    交流电压过低_1    主要
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    交流电压过低
    #产生
    ${snmp英文名}    获取单体类型的snmp告警英文名    交流电压过低    0    1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    交流电压参数恢复默认值
    同时设置三相电压频率    220    50
    关闭负载输出
    Wait Until Keyword Succeeds    4m    1    判断告警不存在    交流电压过低_1
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    run keywords    交流电压参数恢复默认值
    ...    AND    同时设置三相电压频率    220    50
    ...    AND    设置web设备参数量为默认值    交流电压不平衡

snmp_0028_交流电压过低-2
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压
    ${可设置范围}    获取web参数可设置范围    交流电压过低阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压过低阈值    ${可设置范围}[1]
    ${电压低设置值}    获取web参数量    交流电压过低阈值
    ${判断点1}    evaluate    ${电压低设置值}-5
    分别设置各相电压频率    220    ${判断点1}    220    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压过低_2    屏蔽
    #调试发现，有的时候会报交流电压不平衡告警，
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压不平衡    屏蔽
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    交流电压过低_2
    ###以上告警已产生，先屏蔽
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    Wait Until Keyword Succeeds    30    1    设置web参数量    交流电压过低_2    主要
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    交流电压过低
    #产生
    ${snmp英文名}    获取单体类型的snmp告警英文名    交流电压过低    0    2
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    交流电压参数恢复默认值
    同时设置三相电压频率    220    50
    关闭负载输出
    Wait Until Keyword Succeeds    4m    1    判断告警不存在    交流电压过低_2
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    run keywords    交流电压参数恢复默认值
    ...    AND    同时设置三相电压频率    220    50
    ...    AND    设置web设备参数量为默认值    交流电压不平衡

snmp_0030_交流电压过低-3
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压
    ${可设置范围}    获取web参数可设置范围    交流电压过低阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压过低阈值    ${可设置范围}[1]
    ${电压低设置值}    获取web参数量    交流电压过低阈值
    ${判断点1}    evaluate    ${电压低设置值}-5
    分别设置各相电压频率    220    220    ${判断点1}    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压过低_3    屏蔽
    #调试发现，有的时候会报交流电压不平衡告警，
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压不平衡    屏蔽
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    交流电压过低_3
    ###以上告警已产生，先屏蔽
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    Wait Until Keyword Succeeds    30    1    设置web参数量    交流电压过低_3    主要
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    交流电压过低
    #产生
    ${snmp英文名}    获取单体类型的snmp告警英文名    交流电压过低    0    3
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    交流电压参数恢复默认值
    同时设置三相电压频率    220    50
    关闭负载输出
    Wait Until Keyword Succeeds    4m    1    判断告警不存在    交流电压过低_3
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    run keywords    交流电压参数恢复默认值
    ...    AND    同时设置三相电压频率    220    50
    ...    AND    设置web设备参数量为默认值    交流电压不平衡

snmp_0032_交流电压过高-1
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压
    ${可设置范围}    获取web参数可设置范围    交流电压高阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高阈值    ${可设置范围}[0]
    ${电压高设置值}    获取web参数量    交流电压高阈值
    ${可设置范围1}    获取web参数可设置范围    交流电压过高阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压过高阈值    ${可设置范围1}[0]
    ${电压过高设置值}    获取web参数量    交流电压过高阈值
    ${判断点1}    evaluate    ${电压过高设置值}+5
    分别设置各相电压频率    ${判断点1}    220    220    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压过高_1    屏蔽
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    交流电压过高_1
    ###以上告警已产生，先屏蔽
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    Wait Until Keyword Succeeds    30    1    设置web参数量    交流电压过高_1    主要
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    交流电压过高
    #产生
    ${snmp英文名}    获取单体类型的snmp告警英文名    交流电压过高    0    1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    交流电压参数恢复默认值
    同时设置三相电压频率    220    50
    关闭负载输出
    Wait Until Keyword Succeeds    4m    1    判断告警不存在    交流电压过高_1
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    run keywords    交流电压参数恢复默认值
    ...    AND    同时设置三相电压频率    220    50

snmp_0034_交流电压过高-2
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压
    ${可设置范围}    获取web参数可设置范围    交流电压高阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高阈值    ${可设置范围}[0]
    ${电压高设置值}    获取web参数量    交流电压高阈值
    ${可设置范围1}    获取web参数可设置范围    交流电压过高阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压过高阈值    ${可设置范围1}[0]
    ${电压过高设置值}    获取web参数量    交流电压过高阈值
    ${判断点1}    evaluate    ${电压过高设置值}+5
    分别设置各相电压频率    220    ${判断点1}    220    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压过高_2    屏蔽
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    交流电压过高_2
    ###以上告警已产生，先屏蔽
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    Wait Until Keyword Succeeds    30    1    设置web参数量    交流电压过高_2    主要
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    交流电压过高
    #产生
    ${snmp英文名}    获取单体类型的snmp告警英文名    交流电压过高    0    2
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    交流电压参数恢复默认值
    同时设置三相电压频率    220    50
    关闭负载输出
    Wait Until Keyword Succeeds    4m    1    判断告警不存在    交流电压过高_2
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    run keywords    交流电压参数恢复默认值
    ...    AND    同时设置三相电压频率    220    50

snmp_0036_交流电压过高-3
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    交流电压
    ${可设置范围}    获取web参数可设置范围    交流电压高阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高阈值    ${可设置范围}[0]
    ${电压高设置值}    获取web参数量    交流电压高阈值
    ${可设置范围1}    获取web参数可设置范围    交流电压过高阈值
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压过高阈值    ${可设置范围1}[0]
    ${电压过高设置值}    获取web参数量    交流电压过高阈值
    ${判断点1}    evaluate    ${电压过高设置值}+5
    分别设置各相电压频率    220    220    ${判断点1}    50
    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压过高_3    屏蔽
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    交流电压过高_3
    ###以上告警已产生，先屏蔽
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    Wait Until Keyword Succeeds    30    1    设置web参数量    交流电压过高_3    主要
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    交流电压过高
    #产生
    ${snmp英文名}    获取单体类型的snmp告警英文名    交流电压过高    0    3
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    交流电压参数恢复默认值
    同时设置三相电压频率    220    50
    关闭负载输出
    Wait Until Keyword Succeeds    4m    1    判断告警不存在    交流电压过高_3
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    run keywords    交流电压参数恢复默认值
    ...    AND    同时设置三相电压频率    220    50
