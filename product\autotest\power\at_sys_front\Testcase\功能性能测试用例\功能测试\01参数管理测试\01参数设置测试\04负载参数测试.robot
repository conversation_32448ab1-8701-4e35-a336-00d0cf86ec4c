*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
直流电压高阈值设置
    [Documentation]    直流电压高范围：53-59，默认58.5；>=直流电压低+1，<=直流电压过高。。。
    ...    直流电压低范围：44-55，默认46.5；<=直流电压高-1，>=直流电压过低。。。
    ...    直流电压过高：53-60，默认59.5。。。
    ...    直流电压过低：44-55，默认45.5。。
    [Tags]    view    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    直流电压高阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    直流电压高阈值
    ${缺省值}    获取web参数上下限范围    直流电压高阈值
    ${可设置范围}    获取web参数可设置范围    直流电压高阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    直流电压高阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    直流电压高阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    直流电压高阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    直流电压高阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    直流电压参数恢复默认值

直流电压低阈值设置
    [Documentation]    直流电压高范围：53-59，默认58.5；>=直流电压低+1，<=直流电压过高。。。
    ...    直流电压低范围：44-55，默认46.5；<=直流电压高-1，>=直流电压过低。。。
    ...    直流电压过高：53-60，默认59.5。。。
    ...    直流电压过低：44-55，默认45.5。。
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    直流电压低阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    直流电压低阈值
    ${缺省值}    获取web参数上下限范围    直流电压低阈值
    ${可设置范围}    获取web参数可设置范围    直流电压低阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    直流电压低阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    直流电压低阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    直流电压低阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    直流电压低阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    直流电压参数恢复默认值

直流电压过高阈值设置
    [Documentation]    直流电压高范围：53-59，默认58.5；>=直流电压低+1，<=直流电压过高。。。
    ...    直流电压低范围：44-55，默认46.5；<=直流电压高-1，>=直流电压过低。。。
    ...    直流电压过高：53-60，默认59.5。。。
    ...    直流电压过低：44-55，默认45.5。。
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    直流电压过高阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    直流电压过高阈值
    ${缺省值}    获取web参数上下限范围    直流电压过高阈值
    ${可设置范围}    获取web参数可设置范围    直流电压过高阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    直流电压过高阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    直流电压过高阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    直流电压过高阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    直流电压过高阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    直流电压参数恢复默认值

直流电压过低阈值设置
    [Documentation]    直流电压高范围：53-59，默认58.5；>=直流电压低+1，<=直流电压过高。。。
    ...    直流电压低范围：44-55，默认46.5；<=直流电压高-1，>=直流电压过低。。。
    ...    直流电压过高：53-60，默认59.5。。。
    ...    直流电压过低：44-55，默认45.5。。
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    直流电压过低阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    直流电压过低阈值
    ${缺省值}    获取web参数上下限范围    直流电压过低阈值
    ${可设置范围}    获取web参数可设置范围    直流电压过低阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    直流电压过低阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    直流电压过低阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    直流电压过低阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    直流电压过低阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    直流电压参数恢复默认值
