*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
自动节能模式监控复位
    # [Setup]    测试用例前置条件
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    系统复位
    Wait Until Keyword Succeeds    1m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3

自动节能模式监控复位（系统异常复位）
    [Documentation]    整流器正常工作时，电池电压无法调节至阈值以下
    [Tags]    notest
    # [Setup]    测试用例前置条件
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    ${告警阈值}    获取web参数量    直流电压过低阈值
    向下调节电池电压    ${告警阈值}-0.5
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息    直流电压过低
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    交流节能状态    自动节能
    向上调节电池电压    53.5
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    直流电压过低
    系统复位
    Wait Until Keyword Succeeds    1m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3

自动节能模式电池检测异常
    [Documentation]    电池检测异常告警出不来？
    [Tags]    notest
    [Setup]    重置电池模拟器输出
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    ${电压设置值}    获取web参数量    浮充电压
    Wait Until Keyword Succeeds    10    2s    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    ${电压获取值}    获取web实时数据    直流电压
    ${电压差值}    evaluate    ${电压设置值}-${电压获取值}
    Wait Until Keyword Succeeds    10    2s    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池检测异常    次要
    关闭电池模拟器输出
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    电池检测异常
    Comment    run keyword if    ${电压差值}>0.5 \ \ and ${电压获取值} < ${电压设置值}    向上调节电池电压    ${电压设置值} + 0.5
    Comment    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    重置电池模拟器输出
    Wait Until Keyword Succeeds    5m    2s    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    电池检测异常
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    电池检测异常
    sleep    10
    系统复位
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3
    ...    AND    重置电池模拟器输出

自动节能模式下交流停电
    # [Setup]    测试用例前置条件
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    关闭交流源输出
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    市电停电
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    0
    打开交流源输出
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    交流供电状态    市电1
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    停电
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    系统停电
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    过渡
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    系统停电
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3
    ...    AND    打开交流源输出

自动节能模式下电池测试
    # [Setup]    测试用例前置条件
    电池管理初始化
    系统复位
    #进入节能模式
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动测试
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    测试
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    浮充
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3

自动节能模式下电池容量无效
    # [Setup]    测试用例前置条件
    电池管理初始化
    系统复位
    #进入节能模式
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    0
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    100

自动节能模式下电池电压低
    # [Setup]    测试用例前置条件
    电池管理初始化
    系统复位
    #进入节能模式
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    ${电压设置值}    获取web参数量    浮充电压
    Wait Until Keyword Succeeds    10    2s    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    ${电压获取值}    获取web实时数据    直流电压
    ${电压差值}    evaluate    ${电压获取值} - ${电压设置值}
    run keyword if    0.5 < ${电压差值} < 0.5 and ${电压获取值} < ${电压设置值}    向上调节电池电压    ${电压设置值} + 0.5
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    ${级别设置值}    获取web参数量    电池电压低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池电压低    主要
    ${可设置范围}    获取web参数可设置范围    电池电压低阈值
    设置web参数量    电池电压低阈值    ${可设置范围}[1]
    ${电池电压低设置值}    获取web参数量    电池电压低阈值
    向下调节电池电压    ${电池电压低设置值}-0.5
    wait until keyword succeeds    5m    2    查询指定告警信息    电池电压低
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    向上调节电池电压    ${电池电压低设置值}+1
    wait until keyword succeeds    5m    1    判断告警不存在    电池电压低
    系统复位
    #进入节能模式
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    [Teardown]    Run keywords    设置web设备参数量为默认值    电池电压低阈值
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3
    ...    AND    重置电池模拟器输出

自动节能模式下电池回路断（一组回路断一组回路正常）
    # [Setup]    测试用例前置条件
    电池管理初始化
    系统复位
    #进入节能模式
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_3    100
    ${级别设置值}    获取web参数量    电池回路断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池回路断    严重
    wait until keyword succeeds    5m    2    查询指定告警信息    电池回路断
    # Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    # Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    # Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_3    0
    # wait until keyword succeeds    5m    2    查询指定告警信息不为    电池回路断
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_3    0
    wait until keyword succeeds    5m    2    查询指定告警信息不为    电池回路断
    系统复位
    #进入节能模式
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_3    0
    ...    AND    设置web参数量    电池回路断    主要

自动节能模式下电池回路断（只有一组电池且回路断）
    # [Setup]    测试用例前置条件
    电池管理初始化
    系统复位
    #进入节能模式
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_3    100
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    0
    ${级别设置值}    获取web参数量    电池回路断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池回路断    严重
    wait until keyword succeeds    5m    2    查询指定告警信息    电池回路断
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_3    0
    wait until keyword succeeds    5m    2    查询指定告警信息不为    电池回路断
    系统复位
    #进入节能模式
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    100
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_3    0
    ...    AND    设置web参数量    电池回路断    主要

自动节能模式下电池电压异常
    [Tags]    notest
    # [Setup]    测试用例前置条件
