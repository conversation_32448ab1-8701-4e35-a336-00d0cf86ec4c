*** Settings ***
Test Setup        Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
uniform_0002_电池充电保护√
    [Documentation]    0:否/No;1:是/Yes
    连接CSU
    显示属性配置    电池充电保护    数字量    web_attr=On    gui_attr=On
    #子设备工具模拟
    设置子工具值    smartli    all    只读    电池充电保护    1
    Comment    @{锂电序号lsit}    Create list    1    8    ${uniform锂电最大数}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    电池充电保护-${锂电序号}    是
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    锂电池状态量    充电保护状态-${锂电序号}
        should be true    ${uniform_获取值1}==1
    END
    设置子工具值    smartli    all    只读    电池充电保护    0
    Comment    @{锂电序号lsit}    Create list    1    8    ${uniform锂电最大数}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    电池充电保护-${锂电序号}    否
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    锂电池状态量    充电保护状态-${锂电序号}
        should be true    ${uniform_获取值1}==0
    END
    显示属性配置    电池充电保护    数字量    web_attr=Off    gui_attr=Off

uniform_0004_电池放电保护√
    [Documentation]    0:否/No;1:是/Yes
    连接CSU
    显示属性配置    电池充电保护    数字量    web_attr=On    gui_attr=On
    #子设备工具模拟
    设置子工具值    smartli    all    只读    电池放电保护    1
    Comment    @{锂电序号lsit}    Create list    1    8    ${uniform锂电最大数}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    电池放电保护-${锂电序号}    是
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    锂电池状态量    放电保护状态-${锂电序号}
        should be true    ${uniform_获取值1}==1
    END
    设置子工具值    smartli    all    只读    电池放电保护    0
    Comment    @{锂电序号lsit}    Create list    1    8    ${uniform锂电最大数}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    电池放电保护-${锂电序号}    否
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    锂电池状态量    放电保护状态-${锂电序号}
        should be true    ${uniform_获取值1}==0
    END
    显示属性配置    电池充电保护    数字量    web_attr=Off    gui_attr=Off

uniform_0006_单体均衡启动状态√
    [Documentation]    0:否/No;1:是/Yes
    连接CSU
    显示属性配置    单体均衡启动状态    数字量    web_attr=On    gui_attr=On
    #子设备工具模拟
    设置子工具值    smartli    all    只读    单体均衡启动状态    1
    Comment    @{锂电序号lsit}    Create list    1    8    ${uniform锂电最大数}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    单体均衡启动状态-${锂电序号}    启动
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    锂电池状态量    电池均衡状态-${锂电序号}
        should be true    ${uniform_获取值1}==1
    END
    设置子工具值    smartli    all    只读    单体均衡启动状态    0
    Comment    @{锂电序号lsit}    Create list    1    8    ${uniform锂电最大数}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    单体均衡启动状态-${锂电序号}    关闭
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    锂电池状态量    电池均衡状态-${锂电序号}
        should be true    ${uniform_获取值1}==0
    END
    显示属性配置    单体均衡启动状态    数字量    web_attr=Off    gui_attr=Off

uniform_0008_电池运行状态√
    [Documentation]    0:充电/Charge;1:放电管理/Discharge;2:在线非浮充/On Not Float;3:离线/Off-line
    ...
    ...    默认0
    连接CSU
    设置子工具值    smartli    all    只读    电池运行状态    1
    Comment    @{锂电序号lsit}    Create list    1    8    ${uniform锂电最大数}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    电池运行状态-${锂电序号}    放电管理
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    锂电池状态量    电池运行状态-${锂电序号}
        should be true    ${uniform_获取值1}==1
    END
    设置子工具值    smartli    all    只读    电池运行状态    2
    Comment    @{锂电序号lsit}    Create list    1    8    ${uniform锂电最大数}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    电池运行状态-${锂电序号}    在线非浮充
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    锂电池状态量    电池运行状态-${锂电序号}
        should be true    ${uniform_获取值1}==2
    END
    设置子工具值    smartli    all    只读    电池运行状态    3
    Comment    @{锂电序号lsit}    Create list    1    8    ${uniform锂电最大数}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    电池运行状态-${锂电序号}    离线
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    锂电池状态量    电池运行状态-${锂电序号}
        should be true    ${uniform_获取值1}==3
    END
    设置子工具值    smartli    all    只读    电池运行状态    0
    Comment    @{锂电序号lsit}    Create list    1    8    ${uniform锂电最大数}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    电池运行状态-${锂电序号}    充电
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    锂电池状态量    电池运行状态-${锂电序号}
        should be true    ${uniform_获取值1}==0
    END
