*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
整流器ID获取测试
    [Tags]    view
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-2    否
    ${整流器1ID}    获取web实时数据    整流器ID-1
    log    ${整流器1ID}
    should be equal as strings    ${整流器1ID}    0x12760437
    ${整流器2ID}    获取web实时数据    整流器ID-2
    log    ${整流器2ID}
    should be equal as strings    ${整流器2ID}    0x126A0270

整流器额定最大输出电流获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-2    否    #为保证整流器数据获取正常
    ${整流器1额定最大输出电流}    获取web实时数据    整流器额定最大输出电流-1
    log    ${整流器1额定最大输出电流}
    should be equal as numbers    ${整流器1额定最大输出电流}    63.5
    ${整流器2额定最大输出电流}    获取web实时数据    整流器额定最大输出电流-2
    log    ${整流器2额定最大输出电流}
    should be equal as numbers    ${整流器2额定最大输出电流}    63.5

整流器前级软件版本获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-2    否    #为保证整流器数据获取正常
    ${整流器1前级软件版本}    获取web实时数据    整流器前级软件版本-1
    log    ${整流器1前级软件版本}
    should be equal as strings    '${整流器1前级软件版本}'    'V1.01 '
    ${整流器2前级软件版本}    获取web实时数据    整流器前级软件版本-2
    log    ${整流器2前级软件版本}
    should be equal as strings    '${整流器2前级软件版本}'    'V1.01 '

整流器后级软件版本获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-2    否    #为保证整流器数据获取正常
    ${整流器1后级软件版本}    获取web实时数据    整流器后级软件版本-1
    log    ${整流器1后级软件版本}
    should be equal as strings    '${整流器1后级软件版本}'    'V1.01 '
    ${整流器2后级软件版本}    获取web实时数据    整流器后级软件版本-2
    log    ${整流器2后级软件版本}
    should be equal as strings    '${整流器2后级软件版本}'    'V1.01 '

整流器前级软件发布日期获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-2    否    #为保证整流器数据获取正常
    ${整流器1前级软件发布日期}    获取web实时数据    整流器前级软件发布日期-1
    log    ${整流器1前级软件发布日期}
    should be equal as strings    ${整流器1前级软件发布日期}    2019-05-25
    ${整流器2前级软件发布日期}    获取web实时数据    整流器前级软件发布日期-2
    log    ${整流器2前级软件发布日期}
    should be equal as strings    ${整流器2前级软件发布日期}    2019-05-25

整流器后级软件发布日期获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-2    否    #为保证整流器数据获取正常
    ${整流器1后级软件发布日期}    获取web实时数据    整流器后级软件发布日期-1
    log    ${整流器1后级软件发布日期}
    should be equal as strings    ${整流器1后级软件发布日期}    2019-05-25
    ${整流器2后级软件发布日期}    获取web实时数据    整流器后级软件发布日期-2
    log    ${整流器2后级软件发布日期}
    should be equal as strings    ${整流器2后级软件发布日期}    2019-05-25

整流器序号获取测试
    [Documentation]    没有整流器序号
    [Tags]
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    ${整流器1序号}    获取web实时数据    整流器条码-1
    log    ${整流器1序号}
    should be equal as strings    ${整流器1序号}    210100455940
    ${整流器2序号}    获取web实时数据    整流器条码-2
    log    ${整流器2序号}
    should be equal as strings    ${整流器2序号}    210097891752

整流器数控平台版本获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-2    否    #为保证整流器数据获取正常
    ${整流器1数控平台版本}    获取web实时数据    整流器数控平台版本-1
    log    ${整流器1数控平台版本}
    should be equal as strings    ${整流器1数控平台版本}    V1.83B01
    ${整流器2数控平台版本}    获取web实时数据    整流器数控平台版本-2
    log    ${整流器2数控平台版本}
    should be equal as strings    ${整流器2数控平台版本}    V1.83B01

整流器系统名称获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-2    否    #为保证整流器数据获取正常
    ${整流器1系统名称}    获取web实时数据    整流器系统名称-1
    log    ${整流器1系统名称}
    should be equal as strings    ${整流器1系统名称}    ZXD3000(V6.0R03)
    ${整流器2系统名称}    获取web实时数据    整流器系统名称-2
    log    ${整流器2系统名称}
    should be equal as strings    ${整流器2系统名称}    ZXD3000(V6.0R03)
