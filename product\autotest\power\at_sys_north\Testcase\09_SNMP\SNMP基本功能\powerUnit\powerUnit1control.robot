*** Settings ***
Documentation     可能需要设置多次才会成功
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp_test关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/Web关键字_V30.robot
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
snmp_0002_PU休眠√
    [Documentation]    0:否/No;1:是/Yes
    [Setup]
    连接CSU
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ###休眠1
    FOR    ${i}    IN RANGE    0    4
        ${设置结果}    设置SNMP控制量    pUSleep1value
        Exit For Loop if    '${设置结果}'=='True'
        sleep    20
    END
    Wait Until Keyword Succeeds    20m    2    信号量数据值为    PU休眠状态-1    是
    sleep    20
    ${snmp获取值1}    获取SNMP数据_单个    powerUnitSleepStatus1value
    should be equal    ${snmp获取值1}    1
    FOR    ${i}    IN RANGE    0    4
        ${设置结果}    设置SNMP控制量    pUSleep6value
        Exit For Loop if    '${设置结果}'=='True'
        sleep    20
    END
    ###休眠2
    Wait Until Keyword Succeeds    20m    2    信号量数据值为    PU休眠状态-6    是
    sleep    20
    ${snmp获取值2}    获取SNMP数据_单个    powerUnitSleepStatus6value
    should be equal    ${snmp获取值2}    1
    sleep    20
    ###休眠3
    FOR    ${i}    IN RANGE    0    4
        ${设置结果}    设置SNMP控制量    pUSleep8value
        Exit For Loop if    '${设置结果}'=='True'
        sleep    20
    END
    Wait Until Keyword Succeeds    20m    2    信号量数据值为    PU休眠状态-8    是
    sleep    20
    ${snmp获取值3}    获取SNMP数据_单个    powerUnitSleepStatus8value
    should be equal    ${snmp获取值3}    1
    断开连接SNMP

snmp_0004_PU唤醒√
    [Documentation]    0:否/No;1:是/Yes
    [Setup]
    连接CSU
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ###唤醒1
    Comment    Wait Until Keyword Succeeds    3m    1m    设置SNMP控制量    pUWaken1value
    FOR    ${i}    IN RANGE    0    4
        ${设置结果}    设置SNMP控制量    pUWaken1value
        Exit For Loop if    '${设置结果}'=='True'
        sleep    20
    END
    Wait Until Keyword Succeeds    20m    2    信号量数据值为    PU休眠状态-1    否
    ${snmp获取值1}    获取SNMP数据_单个    powerUnitSleepStatus1value
    should be equal    ${snmp获取值1}    0
    sleep    20
    ###唤醒2
    Comment    Wait Until Keyword Succeeds    3m    1m    设置SNMP控制量    pUWaken2value
    FOR    ${i}    IN RANGE    0    4
        ${设置结果}    设置SNMP控制量    pUWaken6value
        Exit For Loop if    '${设置结果}'=='True'
        sleep    20
    END
    Wait Until Keyword Succeeds    20m    2    信号量数据值为    PU休眠状态-6    否
    ${snmp获取值2}    获取SNMP数据_单个    powerUnitSleepStatus6value
    should be equal    ${snmp获取值2}    0
    sleep    20
    ###唤醒3
    FOR    ${i}    IN RANGE    0    4
        ${设置结果}    设置SNMP控制量    pUWaken8value
        Exit For Loop if    '${设置结果}'=='True'
        sleep    20
    END
    Wait Until Keyword Succeeds    20m    2    信号量数据值为    PU休眠状态-8    否
    ${snmp获取值3}    获取SNMP数据_单个    powerUnitSleepStatus8value
    should be equal    ${snmp获取值3}    0
    断开连接SNMP

snmp_0006_PU风扇调速允许√
    [Documentation]    允许时，状态为 自动
    ...
    ...    0:自动/Auto;1:全速/Full Speed
    [Setup]
    连接CSU
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ###允许1
    FOR    ${i}    IN RANGE    0    4
        ${设置结果}    设置SNMP控制量    pUFanControlEnable1value
        Exit For Loop if    '${设置结果}'=='True'
        sleep    20
    END
    Wait Until Keyword Succeeds    20m    2    信号量数据值为    PU风扇控制状态-1    自动
    ${snmp获取值1}    获取SNMP数据_单个    pUFanControlState1value
    should be equal    ${snmp获取值1}    0
    ###允许2
    FOR    ${i}    IN RANGE    0    4
        ${设置结果}    设置SNMP控制量    pUFanControlEnable6value
        Exit For Loop if    '${设置结果}'=='True'
        sleep    20
    END
    Wait Until Keyword Succeeds    20m    2    信号量数据值为    PU风扇控制状态-6    自动
    ${snmp获取值2}    获取SNMP数据_单个    pUFanControlState6value
    should be equal    ${snmp获取值2}    0
    ###允许3
    FOR    ${i}    IN RANGE    0    4
        ${设置结果}    设置SNMP控制量    pUFanControlEnable8value
        Exit For Loop if    '${设置结果}'=='True'
        sleep    20
    END
    Wait Until Keyword Succeeds    20m    2    信号量数据值为    PU风扇控制状态-8    自动
    ${snmp获取值3}    获取SNMP数据_单个    pUFanControlState8value
    should be equal    ${snmp获取值3}    0
    断开连接SNMP

snmp_0008_PU风扇调速禁止√
    [Documentation]    允许时，状态为 自动
    ...
    ...    0:自动/Auto;1:全速/Full Speed
    [Setup]
    连接CSU
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ###允许1
    Comment    Wait Until Keyword Succeeds    3m    1m    设置SNMP控制量    pUFanControlDisable1value
    FOR    ${i}    IN RANGE    0    4
        ${设置结果}    设置SNMP控制量    pUFanControlDisable1value
        Exit For Loop if    '${设置结果}'=='True'
        sleep    20
        Comment    should be true    ${设置控制量}
    END
    Wait Until Keyword Succeeds    20m    2    信号量数据值为    PU风扇控制状态-1    全速
    sleep    10
    ${snmp获取值1}    获取SNMP数据_单个    pUFanControlState1value
    should be equal    ${snmp获取值1}    1
    sleep    20
    ###允许2
    FOR    ${i}    IN RANGE    0    4
        ${设置结果}    设置SNMP控制量    pUFanControlDisable6value
        Exit For Loop if    '${设置结果}'=='True'
        sleep    20
    END
    Wait Until Keyword Succeeds    20m    2    信号量数据值为    PU风扇控制状态-6    全速
    sleep    20
    ${snmp获取值2}    获取SNMP数据_单个    pUFanControlState6value
    should be equal    ${snmp获取值2}    1
    sleep    20
    ###允许3
    FOR    ${i}    IN RANGE    0    4
        ${设置结果}    设置SNMP控制量    pUFanControlDisable8value
        Exit For Loop if    '${设置结果}'=='True'
        sleep    20
    END
    Wait Until Keyword Succeeds    20m    2    信号量数据值为    PU风扇控制状态-8    全速
    sleep    20s
    ${snmp获取值3}    获取SNMP数据_单个    pUFanControlState8value
    should be equal    ${snmp获取值3}    1
    sleep    20
    断开连接SNMP
