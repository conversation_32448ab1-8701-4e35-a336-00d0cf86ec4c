*** Settings ***
Suite Setup       测试用例前置条件
Suite Teardown    测试用例后置条件
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
snmp_0002_多个整流器模块告警
    [Documentation]    主要测试告警类5个级别的设置和8个干接点的设置。干接点实际是否动作需要将输出干接点连接到输入干接点，输出干接点接UIB_X3_DI1；请在测试前连接好
    [Tags]    T1-1
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    ${级别设置值}    获取web参数量    多个整流器模块告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    多个整流器模块告警    主要
    Wait Until Keyword Succeeds    15m    1    查询指定告警信息不为    多个整流器模块告警
    #使整流器输出过压来制造多个整流器告警
    ${均充电压可设置范围}    获取web参数可设置范围    均充电压
    设置web参数量    均充电压    ${均充电压可设置范围}[0]
    ${输出高停机电压可设置范围}    获取web参数可设置范围    整流器输出高停机电压
    设置web参数量    整流器输出高停机电压    ${输出高停机电压可设置范围}[0]
    ${整流器输出高电压}    获取web参数量    整流器输出高停机电压
    向上调节电池电压    ${整流器输出高电压} + 0.5
    Wait Until Keyword Succeeds    15m    1    信号量数据值为    工作整流器数量    0
    Wait Until Keyword Succeeds    15m    2    设置web参数量    多个整流器模块告警    屏蔽
    sleep    3
    Wait Until Keyword Succeeds    15m    1    判断告警不存在    多个整流器模块告警
    ###以上告警已产生，先屏蔽
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    Wait Until Keyword Succeeds    30    1    设置web参数量    多个整流器模块告警    主要
    wait until keyword succeeds    13m    2    判断告警存在    多个整流器模块告警
    #产生
    ${snmp英文名}    获取snmp单个告警英文名    多个整流器模块告警
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    #整流器过压恢复，5min应能恢复
    设置web设备参数量为默认值    整流器输出高停机电压
    设置web设备参数量为默认值    均充电压
    向下调节电池电压    53.5
    ${在线整流器数量}    获取web实时数据    在线整流器数量
    Comment    Wait Until Keyword Succeeds    5m20s    10    信号量数据值为    工作整流器数量    ${在线整流器数量}
    Wait Until Keyword Succeeds    15m    1    判断告警不存在    多个整流器模块告警
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    # [Teardown]    重置电池模拟器输出
