*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
恢复默认值参数测试
    [Setup]
    连接CSU
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    power_para.zip
    sleep    2m
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    100
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    ${channel_config}    显示属性配置    CPU利用率    模拟量    ON    ON
    @{channel_config}    获取显示属性配置    CPU利用率    模拟量
    should be equal    @{channel_config}[0]    ON
    should be equal    @{channel_config}[1]    ON
    Wait Until Keyword Succeeds    60    2    设置web参数量    <<禁止所有告警~0x1001030010001>>    次要
    ${告警级别获取}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    should be true    '${告警级别获取}' == '次要'
    Wait Until Keyword Succeeds    60    2    设置web参数量    系统过载告警    次要
    ${告警级别获取}    获取web参数量    系统过载告警
    should be true    '${告警级别获取}' == '次要'
    Wait Until Keyword Succeeds    60    2    设置web参数量    CPU利用率高阈值    10    #下限
    ${阈值获取}    获取web参数量    CPU利用率高阈值
    should be true    ${阈值获取}==10
    ${channel_config}    通道配置_AI    UIB_X4_T1    0.0000    3.0000    电池_1    电池温度
    should be true    ${channel_config} == True
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    should be true    ${电池温度}>40.0
    ${起始时间}    获取系统时间
    sleep    5
    恢复默认值并重新登录    #SUPERMAN用户支持用户参数和站点配置参数恢复，
    sleep    10
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    100
    should contain match    ${历史事件内容1}    *恢复默认值 配置和参数成功*
    @{channel_config}    获取显示属性配置    CPU利用率    模拟量
    should be equal    @{channel_config}[0]    OFF
    should be equal    @{channel_config}[1]    OFF
    ${告警级别获取}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    should be true    '${告警级别获取}'=='严重'
    ${告警级别获取}    获取web参数量    系统过载告警
    should be true    '${告警级别获取}'=='严重'
    ${CPU占用率高阈值获取}    获取web参数量    CPU利用率高阈值
    should be true    ${CPU占用率高阈值获取}==80
    ${电池温度}    获取web实时数据    电池温度-1    #配置参数恢复默认值后，电池温度应变为正常
    should be true    ${电池温度}>40.0
