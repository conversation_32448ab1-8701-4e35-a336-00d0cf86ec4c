*** Settings ***
Suite Setup       PU测试前置条件
Suite Teardown    PU测试结束条件
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
snmp_0002_solarEnergy1analogData
    ${比较结果}    对比数据_V2C    solarEnergy1analogData
    should be true    ${比较结果}

snmp_0004_solarEnergy1digitalData
    ${比较结果}    对比数据_V2C    solarEnergy1digitalData
    should be true    ${比较结果}

snmp_0006_solarEnergy1alarm_level
    ${比较结果}    对比告警级别_V2C    solarEnergy1alarm
    should be true    ${比较结果}

snmp_0008_solarEnergy1alarm_level_write
    ${比较结果}    批量设置四种/五种告警级别    solarEnergy1alarm
    should be true    ${比较结果}

snmp_0010_solarEnergy1alarm_relay
    ${比较结果}    对比告警干接点_V2C    solarEnergy1alarm
    should be true    ${比较结果}

snmp_0012_solarEnergy1alarm_relay_write
    ${比较结果}    批量修改告警干接点_V2C    solarEnergy1alarm
    should be true    ${比较结果}

snmp_0014_solarEnergy1control
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    设置子工具个数    pu    1
    sleep    3m
    ${级别设置值}    获取web参数量    PU通讯中断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    PU通讯中断    严重
    wait until keyword succeeds    10m    2    查询指定告警信息    PU通讯中断
    wait until keyword succeeds    30m    20    获取指定告警数量大于    PU通讯中断    46
    wait until keyword succeeds    1m    2    设置web控制量    PU设备统计
    FOR    ${i}    IN RANGE    0    4
        ${设置结果}    设置SNMP控制量    pUDeviceStatisticvalue
        ${存在结果}    判断web历史记录存在snmp控制内容    太阳能    PU设备统计    180    1
        Exit For Loop if    '${存在结果}'=='True'
        sleep    10
    END
    wait until keyword succeeds    23    2    查询指定告警信息不为    PU通讯中断
    断开连接SNMP
    [Teardown]    Run keywords    设置子工具个数    pu    48
    ...    AND    sleep    3m

snmp_0016_solarEnergy1parameter
    [Setup]
    ${比较结果}    对比数据_V2C    solarEnergy1parameter
    should be true    ${比较结果}
    [Teardown]

snmp_0018_solarEnergy1parameter_write
    ${比较结果}    SNMP批量参数设置    solarEnergy1parameter
    should be true    ${比较结果}
    [Teardown]

snmp_0020_solarEnergy1stasticData
    ${比较结果}    对比数据_V2C    solarEnergy1stasticData
    should be true    ${比较结果}
    [Teardown]
