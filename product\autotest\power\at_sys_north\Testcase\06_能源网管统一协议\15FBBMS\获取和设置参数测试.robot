*** Settings ***
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_获取FBBMS模拟量
    [Tags]    3
    ${能源网管数据}    能源网管协议_获取数据    获取FBBMS模拟量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取FBBMS模拟量
    Should Be True    ${数据对比结果}
    ${校验结果}    能源网管协议_校验数据上送完整性    ${能源网管数据}    FBBMS    analog data
    Comment   Should Be True    ${校验结果}

power_sm_0002_获取FBBMS数字量
    [Tags]    3
    ${能源网管数据}    能源网管协议_获取数据    获取FBBMS数字量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取FBBMS数字量
    Should Be True    ${数据对比结果}
    ${校验结果}    能源网管协议_校验数据上送完整性    ${能源网管数据}    FBBMS    digital data
    Comment   Should Be True    ${校验结果}

power_sm_0003_获取FBBMS统计量
    ${能源网管数据}    能源网管协议_获取数据    获取FBBMS统计量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取FBBMS统计量
    Should Be True    ${数据对比结果}
    ${校验结果}    能源网管协议_校验数据上送完整性    ${能源网管数据}    FBBMS    stastic data
    Comment  Should Be True    ${校验结果}

power_sm_0004_获取FBBMS参数量
    ${能源网管数据}    能源网管协议_获取数据    获取FBBMS参数量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取FBBMS参数量
    Should Be True    ${数据对比结果}
    ${校验结果}    能源网管协议_校验数据上送完整性    ${能源网管数据}    FBBMS    parameter
    Comment   Should Be True    ${校验结果}

power_sm_0005_设置FBBMS参数量
    ${sheet_name}    Set Variable    设置FBBMS参数
    ${能源网管数据}    能源网管协议_设置单个参数    ${sheet_name}    ${BOARD_IP}    ${SSH}
    ${对比结果}    能源网管协议_批量对比参数设置    ${能源网管数据}    设置FBBMS参数
    Should Be True    ${对比结果}

power_sm_0006_获取FBBMS设备信息
    ${能源网管数据}    能源网管协议_获取数据    获取FBBMS设备信息    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取FBBMS设备信息
    Should Be True    ${数据对比结果}
    ${校验结果}    能源网管协议_校验数据上送完整性    ${能源网管数据}    FBBMS    device info
    Comment   Should Be True    ${校验结果}

power_sm_0007_设置FBBMS控制量
    Comment    ${sheet_name}    Set Variable    设置FBBMS控制量
    Comment    ${能源网管数据}    能源网管协议_设置控制量    ${sheet_name}    ${BOARD_IP}    ${SSH}
    Comment    ${对比结果}    能源网管协议_批量对比参数设置    ${能源网管数据}    设置FBBMS控制量
    Comment    Should Be True    ${对比结果}
    @{锂电随机list}    从1-15个数中随机选n个不重复的单体    ${北向协议铁锂最大数}    4
    FOR    ${锂电序号}    IN    @{锂电随机list}
        run keyword and ignore error    Wait Until Keyword Succeeds    50    5    能源网管协议_设置单个设备单个控制量    FBBMS_${锂电序号}    4    BMS复位    ${SSH}
        run keyword and ignore error    Wait Until Keyword Succeeds    50    5    能源网管协议_设置单个设备单个控制量    FBBMS_${锂电序号}    4    BMS通讯中断告警清除    ${SSH}
    END
