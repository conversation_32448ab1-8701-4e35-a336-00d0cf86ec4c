*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
输出干接点通道配置测试
    连接CSU
    FOR    ${VAR}    IN RANGE    8    4    -1
        ${pos}    evaluate    str(${VAR}-1)
        ${VAR}    evaluate    str(${VAR})
        ${状态}    Run Keyword And Return Status    设置通道配置    ${plat.relay_out}[${pos}]    能源系统    应急照明状态    断开
        should be true    ${状态}
        sleep    5
        ${状态}    Run Keyword And Return Status    Wait Until Keyword Succeeds    5X    2    设置web参数量    系统过载告警干接点    ${VAR}
        should not be true    ${状态}
        wait until keyword succeeds    30    1    设置通道配置    ${plat.relay_out}[${pos}]    无    无    断开
        sleep    5
        ${状态}    Run Keyword And Return Status    Wait Until Keyword Succeeds    5X    2    设置web参数量    系统过载告警干接点    ${VAR}
        should be true    ${状态}
        Wait Until Keyword Succeeds    5X    2    设置web参数量    系统过载告警干接点    0
    END
    FOR    ${VAR}    IN RANGE    4    0    -1
        ${pos}    evaluate    str(${VAR}-1)
        ${VAR}    evaluate    str(${VAR})
        ${状态}    Run Keyword And Return Status    设置通道配置    ${plat.relay_out}[${pos}]    能源系统    应急照明状态    断开
        should be true    ${状态}
        sleep    5
        ${状态}    Run Keyword And Return Status    Wait Until Keyword Succeeds    5X    2    设置web参数量    系统过载告警干接点    ${VAR}
        should not be true    ${状态}
        wait until keyword succeeds    30    1    设置通道配置    ${plat.relay_out}[${pos}]    无    无    断开
        sleep    5
        ${状态}    Run Keyword And Return Status    Wait Until Keyword Succeeds    5X    2    设置web参数量    系统过载告警干接点    ${VAR}
        should be true    ${状态}
        Wait Until Keyword Succeeds    5X    2    设置web参数量    系统过载告警干接点    0
    END
    [Teardown]    run keywords    连接CSU
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web所有告警干接点为默认值
