*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
电池放电记录保存内容（停电进入均充退出）
    [Setup]    测试用例前置条件
    电池管理初始化
    ${开始数量}    获取web事件记录数量    电池放电记录
    run keyword if    ${开始数量}>995    删除历史记录    电池放电记录
    sleep    30
    ${开始数量}    获取web事件记录数量    电池放电记录
    ${电压设置值}    获取web参数量    均充电压
    向上调节电池电压    ${电压设置值}+1
    ${电池电流1}    获取web实时数据    电池电流-1
    run keyword if    ${电池电流1}>0    Wait Until Keyword Succeeds    30    1    设置通道配置    CIB_X9_IB1    -${电池电流1}    1    直流配电    电池分流器电流_1
    sleep    1m
    ${电池电流调节后1}    获取web实时数据    电池电流-1
    ${开始数量}    获取web事件记录数量    电池放电记录
    sleep    30
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动均充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    均充
    ${电压}    获取web实时数据    直流电压
    关闭交流源输出
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    系统停电
    #（1）产生充电电流
    #设置电池电流零点
    Wait Until Keyword Succeeds    30    1    设置通道配置    CIB_X9_IB1    -20    1    直流配电    电池分流器电流_1
    ${起始时间}    获取系统时间
    ${起始SOC}    获取web实时数据    电池组当前容量比率-1
    ${起始电池电压}    获取web实时数据    电池电压-1
    ${起始电量}    获取web实时数据    电池放电电量-1
    ${电池电流}    获取web实时数据    电池组总电流
    ${放电起始时间}    获取web实时数据    电池放电累计时间
    sleep    2m
    打开交流源输出
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    均充
    #（1）产生充电电流
    #设置电池电流零点
    Wait Until Keyword Succeeds    30    1    设置通道配置    CIB_X9_IB1    0    1    直流配电    电池分流器电流_1
    ${电池电流}    获取web实时数据    电池组总电流
    ${结束时间}    获取系统时间
    ${结束SOC}    获取web实时数据    电池组当前容量比率-1
    ${结束电池电压}    获取web实时数据    电池电压-1
    ${结束电量}    获取web实时数据    电池放电电量-1
    ${放电结束时间}    获取web实时数据    电池放电累计时间
    ${持续时间}    evaluate    ${放电结束时间}-${放电起始时间}
    sleep    30
    ${结束数量}    获取web事件记录数量    电池放电记录
    @{记录内容}    获取web事件记录最新一条    电池放电记录
    should be true    @{记录内容}[0] == ${结束数量}
    should be true    ${结束数量} == ${开始数量} + 1
    ${起始时间差}    subtract date from date    @{记录内容}[1]    ${起始时间}
    should be true    -20<=${起始时间差}<=20
    ${结束时间差}    subtract date from date    @{记录内容}[2]    ${结束时间}
    should be true    -20<=${结束时间差}<=20
    should be true    @{记录内容}[3] >= ${持续时间}
    should be equal    '@{记录内容}[4]'    '停电'
    should be equal    '@{记录内容}[5]'    '均充'
    should be true    @{记录内容}[6] == ${起始SOC}
    should be true    @{记录内容}[7] == ${结束SOC}
    should be true    ${起始电池电压}-0.3<= @{记录内容}[8] <= ${起始电池电压}+0.3
    should be true    ${结束电池电压}-0.3<= @{记录内容}[9] <= ${结束电池电压}+0.3
    should be true    ${起始电量}-0.2 <= @{记录内容}[10] <= ${起始电量} +0.2
    should be true    ${结束电量}-0.2<= @{记录内容}[11] <= ${结束电量}+0.2
    向下调节电池电压    ${电压设置值}
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    CIB_X9_IB1    0    1    直流配电    电池分流器电流_1
    ...    AND    重置电池模拟器输出
    ...    AND    打开交流源输出

电池放电记录保存内容（停电进入浮充退出）
    [Setup]    测试用例前置条件
    电池管理初始化
    ${开始数量}    获取web事件记录数量    电池放电记录
    run keyword if    ${开始数量}>995    删除历史记录    电池放电记录
    sleep    30
    ${开始数量}    获取web事件记录数量    电池放电记录
    ${电压设置值}    获取web参数量    浮充电压
    向上调节电池电压    ${电压设置值}+1
    ${电池电流1}    获取web实时数据    电池电流-1
    run keyword if    ${电池电流1}>0    Wait Until Keyword Succeeds    30    1    设置通道配置    CIB_X9_IB1    -${电池电流1}    1    直流配电    电池分流器电流_1
    sleep    1m
    ${电池电流调节后1}    获取web实时数据    电池电流-1
    ${开始数量}    获取web事件记录数量    电池放电记录
    sleep    30
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    ${电压}    获取web实时数据    直流电压
    关闭交流源输出
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    系统停电
    #（1）产生充电电流
    #设置电池电流零点
    Wait Until Keyword Succeeds    30    1    设置通道配置    CIB_X9_IB1    -20    1    直流配电    电池分流器电流_1
    ${起始时间}    获取系统时间
    ${起始SOC}    获取web实时数据    电池组当前容量比率-1
    ${起始电池电压}    获取web实时数据    电池电压-1
    ${起始电量}    获取web实时数据    电池放电电量-1
    ${电池电流}    获取web实时数据    电池组总电流
    ${放电起始时间}    获取web实时数据    电池放电累计时间
    sleep    2m
    打开交流源输出
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    #（1）产生充电电流
    #设置电池电流零点
    Wait Until Keyword Succeeds    30    1    设置通道配置    CIB_X9_IB1    0    1    直流配电    电池分流器电流_1
    ${电池电流}    获取web实时数据    电池组总电流
    ${结束时间}    获取系统时间
    ${结束SOC}    获取web实时数据    电池组当前容量比率-1
    ${结束电池电压}    获取web实时数据    电池电压-1
    ${结束电量}    获取web实时数据    电池放电电量-1
    ${放电结束时间}    获取web实时数据    电池放电累计时间
    ${持续时间}    evaluate    ${放电结束时间}-${放电起始时间}
    sleep    30
    ${结束数量}    获取web事件记录数量    电池放电记录
    @{记录内容}    获取web事件记录最新一条    电池放电记录
    should be true    @{记录内容}[0] == ${结束数量}
    should be true    ${结束数量} == ${开始数量} + 1
    ${起始时间差}    subtract date from date    @{记录内容}[1]    ${起始时间}
    should be true    -20<=${起始时间差}<=20
    ${结束时间差}    subtract date from date    @{记录内容}[2]    ${结束时间}
    should be true    -20<=${结束时间差}<=20
    should be true    @{记录内容}[3] >= ${持续时间}
    should be equal    '@{记录内容}[4]'    '停电'
    should be equal    '@{记录内容}[5]'    '浮充'
    should be true    @{记录内容}[6] == ${起始SOC}
    should be true    @{记录内容}[7] == ${结束SOC}
    should be true    ${起始电池电压}-0.3<= @{记录内容}[8] <= ${起始电池电压}+0.3
    should be true    ${结束电池电压}-0.3<= @{记录内容}[9] <= ${结束电池电压}+0.3
    should be true    ${起始电量}-0.2 <= @{记录内容}[10] <= ${起始电量} +0.2
    should be true    ${结束电量}-0.2<= @{记录内容}[11] <= ${结束电量}+0.2
    向下调节电池电压    ${电压设置值}
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    CIB_X9_IB1    0    1    直流配电    电池分流器电流_1
    ...    AND    重置电池模拟器输出
    ...    AND    打开交流源输出

电池放电记录保存内容（测试进入均充退出）
    [Setup]    测试用例前置条件
    电池管理初始化
    ${开始数量}    获取web事件记录数量    电池放电记录
    run keyword if    ${开始数量}>995    删除历史记录    电池放电记录
    sleep    30
    ${开始数量}    获取web事件记录数量    电池放电记录
    ${电压设置值}    获取web参数量    浮充电压
    向上调节电池电压    ${电压设置值}+1
    ${电池电流1}    获取web实时数据    电池电流-1
    run keyword if    ${电池电流1}>0    Wait Until Keyword Succeeds    30    1    设置通道配置    CIB_X9_IB1    -${电池电流1}    1    直流配电    电池分流器电流_1
    sleep    1m
    ${电池电流调节后1}    获取web实时数据    电池电流-1
    ${开始数量}    获取web事件记录数量    电池放电记录
    sleep    30
    ${缺省值}    获取web参数上下限范围_有单位    测试最长时间
    ${可设置范围}    获取web参数可设置范围    测试最长时间
    ${测试最长时间设置值}    run keyword if    '@{缺省值}[3]'== 'Hour'    evaluate    @{可设置范围}[0]+1
    ...    ELSE    evaluate    @{可设置范围}[0]+5
    ${测试最长时间转换后}    run keyword if    '@{缺省值}[3]'== 'Hour'    evaluate    ${测试最长时间设置值}*60
    ...    ELSE    set variable    ${测试最长时间设置值}
    Comment    ${设置值}    evaluate    @{可设置范围}[0]+5
    设置web参数量    测试最长时间    ${测试最长时间设置值}
    设置web参数量    均充使能    允许
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动测试
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    测试
    Comment    sleep    5m15s
    ${等待时间}    evaluate    ${测试最长时间转换后}+2
    #（1）产生充电电流
    #设置电池电流零点
    Wait Until Keyword Succeeds    30    1    设置通道配置    CIB_X9_IB1    -20    1    直流配电    电池分流器电流_1
    ${起始时间}    获取系统时间
    ${起始SOC}    获取web实时数据    电池组当前容量比率-1
    ${起始电池电压}    获取web实时数据    电池电压-1
    ${起始电量}    获取web实时数据    电池放电电量-1
    ${电池电流}    获取web实时数据    电池组总电流
    ${放电起始时间}    获取web实时数据    电池放电累计时间
    Wait Until Keyword Succeeds    ${等待时间}m    1    信号量数据值为    电池管理状态    均充
    #（1）产生充电电流
    #设置电池电流零点
    Wait Until Keyword Succeeds    30    1    设置通道配置    CIB_X9_IB1    0    1    直流配电    电池分流器电流_1
    ${电池电流}    获取web实时数据    电池组总电流
    ${结束时间}    获取系统时间
    ${结束SOC}    获取web实时数据    电池组当前容量比率-1
    ${结束电池电压}    获取web实时数据    电池电压-1
    ${结束电量}    获取web实时数据    电池放电电量-1
    ${放电结束时间}    获取web实时数据    电池放电累计时间
    ${持续时间}    evaluate    ${放电结束时间}-${放电起始时间}
    sleep    30
    ${结束数量}    获取web事件记录数量    电池放电记录
    @{记录内容}    获取web事件记录最新一条    电池放电记录
    should be true    @{记录内容}[0] == ${结束数量}
    should be true    ${结束数量} == ${开始数量} + 1
    ${起始时间差}    subtract date from date    @{记录内容}[1]    ${起始时间}
    should be true    -20<=${起始时间差}<=20
    ${结束时间差}    subtract date from date    @{记录内容}[2]    ${结束时间}
    should be true    -20<=${结束时间差}<=20
    should be true    @{记录内容}[3] >= ${持续时间}
    should be equal    '@{记录内容}[4]'    '测试'
    should be equal    '@{记录内容}[5]'    '均充'
    should be true    @{记录内容}[6] == ${起始SOC}
    should be true    @{记录内容}[7] == ${结束SOC}
    should be true    ${起始电池电压}-0.3<= @{记录内容}[8] <= ${起始电池电压}+0.3
    should be true    ${结束电池电压}-0.3<= @{记录内容}[9] <= ${结束电池电压}+0.3
    should be true    ${起始电量}-0.2 <= @{记录内容}[10] <= ${起始电量} +0.2
    should be true    ${结束电量}-0.2<= @{记录内容}[11] <= ${结束电量}+0.2
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    CIB_X9_IB1    0    1    直流配电    电池分流器电流_1
    ...    AND    重置电池模拟器输出

电池放电记录保存内容（检测进入浮充退出）
    [Setup]    测试用例前置条件
    电池管理初始化
    ${开始数量}    获取web事件记录数量    电池放电记录
    run keyword if    ${开始数量}>995    删除历史记录    电池放电记录
    sleep    30
    ${开始数量}    获取web事件记录数量    电池放电记录
    ${电压设置值}    获取web参数量    浮充电压
    向上调节电池电压    ${电压设置值}+1
    ${电池电流1}    获取web实时数据    电池电流-1
    run keyword if    ${电池电流1}>0    Wait Until Keyword Succeeds    30    1    设置通道配置    CIB_X9_IB1    -${电池电流1}    1    直流配电    电池分流器电流_1
    sleep    1m
    ${电池电流调节后1}    获取web实时数据    电池电流-1
    ${开始数量}    获取web事件记录数量    电池放电记录
    sleep    30
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    检测
    ${电压}    获取web实时数据    直流电压
    #（1）产生充电电流
    #设置电池电流零点
    Wait Until Keyword Succeeds    30    1    设置通道配置    CIB_X9_IB1    -20    1    直流配电    电池分流器电流_1
    ${起始时间}    获取系统时间
    ${起始SOC}    获取web实时数据    电池组当前容量比率-1
    ${起始电池电压}    获取web实时数据    电池电压-1
    ${起始电量}    获取web实时数据    电池放电电量-1
    ${电池电流}    获取web实时数据    电池组总电流
    ${放电起始时间}    获取web实时数据    电池放电累计时间
    sleep    1m
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    #（1）产生充电电流
    #设置电池电流零点
    Wait Until Keyword Succeeds    30    1    设置通道配置    CIB_X9_IB1    0    1    直流配电    电池分流器电流_1
    ${电池电流}    获取web实时数据    电池组总电流
    ${结束时间}    获取系统时间
    ${结束SOC}    获取web实时数据    电池组当前容量比率-1
    ${结束电池电压}    获取web实时数据    电池电压-1
    ${结束电量}    获取web实时数据    电池放电电量-1
    ${放电结束时间}    获取web实时数据    电池放电累计时间
    ${持续时间}    evaluate    ${放电结束时间}-${放电起始时间}
    sleep    30
    ${结束数量}    获取web事件记录数量    电池放电记录
    @{记录内容}    获取web事件记录最新一条    电池放电记录
    should be true    @{记录内容}[0] == ${结束数量}
    should be true    ${结束数量} == ${开始数量} + 1
    ${起始时间差}    subtract date from date    @{记录内容}[1]    ${起始时间}
    should be true    -20<=${起始时间差}<=20
    ${结束时间差}    subtract date from date    @{记录内容}[2]    ${结束时间}
    should be true    -20<=${结束时间差}<=20
    should be true    @{记录内容}[3] >= ${持续时间}
    should be equal    '@{记录内容}[4]'    '检测'
    should be equal    '@{记录内容}[5]'    '浮充'
    should be true    @{记录内容}[6] == ${起始SOC}
    should be true    @{记录内容}[7] == ${结束SOC}
    should be true    ${起始电池电压}-0.3<= @{记录内容}[8] <= ${起始电池电压}+0.3
    should be true    ${结束电池电压}-0.3<= @{记录内容}[9] <= ${结束电池电压}+0.3
    should be true    ${起始电量}-0.2 <= @{记录内容}[10] <= ${起始电量} +0.2
    should be true    ${结束电量}-0.2<= @{记录内容}[11] <= ${结束电量}+0.2
    向下调节电池电压    ${电压设置值}
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    CIB_X9_IB1    0    1    直流配电    电池分流器电流_1
    ...    AND    重置电池模拟器输出

电池放电记录掉电保存
    [Setup]    测试用例前置条件
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    ${记录数量}    获取web事件记录数量    电池放电记录
    run keyword if    ${记录数量}>990    删除历史记录    电池放电记录
    测试台上下电操作    T1-1    OFF
    sleep    15
    测试台上下电操作    T1-1    ON
    实时告警刷新完成
    sleep    5
    ${记录数量1}    获取web事件记录数量    电池放电记录
    should be true    ${记录数量}==${记录数量1}

电池放电记录最大条数测试
    [Setup]    测试用例前置条件
    电池管理初始化
    sleep    20
    ${电压设置值}    获取web参数量    浮充电压
    向上调节电池电压    ${电压设置值}+1
    ${电池电流1}    获取web实时数据    电池电流-1
    run keyword if    ${电池电流1}>0    Wait Until Keyword Succeeds    30    1    设置通道配置    CIB_X9_IB1    -${电池电流1}    1    直流配电    电池分流器电流_1
    sleep    1m
    ${电池电流调节后1}    获取web实时数据    电池电流-1
    #产生充电记录
    FOR    ${k}    IN RANGE    1001
    Wait Until Keyword Succeeds    10    1    设置web控制量    启动均充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    均充
    ${电压}    获取web实时数据    直流电压
    #（1）产生充电电流
    #设置电池电流零点
    Wait Until Keyword Succeeds    30    1    设置通道配置    CIB_X9_IB1    -20    1    直流配电    电池分流器电流_1
    sleep    2m
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    #（1）产生充电电流
    #设置电池电流零点
    Wait Until Keyword Succeeds    30    1    设置通道配置    CIB_X9_IB1    0    1    直流配电    电池分流器电流_1
    ${电池电流}    获取web实时数据    电池组总电流
    sleep    30
    ${记录数量}    获取web事件记录数量    电池放电记录
    exit for loop if    ${记录数量}>=1002
    sleep    2m
    ${记录数量}    获取web事件记录数量    电池放电记录
    should be true    ${记录数量} == 1000
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动均充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    均充
    ${电压}    获取web实时数据    直流电压
    #（1）产生充电电流
    #设置电池电流零点
    Wait Until Keyword Succeeds    30    1    设置通道配置    CIB_X9_IB1    -20    1    直流配电    电池分流器电流_1
    sleep    2m
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    #（1）恢复充电电流
    #设置电池电流零点
    Wait Until Keyword Succeeds    30    1    设置通道配置    CIB_X9_IB1    0    1    直流配电    电池分流器电流_1
    sleep    30
    ${记录数量}    获取web事件记录数量    电池放电记录
    should be true    ${记录数量} == 1000
    向下调节电池电压    ${电压设置值}
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    CIB_X9_IB1    0    1    直流配电    电池分流器电流_1
    ...    AND    重置电池模拟器输出
