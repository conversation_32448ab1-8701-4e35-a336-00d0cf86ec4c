*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
获取直流空调参数量
    ${能源网管数据}    能源网管协议_获取数据    获取直流空调参数量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取直流空调参数量
    Should Be True    ${数据对比结果}

设置直流空调参数量
    ${能源网管数据}    能源网管协议_设置单个参数    设置直流空调参数    ${BOARD_IP}    ${SSH}
    ${对比结果}    能源网管协议_批量对比参数设置    ${能源网管数据}    设置直流空调参数
    Should Be True    ${对比结果}

获取直流空调告警级别
    ${能源网管数据}    能源网管协议_获取数据    获取直流空调告警级别    ${SSH}
    ${web数据}    能源网管协议_获取web对应告警级别    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取直流空调告警级别
    Should Be True    ${数据对比结果}

设置直流空调告警级别
    ${sheet_name}    Set Variable    设置直流空调告警级别
    ${能源网管数据}    能源网管协议_设置单个告警    ${sheet_name}    ${BOARD_IP}    ${SSH}
    ${对比结果}    能源网管协议_批量对比参数设置    ${能源网管数据}    设置直流空调告警级别
    Should Be True    ${对比结果}

获取直流空调告警输出干接点
    ${能源网管数据}    能源网管协议_获取数据    获取直流空调告警输出干接点    ${SSH}
    ${web数据}    能源网管协议_获取web对应告警输出干接点    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取直流空调告警输出干接点
    Should Be True    ${数据对比结果}

设置直流空调告警输出干接点
    ${sheet_name}    Set Variable    设置直流空调告警输出干接点
    ${能源网管数据}    能源网管协议_设置单个告警输出干接点    ${sheet_name}    ${BOARD_IP}    ${SSH}
    ${对比结果}    能源网管协议_批量对比参数设置    ${能源网管数据}    设置直流空调告警输出干接点
    Should Be True    ${对比结果}

设置直流空调控制量
    [Setup]
    FOR    ${直流空调序号}    IN RANGE    1    ${直流空调最大数}+1
        Wait Until Keyword Succeeds    10    2    设置web参数量    直流空调工作模式-${直流空调序号}    手动模式
    END
    sleep    30
    FOR    ${直流空调序号}    IN RANGE    1    ${直流空调最大数}+1
        ${控制成功}    能源网管协议_设置单个设备单个控制量    直流空调_${直流空调序号}    6    直流空调关闭    ${SSH}
        should be true    ${控制成功}
        ${控制成功}    能源网管协议_设置单个设备单个控制量    直流空调_${直流空调序号}    6    直流空调开启    ${SSH}
        should be true    ${控制成功}
    END
