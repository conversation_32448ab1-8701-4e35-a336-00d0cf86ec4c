*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
交流制式参数测试
    [Tags]    PMSA-NTest    Restr
    [Setup]    判断web参数是否存在    交流制式
    显示属性配置    交流接线方式_1    数字量    web_attr=ON    gui_attr=ON
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    交流制式
    ${参数设置范围dict}    获取web参数的取值约定    交流制式
    ${参数设置范围values}    get dictionary values    ${参数设置范围dict}
    ${结果判定范围}    set variable    L1    L1-L2    L1    L1-L2
    FOR    ${参数设置}    ${结果判定}    IN ZIP    ${参数设置范围values}    ${结果判定范围}
        run keyword if    '${参数设置}'=='L-N(220V)' or '${参数设置}'=='L-N(110V)' or '${参数设置}'=='L1N(220V)' or '${参数设置}'=='L1L2(110V)'    run keyword and ignore error    设置web参数量    空调接入相位    L1
        设置web参数量    交流制式    ${参数设置}
        sleep    3
        ${结果获取}    获取web实时数据    交流接线方式_1
        should be equal    ${结果获取}    ${结果判定}
    END
    显示属性配置    交流接线方式_1    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    设置web设备参数量为默认值    交流制式

市电配置参数测试
    [Tags]    PMSA-NTest    Restr
    [Setup]    判断web参数是否存在    市电配置
    ${取值约定dict}    获取web参数的取值约定    市电配置
    ${取值约定values}    get dictionary values    ${取值约定dict}
    ${市电配置数量}    获取web参数的数量    市电配置
    FOR    ${i}    IN RANGE    ${市电配置数量}    0    -1
        嵌入for循环    市电配置_${i}    ${取值约定values}
    END
    [Teardown]    run keywords    设置web参数量    市电配置_1    有
    ...    AND    设置web参数量    市电配置_2    无

电池组容量参数测试
    [Tags]    view    PMSA-NTest    Restr
    [Setup]    判断web参数是否存在    电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    ${电池组当前容量比率}    获取web参数的数量    电池组当前容量比率
    ${缺省值}    获取web参数上下限范围    电池组容量
    ${可设置范围}    获取web参数可设置范围    电池组容量_1
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    ${数量}    run keyword and ignore error    获取web参数的数量    主从机选择    #并机系统只测试主机
    ${测试电池组数量}    run keyword if    '${数量}[0]'=='PASS' and '${数量}[1]'=='1'    set variable    4
    ...    ELSE    set variable    ${电池组数量}
    FOR    ${i}    IN RANGE    ${测试电池组数量}    0    -1
        ${原参数}    Wait Until Keyword Succeeds    30    2    获取web参数量    电池组容量_${i}
    #(1)超范围设置不成功
    #超下限
        ${设置结果}    run keyword and return status    设置web参数量    电池组容量_${i}    ${超下限}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    电池组容量_${i}
        should be true    ${参数获取}==${原参数}
    #超上限
        ${设置结果}    run keyword and return status    设置web参数量    电池组容量_${i}    ${超上限}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    电池组容量_${i}
        should be true    ${参数获取}==${原参数}
    #(2)范围内设置成功
    #容量设置为10AH（不含）以下认为是无效电池容量
    #设置一次
        设置web参数量    电池组容量_${i}    0
        sleep    3
        ${参数获取0}    获取web参数量    电池组容量_${i}
        should be true    ${参数获取0}==0
        Comment    ${参数获取0}    获取web实时数据    电池组当前容量比率-${i}
        Comment    should be true    '${参数获取0}'==''
    #设置两次
        设置web参数量    电池组容量_${i}    5
        sleep    3
        ${参数获取0}    获取web参数量    电池组容量_${i}
        should be true    ${参数获取0}==5
        Comment    ${参数获取0}    获取web实时数据    电池组当前容量比率_${i}
        Comment    should be true    '${参数获取0}'==''
    #容量设置为10AH（含）以上才有效
    #设置一次
        设置web参数量    电池组容量_${i}    10
        sleep    8
        ${参数获取1}    获取web参数量    电池组容量_${i}
        should be true    ${参数获取1}==10
        ${参数获取1}    获取web实时数据    电池组当前容量比率-${i}
        should be true    ${参数获取1}==100
    #设置两次
        设置web参数量    电池组容量_${i}    ${可设置范围}[1]
        sleep    8
        ${参数获取1}    获取web参数量    电池组容量_${i}
        should be true    ${参数获取1}==${可设置范围}[1]
        ${参数获取1}    获取web实时数据    电池组当前容量比率-${i}
        should be true    ${参数获取1}==100
    #设置三次（缺省值）
        设置web参数量    电池组容量_${i}    ${缺省值}[0]
        sleep    8
        ${参数获取1}    获取web参数量    电池组容量_${i}
        should be true    ${参数获取1}==${缺省值}[0]
        ${参数获取1}    获取web实时数据    电池组当前容量比率-${i}
        should be true    ${参数获取1}==100
    END
    #只设置一组电池组
    FOR    ${i}    IN RANGE    ${电池组数量}    0    -1
        设置web参数量    电池组容量_${i}    0
    END
    设置web参数量    电池组容量_1    100
    [Teardown]    设置web参数量    电池组容量_1    100

交流节能模式测试
    [Tags]    PMSA-NTest    Restr
    [Setup]    run keywords    测试用例前置条件
    ...    AND    判断web参数是否存在    交流节能模式
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    交流节能模式
    ${缺省值}    获取web参数上下限范围    交流节能模式
    ${取值约定dict}    获取web参数的取值约定    交流节能模式
    ${取值约定values}    get dictionary values    ${取值约定dict}
    ${val}    Get From Dictionary    ${取值约定dict}    ${缺省值}[0]
    FOR    ${参数设置}    IN    @{取值约定values}
        Wait Until Keyword Succeeds    10    2    设置web参数量    交流节能模式    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    交流节能模式
        should be equal    ${参数获取}    ${参数设置}
    END
    run keyword if    '${val}' != '${原参数}'    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    ${val}
    [Teardown]    设置web设备参数量为默认值    交流节能模式

电池应用场景测试
    [Tags]    PMSA-NTest    Restr
    [Setup]    判断web参数是否存在    电池应用场景
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    电池应用场景
    ${缺省值}    获取web参数上下限范围    电池应用场景
    ${取值约定dict}    获取web参数的取值约定    电池应用场景
    ${取值约定values}    get dictionary values    ${取值约定dict}
    ${val}    Get From Dictionary    ${取值约定dict}    ${缺省值}[0]
    FOR    ${参数设置}    IN    @{取值约定values}
        Wait Until Keyword Succeeds    10    2    设置web参数量    电池应用场景    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    电池应用场景
        should be equal    ${参数获取}    ${参数设置}
    END
    run keyword if    '${val}' != '${原参数}'    Wait Until Keyword Succeeds    10    1    设置web参数量    电池应用场景    ${val}
    [Teardown]    设置web设备参数量为默认值    电池应用场景

市电电池优先级设置测试
    [Tags]    PMSA-NTest    Restr
    [Setup]    Run keywords    判断web参数是否存在    交流输入场景
    ...    AND    设置web参数量    交流输入场景    油电
    ...    AND    判断web参数是否存在    电池应用场景
    ...    AND    设置web参数量    电池应用场景    循环场景
    ...    AND    判断web参数是否存在    市电电池优先级设置
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    市电电池优先级设置
    ${缺省值}    获取web参数上下限范围    市电电池优先级设置
    ${取值约定dict}    获取web参数的取值约定    市电电池优先级设置
    ${取值约定values}    get dictionary values    ${取值约定dict}
    ${val}    Get From Dictionary    ${取值约定dict}    ${缺省值}[0]
    FOR    ${参数设置}    IN    @{取值约定values}
        Wait Until Keyword Succeeds    10    2    设置web参数量    市电电池优先级设置    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    市电电池优先级设置
        should be equal    ${参数获取}    ${参数设置}
    END
    run keyword if    '${val}' != '${原参数}'    Wait Until Keyword Succeeds    10    1    设置web参数量    市电电池优先级设置    ${val}
    [Teardown]    设置web设备参数量为默认值    市电电池优先级设置    电池应用场景    交流输入场景

智能锂电类型测试
    [Tags]    PMSA-NTest    Restr
    [Setup]    Run keywords    判断web参数是否存在    电池配置
    ...    AND    设置web参数量    电池配置    铅酸&锂电混用
    ...    AND    判断web参数是否存在    智能锂电类型
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    智能锂电类型
    ${缺省值}    获取web参数上下限范围    智能锂电类型
    ${取值约定dict}    获取web参数的取值约定    智能锂电类型
    ${取值约定values}    get dictionary values    ${取值约定dict}
    ${val}    Get From Dictionary    ${取值约定dict}    ${缺省值}[0]
    FOR    ${参数设置}    IN    @{取值约定values}
        Wait Until Keyword Succeeds    10    2    设置web参数量    智能锂电类型    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    智能锂电类型
        should be equal    ${参数获取}    ${参数设置}
    END
    run keyword if    '${val}' != '${原参数}'    Wait Until Keyword Succeeds    10    1    设置web参数量    智能锂电类型    ${val}
    [Teardown]    设置web设备参数量为默认值    智能锂电类型    电池配置

常规锂电类型测试
    [Tags]    PMSA-NTest    Restr
    [Setup]    Run keywords    判断web参数是否存在    电池配置
    ...    AND    设置web参数量    电池配置    常规锂电池
    ...    AND    判断web参数是否存在    常规锂电类型
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    常规锂电类型
    ${缺省值}    获取web参数上下限范围    常规锂电类型
    ${取值约定dict}    获取web参数的取值约定    常规锂电类型
    ${取值约定values}    get dictionary values    ${取值约定dict}
    ${val}    Get From Dictionary    ${取值约定dict}    ${缺省值}[0]
    FOR    ${参数设置}    IN    @{取值约定values}
        Wait Until Keyword Succeeds    10    2    设置web参数量    常规锂电类型    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    常规锂电类型
        should be equal    ${参数获取}    ${参数设置}
    END
    run keyword if    '${val}' != '${原参数}'    Wait Until Keyword Succeeds    10    1    设置web参数量    常规锂电类型    ${val}
    [Teardown]    设置web设备参数量为默认值    常规锂电类型    电池配置

铅酸类型测试
    [Tags]    PMSA-NTest    Restr
    [Setup]    Run keywords    判断web参数是否存在    电池配置
    ...    AND    设置web参数量    电池配置    铅酸&锂电混用
    ...    AND    判断web参数是否存在    铅酸类型
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    铅酸类型
    ${缺省值}    获取web参数上下限范围    铅酸类型
    ${取值约定dict}    获取web参数的取值约定    铅酸类型
    ${取值约定values}    get dictionary values    ${取值约定dict}
    ${val}    Get From Dictionary    ${取值约定dict}    ${缺省值}[0]
    FOR    ${参数设置}    IN    @{取值约定values}
        Wait Until Keyword Succeeds    10    2    设置web参数量    铅酸类型    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    铅酸类型
        should be equal    ${参数获取}    ${参数设置}
    END
    run keyword if    '${val}' != '${原参数}'    Wait Until Keyword Succeeds    10    1    设置web参数量    铅酸类型    ${val}
    [Teardown]    设置web设备参数量为默认值    铅酸类型    电池配置

电池配置测试
    [Tags]    PMSA-NTest    Restr
    [Setup]    判断web参数是否存在    电池配置
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    电池配置
    ${缺省值}    获取web参数上下限范围    电池配置
    ${取值约定dict}    获取web参数的取值约定    电池配置
    ${取值约定values}    get dictionary values    ${取值约定dict}
    ${val}    Get From Dictionary    ${取值约定dict}    ${缺省值}[0]
    FOR    ${参数设置}    IN    @{取值约定values}
        Wait Until Keyword Succeeds    10    2    设置web参数量    电池配置    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    电池配置
        should be equal    ${参数获取}    ${参数设置}
    END
    run keyword if    '${val}' != '${原参数}'    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    ${val}
    [Teardown]    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    纯铅酸

交流输入场景测试
    [Tags]    PMSA-NTest    Restr
    [Setup]    判断web参数是否存在    交流输入场景
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    交流输入场景
    ${缺省值}    获取web参数上下限范围    交流输入场景
    ${取值约定dict}    获取web参数的取值约定    交流输入场景
    ${取值约定values}    get dictionary values    ${取值约定dict}
    ${val}    Get From Dictionary    ${取值约定dict}    ${缺省值}[0]
    FOR    ${参数设置}    IN    @{取值约定values}
        Wait Until Keyword Succeeds    10    2    设置web参数量    交流输入场景    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    交流输入场景
        should be equal    ${参数获取}    ${参数设置}
    END
    run keyword if    '${val}' != '${原参数}'    Wait Until Keyword Succeeds    10    1    设置web参数量    交流输入场景    ${val}
    [Teardown]    设置web参数量    交流输入场景    市电
