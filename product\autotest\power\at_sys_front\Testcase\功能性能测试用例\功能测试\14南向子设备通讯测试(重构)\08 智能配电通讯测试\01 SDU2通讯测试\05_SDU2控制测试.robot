*** Settings ***
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
配电单元电表清零测试
    连接CSU
    设置子工具值    SDU2    1    呼叫    SDU电量1    12522
    设置子工具值    SDU2    1    呼叫    SDU电量2    12522
    设置子工具值    SDU2    2    呼叫    SDU电量1    20131
    设置子工具值    SDU2    2    呼叫    SDU电量2    20131
    设置子工具值    SDU2    1    批量查询私有参数    SDU电量参数1    120
    设置子工具值    SDU2    1    批量查询私有参数    SDU电量参数2    130
    设置子工具值    SDU2    2    批量查询私有参数    SDU电量参数1    140
    设置子工具值    SDU2    2    批量查询私有参数    SDU电量参数2    150
    sleep    30
    ${电量1}    获取web实时数据    配电单元电量_1-1
    ${电量2}    获取web实时数据    配电单元电量_2-1
    ${电量3}    获取web实时数据    配电单元电量_1-2
    ${电量4}    获取web实时数据    配电单元电量_2-2
    should be true    ${电量1}==12522
    should be true    ${电量2}==12522
    should be true    ${电量3}==20131
    should be true    ${电量4}==20131
    ${SDU2电量参数1}    获取子工具值    SDU2    1    批量查询私有参数    SDU电量参数1
    @{SDU2电量参数1}    split string    @{SDU2电量参数1}    ,
    ${SDU2电量参数2}    获取子工具值    SDU2    1    批量查询私有参数    SDU电量参数2
    @{SDU2电量参数2}    split string    @{SDU2电量参数2}    ,
    ${SDU2电量参数3}    获取子工具值    SDU2    2    批量查询私有参数    SDU电量参数1
    @{SDU2电量参数3}    split string    @{SDU2电量参数3}    ,
    ${SDU2电量参数4}    获取子工具值    SDU2    2    批量查询私有参数    SDU电量参数2
    @{SDU2电量参数4}    split string    @{SDU2电量参数4}    ,
    should be true    ${SDU2电量参数1}[2]==120.0
    should be true    ${SDU2电量参数2}[2]==130.0
    should be true    ${SDU2电量参数3}[2]==140.0
    should be true    ${SDU2电量参数4}[2]==150.0
    Wait Until Keyword Succeeds    10X    2    设置web控制量    配电单元电量清零_1-1
    Comment    Wait Until Keyword Succeeds    5m    5    信号量数据值为    配电单元电量_1-1    0
    ${SDU2电量参数1}    获取子工具值    SDU2    1    批量查询私有参数    SDU电量参数1
    @{SDU2电量参数1}    split string    @{SDU2电量参数1}    ,
    should be true    ${SDU2电量参数1}[2]==0
    Wait Until Keyword Succeeds    10X    2    设置web控制量    配电单元电量清零_2-1
    Comment    Wait Until Keyword Succeeds    5m    5    信号量数据值为    配电单元电量_2-1    0
    ${SDU2电量参数2}    获取子工具值    SDU2    1    批量查询私有参数    SDU电量参数2
    @{SDU2电量参数2}    split string    @{SDU2电量参数2}    ,
    should be true    ${SDU2电量参数2}[2]==0
    Wait Until Keyword Succeeds    10X    2    设置web控制量    配电单元电量清零_1-2
    Comment    Wait Until Keyword Succeeds    5m    5    信号量数据值为    配电单元电量_1-2    0
    ${SDU2电量参数3}    获取子工具值    SDU2    2    批量查询私有参数    SDU电量参数1
    @{SDU2电量参数3}    split string    @{SDU2电量参数3}    ,
    should be true    ${SDU2电量参数3}[2]==0
    Wait Until Keyword Succeeds    10X    2    设置web控制量    配电单元电量清零_2-2
    Comment    Wait Until Keyword Succeeds    5m    5    信号量数据值为    配电单元电量_2-2    0
    ${SDU2电量参数4}    获取子工具值    SDU2    2    批量查询私有参数    SDU电量参数2
    @{SDU2电量参数4}    split string    @{SDU2电量参数4}    ,
    should be true    ${SDU2电量参数4}[2]==0
