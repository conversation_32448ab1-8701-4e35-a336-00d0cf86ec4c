*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
snmp批量获取SSW数字量
    写入CSV文档    SSW数字量获取测试    信号名称    信号值    结果    备注
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=digital data    INDEX_DEVICE_CH=智能空开
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SSW排除数字量信号}    ${排除列表}    1
    ${设备名称}    Set Variable    smartSwitch
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    ${设备名称}    digitalData
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    1    snmp
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    snmp    DMU_IntelAirSwit    呼叫    ${缺省值列表}    SSW数字量获取测试    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    2    snmp
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    snmp    DMU_IntelAirSwit    呼叫    ${缺省值列表}    SSW数字量获取测试    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    0    snmp
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    snmp    DMU_IntelAirSwit    呼叫    ${缺省值列表}    SSW数字量获取测试    null
    断开连接SNMP

