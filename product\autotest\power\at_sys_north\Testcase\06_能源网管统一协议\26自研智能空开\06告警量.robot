*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
批量获取智能空开告警量
    [Documentation]    智能空开下电告警无法产生
    写入CSV文档    智能空开数字量和告警量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=智能空开
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SSW排除告警量信号}    ${排除列表}    1    1    0    2
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    智能空开    alarm    ${SSW排除告警量信号}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    ${待测数据长度}    Get Length    ${power_sm待测}
    ${number}    Set Variable    1
    @{信号名称列表1}  create list
    FOR    ${i}    IN    @{power_sm待测}
        Log    正在验证第${number}个数据，一共${待测数据长度}个数据
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${实时告警中的设备名称}    主动告警_组合实时告警中的设备名称    智能空开    ${信号名称}
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
        Set To Dictionary    ${dict}     device_name     ${实时告警中的设备名称}
		Append To List       ${信号名称列表1}    ${dict}
        ${number}    Evaluate    ${number}+1
	END
    Run Keyword And Continue On Failure    南向RS485子设备告警量列表产生封装判断结果   power_sm    DMU_IntelAirSwit    ${信号名称列表1}    
    ...    呼叫    ${告警产生}    智能空开数字量和告警量获取测试    负载    智能空开    
    ...    null    null    null    null    null    null
    Run Keyword And Continue On Failure    南向RS485子设备告警量列表恢复封装判断结果    power_sm    DMU_IntelAirSwit    ${信号名称列表1}    
    ...    呼叫    ${告警恢复}    智能空开数字量和告警量获取测试    负载    智能空开    
    ...    null    null    null    null    null    null

智能空开通讯断
    连接CSU
    ${级别设置值}    获取web参数量    智能空开通讯断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    智能空开通讯断    严重
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    智能空开通讯断-1
    控制子工具运行停止    DMU_IntelAirSwit    关闭
    FOR    ${智能空开序号}    IN RANGE    1    41
        Wait Until Keyword Succeeds    10m    5    信号量数据值为    智能空开工作状态-${智能空开序号}    通讯断
        wait until keyword succeeds    10m    1    判断告警存在    智能空开通讯断-${智能空开序号}
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    智能空开通讯断-${智能空开序号}    ${SSH}
        should be true    ${power_sm告警存在}[0]
    END
    控制子工具运行停止    DMU_IntelAirSwit    启动
    FOR    ${智能空开序号}    IN RANGE    1    41
        Wait Until Keyword Succeeds    20m    5    信号量数据值为    智能空开工作状态-${智能空开序号}    正常
        wait until keyword succeeds    10m    1    判断告警不存在    智能空开通讯断-${智能空开序号}
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    智能空开通讯断-${智能空开序号}    ${SSH}
        should not be true    ${power_sm告警存在}[0]
    END
    [Teardown]
