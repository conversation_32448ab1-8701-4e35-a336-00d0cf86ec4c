*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
漏油记录保存测试(立方体）
    连接CSU
    Wait Until Keyword Succeeds    10    2    设置web参数量    油箱形状-1    立方体
    Wait Until Keyword Succeeds    10    2    设置web参数量    油箱长度-1    2000
    Wait Until Keyword Succeeds    10    2    设置web参数量    油箱宽度-1    1200
    ${可设置范围}    获取web参数可设置范围    油箱高度-1
    Wait Until Keyword Succeeds    10    2    设置web参数量    油箱高度-1    ${可设置范围}[1]
    设置子工具个数    llSensor    1
    comment     Wait Until Keyword Succeeds    10    2    设置web控制量    RS485总线设备统计
    comment     sleep    1m
    comment     wait until keyword succeeds    1m    1    判断告警不存在    液位传感器通讯中断-2
    Wait Until Keyword Succeeds    10m    1    信号量数据值为(强制获取)    液位传感器工作状态-1     正常
    ${开始数量}    获取web事件记录数量    油机漏油记录
    run keyword if    ${开始数量}>995    删除历史记录    油机漏油记录
    sleep    30
    ${开始数量}    获取web事件记录数量    油机漏油记录
    ${可设置范围}    获取web参数可设置范围    燃油泄漏告警阈值-1
    ${漏油阈值}    evaluate    ${可设置范围}[0]+90
    Wait Until Keyword Succeeds    10    1    设置web参数量    燃油泄漏告警阈值-1    ${漏油阈值}
    ${设置值}    获取web参数量    燃油泄漏告警阈值-1
    ${告警级别}    获取web参数量    油机燃油泄漏告警
    run keyword if    '${告警级别}' == '屏蔽'    设置web参数量    油机燃油泄漏告警    主要
    设置子工具值    llSensor    all    只读    液位    65535995
    sleep    40
    ${高度获取值}    获取web实时数据    液位传感器高度-1  
    should be true    ${高度获取值}==1000
    ${油量起始值}    获取web实时数据    油机剩余油量
    ${起始时间}    获取系统时间
    sleep    90m
    设置子工具值    llSensor    all    只读    液位    65535
    sleep    40
    ${新高度值}    获取web实时数据    液位传感器高度-1
    should be true    ${新高度值}==1
    ${油量结束值}    获取web实时数据    油机剩余油量
    ${漏油量}    evaluate    ${油量起始值}-${油量结束值}
    Comment    sleep    25m
    wait until keyword succeeds    50m    10    判断告警存在    油机燃油泄漏告警
    ${结束时间}    获取系统时间
    wait until keyword succeeds    50m    1    判断告警不存在    油机燃油泄漏告警
    sleep    10m
    ${结束数量}    获取web事件记录数量    油机漏油记录
    should be true    ${结束数量} == ${开始数量}+1
    @{记录内容}    获取web事件记录最新一条    油机漏油记录
    should be true    ${记录内容}[0] == ${结束数量}
    ${起始时间差}    subtract date from date    ${记录内容}[1]    ${起始时间}
    should be true    -5000<=${起始时间差}<=5000
    ${结束时间差}    subtract date from date    ${记录内容}[2]    ${结束时间}
    should be true    -1800<=${结束时间差}<=1800
    should be true    ${记录内容}[3] == ${油量起始值}
    should be true    ${记录内容}[4] == ${油量结束值}
    should be true    ${记录内容}[5] == ${漏油量}
    [Teardown]    设置web设备参数量为默认值    油箱高度-1    燃油泄漏告警阈值-1

漏油记录掉电保存功能测试
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    ${记录数量}    获取web事件记录数量    油机漏油记录
    run keyword if    ${记录数量}>990    删除历史记录    油机漏油记录
    测试台上下电操作    ${测试台编号}    OFF
    sleep    15
    测试台上下电操作    ${测试台编号}    ON
    实时告警刷新完成
    sleep    5
    ${记录数量1}    获取web事件记录数量    油机漏油记录
    should be true    ${记录数量}==${记录数量1}

漏油记录删除测试
    连接CSU
    ${开始数量}    获取web事件记录数量    油机漏油记录
    删除历史记录    油机漏油记录
    sleep    30
    ${结束数量}    获取web事件记录数量    油机漏油记录
    should be true    ${结束数量} == 0
