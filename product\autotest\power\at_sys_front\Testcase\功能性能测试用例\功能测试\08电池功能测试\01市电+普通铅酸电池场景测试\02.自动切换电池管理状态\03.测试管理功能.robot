*** Settings ***
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
测试时交流停电转停电
    [Documentation]    0:浮充/Float;1:均充/Equal;2:测试/Test;3:交流停电（以前叫放电）/Discharge;4:检测/Detect;5:过渡/Transition;6:充电/Charge
    
    电池管理初始化
    sleep    5
    Wait Until Keyword Succeeds    1m    2    设置web控制量    启动测试
    Wait Until Keyword Succeeds    20    1    信号量数据值为    电池管理状态    测试
    关闭交流源输出
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    系统停电
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    系统停电
    [Teardown]    电池管理参数恢复默认值

测试时无正常工作整流器转停电
    [Documentation]    ==20190321-f：启动测试成功，获取电池管理状态仍然为浮充（wait30s）    失败
    
    电池管理初始化
    Wait Until Keyword Succeeds    3m    2    信号量数据值为    交流供电状态    市电1
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    过渡
    设置web控制量    启动测试
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    测试
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    测试
    ${均充电压可设置范围}    获取web参数可设置范围    均充电压
    设置web参数量    均充电压    ${均充电压可设置范围}[0]
    ${输出高停机电压可设置范围}    获取web参数可设置范围    整流器输出高停机电压
    设置web参数量    整流器输出高停机电压    ${输出高停机电压可设置范围}[0]
    #使整流器输出过压保证无正常工作的整流器
    ${整流器输出高电压}    获取web参数量    整流器输出高停机电压
    向上调节电池电压    ${整流器输出高电压}+0.5
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    工作整流器数量    0
    ${状态}    获取web实时数据    电池管理状态
    #无整流器放电状态启动浮充，应F
    设置负载电压电流    53.5    10
    打开负载输出
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    系统停电
    #整流器过压恢复，5min应能恢复
    关闭负载输出
    向下调节电池电压    53.5
    ${在线整流器数量}    获取web实时数据    在线整流器数量
    Wait Until Keyword Succeeds    5m10s    1    信号量数据值为    工作整流器数量    ${在线整流器数量}
    [Teardown]    Run keywords    电池管理参数恢复默认值
    ...    AND    重置电池模拟器输出

测试时电池组无效转浮充
    
    电池管理初始化
    设置web控制量    启动测试
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    测试
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    测试
    sleep    5
    Wait Until Keyword Succeeds    30    1s    设置web参数量    电池组容量_1    0
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    浮充
    [Teardown]    设置web参数量    电池组容量_1    100

测试时间到转均充
    [Documentation]    0:浮充/Float;1:均充/Equal;2:测试/Test;3:交流停电（以前叫放电）/Discharge;4:检测/Detect;5:过渡/Transition;6:充电/Charge
    ...    ==20190323-f：测试最长时间到仍为2（增加启动测试后的电池状态判断，然后再去判断持续时间ok）
    
    电池管理初始化
    ${缺省值}    获取web参数上下限范围_有单位    测试最长时间
    ${可设置范围}    获取web参数可设置范围    测试最长时间
    ${测试最长时间设置值}    run keyword if    '${缺省值}[3]'== 'Hour'    evaluate    ${可设置范围}[0]+1
    ...    ELSE    evaluate    ${可设置范围}[0]+5
    ${测试最长时间转换后}    run keyword if    '${缺省值}[3]'== 'Hour'    evaluate    ${测试最长时间设置值}*60
    ...    ELSE    set variable    ${测试最长时间设置值}
    Comment    ${设置值}    evaluate    ${可设置范围}[0]+5
    设置web参数量    测试最长时间    ${测试最长时间设置值}
    设置web参数量    均充使能    允许
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动测试
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    测试
    Comment    sleep    5m15s
    ${等待时间}    evaluate    ${测试最长时间转换后}+2
    Wait Until Keyword Succeeds    ${等待时间}m    1    信号量数据值为    电池管理状态    均充
    [Teardown]    设置web参数量    测试最长时间    ${缺省值}[0]

测试终止电压到转均充
    [Documentation]   
    
    电池管理初始化
    #设置测试终止电压
    ${测试终止电压缺省值}    获取web参数上下限范围    测试终止电压
    ${测试终止电压可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${测试终止电压可设置范围}[1]
    #设置测试最大时间
    ${测试最长时间缺省值}    获取web参数上下限范围    测试最长时间
    ${测试最长时间可设置范围}    获取web参数可设置范围    测试最长时间
    设置web参数量    测试最长时间    ${测试最长时间可设置范围}[1]
    #设置测试终止容量
    ${测试终止容量缺省值}    获取web参数上下限范围_有单位    测试终止SOC阈值
    ${测试终止容量可设置范围}    获取web参数可设置范围    测试终止SOC阈值
    设置web参数量    测试终止SOC阈值    ${测试终止容量可设置范围}[0]
    ${测试失败容量阈值缺省值}    获取web参数上下限范围_有单位    测试失败SOC阈值
    ${测试失败容量阈值可设置范围}    获取web参数可设置范围    测试失败SOC阈值
    设置web参数量    测试失败SOC阈值    ${测试失败容量阈值可设置范围}[1]
    sleep    5
    #设置均充相关
    设置web参数量    均充使能    允许
    设置负载电压电流    53.5    30
    打开负载输出
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动测试
    Wait Until Keyword Succeeds    20    1    信号量数据值为    电池管理状态    测试
    ${测试终止SOC设置值}    获取web参数量    测试终止SOC阈值
    ${测试失败SOC设置值}    获取web参数量    测试失败SOC阈值
    ${测试终止容量转换}    run keyword if    '${测试终止容量缺省值}[3]'== 'C10'    evaluate    ${测试终止SOC设置值}*100
    ...    ELSE    evaluate    ${测试终止SOC设置值}
    ${测试失败SOC转换}    run keyword if    '${测试失败容量阈值缺省值}[3]'== 'C10'    evaluate    ${测试失败SOC设置值}*100
    ...    ELSE    evaluate    ${测试失败SOC设置值}
    ${测试终止电压}    获取web参数量    测试终止电压
    ${测试最长时间}    获取web参数量    测试最长时间
    向下调节电池电压    ${测试终止电压}
    ${电池组当前容量比率}    获取web实时数据    电池组当前容量比率-1
    ${电池电压}    获取web实时数据    电池电压-1
    ${电池测试持续时间}    获取web实时数据    电池状态持续时间
    should be true    ${测试终止容量转换}<${电池组当前容量比率}<=${测试失败SOC转换}
    should be true    ${电池测试持续时间}<${测试最长时间}
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    均充
    关闭负载输出
    向上调节电池电压    53.5
    [Teardown]    Run keywords    设置web设备参数量为默认值    测试终止电压    测试最长时间    测试终止SOC阈值    测试失败SOC阈值
    ...    AND    重置电池模拟器输出
    ...    AND    关闭负载输出

测试终止容量到转均充
    [Documentation]
    [Setup]    Run keywords    测试用例前置条件
    ...    AND    重置电池模拟器输出
    电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    20
    #设置测试终止电压
    ${测试终止电压缺省值}    获取web参数上下限范围    测试终止电压
    ${测试终止电压可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${测试终止电压可设置范围}[0]
    #设置测试最大时间
    ${测试最长时间缺省值}    获取web参数上下限范围    测试最长时间
    ${测试最长时间可设置范围}    获取web参数可设置范围    测试最长时间
    设置web参数量    测试最长时间    ${测试最长时间可设置范围}[1]
    #设置测试终止容量
    ${测试终止容量缺省值}    获取web参数上下限范围_有单位    测试终止SOC阈值
    ${测试终止容量可设置范围}    获取web参数可设置范围    测试终止SOC阈值
    设置web参数量    测试终止SOC阈值    ${测试终止容量可设置范围}[1]
    ${测试失败容量阈值缺省值}    获取web参数上下限范围_有单位    测试失败SOC阈值
    ${测试失败容量阈值可设置范围}    获取web参数可设置范围    测试失败SOC阈值
    设置web参数量    测试失败SOC阈值    ${测试失败容量阈值可设置范围}[1]
    sleep    5
    #设置均充相关
    设置web参数量    均充使能    允许
    缓慢设置负载电压电流    53.5    50
    打开负载输出
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动测试
    Wait Until Keyword Succeeds    20    1    信号量数据值为    电池管理状态    测试
    ${测试终止SOC设置值}    获取web参数量    测试终止SOC阈值
    ${测试失败SOC设置值}    获取web参数量    测试失败SOC阈值
    ${测试终止容量转换}    run keyword if    '${测试终止容量缺省值}[3]'== 'C10'    evaluate    ${测试终止SOC设置值}*100
    ...    ELSE    evaluate    ${测试终止SOC设置值}
    ${测试失败SOC转换}    run keyword if    '${测试失败容量阈值缺省值}[3]'== 'C10'    evaluate    ${测试失败SOC设置值}*100
    ...    ELSE    evaluate    ${测试失败SOC设置值}
    ${测试终止电压}    获取web参数量    测试终止电压
    ${测试最长时间}    获取web参数量    测试最长时间
    Wait Until Keyword Succeeds    20m    1    信号量数据值为    电池组当前容量比率-1    ${测试终止容量转换}
    ${电池组当前容量比率}    获取web实时数据    电池组当前容量比率-1
    ${电池电压}    获取web实时数据    电池电压-1
    ${电池测试持续时间}    获取web实时数据    电池状态持续时间
    should be true    ${电池电压}>${测试终止电压}
    should be true    ${电池测试持续时间}<${测试最长时间}
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    均充
    关闭负载输出
    [Teardown]    Run keywords    设置web设备参数量为默认值    测试终止电压    测试最长时间    测试终止SOC阈值    测试失败SOC阈值
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    100
    ...    AND    关闭负载输出

测试失败转浮充
    [Documentation]    ==20190322-F：测试失败进入均充，而没有进浮充
    [Setup]    Run keywords    测试用例前置条件
    ...    AND    重置电池模拟器输出
    电池管理初始化
    #获取告警级别
    ${级别设置值}    获取web参数量    电池测试失败
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池测试失败    严重
    #设置测试终止电压
    ${测试终止电压缺省值}    获取web参数上下限范围    测试终止电压
    ${测试终止电压可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${测试终止电压可设置范围}[1]
    #设置测试最大时间
    ${测试最长时间缺省值}    获取web参数上下限范围    测试最长时间
    ${测试最长时间可设置范围}    获取web参数可设置范围    测试最长时间
    设置web参数量    测试最长时间    ${测试最长时间可设置范围}[1]
    #设置测试终止容量
    ${测试终止容量缺省值}    获取web参数上下限范围_有单位    测试终止SOC阈值
    ${测试终止容量可设置范围}    获取web参数可设置范围    测试终止SOC阈值
    设置web参数量    测试终止SOC阈值    ${测试终止容量可设置范围}[0]
    ${测试失败容量阈值缺省值}    获取web参数上下限范围_有单位    测试失败SOC阈值
    ${测试失败容量阈值可设置范围}    获取web参数可设置范围    测试失败SOC阈值
    设置web参数量    测试失败SOC阈值    ${测试失败容量阈值可设置范围}[0]
    sleep    5
    #设置均充相关
    设置web参数量    均充使能    允许
    缓慢设置负载电压电流    53.5    30
    打开负载输出
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动测试
    Wait Until Keyword Succeeds    20    1    信号量数据值为    电池管理状态    测试
    ${测试终止SOC设置值}    获取web参数量    测试终止SOC阈值
    ${测试失败SOC设置值}    获取web参数量    测试失败SOC阈值
    ${测试终止容量转换}    run keyword if    '${测试终止容量缺省值}[3]'== 'C10'    evaluate    ${测试终止SOC设置值}*100
    ...    ELSE    evaluate    ${测试终止SOC设置值}
    ${测试失败SOC转换}    run keyword if    '${测试失败容量阈值缺省值}[3]'== 'C10'    evaluate    ${测试失败SOC设置值}*100
    ...    ELSE    evaluate    ${测试失败SOC设置值}
    ${测试终止电压}    获取web参数量    测试终止电压
    ${测试最长时间}    获取web参数量    测试最长时间
    向下调节电池电压    ${测试终止电压}
    ${电池组当前容量比率}    获取web实时数据    电池组当前容量比率-1
    ${电池电压}    获取web实时数据    电池电压-1
    ${电池测试持续时间}    获取web实时数据    电池状态持续时间
    should be true    ${电池组当前容量比率}>${测试失败SOC转换}
    should be true    ${电池测试持续时间}<${测试最长时间}
    wait until keyword succeeds    1m    1    判断告警存在    电池测试失败
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    均充
    关闭负载输出
    向上调节电池电压    53.5
    设置web参数量    测试失败SOC阈值    ${测试失败容量阈值缺省值}[0]
    设置web参数量    测试终止SOC阈值    ${测试终止容量缺省值}[0]
    设置web参数量    测试终止电压    ${测试终止电压缺省值}[0]
    ${测试最长时间设置}    evaluate    ${测试最长时间可设置范围}[0] + 5
    设置web参数量    测试最长时间    ${测试最长时间设置}
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动测试
    Wait Until Keyword Succeeds    20    1    信号量数据值为    电池管理状态    测试
    wait until keyword succeeds    10m    1    判断告警不存在    电池测试失败
    [Teardown]    Run keywords    设置web设备参数量为默认值    测试终止电压    测试最长时间    测试终止SOC阈值    测试失败SOC阈值
    ...    AND    关闭负载输出

电池下电退出测试转浮充
    [Documentation]    0:浮充/Float;1:均充/Equal;2:测试/Test;3:交流停电（以前叫放电）/Discharge;4:检测/Detect;5:过渡/Transition;6:充电/Charge
    ...    ==20190323-f：测试最长时间到仍为2（增加启动测试后的电池状态判断，然后再去判断持续时间ok）
    
    电池管理初始化
    ${缺省值}    获取web参数上下限范围_有单位    测试最长时间
    ${可设置范围}    获取web参数可设置范围    测试最长时间
    ${测试最长时间设置值}    run keyword if    '${缺省值}[3]'== 'Hour'    evaluate    ${可设置范围}[0]+1
    ...    ELSE    evaluate    ${可设置范围}[0]+10
    ${测试最长时间转换后}    run keyword if    '${缺省值}[3]'== 'Hour'    evaluate    ${测试最长时间设置值}*60
    ...    ELSE    set variable    ${测试最长时间设置值}
    Comment    ${设置值}    evaluate    ${可设置范围}[0]+5
    设置web参数量    测试最长时间    ${测试最长时间设置值}
    设置web参数量    均充使能    允许
    wait until keyword succeeds    10    1    设置web参数量    电池高温下电使能    允许
    sleep    10
    ${可设范围}    获取web参数可设置范围    电池高温下电温度
    设置web参数量    电池高温下电温度    ${可设范围}[0]
    run keyword and ignore error    设置web参数量    下电控制延时    0
    显示属性配置    电池高温下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    3
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动测试
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    测试
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    10    2    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    设置负载电压电流    53.5    10
    打开负载输出
    wait until keyword succeeds    5m    1    判断告警存在    电池高温下电
    Wait Until Keyword Succeeds    3m    2    信号量数据值为    电池高温下电控制状态    动作
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    #恢复
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    -10    1.6    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    电池高温下电控制状态    恢复
    wait until keyword succeeds    4m    2    判断告警不存在    电池高温下电
    显示属性配置    电池高温下电控制状态    数字量    web_attr=Off    gui_attr=Off
    关闭负载输出
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池高温下电温度    电池高温下电使能    测试最长时间
    ...    AND    关闭负载输出

电池测试触发电池检测
    [Documentation]    0:浮充/Float;1:均充/Equal;2:测试/Test;3:交流停电（以前叫放电）/Discharge;4:检测/Detect;5:过渡/Transition;6:充电/Charge
    ...    ==20190323-f：测试最长时间到仍为2（增加启动测试后的电池状态判断，然后再去判断持续时间ok）
    
    电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池周期均充使能    允许
    设置web参数量    电池均充周期    0    #默认90
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池周期测试使能    允许
    设置web参数量    电池测试周期    0    #默认0天
    设置web参数量    电池检测周期    30    #默认0
    设置web参数量    电池检测持续时间    2
    ${测试最长时间范围}    获取web参数上下限范围_有单位    测试最长时间
    ${测试最长时间}    run keyword if    '${测试最长时间范围}[3]'== 'Hour'    evaluate    ${测试最长时间范围}[1]+1
    ...    ELSE    evaluate    ${测试最长时间范围}[1]+5
    设置web参数量    测试最长时间    ${测试最长时间}
    ${电池检测周期}    获取web 参数量    电池检测周期
    sleep    5
    ${初始下次检测时间}    获取web实时数据    下次检测时间
    ${当前时间}    获取系统时间
    ${调整时间}    add time to date    ${当前时间}    10d    exclude_millis=yes
    sleep    5
    设置系统时间    ${调整时间}
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动测试
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    测试
    sleep    30
    ${下次检测时间_cal}    add time to date    ${初始下次检测时间}    10d    exclude_millis=yes
    ${下次检测时间_new}    获取web实时数据    下次检测时间
    should be equal    ${下次检测时间_new}    ${下次检测时间_cal}
    [Teardown]    Run keywords    设置web设备参数量为默认值    测试最长时间    电池检测周期    电池检测持续时间
    ...    AND    同步系统时间
