*** Settings ***
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
01 系统过载告警参数测试
    [Documentation]    0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning
    ...
    ...
    ...    -v BOARD_IP:********** -v SSHKEY:pmsa_ssh -v LANGUAGE:zh_CN.UTF-8
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    系统过载告警
    [Template]
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    系统过载告警    ${VAR}
        sleep    2
        ${系统过载告警告警级别获取}    获取web参数量    系统过载告警
        should be equal    ${系统过载告警告警级别获取}    ${VAR}
    END
    设置web参数量    系统过载告警    严重
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    5X    2    设置web参数量    系统过载告警干接点    ${VAR}
        ${系统过载告警告警干接点获取}    获取web参数量    系统过载告警干接点
        should be true    ${系统过载告警告警干接点获取}==${VAR}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    系统过载告警干接点    0
    ...    AND    设置web参数量    系统过载告警    严重

02 模块槽位异常告警参数测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    模块槽位异常告警
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    模块槽位异常告警    ${VAR}
        sleep    2
        ${模块槽位异常告警告警级别获取}    获取web参数量    模块槽位异常告警
        should be equal    ${模块槽位异常告警告警级别获取}    ${VAR}
    END
    设置web参数量    模块槽位异常告警    次要
    sleep    5
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        设置web参数量    模块槽位异常告警干接点    ${VAR}
        ${模块槽位异常告警告警干接点获取}    获取web参数量    模块槽位异常告警干接点
        should be true    ${模块槽位异常告警告警干接点获取}==${VAR}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    模块槽位异常告警干接点    0
    ...    AND    设置web参数量    模块槽位异常告警    次要

03 禁止所有告警参数测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    <<禁止所有告警~0x1001030010001>>
    FOR    ${VAR}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    10    2    设置web参数量    <<禁止所有告警~0x1001030010001>>    ${VAR}
        sleep    2
        ${禁止所有告警告警级别获取}    获取web参数量    <<禁止所有告警~0x1001030010001>>
        should be equal    ${禁止所有告警告警级别获取}    ${VAR}
    END
    设置web参数量    <<禁止所有告警~0x1001030010001>>    严重
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    5X    2    设置web参数量    <<禁止所有告警干接点~0x1001030010001>>    ${VAR}
        ${禁止所有告警告警干接点获取}    获取web参数量    <<禁止所有告警干接点~0x1001030010001>>
        should be true    ${禁止所有告警告警干接点获取}==${VAR}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    <<禁止所有告警干接点~0x1001030010001>>    0
    ...    AND    设置web参数量    <<禁止所有告警~0x1001030010001>>    严重

04 MAC地址未设置参数测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    MAC地址未设置
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    MAC地址未设置    ${VAR}
        sleep    2
        ${禁止所有告警告警级别获取}    获取web参数量    MAC地址未设置
        should be equal    ${禁止所有告警告警级别获取}    ${VAR}
    END
    设置web参数量    MAC地址未设置    主要
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    5X    2    设置web参数量    MAC地址未设置干接点    ${VAR}
        ${禁止所有告警告警干接点获取}    获取web参数量    MAC地址未设置干接点
        should be true    ${禁止所有告警告警干接点获取}==${VAR}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    MAC地址未设置干接点    0
    ...    AND    设置web参数量    MAC地址未设置    主要

05 输入干接点告警参数测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    输入干接点告警
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    输入干接点告警_1    ${VAR}
        sleep    2
        ${告警级别获取}    获取web参数量    输入干接点告警_1
        should be equal    ${告警级别获取}    ${VAR}
    END
    设置web参数量    输入干接点告警_1    次要
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        设置web参数量    输入干接点告警_1干接点    ${VAR}
        ${告警级别获取}    获取web参数量    输入干接点告警_1干接点
        should be true    ${告警级别获取}==${VAR}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    输入干接点告警_1干接点    0
    ...    AND    设置web参数量    输入干接点告警_1    次要

06 CPU利用率高告警参数测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    CPU利用率高告警
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    CPU利用率高告警    ${VAR}
        sleep    2
        ${CPU占用率高告警告警级别获取}    获取web参数量    CPU利用率高告警
        should be equal    ${CPU占用率高告警告警级别获取}    ${VAR}
    END
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    CPU利用率高告警干接点    ${VAR}
        ${CPU占用率高告警告警干接点获取}    获取web参数量    CPU利用率高告警干接点
        should be true    ${CPU占用率高告警告警干接点获取}==${VAR}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    CPU利用率高告警干接点    0
    ...    AND    设置web参数量    CPU利用率高告警    屏蔽

07 内存利用率高告警参数测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    内存利用率高告警
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    内存利用率高告警    ${VAR}
        sleep    2
        ${内存占用率过高告警告警级别获取}    获取web参数量    内存利用率高告警
        should be equal    ${内存占用率过高告警告警级别获取}    ${VAR}
    END
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    内存利用率高告警干接点    ${VAR}
        ${内存占用率过高告警告警干接点获取}    获取web参数量    内存利用率高告警干接点
        should be true    ${内存占用率过高告警告警干接点获取}==${VAR}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    内存利用率高告警干接点    0
    ...    AND    设置web参数量    内存利用率高告警    屏蔽

08 交流输入场景配置错误告警
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    交流输入场景配置错误告警
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    交流输入场景配置错误告警    ${VAR}
        sleep    2
        ${交流输入场景配置错误告警级别获取}    获取web参数量    交流输入场景配置错误告警
        should be equal    ${交流输入场景配置错误告警级别获取}    ${VAR}
    END
    设置web参数量    交流输入场景配置错误告警    次要
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        设置web参数量    交流输入场景配置错误告警干接点    ${VAR}
        ${交流输入场景配置错误告警干接点获取}    获取web参数量    交流输入场景配置错误告警干接点
        should be true    ${交流输入场景配置错误告警干接点获取}==${VAR}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    交流输入场景配置错误告警干接点    0
    ...    AND    设置web参数量    交流输入场景配置错误告警    次要

09 UIB通讯断告警测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    UIB通讯断
    FOR    ${VAR}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    10    2    设置web参数量    UIB通讯断    ${VAR}
        sleep    2
        ${告警级别获取}    获取web参数量    UIB通讯断
        should be equal    ${告警级别获取}    ${VAR}
    END
    设置web参数量    UIB通讯断    严重
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    5X    2    设置web参数量    UIB通讯断干接点    ${VAR}
        ${告警干接点获取}    获取web参数量    UIB通讯断干接点
        should be true    ${告警干接点获取}==${VAR}
    END
    [Teardown]    run keywords    设置web参数量    UIB通讯断干接点    0
    ...    AND    设置web参数量    UIB通讯断    严重

10 IDDB通讯断告警测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    IDDB通讯断
    FOR    ${VAR}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    10    2    设置web参数量    IDDB通讯断    ${VAR}
        sleep    2
        ${告警级别获取}    获取web参数量    IDDB通讯断
        should be equal    ${告警级别获取}    ${VAR}
    END
    设置web参数量    IDDB通讯断    严重
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    5X    2    设置web参数量    IDDB通讯断干接点    ${VAR}
        ${告警干接点获取}    获取web参数量    IDDB通讯断干接点
        should be true    ${告警干接点获取}==${VAR}
    END
    [Teardown]    run keywords    设置web参数量    IDDB通讯断干接点    0
    ...    AND    设置web参数量    IDDB通讯断    严重
