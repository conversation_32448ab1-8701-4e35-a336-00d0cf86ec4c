*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
系统过载告警阈值测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    系统过载告警阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    系统过载告警阈值
    ${缺省值}    获取web参数上下限范围    系统过载告警阈值
    ${可设置范围}    获取web参数可设置范围    系统过载告警阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    系统过载告警阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    系统过载告警阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    系统过载告警阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    系统过载告警阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    系统过载告警阈值

交流输入场景测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    交流输入场景
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    交流输入场景
    ${取值约定dict}    获取web参数的取值约定    交流输入场景
    ${取值约定values}    get dictionary values    ${取值约定dict}
    FOR    ${参数设置}    IN    @{取值约定values}
        设置web参数量    交流输入场景    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    交流输入场景
        should be equal    ${参数获取}    ${参数设置}
    END
    [Teardown]    设置web参数量    交流输入场景    市电

站点名称测试
    [Tags]    view    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    站点名称
    Wait Until Keyword Succeeds    10    2    设置web参数量    站点名称    Site-X#8
    sleep    2
    ${站点名称获取}    获取web参数量    站点名称
    should be true    '${站点名称获取}'=='Site-X#8'
    [Teardown]    设置web设备参数量为默认值    站点名称

环境监测使能参数测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    环境监测使能
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    环境监测使能
    ${取值约定dict}    获取web参数的取值约定    环境监测使能
    ${取值约定values}    get dictionary values    ${取值约定dict}
    FOR    ${参数设置}    IN    @{取值约定values}
        设置web参数量    环境监测使能    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    环境监测使能
        should be equal    ${参数获取}    ${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    环境监测使能

CPU利用率高阈值测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    CPU利用率高阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    CPU利用率高阈值
    ${缺省值}    获取web参数上下限范围    CPU利用率高阈值
    ${可设置范围}    获取web参数可设置范围    CPU利用率高阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    CPU利用率高阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    CPU利用率高阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    CPU利用率高阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    CPU利用率高阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    CPU利用率高阈值

内存利用率高阈值测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    内存利用率高阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    内存利用率高阈值
    ${缺省值}    获取web参数上下限范围    内存利用率高阈值
    ${可设置范围}    获取web参数可设置范围    内存利用率高阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    内存利用率高阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    内存利用率高阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    内存利用率高阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    内存利用率高阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    内存利用率高阈值

CSU主动告警使能测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    CSU主动告警使能
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    CSU主动告警使能
    ${取值约定dict}    获取web参数的取值约定    CSU主动告警使能
    ${取值约定values}    get dictionary values    ${取值约定dict}
    FOR    ${参数设置}    IN    @{取值约定values}
        设置web参数量    CSU主动告警使能    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    CSU主动告警使能
        should be equal    ${参数获取}    ${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    CSU主动告警使能

直流备用输入配置测试
    [Tags]    no test
    [Setup]    判断web参数是否存在    直流备用输入配置
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    直流备用输入配置
    ${取值约定dict}    获取web参数的取值约定    直流备用输入配置
    ${取值约定values}    get dictionary values    ${取值约定dict}
    FOR    ${参数设置}    IN    @{取值约定values}
        设置web参数量    直流备用输入配置    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    直流备用输入配置
        should be equal    ${参数获取}    ${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    直流备用输入配置

电池配置测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    电池配置
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    电池配置
    ${取值约定dict}    获取web参数的取值约定    电池配置
    ${取值约定values}    get dictionary values    ${取值约定dict}
    FOR    ${参数设置}    IN    @{取值约定values}
        设置web参数量    电池配置    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    电池配置
        should be equal    ${参数获取}    ${参数设置}
    END
    [Teardown]    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    纯铅酸

铅酸类型测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    铅酸类型
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    铅酸类型
    ${取值约定dict}    获取web参数的取值约定    铅酸类型
    ${取值约定values}    get dictionary values    ${取值约定dict}
    FOR    ${参数设置}    IN    @{取值约定values}
        设置web参数量    铅酸类型    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    铅酸类型
        should be equal    ${参数获取}    ${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    铅酸类型

经度测试
    [Documentation]    经度范围：-180.000~180.000
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    经度
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    经度
    ${缺省值}    获取web参数上下限范围    经度
    ${可设置范围}    获取web参数可设置范围    经度
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    经度    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    经度
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    经度    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    经度
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    经度

纬度测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    纬度
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    纬度
    ${缺省值}    获取web参数上下限范围    纬度
    ${可设置范围}    获取web参数可设置范围    纬度
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    纬度    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    纬度
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    纬度    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    纬度
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    纬度

海拔高度测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    海拔高度
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    海拔高度
    ${缺省值}    获取web参数上下限范围    海拔高度
    ${可设置范围}    获取web参数可设置范围    海拔高度
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    海拔高度    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    海拔高度
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    海拔高度    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    海拔高度
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    海拔高度

历史数据保存间隔测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    历史数据保存间隔
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    历史数据保存间隔
    ${缺省值}    获取web参数上下限范围    历史数据保存间隔
    ${可设置范围}    获取web参数可设置范围    历史数据保存间隔
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    历史数据保存间隔    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    历史数据保存间隔
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    历史数据保存间隔    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    历史数据保存间隔
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    历史数据保存间隔
