*** Settings ***
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp_test关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/Web关键字_V30.robot
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
snmp_0002_液位传感器在位状态√
    [Documentation]    0:不在位/Not Exist;1:在位/Is Exist
    连接CSU
    显示属性配置    液位传感器在位状态    数字量    web_attr=On    gui_attr=On
    设置子工具个数    llSensor    2
    sleep    40
    ${名称1}    获取web实时数据    液位传感器在位状态-1
    Comment    ${名称2}    获取web实时数据    液位传感器在位状态-2
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${snmp获取值1}    获取SNMP数据_单个    lLSExistState1value
    Comment    ${snmp获取值2}    获取SNMP数据_单个    lLSExistState2value
    should be equal    '${名称1}'    '在位'
    Comment    should be equal    '${名称2}'    '在位'
    should be true    ${snmp获取值1}    1
    Comment    should be true    ${snmp获取值2}    1
    设置子工具个数    llSensor    1
    sleep    40
    ${名称1}    获取web实时数据    液位传感器在位状态-1
    Comment    ${名称2}    获取web实时数据    液位传感器在位状态-2
    ${snmp获取值1}    获取SNMP数据_单个    lLSExistState1value
    Comment    ${snmp获取值2}    获取SNMP数据_单个    lLSExistState2value
    should be equal    '${名称1}'    '在位'
    Comment    should be equal    '${名称2}'    '在位'
    should be true    ${snmp获取值1}    1
    Comment    should be true    ${snmp获取值2}    1
    控制子工具运行停止    llSensor    关闭
    sleep    40
    ${名称1}    获取web实时数据    液位传感器在位状态-1
    Comment    ${名称2}    获取web实时数据    液位传感器在位状态-2
    ${snmp获取值1}    获取SNMP数据_单个    lLSExistState1value
    Comment    ${snmp获取值2}    获取SNMP数据_单个    lLSExistState2value
    should be equal    '${名称1}'    '在位'
    Comment    should be equal    '${名称2}'    '在位'
    should be true    ${snmp获取值1}    1
    Comment    should be true    ${snmp获取值2}    1
    断开连接SNMP
    显示属性配置    液位传感器通讯状态    数字量    web_attr=Off    gui_attr=Off
    控制子工具运行停止    llSensor    开启
    sleep    1m
    [Teardown]    控制子工具运行停止    llSensor    开启

snmp_0004_液位传感器工作状态√
    [Documentation]    0:无/Null;1:正常/Normal;2:告警/Alarm;3:通讯断/CommFail
    连接CSU
    显示属性配置    液位传感器工作状态    数字量    web_attr=On    gui_attr=On
    设置子工具个数    llSensor    2
    sleep    40
    ${名称1}    获取web实时数据    液位传感器工作状态-1
    Comment    ${名称2}    获取web实时数据    液位传感器工作状态-2
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${snmp获取值1}    获取SNMP数据_单个    lLSWorkState1value
    Comment    ${snmp获取值2}    获取SNMP数据_单个    lLSWorkState2value
    should be equal    '${名称1}'    '正常'
    Comment    should be equal    '${名称2}'    '正常'
    should be true    ${snmp获取值1}    1
    Comment    should be true    ${snmp获取值2}    1
    设置子工具个数    llSensor    1
    sleep    40
    ${名称1}    获取web实时数据    液位传感器工作状态-1
    Comment    ${名称2}    获取web实时数据    液位传感器工作状态-2
    ${snmp获取值1}    获取SNMP数据_单个    lLSWorkState1value
    Comment    ${snmp获取值2}    获取SNMP数据_单个    lLSWorkState2value
    should be equal    '${名称1}'    '正常'
    Comment    should be equal    '${名称2}'    '通讯断'
    should be true    ${snmp获取值1}    1
    Comment    should be true    ${snmp获取值2}    3
    控制子工具运行停止    llSensor    关闭
    sleep    40
    ${名称1}    获取web实时数据    液位传感器工作状态-1
    Comment    ${名称2}    获取web实时数据    液位传感器工作状态-2
    ${snmp获取值1}    获取SNMP数据_单个    lLSWorkState1value
    Comment    ${snmp获取值2}    获取SNMP数据_单个    lLSWorkState2value
    should be equal    '${名称1}'    '通讯断'
    Comment    should be equal    '${名称2}'    '通讯断'
    should be true    ${snmp获取值1}    3
    Comment    should be true    ${snmp获取值2}    3
    断开连接SNMP
    显示属性配置    液位传感器通讯状态    数字量    web_attr=Off    gui_attr=Off
    控制子工具运行停止    llSensor    开启
    sleep    1m
    [Teardown]    控制子工具运行停止    llSensor    开启

snmp_0006_液位传感器通讯状态√
    [Documentation]    0:正常/Normal;1:异常/Abnormal;
    连接CSU
    显示属性配置    液位传感器通讯状态    数字量    web_attr=On    gui_attr=On
    设置子工具个数    llSensor    2
    sleep    40
    ${名称1}    获取web实时数据    液位传感器通讯状态-1
    Comment    ${名称2}    获取web实时数据    液位传感器通讯状态-2
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${snmp获取值1}    获取SNMP数据_单个    lLSCommunicationState1value
    Comment    ${snmp获取值2}    获取SNMP数据_单个    lLSCommunicationState2value
    should be equal    '${名称1}'    '正常'
    Comment    should be equal    '${名称2}'    '正常'
    should be equal    ${snmp获取值1}    0
    Comment    should be equal    ${snmp获取值2}    0
    设置子工具个数    llSensor    1
    sleep    40
    ${名称1}    获取web实时数据    液位传感器通讯状态-1
    Comment    ${名称2}    获取web实时数据    液位传感器通讯状态-2
    ${snmp获取值1}    获取SNMP数据_单个    lLSCommunicationState1value
    Comment    ${snmp获取值2}    获取SNMP数据_单个    lLSCommunicationState2value
    should be equal    '${名称1}'    '正常'
    Comment    should be equal    '${名称2}'    '异常'
    should be equal    ${snmp获取值1}    0
    Comment    should be equal    ${snmp获取值2}    1
    控制子工具运行停止    llSensor    关闭
    sleep    40
    ${名称1}    获取web实时数据    液位传感器通讯状态-1
    Comment    ${名称2}    获取web实时数据    液位传感器通讯状态-2
    ${snmp获取值1}    获取SNMP数据_单个    lLSCommunicationState1value
    Comment    ${snmp获取值2}    获取SNMP数据_单个    lLSCommunicationState2value
    should be equal    '${名称1}'    '异常'
    Comment    should be equal    '${名称2}'    '异常'
    should be equal    ${snmp获取值1}    1
    Comment    should be equal    ${snmp获取值2}    1
    断开连接SNMP
    显示属性配置    液位传感器通讯状态    数字量    web_attr=Off    gui_attr=Off
    控制子工具运行停止    llSensor    开启
    sleep    1m
    [Teardown]    控制子工具运行停止    llSensor    开启
