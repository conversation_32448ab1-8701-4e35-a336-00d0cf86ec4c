*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
设置PU序列号
    连接CSU
    FOR    ${地址}    IN RANGE    1    49
        ${序列号}    evaluate     2147483647+${地址}
        ${序列号}    evaluate     str(${序列号})
        ${地址}    evaluate     str(${地址})
        设置子工具值    pu    ${地址}    厂家信息    PU序列号    ${序列号}
        设置子工具值    pu    ${地址}    版本    PU序列号    ${序列号}
    END

批量获取PU模拟量测试
    [Tags]    PMSA-NTest
    写入CSV文档    PU模拟量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=太阳能模块
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除模拟量信号}    ${排除列表}    1    ${模拟PU起始地址}
    ${缺省值列表}   获取缺省值列表  ${列表1}    1
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    pu    模拟量    ${缺省值列表}    2    PU模拟量获取测试
    ${缺省值列表}   获取缺省值列表  ${列表1}    2
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    pu    模拟量    ${缺省值列表}    2    PU模拟量获取测试
    ${缺省值列表}   获取缺省值列表  ${列表1}    0
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    pu    模拟量    ${缺省值列表}    2    PU模拟量获取测试
