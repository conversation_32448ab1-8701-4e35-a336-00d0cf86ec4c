*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
批量获取智能空开设备信息
    写入CSV文档    智能空开设备信息获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string32    INDEX_DEVICE_CH=智能空开
    ${排除列表}    create list
    @{信号名称列表1}    create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SSW排除系统名称}    ${排除列表}    1
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    智能空开    device info    ${SSW排除系统名称}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    FOR    ${i}    IN    @{power_sm待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
		Append To List       ${信号名称列表1}    ${dict}
    END
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    DMU_IntelAirSwit    ${信号名称列表1}    建链    V99.23    智能空开设备信息获取测试    字符    获取智能空开设备信息    
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    DMU_IntelAirSwit    ${信号名称列表1}    建链    V10.10    智能空开设备信息获取测试    字符    获取智能空开设备信息    
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    DMU_IntelAirSwit    ${信号名称列表1}    建链    V12345    智能空开设备信息获取测试    字符    获取智能空开设备信息    
    
     
    


    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string32    INDEX_DEVICE_CH=智能空开
    ${排除列表}    create list
    @{信号名称列表2}    create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SSW排除软件版本}    ${排除列表}    1
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    智能空开    device info    ${SSW排除软件版本}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    FOR    ${i}    IN    @{power_sm待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
		Append To List       ${信号名称列表2}    ${dict}
    END
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    DMU_IntelAirSwit    ${信号名称列表2}    建链    VZXDU48 FB100B3    智能空开设备信息获取测试    字符    获取智能空开设备信息    
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    DMU_IntelAirSwit    ${信号名称列表2}    建链    ZTE-smartli        智能空开设备信息获取测试    字符    获取智能空开设备信息    
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    DMU_IntelAirSwit    ${信号名称列表2}    建链    zte                智能空开设备信息获取测试    字符    获取智能空开设备信息   
    
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=float    INDEX_DEVICE_CH=智能空开
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    1
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    智能空开    device info    null
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    1    power_sm
    Run Keyword And Continue On Failure    南向子设备厂家信息列表封装判断结果    power_sm    DMU_IntelAirSwit    ${缺省值列表}    建链     智能空开设备信息获取测试    数值    获取智能空开设备信息   
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    2    power_sm
    Run Keyword And Continue On Failure    南向子设备厂家信息列表封装判断结果    power_sm    DMU_IntelAirSwit    ${缺省值列表}    建链     智能空开设备信息获取测试    数值    获取智能空开设备信息    
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    0    power_sm
    Run Keyword And Continue On Failure    南向子设备厂家信息列表封装判断结果    power_sm    DMU_IntelAirSwit    ${缺省值列表}    建链      智能空开设备信息获取测试    数值    获取智能空开设备信息    

    
    #模拟工具无智能空开类型
    Comment    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=int    INDEX_DEVICE_CH=智能空开
    Comment    ${排除列表}    create list
    Comment    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    1
    Comment    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    智能空开    device info    null
    Comment    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    Comment    FOR    ${i}    IN    @{power_sm待测}
    Comment    \    ${信号名称}    Get From Dictionary    ${i}    signal_name
    Comment    \    ${缺省值}    获取web参数上下限范围    ${信号名称}
    Comment    \    Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    power_sm    DMU_IntelAirSwit    ${信号名称}    建链    ${缺省值}[1]    智能空开设备信息获取测试    数值    获取智能空开设备信息    
    Comment    \    Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    power_sm    DMU_IntelAirSwit    ${信号名称}    建链    ${缺省值}[2]    智能空开设备信息获取测试    数值    获取智能空开设备信息    
    Comment    \    Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    power_sm    DMU_IntelAirSwit    ${信号名称}    建链    ${缺省值}[0]    智能空开设备信息获取测试    数值    获取智能空开设备信息    
    Comment    END
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=date    INDEX_DEVICE_CH=智能空开
    ${排除列表}    create list
    @{信号名称列表3}    create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    1
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    智能空开    device info    null
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    FOR    ${i}    IN    @{power_sm待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
		Append To List       ${信号名称列表3}    ${dict}
    END
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    DMU_IntelAirSwit    ${信号名称列表3}    建链    2018-11-15    智能空开设备信息获取测试    字符    获取智能空开设备信息    
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    DMU_IntelAirSwit    ${信号名称列表3}    建链    2021-08-23    智能空开设备信息获取测试    字符    获取智能空开设备信息    
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    DMU_IntelAirSwit    ${信号名称列表3}    建链    2018-10-31    智能空开设备信息获取测试    字符    获取智能空开设备信息   
    


   