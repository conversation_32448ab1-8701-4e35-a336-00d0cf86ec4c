*** Settings ***
Resource          ../../../测试用例关键字.robot
Suite Setup    测试用例前置条件
Suite Teardown    测试用例后置条件

*** Test Cases ***
power_sm_0001_获取油机所有模拟量
    [Tags]    3
    ${能源网管数据}    能源网管协议_获取数据    获取油机模拟量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取油机模拟量
    Should Be True    ${数据对比结果}
    ${校验结果}    能源网管协议_校验数据上送完整性    ${能源网管数据}    油机    analog data
    Comment     Should Be True    ${校验结果}

power_sm_0002_获取油机所有数字量
    [Tags]    3
    ${能源网管数据}    能源网管协议_获取数据    获取油机数字量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取油机数字量
    Should Be True    ${数据对比结果}
    ${校验结果}    能源网管协议_校验数据上送完整性    ${能源网管数据}    油机    digital data
    Comment   Should Be True    ${校验结果}

power_sm_0003_获取油机所有参数量
    ${能源网管数据}    能源网管协议_获取数据    获取油机参数量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取油机参数量
    Should Be True    ${数据对比结果}
    ${校验结果}    能源网管协议_校验数据上送完整性    ${能源网管数据}    油机    parameter
    Comment    Should Be True    ${校验结果}

power_sm_0004_设置油机参数量
    ${sheet_name}    Set Variable    设置油机参数
    ${能源网管数据}    能源网管协议_设置单个参数    ${sheet_name}    ${BOARD_IP}    ${SSH}
    ${对比结果}    能源网管协议_批量对比参数设置    ${能源网管数据}    设置油机参数
    Should Be True    ${对比结果}

power_sm_0005_获取油机统计量
    ${能源网管数据}    能源网管协议_获取数据    获取油机统计量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取油机统计量
    Should Be True    ${数据对比结果}
    ${校验结果}    能源网管协议_校验数据上送完整性    ${能源网管数据}    油机    stastic data
    Comment   Should Be True    ${校验结果}

power_sm_0006_设置油机控制量
    sleep    3m
    ${能源网管数据}    能源网管协议_设置控制量    设置油机控制量    ${BOARD_IP}    ${SSH}
    ${对比结果}    能源网管协议_批量对比参数设置    ${能源网管数据}    设置油机控制量
    Should Be True    ${对比结果}

power_sm_0007_油机开启
    [Documentation]    0:未配置/Not Config;1:停止/OFF;2:运行/ON;3:异常/Abnormal;4:过渡/Transition
    [Tags]    T1-1
    Comment    ${能源网管数据}    能源网管协议_设置控制量    设置油机控制量    ${BOARD_IP}    ${SSH}
    Comment    ${对比结果}    能源网管协议_批量对比参数设置    ${能源网管数据}    设置油机控制量
    Comment    Should Be True    ${对比结果}
    油机管理初始化
    能源网管协议_设置单个设备单个控制量    1    油机开启    ${BOARD_IP}    ${SSH}
    ${能源网管数据}    能源网管协议_设置控制量    设置油机控制量    ${BOARD_IP}    ${SSH}
    ${对比结果}    能源网管协议_批量对比参数设置    ${能源网管数据}    设置油机控制量
    Should Be True    ${对比结果}
    同时设置三相电压频率    220    50
    打开交流源输出
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    过渡
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取油机数字量    油机状态-1    ${SSH}
    should be equal as numbers    ${power_sm获取值1}    4
    Wait Until Keyword Succeeds    10m    1    信号量数据值为    油机状态    运行
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取油机数字量    油机状态-1    ${SSH}
    should be equal as numbers    ${power_sm获取值1}    2
    #断开连接SNMP
    [Teardown]    Run keywords    同时设置三相电压频率    220    50
    ...    AND    打开交流源输出

power_sm_0008_油机关闭
    [Tags]    T1-1
    Comment    ${能源网管数据}    能源网管协议_设置控制量    设置油机控制量    ${BOARD_IP}    ${SSH}
    Comment    ${对比结果}    能源网管协议_批量对比参数设置    ${能源网管数据}    设置油机控制量
    Comment    Should Be True    ${对比结果}
    油机管理初始化
    能源网管协议_设置单个设备单个控制量    1    油机关闭    ${BOARD_IP}    ${SSH}
    ${能源网管数据}    能源网管协议_设置控制量    设置油机控制量    ${BOARD_IP}    ${SSH}
    ${对比结果}    能源网管协议_批量对比参数设置    ${能源网管数据}    设置油机控制量
    Comment    能源网管协议_设置单个设备单个控制量    2    整流器唤醒    ${BOARD_IP}    ${SSH}
    Comment    能源网管协议_设置单个设备单个控制量    2    整流器风扇调速允许    ${BOARD_IP}    ${SSH}
    Comment    Should Be True    ${对比结果}
    [Teardown]    Run keywords    同时设置三相电压频率    220    50
    ...    AND    打开交流源输出
