*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
恢复出厂设置功能测试
    [Setup]
    连接CSU
    恢复默认值并重新登录
    连接CSU
    #监控当前历史记录数量
    ${历史告警开始数量}    获取web历史告警数量    ${empty}    ${empty}
    ${历史数据开始数量}    获取web历史数据数量    ${empty}    ${empty}
    ${操作记录开始数量}    获取web历史事件数量    ${empty}    ${empty}    所有
    ${充电记录开始数量}    获取web事件记录数量    电池充电记录
    ${放电记录开始数量}    获取web事件记录数量    电池放电记录
    ${测试记录开始数量}    获取web事件记录数量    电池测试记录
    ${均充记录开始数量}    获取web事件记录数量    电池均充记录
    ${太阳能工作记录开始数量}    获取web事件记录数量    太阳能工作记录
    ${市电工作记录开始数量}    获取web事件记录数量    市电工作记录
    ${油机加油记录开始数量}    获取web事件记录数量    油机加油记录
    ${油机漏油记录开始数量}    获取web事件记录数量    油机漏油记录
    ${油机启停记录开始数量}    获取web事件记录数量    油机启停记录
    ${channel_config}    显示属性配置    CPU利用率    模拟量    ON    ON
    @{channel_config}    获取显示属性配置    CPU利用率    模拟量
    should be equal    ${channel_config}[0]    ON
    should be equal    ${channel_config}[1]    ON
    Wait Until Keyword Succeeds    10    2    设置web参数量    <<禁止所有告警~0x1001030010001>>    次要
    ${告警级别获取}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    should be true    '${告警级别获取}' == '次要'
    Wait Until Keyword Succeeds    10    2    设置web参数量    系统过载告警    次要
    ${告警级别获取}    获取web参数量    系统过载告警
    should be true    '${告警级别获取}' == '次要'
    Wait Until Keyword Succeeds    10    2    设置web参数量    CPU利用率高阈值    10    #下限
    ${阈值获取}    获取web参数量    CPU利用率高阈值
    should be true    ${阈值获取}==10
    Wait Until Keyword Succeeds    30    2    设置web参数量    站点名称    Site-X#8    #站点名称需要继承
    ${站点名称获取值}    获取web参数量    站点名称
    should be true    '${站点名称获取值}'=='Site-X#8'
    ${channel_config}    通道配置_AI    ${plat.batttemp1}    0.0000    3.0000    电池_1    电池温度
    should be true    ${channel_config} == True
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    should be true    ${电池温度}>40.0
    恢复出厂设置并重新登录
    #恢复出厂设置功能后历史记录数量
    ${历史告警结束数量}    获取web历史告警数量    ${empty}    ${empty}
    should be true    ${历史告警结束数量} == 0
    ${历史数据结束数量}    获取web历史数据数量    ${empty}    ${empty}
    should be true    ${历史数据结束数量}>0
    ${操作记录结束数量}    获取web历史事件数量    ${empty}    ${empty}    所有
    @{操作记录内容1}    获取web历史事件内容    ${empty}    ${empty}    100    1    10
    should contain    ${操作记录内容1}[0]    系统复位 恢复出厂设置
    ${充电记录结束数量}    获取web事件记录数量    电池充电记录
    should be true    ${充电记录结束数量} <= 1
    ${放电记录结束数量}    获取web事件记录数量    电池放电记录
    should be true    ${放电记录结束数量} == 0
    ${测试记录结束数量}    获取web事件记录数量    电池测试记录
    should be true    ${测试记录结束数量} == 0
    ${均充记录结束数量}    获取web事件记录数量    电池均充记录
    should be true    ${均充记录结束数量} == 0
    ${太阳能工作记录结束数量}    获取web事件记录数量    太阳能工作记录
    should be true    ${太阳能工作记录结束数量} == 0
    ${市电工作记录结束数量}    获取web事件记录数量    市电工作记录
    should be true    ${市电工作记录结束数量}==0
    ${油机启停记录结束数量}    获取web事件记录数量    油机启停记录
    should be true    ${油机启停记录结束数量} ==0
    ${油机加油记录结束数量}    获取web事件记录数量    油机加油记录
    should be true    ${油机加油记录结束数量} == 0
    ${油机漏油记录结束数量}    获取web事件记录数量    油机漏油记录
    should be true    ${油机漏油记录结束数量} == 0
    @{channel_config}    获取显示属性配置    CPU利用率    模拟量
    should be equal    ${channel_config}[0]    OFF
    should be equal    ${channel_config}[1]    OFF
    ${告警级别获取}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    should be true    '${告警级别获取}'=='严重'
    ${告警级别获取}    获取web参数量    系统过载告警
    should be true    '${告警级别获取}'=='严重'
    ${缺省值}    获取web参数上下限范围    CPU利用率高阈值
    ${获取值}    获取web参数量    CPU利用率高阈值
    should be true    ${获取值}==${缺省值}[0]
    ${获取值}    获取web参数量    站点名称
    Should Be Equal    ${获取值}    ${站点名称获取值}
    ${电池温度}    获取web实时数据    电池温度-1    #配置参数恢复默认值后，电池温度应变为正常
    should be true    ${电池温度}>40.0
    [Teardown]    Run Keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    0    1    电池_1    电池温度
    ...           AND    重启CSU容器
