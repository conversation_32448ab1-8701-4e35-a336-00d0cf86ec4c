*** Settings ***
Suite Setup       设置历史记录最大条数    dg_refuel    8    100
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
WEB导出油机加油记录测试
    连接CSU
    ${数据条数1}    ${第一条数据内容1}    ${最新一条数据内容1}    查询指定历史记录    油机加油记录
    ${导出路径}    导出指定历史记录    油机加油记录    ${datapassword}
    ${数据条数2}    ${第一条数据内容2}    ${最新一条数据内容2}    解析指定历史记录文件    油机加油记录    ${导出路径}
    Run Keyword if    ${数据条数1}!=False and ${数据条数2}!=False    should be equal    ${第一条数据内容1}[0]    ${第一条数据内容2}[0]
    Run Keyword if    ${数据条数1}!=False and ${数据条数2}!=False    should be equal    ${第一条数据内容1}[1]    ${第一条数据内容2}[1]
    Run Keyword if    ${数据条数1}!=False and ${数据条数2}!=False    should be equal    ${第一条数据内容1}[2]    ${第一条数据内容2}[2]
    Run Keyword if    ${数据条数1}!=False and ${数据条数2}!=False    should be equal    ${第一条数据内容1}[3]    ${第一条数据内容2}[3]
    Run Keyword if    ${数据条数1}!=False and ${数据条数2}!=False    should be equal    ${第一条数据内容1}[4]    ${第一条数据内容2}[4]
    Run Keyword if    ${数据条数1}!=False and ${数据条数2}!=False    should be equal    ${数据条数1}    ${数据条数2}
    Run Keyword if    ${数据条数1}!=False and ${数据条数2}!=False    should be equal    ${最新一条数据内容1}[0]    ${最新一条数据内容2}[0]
    Run Keyword if    ${数据条数1}!=False and ${数据条数2}!=False    should be equal    ${最新一条数据内容1}[1]    ${最新一条数据内容2}[1]
    Run Keyword if    ${数据条数1}!=False and ${数据条数2}!=False    should be equal    ${最新一条数据内容1}[2]    ${最新一条数据内容2}[2]
    Run Keyword if    ${数据条数1}!=False and ${数据条数2}!=False    should be equal    ${最新一条数据内容1}[3]    ${最新一条数据内容2}[3]
    Run Keyword if    ${数据条数1}!=False and ${数据条数2}!=False    should be equal    ${最新一条数据内容1}[4]    ${最新一条数据内容2}[4]
