*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
snmp批量获取SDDU数字量测试
    写入CSV文档    SDDU数字量获取测试    信号名称    信号值    结果    备注
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=digital data    INDEX_DEVICE_CH=智能直流配电单元
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SDDU2排除数字量信号}    ${排除列表}
    ${设备名称}    Set Variable    smartDCDistributionUnit
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    ${设备名称}    digitalData
    @{SDDU2数字量数据}    create list
    FOR    ${i}    IN    @{snmp待测}
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        ${信号序号}    BuiltIn.Convert To Integer    ${信号序号}
        Run Keyword IF    ${信号序号}==1    Append to List    ${SDDU2数字量数据}    ${i}
        Run Keyword IF    ${信号序号}==5    Append to List    ${SDDU2数字量数据}    ${i}
        Run Keyword IF    ${信号序号}==9    Append to List    ${SDDU2数字量数据}    ${i}
        Run Keyword IF    ${信号序号}==13    Append to List    ${SDDU2数字量数据}    ${i}
    END
    FOR    ${i}    IN    @{SDDU2数字量数据}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    snmp_name
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        ${信号序号1}    BuiltIn.Convert To Integer    ${信号序号}
        ${呼叫命令}    Evaluate    (${信号序号1}-1)//4+1
        ${命令名称}    Run Keyword IF    "${呼叫命令}"=="1"    Set Variable    呼叫
        ...    ELSE    Set Variable    呼叫${呼叫命令}
        ${缺省值}    获取web参数上下限范围    ${信号名称}
        Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    snmp    IntelAirSwitExtend    ${信号名称}    ${命令名称}    ${缺省值}[1]    SDDU数字量获取测试    null    ${节点名称}    ${信号序号}
        Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    snmp    IntelAirSwitExtend    ${信号名称}    ${命令名称}    ${缺省值}[2]    SDDU数字量获取测试    null    ${节点名称}    ${信号序号}
        Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    snmp    IntelAirSwitExtend    ${信号名称}    ${命令名称}    ${缺省值}[0]    SDDU数字量获取测试    null    ${节点名称}    ${信号序号}
    END
    断开连接SNMP
