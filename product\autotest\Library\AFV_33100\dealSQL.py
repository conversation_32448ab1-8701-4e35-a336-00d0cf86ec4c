#-*-coding:utf-8 -*-
import sqlite3
import os
import const
#######################
#  新增/删除数据库表
#######################
class ConnectSql:
    sDbFilePath = ''
    id_AllInfo = 0
    bPrtInfo = False
    
    def __init__(self, path=None):
        self.sDbFilePath = path
    #-------------------------
    def get_conn(self, path):
        conn = sqlite3.connect(path)
        if os.path.exists(path) and os.path.isfile(path):
            if self.bPrtInfo:
                print('Hard Disk:[{}]'.format(path))
            return conn
        else:
            conn = None
            if self.bPrtInfo:
                print('memory:[:memory:]')
            return sqlite3.connect(':memory:')
    #-------------------------
    def get_cursor(self, conn):
        if conn is not None:
            return conn.cursor()
        else:
            return self.get_conn('').cursor()
    #-------------------------
    def close_all(self, conn, cu):
        try:
            if cu is not None:
                cu.close()
        finally:
            if cu is not None:
                cu.close()
    #-------------------------
    def save(self, conn, sql, data):
        if sql is not None and sql != '':
            if data is not None:
                cu = self.get_cursor(conn)
                for d in data:
                    cu.execute(sql, d)
                conn.commit()
                self.close_all(conn, cu)
        else:
            print('the [{}] is empty or equal None!'.format(sql))
    #-------------------------
    def requre_tableName(self, conn, sql):
        if sql is not None and sql != '':
            cu = self.get_cursor(conn)        
            cu.execute(sql)
            conn.commit()
            r = cu.fetchall() #要提取查询到的数据,使用游标的fetch***函数
            if len(r) > 0:
                for e in range(len(r)):
                    print(r[e])
            self.close_all(conn, cu)
        else:
            print('the [{}] is empty or equal None!'.format(sql))
    #-------------------------
    def fetchall(self, conn, sql):
        if sql is not None and sql != '':
            cu = self.get_cursor(conn)
            cu.execute(sql)
            return cu.fetchall() #要提取查询到的数据,使用游标的fetch***函数            
        else:
            print('the [{}] is empty or equal None!'.format(sql))
            return []
    #-------------------------
    def sort(self, conn, sql):
        if sql is not None and sql != '':
            cu = self.get_cursor(conn)
            cu.execute(sql)
            return cu.fetchall() #要提取查询到的数据,使用游标的fetch***函数
        else:
            print('the [{}] is empty or equal None!'.format(sql))
            return []
    #-------------------------
    def fetchone(self, conn, sql, data):
        '''查询一条数据'''
        if sql is not None and sql != '':
            if data is not None:
                #Do this instead
                d = (data,) 
                cu = self.get_cursor(conn)
                if self.bPrtInfo:
                    print('执行sql:[{}],参数:[{}]'.format(sql, data))
                cu.execute(sql, d)
                return cu.fetchall() #2015.4.21 增加返回量
            else:
                print('the [{}] equal None!'.format(data))
        else:
            print('the [{}] is empty or equal None!'.format(sql))
    #-------------------------
    def requreRec(self, conn, sql, listData):
        '''条件查询数据'''
        if sql is not None and sql != '':
            cu = self.get_cursor(conn)
            cu.execute(sql, listData)
            r = cu.fetchall()
            return r              
        else:
            print('the [{}] is empty or equal None!'.format(sql))
            return []

    def update(self, conn, sql, data):
        if sql is not None and sql != '':
            if data is not None:
                cu = self.get_cursor(conn)
                for d in data:
                    cu.execute(sql, d)
                conn.commit()
                self.close_all(conn, cu)
        else:
            print('the [{}] is empty or equal None!'.format(sql))
    #-------------------------
    def delete(self, conn, sql, data):
        if sql is not None and sql != '':
            if data is not None:
                cu = self.get_cursor(conn)
                for d in data:
                    cu.execute(sql, d)
                conn.commit()
                self.close_all(conn, cu)
        else:
            print('the [{}] is empty or equal None!'.format(sql))
    #-------------------------
    def drop_table(self, conn, table):
        if table is not None and table != '':
            sql = 'DROP TABLE IF EXISTS ' + table
            cu = self.get_cursor(conn)
            cu.execute(sql)
            conn.commit()
##            if self.bPrtInfo:
##                print('删除数据库表[{}]成功!'.format(table))
            self.close_all(conn, cu)
        else:
            print('the [{}] is empty or equal None!'.format(sql))
    #-------------------------
    def create_table(self, conn, sql, tableName):
        if sql is not None and sql != '':
            cu = self.get_cursor(conn)
##            if self.bPrtInfo:
##                print('执行sql:[{}]'.format(sql))
            cu.execute(sql)
            conn.commit()
            #print('创建数据库表[{}]成功!'.format(tableName))
            self.close_all(conn, cu)
        else:
            print('the [{}] is empty or equal None!'.format(sql))
    #==========================
    def makeTable(self, tableName, cmdSQL):
        conn = self.get_conn(self.sDbFilePath)
        self.drop_table(conn, tableName)        
        self.create_table(conn, cmdSQL, tableName)
    #==========================
    def saveData(self, tableName, save_sql, data):
        conn = self.get_conn(self.sDbFilePath)
        self.save(conn, save_sql, data)
    #==========================    
    #-------------------------
    def requreTableName_test(self):
        conn = self.get_conn(self.sDbFilePath)
        #requreTableName_sql = '''SELECT * FROM SQLITE_MASTER WHERE type = "table"'''
        requreTableName_sql = '''SELECT NAME FROM SQLITE_MASTER WHERE type = "table"'''
        self.requre_tableName(conn, requreTableName_sql)
    #-------------------------
    def fetchall_test(self, tableName):
        fetchall_sql = '''SELECT * FROM ''' + tableName
        conn = self.get_conn(self.sDbFilePath)
        self.fetchall(conn, fetchall_sql)
    #-------------------------
    def sort_test(self, tableName):
        sort_sql = '''SELECT * FROM '''+tableName+''' ORDER BY EnName DESC''' #降序4321
        #sort_sql = '''SELECT * FROM student ORDER BY age''' #升序1234
        conn = self.get_conn(self.sDbFilePath)
        self.sort(conn, sort_sql)
    #-------------------------
    def fetchone_test(self):
        fetchone_sql = 'SELECT * FROM student WHERE ID = ? '
        data = 1
        conn = self.get_conn(self.sDbFilePath)
        self.fetchone(conn, fetchone_sql, data)
    #-------------------------
    def update_test(self):
        update_sql = 'UPDATE student SET name = ? WHERE ID = ? '
        data = [('HongtenAA', 1),
                ('HongtenBB', 2),
                ('HongtenCC', 3),
                ('HongtenDD', 4)]
        conn = self.get_conn(self.sDbFilePath)
        self.update(conn, update_sql, data)
    #-------------------------
    def delete_test(self):
        delete_sql = 'DELETE FROM student WHERE NAME = ? AND ID = ? '
        data = [('HongtenAA', 1),
                ('HongtenCC', 3)]
        conn = self.get_conn(self.sDbFilePath)
        self.delete(conn, delete_sql, data)    
    #=============================================================
    #======  以下内容根据需要,自己发挥 
    #=============================================================
    def creatTable_TypeAndConstDef(self):
        table = 'TypeAndConstDef' #数据字典中的sheet:类型和常量定义
        sql   = 'CREATE TABLE '+table+' ([typeOfDef],[nameOfDef],[byteCnt],[value],[detailOfDef],[nameCn],[remark])'        
        self.makeTable(table, sql)
        
    def creatTable_DataDict(self):
        table = 'DataDict' #数据字典中的sheet:所有的子设备
        sql   = 'CREATE TABLE '+ table + ' ' + const.titleOfDataDict      
        self.makeTable(table, sql)
    #-------------------------
    def creatTable_ModbusBin(self):
        table = 'ModbusBin'
        sql   = 'CREATE TABLE '+ table + ' ' + const.titleOfModbusBin
        self.makeTable(table, sql)
    def creatTable_AllInfo(self):
        table = 'AllInfo'
        sql   = 'CREATE TABLE '+ table + ' ' + const.titleOfAllInfo
        self.makeTable(table, sql)
    def creatTable_BaseH(self):
        table = 'BaseH'
        sql   = 'CREATE TABLE '+table+' ([macroName],[value(hex)],[value],[nameCn])'
        self.makeTable(table, sql)
    #-------------------------
    def creatTable(self):
        self.creatTable_TypeAndConstDef()
        self.creatTable_DataDict()
        #
        self.creatTable_BaseH()
        #
        self.creatTable_ModbusBin()
        self.creatTable_AllInfo()
        
#-------------------------
if __name__ == "__main__":
    myDB = ConnectSql("powerDB")
    myDB.creatTable()


    
