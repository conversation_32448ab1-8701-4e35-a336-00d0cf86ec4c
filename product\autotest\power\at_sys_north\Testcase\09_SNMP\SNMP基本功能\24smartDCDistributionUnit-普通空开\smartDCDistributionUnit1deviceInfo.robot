*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
snmp批量获取SDDU设备信息测试
    写入CSV文档    SDDU设备信息获取测试    信号名称    信号值    结果    备注
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string32    INDEX_DEVICE_CH=智能直流配电单元
    ${排除列表}    create list
    @{信号名称列表1}   create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SDDU排除系统名称}    ${排除列表}    2
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    smartDCDistributionUnit    deviceInfo
    FOR    ${i}    IN    @{snmp待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    snmp_name
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
        Set To Dictionary    ${dict}     snmp_name     ${节点名称}
        Set To Dictionary    ${dict}     Vindex     ${信号序号}
		Append To List       ${信号名称列表1}    ${dict}
    END
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    SDU2    ${信号名称列表1}    建链    V99.23    SDDU设备信息获取测试    字符    null
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    SDU2    ${信号名称列表1}    建链    V10.10    SDDU设备信息获取测试    字符    null
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    SDU2    ${信号名称列表1}    建链    V1.81    SDDU设备信息获取测试    字符    null

    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string32    INDEX_DEVICE_CH=智能直流配电单元
    ${排除列表}    create list
    @{信号名称列表2}   create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SDDU排除软件版本}    ${排除列表}
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    smartDCDistributionUnit    deviceInfo
    FOR    ${i}    IN    @{snmp待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    snmp_name
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
        Set To Dictionary    ${dict}     snmp_name     ${节点名称}
        Set To Dictionary    ${dict}     Vindex     ${信号序号}
		Append To List       ${信号名称列表2}    ${dict}
    END
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    SDU2    ${信号名称列表2}    建链    VZXDU48 FB100B3    SDDU设备信息获取测试    字符    null
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    SDU2    ${信号名称列表2}    建链    ZTE-smartli    SDDU设备信息获取测试    字符    null
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    SDU2    ${信号名称列表2}    建链    VZXDU48 FB100C2    SDDU设备信息获取测试    字符    null

    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=date    INDEX_DEVICE_CH=智能直流配电单元
    ${排除列表}    create list
    @{信号名称列表3}   create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${排除列表}    ${排除列表}
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    smartDCDistributionUnit    deviceInfo
    FOR    ${i}    IN    @{snmp待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    snmp_name
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
        Set To Dictionary    ${dict}     snmp_name     ${节点名称}
        Set To Dictionary    ${dict}     Vindex     ${信号序号}
		Append To List       ${信号名称列表3}    ${dict}
    END
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    SDU2    ${信号名称列表3}    建链    2018-11-15    SDDU设备信息获取测试    字符    null
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    SDU2    ${信号名称列表3}    建链    2021-08-23    SDDU设备信息获取测试    字符    null
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    SDU2    ${信号名称列表3}    建链    2018-10-31    SDDU设备信息获取测试    字符    null

