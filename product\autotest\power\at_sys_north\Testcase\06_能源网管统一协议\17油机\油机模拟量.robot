*** Settings ***
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_批量获取油机模拟量
    [Documentation]    9min
    写入CSV文档    油机模拟量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=油机
    ${排除列表}    create list
    Append To List    ${油机排除模拟量信号}    油机油压~0x190010100f0001
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${油机排除模拟量信号}    ${排除列表}
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    油机    analog data    ${油机排除模拟量信号}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}    True
    Comment    FOR    ${i}    IN    @{power_sm待测}
	${缺省值列表}   获取缺省值列表  ${power_sm待测}    1          power_sm
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    power_sm    oileng      只读     ${缺省值列表}      油机模拟量获取测试    获取油机模拟量  
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    2              power_sm
	Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    power_sm    oileng      只读     ${缺省值列表}      油机模拟量获取测试    获取油机模拟量    
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    0              power_sm
	Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    power_sm    oileng      只读     ${缺省值列表}      油机模拟量获取测试    获取油机模拟量   
    
    



power_sm_0002_批量获取市电模拟量
    [Documentation]    9min
    写入CSV文档    市电模拟量获取测试    信号名称    信号值    结果
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=市电
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${市电排除模拟量信号}    ${排除列表}
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    市电    analog data    ${市电排除模拟量信号}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}    True
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    1    power_sm
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    power_sm    oileng       只读    ${缺省值列表}    市电模拟量获取测试    获取市电模拟量    
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    2    power_sm
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    power_sm    oileng       只读    ${缺省值列表}    市电模拟量获取测试    获取市电模拟量   
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    0    power_sm
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    power_sm    oileng           只读    ${缺省值列表}    市电模拟量获取测试    获取市电模拟量    
	

power_sm_0008_油机视在功率√
    [Documentation]    def,min,max:50,40,75
    连接CSU
    设置子工具值    oileng    all    只读    发电 A 相视在功率    1001
    设置子工具值    oileng    all    只读    发电 B 相视在功率    2002
    设置子工具值    oileng    all    只读    发电 C 相视在功率    3003
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    油机视在功率_1-1    3003
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    油机视在功率_2-1    2002
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    油机视在功率_3-1    1001
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取油机模拟量    油机视在功率_1-1    ${SSH}
    ${power_sm获取值2}    power_sm_获取一个数据的值    获取油机模拟量    油机视在功率_2-1    ${SSH}
    ${power_sm获取值3}    power_sm_获取一个数据的值    获取油机模拟量    油机视在功率_3-1    ${SSH}
    should be equal as numbers    ${power_sm获取值1}    3003
    should be equal as numbers    ${power_sm获取值2}    2002
    should be equal as numbers    ${power_sm获取值3}    1001
    设置子工具值    oileng    all    只读    发电 A 相视在功率    3010
    设置子工具值    oileng    all    只读    发电 B 相视在功率    3120
    设置子工具值    oileng    all    只读    发电 C 相视在功率    3478
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    油机视在功率_1-1    3478
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    油机视在功率_2-1    3120
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    油机视在功率_3-1    3010
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取油机模拟量    油机视在功率_1-1    ${SSH}
    ${power_sm获取值2}    power_sm_获取一个数据的值    获取油机模拟量    油机视在功率_2-1    ${SSH}
    ${power_sm获取值3}    power_sm_获取一个数据的值    获取油机模拟量    油机视在功率_3-1    ${SSH}
    should be equal as numbers    ${power_sm获取值1}    3478
    should be equal as numbers    ${power_sm获取值2}    3120
    should be equal as numbers    ${power_sm获取值3}    3010
    设置子工具值    oileng    all    只读    发电 A 相视在功率    4300
    设置子工具值    oileng    all    只读    发电 B 相视在功率    4334
    设置子工具值    oileng    all    只读    发电 C 相视在功率    4367
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    油机视在功率_1-1    4367
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    油机视在功率_2-1    4334
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    油机视在功率_3-1    4300
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取油机模拟量    油机视在功率_1-1    ${SSH}
    ${power_sm获取值2}    power_sm_获取一个数据的值    获取油机模拟量    油机视在功率_2-1    ${SSH}
    ${power_sm获取值3}    power_sm_获取一个数据的值    获取油机模拟量    油机视在功率_3-1    ${SSH}
    should be equal as numbers    ${power_sm获取值1}    4367
    should be equal as numbers    ${power_sm获取值2}    4334
    should be equal as numbers    ${power_sm获取值3}    4300

power_sm_0009_油机有功功率√
    [Documentation]    def,min,max:50,40,75
    连接CSU
    设置子工具值    oileng    all    只读    发电 A 相有功功率    4000
    设置子工具值    oileng    all    只读    发电 B 相有功功率    5000
    设置子工具值    oileng    all    只读    发电 C 相有功功率    6000
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    油机有功功率_1-1    6000
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    油机有功功率_2-1    5000
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    油机有功功率_3-1    4000
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取油机模拟量    油机有功功率_1-1    ${SSH}
    ${power_sm获取值2}    power_sm_获取一个数据的值    获取油机模拟量    油机有功功率_2-1    ${SSH}
    ${power_sm获取值3}    power_sm_获取一个数据的值    获取油机模拟量    油机有功功率_3-1    ${SSH}
    should be equal as numbers    ${power_sm获取值1}    6000
    should be equal as numbers    ${power_sm获取值2}    5000
    should be equal as numbers    ${power_sm获取值3}    4000
    设置子工具值    oileng    all    只读    发电 A 相有功功率    2000
    设置子工具值    oileng    all    只读    发电 B 相有功功率    3000
    设置子工具值    oileng    all    只读    发电 C 相有功功率    3568
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    油机有功功率_1-1    3568
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    油机有功功率_2-1    3000
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    油机有功功率_3-1    2000
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取油机模拟量    油机有功功率_1-1    ${SSH}
    ${power_sm获取值2}    power_sm_获取一个数据的值    获取油机模拟量    油机有功功率_2-1    ${SSH}
    ${power_sm获取值3}    power_sm_获取一个数据的值    获取油机模拟量    油机有功功率_3-1    ${SSH}
    should be equal as numbers    ${power_sm获取值1}    3568
    should be equal as numbers    ${power_sm获取值2}    3000
    should be equal as numbers    ${power_sm获取值3}    2000
    设置子工具值    oileng    all    只读    发电 A 相有功功率    2200
    设置子工具值    oileng    all    只读    发电 B 相有功功率    2234
    设置子工具值    oileng    all    只读    发电 C 相有功功率    2267
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    油机有功功率_1-1    2267
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    油机有功功率_2-1    2234
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    油机有功功率_3-1    2200
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取油机模拟量    油机有功功率_1-1    ${SSH}
    ${power_sm获取值2}    power_sm_获取一个数据的值    获取油机模拟量    油机有功功率_2-1    ${SSH}
    ${power_sm获取值3}    power_sm_获取一个数据的值    获取油机模拟量    油机有功功率_3-1    ${SSH}
    should be equal as numbers    ${power_sm获取值1}    2267
    should be equal as numbers    ${power_sm获取值2}    2234
    should be equal as numbers    ${power_sm获取值3}    2200

power_sm_0010_油机无功功率√
    [Documentation]    def,min,max:50,40,75
    连接CSU
    设置子工具值    oileng    all    只读    发电 A 相无功功率    98
    设置子工具值    oileng    all    只读    发电 B 相无功功率    88
    设置子工具值    oileng    all    只读    发电 C 相无功功率    68
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    油机无功功率_1-1    68
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    油机无功功率_2-1    88
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    油机无功功率_3-1    98
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取油机模拟量    油机无功功率_1-1    ${SSH}
    ${power_sm获取值2}    power_sm_获取一个数据的值    获取油机模拟量    油机无功功率_2-1    ${SSH}
    ${power_sm获取值3}    power_sm_获取一个数据的值    获取油机模拟量    油机无功功率_3-1    ${SSH}
    should be equal as numbers    ${power_sm获取值1}    68
    should be equal as numbers    ${power_sm获取值2}    88
    should be equal as numbers    ${power_sm获取值3}    98
    设置子工具值    oileng    all    只读    发电 A 相无功功率    1000
    设置子工具值    oileng    all    只读    发电 B 相无功功率    1100
    设置子工具值    oileng    all    只读    发电 C 相无功功率    1468
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    油机无功功率_1-1    1468
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    油机无功功率_2-1    1100
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    油机无功功率_3-1    1000
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取油机模拟量    油机无功功率_1-1    ${SSH}
    ${power_sm获取值2}    power_sm_获取一个数据的值    获取油机模拟量    油机无功功率_2-1    ${SSH}
    ${power_sm获取值3}    power_sm_获取一个数据的值    获取油机模拟量    油机无功功率_3-1    ${SSH}
    should be equal as numbers    ${power_sm获取值1}    1468
    should be equal as numbers    ${power_sm获取值2}    1100
    should be equal as numbers    ${power_sm获取值3}    1000
    设置子工具值    oileng    all    只读    发电 A 相无功功率    200
    设置子工具值    oileng    all    只读    发电 B 相无功功率    234
    设置子工具值    oileng    all    只读    发电 C 相无功功率    267
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    油机无功功率_1-1    267
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    油机无功功率_2-1    234
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    油机无功功率_3-1    200
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取油机模拟量    油机无功功率_1-1    ${SSH}
    ${power_sm获取值2}    power_sm_获取一个数据的值    获取油机模拟量    油机无功功率_2-1    ${SSH}
    ${power_sm获取值3}    power_sm_获取一个数据的值    获取油机模拟量    油机无功功率_3-1    ${SSH}
    should be equal as numbers    ${power_sm获取值1}    267
    should be equal as numbers    ${power_sm获取值2}    234
    should be equal as numbers    ${power_sm获取值3}    200

power_sm_0012_油机油压√
    [Documentation]    油机油压范围0-180
    连接CSU
    Comment    设置子工具值    oileng    all    只读    油压    5600
    Comment    Wait Until Keyword Succeeds    1m    5    信号量数据值为    油机油压-1    5600
    Comment    ${power_sm获取值1}    power_sm_获取一个数据的值    获取油机模拟量    油机油压-1    ${SSH}
    Comment    should be equal as numbers    ${power_sm获取值1}    5600
    设置子工具值    oileng    all    只读    油压    1800
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    油机油压-1    1800
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取油机模拟量    油机油压-1    ${SSH}
    should be equal as numbers    ${power_sm获取值1}    1800
    设置子工具值    oileng    all    只读    油压    67
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    油机油压-1    67
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取油机模拟量    油机油压-1    ${SSH}
    should be equal as numbers    ${power_sm获取值1}    67
    设置子工具值    oileng    all    只读    油压    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    油机油压-1    0
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取油机模拟量    油机油压-1    ${SSH}
    should be equal as numbers    ${power_sm获取值1}    0
