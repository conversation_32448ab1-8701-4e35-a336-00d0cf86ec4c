*** Settings ***
Suite Setup
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
1363_0002_1电池温度高
    [Setup]
    FOR    ${VAR}    IN RANGE    1    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置VRLA段告警级别~CID1=46H>>    <<设置VRLA段告警级别~CID2=82H>>    电池温度高
        ...    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取VRLA段告警级别~CID1=46H>>    <<获取VRLA段告警级别~CID2=81H>>    电池温度高
        ...    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    电池温度高    ${VAR}
        ${交流电压低告警级别}    获取web参数量    电池温度高
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
    Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    电池温度高    主要

1363_0004_2电池温度低告警级别
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置VRLA段告警级别~CID1=46H>>    <<设置VRLA段告警级别~CID2=82H>>    电池温度低
        ...    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取VRLA段告警级别~CID1=46H>>    <<获取VRLA段告警级别~CID2=81H>>    电池温度低
        ...    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    电池温度低    ${VAR}
        ${交流电压低告警级别}    获取web参数量    电池温度低
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
    Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    电池温度低    主要

1363_0006_3电池温度无效
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置VRLA段告警级别~CID1=46H>>    <<设置VRLA段告警级别~CID2=82H>>    电池温度无效
        ...    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取VRLA段告警级别~CID1=46H>>    <<获取VRLA段告警级别~CID2=81H>>    电池温度无效
        ...    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    电池温度无效    ${VAR}
        ${交流电压低告警级别}    获取web参数量    电池温度无效
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
    Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    电池温度无效    主要

1363_0008_4电池电压低
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置VRLA段告警级别~CID1=46H>>    <<设置VRLA段告警级别~CID2=82H>>    电池电压低
        ...    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取VRLA段告警级别~CID1=46H>>    <<获取VRLA段告警级别~CID2=81H>>    电池电压低
        ...    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    电池电压低    ${VAR}
        ${交流电压低告警级别}    获取web参数量    电池电压低
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
    Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    电池电压低    主要
