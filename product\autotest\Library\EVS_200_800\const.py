#-*-coding:utf-8 -*-
#不同的项目需要相应的的配置 start =====================
#数据字典的文件名
fileExcel = 'X500数据字典测试版.xlsx'
#设备信息,需要解析的sheet名称,及对应的其它信息
#'类型和常量定义'不需要在此列出,parseExcel已特殊处理
#'厂家信息'也不需要在此列出
#------------设备名,    设备个数, 预留的寄存器个数(起始地址,模拟量,状态量,告警量,参数量,控制量,厂家信息,告警输出干接点), 厂家信息ID1,设备个数>1时的几个宏定义

devCfgInfo = [['监控模块',  1, [0x1000, 100,100,100,400, 100,100,100], '0x05' ],
              ['环境',     1, [0x3000, 100,100,100,100, 100,100,100], '' ],
              ['充电模块',  2, [0x4000, 100,100,100,200, 100,100,100], '', ['ANA_NUM_PERCHARGER','DIGIT_NUM_PERCHARGER','ALM_NUM_PERCHARGER','FACT_NUM_PERCHARGER']],
              ['旁路模块',  1, [0x5000, 100,100,100,200, 100,100,100], '0x55' ],
              ['功率模块',  10,[0x6000, 100,100,200,400, 100,100,200], '0xF5', ['ANA_NUM_PERPU','DIGIT_NUM_PERPU','ALM_NUM_PERPU','FACT_NUM_PERPU'] ],
              ['厂家信息',  1, [],''],
              ]

#-----------------------------
#不同的项目,子设备描述需要的列数可能不同,需要对应
#第一列的内容是 对应的 sheetName,然后就是数据字典的内容
titleOfDataDict = '([devName],\
[macroName],[dispMode],[structVarName],[ID1H],[ID1L],\
[ID1],[ID2],[nameCnAll],[nameCn],[nameEnAll],\
[nameEn],[count],[unit],[dataType],[arrayLen],\
[precision],[default],[min],[max],[step],\
[suSetEn],[scGetEn],[scSetEn],[paraAttr],[Comments],\
[paraRelation],[remark1],[remark2],[t1],[t2],[t3])'  

#不同的项目需要相应的的配置 end =====================

fileDB    = 'modbusDB'
fileCsv   = 'ModbusNoTable.csv'
#===================================================================
#以下部分尽量不要修改,会影响到具体代码
#===================================================================
titleOfAllInfo = '([devName],\
[modbusAddr],[fullID],[DB_DtTp],[dataTypeEnum],[dataType],\
[arrayLen],[precision],[modbusByte],[default],[min],\
[max],[nameCn],[unit],[defName],[scGet],\
[scSet],[comment],[ID1L],[remark])'

titleOfModbusBin = '([NO_hex],[NO_int],[ID_hex],[ID_int],[RegAddr_hex],\
[RegAddr_int],[SuPre],[DB_DtTp],[ModbusBytes],[DtTpEnum_str],\
[DtTpEnum_int],[ScSet])'        
#===================================================================
#以下部分基本上不需要修改
#===================================================================
lstTitleOfDataDict  = titleOfDataDict[1:-1].split(',')
lstTitleOfAllInfo   = titleOfAllInfo[1:-1].split(',')
lstTitleOfModbusBin = titleOfModbusBin[1:-1].split(',')
#各数据类型对应与MODBUS的寄存器个数
regCnt = {'BOOLEAN':1,
           'INT8U':1,
           'INT8S':1,
           'INT16U':1,
           'INT16S':1,
           'INT32U':2,
           'INT32S':2,
           'PASSWORD':0.5, #4bytes字符串
           'FP32':2,
           'INT64S':4,
           'T_TimeStruct':6,
           'T_DateStruct':3,
           'T_ProcessTimeStruct':12,
           'CHAR':0.5,
           'IPV4_ADDR':0.5,
           'PHONE':0.5,
           'EMAIL_ADDR':0.5
            }

#数据类型宏定义
dataType = ['VOID',    'BOOLEAN',     'CHAR',        'INT8U',              'INT8S',
            'INT16U',  'INT16S',      'INT32U',      'INT32S',             'FP32',
            'INT64S',  'T_TimeStruct','T_DateStruct','T_ProcessTimeStruct','BIT',
            'PASSWORD','PHONE',       'EMAIL_ADDR',  'IPV4_ADDR' ]

