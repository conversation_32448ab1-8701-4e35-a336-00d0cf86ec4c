*** Settings ***
Suite Setup       北向SMR测试前置条件
Suite Teardown    整流器测试结束条件
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
snmp批量获取状态量和SMR告警测试
    [Documentation]    45min
    写入CSV文档    整流器数字量和告警量获取测试    信号名称    信号值    结果
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${级别设置值}    获取web参数量    整流器告警
    run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    整流器告警    严重
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=整流器
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除告警量信号}    ${排除列表}    1    ${模拟整流器地址}    1
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    rectifier    alarm    True    0    1
    Comment    ${指定数据1}    Create Dictionary    signal_name    <<整流器散热器过温关机-7~0x7001030030001>>    convention    False    device_name    整流器    data_type    int    unit    None    snmp_name    sMRAlarm7value    signal_node    sMRAlarm7
    ...    Vindex    0
    Comment    ${指定数据2}    Create Dictionary    signal_name    <<整流器交流过压关机-4~0x7001030040001>>    convention    False    device_name    整流器    data_type    int    unit    None    snmp_name    sMRAlarm7value    signal_node    sMRAlarm4
    ...    Vindex    0
    Comment    @{snmp待测}    Create List    ${指定数据1}    ${指定数据2}
    ${待测数据长度}    Get Length    ${snmp待测}
    ${待测}    Evaluate    ${待测数据长度}-1
    ${待测索引}    Evaluate    random.randint(0,${待测})    random
    FOR    ${i}    IN    ${snmp待测}[${待测索引}]
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    signal_node
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        Run Keyword And Continue On Failure    整流器或PU告警量产生封装判断结果    snmp    SMR    ${信号名称}    数字量    ${告警产生}    ${模拟整流器地址}    整流器告警    整流器数字量和故障量获取测试    整流器    ${节点名称}    ${信号序号}    null    null    null
        ...    null
        Run Keyword And Continue On Failure    整流器或PU告警量恢复封装判断结果    snmp    SMR    ${信号名称}    数字量    ${告警恢复}    ${模拟整流器地址}    整流器告警    整流器数字量和故障量获取测试    整流器    ${节点名称}    ${信号序号}    null    null    null
        ...    null
    END
    断开连接SNMP

snmp批量获取状态量和SMR故障测试
    [Documentation]    25min
    写入CSV文档    整流器数字量和故障量获取测试    信号名称    信号值    结果
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${级别设置值}    获取web参数量    整流器故障
    run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    整流器故障    严重
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=整流器
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除故障量信号}    ${排除列表}    1    ${模拟整流器地址}    1
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    rectifier    alarm    True    0    2
    ${待测数据长度}    Get Length    ${snmp待测}
    ${待测}    Evaluate    ${待测数据长度}-1
    ${待测索引}    Evaluate    random.randint(0,${待测})    random
    FOR    ${i}    IN    ${snmp待测}[${待测索引}]
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    signal_node
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        Run Keyword And Continue On Failure    整流器或PU故障量产生封装判断结果    snmp    SMR    ${信号名称}    数字量    ${告警产生}    ${模拟整流器地址}    整流器故障    整流器数字量和故障量获取测试    整流器    ${节点名称}    ${信号序号}    null    null    null
        ...    null
        Run Keyword And Continue On Failure    整流器或PU故障量恢复封装判断结果    snmp    SMR    ${信号名称}    数字量    ${告警恢复}    ${模拟整流器地址}    整流器故障    整流器数字量和故障量获取测试    整流器    ${节点名称}    ${信号序号}    null    null    null
        ...    null
    END
    断开连接SNMP
