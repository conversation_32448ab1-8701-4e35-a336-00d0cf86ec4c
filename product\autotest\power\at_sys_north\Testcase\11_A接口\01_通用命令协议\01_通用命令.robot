*** Settings ***
Resource          ../../../../../ztepwrlibrary/Resource/协议平台关键字.robot
Resource          ../A接口关键字_V30.robot

*** Test Cases ***
获取时间信息
    ${命令名称}    set variable    获取时间_40H_4DH    获取时间_41H_4DH    获取时间_42H_4DH
    FOR    ${val}    IN    @{命令名称}
        ${获取结果}    ${A接口获取数据}    协议平台获取北向数据    ${val}
        should be true    ${获取结果}
        ${当前CSU时间}    web获取csu时间
        ${A接口对比时间}    A接口对比时间    ${A接口获取数据}    ${当前CSU时间}    A_系统时间_1
        should be true    ${A接口对比时间}
    END

设定时间信息
    Comment    设置正常时间
    ${设置命令}    set variable    设置时间1_40H_4EH    设置时间2_41H_4EH    设置时间3_42H_4EH    设置时间1_40H_4EH    设置时间2_41H_4EH    设置时间3_42H_4EH
    ${命令名称}    set variable    获取时间_40H_4DH    获取时间_41H_4DH    获取时间_42H_4DH    获取时间_40H_4DH    获取时间_41H_4DH    获取时间_42H_4DH
    ${有效时间}    set variable    2000-01-01 00:00:00    2037-12-31 23:59:59    2024-02-29 00:00:00
    ${无效时间}    set variable    1999-12-31 23:59:59    2038-01-01 00:00:00    2023-02-29 00:00:00    2000-01-00 00:00:00    2037-12-31 24:00:00    2023-01-02 00:02:60
    FOR    ${命令1}    ${命令2}    ${命令3}    IN ZIP    ${设置命令}    ${命令名称}    ${有效时间}
        ${协议平台设置北向数据}    ${信息}    协议平台设置北向数据    ${命令1}    设置时间=${命令3}
        should be true    ${协议平台设置北向数据}
        ${返回值}    ${A接口获取数据}    协议平台获取北向数据    ${命令2}
        ${当前CSU时间}    web获取csu时间
        ${A接口对比时间}    A接口对比时间    ${A接口获取数据}    ${命令3}    A_设置系统时间_1
        should be true    ${A接口对比时间}
        ${A接口对比时间}    A接口对比时间    ${A接口获取数据}    ${当前CSU时间}    A_设置系统时间_1
        should be true    ${A接口对比时间}
    END
    ${设置时间}    A接口生成时间
    ${协议平台设置北向数据}    协议平台设置北向数据    设置时间1_40H_4EH    设置时间=${设置时间}
    FOR    ${命令1}    ${命令2}    ${命令3}    IN ZIP    ${设置命令}    ${命令名称}    ${无效时间}
        ${协议平台设置北向数据}    ${信息}    协议平台设置北向数据    ${命令1}    设置时间=${命令3}
        should not be true    ${协议平台设置北向数据}
        ${返回值}    ${A接口获取数据}    协议平台获取北向数据    ${命令2}
        ${A接口对比时间}    A接口对比时间    ${A接口获取数据}    ${设置时间}    A_设置系统时间_1
        should be true    ${A接口对比时间}
    END
    ${返回值}    ${A接口获取数据}    协议平台获取北向数据    获取时间_40H_4DH
    should be true    ${返回值}
    ${A接口对比时间}    A接口对比时间    ${A接口获取数据}    ${设置时间}    A_设置系统时间_1    20

获取通信协议版本号
    ${命令名称}    set variable    获取协议版本_40H_4FH    获取协议版本_41H_4FH    获取协议版本_42H_4FH
    FOR    ${val}    IN    @{命令名称}
        ${协议版本}    A接口获取应答帧信息    ${val}    VER
        should be equal as strings    ${协议版本}    12
    END

获取设备地址信息
    ${命令名称}    set variable    获取设备地址_40H_50H    获取设备地址_41H_50H    获取设备地址_42H_50H
    FOR    ${val}    IN    @{命令名称}
        ${设备地址}    A接口获取应答帧信息    ${val}    ADDR
        should be equal as strings    ${设备地址}    01
    END

获取设备厂家信息
    ${命令名称}    set variable    获取厂家信息_40H_51H    获取厂家信息_41H_51H    获取厂家信息_42H_51H
    FOR    ${val}    IN    @{命令名称}
        ${返回值}    ${A接口获取数据}    协议平台获取北向数据    ${val}
        should be true    ${返回值}
        ${web获取数据}    A接口数据获取web数据    ${A接口获取数据}
        ${对比结果}    批量对比数据_A接口_WEB    ${A接口获取数据}    ${web获取数据}    设备厂家信息_1    0.2
        should be true    ${对比结果}
    END

获取防盗系统随机码
    ${命令名称}    set variable    获取系统防盗随机码_40H_E2H    获取系统防盗随机码_41H_E2H    获取系统防盗随机码_42H_E2H
    FOR    ${val}    IN    @{命令名称}
        ${返回值}    ${A接口获取数据}    协议平台获取北向数据    ${val}
        should not be true    ${返回值}
        should be equal as strings    ${A接口获取数据}    无数据
    END
