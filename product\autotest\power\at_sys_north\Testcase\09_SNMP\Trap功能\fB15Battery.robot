*** Settings ***
Suite Setup       FB100B3铁锂电池测试前置条件
Suite Teardown    FB100B3铁锂电池测试结束条件
Default Tags      3
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
snmp_0002_电池充电过流保护告警测试
    [Setup]
    连接CSU
    Comment    关闭交流源输出
    显示属性配置    电池充电过流保护状态    数字量    web_attr=On    gui_attr=On
    #子设备工具模拟
    设置子工具值    smartli    all    只读    电池充电过流保护    1
    sleep    6m
    ${获取值}    获取web实时数据    电池充电过流保护状态-1
    ${获取值8}    获取web实时数据    电池充电过流保护状态-8
    ${获取值16}    获取web实时数据    电池充电过流保护状态-16
    should be true    '${获取值}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值16}'=='异常'
    ${级别设置值}    获取web参数量    电池充电过流保护
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池充电过流保护    严重
    wait until keyword succeeds    15m    5    查询指定告警信息    电池充电过流保护
    设置子工具值    smartli    all    只读    电池充电过流保护    0
    sleep    6m
    ${获取值}    获取web实时数据    电池充电过流保护状态-1
    ${获取值8}    获取web实时数据    电池充电过流保护状态-8
    ${获取值16}    获取web实时数据    电池充电过流保护状态-16
    should be true    '${获取值}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值16}'=='正常'
    wait until keyword succeeds    15m    5    查询指定告警信息不为    电池充电过流保护
    显示属性配置    电池充电过流保护状态    数字量    web_attr=Off    gui_attr=Off

snmp_0004_电池放电过流保护告警测试
    [Setup]
    连接CSU
    显示属性配置    电池放电过流保护状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    电池放电过流保护    1
    sleep    6m
    ${获取值}    获取web实时数据    电池放电过流保护状态-1
    ${获取值8}    获取web实时数据    电池放电过流保护状态-8
    ${获取值16}    获取web实时数据    电池放电过流保护状态-16
    should be true    '${获取值}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值16}'=='异常'
    ${级别设置值}    获取web参数量    电池放电过流保护
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池放电过流保护    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    电池放电过流保护
    设置子工具值    smartli    all    只读    电池放电过流保护    0
    sleep    6m
    ${获取值}    获取web实时数据    电池放电过流保护状态-1
    ${获取值8}    获取web实时数据    电池放电过流保护状态-8
    ${获取值16}    获取web实时数据    电池放电过流保护状态-16
    should be true    '${获取值}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值16}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    电池放电过流保护
    显示属性配置    电池充电过流保护状态    数字量    web_attr=Off    gui_attr=Off

snmp_0006_电池过压保护告警测试
    [Setup]
    连接CSU
    显示属性配置    电池过压保护状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    电池过压保护    1
    sleep    6m
    ${获取值}    获取web实时数据    电池过压保护状态-1
    ${获取值8}    获取web实时数据    电池过压保护状态-8
    ${获取值16}    获取web实时数据    电池过压保护状态-16
    should be true    '${获取值}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值16}'=='异常'
    ${级别设置值}    获取web参数量    电池过压保护
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池过压保护    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    电池过压保护
    设置子工具值    smartli    all    只读    电池过压保护    0
    sleep    6m
    ${获取值}    获取web实时数据    电池过压保护状态-1
    ${获取值8}    获取web实时数据    电池过压保护状态-8
    ${获取值16}    获取web实时数据    电池过压保护状态-16
    should be true    '${获取值}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值16}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    电池过压保护
    显示属性配置    电池过压保护状态    数字量    web_attr=Off    gui_attr=Off

snmp_0008_单板过温保护告警测试
    [Setup]
    连接CSU
    显示属性配置    单板过温保护状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    单板过温保护    1
    sleep    6m
    ${获取值}    获取web实时数据    单板过温保护状态-1
    ${获取值8}    获取web实时数据    单板过温保护状态-8
    ${获取值16}    获取web实时数据    单板过温保护状态-16
    should be true    '${获取值}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值16}'=='异常'
    ${级别设置值}    获取web参数量    单板过温保护
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    单板过温保护    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    单板过温保护
    设置子工具值    smartli    all    只读    单板过温保护    0
    sleep    6m
    ${获取值}    获取web实时数据    单板过温保护状态-1
    ${获取值8}    获取web实时数据    单板过温保护状态-8
    ${获取值16}    获取web实时数据    单板过温保护状态-16
    should be true    '${获取值}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值16}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    单板过温保护
    显示属性配置    单板过温保护状态    数字量    web_attr=Off    gui_attr=Off

snmp_0010_电池充电过流告警测试
    [Setup]
    连接CSU
    显示属性配置    电池充电过流告警状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    电池充电过流告警    1
    sleep    8m
    ${获取值}    获取web实时数据    电池充电过流告警状态-1
    ${获取值8}    获取web实时数据    电池充电过流告警状态-8
    ${获取值16}    获取web实时数据    电池充电过流告警状态-16
    should be true    '${获取值}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值16}'=='异常'
    ${级别设置值}    获取web参数量    电池充电过流告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池充电过流告警    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    电池充电过流告警
    设置子工具值    smartli    all    只读    电池充电过流告警    0
    sleep    8m
    ${获取值}    获取web实时数据    电池充电过流告警状态-1
    ${获取值8}    获取web实时数据    电池充电过流告警状态-8
    ${获取值16}    获取web实时数据    电池充电过流告警状态-16
    should be true    '${获取值}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值16}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    电池充电过流告警
    显示属性配置    电池充电过流告警状态    数字量    web_attr=Off    gui_attr=Off

snmp_0012_电池放电过流告警测试
    [Setup]
    连接CSU
    显示属性配置    电池放电过流告警状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    电池放电过流告警    1
    sleep    6m
    ${获取值}    获取web实时数据    电池放电过流告警状态-1
    ${获取值8}    获取web实时数据    电池放电过流告警状态-8
    ${获取值16}    获取web实时数据    电池放电过流告警状态-16
    should be true    '${获取值}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值16}'=='异常'
    ${级别设置值}    获取web参数量    电池放电过流告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池放电过流告警    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    电池放电过流告警
    设置子工具值    smartli    all    只读    电池放电过流告警    0
    sleep    6m
    ${获取值}    获取web实时数据    电池放电过流告警状态-1
    ${获取值8}    获取web实时数据    电池放电过流告警状态-8
    ${获取值16}    获取web实时数据    电池放电过流告警状态-16
    should be true    '${获取值}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值16}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    电池放电过流告警
    显示属性配置    电池放电过流告警状态    数字量    web_attr=Off    gui_attr=Off

snmp_0014_电池过压告警测试
    [Setup]
    连接CSU
    显示属性配置    电池过压告警状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    电池过压告警    1
    sleep    6m
    ${获取值}    获取web实时数据    电池过压告警状态-1
    ${获取值8}    获取web实时数据    电池过压告警状态-8
    ${获取值16}    获取web实时数据    电池过压告警状态-16
    should be true    '${获取值}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值16}'=='异常'
    ${级别设置值}    获取web参数量    电池过压告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池过压告警    严重
    wait until keyword succeeds    10m    2    查询指定告警信息    电池过压告警
    设置子工具值    smartli    all    只读    电池过压告警    0
    sleep    6m
    ${获取值}    获取web实时数据    电池过压告警状态-1
    ${获取值8}    获取web实时数据    电池过压告警状态-8
    ${获取值16}    获取web实时数据    电池过压告警状态-16
    should be true    '${获取值}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值16}'=='正常'
    wait until keyword succeeds    10m    2    查询指定告警信息不为    电池过压告警
    显示属性配置    电池过压告警状态    数字量    web_attr=Off    gui_attr=Off

snmp_0016_机内环境温度高告警测试
    [Setup]
    连接CSU
    显示属性配置    机内环境温度高告警状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    机内环境温度高告警    1
    sleep    6m
    ${获取值}    获取web实时数据    机内环境温度高告警状态-1
    ${获取值8}    获取web实时数据    机内环境温度高告警状态-8
    ${获取值16}    获取web实时数据    机内环境温度高告警状态-16
    should be true    '${获取值}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值16}'=='异常'
    ${级别设置值}    获取web参数量    机内环境温度高告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    机内环境温度高告警    严重
    wait until keyword succeeds    10m    2    查询指定告警信息    机内环境温度高告警
    设置子工具值    smartli    all    只读    机内环境温度高告警    0
    sleep    6m
    ${获取值}    获取web实时数据    机内环境温度高告警状态-1
    ${获取值8}    获取web实时数据    机内环境温度高告警状态-8
    ${获取值16}    获取web实时数据    机内环境温度高告警状态-16
    should be true    '${获取值}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值16}'=='正常'
    wait until keyword succeeds    10m    2    查询指定告警信息不为    机内环境温度高告警
    显示属性配置    机内环境温度高告警状态    数字量    web_attr=Off    gui_attr=Off

snmp_0018_机内环境温度低告警测试
    [Setup]
    连接CSU
    显示属性配置    机内环境温度低告警状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    机内环境温度低告警    1
    sleep    6m
    ${获取值}    获取web实时数据    机内环境温度低告警状态-1
    ${获取值8}    获取web实时数据    机内环境温度低告警状态-8
    ${获取值16}    获取web实时数据    机内环境温度低告警状态-16
    should be true    '${获取值}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值16}'=='异常'
    ${级别设置值}    获取web参数量    机内环境温度低告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    机内环境温度低告警    严重
    wait until keyword succeeds    10m    2    查询指定告警信息    机内环境温度低告警
    设置子工具值    smartli    all    只读    机内环境温度低告警    0
    sleep    6m
    ${获取值}    获取web实时数据    机内环境温度低告警状态-1
    ${获取值8}    获取web实时数据    机内环境温度低告警状态-8
    ${获取值16}    获取web实时数据    机内环境温度低告警状态-16
    should be true    '${获取值}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值16}'=='正常'
    wait until keyword succeeds    10m    2    查询指定告警信息不为    机内环境温度低告警
    显示属性配置    机内环境温度低告警状态    数字量    web_attr=Off    gui_attr=Off

snmp_0020_电池欠压告警测试
    [Setup]
    连接CSU
    显示属性配置    电池欠压告警状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    电池欠压告警    1
    sleep    6m
    ${获取值}    获取web实时数据    电池欠压告警状态-1
    ${获取值8}    获取web实时数据    电池欠压告警状态-8
    ${获取值16}    获取web实时数据    电池欠压告警状态-16
    should be true    '${获取值}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值16}'=='异常'
    ${级别设置值}    获取web参数量    电池欠压告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池欠压告警    严重
    wait until keyword succeeds    10m    2    查询指定告警信息    电池欠压告警
    设置子工具值    smartli    all    只读    电池欠压告警    0
    sleep    6m
    ${获取值}    获取web实时数据    电池欠压告警状态-1
    ${获取值8}    获取web实时数据    电池欠压告警状态-8
    ${获取值16}    获取web实时数据    电池欠压告警状态-16
    should be true    '${获取值}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值16}'=='正常'
    wait until keyword succeeds    10m    2    查询指定告警信息不为    电池欠压告警
    显示属性配置    电池欠压告警状态    数字量    web_attr=Off    gui_attr=Off

snmp_0022_电池欠压保护告警测试
    [Setup]
    连接CSU
    显示属性配置    电池欠压保护状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    电池欠压保护    1
    sleep    6m
    ${获取值}    获取web实时数据    电池欠压保护状态-1
    ${获取值8}    获取web实时数据    电池欠压保护状态-8
    ${获取值16}    获取web实时数据    电池欠压保护状态-16
    should be true    '${获取值}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值16}'=='异常'
    ${级别设置值}    获取web参数量    电池欠压保护
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池欠压保护    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    电池欠压保护
    设置子工具值    smartli    all    只读    电池欠压保护    0
    sleep    6m
    ${获取值}    获取web实时数据    电池欠压保护状态-1
    ${获取值8}    获取web实时数据    电池欠压保护状态-8
    ${获取值16}    获取web实时数据    电池欠压保护状态-16
    should be true    '${获取值}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值16}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    电池欠压保护
    显示属性配置    电池欠压保护状态    数字量    web_attr=Off    gui_attr=Off

snmp_0024_单体过压告警测试
    [Setup]
    连接CSU
    显示属性配置    单体过压告警状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    单体过压告警1    1
    设置子工具值    smartli    all    只读    单体过压告警2    1
    设置子工具值    smartli    all    只读    单体过压告警3    1
    设置子工具值    smartli    all    只读    单体过压告警4    1
    设置子工具值    smartli    all    只读    单体过压告警5    1
    设置子工具值    smartli    all    只读    单体过压告警6    1
    设置子工具值    smartli    all    只读    单体过压告警7    1
    设置子工具值    smartli    all    只读    单体过压告警8    1
    设置子工具值    smartli    all    只读    单体过压告警9    1
    设置子工具值    smartli    all    只读    单体过压告警10    1
    设置子工具值    smartli    all    只读    单体过压告警11    1
    设置子工具值    smartli    all    只读    单体过压告警12    1
    设置子工具值    smartli    all    只读    单体过压告警13    1
    设置子工具值    smartli    all    只读    单体过压告警14    1
    设置子工具值    smartli    all    只读    单体过压告警15    1
    sleep    8m
    ${获取值1}    获取web实时数据    单体过压告警状态_1-1
    ${获取值2}    获取web实时数据    单体过压告警状态_8-1
    ${获取值3}    获取web实时数据    单体过压告警状态_15-1
    ${获取值4}    获取web实时数据    单体过压告警状态_1-8
    ${获取值5}    获取web实时数据    单体过压告警状态_8-8
    ${获取值6}    获取web实时数据    单体过压告警状态_15-8
    ${获取值7}    获取web实时数据    单体过压告警状态_1-16
    ${获取值8}    获取web实时数据    单体过压告警状态_8-16
    ${获取值9}    获取web实时数据    单体过压告警状态_15-16
    should be true    '${获取值1}'=='异常'
    should be true    '${获取值2}'=='异常'
    should be true    '${获取值3}'=='异常'
    should be true    '${获取值4}'=='异常'
    should be true    '${获取值5}'=='异常'
    should be true    '${获取值6}'=='异常'
    should be true    '${获取值7}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值9}'=='异常'
    ${级别设置值}    获取web参数量    单体过压告警_1-1
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    单体过压告警_1-1    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    单体过压告警
    设置子工具值    smartli    all    只读    单体过压告警1    0
    设置子工具值    smartli    all    只读    单体过压告警2    0
    设置子工具值    smartli    all    只读    单体过压告警3    0
    设置子工具值    smartli    all    只读    单体过压告警4    0
    设置子工具值    smartli    all    只读    单体过压告警5    0
    设置子工具值    smartli    all    只读    单体过压告警6    0
    设置子工具值    smartli    all    只读    单体过压告警7    0
    设置子工具值    smartli    all    只读    单体过压告警8    0
    设置子工具值    smartli    all    只读    单体过压告警9    0
    设置子工具值    smartli    all    只读    单体过压告警10    0
    设置子工具值    smartli    all    只读    单体过压告警11    0
    设置子工具值    smartli    all    只读    单体过压告警12    0
    设置子工具值    smartli    all    只读    单体过压告警13    0
    设置子工具值    smartli    all    只读    单体过压告警14    0
    设置子工具值    smartli    all    只读    单体过压告警15    0
    sleep    8m
    ${获取值1}    获取web实时数据    单体过压告警状态_1-1
    ${获取值2}    获取web实时数据    单体过压告警状态_8-1
    ${获取值3}    获取web实时数据    单体过压告警状态_15-1
    ${获取值4}    获取web实时数据    单体过压告警状态_1-8
    ${获取值5}    获取web实时数据    单体过压告警状态_8-8
    ${获取值6}    获取web实时数据    单体过压告警状态_15-8
    ${获取值7}    获取web实时数据    单体过压告警状态_1-16
    ${获取值8}    获取web实时数据    单体过压告警状态_8-16
    ${获取值9}    获取web实时数据    单体过压告警状态_15-16
    should be true    '${获取值1}'=='正常'
    should be true    '${获取值2}'=='正常'
    should be true    '${获取值3}'=='正常'
    should be true    '${获取值4}'=='正常'
    should be true    '${获取值5}'=='正常'
    should be true    '${获取值6}'=='正常'
    should be true    '${获取值7}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值9}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    单体过压告警
    显示属性配置    单体过压告警状态    数字量    web_attr=Off    gui_attr=Off

snmp_0026_单体过压保护告警测试
    [Setup]
    连接CSU
    显示属性配置    单体过压保护状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    单体过压保护1    1
    设置子工具值    smartli    all    只读    单体过压保护2    1
    设置子工具值    smartli    all    只读    单体过压保护3    1
    设置子工具值    smartli    all    只读    单体过压保护4    1
    设置子工具值    smartli    all    只读    单体过压保护5    1
    设置子工具值    smartli    all    只读    单体过压保护6    1
    设置子工具值    smartli    all    只读    单体过压保护7    1
    设置子工具值    smartli    all    只读    单体过压保护8    1
    设置子工具值    smartli    all    只读    单体过压保护9    1
    设置子工具值    smartli    all    只读    单体过压保护10    1
    设置子工具值    smartli    all    只读    单体过压保护11    1
    设置子工具值    smartli    all    只读    单体过压保护12    1
    设置子工具值    smartli    all    只读    单体过压保护13    1
    设置子工具值    smartli    all    只读    单体过压保护14    1
    设置子工具值    smartli    all    只读    单体过压保护15    1
    sleep    6m
    ${获取值1}    获取web实时数据    单体过压保护状态_1-1
    ${获取值2}    获取web实时数据    单体过压保护状态_8-1
    ${获取值3}    获取web实时数据    单体过压保护状态_15-1
    ${获取值4}    获取web实时数据    单体过压保护状态_1-8
    ${获取值5}    获取web实时数据    单体过压保护状态_8-8
    ${获取值6}    获取web实时数据    单体过压保护状态_15-8
    ${获取值7}    获取web实时数据    单体过压保护状态_1-16
    ${获取值8}    获取web实时数据    单体过压保护状态_8-16
    ${获取值9}    获取web实时数据    单体过压保护状态_15-16
    should be true    '${获取值1}'=='异常'
    should be true    '${获取值2}'=='异常'
    should be true    '${获取值3}'=='异常'
    should be true    '${获取值4}'=='异常'
    should be true    '${获取值5}'=='异常'
    should be true    '${获取值6}'=='异常'
    should be true    '${获取值7}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值9}'=='异常'
    ${级别设置值}    获取web参数量    单体过压保护_1-1
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    单体过压保护_1-1    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    单体过压保护
    设置子工具值    smartli    all    只读    单体过压保护1    0
    设置子工具值    smartli    all    只读    单体过压保护2    0
    设置子工具值    smartli    all    只读    单体过压保护3    0
    设置子工具值    smartli    all    只读    单体过压保护4    0
    设置子工具值    smartli    all    只读    单体过压保护5    0
    设置子工具值    smartli    all    只读    单体过压保护6    0
    设置子工具值    smartli    all    只读    单体过压保护7    0
    设置子工具值    smartli    all    只读    单体过压保护8    0
    设置子工具值    smartli    all    只读    单体过压保护9    0
    设置子工具值    smartli    all    只读    单体过压保护10    0
    设置子工具值    smartli    all    只读    单体过压保护11    0
    设置子工具值    smartli    all    只读    单体过压保护12    0
    设置子工具值    smartli    all    只读    单体过压保护13    0
    设置子工具值    smartli    all    只读    单体过压保护14    0
    设置子工具值    smartli    all    只读    单体过压保护15    0
    sleep    6m
    ${获取值1}    获取web实时数据    单体过压保护状态_1-1
    ${获取值2}    获取web实时数据    单体过压保护状态_8-1
    ${获取值3}    获取web实时数据    单体过压保护状态_15-1
    ${获取值4}    获取web实时数据    单体过压保护状态_1-8
    ${获取值5}    获取web实时数据    单体过压保护状态_8-8
    ${获取值6}    获取web实时数据    单体过压保护状态_15-8
    ${获取值7}    获取web实时数据    单体过压保护状态_1-16
    ${获取值8}    获取web实时数据    单体过压保护状态_8-16
    ${获取值9}    获取web实时数据    单体过压保护状态_15-16
    should be true    '${获取值1}'=='正常'
    should be true    '${获取值2}'=='正常'
    should be true    '${获取值3}'=='正常'
    should be true    '${获取值4}'=='正常'
    should be true    '${获取值5}'=='正常'
    should be true    '${获取值6}'=='正常'
    should be true    '${获取值7}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值9}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    单体过压保护
    显示属性配置    单体过压保护状态    数字量    web_attr=Off    gui_attr=Off

snmp_0028_单体欠压告警测试
    [Setup]
    连接CSU
    显示属性配置    单体欠压告警状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    单体欠压告警1    1
    设置子工具值    smartli    all    只读    单体欠压告警2    1
    设置子工具值    smartli    all    只读    单体欠压告警3    1
    设置子工具值    smartli    all    只读    单体欠压告警4    1
    设置子工具值    smartli    all    只读    单体欠压告警5    1
    设置子工具值    smartli    all    只读    单体欠压告警6    1
    设置子工具值    smartli    all    只读    单体欠压告警7    1
    设置子工具值    smartli    all    只读    单体欠压告警8    1
    设置子工具值    smartli    all    只读    单体欠压告警9    1
    设置子工具值    smartli    all    只读    单体欠压告警10    1
    设置子工具值    smartli    all    只读    单体欠压告警11    1
    设置子工具值    smartli    all    只读    单体欠压告警12    1
    设置子工具值    smartli    all    只读    单体欠压告警13    1
    设置子工具值    smartli    all    只读    单体欠压告警14    1
    设置子工具值    smartli    all    只读    单体欠压告警15    1
    sleep    14m
    ${获取值1}    获取web实时数据    单体欠压告警状态_1-1
    ${获取值2}    获取web实时数据    单体欠压告警状态_8-1
    ${获取值3}    获取web实时数据    单体欠压告警状态_15-1
    ${获取值4}    获取web实时数据    单体欠压告警状态_1-8
    ${获取值5}    获取web实时数据    单体欠压告警状态_8-8
    ${获取值6}    获取web实时数据    单体欠压告警状态_15-8
    ${获取值7}    获取web实时数据    单体欠压告警状态_1-16
    ${获取值8}    获取web实时数据    单体欠压告警状态_8-16
    ${获取值9}    获取web实时数据    单体欠压告警状态_15-16
    should be true    '${获取值1}'=='异常'
    should be true    '${获取值2}'=='异常'
    should be true    '${获取值3}'=='异常'
    should be true    '${获取值4}'=='异常'
    should be true    '${获取值5}'=='异常'
    should be true    '${获取值6}'=='异常'
    should be true    '${获取值7}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值9}'=='异常'
    ${级别设置值}    获取web参数量    单体欠压告警_1-1
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    单体欠压告警_1-1    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    单体欠压告警
    设置子工具值    smartli    all    只读    单体欠压告警1    0
    设置子工具值    smartli    all    只读    单体欠压告警2    0
    设置子工具值    smartli    all    只读    单体欠压告警3    0
    设置子工具值    smartli    all    只读    单体欠压告警4    0
    设置子工具值    smartli    all    只读    单体欠压告警5    0
    设置子工具值    smartli    all    只读    单体欠压告警6    0
    设置子工具值    smartli    all    只读    单体欠压告警7    0
    设置子工具值    smartli    all    只读    单体欠压告警8    0
    设置子工具值    smartli    all    只读    单体欠压告警9    0
    设置子工具值    smartli    all    只读    单体欠压告警10    0
    设置子工具值    smartli    all    只读    单体欠压告警11    0
    设置子工具值    smartli    all    只读    单体欠压告警12    0
    设置子工具值    smartli    all    只读    单体欠压告警13    0
    设置子工具值    smartli    all    只读    单体欠压告警14    0
    设置子工具值    smartli    all    只读    单体欠压告警15    0
    sleep    14m
    ${获取值1}    获取web实时数据    单体欠压告警状态_1-1
    ${获取值2}    获取web实时数据    单体欠压告警状态_8-1
    ${获取值3}    获取web实时数据    单体欠压告警状态_15-1
    ${获取值4}    获取web实时数据    单体欠压告警状态_1-8
    ${获取值5}    获取web实时数据    单体欠压告警状态_8-8
    ${获取值6}    获取web实时数据    单体欠压告警状态_15-8
    ${获取值7}    获取web实时数据    单体欠压告警状态_1-16
    ${获取值8}    获取web实时数据    单体欠压告警状态_8-16
    ${获取值9}    获取web实时数据    单体欠压告警状态_15-16
    should be true    '${获取值1}'=='正常'
    should be true    '${获取值2}'=='正常'
    should be true    '${获取值3}'=='正常'
    should be true    '${获取值4}'=='正常'
    should be true    '${获取值5}'=='正常'
    should be true    '${获取值6}'=='正常'
    should be true    '${获取值7}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值9}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    单体欠压告警
    显示属性配置    单体欠压告警状态    数字量    web_attr=Off    gui_attr=Off

snmp_0030_单体欠压保护告警测试
    [Setup]
    连接CSU
    显示属性配置    单体欠压保护状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    单体欠压保护1    1
    设置子工具值    smartli    all    只读    单体欠压保护2    1
    设置子工具值    smartli    all    只读    单体欠压保护3    1
    设置子工具值    smartli    all    只读    单体欠压保护4    1
    设置子工具值    smartli    all    只读    单体欠压保护5    1
    设置子工具值    smartli    all    只读    单体欠压保护6    1
    设置子工具值    smartli    all    只读    单体欠压保护7    1
    设置子工具值    smartli    all    只读    单体欠压保护8    1
    设置子工具值    smartli    all    只读    单体欠压保护9    1
    设置子工具值    smartli    all    只读    单体欠压保护10    1
    设置子工具值    smartli    all    只读    单体欠压保护11    1
    设置子工具值    smartli    all    只读    单体欠压保护12    1
    设置子工具值    smartli    all    只读    单体欠压保护13    1
    设置子工具值    smartli    all    只读    单体欠压保护14    1
    设置子工具值    smartli    all    只读    单体欠压保护15    1
    sleep    6m
    ${获取值1}    获取web实时数据    单体欠压保护状态_1-1
    ${获取值2}    获取web实时数据    单体欠压保护状态_8-1
    ${获取值3}    获取web实时数据    单体欠压保护状态_15-1
    ${获取值4}    获取web实时数据    单体欠压保护状态_1-8
    ${获取值5}    获取web实时数据    单体欠压保护状态_8-8
    ${获取值6}    获取web实时数据    单体欠压保护状态_15-8
    ${获取值7}    获取web实时数据    单体欠压保护状态_1-16
    ${获取值8}    获取web实时数据    单体欠压保护状态_8-16
    ${获取值9}    获取web实时数据    单体欠压保护状态_15-16
    should be true    '${获取值1}'=='异常'
    should be true    '${获取值2}'=='异常'
    should be true    '${获取值3}'=='异常'
    should be true    '${获取值4}'=='异常'
    should be true    '${获取值5}'=='异常'
    should be true    '${获取值6}'=='异常'
    should be true    '${获取值7}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值9}'=='异常'
    ${级别设置值}    获取web参数量    单体欠压保护_1-1
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    单体欠压保护_1-1    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    单体欠压保护
    设置子工具值    smartli    all    只读    单体欠压保护1    0
    设置子工具值    smartli    all    只读    单体欠压保护2    0
    设置子工具值    smartli    all    只读    单体欠压保护3    0
    设置子工具值    smartli    all    只读    单体欠压保护4    0
    设置子工具值    smartli    all    只读    单体欠压保护5    0
    设置子工具值    smartli    all    只读    单体欠压保护6    0
    设置子工具值    smartli    all    只读    单体欠压保护7    0
    设置子工具值    smartli    all    只读    单体欠压保护8    0
    设置子工具值    smartli    all    只读    单体欠压保护9    0
    设置子工具值    smartli    all    只读    单体欠压保护10    0
    设置子工具值    smartli    all    只读    单体欠压保护11    0
    设置子工具值    smartli    all    只读    单体欠压保护12    0
    设置子工具值    smartli    all    只读    单体欠压保护13    0
    设置子工具值    smartli    all    只读    单体欠压保护14    0
    设置子工具值    smartli    all    只读    单体欠压保护15    0
    sleep    6m
    ${获取值1}    获取web实时数据    单体欠压保护状态_1-1
    ${获取值2}    获取web实时数据    单体欠压保护状态_8-1
    ${获取值3}    获取web实时数据    单体欠压保护状态_15-1
    ${获取值4}    获取web实时数据    单体欠压保护状态_1-8
    ${获取值5}    获取web实时数据    单体欠压保护状态_8-8
    ${获取值6}    获取web实时数据    单体欠压保护状态_15-8
    ${获取值7}    获取web实时数据    单体欠压保护状态_1-16
    ${获取值8}    获取web实时数据    单体欠压保护状态_8-16
    ${获取值9}    获取web实时数据    单体欠压保护状态_15-16
    should be true    '${获取值1}'=='正常'
    should be true    '${获取值2}'=='正常'
    should be true    '${获取值3}'=='正常'
    should be true    '${获取值4}'=='正常'
    should be true    '${获取值5}'=='正常'
    should be true    '${获取值6}'=='正常'
    should be true    '${获取值7}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值9}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    单体欠压保护
    显示属性配置    单体欠压保护状态    数字量    web_attr=Off    gui_attr=Off

snmp_0032_单体充电高温告警测试
    [Setup]
    连接CSU
    显示属性配置    单体充电高温告警状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    单体充电高温告警1    1
    设置子工具值    smartli    all    只读    单体充电高温告警2    1
    设置子工具值    smartli    all    只读    单体充电高温告警3    1
    设置子工具值    smartli    all    只读    单体充电高温告警4    1
    sleep    6m
    ${获取值1}    获取web实时数据    单体充电高温告警状态_1-1
    ${获取值2}    获取web实时数据    单体充电高温告警状态_4-1
    ${获取值3}    获取web实时数据    单体充电高温告警状态_1-8
    ${获取值4}    获取web实时数据    单体充电高温告警状态_4-8
    ${获取值5}    获取web实时数据    单体充电高温告警状态_1-16
    ${获取值6}    获取web实时数据    单体充电高温告警状态_4-16
    should be true    '${获取值1}'=='异常'
    should be true    '${获取值2}'=='异常'
    should be true    '${获取值3}'=='异常'
    should be true    '${获取值4}'=='异常'
    should be true    '${获取值5}'=='异常'
    should be true    '${获取值6}'=='异常'
    ${级别设置值}    获取web参数量    单体充电高温告警_1-1
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    单体充电高温告警_1-1    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    单体充电高温告警
    设置子工具值    smartli    all    只读    单体充电高温告警1    0
    设置子工具值    smartli    all    只读    单体充电高温告警2    0
    设置子工具值    smartli    all    只读    单体充电高温告警3    0
    设置子工具值    smartli    all    只读    单体充电高温告警4    0
    sleep    6m
    ${获取值1}    获取web实时数据    单体充电高温告警状态_1-1
    ${获取值2}    获取web实时数据    单体充电高温告警状态_4-1
    ${获取值3}    获取web实时数据    单体充电高温告警状态_1-8
    ${获取值4}    获取web实时数据    单体充电高温告警状态_4-8
    ${获取值5}    获取web实时数据    单体充电高温告警状态_1-16
    ${获取值6}    获取web实时数据    单体充电高温告警状态_4-16
    should be true    '${获取值1}'=='正常'
    should be true    '${获取值2}'=='正常'
    should be true    '${获取值3}'=='正常'
    should be true    '${获取值4}'=='正常'
    should be true    '${获取值5}'=='正常'
    should be true    '${获取值6}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    单体充电高温告警
    显示属性配置    单体充电高温告警状态    数字量    web_attr=Off    gui_attr=Off

snmp_0034_单体充电高温保护告警测试
    [Setup]
    连接CSU
    显示属性配置    单体充电高温保护状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    单体充电高温保护1    1
    设置子工具值    smartli    all    只读    单体充电高温保护2    1
    设置子工具值    smartli    all    只读    单体充电高温保护3    1
    设置子工具值    smartli    all    只读    单体充电高温保护4    1
    sleep    6m
    ${获取值1}    获取web实时数据    单体充电高温保护状态_1-1
    ${获取值2}    获取web实时数据    单体充电高温保护状态_4-1
    ${获取值3}    获取web实时数据    单体充电高温保护状态_1-8
    ${获取值4}    获取web实时数据    单体充电高温保护状态_4-8
    ${获取值5}    获取web实时数据    单体充电高温保护状态_1-16
    ${获取值6}    获取web实时数据    单体充电高温保护状态_4-16
    should be true    '${获取值1}'=='异常'
    should be true    '${获取值2}'=='异常'
    should be true    '${获取值3}'=='异常'
    should be true    '${获取值4}'=='异常'
    should be true    '${获取值5}'=='异常'
    should be true    '${获取值6}'=='异常'
    ${级别设置值}    获取web参数量    单体充电高温保护告警_1-1
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    单体充电高温保护告警_1-1    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    单体充电高温保护告警
    设置子工具值    smartli    all    只读    单体充电高温保护1    0
    设置子工具值    smartli    all    只读    单体充电高温保护2    0
    设置子工具值    smartli    all    只读    单体充电高温保护3    0
    设置子工具值    smartli    all    只读    单体充电高温保护4    0
    sleep    6m
    ${获取值1}    获取web实时数据    单体充电高温保护状态_1-1
    ${获取值2}    获取web实时数据    单体充电高温保护状态_4-1
    ${获取值3}    获取web实时数据    单体充电高温保护状态_1-8
    ${获取值4}    获取web实时数据    单体充电高温保护状态_4-8
    ${获取值5}    获取web实时数据    单体充电高温保护状态_1-16
    ${获取值6}    获取web实时数据    单体充电高温保护状态_4-16
    should be true    '${获取值1}'=='正常'
    should be true    '${获取值2}'=='正常'
    should be true    '${获取值3}'=='正常'
    should be true    '${获取值4}'=='正常'
    should be true    '${获取值5}'=='正常'
    should be true    '${获取值6}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    单体充电高温保护告警
    显示属性配置    单体充电高温保护状态    数字量    web_attr=Off    gui_attr=Off

snmp_0036_单体放电高温告警测试
    [Setup]
    连接CSU
    显示属性配置    单体放电高温告警状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    单体放电高温告警1    1
    设置子工具值    smartli    all    只读    单体放电高温告警2    1
    设置子工具值    smartli    all    只读    单体放电高温告警3    1
    设置子工具值    smartli    all    只读    单体放电高温告警4    1
    sleep    6m
    ${获取值1}    获取web实时数据    单体放电高温告警状态_1-1
    ${获取值2}    获取web实时数据    单体放电高温告警状态_4-1
    ${获取值3}    获取web实时数据    单体放电高温告警状态_1-8
    ${获取值4}    获取web实时数据    单体放电高温告警状态_4-8
    ${获取值5}    获取web实时数据    单体放电高温告警状态_1-16
    ${获取值6}    获取web实时数据    单体放电高温告警状态_4-16
    should be true    '${获取值1}'=='异常'
    should be true    '${获取值2}'=='异常'
    should be true    '${获取值3}'=='异常'
    should be true    '${获取值4}'=='异常'
    should be true    '${获取值5}'=='异常'
    should be true    '${获取值6}'=='异常'
    ${级别设置值}    获取web参数量    单体放电高温告警_1-1
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    单体放电高温告警_1-1    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    单体放电高温告警
    设置子工具值    smartli    all    只读    单体放电高温告警1    0
    设置子工具值    smartli    all    只读    单体放电高温告警2    0
    设置子工具值    smartli    all    只读    单体放电高温告警3    0
    设置子工具值    smartli    all    只读    单体放电高温告警4    0
    sleep    6m
    ${获取值1}    获取web实时数据    单体放电高温告警状态_1-1
    ${获取值2}    获取web实时数据    单体放电高温告警状态_4-1
    ${获取值3}    获取web实时数据    单体放电高温告警状态_1-8
    ${获取值4}    获取web实时数据    单体放电高温告警状态_4-8
    ${获取值5}    获取web实时数据    单体放电高温告警状态_1-16
    ${获取值6}    获取web实时数据    单体放电高温告警状态_4-16
    should be true    '${获取值1}'=='正常'
    should be true    '${获取值2}'=='正常'
    should be true    '${获取值3}'=='正常'
    should be true    '${获取值4}'=='正常'
    should be true    '${获取值5}'=='正常'
    should be true    '${获取值6}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    单体放电高温告警
    显示属性配置    单体放电高温告警状态    数字量    web_attr=Off    gui_attr=Off

snmp_0038_单体放电高温保护告警测试
    [Setup]
    连接CSU
    显示属性配置    单体放电高温保护状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    单体放电高温保护1    1
    设置子工具值    smartli    all    只读    单体放电高温保护2    1
    设置子工具值    smartli    all    只读    单体放电高温保护3    1
    设置子工具值    smartli    all    只读    单体放电高温保护4    1
    sleep    6m
    ${获取值1}    获取web实时数据    单体放电高温保护状态_1-1
    ${获取值2}    获取web实时数据    单体放电高温保护状态_4-1
    ${获取值3}    获取web实时数据    单体放电高温保护状态_1-8
    ${获取值4}    获取web实时数据    单体放电高温保护状态_4-8
    ${获取值5}    获取web实时数据    单体放电高温保护状态_1-16
    ${获取值6}    获取web实时数据    单体放电高温保护状态_4-16
    should be true    '${获取值1}'=='异常'
    should be true    '${获取值2}'=='异常'
    should be true    '${获取值3}'=='异常'
    should be true    '${获取值4}'=='异常'
    should be true    '${获取值5}'=='异常'
    should be true    '${获取值6}'=='异常'
    ${级别设置值}    获取web参数量    单体放电高温保护告警_1-1
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    单体放电高温保护告警_1-1    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    单体放电高温保护告警
    设置子工具值    smartli    all    只读    单体放电高温保护1    0
    设置子工具值    smartli    all    只读    单体放电高温保护2    0
    设置子工具值    smartli    all    只读    单体放电高温保护3    0
    设置子工具值    smartli    all    只读    单体放电高温保护4    0
    sleep    6m
    ${获取值1}    获取web实时数据    单体放电高温保护状态_1-1
    ${获取值2}    获取web实时数据    单体放电高温保护状态_4-1
    ${获取值3}    获取web实时数据    单体放电高温保护状态_1-8
    ${获取值4}    获取web实时数据    单体放电高温保护状态_4-8
    ${获取值5}    获取web实时数据    单体放电高温保护状态_1-16
    ${获取值6}    获取web实时数据    单体放电高温保护状态_4-16
    should be true    '${获取值1}'=='正常'
    should be true    '${获取值2}'=='正常'
    should be true    '${获取值3}'=='正常'
    should be true    '${获取值4}'=='正常'
    should be true    '${获取值5}'=='正常'
    should be true    '${获取值6}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    单体放电高温保护告警
    显示属性配置    单体放电高温保护状态    数字量    web_attr=Off    gui_attr=Off

snmp_0040_单体充电低温告警测试
    [Setup]
    连接CSU
    显示属性配置    单体充电低温告警状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    单体充电低温告警1    1
    设置子工具值    smartli    all    只读    单体充电低温告警2    1
    设置子工具值    smartli    all    只读    单体充电低温告警3    1
    设置子工具值    smartli    all    只读    单体充电低温告警4    1
    sleep    6m
    ${获取值1}    获取web实时数据    单体充电低温告警状态_1-1
    ${获取值2}    获取web实时数据    单体充电低温告警状态_4-1
    ${获取值3}    获取web实时数据    单体充电低温告警状态_1-8
    ${获取值4}    获取web实时数据    单体充电低温告警状态_4-8
    ${获取值5}    获取web实时数据    单体充电低温告警状态_1-16
    ${获取值6}    获取web实时数据    单体充电低温告警状态_4-16
    should be true    '${获取值1}'=='异常'
    should be true    '${获取值2}'=='异常'
    should be true    '${获取值3}'=='异常'
    should be true    '${获取值4}'=='异常'
    should be true    '${获取值5}'=='异常'
    should be true    '${获取值6}'=='异常'
    ${级别设置值}    获取web参数量    单体充电低温告警_1-1
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    单体充电低温告警_1-1    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    单体充电低温告警
    设置子工具值    smartli    all    只读    单体充电低温告警1    0
    设置子工具值    smartli    all    只读    单体充电低温告警2    0
    设置子工具值    smartli    all    只读    单体充电低温告警3    0
    设置子工具值    smartli    all    只读    单体充电低温告警4    0
    sleep    6m
    ${获取值1}    获取web实时数据    单体充电低温告警状态_1-1
    ${获取值2}    获取web实时数据    单体充电低温告警状态_4-1
    ${获取值3}    获取web实时数据    单体充电低温告警状态_1-8
    ${获取值4}    获取web实时数据    单体充电低温告警状态_4-8
    ${获取值5}    获取web实时数据    单体充电低温告警状态_1-16
    ${获取值6}    获取web实时数据    单体充电低温告警状态_4-16
    should be true    '${获取值1}'=='正常'
    should be true    '${获取值2}'=='正常'
    should be true    '${获取值3}'=='正常'
    should be true    '${获取值4}'=='正常'
    should be true    '${获取值5}'=='正常'
    should be true    '${获取值6}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    单体充电低温告警
    显示属性配置    单体充电低温告警状态    数字量    web_attr=Off    gui_attr=Off

snmp_0042_单体充电低温保护告警测试
    [Setup]
    连接CSU
    显示属性配置    单体充电低温保护状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    单体充电低温保护1    1
    设置子工具值    smartli    all    只读    单体充电低温保护2    1
    设置子工具值    smartli    all    只读    单体充电低温保护3    1
    设置子工具值    smartli    all    只读    单体充电低温保护4    1
    sleep    6m
    ${获取值1}    获取web实时数据    单体充电低温保护状态_1-1
    ${获取值2}    获取web实时数据    单体充电低温保护状态_4-1
    ${获取值3}    获取web实时数据    单体充电低温保护状态_1-8
    ${获取值4}    获取web实时数据    单体充电低温保护状态_4-8
    ${获取值5}    获取web实时数据    单体充电低温保护状态_1-16
    ${获取值6}    获取web实时数据    单体充电低温保护状态_4-16
    should be true    '${获取值1}'=='异常'
    should be true    '${获取值2}'=='异常'
    should be true    '${获取值3}'=='异常'
    should be true    '${获取值4}'=='异常'
    should be true    '${获取值5}'=='异常'
    should be true    '${获取值6}'=='异常'
    ${级别设置值}    获取web参数量    单体充电低温保护告警_1-1
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    单体充电低温保护告警_1-1    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    单体充电低温保护告警
    设置子工具值    smartli    all    只读    单体充电低温保护1    0
    设置子工具值    smartli    all    只读    单体充电低温保护2    0
    设置子工具值    smartli    all    只读    单体充电低温保护3    0
    设置子工具值    smartli    all    只读    单体充电低温保护4    0
    sleep    6m
    ${获取值1}    获取web实时数据    单体充电低温保护状态_1-1
    ${获取值2}    获取web实时数据    单体充电低温保护状态_4-1
    ${获取值3}    获取web实时数据    单体充电低温保护状态_1-8
    ${获取值4}    获取web实时数据    单体充电低温保护状态_4-8
    ${获取值5}    获取web实时数据    单体充电低温保护状态_1-16
    ${获取值6}    获取web实时数据    单体充电低温保护状态_4-16
    should be true    '${获取值1}'=='正常'
    should be true    '${获取值2}'=='正常'
    should be true    '${获取值3}'=='正常'
    should be true    '${获取值4}'=='正常'
    should be true    '${获取值5}'=='正常'
    should be true    '${获取值6}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    单体充电低温保护告警
    显示属性配置    单体充电低温保护状态    数字量    web_attr=Off    gui_attr=Off

snmp_0044_单体放电低温告警测试
    [Setup]
    连接CSU
    显示属性配置    单体放电低温告警状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    单体放电低温告警1    1
    设置子工具值    smartli    all    只读    单体放电低温告警2    1
    设置子工具值    smartli    all    只读    单体放电低温告警3    1
    设置子工具值    smartli    all    只读    单体放电低温告警4    1
    sleep    6m
    ${获取值1}    获取web实时数据    单体放电低温告警状态_1-1
    ${获取值2}    获取web实时数据    单体放电低温告警状态_4-1
    ${获取值3}    获取web实时数据    单体放电低温告警状态_1-8
    ${获取值4}    获取web实时数据    单体放电低温告警状态_4-8
    ${获取值5}    获取web实时数据    单体放电低温告警状态_1-16
    ${获取值6}    获取web实时数据    单体放电低温告警状态_4-16
    should be true    '${获取值1}'=='异常'
    should be true    '${获取值2}'=='异常'
    should be true    '${获取值3}'=='异常'
    should be true    '${获取值4}'=='异常'
    should be true    '${获取值5}'=='异常'
    should be true    '${获取值6}'=='异常'
    ${级别设置值}    获取web参数量    单体放电低温告警_1-1
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    单体放电低温告警_1-1    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    单体放电低温告警
    设置子工具值    smartli    all    只读    单体放电低温告警1    0
    设置子工具值    smartli    all    只读    单体放电低温告警2    0
    设置子工具值    smartli    all    只读    单体放电低温告警3    0
    设置子工具值    smartli    all    只读    单体放电低温告警4    0
    sleep    6m
    ${获取值1}    获取web实时数据    单体放电低温告警状态_1-1
    ${获取值2}    获取web实时数据    单体放电低温告警状态_4-1
    ${获取值3}    获取web实时数据    单体放电低温告警状态_1-8
    ${获取值4}    获取web实时数据    单体放电低温告警状态_4-8
    ${获取值5}    获取web实时数据    单体放电低温告警状态_1-16
    ${获取值6}    获取web实时数据    单体放电低温告警状态_4-16
    should be true    '${获取值1}'=='正常'
    should be true    '${获取值2}'=='正常'
    should be true    '${获取值3}'=='正常'
    should be true    '${获取值4}'=='正常'
    should be true    '${获取值5}'=='正常'
    should be true    '${获取值6}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    单体放电低温告警
    显示属性配置    单体放电低温告警状态    数字量    web_attr=Off    gui_attr=Off

snmp_0046_单体放电低温保护告警测试
    [Setup]
    连接CSU
    显示属性配置    单体放电低温保护状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    单体放电低温保护1    1
    设置子工具值    smartli    all    只读    单体放电低温保护2    1
    设置子工具值    smartli    all    只读    单体放电低温保护3    1
    设置子工具值    smartli    all    只读    单体放电低温保护4    1
    sleep    6m
    ${获取值1}    获取web实时数据    单体放电低温保护状态_1-1
    ${获取值2}    获取web实时数据    单体放电低温保护状态_4-1
    ${获取值3}    获取web实时数据    单体放电低温保护状态_1-8
    ${获取值4}    获取web实时数据    单体放电低温保护状态_4-8
    ${获取值5}    获取web实时数据    单体放电低温保护状态_1-16
    ${获取值6}    获取web实时数据    单体放电低温保护状态_4-16
    should be true    '${获取值1}'=='异常'
    should be true    '${获取值2}'=='异常'
    should be true    '${获取值3}'=='异常'
    should be true    '${获取值4}'=='异常'
    should be true    '${获取值5}'=='异常'
    should be true    '${获取值6}'=='异常'
    ${级别设置值}    获取web参数量    单体放电低温保护告警_1-1
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    单体放电低温保护告警_1-1    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    单体放电低温保护告警
    设置子工具值    smartli    all    只读    单体放电低温保护1    0
    设置子工具值    smartli    all    只读    单体放电低温保护2    0
    设置子工具值    smartli    all    只读    单体放电低温保护3    0
    设置子工具值    smartli    all    只读    单体放电低温保护4    0
    sleep    6m
    ${获取值1}    获取web实时数据    单体放电低温保护状态_1-1
    ${获取值2}    获取web实时数据    单体放电低温保护状态_4-1
    ${获取值3}    获取web实时数据    单体放电低温保护状态_1-8
    ${获取值4}    获取web实时数据    单体放电低温保护状态_4-8
    ${获取值5}    获取web实时数据    单体放电低温保护状态_1-16
    ${获取值6}    获取web实时数据    单体放电低温保护状态_4-16
    should be true    '${获取值1}'=='正常'
    should be true    '${获取值2}'=='正常'
    should be true    '${获取值3}'=='正常'
    should be true    '${获取值4}'=='正常'
    should be true    '${获取值5}'=='正常'
    should be true    '${获取值6}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    单体放电低温保护告警
    显示属性配置    单体放电低温保护状态    数字量    web_attr=Off    gui_attr=Off

snmp_0048_单体落后电压差告警测试
    [Setup]
    连接CSU
    显示属性配置    单体落后电压差状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    单体落后电压差    1
    sleep    6m
    ${获取值}    获取web实时数据    单体落后电压差状态-1
    ${获取值8}    获取web实时数据    单体落后电压差状态-8
    ${获取值16}    获取web实时数据    单体落后电压差状态-16
    should be true    '${获取值}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值16}'=='异常'
    ${级别设置值}    获取web参数量    单体落后电压差
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    单体落后电压差    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    单体落后电压差
    设置子工具值    smartli    all    只读    单体落后电压差    0
    sleep    6m
    ${获取值}    获取web实时数据    单体落后电压差状态-1
    ${获取值8}    获取web实时数据    单体落后电压差状态-8
    ${获取值16}    获取web实时数据    单体落后电压差状态-16
    should be true    '${获取值}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值16}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    单体落后电压差
    显示属性配置    单体落后电压差状态    数字量    web_attr=Off    gui_attr=Off

snmp_0050_单体落后保护电压差告警测试
    [Setup]
    连接CSU
    显示属性配置    单体落后保护电压差状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    单体落后保护电压差    1
    sleep    6m
    ${获取值}    获取web实时数据    单体落后保护电压差状态-1
    ${获取值8}    获取web实时数据    单体落后保护电压差状态-8
    ${获取值16}    获取web实时数据    单体落后保护电压差状态-16
    should be true    '${获取值}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值16}'=='异常'
    ${级别设置值}    获取web参数量    单体落后保护电压差
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    单体落后保护电压差    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    单体落后保护电压差
    设置子工具值    smartli    all    只读    单体落后保护电压差    0
    sleep    6m
    ${获取值}    获取web实时数据    单体落后保护电压差状态-1
    ${获取值8}    获取web实时数据    单体落后保护电压差状态-8
    ${获取值16}    获取web实时数据    单体落后保护电压差状态-16
    should be true    '${获取值}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值16}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    单体落后保护电压差
    显示属性配置    单体落后保护电压差状态    数字量    web_attr=Off    gui_attr=Off

snmp_0052_电池SOC低告警测试
    [Setup]
    连接CSU
    显示属性配置    电池SOC低告警状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    电池SOC低告警    1
    sleep    6m
    ${获取值}    获取web实时数据    电池SOC低告警状态-1
    ${获取值8}    获取web实时数据    电池SOC低告警状态-8
    ${获取值16}    获取web实时数据    电池SOC低告警状态-16
    should be true    '${获取值}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值16}'=='异常'
    ${级别设置值}    获取web参数量    电池SOC低告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池SOC低告警    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    电池SOC低告警
    设置子工具值    smartli    all    只读    电池SOC低告警    0
    sleep    6m
    ${获取值}    获取web实时数据    电池SOC低告警状态-1
    ${获取值8}    获取web实时数据    电池SOC低告警状态-8
    ${获取值16}    获取web实时数据    电池SOC低告警状态-16
    should be true    '${获取值}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值16}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    电池SOC低告警
    显示属性配置    电池SOC低告警状态    数字量    web_attr=Off    gui_attr=Off

snmp_0054_电池SOC低保护告警测试
    [Setup]
    连接CSU
    显示属性配置    电池SOC低保护状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    电池SOC低保护    1
    sleep    6m
    ${获取值}    获取web实时数据    电池SOC低保护状态-1
    ${获取值8}    获取web实时数据    电池SOC低保护状态-8
    ${获取值16}    获取web实时数据    电池SOC低保护状态-16
    should be true    '${获取值}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值16}'=='异常'
    ${级别设置值}    获取web参数量    电池SOC低保护
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池SOC低保护    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    电池SOC低保护
    设置子工具值    smartli    all    只读    电池SOC低保护    0
    sleep    6m
    ${获取值}    获取web实时数据    电池SOC低保护状态-1
    ${获取值8}    获取web实时数据    电池SOC低保护状态-8
    ${获取值16}    获取web实时数据    电池SOC低保护状态-16
    should be true    '${获取值}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值16}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    电池SOC低保护
    显示属性配置    电池SOC低保护状态    数字量    web_attr=Off    gui_attr=Off

snmp_0056_电池SOH告警测试
    [Setup]
    连接CSU
    显示属性配置    电池SOH告警状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    电池SOH告警    1
    sleep    6m
    ${获取值}    获取web实时数据    电池SOH告警状态-1
    ${获取值8}    获取web实时数据    电池SOH告警状态-8
    ${获取值16}    获取web实时数据    电池SOH告警状态-16
    should be true    '${获取值}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值16}'=='异常'
    ${级别设置值}    获取web参数量    电池SOH告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池SOH告警    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    电池SOH告警
    设置子工具值    smartli    all    只读    电池SOH告警    0
    sleep    6m
    ${获取值}    获取web实时数据    电池SOH告警状态-1
    ${获取值8}    获取web实时数据    电池SOH告警状态-8
    ${获取值16}    获取web实时数据    电池SOH告警状态-16
    should be true    '${获取值}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值16}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    电池SOH告警
    显示属性配置    电池SOH告警状态    数字量    web_attr=Off    gui_attr=Off

snmp_0058_电池SOH保护告警测试
    [Setup]
    连接CSU
    显示属性配置    电池SOH保护状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    电池SOH保护    1
    sleep    6m
    ${获取值}    获取web实时数据    电池SOH保护状态-1
    ${获取值8}    获取web实时数据    电池SOH保护状态-8
    ${获取值16}    获取web实时数据    电池SOH保护状态-16
    should be true    '${获取值}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值16}'=='异常'
    ${级别设置值}    获取web参数量    电池SOH保护
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池SOH保护    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    电池SOH保护
    设置子工具值    smartli    all    只读    电池SOH保护    0
    sleep    6m
    ${获取值}    获取web实时数据    电池SOH保护状态-1
    ${获取值8}    获取web实时数据    电池SOH保护状态-8
    ${获取值16}    获取web实时数据    电池SOH保护状态-16
    should be true    '${获取值}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值16}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    电池SOH保护
    显示属性配置    电池SOH保护状态    数字量    web_attr=Off    gui_attr=Off

snmp_0060_单体损坏保护告警测试
    [Setup]
    连接CSU
    显示属性配置    单体损坏保护状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    单体损坏保护1    1
    设置子工具值    smartli    all    只读    单体损坏保护2    1
    设置子工具值    smartli    all    只读    单体损坏保护3    1
    设置子工具值    smartli    all    只读    单体损坏保护4    1
    设置子工具值    smartli    all    只读    单体损坏保护5    1
    设置子工具值    smartli    all    只读    单体损坏保护6    1
    设置子工具值    smartli    all    只读    单体损坏保护7    1
    设置子工具值    smartli    all    只读    单体损坏保护8    1
    设置子工具值    smartli    all    只读    单体损坏保护9    1
    设置子工具值    smartli    all    只读    单体损坏保护10    1
    设置子工具值    smartli    all    只读    单体损坏保护11    1
    设置子工具值    smartli    all    只读    单体损坏保护12    1
    设置子工具值    smartli    all    只读    单体损坏保护13    1
    设置子工具值    smartli    all    只读    单体损坏保护14    1
    设置子工具值    smartli    all    只读    单体损坏保护15    1
    sleep    6m
    ${获取值1}    获取web实时数据    单体损坏保护状态_1-1
    ${获取值2}    获取web实时数据    单体损坏保护状态_8-1
    ${获取值3}    获取web实时数据    单体损坏保护状态_15-1
    ${获取值4}    获取web实时数据    单体损坏保护状态_1-8
    ${获取值5}    获取web实时数据    单体损坏保护状态_8-8
    ${获取值6}    获取web实时数据    单体损坏保护状态_15-8
    ${获取值7}    获取web实时数据    单体损坏保护状态_1-16
    ${获取值8}    获取web实时数据    单体损坏保护状态_8-16
    ${获取值9}    获取web实时数据    单体损坏保护状态_15-16
    Comment    should be true    '${获取值1}'=='异常'
    Comment    should be true    '${获取值2}'=='异常'
    Comment    should be true    '${获取值3}'=='异常'
    Comment    should be true    '${获取值4}'=='异常'
    Comment    should be true    '${获取值5}'=='异常'
    Comment    should be true    '${获取值6}'=='异常'
    Comment    should be true    '${获取值7}'=='异常'
    Comment    should be true    '${获取值8}'=='异常'
    Comment    should be true    '${获取值9}'=='异常'
    ${级别设置值}    获取web参数量    单体损坏保护_1-1
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    单体损坏保护_1-1    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    单体损坏保护
    设置子工具值    smartli    all    只读    单体损坏保护1    0
    设置子工具值    smartli    all    只读    单体损坏保护2    0
    设置子工具值    smartli    all    只读    单体损坏保护3    0
    设置子工具值    smartli    all    只读    单体损坏保护4    0
    设置子工具值    smartli    all    只读    单体损坏保护5    0
    设置子工具值    smartli    all    只读    单体损坏保护6    0
    设置子工具值    smartli    all    只读    单体损坏保护7    0
    设置子工具值    smartli    all    只读    单体损坏保护8    0
    设置子工具值    smartli    all    只读    单体损坏保护9    0
    设置子工具值    smartli    all    只读    单体损坏保护10    0
    设置子工具值    smartli    all    只读    单体损坏保护11    0
    设置子工具值    smartli    all    只读    单体损坏保护12    0
    设置子工具值    smartli    all    只读    单体损坏保护13    0
    设置子工具值    smartli    all    只读    单体损坏保护14    0
    设置子工具值    smartli    all    只读    单体损坏保护15    0
    sleep    6m
    ${获取值1}    获取web实时数据    单体损坏保护状态_1-1
    ${获取值2}    获取web实时数据    单体损坏保护状态_8-1
    ${获取值3}    获取web实时数据    单体损坏保护状态_15-1
    ${获取值4}    获取web实时数据    单体损坏保护状态_1-8
    ${获取值5}    获取web实时数据    单体损坏保护状态_8-8
    ${获取值6}    获取web实时数据    单体损坏保护状态_15-8
    ${获取值7}    获取web实时数据    单体损坏保护状态_1-16
    ${获取值8}    获取web实时数据    单体损坏保护状态_8-16
    ${获取值9}    获取web实时数据    单体损坏保护状态_15-16
    Comment    should be true    '${获取值1}'=='正常'
    Comment    should be true    '${获取值2}'=='正常'
    Comment    should be true    '${获取值3}'=='正常'
    Comment    should be true    '${获取值4}'=='正常'
    Comment    should be true    '${获取值5}'=='正常'
    Comment    should be true    '${获取值6}'=='正常'
    Comment    should be true    '${获取值7}'=='正常'
    Comment    should be true    '${获取值8}'=='正常'
    Comment    should be true    '${获取值9}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    单体损坏保护
    显示属性配置    单体损坏保护状态    数字量    web_attr=Off    gui_attr=Off

snmp_0062_充电回路开关失效告警测试
    [Setup]
    连接CSU
    显示属性配置    充电回路开关失效告警状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    充电回路开关失效告警    1
    sleep    6m
    ${获取值}    获取web实时数据    充电回路开关失效告警状态-1
    ${获取值8}    获取web实时数据    充电回路开关失效告警状态-8
    ${获取值16}    获取web实时数据    充电回路开关失效告警状态-16
    should be true    '${获取值}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值16}'=='异常'
    ${级别设置值}    获取web参数量    充电回路开关失效告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    充电回路开关失效告警    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    充电回路开关失效告警
    设置子工具值    smartli    all    只读    充电回路开关失效告警    0
    sleep    6m
    ${获取值}    获取web实时数据    充电回路开关失效告警状态-1
    ${获取值8}    获取web实时数据    充电回路开关失效告警状态-8
    ${获取值16}    获取web实时数据    充电回路开关失效告警状态-16
    should be true    '${获取值}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值16}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    充电回路开关失效告警
    显示属性配置    充电回路开关失效告警状态    数字量    web_attr=Off    gui_attr=Off

snmp_0064_放电回路开关失效告警测试
    [Setup]
    连接CSU
    显示属性配置    放电回路开关失效告警状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    放电回路开关失效告警    1
    sleep    6m
    ${获取值}    获取web实时数据    放电回路开关失效告警状态-1
    ${获取值8}    获取web实时数据    放电回路开关失效告警状态-8
    ${获取值16}    获取web实时数据    放电回路开关失效告警状态-16
    should be true    '${获取值}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值16}'=='异常'
    ${级别设置值}    获取web参数量    放电回路开关失效告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    放电回路开关失效告警    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    放电回路开关失效告警
    设置子工具值    smartli    all    只读    放电回路开关失效告警    0
    sleep    6m
    ${获取值}    获取web实时数据    放电回路开关失效告警状态-1
    ${获取值8}    获取web实时数据    放电回路开关失效告警状态-8
    ${获取值16}    获取web实时数据    放电回路开关失效告警状态-16
    should be true    '${获取值}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值16}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    放电回路开关失效告警
    显示属性配置    放电回路开关失效告警状态    数字量    web_attr=Off    gui_attr=Off

snmp_0066_限流回路失效告警测试
    [Setup]
    连接CSU
    显示属性配置    限流回路失效告警状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    限流回路失效告警    1
    sleep    6m
    ${获取值}    获取web实时数据    限流回路失效告警状态-1
    ${获取值8}    获取web实时数据    限流回路失效告警状态-8
    ${获取值16}    获取web实时数据    限流回路失效告警状态-16
    should be true    '${获取值}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值16}'=='异常'
    ${级别设置值}    获取web参数量    限流回路失效告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    限流回路失效告警    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    限流回路失效告警
    设置子工具值    smartli    all    只读    限流回路失效告警    0
    sleep    6m
    ${获取值}    获取web实时数据    限流回路失效告警状态-1
    ${获取值8}    获取web实时数据    限流回路失效告警状态-8
    ${获取值16}    获取web实时数据    限流回路失效告警状态-16
    should be true    '${获取值}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值16}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    限流回路失效告警
    显示属性配置    限流回路失效告警状态    数字量    web_attr=Off    gui_attr=Off

snmp_0068_短路保护告警测试
    [Setup]
    连接CSU
    显示属性配置    短路保护状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    短路保护    1
    sleep    6m
    ${获取值}    获取web实时数据    短路保护状态-1
    ${获取值8}    获取web实时数据    短路保护状态-8
    ${获取值16}    获取web实时数据    短路保护状态-16
    should be true    '${获取值}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值16}'=='异常'
    ${级别设置值}    获取web参数量    短路保护
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    短路保护    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    短路保护
    设置子工具值    smartli    all    只读    短路保护    0
    sleep    6m
    ${获取值}    获取web实时数据    短路保护状态-1
    ${获取值8}    获取web实时数据    短路保护状态-8
    ${获取值16}    获取web实时数据    短路保护状态-16
    should be true    '${获取值}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值16}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    短路保护
    显示属性配置    短路保护状态    数字量    web_attr=Off    gui_attr=Off

snmp_0070_电池反接告警测试
    [Setup]
    连接CSU
    显示属性配置    电池反接告警状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    电池反接告警    1
    sleep    6m
    ${获取值}    获取web实时数据    电池反接告警状态-1
    ${获取值8}    获取web实时数据    电池反接告警状态-8
    ${获取值16}    获取web实时数据    电池反接告警状态-16
    should be true    '${获取值}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值16}'=='异常'
    ${级别设置值}    获取web参数量    电池反接告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池反接告警    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    电池反接告警
    设置子工具值    smartli    all    只读    电池反接告警    0
    sleep    6m
    ${获取值}    获取web实时数据    电池反接告警状态-1
    ${获取值8}    获取web实时数据    电池反接告警状态-8
    ${获取值16}    获取web实时数据    电池反接告警状态-16
    should be true    '${获取值}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值16}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    电池反接告警
    显示属性配置    电池反接告警状态    数字量    web_attr=Off    gui_attr=Off

snmp_0072_单体温度传感器失效告警测试
    [Setup]
    连接CSU
    显示属性配置    单体温度传感器失效告警状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    单体温度传感器失效告警1    1
    设置子工具值    smartli    all    只读    单体温度传感器失效告警2    1
    设置子工具值    smartli    all    只读    单体温度传感器失效告警3    1
    设置子工具值    smartli    all    只读    单体温度传感器失效告警4    1
    sleep    6m
    ${获取值1}    获取web实时数据    单体温度传感器失效告警状态_1-1
    ${获取值2}    获取web实时数据    单体温度传感器失效告警状态_4-1
    ${获取值3}    获取web实时数据    单体温度传感器失效告警状态_1-8
    ${获取值4}    获取web实时数据    单体温度传感器失效告警状态_4-8
    ${获取值5}    获取web实时数据    单体温度传感器失效告警状态_1-16
    ${获取值6}    获取web实时数据    单体温度传感器失效告警状态_4-16
    should be true    '${获取值1}'=='异常'
    should be true    '${获取值2}'=='异常'
    should be true    '${获取值3}'=='异常'
    should be true    '${获取值4}'=='异常'
    should be true    '${获取值5}'=='异常'
    should be true    '${获取值6}'=='异常'
    ${级别设置值}    获取web参数量    单体温度传感器失效告警_1-1
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    单体温度传感器失效告警_1-1    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    单体温度传感器失效告警
    设置子工具值    smartli    all    只读    单体温度传感器失效告警1    0
    设置子工具值    smartli    all    只读    单体温度传感器失效告警2    0
    设置子工具值    smartli    all    只读    单体温度传感器失效告警3    0
    设置子工具值    smartli    all    只读    单体温度传感器失效告警4    0
    sleep    6m
    ${获取值1}    获取web实时数据    单体温度传感器失效告警状态_1-1
    ${获取值2}    获取web实时数据    单体温度传感器失效告警状态_4-1
    ${获取值3}    获取web实时数据    单体温度传感器失效告警状态_1-8
    ${获取值4}    获取web实时数据    单体温度传感器失效告警状态_4-8
    ${获取值5}    获取web实时数据    单体温度传感器失效告警状态_1-16
    ${获取值6}    获取web实时数据    单体温度传感器失效告警状态_4-16
    should be true    '${获取值1}'=='正常'
    should be true    '${获取值2}'=='正常'
    should be true    '${获取值3}'=='正常'
    should be true    '${获取值4}'=='正常'
    should be true    '${获取值5}'=='正常'
    should be true    '${获取值6}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    单体温度传感器失效告警
    显示属性配置    单体温度传感器失效告警状态    数字量    web_attr=Off    gui_attr=Off

snmp_0074_机内过温保护告警测试
    [Setup]
    连接CSU
    显示属性配置    机内过温保护告警状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    机内过温保护告警    1
    sleep    6m
    ${获取值}    获取web实时数据    机内过温保护告警状态-1
    ${获取值8}    获取web实时数据    机内过温保护告警状态-8
    ${获取值16}    获取web实时数据    机内过温保护告警状态-16
    should be true    '${获取值}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值16}'=='异常'
    ${级别设置值}    获取web参数量    机内过温保护告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    机内过温保护告警    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    机内过温保护告警
    设置子工具值    smartli    all    只读    机内过温保护告警    0
    sleep    6m
    ${获取值}    获取web实时数据    机内过温保护告警状态-1
    ${获取值8}    获取web实时数据    机内过温保护告警状态-8
    ${获取值16}    获取web实时数据    机内过温保护告警状态-16
    should be true    '${获取值}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值16}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    机内过温保护告警
    显示属性配置    机内过温保护告警状态    数字量    web_attr=Off    gui_attr=Off

snmp_0076_BDCU电池欠压保护告警测试
    [Setup]
    连接CSU
    显示属性配置    BDCU电池欠压保护状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    BDCU电池欠压保护    1
    sleep    6m
    ${获取值}    获取web实时数据    BDCU电池欠压保护状态-1
    ${获取值8}    获取web实时数据    BDCU电池欠压保护状态-8
    ${获取值16}    获取web实时数据    BDCU电池欠压保护状态-16
    should be true    '${获取值}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值16}'=='异常'
    ${级别设置值}    获取web参数量    BDCU电池欠压保护
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    BDCU电池欠压保护    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    BDCU电池欠压保护
    设置子工具值    smartli    all    只读    BDCU电池欠压保护    0
    sleep    6m
    ${获取值}    获取web实时数据    BDCU电池欠压保护状态-1
    ${获取值8}    获取web实时数据    BDCU电池欠压保护状态-8
    ${获取值16}    获取web实时数据    BDCU电池欠压保护状态-16
    should be true    '${获取值}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值16}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    BDCU电池欠压保护
    显示属性配置    BDCU电池欠压保护状态    数字量    web_attr=Off    gui_attr=Off

snmp_0078_BDCU母排欠压保护告警测试
    [Setup]
    连接CSU
    显示属性配置    BDCU母排欠压保护告警状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    BDCU母排欠压保护告警    1
    sleep    8m
    ${获取值}    获取web实时数据    BDCU母排欠压保护告警状态-1
    ${获取值8}    获取web实时数据    BDCU母排欠压保护告警状态-8
    ${获取值16}    获取web实时数据    BDCU母排欠压保护告警状态-16
    should be true    '${获取值}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值16}'=='异常'
    ${级别设置值}    获取web参数量    BDCU母排欠压保护告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    BDCU母排欠压保护告警    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    BDCU母排欠压保护告警
    设置子工具值    smartli    all    只读    BDCU母排欠压保护告警    0
    sleep    8m
    ${获取值}    获取web实时数据    BDCU母排欠压保护告警状态-1
    ${获取值8}    获取web实时数据    BDCU母排欠压保护告警状态-8
    ${获取值16}    获取web实时数据    BDCU母排欠压保护告警状态-16
    should be true    '${获取值}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值16}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    BDCU母排欠压保护告警
    显示属性配置    BDCU母排欠压保护告警状态    数字量    web_attr=Off    gui_attr=Off

snmp_0080_BDCU EEPROM故障告警测试
    [Setup]
    连接CSU
    显示属性配置    BDCU EEPROM故障告警状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    BDCU EEPROM故障告警    1
    sleep    6m
    ${获取值}    获取web实时数据    BDCU EEPROM故障告警状态-1
    ${获取值8}    获取web实时数据    BDCU EEPROM故障告警状态-8
    ${获取值16}    获取web实时数据    BDCU EEPROM故障告警状态-16
    should be true    '${获取值}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值16}'=='异常'
    ${级别设置值}    获取web参数量    BDCU EEPROM故障告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    BDCU EEPROM故障告警    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    BDCU EEPROM故障告警
    设置子工具值    smartli    all    只读    BDCU EEPROM故障告警    0
    sleep    6m
    ${获取值}    获取web实时数据    BDCU EEPROM故障告警状态-1
    ${获取值8}    获取web实时数据    BDCU EEPROM故障告警状态-8
    ${获取值16}    获取web实时数据    BDCU EEPROM故障告警状态-16
    should be true    '${获取值}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值16}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    BDCU EEPROM故障告警
    显示属性配置    BDCU EEPROM故障告警状态    数字量    web_attr=Off    gui_attr=Off

snmp_0082_单体温度异常告警测试
    [Setup]
    连接CSU
    显示属性配置    单体温度异常状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    单体温度异常    1
    sleep    6m
    ${获取值}    获取web实时数据    单体温度异常状态-1
    ${获取值8}    获取web实时数据    单体温度异常状态-8
    ${获取值16}    获取web实时数据    单体温度异常状态-16
    should be true    '${获取值}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值16}'=='异常'
    ${级别设置值}    获取web参数量    单体温度异常
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    单体温度异常    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    单体温度异常
    设置子工具值    smartli    all    只读    单体温度异常    0
    sleep    6m
    ${获取值}    获取web实时数据    单体温度异常状态-1
    ${获取值8}    获取web实时数据    单体温度异常状态-8
    ${获取值16}    获取web实时数据    单体温度异常状态-16
    should be true    '${获取值}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值16}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    单体温度异常
    显示属性配置    单体温度异常状态    数字量    web_attr=Off    gui_attr=Off

snmp_0084_地址冲突告警测试
    [Setup]
    连接CSU
    显示属性配置    地址冲突告警状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    地址冲突    1
    sleep    6m
    ${获取值}    获取web实时数据    地址冲突告警状态-1
    ${获取值8}    获取web实时数据    地址冲突告警状态-8
    ${获取值16}    获取web实时数据    地址冲突告警状态-16
    should be true    '${获取值}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值16}'=='异常'
    ${级别设置值}    获取web参数量    地址冲突告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    地址冲突告警    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    地址冲突告警
    设置子工具值    smartli    all    只读    地址冲突    0
    sleep    6m
    ${获取值}    获取web实时数据    地址冲突告警状态-1
    ${获取值8}    获取web实时数据    地址冲突告警状态-8
    ${获取值16}    获取web实时数据    地址冲突告警状态-16
    should be true    '${获取值}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值16}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    地址冲突告警
    显示属性配置    地址冲突告警状态    数字量    web_attr=Off    gui_attr=Off

snmp_0086_振动告警测试
    [Setup]
    连接CSU
    显示属性配置    振动告警状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    振动告警    1
    sleep    6m
    ${获取值}    获取web实时数据    振动告警状态-1
    ${获取值8}    获取web实时数据    振动告警状态-8
    ${获取值16}    获取web实时数据    振动告警状态-16
    should be true    '${获取值}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值16}'=='异常'
    ${级别设置值}    获取web参数量    振动告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    振动告警    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    振动告警
    设置子工具值    smartli    all    只读    振动告警    0
    sleep    6m
    ${获取值}    获取web实时数据    振动告警状态-1
    ${获取值8}    获取web实时数据    振动告警状态-8
    ${获取值16}    获取web实时数据    振动告警状态-16
    should be true    '${获取值}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值16}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    振动告警
    显示属性配置    振动告警状态    数字量    web_attr=Off    gui_attr=Off

snmp_0088_单体电压采样异常告警测试
    [Setup]
    连接CSU
    显示属性配置    单体电压采样异常告警状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    单体电压采样异常    1
    sleep    6m
    ${获取值}    获取web实时数据    单体电压采样异常告警状态-1
    ${获取值8}    获取web实时数据    单体电压采样异常告警状态-8
    ${获取值16}    获取web实时数据    单体电压采样异常告警状态-16
    should be true    '${获取值}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值16}'=='异常'
    ${级别设置值}    获取web参数量    单体电压采样异常告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    单体电压采样异常告警    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    单体电压采样异常告警
    设置子工具值    smartli    all    只读    单体电压采样异常    0
    sleep    6m
    ${获取值}    获取web实时数据    单体电压采样异常告警状态-1
    ${获取值8}    获取web实时数据    单体电压采样异常告警状态-8
    ${获取值16}    获取web实时数据    单体电压采样异常告警状态-16
    should be true    '${获取值}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值16}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    单体电压采样异常告警
    显示属性配置    单体电压采样异常告警状态    数字量    web_attr=Off    gui_attr=Off

snmp_0090_BDCU通信断告警测试
    [Setup]
    连接CSU
    显示属性配置    BDCU通信断告警状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    BDU通信断    1
    sleep    6m
    ${获取值}    获取web实时数据    BDCU通信断告警状态-1
    ${获取值8}    获取web实时数据    BDCU通信断告警状态-8
    ${获取值16}    获取web实时数据    BDCU通信断告警状态-16
    should be true    '${获取值}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值16}'=='异常'
    ${级别设置值}    获取web参数量    BDCU通信断告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    BDCU通信断告警    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    BDCU通信断告警
    设置子工具值    smartli    all    只读    BDU通信断    0
    sleep    6m
    ${获取值}    获取web实时数据    BDCU通信断告警状态-1
    ${获取值8}    获取web实时数据    BDCU通信断告警状态-8
    ${获取值16}    获取web实时数据    BDCU通信断告警状态-16
    should be true    '${获取值}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值16}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    BDCU通信断告警
    显示属性配置    BDCU通信断告警状态    数字量    web_attr=Off    gui_attr=Off

snmp_0092_BMS通信断告警测试
    [Setup]
    连接CSU
    显示属性配置    BMS通信断状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    控制子工具运行停止    smartli    关闭
    sleep    8m
    ${获取值}    获取web实时数据    BMS通信断状态-1
    ${获取值8}    获取web实时数据    BMS通信断状态-8
    ${获取值16}    获取web实时数据    BMS通信断状态-16
    should be true    '${获取值}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值16}'=='异常'
    ${级别设置值}    获取web参数量    BMS通信断告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    BMS通信断告警    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    BMS通信断告警
    控制子工具运行停止    smartli    开启
    sleep    8m
    ${获取值}    获取web实时数据    BMS通信断状态-1
    ${获取值8}    获取web实时数据    BMS通信断状态-8
    ${获取值16}    获取web实时数据    BMS通信断状态-16
    should be true    '${获取值}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值16}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    BMS通信断告警
    显示属性配置    BMS通信断状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    控制子工具运行停止    smartli    开启

snmp_0094_BDCU母排过压保护告警测试
    [Setup]
    连接CSU
    显示属性配置    BDCU母排过压保护告警状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    设置子工具值    smartli    all    只读    BDCU母排过压保护告警    1
    sleep    6m
    ${获取值}    获取web实时数据    BDCU母排过压保护告警状态-1
    ${获取值8}    获取web实时数据    BDCU母排过压保护告警状态-8
    ${获取值16}    获取web实时数据    BDCU母排过压保护告警状态-16
    should be true    '${获取值}'=='异常'
    should be true    '${获取值8}'=='异常'
    should be true    '${获取值16}'=='异常'
    ${级别设置值}    获取web参数量    BDCU母排过压保护告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    BDCU母排过压保护告警    严重
    wait until keyword succeeds    15m    2    查询指定告警信息    BDCU母排过压保护告警
    设置子工具值    smartli    all    只读    BDCU母排过压保护告警    0
    sleep    6m
    ${获取值}    获取web实时数据    BDCU母排过压保护告警状态-1
    ${获取值8}    获取web实时数据    BDCU母排过压保护告警状态-8
    ${获取值16}    获取web实时数据    BDCU母排过压保护告警状态-16
    should be true    '${获取值}'=='正常'
    should be true    '${获取值8}'=='正常'
    should be true    '${获取值16}'=='正常'
    wait until keyword succeeds    15m    2    查询指定告警信息不为    BDCU母排过压保护告警
    显示属性配置    BDCU母排过压保护告警状态    数字量    web_attr=Off    gui_attr=Off
