*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
snmp_0002_smartSwitch1parameter
    FOR    ${智能空开序号}    IN RANGE    1    41
        ${比较结果}    对比数据_V2C    smartSwitch${智能空开序号}parameter
        should be true    ${比较结果}
    END

snmp_0004_smartSwitch1parameter_write
    [Setup]    Run Keywords    设置子工具个数    DMU_IntelAirSwit    2
    ...    AND    设置web控制量    RS485总线设备统计
    ...    AND    sleep    5min
    ...    AND    设置WEB参数量    <<智能空开下电使能~0x2c001050010001>>    允许
    ...    AND    设置WEB参数量    <<智能空开定时下电使能~0x2c001050110001>>    允许
    ...    AND    设置WEB参数量    <<智能空开免责下电使能~0x2c001050160001>>    允许
    ...    AND    设置WEB参数量    <<智能空开过流保护使能~0x2c001050040001>>    允许
    @{SSW随机list}    从1-15个数中随机选n个不重复的单体    ${SSW最大数目}    4
    FOR    ${智能空开序号}    IN    @{SSW随机list}
        ${比较结果}    SNMP批量参数设置    smartSwitch${智能空开序号}parameter
        should be true    ${比较结果}
    END
    [Teardown]    Run Keywords    设置子工具个数    DMU_IntelAirSwit    40
    ...    AND    设置web控制量    RS485总线设备统计
    ...    AND    sleep    5min
