*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
批量获取液位传感器模拟量测试
    写入CSV文档    液位传感器模拟量获取测试    信号名称    信号值    结果    备注
    连接CSU
    ${可设置范围}    获取web参数可设置范围    油箱高度-1
    Wait Until Keyword Succeeds    10    2    设置web参数量    油箱高度-1    ${可设置范围}[1]
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=液位传感器
    ${排除列表}    create list
	@{参数值0列表}   create list  
    @{参数值1列表}   create list
    @{参数值2列表}   create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${缺省值}    获取web参数上下限范围    ${信号名称}
        ${转换后}    evaluate    ${缺省值}[1]*65536-5
        ${转换后}    Convert to string    ${转换后}
        ${设置值}    run keyword if    ${转换后}<0    evaluate    0
        ...    ELSE    evaluate    ${转换后}
        ${设置值}    Convert to string    ${设置值}
		${dict1}          Create Dictionary 
        Set To Dictionary    ${dict1}     name     ${信号名称}  
        Set To Dictionary    ${dict1}     value    ${设置值}
		Set To Dictionary    ${dict1}     prepare_value    ${缺省值}[1] 
		Append To List       ${参数值1列表}    ${dict1}
    END
	Run Keyword And Continue On Failure    特殊量获取值和设置值列表对比封装判断结果    llSensor       只读    ${参数值1列表}   液位传感器模拟量获取测试
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${缺省值}    获取web参数上下限范围    ${信号名称}
        ${转换后}    evaluate    ${缺省值}[2]*65536-5
        ${转换后}    Convert to string    ${转换后}
        ${设置值}    run keyword if    ${转换后}<0    evaluate    0
        ...    ELSE    evaluate    ${转换后}
        ${设置值}    Convert to string    ${设置值}
		${dict2}          Create Dictionary 
        Set To Dictionary    ${dict2}     name     ${信号名称}  
        Set To Dictionary    ${dict2}     value    ${设置值}
		Set To Dictionary    ${dict2}     prepare_value    ${缺省值}[2] 
		Append To List       ${参数值2列表}    ${dict2}
    END
	Run Keyword And Continue On Failure    特殊量获取值和设置值列表对比封装判断结果    llSensor       只读    ${参数值2列表}     液位传感器模拟量获取测试
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${缺省值}    获取web参数上下限范围    ${信号名称}
        ${转换后}    evaluate    ${缺省值}[0]*65536-5
        ${转换后}    Convert to string    ${转换后}
        ${设置值}    run keyword if    ${转换后}<0    evaluate    0
        ...    ELSE    evaluate    ${转换后}
        ${设置值}    Convert to string    ${设置值}
		${dict0}          Create Dictionary 
        Set To Dictionary    ${dict0}     name     ${信号名称}  
        Set To Dictionary    ${dict0}     value    ${设置值}
		Set To Dictionary    ${dict0}     prepare_value    ${缺省值}[0] 
		Append To List       ${参数值0列表}    ${dict0}
    END
	Run Keyword And Continue On Failure    特殊量获取值和设置值列表对比封装判断结果    llSensor       只读    ${参数值0列表}      液位传感器模拟量获取测试
	