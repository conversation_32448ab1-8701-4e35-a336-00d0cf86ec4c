*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
获取和设置扩展温湿度参数（扩展温度高阈值）
    [Setup]    Run Keywords    连接CSU
    ...    AND    Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    无
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    FOR    ${i}    IN RANGE    1    9
        Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_${i}
        sleep    10
        ${缺省值}    获取WEB参数上下限范围    扩展温度高阈值_${i}
        ${可设置范围}    获取WEB参数可设置范围    扩展温度高阈值_${i}
        ${初始值}    获取SNMP数据_单个    extendTemperatureHighThresholdvalue    ${i}
        ${超下限}    Evaluate    ${可设置范围}[0]-${缺省值}[4]
        ${超上限}    Evaluate    ${可设置范围}[1]+${缺省值}[4]
        ${设置结果1}    设置SNMP参数量    extendTemperatureHighThresholdvalue    ${超下限}    ${i}
        sleep    10
        ${协议获取结果1}    获取SNMP数据_单个    extendTemperatureHighThresholdvalue    ${i}
        ${web获取结果1}    获取WEB参数量    扩展温度高阈值_${i}
        should not be true    ${设置结果1}
        should be equal as numbers    ${协议获取结果1}    ${初始值}
        should be equal as numbers    ${web获取结果1}    ${初始值}
        ${设置结果2}    设置SNMP参数量    extendTemperatureHighThresholdvalue    ${超上限}    ${i}
        sleep    10
        ${协议获取结果2}    获取SNMP数据_单个    extendTemperatureHighThresholdvalue    ${i}
        ${web获取结果2}    获取WEB参数量    扩展温度高阈值_${i}
        should not be true    ${设置结果2}
        should be equal as numbers    ${协议获取结果2}    ${初始值}
        should be equal as numbers    ${web获取结果2}    ${初始值}
        ${设置结果3}    设置SNMP参数量    extendTemperatureHighThresholdvalue    ${缺省值}[0]    ${i}
        sleep    10
        ${协议获取结果3}    获取SNMP数据_单个    extendTemperatureHighThresholdvalue    ${i}
        ${web获取结果3}    获取WEB参数量    扩展温度高阈值_${i}
        should be true    ${设置结果3}
        should be equal as numbers    ${协议获取结果3}    ${缺省值}[0]
        should be equal as numbers    ${web获取结果3}    ${缺省值}[0]
        ${设置结果4}    设置SNMP参数量    extendTemperatureHighThresholdvalue    ${缺省值}[1]    ${i}
        sleep    10
        ${协议获取结果4}    获取SNMP数据_单个    extendTemperatureHighThresholdvalue    ${i}
        ${web获取结果4}    获取WEB参数量    扩展温度高阈值_${i}
        should be true    ${设置结果4}
        should be equal as numbers    ${协议获取结果4}    ${缺省值}[1]
        should be equal as numbers    ${web获取结果4}    ${缺省值}[1]
        ${设置结果5}    设置SNMP参数量    extendTemperatureHighThresholdvalue    ${初始值}    ${i}
        sleep    10
        ${协议获取结果5}    获取SNMP数据_单个    extendTemperatureHighThresholdvalue    ${i}
        ${web获取结果5}    获取WEB参数量    扩展温度高阈值_${i}
        should be true    ${设置结果5}
        should be equal as numbers    ${协议获取结果5}    ${初始值}
        should be equal as numbers    ${web获取结果5}    ${初始值}
    END
    [Teardown]    Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    无

获取和设置扩展温湿度参数（扩展湿度高阈值）
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${缺省值}    获取WEB参数上下限范围    扩展湿度高阈值
    ${可设置范围}    获取WEB参数可设置范围    扩展湿度高阈值
    ${初始值}    获取SNMP数据_单个    extendHumidityHighThresholdvalue
    ${超下限}    Evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    Evaluate    ${可设置范围}[1]+${缺省值}[4]
    ${设置结果1}    设置SNMP参数量    extendHumidityHighThresholdvalue    ${超下限}
    sleep    10
    ${协议获取结果1}    获取SNMP数据_单个    extendHumidityHighThresholdvalue
    ${web获取结果1}    获取WEB参数量    扩展湿度高阈值
    should not be true    ${设置结果1}
    should be equal as numbers    ${协议获取结果1}    ${初始值}
    should be equal as numbers    ${web获取结果1}    ${初始值}
    ${设置结果2}    设置SNMP参数量    extendHumidityHighThresholdvalue    ${超上限}
    sleep    10
    ${协议获取结果2}    获取SNMP数据_单个    extendHumidityHighThresholdvalue
    ${web获取结果2}    获取WEB参数量    扩展湿度高阈值
    ${设置结果3}    设置SNMP参数量    extendHumidityHighThresholdvalue    ${缺省值}[0]
    sleep    10
    ${协议获取结果3}    获取SNMP数据_单个    extendHumidityHighThresholdvalue
    ${web获取结果3}    获取WEB参数量    扩展湿度高阈值
    should be true    ${设置结果3}
    should be equal as numbers    ${协议获取结果3}    ${缺省值}[0]
    should be equal as numbers    ${web获取结果3}    ${缺省值}[0]
    ${设置结果4}    设置SNMP参数量    extendHumidityHighThresholdvalue    ${缺省值}[1]
    sleep    10
    ${协议获取结果4}    获取SNMP数据_单个    extendHumidityHighThresholdvalue
    ${web获取结果4}    获取WEB参数量    扩展湿度高阈值
    should be true    ${设置结果4}
    should be equal as numbers    ${协议获取结果4}    ${缺省值}[1]
    should be equal as numbers    ${web获取结果4}    ${缺省值}[1]
    ${设置结果5}    设置SNMP参数量    extendHumidityHighThresholdvalue    ${初始值}
    sleep    10
    ${协议获取结果5}    获取SNMP数据_单个    extendHumidityHighThresholdvalue
    ${web获取结果5}    获取WEB参数量    扩展湿度高阈值
    should be true    ${设置结果5}
    should be equal as numbers    ${协议获取结果5}    ${初始值}
    should be equal as numbers    ${web获取结果5}    ${初始值}

获取和设置扩展温湿度参数（扩展湿度低阈值）
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${缺省值}    获取WEB参数上下限范围    扩展湿度低阈值
    ${可设置范围}    获取WEB参数可设置范围    扩展湿度低阈值
    ${初始值}    获取SNMP数据_单个    extendHumidityLowThresholdvalue
    ${超下限}    Evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    Evaluate    ${可设置范围}[1]+${缺省值}[4]
    ${设置结果1}    设置SNMP参数量    extendHumidityLowThresholdvalue    ${超下限}
    sleep    10
    ${协议获取结果1}    获取SNMP数据_单个    extendHumidityLowThresholdvalue
    ${web获取结果1}    获取WEB参数量    扩展湿度低阈值
    ${设置结果2}    设置SNMP参数量    extendHumidityLowThresholdvalue    ${超上限}
    sleep    10
    ${协议获取结果2}    获取SNMP数据_单个    extendHumidityLowThresholdvalue
    ${web获取结果2}    获取WEB参数量    扩展湿度低阈值
    should not be true    ${设置结果2}
    should be equal as numbers    ${协议获取结果2}    ${初始值}
    should be equal as numbers    ${web获取结果2}    ${初始值}
    ${设置结果3}    设置SNMP参数量    extendHumidityLowThresholdvalue    ${缺省值}[0]
    sleep    10
    ${协议获取结果3}    获取SNMP数据_单个    extendHumidityLowThresholdvalue
    ${web获取结果3}    获取WEB参数量    扩展湿度低阈值
    should be true    ${设置结果3}
    should be equal as numbers    ${协议获取结果3}    ${缺省值}[0]
    should be equal as numbers    ${web获取结果3}    ${缺省值}[0]
    ${设置结果4}    设置SNMP参数量    extendHumidityLowThresholdvalue    ${缺省值}[1]
    sleep    10
    ${协议获取结果4}    获取SNMP数据_单个    extendHumidityLowThresholdvalue
    ${web获取结果4}    获取WEB参数量    扩展湿度低阈值
    should be true    ${设置结果4}
    should be equal as numbers    ${协议获取结果4}    ${缺省值}[1]
    should be equal as numbers    ${web获取结果4}    ${缺省值}[1]
    ${设置结果5}    设置SNMP参数量    extendHumidityLowThresholdvalue    ${初始值}
    sleep    10
    ${协议获取结果5}    获取SNMP数据_单个    extendHumidityLowThresholdvalue
    ${web获取结果5}    获取WEB参数量    扩展湿度低阈值
    should be true    ${设置结果5}
    should be equal as numbers    ${协议获取结果5}    ${初始值}
    should be equal as numbers    ${web获取结果5}    ${初始值}

获取和设置扩展温湿度参数（扩展温度低阈值）
    [Setup]    Run Keywords    连接CSU
    ...    AND    Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    无
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    FOR    ${i}    IN RANGE    1    9
        Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_${i}
        sleep    10
        ${缺省值}    获取WEB参数上下限范围    扩展温度低阈值_${i}
        ${可设置范围}    获取WEB参数可设置范围    扩展温度低阈值_${i}
        ${初始值}    获取SNMP数据_单个    extendTemperatureLowThresholdvalue    ${i}
        ${超下限}    Evaluate    ${可设置范围}[0]-${缺省值}[4]
        ${超上限}    Evaluate    ${可设置范围}[1]+${缺省值}[4]
        ${设置结果1}    设置SNMP参数量    extendTemperatureLowThresholdvalue    ${超下限}    ${i}
        sleep    10
        ${协议获取结果1}    获取SNMP数据_单个    extendTemperatureLowThresholdvalue    ${i}
        ${web获取结果1}    获取WEB参数量    扩展温度低阈值_${i}
        should not be true    ${设置结果1}
        should be equal as numbers    ${协议获取结果1}    ${初始值}
        should be equal as numbers    ${web获取结果1}    ${初始值}
        ${设置结果2}    设置SNMP参数量    extendTemperatureLowThresholdvalue    ${超上限}    ${i}
        sleep    10
        ${协议获取结果2}    获取SNMP数据_单个    extendTemperatureLowThresholdvalue    ${i}
        ${web获取结果2}    获取WEB参数量    扩展温度低阈值_${i}
        should not be true    ${设置结果2}
        should be equal as numbers    ${协议获取结果2}    ${初始值}
        should be equal as numbers    ${web获取结果2}    ${初始值}
        ${设置结果3}    设置SNMP参数量    extendTemperatureLowThresholdvalue    ${缺省值}[0]    ${i}
        sleep    10
        ${协议获取结果3}    获取SNMP数据_单个    extendTemperatureLowThresholdvalue    ${i}
        ${web获取结果3}    获取WEB参数量    扩展温度低阈值_${i}
        should be true    ${设置结果3}
        should be equal as numbers    ${协议获取结果3}    ${缺省值}[0]
        should be equal as numbers    ${web获取结果3}    ${缺省值}[0]
        ${设置结果4}    设置SNMP参数量    extendTemperatureLowThresholdvalue    ${缺省值}[1]    ${i}
        sleep    10
        ${协议获取结果4}    获取SNMP数据_单个    extendTemperatureLowThresholdvalue    ${i}
        ${web获取结果4}    获取WEB参数量    扩展温度低阈值_${i}
        should be true    ${设置结果4}
        should be equal as numbers    ${协议获取结果4}    ${缺省值}[1]
        should be equal as numbers    ${web获取结果4}    ${缺省值}[1]
        ${设置结果5}    设置SNMP参数量    extendTemperatureLowThresholdvalue    ${初始值}    ${i}
        sleep    10
        ${协议获取结果5}    获取SNMP数据_单个    extendTemperatureLowThresholdvalue    ${i}
        ${web获取结果5}    获取WEB参数量    扩展温度低阈值_${i}
        should be true    ${设置结果5}
        should be equal as numbers    ${协议获取结果5}    ${初始值}
        should be equal as numbers    ${web获取结果5}    ${初始值}
    END
    [Teardown]    Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    无
