*** Settings ***
Suite Setup       设置历史记录最大条数    his_test    3    50
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
WEB导出电池测试记录测试
    连接CSU
    ${数据条数1}    ${第一条数据内容1}    ${最新一条数据内容1}    查询指定历史记录    电池测试记录
    ${导出路径}    导出指定历史记录    电池测试记录    ${datapassword}
    ${数据条数2}    ${第一条数据内容2}    ${最新一条数据内容2}    解析指定历史记录文件    电池测试记录    ${导出路径}
    should be equal    ${第一条数据内容1}[0]    ${第一条数据内容2}[0]
    should be equal    ${第一条数据内容1}[1]    ${第一条数据内容2}[1]
    should be equal    ${第一条数据内容1}[2]    ${第一条数据内容2}[2]
    should be equal    ${第一条数据内容1}[3]    ${第一条数据内容2}[3]
    should be equal    ${第一条数据内容1}[4]    ${第一条数据内容2}[4]
    should be equal    ${第一条数据内容1}[5]    ${第一条数据内容2}[5]
    should be equal    ${第一条数据内容1}[6]    ${第一条数据内容2}[6]
    should be equal    ${第一条数据内容1}[7]    ${第一条数据内容2}[7]
    should be equal    ${第一条数据内容1}[8]    ${第一条数据内容2}[8]
    should be equal    ${第一条数据内容1}[9]    ${第一条数据内容2}[9]
    should be equal    ${第一条数据内容1}[10]    ${第一条数据内容2}[10]
    should be equal    ${数据条数1}    ${数据条数2}
    should be equal    ${最新一条数据内容1}[0]    ${最新一条数据内容2}[0]
    should be equal    ${最新一条数据内容1}[1]    ${最新一条数据内容2}[1]
    should be equal    ${最新一条数据内容1}[2]    ${最新一条数据内容2}[2]
    should be equal    ${最新一条数据内容1}[3]    ${最新一条数据内容2}[3]
    should be equal    ${最新一条数据内容1}[4]    ${最新一条数据内容2}[4]
    should be equal    ${最新一条数据内容1}[5]    ${最新一条数据内容2}[5]
    should be equal    ${最新一条数据内容1}[6]    ${最新一条数据内容2}[6]
    should be equal    ${最新一条数据内容1}[7]    ${最新一条数据内容2}[7]
    should be equal    ${最新一条数据内容1}[8]    ${最新一条数据内容2}[8]
    should be equal    ${最新一条数据内容1}[9]    ${最新一条数据内容2}[9]
    should be equal    ${最新一条数据内容1}[10]    ${最新一条数据内容2}[10]

中文界面下WEB导出电池测试记录测试
    电池管理初始化
    ${缺省值}    获取web参数上下限范围_有单位    测试最长时间
    ${可设置范围}    获取web参数可设置范围    测试最长时间
    ${测试最长时间设置值}    run keyword if    '${缺省值}[3]'== 'Hour'    evaluate    ${可设置范围}[0]+1
    ...    ELSE    evaluate    ${可设置范围}[0]+5
    ${测试最长时间转换后}    run keyword if    '${缺省值}[3]'== 'Hour'    evaluate    ${测试最长时间设置值}*60
    ...    ELSE    set variable    ${测试最长时间设置值}
    设置web参数量    测试最长时间    ${测试最长时间设置值}
    设置web参数量    均充使能    允许
    sleep    5
    ${开始数量}    获取web事件记录数量    电池测试记录
    run keyword if    ${开始数量}>97    删除历史记录    电池测试记录
    sleep    30
    ${开始数量}    获取web事件记录数量    电池测试记录
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动测试
    ${起始时间}    获取系统时间
    ${起始SOC}    获取web实时数据    电池组当前容量比率-1
    ${起始电池电压}    获取web实时数据    电池电压-1
    ${起始电量}    获取web实时数据    电池放电电量-1
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    测试
    ${等待时间}    evaluate    ${测试最长时间转换后}-2
    sleep    ${等待时间}m
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动浮充
    ${持续时间}    获取web实时数据    电池状态持续时间
    ${结束时间}    获取系统时间
    ${结束SOC}    获取web实时数据    电池组当前容量比率-1
    ${结束电池电压}    获取web实时数据    电池电压-1
    ${结束电量}    获取web实时数据    电池放电电量-1
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    sleep    30
    ${结束数量}    获取web事件记录数量    电池测试记录
    @{记录内容}    获取web事件记录最新一条    电池测试记录
    should be true    ${记录内容}[0] == ${结束数量}
    should be true    ${结束数量} == ${开始数量} + 1
    should be equal    '${记录内容}[4]'    '手动'
    should be equal    '${记录内容}[5]'    '手动'
    ${数据条数1}    ${第一条数据内容1}    ${最新一条数据内容1}    查询指定历史记录    电池测试记录
    ${导出路径}    导出指定历史记录    电池测试记录    ${datapassword}
    ${记录列表}    读CSV文档    ${测试用例地址}/file/download/${导出路径}/batt_test_rec.csv
    log    ${记录列表}[0]
    @{记录列表new}    split string    ${记录列表}[0]    ,
    should be equal    ${记录列表new}[0]    产生时间
    should be equal    ${记录列表new}[1]    恢复时间
    should be equal    ${记录列表new}[3]    电池组:记录开始原因
    ${数据条数2}    ${第一条数据内容2}    ${最新一条数据内容2}    解析指定历史记录文件    电池测试记录    ${导出路径}
    should be equal    ${最新一条数据内容2}[3]    手动
    should be equal    ${最新一条数据内容2}[4]    手动

英文界面下WEB导出电池测试记录测试
    电池管理初始化
    ${缺省值}    获取web参数上下限范围_有单位    测试最长时间
    ${可设置范围}    获取web参数可设置范围    测试最长时间
    ${测试最长时间设置值}    run keyword if    '${缺省值}[3]'== 'Hour'    evaluate    ${可设置范围}[0]+1
    ...    ELSE    evaluate    ${可设置范围}[0]+5
    ${测试最长时间转换后}    run keyword if    '${缺省值}[3]'== 'Hour'    evaluate    ${测试最长时间设置值}*60
    ...    ELSE    set variable    ${测试最长时间设置值}
    设置web参数量    测试最长时间    ${测试最长时间设置值}
    设置web参数量    均充使能    允许
    sleep    5
    ${开始数量}    获取web事件记录数量    电池测试记录
    run keyword if    ${开始数量}>97    删除历史记录    电池测试记录
    sleep    30
    ${开始数量}    获取web事件记录数量    电池测试记录
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动测试
    ${起始时间}    获取系统时间
    ${起始SOC}    获取web实时数据    电池组当前容量比率-1
    ${起始电池电压}    获取web实时数据    电池电压-1
    ${起始电量}    获取web实时数据    电池放电电量-1
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    测试
    ${等待时间}    evaluate    ${测试最长时间转换后}-2
    sleep    ${等待时间}m
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动浮充
    ${持续时间}    获取web实时数据    电池状态持续时间
    ${结束时间}    获取系统时间
    ${结束SOC}    获取web实时数据    电池组当前容量比率-1
    ${结束电池电压}    获取web实时数据    电池电压-1
    ${结束电量}    获取web实时数据    电池放电电量-1
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    sleep    30
    ${结束数量}    获取web事件记录数量    电池测试记录
    @{记录内容}    获取web事件记录最新一条    电池测试记录
    should be true    ${记录内容}[0] == ${结束数量}
    should be true    ${结束数量} == ${开始数量} + 1
    should be equal    '${记录内容}[4]'    '手动'
    should be equal    '${记录内容}[5]'    '手动'
    设置语言    EN
    连接CSU
    ${数据条数1}    ${第一条数据内容1}    ${最新一条数据内容1}    查询指定历史记录    电池测试记录
    ${导出路径}    导出指定历史记录    电池测试记录    ${datapassword}
    ${记录列表}    读CSV文档    ${测试用例地址}/file/download/${导出路径}/batt_test_rec.csv
    log    ${记录列表}[0]
    @{记录列表new}    split string    ${记录列表}[0]    ,
    should be equal    ${记录列表new}[0]    start_time
    should be equal    ${记录列表new}[1]    end_time
    should be equal    ${记录列表new}[3]    Battery Group:Record Start Cause
    ${数据条数2}    ${第一条数据内容2}    ${最新一条数据内容2}    解析指定历史记录文件    电池测试记录    ${导出路径}
    should be equal    ${最新一条数据内容2}[3]    Manual
    should be equal    ${最新一条数据内容2}[4]    Manual
    [Teardown]    run keywords    设置语言    CN
    ...    AND    连接CSU
