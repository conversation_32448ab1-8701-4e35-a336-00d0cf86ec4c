*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
获取温湿度传感器数字量1
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    控制子工具运行停止    DMU_WS312M1    关闭
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    温湿度传感器通讯状态    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    温湿度传感器在位状态    在位
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    温湿度传感器工作状态    通讯断
    ${snmp获取值1}    获取SNMP数据_单个    humitureSensorCommunicateStatusvalue
    should be equal as strings    ${snmp获取值1}    1
    ${snmp获取值1}    获取SNMP数据_单个    humitureSensorExistStatusvalue
    should be equal as strings    ${snmp获取值1}    1
    ${snmp获取值1}    获取SNMP数据_单个    humitureSensorWorkStatusvalue
    should be equal as strings    ${snmp获取值1}    3
    控制子工具运行停止    DMU_WS312M1    开启
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    温湿度传感器通讯状态    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    温湿度传感器在位状态    在位
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    温湿度传感器工作状态    正常
    ${snmp获取值1}    获取SNMP数据_单个    humitureSensorCommunicateStatusvalue
    should be equal as strings    ${snmp获取值1}    0
    ${snmp获取值1}    获取SNMP数据_单个    humitureSensorExistStatusvalue
    should be equal as strings    ${snmp获取值1}    1
    ${snmp获取值1}    获取SNMP数据_单个    humitureSensorWorkStatusvalue
    should be equal as strings    ${snmp获取值1}    1
    [Teardown]    Run Keywords    控制子工具运行停止    DMU_WS312M1    开启
    ...    AND    sleep    3min
