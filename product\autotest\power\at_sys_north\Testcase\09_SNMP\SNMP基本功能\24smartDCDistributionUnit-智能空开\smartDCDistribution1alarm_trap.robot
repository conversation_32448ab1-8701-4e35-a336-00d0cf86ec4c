*** Settings ***
Default Tags      trap
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
snmp批量获取SDDU告警量trap测试
    写入CSV文档    SDU2数字量和告警量获取测试    信号名称    信号值    结果    备注
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=智能直流配电单元
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SDDU2排除告警量信号}    ${排除列表}    1
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    smartDCDistributionUnit    alarm    True
    @{SDDU2告警量数据}    create list
    FOR    ${i}    IN    @{snmp待测}
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        ${信号序号}    BuiltIn.Convert To Integer    ${信号序号}
        Run Keyword IF    ${信号序号}==1    Append to List    ${SDDU2告警量数据}    ${i}
        Run Keyword IF    ${信号序号}==5    Append to List    ${SDDU2告警量数据}    ${i}
        Run Keyword IF    ${信号序号}==9    Append to List    ${SDDU2告警量数据}    ${i}
        Run Keyword IF    ${信号序号}==13    Append to List    ${SDDU2告警量数据}    ${i}
    END
    FOR    ${i}    IN    @{SDDU2告警量数据}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    signal_node
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        ${信号序号1}    BuiltIn.Convert To Integer    ${信号序号}
        ${呼叫命令}    Evaluate    (${信号序号1}-1)//4+1
        ${命令名称}    Run Keyword IF    "${呼叫命令}"=="1"    Set Variable    呼叫
        ...    ELSE    Set Variable    呼叫${呼叫命令}
        Run Keyword And Continue On Failure    南向RS485子设备告警量产生封装判断结果    snmp    IntelAirSwitExtend    ${信号名称}    ${命令名称}    1    SDU2数字量和告警量获取测试    智能直流配电单元    ${节点名称}    ${信号序号}    null    null    null    null
        Run Keyword And Continue On Failure    南向RS485子设备告警量恢复封装判断结果    snmp    IntelAirSwitExtend    ${信号名称}    ${命令名称}    0    SDU2数字量和告警量获取测试    智能直流配电单元    ${节点名称}    ${信号序号}    null    null    null    null
    END

SDDU通讯中断告警
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    设置子工具个数    IntelAirSwitExtend    1
    wait until keyword succeeds    10m    1    判断告警存在    SDDU通讯中断-2
    ${snmp英文名}    获取snmp单个告警英文名    SDDU通讯中断_2
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    设置子工具个数    IntelAirSwitExtend    2
    wait until keyword succeeds    10m    1    判断告警不存在    SDDU通讯中断-2
    ${snmp英文名}    获取snmp单个告警英文名    SDDU通讯中断_2
    ${告警消失}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警消失}
    [Teardown]    Run keywords    设置子工具个数    IntelAirSwitExtend    2
    ...    AND    sleep    5min
