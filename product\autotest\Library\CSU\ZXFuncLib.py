#-*- coding:utf-8 -*-
import time,os
import chardet

#解决中文输出的问题？？中文乱码
def SaveToCSV(dataList,fileName):
    with open(fileName, 'a', newline='') as csvfile:
        writer  = csv.writer(csvfile)
        for row in dataList:
            writer.writerow(row)

def encode_utf8(string):
    return string.encode('utf-8')
def decode_utf8(string):
    return unicode(string, encoding='utf-8')
def test_file_code(fileName):
    f = open(fileName,'r')
    data = f.read()
    code_info = chardet.detect( data ) #<type 'dict'>
    f.close()
    return code_info.get('language'), code_info.get( 'encoding' )

def ZXCSVToDict(FileName):
    os.chdir(os.path.split(os.path.realpath(__file__))[0])#回到当前脚本目录
    print u'转换CSV为Dict'
    f = open(FileName,'r')
    csvlist = f.readlines()
    f.close()
    retDict = {}
    #print len(csvlist)
    for i in range(0,len(csvlist)):
        tempList = csvlist[i].split(',')
        #print tempList
        retDict[tempList[0].decode("utf-8")]=tempList[1].replace('\n','') #.decode("utf-8").encode("gbk")
    import robot.utils
    dtrobot = robot.utils.DotDict(retDict) #该方法是dict转为robot格式的dict
    return  dtrobot

def ZXGetFromList(DataList,index=0):
    return DataList[index]

def ZXPrintData(data):
    print u'类型为：', type(data)
    print u'Dict数据：\n', data

def ZXDevice_List_ListFromDict(ListOfDict):
    #将 list中的dict转为list数据，保存所有的sid到list中
    retList = []
    for i in ListOfDict:
        retList.append(i['sid'])
    return retList

def ZXToJson(Data):
    import json
    return json.loads(Data)

def ZXList_ListFromList(ListData):
    #_Assemble_Paraval_With_Multi_SIDs
    retList = []
    for i in ListData:
        tmpDict = {}
        tmpDict['sid'] = i
        retList.append(tmpDict)
    return retList

def ZXList_Devdata_value_GET(ListData):
    #DeviceMgr.robot/Devdata_value_GET
    retList = []
    for i in ListData:
        tmpDict = {}
        tmpDict['sid'] = i
        tmpDict['value'] = ''
        retList.append(tmpDict)
    return retList

def ZXList_SearchSignalSids(ListData):
    # 将查询设备信号量SID列表 中的for循环替换为对应函数
    retList = []
    for i in ListData:
        retList.append(i['sid'])
    return retList

def ZXList_SearchSID(device_type,device_name,device_list):
    #查询对应名称的设备SID
    #list [ {"device type": "CSU", "device name": "CSU", "sid": "281543696187392"}}
    retList = [] #type,name,device_sid,length
    _type = None
    _name = None
    _device_sid = None
    #_length = None
    for item in device_list:
        _type = item['device type']
        _name = item['device name']
        _device_sid = item['sid']
        if((device_type == _type) and (device_name==_name )):
            break
    return _device_sid

if __name__ == '__main__':

#    print time.asctime( time.localtime(time.time()) )
    ret = ZXCSVToDict('TranslationMap.csv')
    #print ret#.keys()[0])
