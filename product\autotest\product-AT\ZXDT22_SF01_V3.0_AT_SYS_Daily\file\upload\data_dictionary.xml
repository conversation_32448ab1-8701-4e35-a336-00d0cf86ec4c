<?xml version='1.0' encoding='UTF-8'?>
<dictionary>
	<devices type="string64">
		<device name="CSU" value="1" maxnum="1"/>
		<device name="Power System" value="2" maxnum="1"/>
		<device name="System AC Input" value="3" maxnum="1"/>
		<device name="AC Distribution" value="4" maxnum="1"/>
		<device name="Mains" value="5" maxnum="2"/>
		<device name="Mains Group" value="6" maxnum="1"/>
		<device name="Rectifier" value="7" maxnum="40"/>
		<device name="Rectifier Group" value="8" maxnum="1"/>
		<device name="System Running Environment" value="9" maxnum="1"/>
		<device name="DC Distribution" value="10" maxnum="1"/>
		<device name="Single Group Battery" value="11" maxnum="4"/>
		<device name="Battery Group" value="12" maxnum="1"/>
		<device name="DC Load" value="13" maxnum="1"/>
		<device name="VRLA Battery" value="14" maxnum="1"/>
	</devices>
	<signals type="string64">
		<signal access="r" name="analog data" value="1" short_name="ANA"/>
		<signal access="r" name="digital data" value="2" short_name="DIG"/>
		<signal access="r" name="alarm" value="3" short_name="ALM"/>
		<signal access="rw" name="control" value="4" short_name="CTL"/>
		<signal access="rw" name="parameter" value="5" short_name="PARA"/>
		<signal access="r" name="record data" value="6" short_name="REC"/>
		<signal access="r" name="stastic data" value="7" short_name="STAT"/>
		<signal access="r" name="device info" value="8" short_name="DEVINFO"/>
	</signals>
	<para_types type="string64">
		<para_type value="1" name="Factory Config Para"/>
		<para_type value="2" name="User Para"/>
		<para_type value="3" name="Site Config Para"/>
	</para_types>
	<almlevels type="string64">
		<almlevel value="0" name="Mask"/>
		<almlevel value="1" name="Critical"/>
		<almlevel value="2" name="Major"/>
		<almlevel value="3" name="Minor"/>
		<almlevel value="4" name="Warning"/>
	</almlevels>
	<macros>
		<macro name="AC_OUTSWITCH_NUM">1</macro>
		<macro name="AC_PHASE_NUM">3</macro>
		<macro name="ACEM_NUM">3</macro>
		<macro name="ACMU_P_NUM">1</macro>
		<macro name="AIR_COND_NUM">2</macro>
		<macro name="BATT_BLOCK_NUM">4</macro>
		<macro name="BATT_CELL_NUM">24</macro>
		<macro name="BATT_LI_CELL_PER_GROUP_NUM">16</macro>
		<macro name="BATT_LI_CELLTEMP_PER_GROUP_NUM">8</macro>
		<macro name="BATT_LI_EQUALTEMP_PER_GROUP_NUM">3</macro>
		<macro name="BATT_NUM">4</macro>
		<macro name="BCU_NUM">4</macro>
		<macro name="BLVD_LOAD_NUM">8</macro>
		<macro name="BMU_NUM">4</macro>
		<macro name="BRU_MAC_ADR_LEN">4</macro>
		<macro name="CABINET_DC_AIR_COND_NUM">4</macro>
		<macro name="CELL_PER_GROUP_TB_NUM">13</macro>
		<macro name="CSU_P_NUM">1</macro>
		<macro name="CSU_SOFT_LEN">20</macro>
		<macro name="DC_BREAKER_NUM">3</macro>
		<macro name="DC_DC_PNUM">1</macro>
		<macro name="DC_FUSE_NUM">12</macro>
		<macro name="DC_LOAD_NUM">12</macro>
		<macro name="DC_LOOP_EXT_NUM">1</macro>
		<macro name="DC_LOOP_NUM">8</macro>
		<macro name="DCEM_CHANNEL_NUM">8</macro>
		<macro name="DCEM_NUM">2</macro>
		<macro name="DCEM_USER_NUM">4</macro>
		<macro name="DCMU_P_NUM">1</macro>
		<macro name="DISCHG_STEP_NUM">10</macro>
		<macro name="ENV_P_NUM">1</macro>
		<macro name="FB_BAT_NUM">16</macro>
		<macro name="FB_BATT_FELI_TEMP_NUM">4</macro>
		<macro name="FBMU_NUM">4</macro>
		<macro name="FBMU_SOFT_VER_LEN">20</macro>
		<macro name="GCP_DI_NUM">7</macro>
		<macro name="GCP_DO_NUM">7</macro>
		<macro name="GEN_NUM">2</macro>
		<macro name="GMU_SOFT_LEN">20</macro>
		<macro name="ID2_BYTE_NUM">2</macro>
		<macro name="IMPORT_FAN_NUM">1</macro>
		<macro name="IN_RELAY_NAME_LEN">20</macro>
		<macro name="IN_RELAY_NUM">8</macro>
		<macro name="LLVD1_LOAD_NUM">8</macro>
		<macro name="LLVD2_LOAD_NUM">8</macro>
		<macro name="LLVD3_LOAD_NUM">1</macro>
		<macro name="LONG_LINK_NUM">1</macro>
		<macro name="MAJ_CURR_NUM">2</macro>
		<macro name="OUT_RELAY_NUM">14</macro>
		<macro name="PERIOD_NUM">3</macro>
		<macro name="PU_NUM">20</macro>
		<macro name="PU_P_NUM">1</macro>
		<macro name="RECIPENTNUM">3</macro>
		<macro name="RESISTANCE_NUM">10</macro>
		<macro name="SC_COMM_PORT_NUM">1</macro>
		<macro name="SC_IP_PORT_NUM">3</macro>
		<macro name="SC_NUM">4</macro>
		<macro name="SDU_NUM">1</macro>
		<macro name="SIM_NUM">2</macro>
		<macro name="SINK_NUM">3</macro>
		<macro name="SM_SOFT_LEN">6</macro>
		<macro name="SMR_ID_LEN">11</macro>
		<macro name="SMR_NUM">40</macro>
		<macro name="SMR_P_NUM">1</macro>
		<macro name="SMR_SOFT_LEN">6</macro>
		<macro name="SMS_MONITOR_NUM">3</macro>
		<macro name="STRING_32_LEN">32</macro>
		<macro name="SUB_DEVICE_NUM">25</macro>
		<macro name="SYSNAME_LEN">30</macro>
		<macro name="TB_BAT_NUM">16</macro>
		<macro name="TENANT_NUM">4</macro>
		<macro name="TOT_BATT_12V_CELL_NUM">480</macro>
		<macro name="TOT_BATT_2V_CELL_NUM">7680</macro>
		<macro name="TOT_FB_BATT_FELI_TEMP_NUM">64</macro>
		<macro name="TOT_FB_BATT_LI_CELL_PER_GROUP_NUM">256</macro>
		<macro name="TOT_LI_BATT_CELL_NUM">64</macro>
		<macro name="TOT_LI_BATT_EQUALTEM_NUM">12</macro>
		<macro name="TOT_LI_BATT_TEMP_NUM">32</macro>
		<macro name="TOT_TB_BATT_EQUALTEM_NUM">48</macro>
		<macro name="TOT_TB_BATT_LI_CELL_PER_GROUP_NUM">208</macro>
		<macro name="TOTAL_BATT_LI_NUM">16</macro>
		<macro name="TOTAL_BATT_NUM">8</macro>
		<macro name="TOTAL_DISCHG_STEP_NUM">40</macro>
		<macro name="TYPE_STRING_24_LEN">24</macro>
		<macro name="WT_ID_LEN">11</macro>
		<macro name="WT_NUM">2</macro>
		<macro name="WT_P_NUM">1</macro>
		<macro name="WT_SOFT_LEN">6</macro>
		<macro name="MAINS_NUM">2</macro>
		<macro name="SHUNT_NUM">4</macro>
	</macros>
	<operators>
		<operator name="&gt;=">0</operator>
		<operator name="&lt;=">1</operator>
	</operators>
	<data_definition>
		<sid>sid</sid>
		<attr.r>
			<full_name>string256</full_name>
			<short_name>string64</short_name>
			<signal_type>int</signal_type>
			<unit>string32</unit>
			<dimension max="5">int</dimension>
			<data_type>int</data_type>
			<default_value>double</default_value>
			<min_value>double</min_value>
			<max_value>double</max_value>
			<precision>int</precision>
			<step>int</step>
			<para_type>int</para_type>
			<convention>longlong</convention>
			<constraint>longlong</constraint>
		</attr.r>
		<attr.rw>
			<alm_level>int</alm_level>
			<alm_threshold>double</alm_threshold>
			<alm_delay>int</alm_delay>
			<alm_relay>int</alm_relay>
			<alm_backlash>double</alm_backlash>
			<period>int</period>
			<absolute_threshold>float</absolute_threshold>
			<percent_threshold>float</percent_threshold>
		</attr.rw>
		<attr.show>
			<data>int</data>
			<web>int</web>
			<gui>int</gui>
		</attr.show>
	</data_definition>
	<attr_attr_types>
		<attr_attr_type name="enumeration">0</attr_attr_type>
		<attr_attr_type name="range">1</attr_attr_type>
	</attr_attr_types>
	<rw_template>
		<alm_level>
			<alm_level_type>0</alm_level_type>
			<alm_level_value>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</alm_level_value>
		</alm_level>
		<alm_threshold>
			<alm_threshold_type>1</alm_threshold_type>
			<alm_threshold_value>"must have alm_threshold_values"</alm_threshold_value>
		</alm_threshold>
		<alm_delay>
			<alm_delay_type>1</alm_delay_type>
			<alm_delay_value>0;28800</alm_delay_value>
		</alm_delay>
		<alm_relay>
			<alm_relay_type>1</alm_relay_type>
			<alm_relay_value>"must have alm_relay_values"</alm_relay_value>
		</alm_relay>
		<alm_backlash>
			<alm_backlash_type>1</alm_backlash_type>
			<alm_backlash_value>"must have alm_backlash"</alm_backlash_value>
		</alm_backlash>
		<period>
			<period_type>1</period_type>
			<period_value>0;28800</period_value>
		</period>
		<absolute_threshold>
			<absolute_threshold_type>1</absolute_threshold_type>
			<absolute_threshold_value>"must have absolute_threshold_values"</absolute_threshold_value>
		</absolute_threshold>
		<percent_threshold>
			<percent_threshold_type>1</percent_threshold_type>
			<percent_threshold_value>0;100</percent_threshold_value>
		</percent_threshold>
	</rw_template>
	<device name="CSU">
		<sid value="0x1001010010001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
			</attr.show>
			<attr.s>
				<ID2>0x35</ID2>
				<dev_macro>DEV_CSU</dev_macro>
				<struct_var_name>csu_cpu_usage_rate</struct_var_name>
				<ID1>0x00</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>100</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>CPU Usage Rate</short_name>
					<full_name>CPU Usage Rate</full_name>
				</name>
				<unit>%</unit>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0x1001010020001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
			</attr.show>
			<attr.s>
				<ID2>0x36</ID2>
				<dev_macro>DEV_CSU</dev_macro>
				<struct_var_name>csu_mem_usage_rate</struct_var_name>
				<ID1>0x00</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>100</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Mem.Usage Rate</short_name>
					<full_name>Memory Usage Rate</full_name>
				</name>
				<unit>%</unit>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0x1001020010001">
			<attr.s>
				<dev_macro>DEV_CSU</dev_macro>
				<struct_var_name>csu_fault_status</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>CSU Fault</short_name>
					<full_name>CSU Fault</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
			</attr.show>
		</sid>
		<sid value="0x1001020020001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<dev_macro>DEV_CSU</dev_macro>
				<struct_var_name>csu_in_relay_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>IN_RELAY_NUM</dimension>
				</dimensions>
				<name>
					<short_name>Input Rly.State</short_name>
					<full_name>Input Relay State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x1001030010001">
			<attr.s>
				<ID2>0x12</ID2>
				<dev_macro>DEV_CSU</dev_macro>
				<struct_var_name>csu_disable_alm</struct_var_name>
				<ID1>0x02</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>All Alarm Blocked</short_name>
					<full_name>All Alarm Blocked</full_name>
				</name>
				<convention>1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_level_value>1:Critical;2:Major;3:Minor;4:Warning</alm_level_value>
					<alm_level>1</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x1001030020001">
			<attr.s>
				<ID2>0x44</ID2>
				<dev_macro>DEV_CSU</dev_macro>
				<struct_var_name>csu_mac_addr_not_set</struct_var_name>
				<ID1>0x02</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>MAC Not Set</short_name>
					<full_name>MAC Address Not Set</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x1001030030001">
			<attr.s>
				<ID2>0xFE</ID2>
				<dev_macro>DEV_CSU</dev_macro>
				<struct_var_name>csu_in_relay_alm</struct_var_name>
				<ID1>0x02</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>IN_RELAY_NUM</dimension>
				</dimensions>
				<name>
					<short_name>Input Rly.Alm</short_name>
					<full_name>Input Relay Alarm</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_level>0</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x1001030040001">
			<attr.s>
				<dev_macro>DEV_CSU</dev_macro>
				<struct_var_name>csu_cpu_usage_rate_high</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>CPU Usage Rate high</short_name>
					<full_name>CPU Usage Rate High Alarm</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_threshold_type>1</alm_threshold_type>
					<alm_delay>30</alm_delay>
					<alm_level>0</alm_level>
					<alm_backlash_type>1</alm_backlash_type>
					<alm_threshold_value>0;100</alm_threshold_value>
					<alm_backlash_value>-20;0</alm_backlash_value>
					<alm_backlash>-10</alm_backlash>
					<alm_threshold>60</alm_threshold>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x1001030050001">
			<attr.s>
				<dev_macro>DEV_CSU</dev_macro>
				<struct_var_name>csu_mem_usage_rate_high</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>memory Usage Rate High</short_name>
					<full_name>memory Usage Rate High Alarm</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_threshold_type>1</alm_threshold_type>
					<alm_delay>30</alm_delay>
					<alm_level>0</alm_level>
					<alm_backlash_type>1</alm_backlash_type>
					<alm_threshold_value>0;100</alm_threshold_value>
					<alm_backlash_value>-20;0</alm_backlash_value>
					<alm_backlash>-10</alm_backlash>
					<alm_threshold>60</alm_threshold>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x1001040010001">
			<attr.s>
				<ID2>0x10</ID2>
				<dev_macro>DEV_CSU</dev_macro>
				<struct_var_name>csu_ctrl_alm_disable</struct_var_name>
				<ID1>0x04</ID1>
			</attr.s>
			<attr.r>
				<signal_type>4</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Total Alm. Dis</short_name>
					<full_name>Total Alarm Disable</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x1001040020001">
			<attr.s>
				<ID2>0x11</ID2>
				<dev_macro>DEV_CSU</dev_macro>
				<struct_var_name>csu_ctrl_alm_enable</struct_var_name>
				<ID1>0x04</ID1>
			</attr.s>
			<attr.r>
				<signal_type>4</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Total Alm. En</short_name>
					<full_name>Total Alarm Enable</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x1001040030001">
			<attr.s>
				<ID2>0x14</ID2>
				<dev_macro>DEV_CSU</dev_macro>
				<struct_var_name>csu_config_sm_bus_state</struct_var_name>
				<ID1>0x04</ID1>
			</attr.s>
			<attr.r>
				<signal_type>4</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>CAN Dev.Stat</short_name>
					<full_name>CAN Bus Device Statistic</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x1001040040001">
			<attr.s>
				<ID2>0x15</ID2>
				<dev_macro>DEV_CSU</dev_macro>
				<struct_var_name>csu_config_rs485_bus_state</struct_var_name>
				<ID1>0x04</ID1>
			</attr.s>
			<attr.r>
				<signal_type>4</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>RS485 Dev.Stat</short_name>
					<full_name>RS485 Bus Device Statistic</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x1001040050001">
			<attr.s>
				<ID2>0x54</ID2>
				<dev_macro>DEV_CSU</dev_macro>
				<struct_var_name>csu_ctrl_open_ssh</struct_var_name>
				<ID1>0x04</ID1>
			</attr.s>
			<attr.r>
				<signal_type>4</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Open SSH</short_name>
					<full_name>Open SSH</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x1001040060001">
			<attr.s>
				<ID2>0x55</ID2>
				<dev_macro>DEV_CSU</dev_macro>
				<struct_var_name>csu_ctrl_close_ssh</struct_var_name>
				<ID1>0x04</ID1>
			</attr.s>
			<attr.r>
				<signal_type>4</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Close SSH</short_name>
					<full_name>Close SSH</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x1001050010001">
			<attr.s>
				<ID2>0xC5</ID2>
				<dev_macro>DEV_CSU</dev_macro>
				<struct_var_name>csu_Log_save_interval</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<para_type>2</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>1440</default_value>
					<data_type>int</data_type>
					<max_value>1440</max_value>
					<min_value>5</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<name>
					<short_name>Log Save Inter</short_name>
					<full_name>Log Save Interval</full_name>
				</name>
				<unit>Min</unit>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x1001050020001">
			<attr.s>
				<dev_macro>DEV_CSU</dev_macro>
				<struct_var_name>csu_cpu_usage_high_thre</struct_var_name>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>80</default_value>
					<data_type>int</data_type>
					<max_value>100</max_value>
					<min_value>10</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>%</unit>
				<name>
					<short_name>CPU Usage High Thre.</short_name>
					<full_name>CPU Usage Rate High Threshold</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x1001050030001">
			<attr.s>
				<dev_macro>DEV_CSU</dev_macro>
				<struct_var_name>csu_mem_usage_high_thre</struct_var_name>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>80</default_value>
					<data_type>int</data_type>
					<max_value>100</max_value>
					<min_value>10</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>%</unit>
				<name>
					<short_name>Memory Usage High Thre.</short_name>
					<full_name>Memory Usage Rate High Threshold</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x1001050040001">
			<attr.s>
				<dev_macro>DEV_CSU</dev_macro>
				<struct_var_name>csu_pos_alm_enable</struct_var_name>
			</attr.s>
			<attr.r>
				<para_type>3</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<step>1</step>
					<min_value>0</min_value>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Pos Alm Enbale</short_name>
					<full_name>Positive Alarm Enable</full_name>
				</name>
				<convention>0:Disable;1:Enable</convention>
			</attr.r>
		</sid>
		<sid value="0x1001080010001">
			<attr.s>
				<ID2>0x0D</ID2>
				<dev_macro>DEV_CSU</dev_macro>
				<struct_var_name>csu_serial_number</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>8</signal_type>
				<values>
					<data_type>string32</data_type>
				</values>
				<name>
					<short_name>SN</short_name>
					<full_name>Serial Number</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x1001080020001">
			<attr.s>
				<ID2>0x79</ID2>
				<dev_macro>DEV_CSU</dev_macro>
				<struct_var_name>csu_soft_platform_version</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>8</signal_type>
				<values>
					<data_type>string32</data_type>
				</values>
				<name>
					<short_name>Platform Version</short_name>
					<full_name>CSU Software Platform Version</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x1001080030001">
			<attr.s>
				<ID2>0x80</ID2>
				<dev_macro>DEV_CSU</dev_macro>
				<struct_var_name>csu_soft_ware_name</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>8</signal_type>
				<values>
					<data_type>string32</data_type>
				</values>
				<name>
					<short_name>SoftWare Name</short_name>
					<full_name>SoftWare Name</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x1001080040001">
			<attr.s>
				<ID2>0xF0</ID2>
				<dev_macro>DEV_CSU</dev_macro>
				<struct_var_name>csu_boot_version</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>8</signal_type>
				<values>
					<default_value>1.3.4</default_value>
					<data_type>string32</data_type>
				</values>
				<name>
					<short_name>Boot Version</short_name>
					<full_name>Boot Version</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x1001080050001">
			<attr.s>
				<ID2>0xF1</ID2>
				<dev_macro>DEV_CSU</dev_macro>
				<struct_var_name>csu_Kernel_version</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>8</signal_type>
				<values>
					<default_value>2.6.27</default_value>
					<data_type>string32</data_type>
				</values>
				<name>
					<short_name>Kernel Version</short_name>
					<full_name>Kernel Version</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x1001080060001">
			<attr.s>
				<ID2>0xF3</ID2>
				<dev_macro>DEV_CSU</dev_macro>
				<struct_var_name>csu_soft_ware_version</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>8</signal_type>
				<values>
					<data_type>string32</data_type>
				</values>
				<name>
					<short_name>Software Version</short_name>
					<full_name>Software Version</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x1001080070001">
			<attr.s>
				<ID2>0xFC</ID2>
				<dev_macro>DEV_CSU</dev_macro>
				<struct_var_name>csu_manuftory_name</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>8</signal_type>
				<values>
					<default_value>ZTE</default_value>
					<data_type>string32</data_type>
				</values>
				<name>
					<short_name>Manufactory Name</short_name>
					<full_name>Manufactory Name</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x1001080080001">
			<attr.s>
				<ID2>0xFF</ID2>
				<dev_macro>DEV_CSU</dev_macro>
				<struct_var_name>csu_soft_release_date</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>8</signal_type>
				<values>
					<data_type>string32</data_type>
				</values>
				<name>
					<short_name>Soft.Release Date</short_name>
					<full_name>Software Release Date</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x1001080090001">
			<attr.s>
				<dev_macro>DEV_CSU</dev_macro>
				<struct_var_name>csu_soft_platform_release_date</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>8</signal_type>
				<values>
					<data_type>string32</data_type>
				</values>
				<name>
					<short_name>platform release date</short_name>
					<full_name>platform release date</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x10010800a0001">
			<attr.s>
				<dev_macro>DEV_CSU</dev_macro>
				<struct_var_name>csu_uib_ver</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>8</signal_type>
				<values>
					<data_type>string32</data_type>
				</values>
				<name>
					<short_name>uib version</short_name>
					<full_name>uib version</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x10010800b0001">
			<attr.s>
				<dev_macro>DEV_CSU</dev_macro>
				<struct_var_name>csu_iddb_ver</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>8</signal_type>
				<values>
					<data_type>string32</data_type>
				</values>
				<name>
					<short_name>iddb version</short_name>
					<full_name>iddb version</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x10010800c0001">
			<attr.s>
				<dev_macro>DEV_CSU</dev_macro>
				<struct_var_name>csu_mac_addr</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>8</signal_type>
				<values>
					<data_type>string32</data_type>
				</values>
				<name>
					<short_name>MAC addr.</short_name>
					<full_name>MAC Address</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x10010800d0001">
			<attr.s>
				<dev_macro>DEV_CSU</dev_macro>
				<struct_var_name>csu_uib_release_date</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>8</signal_type>
				<values>
					<data_type>string32</data_type>
				</values>
				<name>
					<short_name>UIB Release Date</short_name>
					<full_name>UIB Release Date</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x10010800e0001">
			<attr.s>
				<dev_macro>DEV_CSU</dev_macro>
				<struct_var_name>csu_iddb_release_date</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>8</signal_type>
				<values>
					<data_type>string32</data_type>
				</values>
				<name>
					<short_name>IDDB Release Date</short_name>
					<full_name>IDDB Release Date</full_name>
				</name>
			</attr.r>
		</sid>
	</device>
	<device name="Power System">
		<sid value="0x2001010010001">
			<attr.s>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_dc_vol</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>48</default_value>
					<max_value>60</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>DC Voltage</short_name>
					<full_name>DC Voltage</full_name>
				</name>
				<unit>V</unit>
			</attr.r>
		</sid>
		<sid value="0x2001010020001">
			<attr.s>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_load_total_cur</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>3000</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>Load Current</short_name>
					<full_name>Load Total Current</full_name>
				</name>
				<unit>A</unit>
			</attr.r>
		</sid>
		<sid value="0x2001010030001">
			<attr.s>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_load_total_pow</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>600</max_value>
					<min_value>0</min_value>
					<precision>2</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>Load Total Pwr.</short_name>
					<full_name>DC Load Total Power</full_name>
				</name>
				<unit>kW</unit>
			</attr.r>
		</sid>
		<sid value="0x2001010040001">
			<attr.s>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_bat_totalcur</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<data_type>float</data_type>
					<max_value>3000</max_value>
					<min_value>-3000</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>A</unit>
				<name>
					<short_name>Batt.Total Curr.</short_name>
					<full_name>Battery Total Current</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x2001010050001">
			<attr.s>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_bat_manager_time</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>65535</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>Min</unit>
				<name>
					<short_name>Batt.Stat.Dura.</short_name>
					<full_name>Battery Status Duration</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x2001010060001">
			<attr.s>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_smrs_total_cur</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>3000</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>Smr Total Curr</short_name>
					<full_name>SMR Total Output Current</full_name>
				</name>
				<unit>A</unit>
			</attr.r>
		</sid>
		<sid value="0x2001010070001">
			<attr.s>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_smrs_set_volt_out</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>53.5</default_value>
					<max_value>58</max_value>
					<min_value>42</min_value>
					<precision>2</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>SMR Set Volt</short_name>
					<full_name>SMR Setting Voltage</full_name>
				</name>
				<unit>V</unit>
			</attr.r>
		</sid>
		<sid value="0x2001010080001">
			<attr.s>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_smrs_set_cur_limit</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>1000</default_value>
					<max_value>1000</max_value>
					<min_value>50</min_value>
					<precision>0</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>SMR CL Rate</short_name>
					<full_name>SMR Setting Current Limit Rate</full_name>
				</name>
				<unit>‰</unit>
			</attr.r>
		</sid>
		<sid value="0x2001010090001">
			<attr.s>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_smrs_working_num</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>SMR_NUM</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Work Num</short_name>
					<full_name>SMR Working Number</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x20010100a0001">
			<attr.s>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_smrs_online_num</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>SMR_NUM</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Online Num</short_name>
					<full_name>SMR Online Number</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x20010100b0001">
			<attr.s>
				<optional_attribute>env_survey_enabled</optional_attribute>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_env_temp</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>25</default_value>
					<max_value>100</max_value>
					<min_value>-40</min_value>
					<precision>0</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>Environment Temp</short_name>
					<full_name>Environment Temperature</full_name>
				</name>
				<unit>℃</unit>
			</attr.r>
		</sid>
		<sid value="0x20010100c0001">
			<attr.s>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_phase_volt</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>220</default_value>
					<max_value>300</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>AC_PHASE_NUM</dimension>
				</dimensions>
				<unit>V</unit>
				<name>
					<short_name>System AC Voltage</short_name>
					<full_name>System AC Voltage</full_name>
				</name>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0x20010100d0001">
			<attr.s>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_phase_curr</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>600</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>AC_PHASE_NUM</dimension>
				</dimensions>
				<unit>A</unit>
				<name>
					<short_name>System AC Current</short_name>
					<full_name>System AC Current</full_name>
				</name>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0x2001020010001">
			<attr.s>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_bat_manage_status</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>6</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Batt Manag Status</short_name>
					<full_name>Battery Management Status</full_name>
				</name>
				<convention>0:Float;1:Equal;2:Test;3:PowerOff;4:Detect;5:Transition;6:Charge</convention>
			</attr.r>
		</sid>
		<sid value="0x2001020020001">
			<attr.s>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_acin_source</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>8</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>AC Source</short_name>
					<full_name>AC Power Source</full_name>
				</name>
				<convention>0:Null;2:DG1;3:DG2;4:AC1;5:AC2</convention>
			</attr.r>
		</sid>
		<sid value="0x2001020030001">
			<attr.s>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_smrs_save_mode</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>6</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>AC Save Energy Status</short_name>
					<full_name>AC Save Energy Status</full_name>
				</name>
				<convention>0:Safe;1:Free Mode;2:Auto NonSave;3:Auto Save;4:Temp NonSave;5:Perm NonSave;6:Manual Detect</convention>
			</attr.r>
		</sid>
		<sid value="0x2001020040001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
			<attr.s>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_emerg_light_status</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Emerg.Light Status</short_name>
					<full_name>Emergency Light Status</full_name>
				</name>
				<convention>0:Off;1:On</convention>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0x2001030010001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x14</ID2>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_sys_expansion_alm</struct_var_name>
				<ID1>0x02</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Sys.Expansion Alm</short_name>
					<full_name>System Expansion Alarm</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x2001030020001">
			<attr.s>
				<ID2>0x16</ID2>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_overload_alm</struct_var_name>
				<ID1>0x02</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Sys.OverLoad Alm</short_name>
					<full_name>System OverLoad Alarm</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_backlash>-5</alm_backlash>
					<alm_backlash_type>1</alm_backlash_type>
					<alm_level>1</alm_level>
					<alm_backlash_value>-20;0</alm_backlash_value>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x2001030030001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0xFD</ID2>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_common_alm</struct_var_name>
				<ID1>0x02</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Common Alarm</short_name>
					<full_name>Common Alarm</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_level>0</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x2001030040001">
			<attr.s>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_module_slot_fault_alm</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Module Slot Fault Alarm</short_name>
					<full_name>Module Slot Fault Alarm</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>1</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x2001050010001">
			<attr.s>
				<ID2>0x69</ID2>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_sys_name</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<para_type>1</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>ZXDT22 SF01 V3.0 DC POWER</default_value>
					<precision>0</precision>
					<data_type>string32</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Para System Name</short_name>
					<full_name>Para System Name</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x2001050020001">
			<attr.s>
				<ID2>0x6F</ID2>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_sys_overload_threshold</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>80</default_value>
					<data_type>int</data_type>
					<max_value>100</max_value>
					<min_value>10</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>%</unit>
				<name>
					<short_name>Sys.OverLoad Thre</short_name>
					<full_name>System OverLoad Alarm Threshold</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x2001050030001">
			<attr.s>
				<ID2>0xB5</ID2>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_bat_type</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>0</default_value>
					<data_type>int</data_type>
					<max_value>6</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<convention>0:VRLA Batt;1:Fast Charge Batt;2:Deep Cycling Batt;3:FeLiPO4 Batt;4:High Temp Batt;5:TB Batt;6:FB Batt</convention>
				<para_type>3</para_type>
				<name>
					<short_name>Battery Type</short_name>
					<full_name>Battery Type</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
			</attr.show>
		</sid>
		<sid value="0x2001050040001">
			<attr.s>
				<ID2>0x42</ID2>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_ac_input_scen</struct_var_name>
				<ID1>0x13</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>0</default_value>
					<data_type>int</data_type>
					<max_value>3</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<convention>0:Mains;1:Mains and DG;2:DG;3:None</convention>
				<para_type>3</para_type>
				<name>
					<short_name>AC In.Scenario</short_name>
					<full_name>AC Input Scenario</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
			</attr.show>
		</sid>
		<sid value="0x2001050050001">
			<attr.s>
				<ID2>0x48</ID2>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_sys_expan_thre</struct_var_name>
				<ID1>0x33</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>100</default_value>
					<data_type>float</data_type>
					<max_value>3000</max_value>
					<min_value>10</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>A</unit>
				<name>
					<short_name>Sys.Expan.Thre</short_name>
					<full_name>System Expansion Threshold</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x2001050060001">
			<attr.s>
				<ID2>0x9A</ID2>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_site_name</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<para_type>3</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>Site-X#</default_value>
					<precision>0</precision>
					<data_type>string32</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Site Name</short_name>
					<full_name>Site Name</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x2001050070001">
			<attr.s>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_env_survey_enable</struct_var_name>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>1</default_value>
					<data_type>int</data_type>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<convention>0:Disable;1:Enable</convention>
				<para_type>3</para_type>
				<name>
					<short_name>Env Survey Enable</short_name>
					<full_name>Env Survey Enable</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x2001060010001">
			<attr.s>
				<ID2>0x1E</ID2>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_his_data_save_source</struct_var_name>
				<ID1>0x01</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>10</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>HisData Sav.Cause</short_name>
					<full_name>Historical data save Cause</full_name>
				</name>
				<convention>0:Auto;1:Para Change;2:Alarm Change;3:Batt Stat Change;4:Gen Start and Stop;5:Mains Start and Stop;6:Gen Run 10min;7:Gen Run 30min;8:Gen Run 60min;9:Batt Testing;10:Batt Detect</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x2001060020001">
			<attr.s>
				<ID2>0xFB</ID2>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_real_alm_time</struct_var_name>
				<ID1>0x02</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<data_type>time</data_type>
				</values>
				<name>
					<short_name>Real Alm.Time</short_name>
					<full_name>Real Time Alarm Occurrence Time</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x2001060030001">
			<attr.s>
				<ID2>0xFC</ID2>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_his_alm_time</struct_var_name>
				<ID1>0x02</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<data_type>process_time</data_type>
				</values>
				<name>
					<short_name>His.Alm.Time</short_name>
					<full_name>History Alarm Time</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x2001060040001">
			<attr.s>
				<ID2>0x6D</ID2>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_dg</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>DG</short_name>
					<full_name>Diesel Generator</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x2001060050001">
			<attr.s>
				<ID2>0x6E</ID2>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_pu</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Solar</short_name>
					<full_name>Solar</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x2001060060001">
			<attr.s>
				<ID2>0x6F</ID2>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_wt</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Wind Turbine</short_name>
					<full_name>Wind Turbine</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x2001060070001">
			<attr.s>
				<ID2>0x70</ID2>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_battery</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Battery</short_name>
					<full_name>Battery</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x2001060080001">
			<attr.s>
				<ID2>0x72</ID2>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_mains</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Mains</short_name>
					<full_name>Mains</full_name>
				</name>
				<convention>0:Null;1:Exist</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x2001060090001">
			<attr.s>
				<ID2>0x05</ID2>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_max_peak_time</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<data_type>time</data_type>
				</values>
				<name>
					<short_name>Max.Peak.Time</short_name>
					<full_name>Maximum Peak Time</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x20010600a0001">
			<attr.s>
				<ID2>0x06</ID2>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_min_peak_time</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<data_type>time</data_type>
				</values>
				<name>
					<short_name>Min.Peak.Time</short_name>
					<full_name>Minimum Peak Time</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x20010600b0001">
			<attr.s>
				<ID2>0xF9</ID2>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_his_data_rec_time</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<data_type>time</data_type>
				</values>
				<name>
					<short_name>Data Rec.Time</short_name>
					<full_name>History Data Record Time</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x20010600c0001">
			<attr.s>
				<ID2>0xFA</ID2>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_his_event_rec_time</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<data_type>time</data_type>
				</values>
				<name>
					<short_name>Event Rec.Time</short_name>
					<full_name>History Event Record Time</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x20010600d0001">
			<attr.s>
				<ID2>0x2E</ID2>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_maintain_record_time</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<precision>0</precision>
					<data_type>process_time</data_type>
				</values>
				<name>
					<short_name>Maint.Rec.Time</short_name>
					<full_name>Maintain Record Time</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x20010600e0001">
			<attr.s>
				<ID2>0x50</ID2>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_log_record_time</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<data_type>process_time</data_type>
				</values>
				<name>
					<short_name>Log Record Time</short_name>
					<full_name>Log Record Time</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x20010600f0001">
			<attr.s>
				<ID2>0x5D</ID2>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_energy_statistics</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Energy Stat</short_name>
					<full_name>Energy Statistics</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x2001060100001">
			<attr.s>
				<ID2>0x5E</ID2>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_peak_statistics</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Peak Stat</short_name>
					<full_name>Peak Statistics</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x2001060110001">
			<attr.s>
				<ID2>0x5F</ID2>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_his_recd_sn</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>His.Recd.SN</short_name>
					<full_name>History Record Serial Number</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x2001060120001">
			<attr.s>
				<ID2>0x73</ID2>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_event_duration</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Event Duration</short_name>
					<full_name>Event Duration</full_name>
				</name>
				<unit>Min</unit>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x2001060130001">
			<attr.s>
				<ID2>0x78</ID2>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_count_statistics</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Count Stat</short_name>
					<full_name>Count Statistics</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x2001080010001">
			<attr.s>
				<ID2>0xFD</ID2>
				<dev_macro>DEV_PS</dev_macro>
				<struct_var_name>ps_sys_name</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>8</signal_type>
				<values>
					<data_type>string32</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>System Name</short_name>
					<full_name>System Name</full_name>
				</name>
			</attr.r>
		</sid>
	</device>
	<device name="System AC Input">
		<sid value="0x3001010010001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x0F</ID2>
				<dev_macro>DEV_ACIN</dev_macro>
				<struct_var_name>acin_phase_volt</struct_var_name>
				<ID1>0x10</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>220</default_value>
					<max_value>300</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>AC_PHASE_NUM</dimension>
				</dimensions>
				<unit>V</unit>
				<name>
					<short_name>System AC Voltage</short_name>
					<full_name>System AC Voltage</full_name>
				</name>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0x3001010020001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x10</ID2>
				<dev_macro>DEV_ACIN</dev_macro>
				<struct_var_name>acin_phase_curr</struct_var_name>
				<ID1>0x10</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>600</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>AC_PHASE_NUM</dimension>
				</dimensions>
				<unit>A</unit>
				<name>
					<short_name>System AC Current</short_name>
					<full_name>System AC Current</full_name>
				</name>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0x3001010030001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x02</ID2>
				<dev_macro>DEV_ACIN</dev_macro>
				<struct_var_name>acin_phase_volt_ul1</struct_var_name>
				<ID1>0x10</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>220</default_value>
					<max_value>300</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>V</unit>
				<name>
					<short_name>UL1</short_name>
					<full_name>Phase Voltage UL1</full_name>
				</name>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x3001010040001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<dev_macro>DEV_ACIN</dev_macro>
				<struct_var_name>acin_phase_volt_ul2</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>220</default_value>
					<max_value>300</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>V</unit>
				<name>
					<short_name>UL2</short_name>
					<full_name>Phase Voltage UL2</full_name>
				</name>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x3001010050001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<dev_macro>DEV_ACIN</dev_macro>
				<struct_var_name>acin_phase_volt_ul3</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>220</default_value>
					<max_value>300</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>V</unit>
				<name>
					<short_name>UL3</short_name>
					<full_name>Phase Voltage UL3</full_name>
				</name>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x3001010060001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x04</ID2>
				<dev_macro>DEV_ACIN</dev_macro>
				<struct_var_name>acin_line_volt_ul12</struct_var_name>
				<ID1>0x10</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>380</default_value>
					<max_value>520</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>V</unit>
				<name>
					<short_name>UL12</short_name>
					<full_name>Line Voltage UL12</full_name>
				</name>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x3001010070001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<dev_macro>DEV_ACIN</dev_macro>
				<struct_var_name>acin_line_volt_ul23</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>380</default_value>
					<max_value>520</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>V</unit>
				<name>
					<short_name>UL23</short_name>
					<full_name>Line Voltage UL23</full_name>
				</name>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x3001010080001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<dev_macro>DEV_ACIN</dev_macro>
				<struct_var_name>acin_line_volt_ul31</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>380</default_value>
					<max_value>520</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>V</unit>
				<name>
					<short_name>UL31</short_name>
					<full_name>Line Voltage UL31</full_name>
				</name>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x3001010090001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x06</ID2>
				<dev_macro>DEV_ACIN</dev_macro>
				<struct_var_name>acin_phase_current_il1</struct_var_name>
				<ID1>0x10</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>600</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>A</unit>
				<name>
					<short_name>IL1</short_name>
					<full_name>Phase Current IL1</full_name>
				</name>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x30010100a0001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x06</ID2>
				<dev_macro>DEV_ACIN</dev_macro>
				<struct_var_name>acin_phase_current_il2</struct_var_name>
				<ID1>0x10</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>600</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>A</unit>
				<name>
					<short_name>IL2</short_name>
					<full_name>Phase Current IL2</full_name>
				</name>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x30010100b0001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x06</ID2>
				<dev_macro>DEV_ACIN</dev_macro>
				<struct_var_name>acin_phase_current_il3</struct_var_name>
				<ID1>0x10</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>600</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>A</unit>
				<name>
					<short_name>IL3</short_name>
					<full_name>Phase Current IL3</full_name>
				</name>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x30010100c0001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x08</ID2>
				<dev_macro>DEV_ACIN</dev_macro>
				<struct_var_name>acin_line_current_il12</struct_var_name>
				<ID1>0x10</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>600</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>A</unit>
				<name>
					<short_name>IL12</short_name>
					<full_name>Line Current IL12</full_name>
				</name>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x30010100d0001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x08</ID2>
				<dev_macro>DEV_ACIN</dev_macro>
				<struct_var_name>acin_line_current_il23</struct_var_name>
				<ID1>0x10</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>600</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>A</unit>
				<name>
					<short_name>IL23</short_name>
					<full_name>Line Current IL23</full_name>
				</name>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x30010100e0001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x08</ID2>
				<dev_macro>DEV_ACIN</dev_macro>
				<struct_var_name>acin_line_current_il31</struct_var_name>
				<ID1>0x10</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>600</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>A</unit>
				<name>
					<short_name>IL31</short_name>
					<full_name>Line Current IL31</full_name>
				</name>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x3001020010001">
			<attr.s>
				<ID2>0x10</ID2>
				<dev_macro>DEV_ACIN</dev_macro>
				<struct_var_name>acin_source</struct_var_name>
				<ID1>0x11</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>8</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>AC Source</short_name>
					<full_name>AC Power Source</full_name>
				</name>
				<convention>0:Null;2:DG1;3:DG2;4:AC1;5:AC2</convention>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0x3001030010001">
			<attr.s>
				<ID2>0x01</ID2>
				<dev_macro>DEV_ACIN</dev_macro>
				<struct_var_name>acin_power_off</struct_var_name>
				<ID1>0x12</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>AC Power Off</short_name>
					<full_name>AC Power Off</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>5</alm_delay>
					<alm_backlash>9</alm_backlash>
					<alm_backlash_type>1</alm_backlash_type>
					<alm_level>2</alm_level>
					<alm_backlash_value>-10;10</alm_backlash_value>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x3001030020001">
			<attr.s>
				<ID2>0x05</ID2>
				<dev_macro>DEV_ACIN</dev_macro>
				<struct_var_name>acin_phase_lack</struct_var_name>
				<ID1>0x12</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>AC_PHASE_NUM</dimension>
				</dimensions>
				<name>
					<short_name>AC Phase Lack</short_name>
					<full_name>AC Phase Lack</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>13</alm_delay>
					<alm_backlash>9</alm_backlash>
					<alm_backlash_type>1</alm_backlash_type>
					<alm_level>2</alm_level>
					<alm_backlash_value>-10;10</alm_backlash_value>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x3001030030001">
			<attr.s>
				<ID2>0x14</ID2>
				<dev_macro>DEV_ACIN</dev_macro>
				<struct_var_name>acin_vol_low</struct_var_name>
				<ID1>0x12</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>AC_PHASE_NUM</dimension>
				</dimensions>
				<name>
					<short_name>AC Volt.Low</short_name>
					<full_name>AC Voltage Low</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>15</alm_delay>
					<alm_backlash>9</alm_backlash>
					<alm_backlash_type>1</alm_backlash_type>
					<alm_level>2</alm_level>
					<alm_backlash_value>-10;10</alm_backlash_value>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x3001030040001">
			<attr.s>
				<ID2>0x15</ID2>
				<dev_macro>DEV_ACIN</dev_macro>
				<struct_var_name>acin_vol_high</struct_var_name>
				<ID1>0x12</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>AC_PHASE_NUM</dimension>
				</dimensions>
				<name>
					<short_name>AC Volt.High</short_name>
					<full_name>AC Voltage High</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>13</alm_delay>
					<alm_backlash>-9</alm_backlash>
					<alm_backlash_type>1</alm_backlash_type>
					<alm_level>2</alm_level>
					<alm_backlash_value>-10;10</alm_backlash_value>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x3001030050001">
			<attr.s>
				<ID2>0x16</ID2>
				<dev_macro>DEV_ACIN</dev_macro>
				<struct_var_name>acin_curr_high</struct_var_name>
				<ID1>0x12</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>AC_PHASE_NUM</dimension>
				</dimensions>
				<name>
					<short_name>AC Curr.High</short_name>
					<full_name>AC Current High</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_backlash>-4</alm_backlash>
					<alm_backlash_type>1</alm_backlash_type>
					<alm_level>2</alm_level>
					<alm_backlash_value>-10;10</alm_backlash_value>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x3001030060001">
			<attr.s>
				<ID2>0x3D</ID2>
				<dev_macro>DEV_ACIN</dev_macro>
				<struct_var_name>acin_phase_unbalance</struct_var_name>
				<ID1>0x12</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>AC Volt.Imbala</short_name>
					<full_name>AC Voltage Imbalance</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>13</alm_delay>
					<alm_backlash>-5</alm_backlash>
					<alm_backlash_type>1</alm_backlash_type>
					<alm_level>2</alm_level>
					<alm_backlash_value>-10;10</alm_backlash_value>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x3001030070001">
			<attr.s>
				<dev_macro>DEV_ACIN</dev_macro>
				<struct_var_name>acin_vol_too_low</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>AC_PHASE_NUM</dimension>
				</dimensions>
				<name>
					<short_name>AC Volt.T.Low</short_name>
					<full_name>AC Voltage Too Low</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>15</alm_delay>
					<alm_backlash>9</alm_backlash>
					<alm_backlash_type>1</alm_backlash_type>
					<alm_level>2</alm_level>
					<alm_backlash_value>-10;10</alm_backlash_value>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x3001030080001">
			<attr.s>
				<dev_macro>DEV_ACIN</dev_macro>
				<struct_var_name>acin_vol_too_high</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>AC_PHASE_NUM</dimension>
				</dimensions>
				<name>
					<short_name>AC Volt.T.High</short_name>
					<full_name>AC Voltage Too High</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>13</alm_delay>
					<alm_backlash>-9</alm_backlash>
					<alm_backlash_type>1</alm_backlash_type>
					<alm_level>2</alm_level>
					<alm_backlash_value>-10;10</alm_backlash_value>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x3001050010001">
			<attr.s>
				<ID2>0x06</ID2>
				<dev_macro>DEV_ACIN</dev_macro>
				<struct_var_name>acin_curr_max_thres</struct_var_name>
				<ID1>0x13</ID1>
			</attr.s>
			<attr.r>
				<para_type>2</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>80</default_value>
					<data_type>float</data_type>
					<max_value>600</max_value>
					<min_value>5</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<name>
					<short_name>AC Curr.High Thre</short_name>
					<full_name>AC Current High Threshold</full_name>
				</name>
				<unit>A</unit>
			</attr.r>
		</sid>
		<sid value="0x3001050020001">
			<attr.s>
				<optional_attribute>ac_power_supply_opt_en</optional_attribute>
				<ID2>0x1C</ID2>
				<dev_macro>DEV_ACIN</dev_macro>
				<struct_var_name>acin_power_supply</struct_var_name>
				<ID1>0x13</ID1>
			</attr.s>
			<attr.r>
				<para_type>3</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>0</default_value>
					<data_type>int</data_type>
					<max_value>3</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<name>
					<short_name>AC Power Supply</short_name>
					<full_name>AC Power Supply</full_name>
				</name>
				<convention>0:L1L2L3N-220V;1:L1L2L3-110V;2:L1N-220V;3:L1L2-110V</convention>
			</attr.r>
		</sid>
		<sid value="0x3001050030001">
			<attr.s>
				<ID2>0x1E</ID2>
				<dev_macro>DEV_ACIN</dev_macro>
				<struct_var_name>acin_vol_max_thres</struct_var_name>
				<ID1>0x13</ID1>
			</attr.s>
			<attr.r>
				<name>
					<short_name>AC Volt.H.Thre</short_name>
					<full_name>AC Voltage High Threshold</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>286</default_value>
					<data_type>float</data_type>
					<max_value>290</max_value>
					<min_value>240</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<para_type>2</para_type>
				<unit>V</unit>
				<constraints>
					<constraint operator="&lt;=" full_name="AC Voltage Too High Threshold" backlash="-10"/>
				</constraints>
			</attr.r>
		</sid>
		<sid value="0x3001050040001">
			<attr.s>
				<ID2>0x1F</ID2>
				<dev_macro>DEV_ACIN</dev_macro>
				<struct_var_name>acin_vol_min_thres</struct_var_name>
				<ID1>0x13</ID1>
			</attr.s>
			<attr.r>
				<name>
					<short_name>AC Volt.L.Thre</short_name>
					<full_name>AC Voltage Low Threshold</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>154</default_value>
					<data_type>float</data_type>
					<max_value>200</max_value>
					<min_value>80</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<para_type>2</para_type>
				<unit>V</unit>
				<constraints>
					<constraint operator="&gt;=" full_name="AC Voltage Too Low Threshold" backlash="10"/>
				</constraints>
			</attr.r>
		</sid>
		<sid value="0x3001050050001">
			<attr.s>
				<ID2>0x38</ID2>
				<dev_macro>DEV_ACIN</dev_macro>
				<struct_var_name>acin_vol_unbalance_thres</struct_var_name>
				<ID1>0x13</ID1>
			</attr.s>
			<attr.r>
				<para_type>2</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>55</default_value>
					<data_type>float</data_type>
					<max_value>220</max_value>
					<min_value>22</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<name>
					<short_name>AC Volt.Imbala</short_name>
					<full_name>AC Voltage Imbalance Threshold</full_name>
				</name>
				<unit>V</unit>
			</attr.r>
		</sid>
		<sid value="0x3001050060001">
			<attr.s>
				<optional_attribute>ac_input_scenario_contain_mains</optional_attribute>
				<dev_macro>DEV_ACIN</dev_macro>
				<struct_var_name>mains_config</struct_var_name>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>MAINS_NUM</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>1</default_value>
					<data_type>int</data_type>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<convention>0:Null;1:Exist</convention>
				<para_type>3</para_type>
				<name>
					<short_name>Mains Config</short_name>
					<full_name>Mains Config</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x3001050070001">
			<attr.s>
				<dev_macro>DEV_ACIN</dev_macro>
				<struct_var_name>acin_vol_too_max_thres</struct_var_name>
			</attr.s>
			<attr.r>
				<name>
					<short_name>AC Volt.T.H.Thre</short_name>
					<full_name>AC Voltage Too High Threshold</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>296</default_value>
					<data_type>float</data_type>
					<max_value>300</max_value>
					<min_value>250</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<para_type>2</para_type>
				<unit>V</unit>
				<constraints>
					<constraint operator="&gt;=" full_name="AC Voltage High Threshold" backlash="10"/>
				</constraints>
			</attr.r>
		</sid>
		<sid value="0x3001050080001">
			<attr.s>
				<dev_macro>DEV_ACIN</dev_macro>
				<struct_var_name>acin_vol_too_min_thres</struct_var_name>
			</attr.s>
			<attr.r>
				<name>
					<short_name>AC Volt.T.L.Thre</short_name>
					<full_name>AC Voltage Too Low Threshold</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>144</default_value>
					<data_type>float</data_type>
					<max_value>190</max_value>
					<min_value>70</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<para_type>2</para_type>
				<unit>V</unit>
				<constraints>
					<constraint operator="&lt;=" full_name="AC Voltage Low Threshold" backlash="-10"/>
				</constraints>
			</attr.r>
		</sid>
	</device>
	<device name="AC Distribution">
		<sid value="0x4001010010001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
			<attr.s>
				<ID2>0x0F</ID2>
				<dev_macro>DEV_ACD</dev_macro>
				<struct_var_name>acd_phase_vol</struct_var_name>
				<ID1>0x10</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>220</default_value>
					<max_value>300</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>AC_PHASE_NUM</dimension>
				</dimensions>
				<unit>V</unit>
				<name>
					<short_name>AC Voltage</short_name>
					<full_name>AC Voltage</full_name>
				</name>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0x4001010020001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
			<attr.s>
				<ID2>0x10</ID2>
				<dev_macro>DEV_ACD</dev_macro>
				<struct_var_name>acd_phase_cur</struct_var_name>
				<ID1>0x10</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>600</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>AC_PHASE_NUM</dimension>
				</dimensions>
				<unit>A</unit>
				<name>
					<short_name>AC Current</short_name>
					<full_name>AC Current</full_name>
				</name>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0x4001020010001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
			<attr.s>
				<optional_attribute>ac_input_switch_conf</optional_attribute>
				<ID2>0x02</ID2>
				<dev_macro>DEV_ACD</dev_macro>
				<struct_var_name>acd_main_switch_state</struct_var_name>
				<ID1>0x11</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>2</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>AC Input Switch</short_name>
					<full_name>AC Input Switch Status</full_name>
				</name>
				<convention>0:close;1:break;2:Null</convention>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0x4001020020001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
			<attr.s>
				<optional_attribute>ac_output_switch_conf</optional_attribute>
				<ID2>0x03</ID2>
				<dev_macro>DEV_ACD</dev_macro>
				<struct_var_name>acd_aux_switch_state</struct_var_name>
				<ID1>0x11</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>2</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>AC_OUTSWITCH_NUM</dimension>
				</dimensions>
				<name>
					<short_name>AC Out.Switch</short_name>
					<full_name>AC Output Switch Status</full_name>
				</name>
				<convention>0:close;1:break;2:Null</convention>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0x4001020030001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
			<attr.s>
				<optional_attribute>ac_spd_detect</optional_attribute>
				<ID2>0x04</ID2>
				<dev_macro>DEV_ACD</dev_macro>
				<struct_var_name>acd_arrest_state</struct_var_name>
				<ID1>0x11</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>2</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>AC SPD</short_name>
					<full_name>AC SPD Status</full_name>
				</name>
				<convention>0:Normal;1:Fault;2:Null</convention>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0x4001020040001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
			<attr.s>
				<ID2>0x07</ID2>
				<dev_macro>DEV_ACD</dev_macro>
				<struct_var_name>acd_connect_mode</struct_var_name>
				<ID1>0x11</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>5</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>AC_PHASE_NUM</dimension>
				</dimensions>
				<name>
					<short_name>AC Connect Mode</short_name>
					<full_name>AC Connect Mode</full_name>
				</name>
				<convention>0:L1;1:L2;2:L3;3:L1-L2;4:L3-L1;5:L2-L3</convention>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0x4001030010001">
			<attr.s>
				<optional_attribute>ac_input_switch_conf</optional_attribute>
				<ID2>0x02</ID2>
				<dev_macro>DEV_ACD</dev_macro>
				<struct_var_name>acd_main_switch_alarm</struct_var_name>
				<ID1>0x12</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>AC In.Switch Off</short_name>
					<full_name>AC Input Switch Off</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x4001030020001">
			<attr.s>
				<optional_attribute>ac_spd_detect</optional_attribute>
				<ID2>0x0D</ID2>
				<dev_macro>DEV_ACD</dev_macro>
				<struct_var_name>acd_arrest_alarm</struct_var_name>
				<ID1>0x12</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>AC SPD Abr</short_name>
					<full_name>AC SPD Abnormal</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x4001030030001">
			<attr.s>
				<optional_attribute>ac_output_switch_conf</optional_attribute>
				<ID2>0x10</ID2>
				<dev_macro>DEV_ACD</dev_macro>
				<struct_var_name>acd_aux_switch_alarm</struct_var_name>
				<ID1>0x12</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>AC_OUTSWITCH_NUM</dimension>
				</dimensions>
				<name>
					<short_name>AC Out.SW Off</short_name>
					<full_name>AC Output Switch Off</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x4001050010001">
			<attr.s>
				<optional_attribute>ac_transmitter_not_conf</optional_attribute>
				<ID2>0x18</ID2>
				<dev_macro>DEV_ACD</dev_macro>
				<struct_var_name>acd_vol_zero</struct_var_name>
				<ID1>0x13</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>AC_PHASE_NUM</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>0</default_value>
					<data_type>float</data_type>
					<max_value>10</max_value>
					<min_value>-10</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<para_type>3</para_type>
				<unit>V</unit>
				<name>
					<short_name>AC Volt.Offset</short_name>
					<full_name>AC Voltage Offset</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
			</attr.show>
		</sid>
		<sid value="0x4001050020001">
			<attr.s>
				<optional_attribute>ac_transmitter_not_conf</optional_attribute>
				<ID2>0x19</ID2>
				<dev_macro>DEV_ACD</dev_macro>
				<struct_var_name>acd_vol_slope</struct_var_name>
				<ID1>0x13</ID1>
			</attr.s>
			<attr.r>
				<para_type>3</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>1</default_value>
					<data_type>float</data_type>
					<max_value>5</max_value>
					<min_value>0.001</min_value>
					<precision>3</precision>
					<step>0.001</step>
				</values>
				<dimensions>
					<dimension>AC_PHASE_NUM</dimension>
				</dimensions>
				<name>
					<short_name>AC Volt.Slope</short_name>
					<full_name>AC Voltage Slope</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
			</attr.show>
		</sid>
		<sid value="0x4001050030001">
			<attr.s>
				<optional_attribute>ac_transmitter_not_conf</optional_attribute>
				<ID2>0x1A</ID2>
				<dev_macro>DEV_ACD</dev_macro>
				<struct_var_name>acd_cur_zero</struct_var_name>
				<ID1>0x13</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>AC_PHASE_NUM</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>0</default_value>
					<data_type>float</data_type>
					<max_value>10</max_value>
					<min_value>-10</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<para_type>3</para_type>
				<unit>A</unit>
				<name>
					<short_name>AC Curr.Offset</short_name>
					<full_name>AC Current Offset</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
			</attr.show>
		</sid>
		<sid value="0x4001050040001">
			<attr.s>
				<optional_attribute>ac_transmitter_not_conf</optional_attribute>
				<ID2>0x1B</ID2>
				<dev_macro>DEV_ACD</dev_macro>
				<struct_var_name>acd_cur_slope</struct_var_name>
				<ID1>0x13</ID1>
			</attr.s>
			<attr.r>
				<para_type>3</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>1</default_value>
					<data_type>float</data_type>
					<max_value>5</max_value>
					<min_value>0.001</min_value>
					<precision>3</precision>
					<step>0.001</step>
				</values>
				<dimensions>
					<dimension>AC_PHASE_NUM</dimension>
				</dimensions>
				<name>
					<short_name>AC Curr.Slope</short_name>
					<full_name>AC Current Slope</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
			</attr.show>
		</sid>
	</device>
	<device name="Mains">
		<sid value="0x5001010010001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x3F</ID2>
				<dev_macro>DEV_MAINS</dev_macro>
				<struct_var_name>mains_phase_volt</struct_var_name>
				<ID1>0x10</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>220</default_value>
					<max_value>300</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>AC_PHASE_NUM</dimension>
				</dimensions>
				<unit>V</unit>
				<name>
					<short_name>Mns.Ph.Volt</short_name>
					<full_name>MAINS Phase Voltage</full_name>
				</name>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x5001010020001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x40</ID2>
				<dev_macro>DEV_MAINS</dev_macro>
				<struct_var_name>mains_line_volt</struct_var_name>
				<ID1>0x10</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>380</default_value>
					<max_value>520</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>AC_PHASE_NUM</dimension>
				</dimensions>
				<unit>V</unit>
				<name>
					<short_name>Mns.Line Volt</short_name>
					<full_name>MAINS Line Voltage</full_name>
				</name>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x5001010030001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x41</ID2>
				<dev_macro>DEV_MAINS</dev_macro>
				<struct_var_name>mains_phase_curr</struct_var_name>
				<ID1>0x10</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>600</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>AC_PHASE_NUM</dimension>
				</dimensions>
				<unit>A</unit>
				<name>
					<short_name>Mns.Ph.Curr</short_name>
					<full_name>MAINS Phase Current</full_name>
				</name>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x5001010040001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x42</ID2>
				<dev_macro>DEV_MAINS</dev_macro>
				<struct_var_name>mains_act_power</struct_var_name>
				<ID1>0x10</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>200</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>AC_PHASE_NUM</dimension>
				</dimensions>
				<unit>kW</unit>
				<name>
					<short_name>Mns.Act.Pwr</short_name>
					<full_name>MAINS Active Power</full_name>
				</name>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x5001010050001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x43</ID2>
				<dev_macro>DEV_MAINS</dev_macro>
				<struct_var_name>mains_react_power</struct_var_name>
				<ID1>0x10</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>200</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>AC_PHASE_NUM</dimension>
				</dimensions>
				<unit>kvar</unit>
				<name>
					<short_name>Mns.Rct.Pwr</short_name>
					<full_name>MAINS Reactive Power</full_name>
				</name>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x5001010060001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x44</ID2>
				<dev_macro>DEV_MAINS</dev_macro>
				<struct_var_name>mains_apparent_power</struct_var_name>
				<ID1>0x10</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>200</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>AC_PHASE_NUM</dimension>
				</dimensions>
				<unit>kVA</unit>
				<name>
					<short_name>Mns.App.Pwr</short_name>
					<full_name>MAINS Apparent Power</full_name>
				</name>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x5001010070001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x45</ID2>
				<dev_macro>DEV_MAINS</dev_macro>
				<struct_var_name>mains_power_factor</struct_var_name>
				<ID1>0x10</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>1</default_value>
					<max_value>1</max_value>
					<min_value>-1</min_value>
					<precision>2</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>AC_PHASE_NUM</dimension>
				</dimensions>
				<name>
					<short_name>Mns.PF</short_name>
					<full_name>MAINS Power Factor</full_name>
				</name>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x5001010080001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x46</ID2>
				<dev_macro>DEV_MAINS</dev_macro>
				<struct_var_name>mains_out_freq</struct_var_name>
				<ID1>0x10</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>50</default_value>
					<max_value>75</max_value>
					<min_value>40</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Mns.Out.Freq</short_name>
					<full_name>MAINS Output Frequency</full_name>
				</name>
				<unit>Hz</unit>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x5001010090001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x47</ID2>
				<dev_macro>DEV_MAINS</dev_macro>
				<struct_var_name>mains_tot_act_power</struct_var_name>
				<ID1>0x10</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>200</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>Mns.Tot.Act.Pwr</short_name>
					<full_name>MAINS Total Active Power</full_name>
				</name>
				<unit>kW</unit>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x50010100a0001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x48</ID2>
				<dev_macro>DEV_MAINS</dev_macro>
				<struct_var_name>mains_tot_react_power</struct_var_name>
				<ID1>0x10</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>200</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>Mns.Tot.Rct.Pwr</short_name>
					<full_name>MAINS Total Reactive Power</full_name>
				</name>
				<unit>kvar</unit>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x50010100b0001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x49</ID2>
				<dev_macro>DEV_MAINS</dev_macro>
				<struct_var_name>mains_tot_apparent_power</struct_var_name>
				<ID1>0x10</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>200</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>Mns.Tot.App.Pwr</short_name>
					<full_name>MAINS Total Apparent Power</full_name>
				</name>
				<unit>kVA</unit>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x50010100c0001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x4A</ID2>
				<dev_macro>DEV_MAINS</dev_macro>
				<struct_var_name>mains_tot_power_factor</struct_var_name>
				<ID1>0x10</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>1</default_value>
					<max_value>1</max_value>
					<min_value>-1</min_value>
					<precision>2</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>Mns.Avr.PF</short_name>
					<full_name>MAINS Average Power Factor</full_name>
				</name>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x5001020010001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x11</ID2>
				<dev_macro>DEV_MAINS</dev_macro>
				<struct_var_name>mains_status</struct_var_name>
				<ID1>0x11</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Mns.Status</short_name>
					<full_name>MAINS Status</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x5001030010001">
			<attr.s>
				<ID2>0x2F</ID2>
				<dev_macro>DEV_MAINS</dev_macro>
				<struct_var_name>mains_high_freq</struct_var_name>
				<ID1>0x12</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Mns.High Freq</short_name>
					<full_name>MAINS High Frequency</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>1</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x5001030020001">
			<attr.s>
				<ID2>0x30</ID2>
				<dev_macro>DEV_MAINS</dev_macro>
				<struct_var_name>mains_phase_volt_unbalance</struct_var_name>
				<ID1>0x12</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Mns.Volt.Imba</short_name>
					<full_name>MAINS Phase Voltage Imbalance</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>1</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x5001030030001">
			<attr.s>
				<ID2>0x31</ID2>
				<dev_macro>DEV_MAINS</dev_macro>
				<struct_var_name>mains_phase_seq_error</struct_var_name>
				<ID1>0x12</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Mns.Ph.Seq.Err</short_name>
					<full_name>MAINS Phase Sequence Error</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>1</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x5001030040001">
			<attr.s>
				<ID2>0x38</ID2>
				<dev_macro>DEV_MAINS</dev_macro>
				<struct_var_name>mains_fail</struct_var_name>
				<ID1>0x12</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Mns.Failure</short_name>
					<full_name>MAINS Failure</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x5001030050001">
			<attr.s>
				<ID2>0x39</ID2>
				<dev_macro>DEV_MAINS</dev_macro>
				<struct_var_name>mains_phase_volt_high</struct_var_name>
				<ID1>0x12</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Mns.Pha.Volt.High</short_name>
					<full_name>MAINS Phase Voltage High</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>1</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x5001030060001">
			<attr.s>
				<ID2>0x3A</ID2>
				<dev_macro>DEV_MAINS</dev_macro>
				<struct_var_name>mains_phase_volt_low</struct_var_name>
				<ID1>0x12</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Mns.Pha.Volt.Low</short_name>
					<full_name>MAINS Phase Voltage Low</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x5001030070001">
			<attr.s>
				<ID2>0x3B</ID2>
				<dev_macro>DEV_MAINS</dev_macro>
				<struct_var_name>mains_phase_curr_high</struct_var_name>
				<ID1>0x12</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Mns.Pha.Curr.High</short_name>
					<full_name>MAINS Phase Current High</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>1</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x5001030080001">
			<attr.s>
				<ID2>0x42</ID2>
				<dev_macro>DEV_MAINS</dev_macro>
				<struct_var_name>mains_low_freq</struct_var_name>
				<ID1>0x12</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Mns.Low Freq</short_name>
					<full_name>MAINS Low Frequency</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x5001050010001">
			<attr.s>
				<ID2>0x8B</ID2>
				<dev_macro>DEV_MAINS</dev_macro>
				<struct_var_name>mains_condition</struct_var_name>
				<ID1>0x13</ID1>
			</attr.s>
			<attr.r>
				<para_type>3</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>1</default_value>
					<data_type>int</data_type>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<name>
					<short_name>Mns.Con</short_name>
					<full_name>MAINS Condition</full_name>
				</name>
				<convention>0:Bad Grid;1:Good Grid</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x5001060010001">
			<attr.s>
				<ID2>0x54</ID2>
				<dev_macro>DEV_MAINS</dev_macro>
				<struct_var_name>mains_fail_process_time</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<precision>0</precision>
					<data_type>process_time</data_type>
				</values>
				<name>
					<short_name>Mns.Fail.Time</short_name>
					<full_name>MAINS Failure Time</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x5001060020001">
			<attr.s>
				<ID2>0x55</ID2>
				<dev_macro>DEV_MAINS</dev_macro>
				<struct_var_name>mains_work_process_time</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<precision>0</precision>
					<data_type>process_time</data_type>
				</values>
				<name>
					<short_name>Mns.Work Time</short_name>
					<full_name>MAINS Work Time</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x5001060030001">
			<attr.s>
				<ID2>0x19</ID2>
				<dev_macro>DEV_MAINS</dev_macro>
				<struct_var_name>mains_start_src</struct_var_name>
				<ID1>0x15</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>MAINS.Start Rsn</short_name>
					<full_name>MAINS Start Reason</full_name>
				</name>
				<convention>0:Null;1:Local;2:SC;3:Low Voltage;4:Low SOC;5:Batt Time;6:Full Charge;7:Period On;8:MAINS regain;9:Green Energy Short;10:Sys Abnormal;11:Gen Mode Switch</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x5001060040001">
			<attr.s>
				<ID2>0x1A</ID2>
				<dev_macro>DEV_MAINS</dev_macro>
				<struct_var_name>mains_stop_src</struct_var_name>
				<ID1>0x15</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>MAINS.Stop Rsn</short_name>
					<full_name>MAINS Stop Reason</full_name>
				</name>
				<convention>0:Null;1:Local;2:SC;3:High Voltage;4:High SOC;5:Batt Curr;6:Max Time;7:Full Charge;8:Period Off;9:MAINS Fail;10:Green Energ Enough</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x5001060050001">
			<attr.s>
				<ID2>0x30</ID2>
				<dev_macro>DEV_MAINS</dev_macro>
				<struct_var_name>mains_stop_count_statistics</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>P.F.Times Stat</short_name>
					<full_name>Power Failure Times Statistics</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x5001060060001">
			<attr.s>
				<ID2>0x31</ID2>
				<dev_macro>DEV_MAINS</dev_macro>
				<struct_var_name>mains_stop_count_statistics_dura</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>P.F.Times Period</short_name>
					<full_name>Period for Power Failure Times Statistics</full_name>
				</name>
				<convention>0:January;1:February;2:March;3:April;4:May;5:June;6:July;7:August;8:September;9:October;10:November;11:December</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x5001060070001">
			<attr.s>
				<ID2>0x3D</ID2>
				<dev_macro>DEV_MAINS</dev_macro>
				<struct_var_name>mains_stop_time_statistics</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>P.F.Time Stat</short_name>
					<full_name>Power Failure Time Statistics</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x5001060080001">
			<attr.s>
				<ID2>0x3E</ID2>
				<dev_macro>DEV_MAINS</dev_macro>
				<struct_var_name>mains_stop_time_statistics_dura</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>P.F.Time Period</short_name>
					<full_name>Period for Power Failure Time Statistics</full_name>
				</name>
				<convention>0:January;1:February;2:March;3:April;4:May;5:June;6:July;7:August;8:September;9:October;10:November;11:December</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x5001070010001">
			<attr.s>
				<ID2>0x27</ID2>
				<dev_macro>DEV_MAINS</dev_macro>
				<struct_var_name>mains_fail_dura</struct_var_name>
				<ID1>0x00</ID1>
			</attr.s>
			<attr.r>
				<signal_type>7</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Mns.Fail.Dura</short_name>
					<full_name>MAINS Failure Duration</full_name>
				</name>
				<unit>Min</unit>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x5001070020001">
			<attr.s>
				<ID2>0x28</ID2>
				<dev_macro>DEV_MAINS</dev_macro>
				<struct_var_name>mains_work_dura</struct_var_name>
				<ID1>0x00</ID1>
			</attr.s>
			<attr.r>
				<signal_type>7</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Mns.Work Dura</short_name>
					<full_name>MAINS Work Duration</full_name>
				</name>
				<unit>Min</unit>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x5001070030001">
			<attr.s>
				<ID2>0x56</ID2>
				<dev_macro>DEV_MAINS</dev_macro>
				<struct_var_name>mains_energy</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>7</signal_type>
				<values>
					<default_value>0</default_value>
					<min_value>0</min_value>
					<precision>2</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>Mns.Eng.Prod</short_name>
					<full_name>MAINS Energy Production</full_name>
				</name>
				<unit>kWh</unit>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x5001070040001">
			<attr.s>
				<ID2>0x32</ID2>
				<dev_macro>DEV_MAINS</dev_macro>
				<struct_var_name>mains_stop_count</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>7</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>P.F. Times</short_name>
					<full_name>Power Failure Times</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
	</device>
	<device name="Rectifier">
		<sid value="0x7001010010001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x01</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_out_volt</struct_var_name>
				<ID1>0x20</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>48</default_value>
					<max_value>60</max_value>
					<min_value>0</min_value>
					<precision>2</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>Output Voltage</short_name>
					<full_name>SMR Output Voltage</full_name>
				</name>
				<unit>V</unit>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001010020001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x02</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_out_curr</struct_var_name>
				<ID1>0x20</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>110</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>Output Current</short_name>
					<full_name>SMR Output Current</full_name>
				</name>
				<unit>A</unit>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001010030001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x0A</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_temp</struct_var_name>
				<ID1>0x20</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>25</default_value>
					<max_value>100</max_value>
					<min_value>-40</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>Internal Temp</short_name>
					<full_name>SMR Internal Temperature</full_name>
				</name>
				<unit>℃</unit>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001010040001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x12</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_max_out_curr</struct_var_name>
				<ID1>0x20</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>110</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>Max Out Curr</short_name>
					<full_name>SMR Maximum Output Current</full_name>
				</name>
				<unit>A</unit>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001010050001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x13</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_in_volt</struct_var_name>
				<ID1>0x20</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>380</default_value>
					<max_value>520</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>Input Voltage</short_name>
					<full_name>SMR Input Voltage</full_name>
				</name>
				<unit>V</unit>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001010060001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x14</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_in_cur</struct_var_name>
				<ID1>0x20</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>50</max_value>
					<min_value>0</min_value>
					<precision>2</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>Input Current</short_name>
					<full_name>SMR Input Current</full_name>
				</name>
				<unit>A</unit>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001010070001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x1A</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_in_freq</struct_var_name>
				<ID1>0x20</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>50</default_value>
					<max_value>100</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>In Freq</short_name>
					<full_name>SMR In Freq</full_name>
				</name>
				<unit>Hz</unit>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001010080001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_in_power</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>In Power</short_name>
					<full_name>SMR In Power</full_name>
				</name>
				<unit>W</unit>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001010090001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_out_power</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>Output Power</short_name>
					<full_name>SMR Output Power</full_name>
				</name>
				<unit>W</unit>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x70010100a0001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_fan_speed</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Fan Speed</short_name>
					<full_name>SMR Fan Speed</full_name>
				</name>
				<unit>RPM</unit>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x70010100b0001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_group_address</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Group Address</short_name>
					<full_name>SMR Group Address</full_name>
				</name>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0x70010100c0001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_group_inner_address</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Group Inner Address</short_name>
					<full_name>SMR Group Inner Address</full_name>
				</name>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0x70010100d0001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_slot_address</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>1</default_value>
					<max_value>40</max_value>
					<min_value>1</min_value>
					<precision>1</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Slot Address</short_name>
					<full_name>SMR Slot Address</full_name>
				</name>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x70010100e0001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_positive_bus_volt</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>510</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>Positive Bus Volt</short_name>
					<full_name>SMR Positive Bus Volt</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x70010100f0001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_negative_bus_volt</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>510</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>Negative Bus Volt</short_name>
					<full_name>SMR Negative Bus Volt</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001010100001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_bus1_volt</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>510</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>Bus1 Volt</short_name>
					<full_name>SMR Bus1 Volt</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001010110001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_bus2_volt</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>510</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>Bus2 Volt</short_name>
					<full_name>SMR Bus2 Volt</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001010120001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_bus3_volt</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>510</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>Bus3 Volt</short_name>
					<full_name>SMR Bus3 Volt</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001010130001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_bus4_volt</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>510</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>Bus4 Volt</short_name>
					<full_name>SMR Bus4 Volt</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001020010001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x01</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_turn_off</struct_var_name>
				<ID1>0x21</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Off</short_name>
					<full_name>SMR Off Status</full_name>
				</name>
				<convention>0:No;1:Yes</convention>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001020020001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x03</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_curr_limit</struct_var_name>
				<ID1>0x21</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Current Limit</short_name>
					<full_name>SMR Current Limit Status</full_name>
				</name>
				<convention>0:No;1:Yes</convention>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001020030001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x0B</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_in_pow_limit</struct_var_name>
				<ID1>0x21</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>In Power Limit</short_name>
					<full_name>SMR Input Power Limit</full_name>
				</name>
				<convention>0:No;1:Yes</convention>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001020040001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x0C</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_temp_pow_limit</struct_var_name>
				<ID1>0x21</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>TH Power Limit</short_name>
					<full_name>SMR Temperature Power Limit</full_name>
				</name>
				<convention>0:No;1:Yes</convention>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001020050001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x0D</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_sleep_state</struct_var_name>
				<ID1>0x21</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Sleep</short_name>
					<full_name>SMR Sleep Status</full_name>
				</name>
				<convention>0:No;1:Yes</convention>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001020060001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x0E</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_p2p</struct_var_name>
				<ID1>0x21</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>P2P</short_name>
					<full_name>SMR P2P Status</full_name>
				</name>
				<convention>0:No;1:Yes</convention>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001020070001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x0F</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_close_down</struct_var_name>
				<ID1>0x21</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Closedown</short_name>
					<full_name>SMR Closedown Status</full_name>
				</name>
				<convention>0:No;1:Yes</convention>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001020080001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x10</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_out_pow_limit</struct_var_name>
				<ID1>0x21</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>DC Power Limit</short_name>
					<full_name>SMR DC Power Limit</full_name>
				</name>
				<convention>0:No;1:Yes</convention>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001020090001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x11</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_in_phase</struct_var_name>
				<ID1>0x21</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>5</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR In.Phase</short_name>
					<full_name>SMR Input Phase</full_name>
				</name>
				<convention>0:L1;1:L2;2:L3;3:L1-L2;4:L3-L1;5:L2-L3</convention>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x70010200a0001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x14</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_fan_ctrl_state</struct_var_name>
				<ID1>0x21</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Fan Ctrl</short_name>
					<full_name>SMR Fan Control State</full_name>
				</name>
				<convention>0:Auto;1:Full Speed</convention>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x70010200b0001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0x15</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_update_en</struct_var_name>
				<ID1>0x21</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Update Enable</short_name>
					<full_name>SMR Software Update Enable</full_name>
				</name>
				<convention>0:Disabled;1:Enabled</convention>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x70010200c0001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<ID2>0xFA</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_work_state</struct_var_name>
				<ID1>0x21</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>4</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Work Status</short_name>
					<full_name>SMR Work Status</full_name>
				</name>
				<convention>0:Null;1:Normal;2:Alarm;3:CommFail;4:Update</convention>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x70010200d0001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_fan_fault_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Fan Fault</short_name>
					<full_name>SMR Fan Fault State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x70010200e0001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_comm_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Communication State</short_name>
					<full_name>SMR Communication State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x70010200f0001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_high_temp_off_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Ra.T.H.O sta.</short_name>
					<full_name>SMR Radiator Temperature High Off State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001020100001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_acvolt_high_off_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR In.V.H.O sta.</short_name>
					<full_name>SMR Input Voltage High Off State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001020110001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_acvolt_low_off_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR In.V.L.O sta.</short_name>
					<full_name>SMR Input Voltage Low Off State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001020120001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_dcvolt_high_off_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Out.V.H.O sta.</short_name>
					<full_name>SMR Output Voltage High Off State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001020130001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_dccurr_high_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Out.C.H sta.</short_name>
					<full_name>SMR Output Current High State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001020140001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_pfc_fault_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR PFC.Fault sta.</short_name>
					<full_name>SMR PFC Fault State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001020150001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_inter_temp_high_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Inter.T.H sta.</short_name>
					<full_name>SMR Internal Temperature High State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001020160001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_out_fuse_break_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Out Fuse sta.</short_name>
					<full_name>SMR Output Fuse Broken State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001020170001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_share_curr_fault_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Curr Share sta.</short_name>
					<full_name>SMR Current Share Alarm State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001020180001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_acpower_off_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Input Off sta.</short_name>
					<full_name>SMR AC Input Power Off State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001020190001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_pfc_high_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>PFC Out.V.H sta.</short_name>
					<full_name>SMR PFC Output Voltage High State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x70010201a0001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_pfc_low_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>PFC Out.V.L sta.</short_name>
					<full_name>SMR PFC Output Voltage Low State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x70010201b0001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_eeprom_fault_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR EEPROM sta.</short_name>
					<full_name>SMR EEPROM Fault State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x70010201c0001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_inter_com_fail_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR In.Comm F sta.</short_name>
					<full_name>SMR Internal Communication Fail State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x70010201d0001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_accur_high_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Primy.C.H sta.</short_name>
					<full_name>SMR Primary Current High State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x70010201e0001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_pfc_over_curr_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>PFC Input C.H sta.</short_name>
					<full_name>SMR PFC Input Current High State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x70010201f0001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_slow_start_fault_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Start Abr sta.</short_name>
					<full_name>SMR Slow Start Abnormal State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001020200001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_fusebrk_in_com_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR In.Fuse sta.</short_name>
					<full_name>SMR Input Fuse Break State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001020210001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_acfreq_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR In.Freq sta.</short_name>
					<full_name>SMR Input Frequency Abnormal State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001020220001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_dcvolt_low_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Out.V.L sta.</short_name>
					<full_name>SMR Output Voltage Low State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001020230001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_out_over_load_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Out.O.P sta.</short_name>
					<full_name>SMR Output Over Power State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001020240001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_sn_clash_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR SN Clash sta.</short_name>
					<full_name>SMR SN Clash State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001020250001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_proto_err_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Pro.Err sta.</short_name>
					<full_name>SMR Protocol Error State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001020260001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_type_not_match_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR No Match sta.</short_name>
					<full_name>SMR Type No Match State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001020270001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_ext_temp_high_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Ext.O.T sta.</short_name>
					<full_name>SMR External Over Temperature State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001020280001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_res_temp_high_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Start.R.T.H sta.</short_name>
					<full_name>Starting Resistor Temperature High State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001020290001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_exist_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Exist Sta.</short_name>
					<full_name>SMR Exist State</full_name>
				</name>
				<convention>0:Not Exist;1:Is Exist</convention>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0x70010202a0001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_bus1_under_volt_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Bus1 Un.Volt Sta.</short_name>
					<full_name>SMR Bus1 Under Volt State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x70010202b0001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_bus2_under_volt_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Bus2 Un.Volt Sta.</short_name>
					<full_name>SMR Bus2 Under Volt State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x70010202c0001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_bus3_under_volt_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Bus3 Un.Volt Sta.</short_name>
					<full_name>SMR Bus3 Under Volt State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x70010202d0001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_bus4_under_volt_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Bus4 Un.Volt Sta.</short_name>
					<full_name>SMR Bus4 Under Volt State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x70010202e0001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_bus1_over_volt_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Bus1 Ov.Volt Sta.</short_name>
					<full_name>SMR Bus1 Over Volt State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x70010202f0001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_bus2_over_volt_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Bus2 Ov.Volt Sta.</short_name>
					<full_name>SMR Bus2 Over Volt State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001020300001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_bus3_over_volt_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Bus3 Ov.Volt Sta.</short_name>
					<full_name>SMR Bus3 Over Volt State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001020310001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_bus4_over_volt_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Bus4 Ov.Volt Sta.</short_name>
					<full_name>SMR Bus4 Over Volt State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001020320001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_bus_unbalance_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Unbalance State</short_name>
					<full_name>SMR Bus Unbalance State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0x7001020330001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_ac_moment_state</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Monment State</short_name>
					<full_name>SMR AC Monment State</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x7001030010001">
			<attr.s>
				<ID2>0x01</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_alarm</struct_var_name>
				<ID1>0x22</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Alarm</short_name>
					<full_name>SMR Alarm</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>1</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x7001030020001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x02</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_fan_fault</struct_var_name>
				<ID1>0x22</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Fan Fault</short_name>
					<full_name>SMR Fan Fault</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x7001030030001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x04</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_high_temp_off</struct_var_name>
				<ID1>0x22</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Ra.T.H.O</short_name>
					<full_name>SMR Radiator Temperature High Off</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x7001030040001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x09</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_acvolt_high_off</struct_var_name>
				<ID1>0x22</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR In.V.H.O</short_name>
					<full_name>SMR Input Voltage High Off</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>18</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x7001030050001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x0A</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_acvolt_low_off</struct_var_name>
				<ID1>0x22</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR In.V.L.O</short_name>
					<full_name>SMR Input Voltage Low Off</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>18</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x7001030060001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x0C</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_dcvolt_high_off</struct_var_name>
				<ID1>0x22</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Out.V.H.O</short_name>
					<full_name>SMR Output Voltage High Off</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x7001030070001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x0D</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_dccurr_high</struct_var_name>
				<ID1>0x22</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Out.C.H</short_name>
					<full_name>SMR Output Current High</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x7001030080001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x0E</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_pfc_fault</struct_var_name>
				<ID1>0x22</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR PFC.Fault</short_name>
					<full_name>SMR PFC Fault</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x7001030090001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x0F</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_inter_temp_high</struct_var_name>
				<ID1>0x22</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Inter.T.H</short_name>
					<full_name>SMR Internal Temperature High</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x70010300a0001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x11</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_out_fuse_break</struct_var_name>
				<ID1>0x22</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Out Fuse</short_name>
					<full_name>SMR Output Fuse Broken</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x70010300b0001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x13</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_share_curr_fault</struct_var_name>
				<ID1>0x22</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Curr Share</short_name>
					<full_name>SMR Current Share Alarm</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x70010300c0001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x14</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_acpower_off</struct_var_name>
				<ID1>0x22</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Input Off</short_name>
					<full_name>SMR AC Input Power Off</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>18</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x70010300d0001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x16</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_pfc_high</struct_var_name>
				<ID1>0x22</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>PFC Out.V.H</short_name>
					<full_name>SMR PFC Output Voltage High</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x70010300e0001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x17</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_pfc_low</struct_var_name>
				<ID1>0x22</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>PFC Out.V.L</short_name>
					<full_name>SMR PFC Output Voltage Low</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x70010300f0001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x18</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_eeprom_fault</struct_var_name>
				<ID1>0x22</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR EEPROM</short_name>
					<full_name>SMR EEPROM Fault</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x7001030100001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x19</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_inter_com_fail</struct_var_name>
				<ID1>0x22</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR In.Comm F</short_name>
					<full_name>SMR Internal Communication Fail</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x7001030110001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x1B</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_accur_high</struct_var_name>
				<ID1>0x22</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Primy.C.H</short_name>
					<full_name>SMR Primary Current High</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x7001030120001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x1D</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_pfc_over_curr</struct_var_name>
				<ID1>0x22</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>PFC Input C.H</short_name>
					<full_name>SMR PFC Input Current High</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x7001030130001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x1E</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_slow_start_fault</struct_var_name>
				<ID1>0x22</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Start Abr</short_name>
					<full_name>SMR Slow Start Abnormal</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x7001030140001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x1F</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_fusebrk_in_com_fail</struct_var_name>
				<ID1>0x22</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR In.Fuse</short_name>
					<full_name>SMR Input Fuse Break or Internal Comm.Broken</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x7001030150001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x20</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_acfreq_fault</struct_var_name>
				<ID1>0x22</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR In.Freq</short_name>
					<full_name>SMR Input Frequency Abnormal</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>18</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x7001030160001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x21</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_dcvolt_low</struct_var_name>
				<ID1>0x22</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Out.V.L</short_name>
					<full_name>SMR Output Voltage Low</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x7001030170001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x24</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_out_over_load</struct_var_name>
				<ID1>0x22</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Out.O.P</short_name>
					<full_name>SMR Output Over Power</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x7001030180001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x25</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_sn_clash</struct_var_name>
				<ID1>0x22</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR SN Clash</short_name>
					<full_name>SMR SN Clash</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x7001030190001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x26</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_proto_err</struct_var_name>
				<ID1>0x22</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Pro.Err</short_name>
					<full_name>SMR Protocol Error</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x70010301a0001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x28</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_type_not_match</struct_var_name>
				<ID1>0x22</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR No Match</short_name>
					<full_name>SMR Type No Match</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x70010301b0001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x29</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_ext_temp_high</struct_var_name>
				<ID1>0x22</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Ext.O.T</short_name>
					<full_name>SMR External Over Temperature</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x70010301c0001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x2A</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_res_temp_high</struct_var_name>
				<ID1>0x22</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Start.R.T.H</short_name>
					<full_name>Starting Resistor Temperature High Alarm</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x70010301d0001">
			<attr.s>
				<ID2>0xFF</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_com_fail</struct_var_name>
				<ID1>0x22</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Comm.Fail</short_name>
					<full_name>SMR Communication Fail</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x70010301e0001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_bus1_under_volt</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Bus1 Un.Volt</short_name>
					<full_name>SMR Bus1 Under Volt</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x70010301f0001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_bus2_under_volt</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Bus2 Un.Volt</short_name>
					<full_name>SMR Bus2 Under Volt</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x7001030200001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_bus3_under_volt</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Bus3 Un.Volt</short_name>
					<full_name>SMR Bus3 Under Volt</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x7001030210001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_bus4_under_volt</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Bus4 Un.Volt</short_name>
					<full_name>SMR Bus4 Under Volt</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x7001030220001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_bus1_over_volt</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Bus1 Ov.Volt</short_name>
					<full_name>SMR Bus1 Over Volt</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x7001030230001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_bus2_over_volt</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Bus2 Ov.Volt</short_name>
					<full_name>SMR Bus2 Over Volt</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x7001030240001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_bus3_over_volt</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Bus3 Ov.Volt</short_name>
					<full_name>SMR Bus3 Over Volt</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x7001030250001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_bus4_over_volt</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Bus4 Ov.Volt</short_name>
					<full_name>SMR Bus4 Over Volt</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x7001030260001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_bus_unbalance</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Unbalance</short_name>
					<full_name>SMR Bus Unbalance</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x7001030270001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_fault</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Fault</short_name>
					<full_name>SMR Fault</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>1</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x7001040010001">
			<attr.s>
				<ID2>0x01</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_ctrl_addr_adjust</struct_var_name>
				<ID1>0x24</ID1>
			</attr.s>
			<attr.r>
				<signal_type>4</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Mod SMR Addr</short_name>
					<full_name>Modify SMR Address</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x7001040020001">
			<attr.s>
				<ID2>0x04</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_ctrl_sleep</struct_var_name>
				<ID1>0x24</ID1>
			</attr.s>
			<attr.r>
				<signal_type>4</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Sleep</short_name>
					<full_name>SMR Sleep</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x7001040030001">
			<attr.s>
				<ID2>0x05</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_ctrl_waken</struct_var_name>
				<ID1>0x24</ID1>
			</attr.s>
			<attr.r>
				<signal_type>4</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Waken</short_name>
					<full_name>SMR Waken</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x7001040040001">
			<attr.s>
				<ID2>0x06</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_ctrl_p2p_enter</struct_var_name>
				<ID1>0x24</ID1>
			</attr.s>
			<attr.r>
				<signal_type>4</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Enter P2P</short_name>
					<full_name>SMR Enter Peer to Peer</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x7001040050001">
			<attr.s>
				<ID2>0x07</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_ctrl_p2p_quit</struct_var_name>
				<ID1>0x24</ID1>
			</attr.s>
			<attr.r>
				<signal_type>4</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Quit P2P</short_name>
					<full_name>SMR Quit Peer to Peer</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x7001040060001">
			<attr.s>
				<ID2>0x08</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_ctrl_fan_en</struct_var_name>
				<ID1>0x24</ID1>
			</attr.s>
			<attr.r>
				<signal_type>4</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Fan Ctrl.En</short_name>
					<full_name>SMR Fan Control Enable</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x7001040070001">
			<attr.s>
				<ID2>0x09</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_ctrl_fan_dis</struct_var_name>
				<ID1>0x24</ID1>
			</attr.s>
			<attr.r>
				<signal_type>4</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Fan Ctrl.Dis</short_name>
					<full_name>SMR Fan Control Disable</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x7001040080001">
			<attr.s>
				<ID2>0xF3</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_ctrl_reset</struct_var_name>
				<ID1>0x24</ID1>
			</attr.s>
			<attr.r>
				<signal_type>4</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Restart</short_name>
					<full_name>SMR Restart</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
			</attr.show>
		</sid>
		<sid value="0x7001040090001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_ctrl_comm_fail_alarm_clear</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>4</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Comm.Fail clear</short_name>
					<full_name>SMR Communication Fail alarm clear</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x7001050010001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_addr</struct_var_name>
			</attr.s>
			<attr.r>
				<para_type>2</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>1</default_value>
					<max_value>SMR_NUM</max_value>
					<min_value>1</min_value>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Addr</short_name>
					<full_name>SMR Address</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x7001060010001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_in_peak_volt</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<default_value>380</default_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>Instant Peak Volt.</short_name>
					<full_name>SMR Input Instant Peak Voltage</full_name>
				</name>
				<unit>V</unit>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x7001060020001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_bus_peak_volt</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<default_value>300</default_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>Bus Peak Voltage</short_name>
					<full_name>SMR Bus Peak Voltage</full_name>
				</name>
				<unit>V</unit>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x7001060030001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_effect_peak_volt</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<default_value>380</default_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>Effective Peak Volt.</short_name>
					<full_name>SMR Input Effective Peak Voltage</full_name>
				</name>
				<unit>V</unit>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x7001060040001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_in_peak_volt_count</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<default_value>380</default_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>Instant Peak Volt.Count</short_name>
					<full_name>SMR Instant Peak Voltage Count</full_name>
				</name>
				<unit>V</unit>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x7001060050001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_bus_peak_volt_count</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<default_value>300</default_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>Bus Peak Volt.Count</short_name>
					<full_name>SMR Bus Peak Voltage Count</full_name>
				</name>
				<unit>V</unit>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x7001060060001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_effect_peak_volt_count</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<default_value>380</default_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>Effect Peak Volt. Count</short_name>
					<full_name>SMR Effective Peak Voltage Count</full_name>
				</name>
				<unit>V</unit>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x7001080010001">
			<attr.s>
				<ID2>0x02</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_id</struct_var_name>
				<ID1>0x25</ID1>
			</attr.s>
			<attr.r>
				<signal_type>8</signal_type>
				<values>
					<data_type>string32</data_type>
				</values>
				<name>
					<short_name>SMR ID</short_name>
					<full_name>SMR ID</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x7001080020001">
			<attr.s>
				<ID2>0x04</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_max_rate_curr</struct_var_name>
				<ID1>0x25</ID1>
			</attr.s>
			<attr.r>
				<signal_type>8</signal_type>
				<values>
					<default_value>63.5</default_value>
					<max_value>150</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>Rating Out.Curr</short_name>
					<full_name>SMR Rating Maximum Output Current</full_name>
				</name>
				<unit>A</unit>
			</attr.r>
		</sid>
		<sid value="0x7001080030001">
			<attr.s>
				<ID2>0x05</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_pfc_version</struct_var_name>
				<ID1>0x25</ID1>
			</attr.s>
			<attr.r>
				<signal_type>8</signal_type>
				<values>
					<data_type>string32</data_type>
				</values>
				<name>
					<short_name>SMR PFC Ver</short_name>
					<full_name>SMR PFC Software Version</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x7001080040001">
			<attr.s>
				<ID2>0x06</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_dc_version</struct_var_name>
				<ID1>0x25</ID1>
			</attr.s>
			<attr.r>
				<signal_type>8</signal_type>
				<values>
					<data_type>string32</data_type>
				</values>
				<name>
					<short_name>SMR DC Ver</short_name>
					<full_name>SMR DC Software Version</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x7001080050001">
			<attr.s>
				<ID2>0x07</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_pfc_date</struct_var_name>
				<ID1>0x25</ID1>
			</attr.s>
			<attr.r>
				<signal_type>8</signal_type>
				<values>
					<data_type>string32</data_type>
				</values>
				<name>
					<short_name>SMR PFC Date</short_name>
					<full_name>SMR PFC Software Release Date</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x7001080060001">
			<attr.s>
				<ID2>0x08</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_dc_date</struct_var_name>
				<ID1>0x25</ID1>
			</attr.s>
			<attr.r>
				<signal_type>8</signal_type>
				<values>
					<data_type>string32</data_type>
				</values>
				<name>
					<short_name>SMR DC Date</short_name>
					<full_name>SMR DC Software Release Date</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x7001080070001">
			<attr.s>
				<ID2>0x0A</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_plat_verion</struct_var_name>
				<ID1>0x25</ID1>
			</attr.s>
			<attr.r>
				<signal_type>8</signal_type>
				<values>
					<data_type>string32</data_type>
				</values>
				<name>
					<short_name>SMR Platform</short_name>
					<full_name>SMR Digital Control Platform Version</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x7001080080001">
			<attr.s>
				<ID2>0xFD</ID2>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_sys_name</struct_var_name>
				<ID1>0x25</ID1>
			</attr.s>
			<attr.r>
				<signal_type>8</signal_type>
				<values>
					<data_type>string32</data_type>
				</values>
				<name>
					<short_name>SMR Sys Name</short_name>
					<full_name>SMR System Name</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x7001080090001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_manufactory_id</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>8</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Manufactory ID</short_name>
					<full_name>SMR Manufactory ID</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x70010800a0001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_manufactory_address</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>8</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Manufactory Address</short_name>
					<full_name>SMR Manufactory Address</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x70010800b0001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_barcodes</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>8</signal_type>
				<values>
					<data_type>string32</data_type>
				</values>
				<name>
					<short_name>SMR Barcodes</short_name>
					<full_name>SMR Barcodes</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x70010800c0001">
			<attr.s>
				<dev_macro>DEV_SMR</dev_macro>
				<struct_var_name>smr_manufacture_date</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>8</signal_type>
				<values>
					<data_type>string32</data_type>
				</values>
				<name>
					<short_name>Manufacture Date</short_name>
					<full_name>SMR Manufacture Date</full_name>
				</name>
			</attr.r>
		</sid>
	</device>
	<device name="Rectifier Group">
		<sid value="0x8001010010001">
			<attr.s>
				<ID2>0x03</ID2>
				<dev_macro>DEV_SMRS</dev_macro>
				<struct_var_name>smrs_total_cur</struct_var_name>
				<ID1>0x00</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>3000</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>Smr Total Curr</short_name>
					<full_name>SMR Total Output Current</full_name>
				</name>
				<unit>A</unit>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0x8001010020001">
			<attr.s>
				<ID2>0x06</ID2>
				<dev_macro>DEV_SMRS</dev_macro>
				<struct_var_name>smrs_set_volt_out</struct_var_name>
				<ID1>0x00</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>53.5</default_value>
					<max_value>58</max_value>
					<min_value>42</min_value>
					<precision>2</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>SMR Set Volt</short_name>
					<full_name>SMR Setting Voltage</full_name>
				</name>
				<unit>V</unit>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0x8001010030001">
			<attr.s>
				<ID2>0x1F</ID2>
				<dev_macro>DEV_SMRS</dev_macro>
				<struct_var_name>smrs_set_cur_limit</struct_var_name>
				<ID1>0x00</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>1000</default_value>
					<max_value>1000</max_value>
					<min_value>50</min_value>
					<precision>0</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>SMR CL Rate</short_name>
					<full_name>SMR Setting Current Limit Rate</full_name>
				</name>
				<unit>‰</unit>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0x8001010040001">
			<attr.s>
				<ID2>0x25</ID2>
				<dev_macro>DEV_SMRS</dev_macro>
				<struct_var_name>smrs_working_num</struct_var_name>
				<ID1>0x00</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>SMR_NUM</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Work Num</short_name>
					<full_name>SMR Working Number</full_name>
				</name>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0x8001010050001">
			<attr.s>
				<ID2>0x26</ID2>
				<dev_macro>DEV_SMRS</dev_macro>
				<struct_var_name>smrs_online_num</struct_var_name>
				<ID1>0x00</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>SMR_NUM</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Online Num</short_name>
					<full_name>SMR Online Number</full_name>
				</name>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0x8001010060001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<dev_macro>DEV_SMRS</dev_macro>
				<struct_var_name>smrs_set_volt_out_comp</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>53.5</default_value>
					<max_value>58</max_value>
					<min_value>42</min_value>
					<precision>2</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>SMR Set Volt Comp</short_name>
					<full_name>SMR Setting Voltage comp</full_name>
				</name>
				<unit>V</unit>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0x8001020010001">
			<attr.s>
				<ID2>0x04</ID2>
				<dev_macro>DEV_SMRS</dev_macro>
				<struct_var_name>smrs_save_mode</struct_var_name>
				<ID1>0x01</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>6</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>AC Save Energy Status</short_name>
					<full_name>AC Save Energy Status</full_name>
				</name>
				<convention>0:Safe;1:Free Mode;2:Auto NonSave;3:Auto Save;4:Temp NonSave;5:Perm NonSave;6:Manual Detect</convention>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0x8001030010001">
			<attr.s>
				<ID2>0x29</ID2>
				<dev_macro>DEV_SMRS</dev_macro>
				<struct_var_name>smrs_multi_smr_alarm</struct_var_name>
				<ID1>0x02</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Multi-SMR Alm</short_name>
					<full_name>Multi-SMR Alarm</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x8001040010001">
			<attr.s>
				<optional_attribute>save_energy_mode</optional_attribute>
				<ID2>0x0A</ID2>
				<dev_macro>DEV_SMRS</dev_macro>
				<struct_var_name>smrs_ctrl_auto_save</struct_var_name>
				<ID1>0x04</ID1>
			</attr.s>
			<attr.r>
				<signal_type>4</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Auto Save Ctrl</short_name>
					<full_name>Auto Save Control</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x8001040020001">
			<attr.s>
				<optional_attribute>save_energy_mode</optional_attribute>
				<ID2>0x0B</ID2>
				<dev_macro>DEV_SMRS</dev_macro>
				<struct_var_name>smrs_ctrl_temp_nonsave</struct_var_name>
				<ID1>0x04</ID1>
			</attr.s>
			<attr.r>
				<signal_type>4</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Temp NonSave Ctrl</short_name>
					<full_name>Temporary NonSave Control</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x8001040030001">
			<attr.s>
				<optional_attribute>save_energy_mode</optional_attribute>
				<ID2>0x0C</ID2>
				<dev_macro>DEV_SMRS</dev_macro>
				<struct_var_name>smrs_ctrl_perm_nonsave</struct_var_name>
				<ID1>0x04</ID1>
			</attr.s>
			<attr.r>
				<signal_type>4</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Perm NonSave Ctrl</short_name>
					<full_name>Permanent NonSave Control</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x8001040040001">
			<attr.s>
				<ID2>0x0D</ID2>
				<dev_macro>DEV_SMRS</dev_macro>
				<struct_var_name>smrs_ctrl_man_detece</struct_var_name>
				<ID1>0x04</ID1>
			</attr.s>
			<attr.r>
				<signal_type>4</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Manual Detect</short_name>
					<full_name>Manual Detect</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x8001040050001">
			<attr.s>
				<ID2>0x2E</ID2>
				<dev_macro>DEV_SMRS</dev_macro>
				<struct_var_name>smrs_ctrl_dev_tat</struct_var_name>
				<ID1>0x04</ID1>
			</attr.s>
			<attr.r>
				<signal_type>4</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>SMR Dev Stat</short_name>
					<full_name>SMR Device Statistic</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x8001040060001">
			<attr.s>
				<ID2>0x06</ID2>
				<dev_macro>DEV_SMRS</dev_macro>
				<struct_var_name>smrs_ctrl_test</struct_var_name>
				<ID1>0x14</ID1>
			</attr.s>
			<attr.r>
				<signal_type>4</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Start Smr Test</short_name>
					<full_name>Start Smr Test</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x8001050010001">
			<attr.s>
				<ID2>0x2E</ID2>
				<dev_macro>DEV_SMRS</dev_macro>
				<struct_var_name>smrs_work_mode</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<para_type>2</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>0</default_value>
					<data_type>int</data_type>
					<max_value>2</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<name>
					<short_name>AC Save Energy Mode</short_name>
					<full_name>AC Save Energy Mode</full_name>
				</name>
				<convention>0:Safe;1:Save;2:Free</convention>
			</attr.r>
		</sid>
		<sid value="0x8001050020001">
			<attr.s>
				<optional_attribute>save_energy_mode</optional_attribute>
				<ID2>0x2F</ID2>
				<dev_macro>DEV_SMRS</dev_macro>
				<struct_var_name>smrs_rotate_period</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<para_type>2</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>7</default_value>
					<data_type>int</data_type>
					<max_value>30</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<name>
					<short_name>SMR Rota Period</short_name>
					<full_name>SMR Rotated Period</full_name>
				</name>
				<unit>Day</unit>
			</attr.r>
		</sid>
		<sid value="0x8001050030001">
			<attr.s>
				<optional_attribute>save_or_safe_mode</optional_attribute>
				<ID2>0x45</ID2>
				<dev_macro>DEV_SMRS</dev_macro>
				<struct_var_name>smrs_min_num</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<para_type>3</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>3</default_value>
					<data_type>int</data_type>
					<max_value>3</max_value>
					<min_value>1</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<name>
					<short_name>Min Num Start SMR</short_name>
					<full_name>Minimum Qty of Started SMRs</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0x8001050040001">
			<attr.s>
				<optional_attribute>save_energy_mode</optional_attribute>
				<ID2>0x48</ID2>
				<dev_macro>DEV_SMRS</dev_macro>
				<struct_var_name>smrs_temp_nonsave_delay</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<para_type>2</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>1440</default_value>
					<data_type>int</data_type>
					<max_value>5940</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<name>
					<short_name>Temp NonSave Delay</short_name>
					<full_name>Temporary NonSave Delay</full_name>
				</name>
				<unit>Min</unit>
			</attr.r>
		</sid>
		<sid value="0x8001050050001">
			<attr.s>
				<ID2>0xA0</ID2>
				<dev_macro>DEV_SMRS</dev_macro>
				<struct_var_name>smrs_smart_cool</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<para_type>3</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>0</default_value>
					<data_type>int</data_type>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<name>
					<short_name>SMR Smart Cool En</short_name>
					<full_name>SMR Smart Cooling Enabled</full_name>
				</name>
				<convention>0:Disabled;1:Enabled</convention>
			</attr.r>
		</sid>
		<sid value="0x8001050060001">
			<attr.s>
				<ID2>0x04</ID2>
				<dev_macro>DEV_SMRS</dev_macro>
				<struct_var_name>smrs_soft_start_time</struct_var_name>
				<ID1>0x23</ID1>
			</attr.s>
			<attr.r>
				<para_type>2</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>0</default_value>
					<data_type>int</data_type>
					<max_value>128</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<name>
					<short_name>Soft Start Inter</short_name>
					<full_name>SMR Soft Start Interval</full_name>
				</name>
				<unit>Sec</unit>
			</attr.r>
		</sid>
		<sid value="0x8001050070001">
			<attr.s>
				<ID2>0x0B</ID2>
				<dev_macro>DEV_SMRS</dev_macro>
				<struct_var_name>smrs_shut_volt</struct_var_name>
				<ID1>0x23</ID1>
			</attr.s>
			<attr.r>
				<name>
					<short_name>Out High Off Volt</short_name>
					<full_name>SMR Output High Off Voltage</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>61</default_value>
					<data_type>float</data_type>
					<max_value>62</max_value>
					<min_value>56.5</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<para_type>2</para_type>
				<unit>V</unit>
				<constraints>
					<constraint operator="&gt;=" full_name="Battery Group|Equalized Charge Voltage" backlash="2"/>
					<constraint operator="&gt;=" full_name="Charge Voltage" backlash="2"/>
					<constraint operator="&gt;=" full_name="Battery Group|Float Charge Voltage" backlash="2"/>
				</constraints>
			</attr.r>
		</sid>
		<sid value="0x8001050080001">
			<attr.s>
				<ID2>0x10</ID2>
				<dev_macro>DEV_SMRS</dev_macro>
				<struct_var_name>smrs_def_volt</struct_var_name>
				<ID1>0x23</ID1>
			</attr.s>
			<attr.r>
				<para_type>2</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>50</default_value>
					<data_type>float</data_type>
					<max_value>58</max_value>
					<min_value>42</min_value>
					<precision>2</precision>
					<step>0.1</step>
				</values>
				<name>
					<short_name>Def Out Volt</short_name>
					<full_name>SMR Default Output Voltage</full_name>
				</name>
				<unit>V</unit>
			</attr.r>
		</sid>
		<sid value="0x8001050090001">
			<attr.s>
				<ID2>0x14</ID2>
				<dev_macro>DEV_SMRS</dev_macro>
				<struct_var_name>smrs_def_curr_limit</struct_var_name>
				<ID1>0x23</ID1>
			</attr.s>
			<attr.r>
				<para_type>2</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>1000</default_value>
					<data_type>float</data_type>
					<max_value>1000</max_value>
					<min_value>80</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<name>
					<short_name>SMR Def CL Rate</short_name>
					<full_name>SMR Default Current Limit Rate</full_name>
				</name>
				<unit>‰</unit>
			</attr.r>
		</sid>
		<sid value="0x80010500a0001">
			<attr.s>
				<ID2>0x15</ID2>
				<dev_macro>DEV_SMRS</dev_macro>
				<struct_var_name>smrs_max_num</struct_var_name>
				<ID1>0x23</ID1>
			</attr.s>
			<attr.r>
				<para_type>2</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>SMR_NUM</default_value>
					<data_type>int</data_type>
					<max_value>SMR_NUM</max_value>
					<min_value>SMR_NUM</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<name>
					<short_name>SMR Max Qty</short_name>
					<full_name>SMR Maximum Quantity</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
			</attr.show>
		</sid>
		<sid value="0x80010500b0001">
			<attr.s>
				<optional_attribute>save_or_safe_mode</optional_attribute>
				<ID2>0x17</ID2>
				<dev_macro>DEV_SMRS</dev_macro>
				<struct_var_name>smrs_max_roadrate</struct_var_name>
				<ID1>0x23</ID1>
			</attr.s>
			<attr.r>
				<para_type>2</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>0.8</default_value>
					<data_type>float</data_type>
					<max_value>1</max_value>
					<min_value>0.2</min_value>
					<precision>2</precision>
					<step>0.01</step>
				</values>
				<name>
					<short_name>Load Rate Max</short_name>
					<full_name>Save Load Rate Maximum</full_name>
				</name>
				<constraints>
					<constraint operator="&gt;=" full_name="Save Load Rate Minimum" backlash="0.1"/>
				</constraints>
			</attr.r>
		</sid>
		<sid value="0x80010500c0001">
			<attr.s>
				<optional_attribute>save_or_safe_mode</optional_attribute>
				<ID2>0x18</ID2>
				<dev_macro>DEV_SMRS</dev_macro>
				<struct_var_name>smrs_min_roadrate</struct_var_name>
				<ID1>0x23</ID1>
			</attr.s>
			<attr.r>
				<para_type>2</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>0.2</default_value>
					<data_type>float</data_type>
					<max_value>0.9</max_value>
					<min_value>0.1</min_value>
					<precision>2</precision>
					<step>0.01</step>
				</values>
				<name>
					<short_name>Load Rate Min</short_name>
					<full_name>Save Load Rate Minimum</full_name>
				</name>
				<constraints>
					<constraint operator="&lt;=" full_name="Save Load Rate Maximum" backlash="-0.1"/>
				</constraints>
			</attr.r>
		</sid>
		<sid value="0x80010500d0001">
			<attr.s>
				<ID2>0x19</ID2>
				<dev_macro>DEV_SMRS</dev_macro>
				<struct_var_name>smrs_curr_walkin_en</struct_var_name>
				<ID1>0x23</ID1>
			</attr.s>
			<attr.r>
				<para_type>3</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>0</default_value>
					<data_type>int</data_type>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<name>
					<short_name>Cur Walk-In En</short_name>
					<full_name>Current Walk-In Enable</full_name>
				</name>
				<convention>0:Disabled;1:Enabled</convention>
			</attr.r>
		</sid>
		<sid value="0x80010500e0001">
			<attr.s>
				<optional_attribute>current_walk_in_enabled</optional_attribute>
				<ID2>0x1A</ID2>
				<dev_macro>DEV_SMRS</dev_macro>
				<struct_var_name>smrs_curr_walkin_time</struct_var_name>
				<ID1>0x23</ID1>
			</attr.s>
			<attr.r>
				<para_type>2</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>8</default_value>
					<data_type>int</data_type>
					<max_value>200</max_value>
					<min_value>8</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<name>
					<short_name>Cur Walk-In Time</short_name>
					<full_name>Current Walk-In Time</full_name>
				</name>
				<unit>Sec</unit>
			</attr.r>
		</sid>
		<sid value="0x8001060010001">
			<attr.s>
				<optional_attribute>save_energy_mode</optional_attribute>
				<ID2>0x2F</ID2>
				<dev_macro>DEV_SMRS</dev_macro>
				<struct_var_name>smrs_next_rotate_time</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<data_type>time</data_type>
				</values>
				<name>
					<short_name>Next Rotate Time</short_name>
					<full_name>Next Rotate Time</full_name>
				</name>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
	</device>
	<device name="System Running Environment">
		<sid value="0x9001010010001">
			<attr.s>
				<ID2>0x01</ID2>
				<dev_macro>DEV_ENV</dev_macro>
				<struct_var_name>env_temp</struct_var_name>
				<ID1>0x40</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>25</default_value>
					<max_value>100</max_value>
					<min_value>-40</min_value>
					<precision>0</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>Environment Temp</short_name>
					<full_name>Environment Temperature</full_name>
				</name>
				<unit>℃</unit>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0x9001010020001">
			<attr.s>
				<ID2>0x02</ID2>
				<dev_macro>DEV_ENV</dev_macro>
				<struct_var_name>env_hum</struct_var_name>
				<ID1>0x40</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>50</default_value>
					<max_value>100</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>Environment Hum</short_name>
					<full_name>Environment Humidity</full_name>
				</name>
				<unit>%</unit>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0x9001020010001">
			<attr.s>
				<ID2>0x01</ID2>
				<dev_macro>DEV_ENV</dev_macro>
				<struct_var_name>env_smog</struct_var_name>
				<ID1>0x41</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Smog</short_name>
					<full_name>Smog Status</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x9001020020001">
			<attr.s>
				<ID2>0x02</ID2>
				<dev_macro>DEV_ENV</dev_macro>
				<struct_var_name>env_flood</struct_var_name>
				<ID1>0x41</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Flood</short_name>
					<full_name>Flood Status</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x9001020030001">
			<attr.s>
				<ID2>0x04</ID2>
				<dev_macro>DEV_ENV</dev_macro>
				<struct_var_name>env_door_mag</struct_var_name>
				<ID1>0x41</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Door Status</short_name>
					<full_name>Door Status</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x9001020040001">
			<attr.s>
				<ID2>0x03</ID2>
				<dev_macro>DEV_ENV</dev_macro>
				<struct_var_name>env_door_access</struct_var_name>
				<ID1>0x41</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Access Control</short_name>
					<full_name>Access Control Status</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x9001020050001">
			<attr.s>
				<ID2>0x07</ID2>
				<dev_macro>DEV_ENV</dev_macro>
				<struct_var_name>env_temp_fault</struct_var_name>
				<ID1>0x41</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Env.Temp.Sensor</short_name>
					<full_name>Environment Temperature Sensor Status</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0x9001020060001">
			<attr.s>
				<ID2>0x08</ID2>
				<dev_macro>DEV_ENV</dev_macro>
				<struct_var_name>env_hum_fault</struct_var_name>
				<ID1>0x41</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Env.Hum.Sensor</short_name>
					<full_name>Environment Humidity Sensor Status</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0x9001030010001">
			<attr.s>
				<ID2>0x05</ID2>
				<dev_macro>DEV_ENV</dev_macro>
				<struct_var_name>env_smog_alm</struct_var_name>
				<ID1>0x42</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Smog Alarm</short_name>
					<full_name>Smog Alarm</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x9001030020001">
			<attr.s>
				<ID2>0x06</ID2>
				<dev_macro>DEV_ENV</dev_macro>
				<struct_var_name>env_flood_alm</struct_var_name>
				<ID1>0x42</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Flood Alarm</short_name>
					<full_name>Flood Alarm</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x9001030030001">
			<attr.s>
				<ID2>0x08</ID2>
				<dev_macro>DEV_ENV</dev_macro>
				<struct_var_name>env_door_mag_alm</struct_var_name>
				<ID1>0x42</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Door Alarm</short_name>
					<full_name>Door Alarm</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x9001030040001">
			<attr.s>
				<ID2>0x07</ID2>
				<dev_macro>DEV_ENV</dev_macro>
				<struct_var_name>env_door_access</struct_var_name>
				<ID1>0x42</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Access Ctrl.Alm.</short_name>
					<full_name>Access Control Alarm</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>3</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x9001030050001">
			<attr.s>
				<ID2>0x0B</ID2>
				<dev_macro>DEV_ENV</dev_macro>
				<struct_var_name>env_temp_void</struct_var_name>
				<ID1>0x42</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Env.Temp.Invalid</short_name>
					<full_name>Environment Temperature Invalid</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>3</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x9001030060001">
			<attr.s>
				<ID2>0x01</ID2>
				<dev_macro>DEV_ENV</dev_macro>
				<struct_var_name>env_temp_high</struct_var_name>
				<ID1>0x42</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Env.Temp. High</short_name>
					<full_name>Environment Temperature High</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_backlash>-3</alm_backlash>
					<alm_backlash_type>1</alm_backlash_type>
					<alm_level>3</alm_level>
					<alm_backlash_value>-10;-1</alm_backlash_value>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x9001030070001">
			<attr.s>
				<ID2>0x02</ID2>
				<dev_macro>DEV_ENV</dev_macro>
				<struct_var_name>env_temp_low</struct_var_name>
				<ID1>0x42</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Env.Temp. Low</short_name>
					<full_name>Environment Temperature Low</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_backlash>3</alm_backlash>
					<alm_backlash_type>1</alm_backlash_type>
					<alm_level>3</alm_level>
					<alm_backlash_value>1;10</alm_backlash_value>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x9001030080001">
			<attr.s>
				<ID2>0x0C</ID2>
				<dev_macro>DEV_ENV</dev_macro>
				<struct_var_name>env_hum_void</struct_var_name>
				<ID1>0x42</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Env.Hum.Invalid</short_name>
					<full_name>Environment Humidity Invalid</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>4</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x9001030090001">
			<attr.s>
				<ID2>0x03</ID2>
				<dev_macro>DEV_ENV</dev_macro>
				<struct_var_name>env_hum_high</struct_var_name>
				<ID1>0x42</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Env.Hum. High</short_name>
					<full_name>Environment Humidity High</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_backlash>-3</alm_backlash>
					<alm_backlash_type>1</alm_backlash_type>
					<alm_level>3</alm_level>
					<alm_backlash_value>-10;-1</alm_backlash_value>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x90010300a0001">
			<attr.s>
				<ID2>0x04</ID2>
				<dev_macro>DEV_ENV</dev_macro>
				<struct_var_name>env_hum_low</struct_var_name>
				<ID1>0x42</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Env.Hum. Low</short_name>
					<full_name>Environment Humidity Low</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_backlash>3</alm_backlash>
					<alm_backlash_type>1</alm_backlash_type>
					<alm_level>3</alm_level>
					<alm_backlash_value>1;10</alm_backlash_value>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x90010300b0001">
			<attr.s>
				<dev_macro>DEV_ENV</dev_macro>
				<struct_var_name>env_temp_too_high</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Env.Temp. Too High</short_name>
					<full_name>Environment Temperature Too High</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_backlash>-3</alm_backlash>
					<alm_backlash_type>1</alm_backlash_type>
					<alm_level>3</alm_level>
					<alm_backlash_value>-10;-1</alm_backlash_value>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x90010300c0001">
			<attr.s>
				<dev_macro>DEV_ENV</dev_macro>
				<struct_var_name>env_temp_too_low</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Env.Temp.Too Low</short_name>
					<full_name>Environment Temperature Too Low</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_backlash>3</alm_backlash>
					<alm_backlash_type>1</alm_backlash_type>
					<alm_level>3</alm_level>
					<alm_backlash_value>1;10</alm_backlash_value>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x90010300d0001">
			<attr.s>
				<ID2>0x11</ID2>
				<dev_macro>DEV_ENV</dev_macro>
				<struct_var_name>env_temp_unit_fault</struct_var_name>
				<ID1>0x42</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>T.Ctrl Unit Alm</short_name>
					<full_name>Temperature Control Unit Alarm</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>3</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0x9001050010001">
			<attr.s>
				<ID2>0x0102</ID2>
				<dev_macro>DEV_ENV</dev_macro>
				<struct_var_name>env_temp_sensor_type</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<para_type>1</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>1</default_value>
					<data_type>int</data_type>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<name>
					<short_name>Temp.Sen.Type</short_name>
					<full_name>Temperature Sensor Type</full_name>
				</name>
				<convention>0:AD590;1:NTC</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0x9001050020001">
			<attr.s>
				<ID2>0x01</ID2>
				<dev_macro>DEV_ENV</dev_macro>
				<struct_var_name>env_temp_max</struct_var_name>
				<ID1>0x43</ID1>
			</attr.s>
			<attr.r>
				<name>
					<short_name>Env.Temp.H.Thre.</short_name>
					<full_name>Environment Temperature High Threshold</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>55</default_value>
					<data_type>float</data_type>
					<max_value>60</max_value>
					<min_value>30</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>℃</unit>
				<constraints>
					<constraint operator="&lt;=" full_name="Environment Temperature Too High Threshold" backlash="-3"/>
				</constraints>
			</attr.r>
		</sid>
		<sid value="0x9001050030001">
			<attr.s>
				<ID2>0x02</ID2>
				<dev_macro>DEV_ENV</dev_macro>
				<struct_var_name>env_temp_min</struct_var_name>
				<ID1>0x43</ID1>
			</attr.s>
			<attr.r>
				<name>
					<short_name>Env.Temp.L.Thre.</short_name>
					<full_name>Environment Temperature Low Threshold</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>-5</default_value>
					<data_type>float</data_type>
					<max_value>20</max_value>
					<min_value>-30</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>℃</unit>
				<constraints>
					<constraint operator="&gt;=" full_name="Environment Temperature Too Low Threshold" backlash="3"/>
				</constraints>
			</attr.r>
		</sid>
		<sid value="0x9001050040001">
			<attr.s>
				<ID2>0x03</ID2>
				<dev_macro>DEV_ENV</dev_macro>
				<struct_var_name>env_hum_max</struct_var_name>
				<ID1>0x43</ID1>
			</attr.s>
			<attr.r>
				<para_type>2</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>90</default_value>
					<data_type>float</data_type>
					<max_value>100</max_value>
					<min_value>70</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<name>
					<short_name>Env.Hum.H.Thre.</short_name>
					<full_name>Environment Humidity High Threshold</full_name>
				</name>
				<unit>%</unit>
			</attr.r>
		</sid>
		<sid value="0x9001050050001">
			<attr.s>
				<ID2>0x04</ID2>
				<dev_macro>DEV_ENV</dev_macro>
				<struct_var_name>env_hum_min</struct_var_name>
				<ID1>0x43</ID1>
			</attr.s>
			<attr.r>
				<para_type>2</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>20</default_value>
					<data_type>float</data_type>
					<max_value>50</max_value>
					<min_value>10</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<name>
					<short_name>Env.Hum.L.Thre.</short_name>
					<full_name>Environment Humidity Low Threshold</full_name>
				</name>
				<unit>%</unit>
			</attr.r>
		</sid>
		<sid value="0x9001050060001">
			<attr.s>
				<dev_macro>DEV_ENV</dev_macro>
				<struct_var_name>env_temp_too_max</struct_var_name>
				<ID1>0x43</ID1>
			</attr.s>
			<attr.r>
				<name>
					<short_name>Env.Temp.Too H.Thre.</short_name>
					<full_name>Environment Temperature Too High Threshold</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>58</default_value>
					<data_type>float</data_type>
					<max_value>63</max_value>
					<min_value>33</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>℃</unit>
				<constraints>
					<constraint operator="&gt;=" full_name="Environment Temperature High Threshold" backlash="3"/>
				</constraints>
			</attr.r>
		</sid>
		<sid value="0x9001050070001">
			<attr.s>
				<dev_macro>DEV_ENV</dev_macro>
				<struct_var_name>env_temp_too_min</struct_var_name>
				<ID1>0x43</ID1>
			</attr.s>
			<attr.r>
				<name>
					<short_name>Env.Temp.Too L.Thre.</short_name>
					<full_name>Environment Temperature Too Low Threshold</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>-8</default_value>
					<data_type>float</data_type>
					<max_value>17</max_value>
					<min_value>-33</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>℃</unit>
				<constraints>
					<constraint operator="&lt;=" full_name="Environment Temperature Low Threshold" backlash="-3"/>
				</constraints>
			</attr.r>
		</sid>
	</device>
	<device name="DC Distribution">
		<sid value="0xa001010010001">
			<attr.s>
				<ID2>0x01</ID2>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_dc_vol</struct_var_name>
				<ID1>0x30</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>48</default_value>
					<max_value>60</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>DC Voltage</short_name>
					<full_name>DC Voltage</full_name>
				</name>
				<unit>V</unit>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xa001010020001">
			<attr.s>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_dc_curr</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>3000</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>DC Load Current</short_name>
					<full_name>DC Load Current</full_name>
				</name>
				<unit>A</unit>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xa001010030001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
			<attr.s>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_batt_shunt_curr</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>3000</max_value>
					<min_value>-3000</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>BATT_NUM</dimension>
				</dimensions>
				<unit>A</unit>
				<name>
					<short_name>Batt.Shunt Curr.</short_name>
					<full_name>Battery Shunt Current</full_name>
				</name>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xa001010040001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
			<attr.s>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_batt_blcok_vol_det</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>60</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>BATT_BLOCK_NUM</dimension>
				</dimensions>
				<name>
					<short_name>Batt.Block Vol.Det.</short_name>
					<full_name>Battery Block Voltage Detect</full_name>
				</name>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xa001020010001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
			<attr.s>
				<ID2>0x02</ID2>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_dc_arrest</struct_var_name>
				<ID1>0x31</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>DCLP</short_name>
					<full_name>DCLP Status</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xa001020020001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
			<attr.s>
				<optional_attribute>load_no_disconnect</optional_attribute>
				<ID2>0x01</ID2>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_dc_loop_state</struct_var_name>
				<ID1>0x31</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>DC_LOOP_NUM</dimension>
				</dimensions>
				<name>
					<short_name>DC Loop</short_name>
					<full_name>DC Loop Status</full_name>
				</name>
				<convention>0:Close;1:Break</convention>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xa001020030001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
			<attr.s>
				<optional_attribute>load_no_disconnect</optional_attribute>
				<ID2>0x0E</ID2>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_dc_ext_loop_status</struct_var_name>
				<ID1>0x31</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>DC_LOOP_EXT_NUM</dimension>
				</dimensions>
				<name>
					<short_name>DC Ext.Loop</short_name>
					<full_name>DC Extend Loop Status</full_name>
				</name>
				<convention>0:Close;1:Break</convention>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xa001020040001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
			<attr.s>
				<optional_attribute>llvd1_conf</optional_attribute>
				<ID2>0x07</ID2>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_llvd1_loop_status</struct_var_name>
				<ID1>0x31</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>LLVD1_LOAD_NUM</dimension>
				</dimensions>
				<name>
					<short_name>LLVD1 Loop</short_name>
					<full_name>LLVD1 Loop Status</full_name>
				</name>
				<convention>0:Close;1:Break</convention>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xa001020050001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
			<attr.s>
				<optional_attribute>llvd1_conf</optional_attribute>
				<ID2>0x06</ID2>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_llvd1_loop_e_status</struct_var_name>
				<ID1>0x31</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>LLVD1 Extend Loop</short_name>
					<full_name>LLVD1 Extend Loop Status</full_name>
				</name>
				<convention>0:Close;1:Break</convention>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xa001020060001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
			<attr.s>
				<optional_attribute>llvd2_conf</optional_attribute>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_llvd2_loop_status</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>LLVD2_LOAD_NUM</dimension>
				</dimensions>
				<name>
					<short_name>LLVD2 Loop</short_name>
					<full_name>LLVD2 Loop Status</full_name>
				</name>
				<convention>0:Close;1:Break</convention>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xa001020070001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
			<attr.s>
				<optional_attribute>llvd2_conf</optional_attribute>
				<ID2>0x08</ID2>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_llvd2_loop_e_status</struct_var_name>
				<ID1>0x31</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>LLVD2 Extend Loop</short_name>
					<full_name>LLVD2 Extend Loop Status</full_name>
				</name>
				<convention>0:Close;1:Break</convention>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xa001020080001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
			<attr.s>
				<optional_attribute>blvd_conf</optional_attribute>
				<ID2>0x0D</ID2>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_blvd_loop_status</struct_var_name>
				<ID1>0x31</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>BLVD_LOAD_NUM</dimension>
				</dimensions>
				<name>
					<short_name>BLVD Loop</short_name>
					<full_name>BLVD Loop Status</full_name>
				</name>
				<convention>0:Close;1:Break</convention>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xa001020090001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
			<attr.s>
				<optional_attribute>blvd_conf</optional_attribute>
				<ID2>0x0C</ID2>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_blvd_loop_e_status</struct_var_name>
				<ID1>0x31</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>BLVD Extend Loop</short_name>
					<full_name>BLVD Extend Loop Status</full_name>
				</name>
				<convention>0:Close;1:Break</convention>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xa0010200a0001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
			<attr.s>
				<optional_attribute>llvd1_conf</optional_attribute>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_llvd1_load_module_status</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>DC_LOOP_NUM</dimension>
				</dimensions>
				<name>
					<short_name>LLVD1 Load Module</short_name>
					<full_name>LLVD1 Load Module status</full_name>
				</name>
				<convention>0:Close;1:Break</convention>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xa0010200b0001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
			<attr.s>
				<optional_attribute>llvd2_conf</optional_attribute>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_llvd2_load_module_status</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>DC_LOOP_NUM</dimension>
				</dimensions>
				<name>
					<short_name>LLVD2 Load Module</short_name>
					<full_name>LLVD2 Load Module status</full_name>
				</name>
				<convention>0:Close;1:Break</convention>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xa0010200c0001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
			<attr.s>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_f09_detect_channel_status</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>F09 Detect Ch.Status</short_name>
					<full_name>F09 Detect Channel Status</full_name>
				</name>
				<convention>0:Close;1:Break</convention>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xa0010200d0001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
			<attr.s>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_f10_detect_channel_status</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>F10 Detect Ch.Status</short_name>
					<full_name>F10 Detect Channel Status</full_name>
				</name>
				<convention>0:Close;1:Break</convention>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xa0010200e0001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
			<attr.s>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_f11_detect_channel_status</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>F11 Detect Ch.Status</short_name>
					<full_name>F11 Detect Channel Status</full_name>
				</name>
				<convention>0:Close;1:Break</convention>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xa0010200f0001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
			<attr.s>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_f12_detect_channel_status</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>F12 Detect Ch.Status</short_name>
					<full_name>F12 Detect Channel Status</full_name>
				</name>
				<convention>0:Close;1:Break</convention>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xa001020100001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
			<attr.s>
				<optional_attribute>blvd_conf</optional_attribute>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_blvd_load_module_status</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>DC_LOOP_NUM</dimension>
				</dimensions>
				<name>
					<short_name>BLVD Load Module</short_name>
					<full_name>BLVD Load Module status</full_name>
				</name>
				<convention>0:Close;1:Break</convention>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xa001030010001">
			<attr.s>
				<ID2>0x04</ID2>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_dc_arrest_abnormal</struct_var_name>
				<ID1>0x32</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>DCLP Abr</short_name>
					<full_name>DCLP Abnormal</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0xa001030020001">
			<attr.s>
				<optional_attribute>load_no_disconnect</optional_attribute>
				<ID2>0x03</ID2>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_dc_loop_brk</struct_var_name>
				<ID1>0x32</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>DC_LOOP_NUM</dimension>
				</dimensions>
				<name>
					<short_name>DC.Loop.Brk</short_name>
					<full_name>DC Loop Broken</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>40</alm_delay>
					<alm_level>1</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0xa001030030001">
			<attr.s>
				<optional_attribute>load_no_disconnect</optional_attribute>
				<ID2>0x1F</ID2>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_dc_ext_loop_brk</struct_var_name>
				<ID1>0x32</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>DC_LOOP_EXT_NUM</dimension>
				</dimensions>
				<name>
					<short_name>Load Ext.Brk.</short_name>
					<full_name>Load Extend Loop Broken</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>40</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0xa001030040001">
			<attr.s>
				<ID2>0x01</ID2>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_dc_vol_high_alm</struct_var_name>
				<ID1>0x32</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>DC Volt.High</short_name>
					<full_name>DC Voltage High</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_backlash>-0.5</alm_backlash>
					<alm_backlash_type>1</alm_backlash_type>
					<alm_level>2</alm_level>
					<alm_backlash_value>-0.8;-0.1</alm_backlash_value>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0xa001030050001">
			<attr.s>
				<ID2>0x02</ID2>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_dc_vol_low_alm</struct_var_name>
				<ID1>0x32</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>DC Volt.Low</short_name>
					<full_name>DC Voltage Low</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_backlash>0.5</alm_backlash>
					<alm_backlash_type>1</alm_backlash_type>
					<alm_level>2</alm_level>
					<alm_backlash_value>0.1;0.8</alm_backlash_value>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0xa001030060001">
			<attr.s>
				<optional_attribute>llvd1_conf</optional_attribute>
				<ID2>0x11</ID2>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_llvd1_loop_brk</struct_var_name>
				<ID1>0x32</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>LLVD1_LOAD_NUM</dimension>
				</dimensions>
				<name>
					<short_name>LLVD1 Loop Brk.</short_name>
					<full_name>LLVD1 Loop Broken</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>40</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0xa001030070001">
			<attr.s>
				<optional_attribute>llvd1_conf</optional_attribute>
				<ID2>0x10</ID2>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_llvd1_loop_e_brk</struct_var_name>
				<ID1>0x32</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>LLVD1 Extend Brk.</short_name>
					<full_name>LLVD1 Extend Loop Broken</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>40</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0xa001030080001">
			<attr.s>
				<optional_attribute>llvd2_conf</optional_attribute>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_llvd2_loop_brk</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>LLVD2_LOAD_NUM</dimension>
				</dimensions>
				<name>
					<short_name>LLVD2 Loop Brk.</short_name>
					<full_name>LLVD2 Loop Broken</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>40</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0xa001030090001">
			<attr.s>
				<optional_attribute>llvd2_conf</optional_attribute>
				<ID2>0x12</ID2>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_llvd2_loop_e_brk</struct_var_name>
				<ID1>0x32</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>LLVD2 Extend Brk.</short_name>
					<full_name>LLVD2 Extend Loop Broken</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>40</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0xa0010300a0001">
			<attr.s>
				<optional_attribute>blvd_conf</optional_attribute>
				<ID2>0x17</ID2>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_blvd_loop</struct_var_name>
				<ID1>0x32</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>BLVD_LOAD_NUM</dimension>
				</dimensions>
				<name>
					<short_name>BLVD Loop Brk.</short_name>
					<full_name>BLVD Loop Broken</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>40</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0xa0010300b0001">
			<attr.s>
				<optional_attribute>blvd_conf</optional_attribute>
				<ID2>0x16</ID2>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_blvd_loop_e</struct_var_name>
				<ID1>0x32</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>BLVD Extend Brk.</short_name>
					<full_name>BLVD Extend Loop Broken</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>40</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0xa0010300c0001">
			<attr.s>
				<optional_attribute>llvd1_conf</optional_attribute>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_llvd1_load_module_alm</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>DC_LOOP_NUM</dimension>
				</dimensions>
				<name>
					<short_name>LLVD1 Load Module Alm.</short_name>
					<full_name>LLVD1 Load Module Alarm</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>40</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0xa0010300d0001">
			<attr.s>
				<optional_attribute>llvd2_conf</optional_attribute>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_llvd2_load_module_alm</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>DC_LOOP_NUM</dimension>
				</dimensions>
				<name>
					<short_name>LLVD2 Load Module Alm.</short_name>
					<full_name>LLVD2 Load Module Alarm</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>40</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0xa0010300e0001">
			<attr.s>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_dc_vol_too_high_alm</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>DC Volt.T.High</short_name>
					<full_name>DC Voltage Too High</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_backlash>-0.5</alm_backlash>
					<alm_backlash_type>1</alm_backlash_type>
					<alm_level>2</alm_level>
					<alm_backlash_value>-0.8;-0.1</alm_backlash_value>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0xa0010300f0001">
			<attr.s>
				<ID2>0x02</ID2>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_dc_vol_too_low_alm</struct_var_name>
				<ID1>0x32</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>DC Volt.T.Low</short_name>
					<full_name>DC Voltage Too Low</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_backlash>0.5</alm_backlash>
					<alm_backlash_type>1</alm_backlash_type>
					<alm_level>2</alm_level>
					<alm_backlash_value>0.1;0.8</alm_backlash_value>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0xa001030100001">
			<attr.s>
				<optional_attribute>blvd_conf</optional_attribute>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_blvd_load_module_alm</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>DC_LOOP_NUM</dimension>
				</dimensions>
				<name>
					<short_name>BLVD Load Module Alm.</short_name>
					<full_name>BLVD Load Module Alarm</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>40</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0xa001050010001">
			<attr.s>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_dc_vol_max</struct_var_name>
			</attr.s>
			<attr.r>
				<name>
					<short_name>DC Volt.High Thre.</short_name>
					<full_name>DC Voltage High Threshold</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>58.5</default_value>
					<data_type>float</data_type>
					<max_value>59</max_value>
					<min_value>53</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<para_type>2</para_type>
				<unit>V</unit>
				<constraints>
					<constraint operator="&gt;=" full_name="DC Voltage Low Threshold" backlash="1"/>
					<constraint operator="&lt;=" full_name="DC Voltage Too High Threshold"/>
				</constraints>
			</attr.r>
		</sid>
		<sid value="0xa001050020001">
			<attr.s>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_dc_vol_min</struct_var_name>
			</attr.s>
			<attr.r>
				<name>
					<short_name>DC Volt.Low Thre.</short_name>
					<full_name>DC Voltage Low Threshold</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>46.5</default_value>
					<data_type>float</data_type>
					<max_value>55</max_value>
					<min_value>44</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<para_type>2</para_type>
				<unit>V</unit>
				<constraints>
					<constraint operator="&lt;=" full_name="DC Voltage High Threshold" backlash="-1"/>
					<constraint operator="&gt;=" full_name="DC Voltage Too Low Threshold"/>
				</constraints>
			</attr.r>
		</sid>
		<sid value="0xa001050030001">
			<attr.s>
				<optional_attribute>load_shunt_no_conf</optional_attribute>
				<ID2>0x0E</ID2>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_dc_cur_slope</struct_var_name>
				<ID1>0x33</ID1>
			</attr.s>
			<attr.r>
				<para_type>3</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>1</default_value>
					<data_type>float</data_type>
					<max_value>5</max_value>
					<min_value>0.001</min_value>
					<precision>3</precision>
					<step>0.001</step>
				</values>
				<name>
					<short_name>Load Curr.Slope</short_name>
					<full_name>Load Current Slope</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
			</attr.show>
		</sid>
		<sid value="0xa001050040001">
			<attr.s>
				<optional_attribute>load_shunt_no_conf</optional_attribute>
				<ID2>0x0D</ID2>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_dc_cur_zero</struct_var_name>
				<ID1>0x33</ID1>
			</attr.s>
			<attr.r>
				<para_type>3</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>0</default_value>
					<data_type>float</data_type>
					<max_value>50</max_value>
					<min_value>-50</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<name>
					<short_name>Load Curr.Offset</short_name>
					<full_name>Load Current Offset</full_name>
				</name>
				<unit>A</unit>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
			</attr.show>
		</sid>
		<sid value="0xa001050050001">
			<attr.s>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_dc_vol_too_high_thres</struct_var_name>
			</attr.s>
			<attr.r>
				<name>
					<short_name>DC Volt.T.High Thre.</short_name>
					<full_name>DC Voltage Too High Threshold</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>59</default_value>
					<data_type>float</data_type>
					<max_value>59</max_value>
					<min_value>53</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<para_type>2</para_type>
				<unit>V</unit>
				<constraints>
					<constraint operator="&gt;=" full_name="DC Voltage High Threshold"/>
				</constraints>
			</attr.r>
		</sid>
		<sid value="0xa001050060001">
			<attr.s>
				<dev_macro>DEV_DCD</dev_macro>
				<struct_var_name>dcd_dc_vol_too_low_thres</struct_var_name>
			</attr.s>
			<attr.r>
				<name>
					<short_name>DC Volt.T.Low Thre.</short_name>
					<full_name>DC Voltage Too Low Threshold</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>46</default_value>
					<data_type>float</data_type>
					<max_value>55</max_value>
					<min_value>44</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<para_type>2</para_type>
				<unit>V</unit>
				<constraints>
					<constraint operator="&lt;=" full_name="DC Voltage Low Threshold"/>
				</constraints>
			</attr.r>
		</sid>
	</device>
	<device name="Single Group Battery">
		<sid value="0xb001010010001">
			<attr.s>
				<ID2>0x04</ID2>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_bat_vol</struct_var_name>
				<ID1>0x30</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>48</default_value>
					<max_value>60</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>V</unit>
				<name>
					<short_name>Battery Volt</short_name>
					<full_name>Battery Voltage</full_name>
				</name>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xb001010020001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<optional_attribute>vrla_batt</optional_attribute>
				<ID2>0x0B</ID2>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_bat_mid_vol</struct_var_name>
				<ID1>0x30</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>24</default_value>
					<max_value>30</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>V</unit>
				<name>
					<short_name>Batt.Midd.Volt</short_name>
					<full_name>Battery Middle Voltage</full_name>
				</name>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0xb001010030001">
			<attr.s>
				<ID2>0x05</ID2>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_bat_cur</struct_var_name>
				<ID1>0x30</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>3000</max_value>
					<min_value>-3000</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>A</unit>
				<name>
					<short_name>Battery Curr</short_name>
					<full_name>Battery Current</full_name>
				</name>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xb001010040001">
			<attr.s>
				<ID2>0x06</ID2>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_bat_temp</struct_var_name>
				<ID1>0x30</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>25</default_value>
					<max_value>100</max_value>
					<min_value>-40</min_value>
					<precision>0</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>℃</unit>
				<name>
					<short_name>Battery Temp</short_name>
					<full_name>Battery Temperature</full_name>
				</name>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xb001010050001">
			<attr.s>
				<ID2>0x0C</ID2>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_bat_est_time</struct_var_name>
				<ID1>0x30</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>65535</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>Min</unit>
				<name>
					<short_name>Estimate Dura.</short_name>
					<full_name>Estimated Battery Discharge Duration</full_name>
				</name>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xb001010060001">
			<attr.s>
				<ID2>0x0E</ID2>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_bat_cap</struct_var_name>
				<ID1>0x30</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>100</default_value>
					<max_value>100</max_value>
					<min_value>5</min_value>
					<precision>0</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>%</unit>
				<name>
					<short_name>Batt.Prst.SOC</short_name>
					<full_name>Battery Present SOC</full_name>
				</name>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xb001010070001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x63</ID2>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_bat_current_cycle_time</struct_var_name>
				<ID1>0x30</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>100000</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Total Cyc.Times</short_name>
					<full_name>Battery Total Recycle Times</full_name>
				</name>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xb001010080001">
			<attr.s>
				<ID2>0x4C</ID2>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_bat_soh</struct_var_name>
				<ID1>0x30</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<precision>0</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>%</unit>
				<name>
					<short_name>Battery SOH</short_name>
					<full_name>Battery SOH</full_name>
				</name>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xb001010090001">
			<attr.s>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_bat_real_cap</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>200</default_value>
					<max_value>9999</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>AH</unit>
				<name>
					<short_name>Batt.Prst.Cap</short_name>
					<full_name>Battery Present Cap</full_name>
				</name>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xb0010100a0001">
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
			<attr.s>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_bat_block_vol</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>15</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>BATT_BLOCK_NUM</dimension>
				</dimensions>
				<unit>V</unit>
				<name>
					<short_name>Batt.Block Volt.</short_name>
					<full_name>Battery Block Voltage</full_name>
				</name>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0xb001020010001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
			<attr.s>
				<ID2>0x03</ID2>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_loop_state</struct_var_name>
				<ID1>0x31</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>Battery Loop</short_name>
					<full_name>Battery Loop Status</full_name>
				</name>
				<convention>0:Close;1:Break</convention>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xb001020020001">
			<attr.s>
				<ID2>0x04</ID2>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_bat_temp_fault</struct_var_name>
				<ID1>0x31</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Batt.T.Sensor</short_name>
					<full_name>Battery Temperature Sensor Status</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xb001020030001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x46</ID2>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_bat_health_stat</struct_var_name>
				<ID1>0x31</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>2</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Battery Health</short_name>
					<full_name>Battery Health</full_name>
				</name>
				<convention>0:Health;1:Sub-health;2:Fault</convention>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xb001030010001">
			<attr.s>
				<ID2>0x08</ID2>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_bat_temp_high</struct_var_name>
				<ID1>0x32</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Batt.Temp.High</short_name>
					<full_name>Battery Temperature High</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>3</alm_delay>
					<alm_backlash>-3</alm_backlash>
					<alm_backlash_type>1</alm_backlash_type>
					<alm_level>2</alm_level>
					<alm_backlash_value>-10;-1</alm_backlash_value>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0xb001030020001">
			<attr.s>
				<ID2>0x09</ID2>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_bat_temp_Low</struct_var_name>
				<ID1>0x32</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Batt.Temp.Low</short_name>
					<full_name>Battery Temperature Low</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>3</alm_delay>
					<alm_backlash>3</alm_backlash>
					<alm_backlash_type>1</alm_backlash_type>
					<alm_level>2</alm_level>
					<alm_backlash_value>1;10</alm_backlash_value>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0xb001030030001">
			<attr.s>
				<ID2>0x0A</ID2>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_bat_loop_brk</struct_var_name>
				<ID1>0x32</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Batt.Loop Brk</short_name>
					<full_name>Battery Loop Broken</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0xb001030040001">
			<attr.s>
				<ID2>0x0C</ID2>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_bat_temp_void</struct_var_name>
				<ID1>0x32</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Batt.T.Invalid</short_name>
					<full_name>Battery Temperature Invalid</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>3</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0xb001030050001">
			<attr.s>
				<ID2>0x05</ID2>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_bat_vol_low</struct_var_name>
				<ID1>0x32</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Batt.Volt.Low</short_name>
					<full_name>Battery Voltage Low</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>3</alm_delay>
					<alm_backlash>0.5</alm_backlash>
					<alm_backlash_type>1</alm_backlash_type>
					<alm_level>2</alm_level>
					<alm_backlash_value>0.1;0.8</alm_backlash_value>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0xb001030060001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x76</ID2>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_soh_fault</struct_var_name>
				<ID1>0x32</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Batt.Fault</short_name>
					<full_name>Battery Fault</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>3</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0xb001030070001">
			<attr.s>
				<ID2>0x0B</ID2>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_bat_discharge</struct_var_name>
				<ID1>0x32</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Batt.Dischg.</short_name>
					<full_name>Battery Discharge</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>7</alm_delay>
					<alm_backlash>2</alm_backlash>
					<alm_backlash_type>1</alm_backlash_type>
					<alm_level>2</alm_level>
					<alm_backlash_value>1;3</alm_backlash_value>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0xb001030080001">
			<attr.s>
				<ID2>0x0E</ID2>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_bat_missing</struct_var_name>
				<ID1>0x32</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Batt. Missing</short_name>
					<full_name>Battery Missing</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>3</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0xb001030090001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x07</ID2>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_bat_cur_fault</struct_var_name>
				<ID1>0x32</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Batt.Curr.Abr.</short_name>
					<full_name>Battery Current Abnormal</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>3</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0xb0010300a0001">
			<attr.s>
				<ID2>0x01</ID2>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_bat_test_fail</struct_var_name>
				<ID1>0x02</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Batt.Test Fail</short_name>
					<full_name>Battery Test Fail</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>3</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0xb0010300b0001">
			<attr.s>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_bat_vol_too_low</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Batt.Volt.too Low</short_name>
					<full_name>Battery Voltage Too Low</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>3</alm_delay>
					<alm_backlash>0.5</alm_backlash>
					<alm_backlash_type>1</alm_backlash_type>
					<alm_level>2</alm_level>
					<alm_backlash_value>0.1;0.8</alm_backlash_value>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0xb001050010001">
			<attr.s>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_bat_start_use_date</struct_var_name>
			</attr.s>
			<attr.r>
				<para_type>3</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>2037/12/31</default_value>
					<data_type>date</data_type>
					<max_value>2037/12/31</max_value>
					<min_value>2000/1/1</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Batt.Start Use Date</short_name>
					<full_name>Battery Start Use Date</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xb001060010001">
			<attr.s>
				<ID2>0x0F</ID2>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_batt_initial_cap</struct_var_name>
				<ID1>0x30</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<default_value>100</default_value>
					<max_value>100</max_value>
					<min_value>5</min_value>
					<precision>0</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>%</unit>
				<name>
					<short_name>Batt.Init.SOC</short_name>
					<full_name>Battery Initial SOC</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xb001060020001">
			<attr.s>
				<ID2>0x10</ID2>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_batt_final_cap</struct_var_name>
				<ID1>0x30</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<default_value>100</default_value>
					<max_value>100</max_value>
					<min_value>5</min_value>
					<precision>0</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>%</unit>
				<name>
					<short_name>Batt.Final SOC</short_name>
					<full_name>Battery Final SOC</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xb001060030001">
			<attr.s>
				<ID2>0x0D</ID2>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_batt_chg_cap</struct_var_name>
				<ID1>0x30</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<default_value>100</default_value>
					<max_value>100</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>%</unit>
				<name>
					<short_name>Batt.Chg.SOC</short_name>
					<full_name>Battery Changed SOC</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xb001060040001">
			<attr.s>
				<ID2>0x22</ID2>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_bat_init_vol</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<step>0.1</step>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>V</unit>
				<name>
					<short_name>Init.Batt.Volt.</short_name>
					<full_name>Initial Battery Voltage</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xb001060050001">
			<attr.s>
				<ID2>0x23</ID2>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_batt_final_vol</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<step>0.1</step>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>V</unit>
				<name>
					<short_name>Fina.Batt.Volt.</short_name>
					<full_name>Final Battery Voltage</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xb001060060001">
			<attr.s>
				<ID2>0x83</ID2>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_batt_avg_dis_cur</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<step>0.01</step>
					<precision>2</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>A</unit>
				<name>
					<short_name>Avg.Dis.Curr</short_name>
					<full_name>Battery Average Discharge Current</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xb001060070001">
			<attr.s>
				<ID2>0x82</ID2>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_batt_test_result</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Test Result</short_name>
					<full_name>Battery Test Result</full_name>
				</name>
				<convention>0:Null;1:Normal;2:Fault</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xb001060080001">
			<attr.s>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_batt_initial_chg_power</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<step>0.01</step>
					<precision>2</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>kWh</unit>
				<name>
					<short_name>Bat.Init.Chg.Pwr.</short_name>
					<full_name>Battery Initial Charge Power</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xb001060090001">
			<attr.s>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_batt_final_chg_power</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<step>0.01</step>
					<precision>2</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>kWh</unit>
				<name>
					<short_name>Bat.Fina.Chg.Pwr.</short_name>
					<full_name>Battery Final Charge Power</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xb0010600a0001">
			<attr.s>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_batt_initial_disch_power</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<step>0.01</step>
					<precision>2</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>kWh</unit>
				<name>
					<short_name>Bat.Init.Dis.Pwr.</short_name>
					<full_name>Battery Initial Discharge Power</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xb0010600b0001">
			<attr.s>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_batt_final_disch_power</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<step>0.01</step>
					<precision>2</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>kWh</unit>
				<name>
					<short_name>Bat.Fina.Dis.Pwr.</short_name>
					<full_name>Battery Final Discharge Power</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xb001070010001">
			<attr.s>
				<ID2>0x4C</ID2>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_batt_disch_cap</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>7</signal_type>
				<values>
					<precision>2</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>AH</unit>
				<name>
					<short_name>Bat.Dischg.Cap.</short_name>
					<full_name>Battery Discharge Capacity</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xb001070020001">
			<attr.s>
				<ID2>0x4D</ID2>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_batt_disch_power</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>7</signal_type>
				<values>
					<step>0.01</step>
					<precision>2</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>kWh</unit>
				<name>
					<short_name>Bat.Dis Power</short_name>
					<full_name>Battery Discharge Power</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xb001070030001">
			<attr.s>
				<ID2>0x4E</ID2>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_batt_chg_dura</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>7</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>Min</unit>
				<name>
					<short_name>Bat.Chg.Dura.</short_name>
					<full_name>Battery Charge Duration</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xb001070040001">
			<attr.s>
				<ID2>0x19</ID2>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_batt_charge_cap</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>7</signal_type>
				<values>
					<precision>2</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>AH</unit>
				<name>
					<short_name>Batt.Chg. Cap.</short_name>
					<full_name>Battery Charge Capacity</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xb001070050001">
			<attr.s>
				<ID2>0x4B</ID2>
				<dev_macro>DEV_BATT</dev_macro>
				<struct_var_name>batt_batt_chg_power</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>7</signal_type>
				<values>
					<step>0.01</step>
					<precision>2</precision>
					<data_type>float</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>kWh</unit>
				<name>
					<short_name>Bat.Chg.Pwr.</short_name>
					<full_name>Battery Charge Power</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
	</device>
	<device name="Battery Group">
		<sid value="0xc001010010001">
			<attr.s>
				<ID2>0x04</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_bat_totalcur</struct_var_name>
				<ID1>0x00</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<data_type>float</data_type>
					<max_value>3000</max_value>
					<min_value>-3000</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>A</unit>
				<name>
					<short_name>Batt.Total Curr.</short_name>
					<full_name>Battery Total Current</full_name>
				</name>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xc001010020001">
			<attr.s>
				<ID2>0x17</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_bat_manager_time</struct_var_name>
				<ID1>0x00</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>65535</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>Min</unit>
				<name>
					<short_name>Batt.Stat.Dura.</short_name>
					<full_name>Battery Status Duration</full_name>
				</name>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xc001020010001">
			<attr.s>
				<ID2>0x01</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_bat_manage_status</struct_var_name>
				<ID1>0x01</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>6</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Batt Manag Status</short_name>
					<full_name>Battery Management Status</full_name>
				</name>
				<convention>0:Float;1:Equal;2:Test;3:PowerOff;4:Detect;5:Transition;6:Charge</convention>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xc001020020001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
			<attr.s>
				<optional_attribute>llvd1_enabled</optional_attribute>
				<ID2>0x05</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_llvd1_contrl_status</struct_var_name>
				<ID1>0x01</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>LLVD1 Ctrl.St.</short_name>
					<full_name>LLVD1 Control Status</full_name>
				</name>
				<convention>0:Reset;1:Set</convention>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xc001020030001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
			<attr.s>
				<optional_attribute>llvd2_enabled</optional_attribute>
				<ID2>0x06</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_llvd2_contrl_status</struct_var_name>
				<ID1>0x01</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>LLVD2 Ctrl.St.</short_name>
					<full_name>LLVD2 Control Status</full_name>
				</name>
				<convention>0:Reset;1:Set</convention>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xc001020040001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
			<attr.s>
				<optional_attribute>blvd_enabled</optional_attribute>
				<ID2>0x08</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_blvd_contrl_status</struct_var_name>
				<ID1>0x01</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>BLVD Ctrl.St.</short_name>
					<full_name>BLVD Control Status</full_name>
				</name>
				<convention>0:Reset;1:Set</convention>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xc001020050001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x10</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_bat_detect_status</struct_var_name>
				<ID1>0x01</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Battery Status</short_name>
					<full_name>Battery Detect Status</full_name>
				</name>
				<convention>0:Normal;1:Fault</convention>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xc001020060001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
			<attr.s>
				<optional_attribute>bhtd_enabled</optional_attribute>
				<ID2>0x0A</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_htd_contrl_status</struct_var_name>
				<ID1>0x01</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>BHTD Ctrl.St.</short_name>
					<full_name>BHTD Control Status</full_name>
				</name>
				<convention>0:Reset;1:Set</convention>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xc001020070001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>1</data>
			</attr.show>
			<attr.s>
				<optional_attribute>bltd_enabled</optional_attribute>
				<ID2>0x15</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_bltd_contrl_status</struct_var_name>
				<ID1>0x01</ID1>
			</attr.s>
			<attr.r>
				<signal_type>2</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>BLTD Ctrl.St.</short_name>
					<full_name>BLTD Control Status</full_name>
				</name>
				<convention>0:Reset;1:Set</convention>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xc001030010001">
			<attr.s>
				<optional_attribute>llvd1_enabled</optional_attribute>
				<ID2>0x04</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_llvd1_alm</struct_var_name>
				<ID1>0x02</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>LLVD1 Alarm</short_name>
					<full_name>LLVD1 Alarm</full_name>
				</name>
				<convention>1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>1</alm_delay>
					<alm_level_value>1:Critical;2:Major;3:Minor;4:Warning</alm_level_value>
					<alm_level>1</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0xc001030020001">
			<attr.s>
				<optional_attribute>llvd2_enabled</optional_attribute>
				<ID2>0x06</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_llvd2_alm</struct_var_name>
				<ID1>0x02</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>LLVD2 Alarm</short_name>
					<full_name>LLVD2 Alarm</full_name>
				</name>
				<convention>1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>1</alm_delay>
					<alm_level_value>1:Critical;2:Major;3:Minor;4:Warning</alm_level_value>
					<alm_level>1</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0xc001030030001">
			<attr.s>
				<optional_attribute>blvd_enabled</optional_attribute>
				<ID2>0x0B</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_blvd_alm</struct_var_name>
				<ID1>0x02</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<name>
					<short_name>BLVD Alarm</short_name>
					<full_name>BLVD Alarm</full_name>
				</name>
				<convention>1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>1</alm_delay>
					<alm_level_value>1:Critical;2:Major;3:Minor;4:Warning</alm_level_value>
					<alm_level>1</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0xc001030040001">
			<attr.s>
				<ID2>0x0E</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_bat_fault</struct_var_name>
				<ID1>0x02</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Batt.Det.Abr.</short_name>
					<full_name>Battery Detection Abnormal</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>3</alm_delay>
					<alm_level>1</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0xc001030050001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x24</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_bat_curr_imbalance</struct_var_name>
				<ID1>0x32</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>B.Curr.Imbal.</short_name>
					<full_name>Battery Current Imbalance</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>3</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0xc001030060001">
			<attr.s>
				<ID2>0x23</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_bat_test</struct_var_name>
				<ID1>0x02</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Battery Testing</short_name>
					<full_name>Battery Testing</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>3</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0xc001030070001">
			<attr.s>
				<optional_attribute>equalized_enabled</optional_attribute>
				<ID2>0x1C</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_bat_equal</struct_var_name>
				<ID1>0x02</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Batt. Equal</short_name>
					<full_name>Battery Equalized Charge</full_name>
				</name>
				<convention>0:Mask;1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>3</alm_delay>
					<alm_level>2</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0xc001030080001">
			<attr.s>
				<optional_attribute>bhtd_enabled</optional_attribute>
				<ID2>0x0F</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_htd</struct_var_name>
				<ID1>0x02</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>BHTD Alarm</short_name>
					<full_name>BHTD Alarm</full_name>
				</name>
				<convention>1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>1</alm_delay>
					<alm_level_value>1:Critical;2:Major;3:Minor;4:Warning</alm_level_value>
					<alm_level>1</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0xc001030090001">
			<attr.s>
				<optional_attribute>bltd_enabled</optional_attribute>
				<ID2>0x18</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_bltd</struct_var_name>
				<ID1>0x02</ID1>
			</attr.s>
			<attr.r>
				<signal_type>3</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>BLTD Alarm</short_name>
					<full_name>BLTD Alarm</full_name>
				</name>
				<convention>1:Critical;2:Major;3:Minor;4:Warning</convention>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_delay>1</alm_delay>
					<alm_level_value>1:Critical;2:Major;3:Minor;4:Warning</alm_level_value>
					<alm_level>1</alm_level>
				</alarm>
			</attr.rw>
		</sid>
		<sid value="0xc001040010001">
			<attr.s>
				<optional_attribute>vrla_batt</optional_attribute>
				<ID2>0x03</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_ctrl_float</struct_var_name>
				<ID1>0x04</ID1>
			</attr.s>
			<attr.r>
				<signal_type>4</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Start Float</short_name>
					<full_name>Start Float Charge</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc001040020001">
			<attr.s>
				<optional_attribute>equalized_enabled</optional_attribute>
				<ID2>0x04</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_ctrl_equal</struct_var_name>
				<ID1>0x04</ID1>
			</attr.s>
			<attr.r>
				<signal_type>4</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Start Equal</short_name>
					<full_name>Start Equalized Charge</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc001040030001">
			<attr.s>
				<ID2>0x05</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_ctrl_test</struct_var_name>
				<ID1>0x04</ID1>
			</attr.s>
			<attr.r>
				<signal_type>4</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Start Test</short_name>
					<full_name>Start Test</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc001040040001">
			<attr.s>
				<ID2>0x56</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_ctrl_test_manual_abort</struct_var_name>
				<ID1>0x04</ID1>
			</attr.s>
			<attr.r>
				<signal_type>4</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Manual Abort Test</short_name>
					<full_name>Manual Abort Battery Test</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xc001040050001">
			<attr.s>
				<ID2>0x12</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_ctrl_start_check</struct_var_name>
				<ID1>0x04</ID1>
			</attr.s>
			<attr.r>
				<signal_type>4</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Start Batt.Det.</short_name>
					<full_name>Start Battery Detect</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc001040060001">
			<attr.s>
				<ID2>0x16</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_ctrl_bat_cyctimes_reset</struct_var_name>
				<ID1>0x34</ID1>
			</attr.s>
			<attr.r>
				<signal_type>4</signal_type>
				<values>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Bat.Cyc.Times.Reset</short_name>
					<full_name>Battery Cycle Times Reset</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xc001050010001">
			<attr.s>
				<ID2>0x14</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_bat_cap</struct_var_name>
				<ID1>0x33</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>BATT_NUM</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>81</default_value>
					<data_type>float</data_type>
					<max_value>9990</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>3</para_type>
				<unit>AH</unit>
				<name>
					<short_name>Battery Cap</short_name>
					<full_name>Battery Capacity</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc001050020001">
			<attr.s>
				<optional_attribute>vrla_batt</optional_attribute>
				<ID2>0x05</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_float_vol</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Float Voltage</short_name>
					<full_name>Float Charge Voltage</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>53.5</default_value>
					<data_type>float</data_type>
					<max_value>58</max_value>
					<min_value>42</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<para_type>2</para_type>
				<unit>V</unit>
				<constraints>
					<constraint operator="&lt;=" full_name="Battery Group|Equalized Charge Voltage"/>
					<constraint operator="&lt;=" full_name="SMR Output High Off Voltage" backlash="-2"/>
				</constraints>
			</attr.r>
		</sid>
		<sid value="0xc001050030001">
			<attr.s>
				<optional_attribute>equalized_enabled</optional_attribute>
				<ID2>0x07</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_equal_vol</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Equalized Voltage</short_name>
					<full_name>Equalized Charge Voltage</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>56.4</default_value>
					<data_type>float</data_type>
					<max_value>58</max_value>
					<min_value>42</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<para_type>2</para_type>
				<unit>V</unit>
				<constraints>
					<constraint operator="&gt;=" full_name="Battery Group|Float Charge Voltage"/>
					<constraint operator="&lt;=" full_name="SMR Output High Off Voltage" backlash="-2"/>
					<constraint operator="&lt;=" full_name="Wind Turbine Output OVP Threshold" backlash="-2"/>
					<constraint operator="&lt;=" full_name="PU Output OVP Voltage Threshold" backlash="-2"/>
				</constraints>
			</attr.r>
		</sid>
		<sid value="0xc001050040001">
			<attr.s>
				<ID2>0x15</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_test_vol</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Test Stop Volt</short_name>
					<full_name>Test Stop Voltage</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>46</default_value>
					<data_type>float</data_type>
					<max_value>50</max_value>
					<min_value>42</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<para_type>2</para_type>
				<unit>V</unit>
				<constraints>
					<constraint operator="&gt;=" full_name="Battery Group|LLVD1 Voltage" backlash="1"/>
					<constraint operator="&gt;=" full_name="Battery Group|LLVD2 Voltage" backlash="1"/>
					<constraint operator="&gt;=" full_name="Battery Group|BLVD Voltage" backlash="1"/>
					<constraint operator="&gt;=" full_name="Battery Group|TLLVD1 Voltage" backlash="1"/>
				</constraints>
			</attr.r>
		</sid>
		<sid value="0xc001050050001">
			<attr.s>
				<ID2>0x06</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_en_equal</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>1</default_value>
					<data_type>int</data_type>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<convention>0:Disabled;1:Enabled</convention>
				<para_type>3</para_type>
				<name>
					<short_name>Equalized Enabled</short_name>
					<full_name>Equalized Charge Enabled</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc001050060001">
			<attr.s>
				<ID2>0x2D</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_dev_vol</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>-1.5</default_value>
					<data_type>float</data_type>
					<max_value>-0.5</max_value>
					<min_value>-2</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<para_type>2</para_type>
				<unit>V</unit>
				<name>
					<short_name>Start Volt.Dev.</short_name>
					<full_name>Start Voltage Deviation in Transition</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc001050070001">
			<attr.s>
				<ID2>0x0109</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_shunt_lim_cur_rat</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>80</default_value>
					<data_type>int</data_type>
					<max_value>100</max_value>
					<min_value>50</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>%</unit>
				<name>
					<short_name>Shu.Lim.Curr.Rat</short_name>
					<full_name>Shunt Limit Current Ratio</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc001050080001">
			<attr.s>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_bat_apl_scenario</struct_var_name>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<convention>0:Standby Scenario;1:Cycle Scenario</convention>
				<para_type>3</para_type>
				<name>
					<short_name>Batt.Apl.Scenario</short_name>
					<full_name>Battery Application Scenario</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc001050090001">
			<attr.s>
				<ID2>0x06</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_bat_temp_max</struct_var_name>
				<ID1>0x33</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Batt.Temp.H.Thre.</short_name>
					<full_name>Battery Temperature High Threshold</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>45</default_value>
					<data_type>float</data_type>
					<max_value>60</max_value>
					<min_value>30</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>℃</unit>
				<constraints>
					<constraint operator="&lt;=" full_name="BHTD Temperature" backlash="-3"/>
				</constraints>
			</attr.r>
		</sid>
		<sid value="0xc0010500a0001">
			<attr.s>
				<ID2>0x07</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_bat_temp_min</struct_var_name>
				<ID1>0x33</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Batt.Temp.L.Thre.</short_name>
					<full_name>Battery Temperature Low Threshold</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>-5</default_value>
					<data_type>float</data_type>
					<max_value>20</max_value>
					<min_value>-30</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>℃</unit>
				<constraints>
					<constraint operator="&gt;=" full_name="BLTD Temperature" backlash="3"/>
				</constraints>
			</attr.r>
		</sid>
		<sid value="0xc0010500b0001">
			<attr.s>
				<ID2>0x46</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_bat_det_period</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>0</default_value>
					<data_type>int</data_type>
					<max_value>90</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>Day</unit>
				<name>
					<short_name>Batt.Det.Period</short_name>
					<full_name>Battery Detect Period</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc0010500c0001">
			<attr.s>
				<ID2>0x08</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_bat_loop_threshold</struct_var_name>
				<ID1>0x33</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>0.5</default_value>
					<data_type>float</data_type>
					<max_value>0.8</max_value>
					<min_value>0.1</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<para_type>2</para_type>
				<unit>V</unit>
				<name>
					<short_name>Batt.Loop B.Thre.</short_name>
					<full_name>Battery Loop Broken Threshold</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc0010500d0001">
			<attr.s>
				<ID2>0x13</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_bat_disch_threshold</struct_var_name>
				<ID1>0x33</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>-6</default_value>
					<data_type>float</data_type>
					<max_value>-3</max_value>
					<min_value>-50</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<para_type>2</para_type>
				<unit>A</unit>
				<name>
					<short_name>Batt.Dischg Thre.</short_name>
					<full_name>Battery Discharge Current Threshold</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc0010500e0001">
			<attr.s>
				<ID2>0x03</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_bat_vol_min</struct_var_name>
				<ID1>0x33</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Batt.Volt.L.Thre.</short_name>
					<full_name>Battery Voltage Low Threshold</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>46</default_value>
					<data_type>float</data_type>
					<max_value>52</max_value>
					<min_value>39</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<para_type>2</para_type>
				<unit>V</unit>
				<constraints>
					<constraint operator="&gt;=" full_name="Battery Voltage Too Low Threshold"/>
					<constraint operator="&gt;=" full_name="Battery Group|LLVD1 Voltage" backlash="1"/>
					<constraint operator="&gt;=" full_name="Battery Group|LLVD2 Voltage" backlash="1"/>
					<constraint operator="&gt;=" full_name="Battery Group|BLVD Voltage" backlash="1"/>
					<constraint operator="&gt;=" full_name="Battery Group|TLLVD1 Voltage" backlash="1"/>
				</constraints>
			</attr.r>
		</sid>
		<sid value="0xc0010500f0001">
			<attr.s>
				<ID2>0x05</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_bat_cur_faultvalve</struct_var_name>
				<ID1>0x33</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>120</default_value>
					<data_type>int</data_type>
					<max_value>120</max_value>
					<min_value>10</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>%</unit>
				<name>
					<short_name>Batt.Curr.Abr.Rate</short_name>
					<full_name>Battery Current Abnormal Rate</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xc001050100001">
			<attr.s>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_bat_cur_coef</struct_var_name>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>0.15</default_value>
					<data_type>float</data_type>
					<max_value>0.6</max_value>
					<min_value>0.051</min_value>
					<precision>3</precision>
					<step>0.001</step>
				</values>
				<para_type>2</para_type>
				<unit>C10</unit>
				<name>
					<short_name>Chg.Curr.Coeff.</short_name>
					<full_name>Battery Charge Current Coefficient</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc001050110001">
			<attr.s>
				<ID2>0x13</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_test_period</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>0</default_value>
					<data_type>int</data_type>
					<max_value>365</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>Day</unit>
				<name>
					<short_name>Test Period</short_name>
					<full_name>Battery Test Period</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc001050120001">
			<attr.s>
				<ID2>0x17</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_test_max_hour</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>480</default_value>
					<data_type>int</data_type>
					<max_value>1440</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>Min</unit>
				<name>
					<short_name>Test Max. Dura.</short_name>
					<full_name>Test Maximum Duration</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc001050130001">
			<attr.s>
				<ID2>0x16</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_test_stop_cap</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Test Stop SOC</short_name>
					<full_name>Test Stop SOC Threshold</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>65</default_value>
					<data_type>float</data_type>
					<max_value>100</max_value>
					<min_value>41</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>%</unit>
				<constraints>
					<constraint operator="&gt;=" full_name="Battery Group|LLVD1 SOC Threshold" backlash="1"/>
					<constraint operator="&gt;=" full_name="Battery Group|LLVD2 SOC Threshold" backlash="1"/>
					<constraint operator="&gt;=" full_name="Battery Group|BLVD SOC Threshold" backlash="1"/>
					<constraint operator="&gt;=" full_name="Battery Group|TLLVD1 SOC Threshold" backlash="1"/>
					<constraint operator="&lt;=" full_name="Battery Group|Battery Test Fail SOC Threshold"/>
				</constraints>
			</attr.r>
		</sid>
		<sid value="0xc001050140001">
			<attr.s>
				<ID2>0xA1</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_test_fail_cap</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Test Fail SOC</short_name>
					<full_name>Battery Test Fail SOC Threshold</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>85</default_value>
					<data_type>float</data_type>
					<max_value>100</max_value>
					<min_value>50</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>%</unit>
				<constraints>
					<constraint operator="&gt;=" full_name="Battery Group|Test Stop SOC Threshold"/>
				</constraints>
			</attr.r>
		</sid>
		<sid value="0xc001050150001">
			<attr.s>
				<optional_attribute>equalized_enabled</optional_attribute>
				<ID2>0x3C</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_preset_equ_en</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>0</default_value>
					<data_type>int</data_type>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<convention>0:Disabled;1:Enabled</convention>
				<para_type>2</para_type>
				<name>
					<short_name>Preset Equ.En.</short_name>
					<full_name>Preset Equalized Charge Enabled</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc001050160001">
			<attr.s>
				<optional_attribute>preset_equ_chg_enabled</optional_attribute>
				<ID2>0x3D</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_order_equal_data</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<para_type>2</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>2037/12/31</default_value>
					<data_type>date</data_type>
					<max_value>2037/12/31</max_value>
					<min_value>2000/1/1</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Preset Equ.Date</short_name>
					<full_name>Preset Equalized Charge Date</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc001050170001">
			<attr.s>
				<optional_attribute>preset_equ_chg_enabled</optional_attribute>
				<ID2>0x3B</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_pre_equ_chg_dura</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>0</default_value>
					<data_type>int</data_type>
					<max_value>2880</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>Min</unit>
				<name>
					<short_name>Pre.Equ.Chg.Dura.</short_name>
					<full_name>Preset Equalized Charge Duration</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc001050180001">
			<attr.s>
				<optional_attribute>Load_distributing_mode</optional_attribute>
				<ID2>0x5E</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_lvd_mode</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<para_type>3</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>1</default_value>
					<data_type>int</data_type>
					<max_value>3</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<name>
					<short_name>Disconnect Mode</short_name>
					<full_name>Disconnect Mode</full_name>
				</name>
				<convention>0:Disabled;1:Batt Volt;2:PowerOff Time;3:Batt Rem Cap</convention>
			</attr.r>
		</sid>
		<sid value="0xc001050190001">
			<attr.s>
				<optional_attribute>Load_distributing_mode</optional_attribute>
				<ID2>0xA8</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_discon_ctrl_delay</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<para_type>2</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>0</default_value>
					<data_type>int</data_type>
					<max_value>900</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<name>
					<short_name>Discon. Ctrl Delay</short_name>
					<full_name>Disconnect Ctrl Delay</full_name>
				</name>
				<unit>sec</unit>
			</attr.r>
		</sid>
		<sid value="0xc0010501a0001">
			<attr.s>
				<optional_attribute>Load_distributing_mode</optional_attribute>
				<ID2>0xC6</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_discon_rec_back</struct_var_name>
				<ID1>0x33</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>3</default_value>
					<data_type>float</data_type>
					<max_value>5</max_value>
					<min_value>1</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<para_type>2</para_type>
				<unit>V</unit>
				<name>
					<short_name>Disc.Rec.Back.</short_name>
					<full_name>Disconnect Recover Backlash</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc0010501b0001">
			<attr.s>
				<optional_attribute>vrla_batt</optional_attribute>
				<ID2>0xAE</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_temp_comp_mode</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>0</default_value>
					<data_type>int</data_type>
					<max_value>3</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<convention>0:Enabled;1:Float;2:Equal;3:Disabled</convention>
				<para_type>3</para_type>
				<name>
					<short_name>Temp. Comp. Mode</short_name>
					<full_name>Temperature Compensation Mode</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc0010501c0001">
			<attr.s>
				<optional_attribute>vrla_batt_temp_comp_enabled</optional_attribute>
				<ID2>0x1D</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_temp_comp_ref</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>25</default_value>
					<data_type>float</data_type>
					<max_value>30</max_value>
					<min_value>20</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>℃</unit>
				<name>
					<short_name>Temp.Comp.Ref.</short_name>
					<full_name>Temperature Compensation Reference</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc0010501d0001">
			<attr.s>
				<optional_attribute>equalized_enabled</optional_attribute>
				<ID2>0x09</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_equal_period</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>90</default_value>
					<data_type>int</data_type>
					<max_value>365</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>Day</unit>
				<name>
					<short_name>Equalized Period</short_name>
					<full_name>Equalized Charge Period</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc0010501e0001">
			<attr.s>
				<optional_attribute>equalized_enabled</optional_attribute>
				<ID2>0x0A</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_equ_max_dura</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Equ.Max.Dura.</short_name>
					<full_name>Equalized Charge Maximum Duration</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>600</default_value>
					<data_type>int</data_type>
					<max_value>2880</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>Min</unit>
				<constraints>
					<constraint operator="&gt;=" full_name="Equalized Charge Minimum Duration"/>
				</constraints>
			</attr.r>
		</sid>
		<sid value="0xc0010501f0001">
			<attr.s>
				<optional_attribute>equalized_enabled</optional_attribute>
				<ID2>0x0B</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_equ_min_dura</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Equ.Min.Dura.</short_name>
					<full_name>Equalized Charge Minimum Duration</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>180</default_value>
					<data_type>int</data_type>
					<max_value>2880</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>Min</unit>
				<constraints>
					<constraint operator="&gt;=" full_name="Equalized Charge End Duration"/>
					<constraint operator="&lt;=" full_name="Equalized Charge Maximum Duration"/>
				</constraints>
			</attr.r>
		</sid>
		<sid value="0xc001050200001">
			<attr.s>
				<optional_attribute>equalized_enabled</optional_attribute>
				<ID2>0x0D</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_equal_end_cur_coef</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>0.015</default_value>
					<data_type>float</data_type>
					<max_value>0.05</max_value>
					<min_value>0.001</min_value>
					<precision>3</precision>
					<step>0.001</step>
				</values>
				<para_type>2</para_type>
				<unit>C10</unit>
				<name>
					<short_name>Equ.End C.Coeff.</short_name>
					<full_name>Equalized Charge End Current Coefficient</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc001050210001">
			<attr.s>
				<optional_attribute>equalized_enabled</optional_attribute>
				<ID2>0x0C</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_equ_end_dura</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Equ.End Dura.</short_name>
					<full_name>Equalized Charge End Duration</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>180</default_value>
					<data_type>int</data_type>
					<max_value>600</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>Min</unit>
				<constraints>
					<constraint operator="&lt;=" full_name="Equalized Charge Minimum Duration"/>
				</constraints>
			</attr.r>
		</sid>
		<sid value="0xc001050220001">
			<attr.s>
				<optional_attribute>equalized_enabled</optional_attribute>
				<ID2>0x11</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_equ_thre_dura</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>600</default_value>
					<data_type>int</data_type>
					<max_value>1440</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>Min</unit>
				<name>
					<short_name>Equ.Thre.Dura.</short_name>
					<full_name>Equalized Charge Threshold Duration</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc001050230001">
			<attr.s>
				<optional_attribute>equalized_enabled</optional_attribute>
				<ID2>0x0E</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_equ_thre_volt</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>48</default_value>
					<data_type>float</data_type>
					<max_value>52.5</max_value>
					<min_value>46</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<para_type>2</para_type>
				<unit>V</unit>
				<name>
					<short_name>Equ.Thre.Volt.</short_name>
					<full_name>Equalized Charge Threshold Voltage</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc001050240001">
			<attr.s>
				<optional_attribute>equalized_enabled</optional_attribute>
				<ID2>0x10</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_equ_thre_cur</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>0.06</default_value>
					<data_type>float</data_type>
					<max_value>0.5</max_value>
					<min_value>0.03</min_value>
					<precision>3</precision>
					<step>0.005</step>
				</values>
				<para_type>2</para_type>
				<unit>C10</unit>
				<name>
					<short_name>Equ.Thre.Curr.</short_name>
					<full_name>Equalized Charge Threshold Current</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc001050250001">
			<attr.s>
				<optional_attribute>equalized_enabled</optional_attribute>
				<ID2>0x0F</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_equ_thre_cap</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>90</default_value>
					<data_type>float</data_type>
					<max_value>100</max_value>
					<min_value>50</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>%</unit>
				<name>
					<short_name>Equ.Thre.SOC</short_name>
					<full_name>Equalized Charge Threshold SOC</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc001050260001">
			<attr.s>
				<optional_attribute>vrla_batt</optional_attribute>
				<ID2>0x49</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_temp_ctrl_ref</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>55</default_value>
					<data_type>float</data_type>
					<max_value>80</max_value>
					<min_value>45</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>℃</unit>
				<name>
					<short_name>Env. T. Ctrl. Ref</short_name>
					<full_name>Environment Temperature Contorl Reference</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc001050270001">
			<attr.s>
				<optional_attribute>vrla_batt</optional_attribute>
				<ID2>0x92</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_temp_ctrl_coeff</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>0.03</default_value>
					<data_type>float</data_type>
					<max_value>0.06</max_value>
					<min_value>0</min_value>
					<precision>3</precision>
					<step>0.001</step>
				</values>
				<para_type>2</para_type>
				<unit>C10/℃</unit>
				<name>
					<short_name>Env. T. Ctrl. Coeff</short_name>
					<full_name>Environment Temperature Contorl Compensation Coefficient</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc001050280001">
			<attr.s>
				<optional_attribute>llvd1_conf</optional_attribute>
				<ID2>0x42</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_en_llvd1</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<para_type>3</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>1</default_value>
					<data_type>int</data_type>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<name>
					<short_name>LLVD1 Enabled</short_name>
					<full_name>LLVD1 Enabled</full_name>
				</name>
				<convention>0:Disabled;1:Enabled</convention>
			</attr.r>
		</sid>
		<sid value="0xc001050290001">
			<attr.s>
				<optional_attribute>llvd1_time_enabled</optional_attribute>
				<ID2>0x23</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_llvd1_time</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<name>
					<short_name>LLVD1 Dura.</short_name>
					<full_name>LLVD1 Duration</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>1440</default_value>
					<data_type>int</data_type>
					<max_value>7200</max_value>
					<min_value>3</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>Min</unit>
				<constraints>
					<constraint operator="&gt;=" full_name="Gen Start Discharge Duration" backlash="30"/>
				</constraints>
			</attr.r>
		</sid>
		<sid value="0xc0010502a0001">
			<attr.s>
				<optional_attribute>llvd1_enabled</optional_attribute>
				<ID2>0x21</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_llvd1_vol</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<name>
					<short_name>LLVD1 Volt.</short_name>
					<full_name>LLVD1 Voltage</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>45</default_value>
					<data_type>float</data_type>
					<max_value>49</max_value>
					<min_value>38</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<para_type>2</para_type>
				<unit>V</unit>
				<constraints>
					<constraint operator="&lt;=" full_name="Battery Group|Test Stop Voltage" backlash="-1"/>
					<constraint operator="&lt;=" full_name="DG.Start Voltage Threshold" backlash="-1"/>
					<constraint operator="&lt;=" full_name="Battery Voltage Low Threshold" backlash="-1"/>
				</constraints>
			</attr.r>
		</sid>
		<sid value="0xc0010502b0001">
			<attr.s>
				<optional_attribute>llvd1_cap_enabled</optional_attribute>
				<ID2>0x55</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_llvd1_cap</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<name>
					<short_name>LLVD1 SOC</short_name>
					<full_name>LLVD1 SOC Threshold</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>20</default_value>
					<data_type>float</data_type>
					<max_value>80</max_value>
					<min_value>10</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>%</unit>
				<constraints>
					<constraint operator="&lt;=" full_name="Battery Group|Test Stop SOC Threshold" backlash="-1"/>
					<constraint operator="&lt;=" full_name="DG.Start SOC Threshold" backlash="-5"/>
				</constraints>
			</attr.r>
		</sid>
		<sid value="0xc0010502c0001">
			<attr.s>
				<optional_attribute>llvd2_conf</optional_attribute>
				<ID2>0x43</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_en_llvd2</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<para_type>3</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>1</default_value>
					<data_type>int</data_type>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<name>
					<short_name>LLVD2 Enabled</short_name>
					<full_name>LLVD2 Enabled</full_name>
				</name>
				<convention>0:Disabled;1:Enabled</convention>
			</attr.r>
		</sid>
		<sid value="0xc0010502d0001">
			<attr.s>
				<optional_attribute>llvd2_time_enabled</optional_attribute>
				<ID2>0x26</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_llvd2_time</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<name>
					<short_name>LLVD2 Dura.</short_name>
					<full_name>LLVD2 Duration</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>1680</default_value>
					<data_type>int</data_type>
					<max_value>7200</max_value>
					<min_value>30</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>Min</unit>
				<constraints>
					<constraint operator="&gt;=" full_name="Gen Start Discharge Duration" backlash="30"/>
					<constraint operator="&gt;=" full_name="TLLVD1 Duration" backlash="5"/>
				</constraints>
			</attr.r>
		</sid>
		<sid value="0xc0010502e0001">
			<attr.s>
				<optional_attribute>llvd2_enabled</optional_attribute>
				<ID2>0x24</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_llvd2_vol</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<name>
					<short_name>LLVD2 Volt.</short_name>
					<full_name>LLVD2 Voltage</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>44</default_value>
					<data_type>float</data_type>
					<max_value>49</max_value>
					<min_value>38</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<para_type>2</para_type>
				<unit>V</unit>
				<constraints>
					<constraint operator="&lt;=" full_name="Battery Group|Test Stop Voltage" backlash="-1"/>
					<constraint operator="&lt;=" full_name="Battery Voltage Low Threshold" backlash="-1"/>
					<constraint operator="&lt;=" full_name="Battery Group|TLLVD1 Voltage" backlash="-1"/>
					<constraint operator="&lt;=" full_name="DG.Start Voltage Threshold" backlash="-1"/>
				</constraints>
			</attr.r>
		</sid>
		<sid value="0xc0010502f0001">
			<attr.s>
				<optional_attribute>llvd2_cap_enabled</optional_attribute>
				<ID2>0x56</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_llvd2_cap</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<name>
					<short_name>LLVD2 SOC</short_name>
					<full_name>LLVD2 SOC Threshold</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>10</default_value>
					<data_type>float</data_type>
					<max_value>80</max_value>
					<min_value>10</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>%</unit>
				<constraints>
					<constraint operator="&lt;=" full_name="Battery Group|TLLVD1 SOC" backlash="-5"/>
					<constraint operator="&lt;=" full_name="DG.Start SOC Threshold" backlash="-5"/>
				</constraints>
			</attr.r>
		</sid>
		<sid value="0xc001050300001">
			<attr.s>
				<optional_attribute>blvd_conf</optional_attribute>
				<ID2>0x37</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_en_blvd</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<para_type>3</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>1</default_value>
					<data_type>int</data_type>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<name>
					<short_name>BLVD Enabled</short_name>
					<full_name>BLVD Enabled</full_name>
				</name>
				<convention>0:Disabled;1:Enabled</convention>
			</attr.r>
		</sid>
		<sid value="0xc001050310001">
			<attr.s>
				<optional_attribute>blvd_time_enabled</optional_attribute>
				<ID2>0x3A</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_blvd_time</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<name>
					<short_name>BLVD Dura.</short_name>
					<full_name>BLVD Duration</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>1680</default_value>
					<data_type>int</data_type>
					<max_value>7200</max_value>
					<min_value>30</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>Min</unit>
				<constraints>
					<constraint operator="&gt;=" full_name="Gen Start Discharge Duration" backlash="30"/>
				</constraints>
			</attr.r>
		</sid>
		<sid value="0xc001050320001">
			<attr.s>
				<optional_attribute>blvd_enabled</optional_attribute>
				<ID2>0x38</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_blvd_volt</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<name>
					<short_name>BLVD Voltage</short_name>
					<full_name>BLVD Voltage</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>44</default_value>
					<data_type>float</data_type>
					<max_value>49</max_value>
					<min_value>38</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<para_type>2</para_type>
				<unit>V</unit>
				<constraints>
					<constraint operator="&lt;=" full_name="Battery Group|Test Stop Voltage" backlash="-1"/>
					<constraint operator="&lt;=" full_name="DG.Start Voltage Threshold" backlash="-1"/>
					<constraint operator="&lt;=" full_name="Battery Voltage Low Threshold" backlash="-1"/>
				</constraints>
			</attr.r>
		</sid>
		<sid value="0xc001050330001">
			<attr.s>
				<optional_attribute>blvd_cap_enabled</optional_attribute>
				<ID2>0x58</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_blvd_cap</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<name>
					<short_name>BLVD SOC</short_name>
					<full_name>BLVD SOC Threshold</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>10</default_value>
					<data_type>float</data_type>
					<max_value>80</max_value>
					<min_value>10</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>%</unit>
				<constraints>
					<constraint operator="&lt;=" full_name="Battery Group|Test Stop SOC Threshold" backlash="-1"/>
					<constraint operator="&lt;=" full_name="DG.Start SOC Threshold" backlash="-5"/>
				</constraints>
			</attr.r>
		</sid>
		<sid value="0xc001050340001">
			<attr.s>
				<optional_attribute>batt_resistance_detect</optional_attribute>
				<ID2>0x0106</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_bat_res_faultthre</struct_var_name>
				<ID1>0x33</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>4.2</default_value>
					<data_type>float</data_type>
					<max_value>100</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<para_type>2</para_type>
				<unit>mΩ</unit>
				<name>
					<short_name>Batt. Res. Fault Thres.</short_name>
					<full_name>Battery Resistance Fault Threshold</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xc001050350001">
			<attr.s>
				<optional_attribute>periodic_batt_detect</optional_attribute>
				<ID2>0x47</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_bat_det_minute</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>2</default_value>
					<data_type>int</data_type>
					<max_value>5</max_value>
					<min_value>1</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>Min</unit>
				<name>
					<short_name>Battery Det.Dura.</short_name>
					<full_name>Battery Detect Duration</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc001050360001">
			<attr.s>
				<optional_attribute>blvd_conf</optional_attribute>
				<ID2>0x5A</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_en_bhtd</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>0</default_value>
					<data_type>int</data_type>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<convention>0:Disabled;1:Enabled</convention>
				<para_type>3</para_type>
				<name>
					<short_name>BHTD Enabled</short_name>
					<full_name>BHTD Enabled</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc001050370001">
			<attr.s>
				<optional_attribute>bhtd_enabled</optional_attribute>
				<ID2>0x5B</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_bhtd_temp</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>BHTD Temp.</short_name>
					<full_name>BHTD Temperature</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>50</default_value>
					<data_type>float</data_type>
					<max_value>60</max_value>
					<min_value>30</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>℃</unit>
				<constraints>
					<constraint operator="&gt;=" full_name="Battery Temperature High Threshold" backlash="3"/>
				</constraints>
			</attr.r>
		</sid>
		<sid value="0xc001050380001">
			<attr.s>
				<optional_attribute>vrla_batt_temp_comp_enabled</optional_attribute>
				<ID2>0x1E</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_vol_temp_comp_coef</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>3</default_value>
					<data_type>float</data_type>
					<max_value>8</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<para_type>2</para_type>
				<unit>mV/Cell/℃</unit>
				<name>
					<short_name>Volt.Temp.Coeff.</short_name>
					<full_name>Battery Voltage Temperature Compensation Coefficient</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc001050390001">
			<attr.s>
				<optional_attribute>vrla_batt_temp_comp_enabled</optional_attribute>
				<ID2>0x1F</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_cur_temp_comp_coef</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>0</default_value>
					<data_type>float</data_type>
					<max_value>0.01</max_value>
					<min_value>0</min_value>
					<precision>3</precision>
					<step>0.001</step>
				</values>
				<para_type>2</para_type>
				<unit>C10/℃</unit>
				<name>
					<short_name>Curr.Temp.Coeff.</short_name>
					<full_name>Battery Current Temperature Compensation Coefficient</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc0010503a0001">
			<attr.s>
				<optional_attribute>blvd_conf</optional_attribute>
				<ID2>0x81</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_en_bltd</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>0</default_value>
					<data_type>int</data_type>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<convention>0:Disabled;1:Enabled</convention>
				<para_type>3</para_type>
				<name>
					<short_name>BLTD Enabled</short_name>
					<full_name>BLTD Enabled</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc0010503b0001">
			<attr.s>
				<optional_attribute>bltd_enabled</optional_attribute>
				<ID2>0xA6</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_bltd_temp</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>BLTD Temp.</short_name>
					<full_name>BLTD Temperature</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>-15</default_value>
					<data_type>float</data_type>
					<max_value>10</max_value>
					<min_value>-40</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>℃</unit>
				<constraints>
					<constraint operator="&lt;=" full_name="Battery Temperature Low Threshold" backlash="-3"/>
				</constraints>
			</attr.r>
		</sid>
		<sid value="0xc0010503c0001">
			<attr.s>
				<optional_attribute>periodic_batt_test</optional_attribute>
				<ID2>0x14</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_test_start_time</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>10</default_value>
					<data_type>int</data_type>
					<max_value>23</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>:00</unit>
				<name>
					<short_name>Test Start Time</short_name>
					<full_name>Battery Test Start Time</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc0010503d0001">
			<attr.s>
				<optional_attribute>vrla_batt</optional_attribute>
				<ID2>0x0115</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_soh_abr_set_cap_ratio</struct_var_name>
				<ID1>0x33</ID1>
			</attr.s>
			<attr.r>
				<para_type>2</para_type>
				<signal_type>5</signal_type>
				<values>
					<default_value>20</default_value>
					<data_type>int</data_type>
					<max_value>100</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>SOH Abr.Cap.Ratio</short_name>
					<full_name>SOH Abnormal Set Capacity Ratio</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc0010503e0001">
			<attr.s>
				<optional_attribute>vrla_batt</optional_attribute>
				<ID2>0xA3</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_charge_mode</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>0</default_value>
					<data_type>int</data_type>
					<max_value>1</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<convention>0:Common;1:Smart</convention>
				<para_type>2</para_type>
				<name>
					<short_name>Batt.Charge Mode</short_name>
					<full_name>Battery Charge Mode</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xc0010503f0001">
			<attr.s>
				<ID2>0x3E</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_temp_comp_max</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>57.5</default_value>
					<data_type>float</data_type>
					<max_value>58</max_value>
					<min_value>50</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<para_type>2</para_type>
				<unit>V</unit>
				<name>
					<short_name>Temp.Volt.Max.</short_name>
					<full_name>Temperature Compensation Voltage Maximum</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc001050400001">
			<attr.s>
				<ID2>0x3F</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_temp_comp_min</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>50</default_value>
					<data_type>float</data_type>
					<max_value>58</max_value>
					<min_value>50</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<para_type>2</para_type>
				<unit>V</unit>
				<name>
					<short_name>Temp.Volt.Min.</short_name>
					<full_name>Temperature Compensation Voltage Minimum</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc001050410001">
			<attr.s>
				<ID2>0xA4</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_fast_chg_curr_coef</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>0.3</default_value>
					<data_type>float</data_type>
					<max_value>0.4</max_value>
					<min_value>0.02</min_value>
					<precision>3</precision>
					<step>0.001</step>
				</values>
				<para_type>2</para_type>
				<unit>C10</unit>
				<name>
					<short_name>Fast Chg.Coeff</short_name>
					<full_name>Battery Fast Charge Current Coefficient</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xc001050420001">
			<attr.s>
				<ID2>0xA5</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_fast_chg_temp_comp_coef</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>0.01</default_value>
					<data_type>float</data_type>
					<max_value>0.015</max_value>
					<min_value>0.005</min_value>
					<precision>3</precision>
					<step>0.001</step>
				</values>
				<para_type>2</para_type>
				<unit>C10/℃</unit>
				<name>
					<short_name>Fast Chg.T.Coeff</short_name>
					<full_name>Battery Fast Charge Current Temperature Compensation Coefficient</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xc001050430001">
			<attr.s>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_bat_too_vol_min</struct_var_name>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Batt.Volt.T.L.Thre.</short_name>
					<full_name>Battery Voltage Too Low Threshold</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>45</default_value>
					<data_type>float</data_type>
					<max_value>52</max_value>
					<min_value>39</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<para_type>2</para_type>
				<unit>V</unit>
				<constraints>
					<constraint operator="&lt;=" full_name="Battery Voltage Low Threshold"/>
				</constraints>
			</attr.r>
		</sid>
		<sid value="0xc001050440001">
			<attr.s>
				<optional_attribute>vrla_batt</optional_attribute>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_bat_series</struct_var_name>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>0</default_value>
					<data_type>int</data_type>
					<max_value>15</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<convention>0:ZXDC12 CA;1:ZXDC12 CG;2:12V GEL;3:12V FT;4:12V AGM;5:2V AGM;6:2V GEL;7:ZXDC12 HP;8:ZXDC12 HP;9:ZXDC02 HC;10:ZXDC02 HL;11:ZXDC02 HL E;12:ZXDC12 HL;13:ZXDC02 HP;14:ZXDC02 HP E;15:ZXDC12HP A</convention>
				<para_type>3</para_type>
				<name>
					<short_name>Battery Series</short_name>
					<full_name>Battery Series</full_name>
				</name>
			</attr.r>
		</sid>
		<sid value="0xc001060010001">
			<attr.s>
				<ID2>0x34</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_bat_dischg_depth_range</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Discharge Depth</short_name>
					<full_name>Discharge Depth Range</full_name>
				</name>
				<convention>0:[0%,10%);1:[10%,20%);2:[20%,30%);3:[30%,40%);4:[40%,50%);5:[50%,60%);6:[60%,70%);7:[70%,80%);8:[80%,90%);9:[90%,100%]</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xc001060020001">
			<attr.s>
				<ID2>0x36</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_bat_temp_statistics</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Batt.Temp.Stat.</short_name>
					<full_name>Battery Temperature Statistics</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xc001060030001">
			<attr.s>
				<ID2>0x37</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_bat_temp_total_range</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Batt.Temp.Range</short_name>
					<full_name>Battery Temperature Range</full_name>
				</name>
				<convention>0:[-40℃,-30℃);1:[-30℃,-20℃);2:[-20℃,-10℃);3:[-10℃,0℃);4:[0℃,10℃);5:[10℃,20℃);6:[20℃,25℃);7:[25℃,30℃);8:[30℃,40℃);9:[40℃,50℃);10:[50℃,60℃);11:[60℃,70℃);12:[70℃,80℃);13:[80℃,90℃);14:[90℃,100℃]</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xc001060040001">
			<attr.s>
				<ID2>0x33</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_bat_dischg_statistics</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Discharge Stat.</short_name>
					<full_name>Discharge Statistics</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xc001060050001">
			<attr.s>
				<ID2>0x03</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_bat_dischg_his_time</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<data_type>time</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Dischg.His.Time</short_name>
					<full_name>Battery Discharge History Time</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xc001060060001">
			<attr.s>
				<ID2>0x09</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_dischg_dura</struct_var_name>
				<ID1>0x00</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1440</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>Min</unit>
				<name>
					<short_name>Discharge Dura.</short_name>
					<full_name>Battery Discharge Duration</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xc001060070001">
			<attr.s>
				<ID2>0x0F</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_test_stop_source</struct_var_name>
				<ID1>0x00</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>12</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Test Stop Cause</short_name>
					<full_name>Battery Test Stop Cause</full_name>
				</name>
				<convention>0:Null;1:Manual;2:SC;3:TimerFault;4:Smr None;5:Batt Curr Fault;6:Max Time;7:Batt Volt;8:Batt Cap;9:System Reset;10:PowerOFF;11:Batt Fault;12:DC Volt</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xc001060080001">
			<attr.s>
				<ID2>0x81</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_bat_test_type</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Batt.Test Type</short_name>
					<full_name>Battery Test Type</full_name>
				</name>
				<convention>0:Test;1:Detect</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xc001060090001">
			<attr.s>
				<ID2>0x0E</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_test_start_source</struct_var_name>
				<ID1>0x00</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>5</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Test Start Cause</short_name>
					<full_name>Battery Test Start Cause</full_name>
				</name>
				<convention>0:Null;1:Manual;2:SC;3:Periodic;4:Last Charge;5:Manual Detect</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xc0010600a0001">
			<attr.s>
				<ID2>0x01</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_test_stop_time</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<data_type>time</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Test Stop Time</short_name>
					<full_name>Battery Test Stop Time</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xc0010600b0001">
			<attr.s>
				<ID2>0x02</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_test_his_time</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<data_type>time</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Test His.Time</short_name>
					<full_name>Battery Test History Time</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xc0010600c0001">
			<attr.s>
				<ID2>0x05</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_batt_test_dura</struct_var_name>
				<ID1>0x00</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1440</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>Min</unit>
				<name>
					<short_name>Batt.Test Dura.</short_name>
					<full_name>Battery Test Duration</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xc0010600d0001">
			<attr.s>
				<optional_attribute>equalized_enabled</optional_attribute>
				<ID2>0x08</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_next_equal_chg_time</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<data_type>time</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Next Equ.Time</short_name>
					<full_name>Next Equalized Charge Time</full_name>
				</name>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0xc0010600e0001">
			<attr.s>
				<optional_attribute>periodic_batt_detect</optional_attribute>
				<ID2>0x09</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_next_detect_time</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<data_type>time</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Next Det.Time</short_name>
					<full_name>Next Detect Time</full_name>
				</name>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0xc0010600f0001">
			<attr.s>
				<optional_attribute>periodic_batt_test</optional_attribute>
				<ID2>0x07</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_next_test_time</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<data_type>time</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Next Test Time</short_name>
					<full_name>Next Test Time</full_name>
				</name>
			</attr.r>
			<attr.show>
				<data>1</data>
			</attr.show>
		</sid>
		<sid value="0xc001060100001">
			<attr.s>
				<optional_attribute>batt_resistance_detect</optional_attribute>
				<ID2>0xAB</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_bat_res_detect_left_time</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>min</unit>
				<name>
					<short_name>Res.Det Left Time</short_name>
					<full_name>Battery Resistance Detect Left Time</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xc001060110001">
			<attr.s>
				<optional_attribute>equalized_enabled</optional_attribute>
				<ID2>0x11</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_equal_end_source</struct_var_name>
				<ID1>0x00</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>15</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Equ.Stop Cause</short_name>
					<full_name>Battery Equalized Charge Stop Cause</full_name>
				</name>
				<convention>0:Null;1:Manual;2:SC;3:TimerFault;4:SMR None;5:Batt Curr Fault;6:Max Time;7:Duration;8:Fixed-time;9:Batt Temp Fault;10:DcVoltFault;11:Equal Disabled;12:PowerOff;13:Batt Fault;14:Env Temp Fault;15:Manual Detect;16:Energy Short</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xc001060120001">
			<attr.s>
				<optional_attribute>equalized_enabled</optional_attribute>
				<ID2>0x10</ID2>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_equal_start_source</struct_var_name>
				<ID1>0x00</ID1>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>8</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Equ.Start Cause</short_name>
					<full_name>Battery Equalized Charge Start Cause</full_name>
				</name>
				<convention>0:Null;1:Manual;2:SC;3:Periodic;4:Order;5:Test;6:PowerOff;7:Batt Curr;8:Repair SOH</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xc001060130001">
			<attr.s>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_rec_dura</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>1440</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<unit>Min</unit>
				<name>
					<short_name>Record Duration</short_name>
					<full_name>Record Duration</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xc001060140001">
			<attr.s>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_stop_source</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>18</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Stop Cause</short_name>
					<full_name>Record Stop Cause</full_name>
				</name>
				<convention>0:Null;1:Manual;2:SC;3:PowerOFF;4:Batt Fault;5:Batt Disconnect;6:TimerFault;7:Smr None;8:Batt Curr Fault;9:Max Time;10:Duration;11:Batt Temp High;12:Dc_Volt_High;13:Equal Disabled;14:Env Temp High;15:Order Time;16:Test Max Time;17:Test Volt;18:Test Cap</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xc001060150001">
			<attr.s>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_start_source</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>9</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Start Cause</short_name>
					<full_name>Record Start Cause</full_name>
				</name>
				<convention>0:Null;1:Manual;2:SC;3:Periodic;4:Order;5:Test Volt;6:Test Cap;7:PowerOff;8:Last Charge;9:Need Equal</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xc001060160001">
			<attr.s>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_stop_batt_status</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>5</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Stop Battery Status</short_name>
					<full_name>Record Stop Battery Status</full_name>
				</name>
				<convention>0:Float;1:Equal;2:Test;3:Power Off;4:Detect;5:Trans</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xc001060170001">
			<attr.s>
				<dev_macro>DEV_BATTS</dev_macro>
				<struct_var_name>batts_start_batt_status</struct_var_name>
			</attr.s>
			<attr.r>
				<signal_type>6</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>5</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<data_type>int</data_type>
				</values>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Start Battery Status</short_name>
					<full_name>Record Start Battery Status</full_name>
				</name>
				<convention>0:Float;1:Equal;2:Test;3:Power Off;4:Detect;5:Trans</convention>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
	</device>
	<device name="DC Load">
		<sid value="0xd001010010001">
			<attr.s>
				<ID2>0x01</ID2>
				<dev_macro>DEV_DCL</dev_macro>
				<struct_var_name>dcl_dc_vol</struct_var_name>
				<ID1>0x30</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>48</default_value>
					<max_value>60</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>DC Voltage</short_name>
					<full_name>DC Voltage</full_name>
				</name>
				<unit>V</unit>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xd001010020001">
			<attr.s>
				<ID2>0x02</ID2>
				<dev_macro>DEV_DCL</dev_macro>
				<struct_var_name>dcl_load_total_cur</struct_var_name>
				<ID1>0x30</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>3000</max_value>
					<min_value>0</min_value>
					<precision>1</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>Load Current</short_name>
					<full_name>Load Total Current</full_name>
				</name>
				<unit>A</unit>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xd001010030001">
			<attr.s>
				<ID2>0x12</ID2>
				<dev_macro>DEV_DCL</dev_macro>
				<struct_var_name>dcl_load_total_pow</struct_var_name>
				<ID1>0x30</ID1>
			</attr.s>
			<attr.r>
				<signal_type>1</signal_type>
				<values>
					<default_value>0</default_value>
					<max_value>600</max_value>
					<min_value>0</min_value>
					<precision>2</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>Load Total Pwr.</short_name>
					<full_name>DC Load Total Power</full_name>
				</name>
				<unit>kW</unit>
			</attr.r>
			<attr.rw>
				<data_storage>
					<period>28800</period>
				</data_storage>
			</attr.rw>
		</sid>
		<sid value="0xd001070010001">
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
			<attr.s>
				<ID2>0x15</ID2>
				<dev_macro>DEV_DCL</dev_macro>
				<struct_var_name>dcl_load_energy</struct_var_name>
				<ID1>0x05</ID1>
			</attr.s>
			<attr.r>
				<signal_type>7</signal_type>
				<values>
					<precision>2</precision>
					<data_type>float</data_type>
				</values>
				<name>
					<short_name>Load Power Cons.</short_name>
					<full_name>Load Power Consumption</full_name>
				</name>
				<unit>kWh</unit>
			</attr.r>
			<attr.rw>
				<alarm>
					<alm_level>28800</alm_level>
				</alarm>
			</attr.rw>
		</sid>
	</device>
	<device name="VRLA Battery">
		<sid value="0xe001050010001">
			<attr.s>
				<dev_macro>DEV_LAB</dev_macro>
				<struct_var_name>lab_bat_cur_coef</struct_var_name>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>0.15</default_value>
					<data_type>float</data_type>
					<max_value>0.6</max_value>
					<min_value>0.051</min_value>
					<precision>3</precision>
					<step>0.001</step>
				</values>
				<para_type>2</para_type>
				<unit>C10</unit>
				<name>
					<short_name>Chg.Curr.Coeff.</short_name>
					<full_name>Battery Charge Current Coefficient</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xe001050020001">
			<attr.s>
				<ID2>0x15</ID2>
				<dev_macro>DEV_LAB</dev_macro>
				<struct_var_name>lab_test_vol</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Test Stop Volt</short_name>
					<full_name>Test Stop Voltage</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>46</default_value>
					<data_type>float</data_type>
					<max_value>50</max_value>
					<min_value>42</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<para_type>2</para_type>
				<unit>V</unit>
				<constraints>
					<constraint operator="&gt;=" full_name="Battery Group|LLVD1 Voltage" backlash="1"/>
					<constraint operator="&gt;=" full_name="Battery Group|LLVD2 Voltage" backlash="1"/>
					<constraint operator="&gt;=" full_name="Battery Group|BLVD Voltage" backlash="1"/>
					<constraint operator="&gt;=" full_name="Battery Group|TLLVD1 Voltage" backlash="1"/>
				</constraints>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xe001050030001">
			<attr.s>
				<ID2>0x16</ID2>
				<dev_macro>DEV_LAB</dev_macro>
				<struct_var_name>lab_test_stop_cap</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Test Stop SOC</short_name>
					<full_name>Test Stop SOC Threshold</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>65</default_value>
					<data_type>float</data_type>
					<max_value>100</max_value>
					<min_value>41</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>%</unit>
				<constraints>
					<constraint operator="&gt;=" full_name="Battery Group|LLVD1 SOC Threshold" backlash="1"/>
					<constraint operator="&gt;=" full_name="Battery Group|LLVD2 SOC Threshold" backlash="1"/>
					<constraint operator="&gt;=" full_name="Battery Group|BLVD SOC Threshold" backlash="1"/>
					<constraint operator="&gt;=" full_name="TLLVD1 SOC Threshold" backlash="1"/>
					<constraint operator="&lt;=" full_name="Battery Group|Battery Test Fail SOC Threshold"/>
				</constraints>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xe001050040001">
			<attr.s>
				<ID2>0xA1</ID2>
				<dev_macro>DEV_LAB</dev_macro>
				<struct_var_name>lab_test_fail_soc</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Test Fail SOC</short_name>
					<full_name>Battery Test Fail SOC Threshold</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>85</default_value>
					<data_type>float</data_type>
					<max_value>100</max_value>
					<min_value>50</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>%</unit>
				<constraints>
					<constraint operator="&gt;=" full_name="Battery Group|Test Stop SOC Threshold"/>
				</constraints>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xe001050050001">
			<attr.s>
				<ID2>0x17</ID2>
				<dev_macro>DEV_LAB</dev_macro>
				<struct_var_name>lab_test_max_hour</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<signal_type>5</signal_type>
				<values>
					<default_value>480</default_value>
					<data_type>int</data_type>
					<max_value>1440</max_value>
					<min_value>0</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>Min</unit>
				<name>
					<short_name>Test Max. Dura.</short_name>
					<full_name>Test Maximum Duration</full_name>
				</name>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xe001050060001">
			<attr.s>
				<optional_attribute>llvd1_time_enabled</optional_attribute>
				<ID2>0x23</ID2>
				<dev_macro>DEV_LAB</dev_macro>
				<struct_var_name>lab_llvd1_time</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<name>
					<short_name>LLVD1 Dura.</short_name>
					<full_name>LLVD1 Duration</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>1440</default_value>
					<data_type>int</data_type>
					<max_value>7200</max_value>
					<min_value>3</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>Min</unit>
				<constraints>
					<constraint operator="&gt;=" full_name="Gen Start Discharge Duration" backlash="30"/>
				</constraints>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xe001050070001">
			<attr.s>
				<optional_attribute>llvd1_enabled</optional_attribute>
				<ID2>0x21</ID2>
				<dev_macro>DEV_LAB</dev_macro>
				<struct_var_name>lab_llvd1_vol</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<name>
					<short_name>LLVD1 Volt.</short_name>
					<full_name>LLVD1 Voltage</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>45</default_value>
					<data_type>float</data_type>
					<max_value>49</max_value>
					<min_value>38</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<para_type>2</para_type>
				<unit>V</unit>
				<constraints>
					<constraint operator="&lt;=" full_name="Battery Group|Test Stop Voltage" backlash="-1"/>
					<constraint operator="&lt;=" full_name="DG.Start Voltage Threshold" backlash="-1"/>
				</constraints>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xe001050080001">
			<attr.s>
				<optional_attribute>llvd1_cap_enabled</optional_attribute>
				<ID2>0x55</ID2>
				<dev_macro>DEV_LAB</dev_macro>
				<struct_var_name>lab_llvd1_cap</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<name>
					<short_name>LLVD1 SOC</short_name>
					<full_name>LLVD1 SOC Threshold</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>20</default_value>
					<data_type>float</data_type>
					<max_value>80</max_value>
					<min_value>10</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>%</unit>
				<constraints>
					<constraint operator="&lt;=" full_name="Battery Group|Test Stop SOC Threshold" backlash="-1"/>
					<constraint operator="&lt;=" full_name="DG.Start SOC Threshold" backlash="-5"/>
				</constraints>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xe001050090001">
			<attr.s>
				<optional_attribute>llvd2_time_enabled</optional_attribute>
				<ID2>0x26</ID2>
				<dev_macro>DEV_LAB</dev_macro>
				<struct_var_name>lab_llvd2_time</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<name>
					<short_name>LLVD2 Dura.</short_name>
					<full_name>LLVD2 Duration</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>1680</default_value>
					<data_type>int</data_type>
					<max_value>7200</max_value>
					<min_value>30</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>Min</unit>
				<constraints>
					<constraint operator="&gt;=" full_name="Gen Start Discharge Duration" backlash="30"/>
					<constraint operator="&gt;=" full_name="TLLVD1 Duration" backlash="5"/>
				</constraints>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xe0010500a0001">
			<attr.s>
				<optional_attribute>llvd2_enabled</optional_attribute>
				<ID2>0x24</ID2>
				<dev_macro>DEV_LAB</dev_macro>
				<struct_var_name>lab_llvd2_vol</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<name>
					<short_name>LLVD2 Volt.</short_name>
					<full_name>LLVD2 Voltage</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>44</default_value>
					<data_type>float</data_type>
					<max_value>49</max_value>
					<min_value>38</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<para_type>2</para_type>
				<unit>V</unit>
				<constraints>
					<constraint operator="&lt;=" full_name="Battery Group|TLLVD1 Voltage" backlash="-1"/>
					<constraint operator="&lt;=" full_name="DG.Start Voltage Threshold" backlash="-1"/>
				</constraints>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xe0010500b0001">
			<attr.s>
				<optional_attribute>llvd2_cap_enabled</optional_attribute>
				<ID2>0x56</ID2>
				<dev_macro>DEV_LAB</dev_macro>
				<struct_var_name>lab_llvd2_cap</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<name>
					<short_name>LLVD2 SOC</short_name>
					<full_name>LLVD2 SOC Threshold</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>10</default_value>
					<data_type>float</data_type>
					<max_value>80</max_value>
					<min_value>10</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>%</unit>
				<constraints>
					<constraint operator="&lt;=" full_name="Battery Group|TLLVD1 SOC" backlash="-5"/>
					<constraint operator="&lt;=" full_name="DG.Start SOC Threshold" backlash="-5"/>
				</constraints>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xe0010500c0001">
			<attr.s>
				<optional_attribute>blvd_time_enabled</optional_attribute>
				<ID2>0x3A</ID2>
				<dev_macro>DEV_LAB</dev_macro>
				<struct_var_name>lab_blvd_time</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<name>
					<short_name>BLVD Dura.</short_name>
					<full_name>BLVD Duration</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>1680</default_value>
					<data_type>int</data_type>
					<max_value>7200</max_value>
					<min_value>30</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>Min</unit>
				<constraints>
					<constraint operator="&gt;=" full_name="Gen Start Discharge Duration" backlash="30"/>
				</constraints>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xe0010500d0001">
			<attr.s>
				<optional_attribute>blvd_enabled</optional_attribute>
				<ID2>0x38</ID2>
				<dev_macro>DEV_LAB</dev_macro>
				<struct_var_name>lab_blvd_volt</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<name>
					<short_name>BLVD Voltage</short_name>
					<full_name>BLVD Voltage</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>44</default_value>
					<data_type>float</data_type>
					<max_value>49</max_value>
					<min_value>38</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<para_type>2</para_type>
				<unit>V</unit>
				<constraints>
					<constraint operator="&lt;=" full_name="Battery Group|Test Stop Voltage" backlash="-1"/>
					<constraint operator="&lt;=" full_name="DG.Start Voltage Threshold" backlash="-1"/>
				</constraints>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xe0010500e0001">
			<attr.s>
				<optional_attribute>blvd_cap_enabled</optional_attribute>
				<ID2>0x58</ID2>
				<dev_macro>DEV_LAB</dev_macro>
				<struct_var_name>lab_blvd_cap</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<name>
					<short_name>BLVD SOC</short_name>
					<full_name>BLVD SOC Threshold</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>10</default_value>
					<data_type>float</data_type>
					<max_value>80</max_value>
					<min_value>10</min_value>
					<precision>0</precision>
					<step>1</step>
				</values>
				<para_type>2</para_type>
				<unit>%</unit>
				<constraints>
					<constraint operator="&lt;=" full_name="Battery Group|Test Stop SOC Threshold" backlash="-1"/>
					<constraint operator="&lt;=" full_name="DG.Start SOC Threshold" backlash="-5"/>
				</constraints>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xe0010500f0001">
			<attr.s>
				<optional_attribute>vrla_batt</optional_attribute>
				<ID2>0x05</ID2>
				<dev_macro>DEV_LAB</dev_macro>
				<struct_var_name>lab_float_vol</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Float Voltage</short_name>
					<full_name>Float Charge Voltage</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>53.5</default_value>
					<data_type>float</data_type>
					<max_value>58</max_value>
					<min_value>42</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<para_type>2</para_type>
				<unit>V</unit>
				<constraints>
					<constraint operator="&lt;=" full_name="Battery Group|Equalized Charge Voltage"/>
					<constraint operator="&lt;=" full_name="SMR Output High Off Voltage" backlash="-2"/>
				</constraints>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
		<sid value="0xe001050100001">
			<attr.s>
				<optional_attribute>equalized_enabled</optional_attribute>
				<ID2>0x07</ID2>
				<dev_macro>DEV_LAB</dev_macro>
				<struct_var_name>lab_equal_vol</struct_var_name>
				<ID1>0x03</ID1>
			</attr.s>
			<attr.r>
				<dimensions>
					<dimension>1</dimension>
				</dimensions>
				<name>
					<short_name>Equalized Voltage</short_name>
					<full_name>Equalized Charge Voltage</full_name>
				</name>
				<signal_type>5</signal_type>
				<values>
					<default_value>56.4</default_value>
					<data_type>float</data_type>
					<max_value>58</max_value>
					<min_value>42</min_value>
					<precision>1</precision>
					<step>0.1</step>
				</values>
				<para_type>2</para_type>
				<unit>V</unit>
				<constraints>
					<constraint operator="&gt;=" full_name="Battery Group|Float Charge Voltage"/>
					<constraint operator="&lt;=" full_name="SMR Output High Off Voltage" backlash="-2"/>
				</constraints>
			</attr.r>
			<attr.show>
				<web>1</web>
				<gui>1</gui>
				<data>2</data>
			</attr.show>
		</sid>
	</device>
</dictionary>