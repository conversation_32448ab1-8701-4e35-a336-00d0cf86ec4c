*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
CPU占有率高告警测试
    [Documentation]    干接点实际是否动作需要将输出干接点连接到输入干接点，输出干接点接${plat.Inrelay1Status}；请在测试前连接好
    
    [Timeout]
    连接CSU
    显示属性配置    CPU利用率    模拟量    web_attr=On    gui_attr=On
    #获取参数/默认值
    ${干接点上下限}    获取web参数量    CPU利用率高告警干接点
    ${告警干接点默认值}    run keyword if    '${干接点上下限}'=='0'    set variable    无干接点
    ...    ELSE    set variable    A1
    #获取告警级别和输出干接点设置值
    ${级别设置值}    获取web参数量    CPU利用率高告警
    ${干接点设置值}    获取web参数量    CPU利用率高告警干接点
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    CPU利用率高告警    严重
    ${可设置范围}    获取web参数可设置范围    CPU利用率高阈值
    设置web参数量    CPU利用率高阈值    ${可设置范围}[1]
    ${获取值}    获取web实时数据    CPU利用率
    ${设置值}    获取web参数量    CPU利用率高阈值
    should be true    ${获取值}<=${设置值}
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    CPU利用率高告警
    ${告警不存在}    判断告警存在_带返回值    CPU利用率高告警
    should not be true    ${告警不存在}    #告警不存在返回FALSE    #CPU占用率无法设置10    #关键字设置成功不提示失败
    设置web参数量    CPU利用率高阈值    ${可设置范围}[0]
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    CPU利用率高告警
    ${告警存在}    判断告警存在_带返回值    CPU利用率高告警
    should be true    ${告警存在}
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        设置web参数量    CPU利用率高告警    ${告警级别设置}
        Wait Until Keyword Succeeds    1m    1    查询指定告警信息    CPU利用率高告警
        sleep    3
        ${CPU占用率过高告警告警级别}    获取web告警属性    CPU利用率高告警    告警级别
        should be equal    ${告警级别设置}    ${CPU占用率过高告警告警级别}
    END
    
    设置web参数量    CPU利用率高告警    次要
    FOR    ${告警干接点设置}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    CPU利用率高告警    告警干接点
        run keyword if    ${告警干接点获取}!=0    设置web参数量    CPU利用率高告警干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        设置web参数量    CPU利用率高告警干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    CPU利用率高告警    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
    END
    显示属性配置    CPU利用率    模拟量    web_attr=Off    gui_attr=Off
    [Teardown]    Run keywords    设置web参数量    CPU利用率高告警    屏蔽
    ...    AND    设置web参数量    CPU利用率高告警干接点    0
    ...    AND    设置web设备参数量为默认值    CPU利用率高阈值
    

内存占用率高告警测试
    [Tags]    V3.0
    
    连接CSU
    显示属性配置    内存利用率    模拟量    web_attr=On    gui_attr=On
    #获取参数/默认值
    #获取告警级别和输出干接点设置值
    ${级别设置值}    获取web参数量    内存利用率高告警
    ${干接点设置值}    获取web参数量    内存利用率高告警干接点
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    内存利用率高告警    严重
    ${可设置范围}    获取web参数可设置范围    内存利用率高阈值
    设置web参数量    内存利用率高阈值    ${可设置范围}[1]
    ${获取值}    获取web实时数据    内存利用率
    ${设置值}    获取web参数量    内存利用率高阈值
    should be true    ${获取值}<=${设置值}
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    内存利用率高告警
    ${告警不存在}    判断告警存在_带返回值    内存利用率高告警
    should not be true    ${告警不存在}    #告警不存在返回FALSE    #CPU占用率无法设置10    #关键字设置成功不提示失败
    设置web参数量    内存利用率高阈值    ${可设置范围}[0]
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    内存利用率高告警
    ${告警存在}    判断告警存在_带返回值    内存利用率高告警
    should be true    ${告警存在}
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        设置web参数量    内存利用率高告警    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    内存利用率高告警
        sleep    3
        ${内存占用率过高告警告警级别}    获取web告警属性    内存利用率高告警    告警级别
        should be equal    ${告警级别设置}    ${内存占用率过高告警告警级别}
    END
    
    设置web参数量    内存利用率高告警    次要
    FOR    ${告警干接点设置}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    内存利用率高告警    告警干接点
        run keyword if    ${告警干接点获取}!=0    设置web参数量    内存利用率高告警干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        设置web参数量    内存利用率高告警干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    内存利用率高告警    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
    END
    显示属性配置    内存利用率    模拟量    web_attr=Off    gui_attr=Off
    [Teardown]    Run keywords    设置web参数量    内存利用率高告警    屏蔽
    ...    AND    设置web参数量    内存利用率高告警干接点    0
    ...    AND    设置web设备参数量为默认值    内存利用率高阈值
    

禁止所有告警功能测试
    [Tags]    V3.0
    
    [Timeout]
    连接CSU
    #获取参数/默认值
    ${干接点上下限}    获取web参数量    <<禁止所有告警干接点~0x1001030010001>>
    ${告警干接点默认值}    run keyword if    '${干接点上下限}'=='0'    set variable    无干接点
    ...    ELSE    set variable    A1
    #获取告警级别和输出干接点设置值
    ${级别设置值}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    ${干接点设置值}    获取web参数量    <<禁止所有告警干接点~0x1001030010001>>
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    <<禁止所有告警~0x1001030010001>>    严重
    Wait Until Keyword Succeeds    5m    2    设置web参数量    内存利用率高告警    严重
    wait until keyword succeeds    30    1    设置web参数量    内存利用率高阈值    10
    wait until keyword succeeds    30    1    设置web控制量    允许所有告警
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    内存利用率高告警
    wait until keyword succeeds    30    1    设置web控制量    <<禁止所有告警~0x1001040010001>>
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    内存利用率高告警
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    禁止所有告警
    FOR    ${告警级别设置}    IN    严重    主要
        wait until keyword succeeds    30    1    设置web参数量    <<禁止所有告警~0x1001030010001>>    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    禁止所有告警
        sleep    3
        ${内存占用率过高告警告警级别}    获取web告警属性    <<禁止所有告警~0x1001030010001>>    告警级别
        should be equal    '${告警级别设置}'    '${内存占用率过高告警告警级别}'
    END
    
    wait until keyword succeeds    30    1    设置web参数量    <<禁止所有告警~0x1001030010001>>    严重    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    FOR    ${告警干接点设置}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    <<禁止所有告警~0x1001030010001>>    告警干接点
        run keyword if    ${告警干接点获取}!=0    wait until keyword succeeds    30    1    设置web参数量    <<禁止所有告警干接点~0x1001030010001>>    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        wait until keyword succeeds    30    1    设置web参数量    <<禁止所有告警干接点~0x1001030010001>>    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    <<禁止所有告警~0x1001030010001>>    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
    END
    [Teardown]    Run keywords    设置web控制量    允许所有告警
    ...    AND    设置web参数量    <<禁止所有告警干接点~0x1001030010001>>    0
    ...    AND    设置web参数量    <<禁止所有告警~0x1001030010001>>    严重
    ...    AND    设置web参数量    内存利用率高阈值    80
    

输入干接点告警
    [Documentation]    通过DI2作为输入干接点告警的输入。输出干接点状态通过DI1的状态读出。
    
    连接CSU
    #获取参数/默认值
    ${干接点上下限}    获取web参数量    输入干接点告警_2干接点
    ${告警干接点默认值}    run keyword if    '${干接点上下限}'=='0'    set variable    无干接点
    ...    ELSE    set variable    A1
    #获取告警级别和输出干接点设置值
    ${级别设置值}    获取web参数量    输入干接点告警_2
    ${干接点设置值}    获取web参数量    输入干接点告警_2干接点
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    输入干接点告警_2    严重
    #设置${plat.Inrelay2Status}，对应的CSU:输入干接点告警[2]
    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay2Status}    CSU    输入干接点告警_2    断开
    Wait Until Keyword Succeeds    5m    2    设置web参数量    输入干接点告警_2    屏蔽
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    输入干接点告警_2
    Wait Until Keyword Succeeds    5m    2    设置web参数量    输入干接点告警_2    严重
    wait until keyword succeeds    1m    1    查询指定告警信息    输入干接点告警_2
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    输入干接点告警_2    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    输入干接点告警_2
        sleep    3
        ${内存占用率过高告警告警级别}    获取web告警属性    输入干接点告警_2    告警级别
        should be equal    '${告警级别设置}'    '${内存占用率过高告警告警级别}'
    END
    
    wait until keyword succeeds    30    1    设置web参数量    输入干接点告警_2    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    FOR    ${告警干接点设置}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    输入干接点告警_2    告警干接点
        run keyword if    ${告警干接点获取}!=0    wait until keyword succeeds    30    1    设置web参数量    输入干接点告警_2干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        wait until keyword succeeds    30    1    设置web参数量    输入干接点告警_2干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    输入干接点告警_2    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
    END
    [Teardown]    Run keywords    设置web参数量    输入干接点告警_2干接点    0
    ...    AND    设置web参数量    输入干接点告警_2    次要
    ...    AND    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay2Status}    无    无    闭合
    

系统过载告警测试
    
    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    #获取参数/默认值
    ${干接点上下限}    获取web参数量    系统过载告警干接点
    ${告警干接点默认值}    run keyword if    '${干接点上下限}'=='0'    set variable    无干接点
    ...    ELSE    set variable    A1
    #获取告警级别和输出干接点设置值
    ${级别设置值}    获取web参数量    系统过载告警
    ${干接点设置值}    获取web参数量    系统过载告警干接点
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    系统过载告警    严重
    Comment    wait until keyword succeeds    10    1    设置web参数量    市电额定有功功率    0    #交流输入限功率告警会屏蔽系统过载
    wait until keyword succeeds    30    1    设置web参数量    系统过载告警阈值    80    #系统配置3个整流器，满载150A。大于30A给系统过载告警
    设置负载电压电流    53.5    70
    打开负载输出
    Wait Until Keyword Succeeds    5m    2    判断告警不存在    系统过载告警
    wait until keyword succeeds    30    1    设置web参数量    系统过载告警阈值    20
    sleep    10
    ${负载总电流}    获取web实时数据    负载总电流
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    系统过载告警
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    系统过载告警    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    系统过载告警
        sleep    3
        ${系统过载告警告警级别}    获取web告警属性    系统过载告警    告警级别
        should be equal    '${告警级别设置}'    '${系统过载告警告警级别}'
    END
    
    wait until keyword succeeds    30    1    设置web参数量    系统过载告警    严重    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    FOR    ${告警干接点设置}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    系统过载告警    告警干接点
        run keyword if    ${告警干接点获取}!=0    wait until keyword succeeds    30    1    设置web参数量    系统过载告警干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        wait until keyword succeeds    30    1    设置web参数量    系统过载告警干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    系统过载告警    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        wait until keyword succeeds    1m    1    查询指定告警信息    系统过载告警
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
    END
    [Teardown]    Run keywords    设置web参数量    系统过载告警    严重
    ...    AND    设置web参数量    系统过载告警干接点    0
    ...    AND    设置web设备参数量为默认值    系统过载告警阈值
    ...    AND    关闭负载输出
    

交流输入场景配置错误测试
    
    电池管理初始化
    #获取参数/默认值
    ${干接点上下限}    获取web参数量    交流输入场景配置错误告警干接点
    ${告警干接点默认值}    run keyword if    '${干接点上下限}'=='0'    set variable    无干接点
    ...    ELSE    set variable    A1
    #获取告警级别和输出干接点设置值
    ${级别设置值}    获取web参数量    交流输入场景配置错误告警
    ${干接点设置值}    获取web参数量    交流输入场景配置错误告警干接点
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    交流输入场景配置错误告警    严重
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流输入场景    无
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    交流输入场景配置错误告警
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    交流输入场景配置错误告警    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    交流输入场景配置错误告警
        sleep    3
        ${系统过载告警告警级别}    获取web告警属性    交流输入场景配置错误告警    告警级别
        should be equal    '${告警级别设置}'    '${系统过载告警告警级别}'
    END
    
    wait until keyword succeeds    30    1    设置web参数量    系统过载告警    严重    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    FOR    ${告警干接点设置}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    交流输入场景配置错误告警    告警干接点
        run keyword if    ${告警干接点获取}!=0    wait until keyword succeeds    30    1    设置web参数量    交流输入场景配置错误告警干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        wait until keyword succeeds    30    1    设置web参数量    交流输入场景配置错误告警干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    交流输入场景配置错误告警    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        wait until keyword succeeds    1m    1    查询指定告警信息    交流输入场景配置错误告警
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
    END
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流输入场景    市电    #市电场景测试
    wait until keyword succeeds    3m    2    判断告警不存在    交流输入场景配置错误告警
    [Teardown]    Run keywords    设置web参数量    交流输入场景配置错误告警    严重
    ...    AND    设置web参数量    交流输入场景配置错误告警干接点    0
    ...    AND    设置web参数量    交流输入场景    市电
    

UIB板通讯断告警测试
    
    实时告警刷新完成
    wait until keyword succeeds    1m    1    判断告警不存在    UIB通讯断
    ${告警}    判断告警存在_带返回值    UIB通讯断
    should not be true    ${告警}
    模拟数字量告警    UIB板通讯断    ON
    sleep    10
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    10    1    设置web参数量    UIB通讯断    ${告警级别}
        wait until keyword succeeds    5m    1    查询指定告警信息    UIB通讯断
        sleep    3
        ${告警级别获取}    获取web告警属性    UIB通讯断    告警级别
        should be equal    ${告警级别}    ${告警级别获取}
    END
    模拟数字量告警    UIB板通讯断    OFF
    sleep    10
    wait until keyword succeeds    5m    1    判断告警不存在    UIB通讯断
    [Teardown]    run keywords    设置web参数量    UIB通讯断    严重
    ...    AND    模拟数字量告警    UIB板通讯断    OFF
    

IDDB板通讯断告警测试
    
    实时告警刷新完成
    wait until keyword succeeds    1m    1    判断告警不存在    IDDB通讯断
    ${告警}    判断告警存在_带返回值    IDDB通讯断
    should not be true    ${告警}
    模拟数字量告警    IDDB板通讯断    ON
    sleep    10
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    10    1    设置web参数量    IDDB通讯断    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    IDDB通讯断
        sleep    3
        ${告警级别获取}    获取web告警属性    IDDB通讯断    告警级别
        should be equal    ${告警级别}    ${告警级别获取}
    END
    
    设置web参数量    IDDB通讯断    次要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    IDDB通讯断    告警干接点
        run keyword if    ${告警干接点获取}!=0    设置web参数量    IDDB通讯断干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        设置web参数量    IDDB通讯断干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    IDDB通讯断    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0
    END
    模拟数字量告警    IDDB板通讯断    OFF
    sleep    10
    wait until keyword succeeds    5m    1    判断告警不存在    IDDB通讯断
    [Teardown]    run keywords    设置web参数量    IDDB通讯断干接点    0
    ...    AND    模拟数字量告警    IDDB板通讯断    OFF
    

CAN通讯断告警测试
    
    实时告警刷新完成
    wait until keyword succeeds    1m    1    判断告警不存在    所有整流器通讯断告警
    ${告警}    判断告警存在_带返回值    所有整流器通讯断告警
    should not be true    ${告警}
    模拟数字量告警    CAN通讯断    ON
    sleep    10
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    10    1    设置web参数量    所有整流器通讯断告警    ${告警级别}
        wait until keyword succeeds    5m    1    查询指定告警信息    所有整流器通讯断告警
        sleep    3
        ${告警级别获取}    获取web告警属性    所有整流器通讯断告警    告警级别
        should be equal    ${告警级别}    ${告警级别获取}
    END
    
    设置web参数量    所有整流器通讯断告警    次要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    所有整流器通讯断告警    告警干接点
        run keyword if    ${告警干接点获取}!=0    设置web参数量    所有整流器通讯断告警干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        设置web参数量    所有整流器通讯断告警干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    所有整流器通讯断告警    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0
    END
    模拟数字量告警    CAN通讯断    OFF
    sleep    10
    wait until keyword succeeds    5m    1    判断告警不存在    所有整流器通讯断告警
    [Teardown]    run keywords    设置web参数量    所有整流器通讯断告警干接点    0
    ...    AND    模拟数字量告警    CAN通讯断    OFF
    

RS485通讯断告警测试
    [Setup]    Run Keywords    FB100B3铁锂电池测试前置条件
    ...        AND    配置电池为普通铅酸电池
    实时告警刷新完成
    wait until keyword succeeds    1m    1    判断告警不存在    <<BMS通信断告警~0x170010302f0001>>
    ${告警}    判断告警存在_带返回值    <<BMS通信断告警~0x170010302f0001>>
    should not be true    ${告警}
    模拟数字量告警    RS485通讯断    ON
    sleep    10
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    10    1    设置web参数量    <<BMS通信断告警~0x170010302f0001>>    ${告警级别}
        wait until keyword succeeds    5m    1    查询指定告警信息    BMS通信断告警
        wait until keyword succeeds    30m    60    获取指定告警数量大于    BMS通信断告警    30
        sleep    3
        ${告警级别获取}    获取web告警属性    <<BMS通信断告警-1~0x170010302f0001>>    告警级别
        should be equal    ${告警级别}    ${告警级别获取}
    END
    
    设置web参数量    <<BMS通信断告警~0x170010302f0001>>    次要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    <<BMS通信断告警-1~0x170010302f0001>>    告警干接点
        run keyword if    ${告警干接点获取}!=0    设置web参数量    <<BMS通信断告警干接点~0x170010302f0001>>    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        设置web参数量    <<BMS通信断告警干接点~0x170010302f0001>>    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    <<BMS通信断告警-1~0x170010302f0001>>    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0
    END
    模拟数字量告警    RS485通讯断    OFF
    sleep    10
    wait until keyword succeeds    30m    1    查询指定告警信息不为    BMS通信断告警
    [Teardown]    run keywords    FB100B3铁锂电池测试结束条件
    ...    AND    模拟数字量告警    RS485通讯断    OFF
    

实时告警掉电保存测试
    实时告警刷新完成
    
    设置web参数量    门磁告警    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    门磁告警
    ${门磁告警}    判断告警存在_带返回值    门磁告警
    should not be true    ${门磁告警}
    模拟数字量告警    门磁告警    ON
    sleep    10
    ${门磁状态}    获取干接点状态    ${plat.MagneticDoor}
    should be true    ${门磁状态}==1    \    #0为正常（门磁闭合），1为断开（门磁断开）
    设置web参数量    门磁告警    次要
    wait until keyword succeeds    1m    1    查询指定告警信息    门磁告警
    ${告警干接点获取}    获取web告警属性    门磁告警    告警干接点
    ${告警级别}    获取web告警属性    门磁告警    告警级别
    ${告警时间}    获取web告警属性    门磁告警    告警时间
    测试台上下电操作    ${测试台编号}    OFF
    sleep    5
    测试台上下电操作    ${测试台编号}    ON
    实时告警刷新完成
    ${系统时间}    获取系统时间
    wait until keyword succeeds    1m    1    查询指定告警信息    门磁告警
    ${告警干接点获取new}    获取web告警属性    门磁告警    告警干接点
    ${告警级别new}    获取web告警属性    门磁告警    告警级别
    ${告警时间new}    获取web告警属性    门磁告警    告警时间
    should be equal    ${告警干接点获取}    ${告警干接点获取new}
    should be equal    ${告警级别}    ${告警级别new}
    ${时间差}    subtract date from date    ${告警时间new}    ${系统时间}
    should be true    -60<=${时间差}<=60
    模拟数字量告警    门磁告警    OFF
    wait until keyword succeeds    1m    1    判断告警不存在    门磁告警
    [Teardown]    模拟数字量告警    门磁告警    OFF
    
