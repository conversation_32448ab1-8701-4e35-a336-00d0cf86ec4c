*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
修改用户参数值导入测试
    连接CSU
    ${CPU利用率高阈值获取}    获取web参数量    CPU利用率高阈值
    ${内存利用率高阈值获取}    获取web参数量    内存利用率高阈值
    ${获取值}    获取web参数量    <<电池启用日期-1~0xb001050010001>>
    ${用户参数文件}    导出参数文件
    修改导出参数值    ${用户参数文件}\\parameter.csv    CPU利用率高阈值=84    内存利用率高阈值=83    电池启用日期=2021/12/31
    压缩文件夹    ${用户参数文件}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${用户参数文件}.zip
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    ${CPU利用率高阈值获取}    获取web参数量    CPU利用率高阈值
    should be true    ${CPU利用率高阈值获取}==84
    ${内存利用率高阈值获取}    获取web参数量    内存利用率高阈值
    should be true    ${内存利用率高阈值获取}==83
    ${获取值}    获取web参数量    <<电池启用日期-1~0xb001050010001>>
    should be true    ${获取值}==2021-12-31
    [Teardown]    设置web设备参数量为默认值    CPU利用率高阈值    内存利用率高阈值    <<电池启用日期-1~0xb001050010001>>

参数值超范围导入测试
    连接CSU
    ${CPU利用率高阈值获取}    获取web参数量    CPU利用率高阈值
    ${内存利用率高阈值获取}    获取web参数量    内存利用率高阈值
    ${获取值}    获取web参数量    <<电池启用日期-1~0xb001050010001>>
    ${用户参数文件}    导出参数文件
    修改导出参数值    ${用户参数文件}\\parameter.csv    CPU利用率高阈值=102    内存利用率高阈值=120    电池启用日期=2038/12/31
    压缩文件夹    ${用户参数文件}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${用户参数文件}.zip
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    @{历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain    @{历史事件内容1}[0]    导入 失败
    should not be true    ${导入结果}
    ${CPU利用率高阈值获取new}    获取web参数量    CPU利用率高阈值
    should be true    ${CPU利用率高阈值获取new}==${CPU利用率高阈值获取}
    ${内存利用率高阈值获取new}    获取web参数量    内存利用率高阈值
    should be true    ${内存利用率高阈值获取new}==${内存利用率高阈值获取}
    ${获取值new}    获取web参数量    <<电池启用日期-1~0xb001050010001>>
    should be true    ${获取值new}==${获取值}
    [Teardown]    设置web设备参数量为默认值    CPU利用率高阈值    内存利用率高阈值    <<电池启用日期-1~0xb001050010001>>

参数值不满足约束关系导入测试
    连接CSU
    ${获取值1}    获取web参数量    浮充电压
    ${获取值2}    获取web参数量    均充电压
    ${获取值3}    获取web参数量    电池下电电压
    ${获取值4}    获取web参数量    测试终止电压
    ${用户参数文件}    导出参数文件
    修改导出参数值    ${用户参数文件}\\parameter.csv    浮充电压=56    均充电压=55    测试终止电压=47    电池下电电压=48
    压缩文件夹    ${用户参数文件}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${用户参数文件}.zip
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    @{历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain    @{历史事件内容1}[0]    导入 失败
    should not be true    ${导入结果}
    ${获取值1new}    获取web参数量    浮充电压
    should be true    ${获取值1}==${获取值1new}
    ${获取值2new}    获取web参数量    均充电压
    should be true    ${获取值2}==${获取值2new}
    ${获取值3new}    获取web参数量    电池下电电压
    should be true    ${获取值3}==${获取值3new}
    ${获取值4new}    获取web参数量    测试终止电压
    should be true    ${获取值4}==${获取值4new}
    [Teardown]    #设置web设备参数量为默认值    电池下电电压    测试终止电压    均充电压    浮充电压

删除部分用户参数导入测试
    连接CSU
    ${CPU利用率高阈值获取}    获取web参数量    CPU利用率高阈值
    ${内存利用率高阈值获取}    获取web参数量    内存利用率高阈值
    ${电池启用获取值}    获取web参数量    <<电池启用日期-1~0xb001050010001>>
    ${用户参数文件}    导出参数文件
    删除指定导出参数值    ${用户参数文件}\\parameter.csv    CPU利用率高阈值_0x1001050020001    内存利用率高阈值_0x1001050030001    电池启用日期_0xb001050010001
    压缩文件夹    ${用户参数文件}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${用户参数文件}.zip
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    ${CPU利用率高阈值获取new}    获取web参数量    CPU利用率高阈值
    should be true    ${CPU利用率高阈值获取new}==${CPU利用率高阈值获取}
    ${内存利用率高阈值获取new}    获取web参数量    内存利用率高阈值
    should be true    ${内存利用率高阈值获取new}==${内存利用率高阈值获取}
    ${获取值new}    获取web参数量    <<电池启用日期-1~0xb001050010001>>
    should be true    ${获取值new}==${电池启用获取值}
    [Teardown]

删除修改部分用户参数导入测试
    连接CSU
    ${CPU利用率高阈值获取}    获取web参数量    CPU利用率高阈值
    ${内存利用率高阈值获取}    获取web参数量    内存利用率高阈值
    ${电池启用获取值}    获取web参数量    <<电池启用日期-1~0xb001050010001>>
    ${交流电流高阈值获取}    获取web参数量    交流电流高阈值
    ${节能带载率上限}    获取web参数量    节能带载率上限
    ${环境温度过高阈值}    获取web参数量    环境温度过高阈值
    ${直流电压高阈值}    获取web参数量    直流电压高阈值
    ${电池电压低阈值获取}    获取web参数量    电池电压低阈值
    ${浮充电压值获取}    获取web参数量    浮充电压
    ${站点名称获取}    获取web参数量    站点名称
    ${获取值}    获取web参数量    市电配置_1
    ${电池容量获取值}    获取web参数量    电池组容量_1
    ${用户参数文件}    导出参数文件
    删除指定导出参数值    ${用户参数文件}\\parameter.csv    CPU利用率高阈值_0x1001050020001    内存利用率高阈值_0x1001050030001    电池启用日期_0xb001050010001
    修改导出参数值    ${用户参数文件}\\parameter.csv    交流电流高阈值=500    直流电压高阈值=57.5    环境温度过高阈值=60    节能带载率上限=1
    修改导出参数值    ${用户参数文件}\\parameter.csv    电池电压低阈值=48    浮充电压=53.6    市电配置[1]=0    电池组容量[1]=200
    压缩文件夹    ${用户参数文件}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${用户参数文件}.zip
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    ${CPU利用率高阈值获取new}    获取web参数量    CPU利用率高阈值
    should be true    ${CPU利用率高阈值获取new}==${CPU利用率高阈值获取}
    ${内存利用率高阈值获取new}    获取web参数量    内存利用率高阈值
    should be true    ${内存利用率高阈值获取new}==${内存利用率高阈值获取}
    ${获取值new}    获取web参数量    <<电池启用日期-1~0xb001050010001>>
    should be true    ${获取值new}==${电池启用获取值}
    ${交流电流高阈值获取}    获取web参数量    交流电流高阈值
    should be true    ${交流电流高阈值获取}==500
    ${节能带载率上限}    获取web参数量    节能带载率上限
    should be true    ${节能带载率上限}==1
    ${环境温度过高阈值}    获取web参数量    环境温度过高阈值
    should be true    ${环境温度过高阈值}==60
    ${直流电压高阈值}    获取web参数量    直流电压高阈值
    should be true    ${直流电压高阈值}==57.5
    ${浮充电压值获取}    获取web参数量    浮充电压
    should be true    ${浮充电压值获取}==53.6
    ${电池电压低值获取}    获取web参数量    电池电压低阈值
    should be true    ${电池电压低值获取}==48
    Comment    ${站点名称获取}    获取web参数量    站点名称
    Comment    Should Be Equal    ${站点名称获取}    Site-X#9
    ${获取值}    获取web参数量    市电配置_1
    Should Be Equal    ${获取值}    无
    ${电池组容量值获取}    获取web参数量    电池组容量_1
    should be true    ${电池组容量值获取}==200
    [Teardown]    设置web设备参数量为默认值    交流电流高阈值    环境温度过高阈值    直流电压高阈值    电池电压低阈值    浮充电压    市电配置_1    电池组容量_1

增加部分用户参数导入测试
    连接CSU
    Wait Until Keyword Succeeds    10    2    设置web参数量    CPU利用率高阈值    10    #下限
    ${CPU利用率高阈值获取}    获取web参数量    CPU利用率高阈值
    should be true    ${CPU利用率高阈值获取}==10
    ${CPU利用率高阈值获取}    获取web参数量    CPU利用率高阈值
    ${内存利用率高阈值获取}    获取web参数量    内存利用率高阈值
    ${电池启用获取值}    获取web参数量    <<电池启用日期-1~0xb001050010001>>
    ${用户参数文件}    导出参数文件
    删除指定导出参数值    ${用户参数文件}\\parameter.csv    CPU利用率高阈值_0x1001050020001    内存利用率高阈值_0x1001050030001    电池启用日期_0xb001050010001
    插入指定导出参数值    ${用户参数文件}\\parameter.csv    0x1001050020001,CSU,CPU利用率高阈值,用户参数,80,10,100,%,
    压缩文件夹    ${用户参数文件}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${用户参数文件}.zip
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    ${内存利用率高阈值获取new}    获取web参数量    内存利用率高阈值
    should be true    ${内存利用率高阈值获取new}==${内存利用率高阈值获取}
    ${获取值new}    获取web参数量    <<电池启用日期-1~0xb001050010001>>
    should be true    ${获取值new}==${电池启用获取值}
    ${CPU利用率高阈值获取}    获取web参数量    CPU利用率高阈值
    should be true    ${CPU利用率高阈值获取}==80

删除增加修改部分用户参数导入测试
    连接CSU
    ${CPU利用率高阈值获取}    获取web参数量    CPU利用率高阈值
    ${内存利用率高阈值获取}    获取web参数量    内存利用率高阈值
    ${电池启用获取值}    获取web参数量    <<电池启用日期-1~0xb001050010001>>
    ${交流电流高阈值获取}    获取web参数量    交流电流高阈值
    ${节能带载率上限}    获取web参数量    节能带载率上限
    ${环境温度过高阈值}    获取web参数量    环境温度过高阈值
    ${直流电压高阈值}    获取web参数量    直流电压高阈值
    ${电池电压低阈值获取}    获取web参数量    电池电压低阈值
    ${浮充电压值获取}    获取web参数量    浮充电压
    ${站点名称获取}    获取web参数量    站点名称
    ${获取值}    获取web参数量    市电配置_1
    ${电池容量获取值}    获取web参数量    电池组容量_1
    ${用户参数文件}    导出参数文件
    删除指定导出参数值    ${用户参数文件}\\parameter.csv    CPU利用率高阈值_0x1001050020001    内存利用率高阈值_0x1001050030001    电池启用日期_0xb001050010001
    插入指定导出参数值    ${用户参数文件}\\parameter.csv    0x1001050020001,CSU,CPU利用率高阈值,用户参数,80,10,100,%,
    修改导出参数值    ${用户参数文件}\\parameter.csv    交流电流高阈值=500    直流电压高阈值=57.5    环境温度过高阈值=60    节能带载率上限=1
    修改导出参数值    ${用户参数文件}\\parameter.csv    电池电压低阈值=48    浮充电压=53.6    市电配置[1]=0    电池组容量[1]=200
    压缩文件夹    ${用户参数文件}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${用户参数文件}.zip
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    ${内存利用率高阈值获取new}    获取web参数量    内存利用率高阈值
    should be true    ${内存利用率高阈值获取new}==${内存利用率高阈值获取}
    ${获取值new}    获取web参数量    <<电池启用日期-1~0xb001050010001>>
    should be true    ${获取值new}==${电池启用获取值}
    ${交流电流高阈值获取}    获取web参数量    交流电流高阈值
    should be true    ${交流电流高阈值获取}==500
    ${节能带载率上限}    获取web参数量    节能带载率上限
    should be true    ${节能带载率上限}==1
    ${环境温度过高阈值}    获取web参数量    环境温度过高阈值
    should be true    ${环境温度过高阈值}==60
    ${直流电压高阈值}    获取web参数量    直流电压高阈值
    should be true    ${直流电压高阈值}==57.5
    ${浮充电压值获取}    获取web参数量    浮充电压
    should be true    ${浮充电压值获取}==53.6
    ${电池电压低值获取}    获取web参数量    电池电压低阈值
    should be true    ${电池电压低值获取}==48
    Comment    ${站点名称获取}    获取web参数量    站点名称
    Comment    Should Be Equal    ${站点名称获取}    Site-X#9
    ${获取值}    获取web参数量    市电配置_1
    Should Be Equal    ${获取值}    无
    ${电池组容量值获取}    获取web参数量    电池组容量_1
    should be true    ${电池组容量值获取}==200
    ${CPU利用率高阈值获取}    获取web参数量    CPU利用率高阈值
    should be true    ${CPU利用率高阈值获取}==80
    [Teardown]    设置web设备参数量为默认值    交流电流高阈值    环境温度过高阈值    直流电压高阈值    电池电压低阈值    浮充电压    市电配置_1    电池组容量_1

修改告警属性参数值导入测试
    连接CSU
    ${交流电压高告警级别获取}    获取web参数量    交流电压高_1
    ${交流电压高告警干接点获取}    获取web参数量    交流电压高_1干接点
    ${多个整流器模块告警级别获取}    获取web参数量    多个整流器模块告警
    ${多个整流器模块告警干接点获取}    获取web参数量    多个整流器模块告警干接点
    ${温度高告警级别获取}    获取web参数量    电池温度高
    ${温度高告警干接点获取}    获取web参数量    电池温度高干接点
    ${直流电压高级别设置值}    获取web参数量    直流电压高
    ${直流电压高干接点设置值}    获取web参数量    直流电压高干接点
    ${环境高告警级别获取}    获取web参数量    环境温度高
    ${环境高告警干接点获取}    获取web参数量    环境温度高干接点
    ${级别设置值}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    ${干接点设置值}    获取web参数量    <<禁止所有告警干接点~0x1001030010001>>
    ${用户参数文件}    导出参数文件
    修改导出参数值    ${用户参数文件}\\alarm_attr_para.csv    交流电压高_level=1    交流电压高_relay=4    多个整流器模块告警_level=3    多个整流器模块告警_relay=3
    修改导出参数值    ${用户参数文件}\\alarm_attr_para.csv    电池温度高_level=1    电池温度高_relay=4    环境温度高_level=2    环境温度高_relay=5
    修改导出参数值    ${用户参数文件}\\alarm_attr_para.csv    直流电压高_level=1    直流电压高_relay=4    禁止所有告警_level=2    禁止所有告警_relay=6
    压缩文件夹    ${用户参数文件}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${用户参数文件}.zip
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    连接CSU
    ${交流电压高告警级别获取}    获取web参数量    交流电压高_1
    should be equal    ${交流电压高告警级别获取}    严重
    ${交流电压高告警干接点获取}    获取web参数量    交流电压高_1干接点
    should be true    ${交流电压高告警干接点获取}==4
    ${多个整流器模块告警级别获取}    获取web参数量    多个整流器模块告警
    should be equal    ${多个整流器模块告警级别获取}    次要
    ${多个整流器模块告警干接点获取}    获取web参数量    多个整流器模块告警干接点
    should be true    ${多个整流器模块告警干接点获取}==3
    ${温度高告警级别获取}    获取web参数量    电池温度高
    should be equal    ${温度高告警级别获取}    严重
    ${温度高告警干接点获取}    获取web参数量    电池温度高干接点
    should be true    ${温度高告警干接点获取}==4
    ${直流电压高级别设置值}    获取web参数量    直流电压高
    should be equal    ${直流电压高级别设置值}    严重
    ${直流电压高干接点设置值}    获取web参数量    直流电压高干接点
    should be true    ${直流电压高干接点设置值}==4
    ${环境高告警级别获取}    获取web参数量    环境温度高
    should be equal    ${环境高告警级别获取}    主要
    ${环境高告警干接点获取}    获取web参数量    环境温度高干接点
    should be true    ${环境高告警干接点获取}==5
    ${级别设置值}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    should be equal    ${级别设置值}    主要
    ${干接点设置值}    获取web参数量    <<禁止所有告警干接点~0x1001030010001>>
    should be true    ${干接点设置值}==6
    [Teardown]    #run keywords    设置web设备参数量为默认值    多个整流器模块告警    多个整流器模块告警干接点    电池温度高    电池温度高干接点    # 直流电压高    直流电压高干接点    环境温度高    环境温度高干接点    <<禁止所有告警~0x1001030010001>>    <<禁止所有告警干接点~0x1001030010001>>    # AND    设置web设备参数量为默认值    交流电压高_1    交流电压高_1干接点

告警属性参数值不合法导入测试
    连接CSU
    ${交流电压高告警级别获取}    获取web参数量    交流电压高_1
    ${交流电压高告警干接点获取}    获取web参数量    交流电压高_1干接点
    ${多个整流器模块告警级别获取}    获取web参数量    多个整流器模块告警
    ${多个整流器模块告警干接点获取}    获取web参数量    多个整流器模块告警干接点
    ${温度高告警级别获取}    获取web参数量    电池温度高
    ${温度高告警干接点获取}    获取web参数量    电池温度高干接点
    ${直流电压高级别设置值}    获取web参数量    直流电压高
    ${直流电压高干接点设置值}    获取web参数量    直流电压高干接点
    ${环境高告警级别获取}    获取web参数量    环境温度高
    ${环境高告警干接点获取}    获取web参数量    环境温度高干接点
    ${级别设置值}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    ${干接点设置值}    获取web参数量    <<禁止所有告警干接点~0x1001030010001>>
    ${用户参数文件}    导出参数文件
    修改导出参数值    ${用户参数文件}\\alarm_attr_para.csv    交流电压高_level=5    交流电压高_relay=9    多个整流器模块告警_level=7    多个整流器模块告警_relay=9
    修改导出参数值    ${用户参数文件}\\alarm_attr_para.csv    电池温度高_level=6    电池温度高_relay=10    环境温度高_level=8    环境温度高_relay=12
    修改导出参数值    ${用户参数文件}\\alarm_attr_para.csv    直流电压高_level=7    直流电压高_relay=11    禁止所有告警_level=9    禁止所有告警_relay=13
    压缩文件夹    ${用户参数文件}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${用户参数文件}.zip
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    @{历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain    @{历史事件内容1}[0]    导入 失败
    should not be true    ${导入结果}
    ${交流电压高告警级别获取new}    获取web参数量    交流电压高_1
    should be equal    ${交流电压高告警级别获取}    ${交流电压高告警级别获取new}
    ${交流电压高告警干接点获取new}    获取web参数量    交流电压高_1干接点
    should be true    ${交流电压高告警干接点获取}==${交流电压高告警干接点获取new}
    ${多个整流器模块告警级别获取new}    获取web参数量    多个整流器模块告警
    should be equal    ${多个整流器模块告警级别获取}    ${多个整流器模块告警级别获取new}
    ${多个整流器模块告警干接点获取new}    获取web参数量    多个整流器模块告警干接点
    should be true    ${多个整流器模块告警干接点获取}==${多个整流器模块告警干接点获取new}
    ${温度高告警级别获取new}    获取web参数量    电池温度高
    should be equal    ${温度高告警级别获取}    ${温度高告警级别获取new}
    ${温度高告警干接点获取new}    获取web参数量    电池温度高干接点
    should be true    ${温度高告警干接点获取}==${温度高告警干接点获取new}
    ${直流电压高级别设置值new}    获取web参数量    直流电压高
    should be equal    ${直流电压高级别设置值}    ${直流电压高级别设置值new}
    ${直流电压高干接点设置值new}    获取web参数量    直流电压高干接点
    should be true    ${直流电压高干接点设置值}==${直流电压高干接点设置值new}
    ${环境高告警级别获取new}    获取web参数量    环境温度高
    should be equal    ${环境高告警级别获取}    ${环境高告警级别获取new}
    ${环境高告警干接点获取new}    获取web参数量    环境温度高干接点
    should be true    ${环境高告警干接点获取}==${环境高告警干接点获取new}
    ${级别设置值new}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    should be equal    ${级别设置值}    ${级别设置值new}
    ${干接点设置值new}    获取web参数量    <<禁止所有告警干接点~0x1001030010001>>
    should be true    ${干接点设置值}==${干接点设置值new}
    [Teardown]    #设置web设备参数量为默认值    交流电压高_1    交流电压高_1干接点    多个整流器模块告警    多个整流器模块告警干接点    电池温度高    # 电池温度高干接点    直流电压高    直流电压高干接点    环境温度高    环境温度高干接点    <<禁止所有告警~0x1001030010001>>    # <<禁止所有告警干接点~0x1001030010001>>

删除告警属性参数值导入测试
    连接CSU
    ${交流电压低告警级别获取}    获取web参数量    交流电压低_1
    ${交流电压低告警干接点获取}    获取web参数量    交流电压低_1干接点
    ${多个整流器模块告警级别获取}    获取web参数量    多个整流器模块告警
    ${多个整流器模块告警干接点获取}    获取web参数量    多个整流器模块告警干接点
    ${温度低告警级别获取}    获取web参数量    电池温度低
    ${温度低告警干接点获取}    获取web参数量    电池温度低干接点
    ${直流电压低级别设置值}    获取web参数量    直流电压低
    ${直流电压低干接点设置值}    获取web参数量    直流电压低干接点
    ${环境低告警级别获取}    获取web参数量    环境温度低
    ${环境低告警干接点获取}    获取web参数量    环境温度低干接点
    ${级别设置值}    获取web参数量    MAC地址未设置
    ${干接点设置值}    获取web参数量    MAC地址未设置干接点
    ${用户参数文件}    导出参数文件
    删除指定导出参数值    ${用户参数文件}\\alarm_attr_para.csv    MAC地址未设置_0x1001030020001    电池温度低_0xb001030020001    交流电压低_0x3001030030001    多个整流器模块告警_0x8001030010001
    删除指定导出参数值    ${用户参数文件}\\alarm_attr_para.csv    直流电压低_0xa001030050001    环境温度低_0x9001030070001
    压缩文件夹    ${用户参数文件}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${用户参数文件}.zip
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    连接CSU
    ${交流电压低告警级别获取new}    获取web参数量    交流电压低_1
    should be equal    ${交流电压低告警级别获取}    ${交流电压低告警级别获取new}
    ${交流电压低告警干接点获取new}    获取web参数量    交流电压低_1干接点
    should be true    ${交流电压低告警干接点获取}==${交流电压低告警干接点获取new}
    ${多个整流器模块告警级别获取new}    获取web参数量    多个整流器模块告警
    should be equal    ${多个整流器模块告警级别获取}    ${多个整流器模块告警级别获取new}
    ${多个整流器模块告警干接点获取new}    获取web参数量    多个整流器模块告警干接点
    should be true    ${多个整流器模块告警干接点获取}==${多个整流器模块告警干接点获取new}
    ${温度低告警级别获取new}    获取web参数量    电池温度低
    should be equal    ${温度低告警级别获取}    ${温度低告警级别获取new}
    ${温度低告警干接点获取new}    获取web参数量    电池温度低干接点
    should be true    ${温度低告警干接点获取}==${温度低告警干接点获取new}
    ${直流电压低级别设置值new}    获取web参数量    直流电压低
    should be equal    ${直流电压低级别设置值}    ${直流电压低级别设置值new}
    ${直流电压低干接点设置值new}    获取web参数量    直流电压低干接点
    should be true    ${直流电压低干接点设置值new} ==${直流电压低干接点设置值}
    ${环境低告警级别获取new}    获取web参数量    环境温度低
    should be equal    ${环境低告警级别获取}    ${环境低告警级别获取new}
    ${环境低告警干接点获取new}    获取web参数量    环境温度低干接点
    should be true    ${环境低告警干接点获取}==${环境低告警干接点获取new}
    ${级别设置值new}    获取web参数量    MAC地址未设置
    should be equal    ${级别设置值}    ${级别设置值new}
    ${干接点设置值new}    获取web参数量    MAC地址未设置干接点
    should be true    ${干接点设置值}==${干接点设置值new}
    [Teardown]    #设置web设备参数量为默认值    交流电压低_1    交流电压低_1干接点    多个整流器模块告警    多个整流器模块告警干接点    电池温度低    # 电池温度低干接点    直流电压低    直流电压低干接点    环境温度低    环境温度低干接点    MAC地址未设置    #设置web设备参数量为默认值 | 交流电压低_1 | 交流电压低_1干接点 | 多个整流器模块告警 | 多个整流器模块告警干接点 | 电池温度低 | # 电池温度低干接点 | 直流电压低 | 直流电压低干接点 | 环境温度低 | 环境温度低干接点 | MAC地址未设置 | # MAC地址未设置干接点

删除修改告警属性参数值导入测试
    连接CSU
    ${交流电压低告警级别获取}    获取web参数量    交流电压低_1
    ${交流电压低告警干接点获取}    获取web参数量    交流电压低_1干接点
    ${多个整流器模块告警级别获取}    获取web参数量    多个整流器模块告警
    ${多个整流器模块告警干接点获取}    获取web参数量    多个整流器模块告警干接点
    ${温度低告警级别获取}    获取web参数量    电池温度低
    ${温度低告警干接点获取}    获取web参数量    电池温度低干接点
    ${直流电压低级别设置值}    获取web参数量    直流电压低
    ${直流电压低干接点设置值}    获取web参数量    直流电压低干接点
    ${环境低告警级别获取}    获取web参数量    环境温度低
    ${环境低告警干接点获取}    获取web参数量    环境温度低干接点
    ${级别设置值1}    获取web参数量    MAC地址未设置
    ${干接点设置值1}    获取web参数量    MAC地址未设置干接点
    ${交流电压高告警级别获取}    获取web参数量    交流电压高_1
    ${交流电压高告警干接点获取}    获取web参数量    交流电压高_1干接点
    ${温度高告警级别获取}    获取web参数量    电池温度高
    ${温度高告警干接点获取}    获取web参数量    电池温度高干接点
    ${直流电压高级别设置值}    获取web参数量    直流电压高
    ${直流电压高干接点设置值}    获取web参数量    直流电压高干接点
    ${环境高告警级别获取}    获取web参数量    环境温度高
    ${环境高告警干接点获取}    获取web参数量    环境温度高干接点
    ${级别设置值}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    ${干接点设置值}    获取web参数量    <<禁止所有告警干接点~0x1001030010001>>
    ${用户参数文件}    导出参数文件
    删除指定导出参数值    ${用户参数文件}\\alarm_attr_para.csv    MAC地址未设置_0x1001030020001    电池温度低_0xb001030020001    交流电压低_0x3001030030001    多个整流器模块告警_0x8001030010001
    删除指定导出参数值    ${用户参数文件}\\alarm_attr_para.csv    直流电压低_0xa001030050001    环境温度低_0x9001030070001
    修改导出参数值    ${用户参数文件}\\alarm_attr_para.csv    交流电压高_level=1    交流电压高_relay=4    多个整流器模块告警_level=3    多个整流器模块告警_relay=3
    修改导出参数值    ${用户参数文件}\\alarm_attr_para.csv    电池温度高_level=1    电池温度高_relay=4    环境温度高_level=2    环境温度高_relay=5
    修改导出参数值    ${用户参数文件}\\alarm_attr_para.csv    直流电压高_level=1    直流电压高_relay=4    禁止所有告警_level=2    禁止所有告警_relay=6
    压缩文件夹    ${用户参数文件}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${用户参数文件}.zip
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    连接CSU
    ${交流电压高告警级别获取}    获取web参数量    交流电压高_1
    should be equal    ${交流电压高告警级别获取}    严重
    ${交流电压高告警干接点获取}    获取web参数量    交流电压高_1干接点
    should be true    ${交流电压高告警干接点获取}==4
    ${多个整流器模块告警级别获取}    获取web参数量    多个整流器模块告警
    should be equal    ${多个整流器模块告警级别获取}    次要
    ${多个整流器模块告警干接点获取}    获取web参数量    多个整流器模块告警干接点
    should be true    ${多个整流器模块告警干接点获取}==3
    ${温度高告警级别获取}    获取web参数量    电池温度高
    should be equal    ${温度高告警级别获取}    严重
    ${温度高告警干接点获取}    获取web参数量    电池温度高干接点
    should be true    ${温度高告警干接点获取}==4
    ${直流电压高级别设置值}    获取web参数量    直流电压高
    should be equal    ${直流电压高级别设置值}    严重
    ${直流电压高干接点设置值}    获取web参数量    直流电压高干接点
    should be true    ${直流电压高干接点设置值}==4
    ${环境高告警级别获取}    获取web参数量    环境温度高
    should be equal    ${环境高告警级别获取}    主要
    ${环境高告警干接点获取}    获取web参数量    环境温度高干接点
    should be true    ${环境高告警干接点获取}==5
    ${级别设置值}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    should be equal    ${级别设置值}    主要
    ${干接点设置值}    获取web参数量    <<禁止所有告警干接点~0x1001030010001>>
    should be true    ${干接点设置值}==6
    ${交流电压低告警级别获取new}    获取web参数量    交流电压低_1
    should be equal    ${交流电压低告警级别获取}    ${交流电压低告警级别获取new}
    ${交流电压低告警干接点获取new}    获取web参数量    交流电压低_1干接点
    should be true    ${交流电压低告警干接点获取}==${交流电压低告警干接点获取new}
    ${温度低告警级别获取new}    获取web参数量    电池温度低
    should be equal    ${温度低告警级别获取}    ${温度低告警级别获取new}
    ${温度低告警干接点获取new}    获取web参数量    电池温度低干接点
    should be true    ${温度低告警干接点获取}==${温度低告警干接点获取new}
    ${直流电压低级别设置值new}    获取web参数量    直流电压低
    should be equal    ${直流电压低级别设置值}    ${直流电压低级别设置值new}
    ${直流电压低干接点设置值new}    获取web参数量    直流电压低干接点
    should be true    ${直流电压低干接点设置值new} ==${直流电压低干接点设置值}
    ${环境低告警级别获取new}    获取web参数量    环境温度低
    should be equal    ${环境低告警级别获取}    ${环境低告警级别获取new}
    ${环境低告警干接点获取new}    获取web参数量    环境温度低干接点
    should be true    ${环境低告警干接点获取}==${环境低告警干接点获取new}
    ${级别设置值new}    获取web参数量    MAC地址未设置
    should be equal    ${级别设置值1}    ${级别设置值new}
    ${干接点设置值new}    获取web参数量    MAC地址未设置干接点
    should be true    ${干接点设置值1}==${干接点设置值new}
    [Teardown]    #设置web设备参数量为默认值    交流电压高_1    交流电压高_1干接点    多个整流器模块告警    多个整流器模块告警干接点    电池温度高    # 电池温度高干接点    直流电压高    直流电压高干接点    环境温度高    环境温度高干接点    <<禁止所有告警~0x1001030010001>>    # <<禁止所有告警干接点~0x1001030010001>>

增加告警属性参数值导入测试
    连接CSU
    ${交流电压低告警级别获取}    获取web参数量    交流电压低_1
    ${交流电压低告警干接点获取}    获取web参数量    交流电压低_1干接点
    ${多个整流器模块告警级别获取}    获取web参数量    多个整流器模块告警
    ${多个整流器模块告警干接点获取}    获取web参数量    多个整流器模块告警干接点
    ${温度低告警级别获取}    获取web参数量    电池温度低
    ${温度低告警干接点获取}    获取web参数量    电池温度低干接点
    ${直流电压低级别设置值}    获取web参数量    直流电压低
    ${直流电压低干接点设置值}    获取web参数量    直流电压低干接点
    ${环境低告警级别获取}    获取web参数量    环境温度低
    ${环境低告警干接点获取}    获取web参数量    环境温度低干接点
    ${级别设置值}    获取web参数量    MAC地址未设置
    ${干接点设置值}    获取web参数量    MAC地址未设置干接点
    ${用户参数文件}    导出参数文件
    删除指定导出参数值    ${用户参数文件}\\alarm_attr_para.csv    MAC地址未设置_0x1001030020001    电池温度低_0xb001030020001    交流电压低_0x3001030030001    多个整流器模块告警_0x8001030010001
    删除指定导出参数值    ${用户参数文件}\\alarm_attr_para.csv    直流电压低_0xa001030050001    环境温度低_0x9001030070001
    插入指定导出参数值    ${用户参数文件}\\alarm_attr_para.csv    0x3001030030001,系统交流输入,交流电压低,1,0:屏蔽;1:严重;2:主要;3:次要;4:警告;,2,0:无;1:UIB_X2_DO1;2:UIB_X2_DO2;3:UIB_X2_DO3;4:UIB_X2_DO4;5:UIB_X1_DO5;6:UIB_X1_DO6;8:UIB_X1_DO8;
    插入指定导出参数值    ${用户参数文件}\\alarm_attr_para.csv    0xa001030050001,直流配电,直流电压低,3,0:屏蔽;1:严重;2:主要;3:次要;4:警告;,4,0:无;1:UIB_X2_DO1;2:UIB_X2_DO2;3:UIB_X2_DO3;4:UIB_X2_DO4;5:UIB_X1_DO5;6:UIB_X1_DO6;8:UIB_X1_DO8;
    压缩文件夹    ${用户参数文件}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${用户参数文件}.zip
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    ${交流电压低告警级别获取}    获取web参数量    交流电压低_1
    should be equal    ${交流电压低告警级别获取}    严重
    ${交流电压低告警干接点获取}    获取web参数量    交流电压低_1干接点
    should be true    ${交流电压低告警干接点获取}==2
    ${直流电压低级别设置值}    获取web参数量    直流电压低
    should be equal    ${直流电压低级别设置值}    次要
    ${直流电压低干接点设置值}    获取web参数量    直流电压低干接点
    should be true    ${直流电压低干接点设置值}==4
    [Teardown]    #设置web设备参数量为默认值    交流电压低_1    交流电压低_1干接点    多个整流器模块告警    多个整流器模块告警干接点    电池温度低    # 电池温度低干接点    直流电压低    直流电压低干接点    环境温度低    环境温度低干接点    MAC地址未设置

删除增加修改告警属性参数值导入测试
    连接CSU
    ${交流电压高告警级别获取}    获取web参数量    交流电压高_1
    ${交流电压高告警干接点获取}    获取web参数量    交流电压高_1干接点
    ${温度高告警级别获取}    获取web参数量    电池温度高
    ${温度高告警干接点获取}    获取web参数量    电池温度高干接点
    ${直流电压高级别设置值}    获取web参数量    直流电压高
    ${直流电压高干接点设置值}    获取web参数量    直流电压高干接点
    ${环境高告警级别获取}    获取web参数量    环境温度高
    ${环境高告警干接点获取}    获取web参数量    环境温度高干接点
    ${级别设置值}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    ${干接点设置值}    获取web参数量    <<禁止所有告警干接点~0x1001030010001>>
    ${用户参数文件}    导出参数文件
    删除指定导出参数值    ${用户参数文件}\\alarm_attr_para.csv    交流电压高_0x3001030040001    电池温度高_0xb001030010001    禁止所有告警_0x1001030010001
    删除指定导出参数值    ${用户参数文件}\\alarm_attr_para.csv    直流电压高_0xa001030040001    环境温度高_0x9001030060001
    插入指定导出参数值    ${用户参数文件}\\alarm_attr_para.csv    0x3001030040001,系统交流输入,交流电压高,3,0:屏蔽;1:严重;2:主要;3:次要;4:警告;,2,0:无;1:UIB_X2_DO1;2:UIB_X2_DO2;3:UIB_X2_DO3;4:UIB_X2_DO4;5:UIB_X1_DO5;6:UIB_X1_DO6;8:UIB_X1_DO8;
    插入指定导出参数值    ${用户参数文件}\\alarm_attr_para.csv    0xa001030040001,直流配电,直流电压高,3,0:屏蔽;1:严重;2:主要;3:次要;4:警告;,5,0:无;1:UIB_X2_DO1;2:UIB_X2_DO2;3:UIB_X2_DO3;4:UIB_X2_DO4;5:UIB_X1_DO5;6:UIB_X1_DO6;8:UIB_X1_DO8;
    插入指定导出参数值    ${用户参数文件}\\alarm_attr_para.csv    0xb001030010001,电池,电池温度高,3,0:屏蔽;1:严重;2:主要;3:次要;4:警告;,5,0:无;1:UIB_X2_DO1;2:UIB_X2_DO2;3:UIB_X2_DO3;4:UIB_X2_DO4;5:UIB_X1_DO5;6:UIB_X1_DO6;8:UIB_X1_DO8;
    插入指定导出参数值    ${用户参数文件}\\alarm_attr_para.csv    0x1001030010001,CSU,禁止所有告警,3,0:屏蔽;1:严重;2:主要;3:次要;4:警告;,5,0:无;1:UIB_X2_DO1;2:UIB_X2_DO2;3:UIB_X2_DO3;4:UIB_X2_DO4;5:UIB_X1_DO5;6:UIB_X1_DO6;8:UIB_X1_DO8;
    插入指定导出参数值    ${用户参数文件}\\alarm_attr_para.csv    0x9001030060001,系统运行环境,环境温度高,3,0:屏蔽;1:严重;2:主要;3:次要;4:警告;,5,0:无;1:UIB_X2_DO1;2:UIB_X2_DO2;3:UIB_X2_DO3;4:UIB_X2_DO4;5:UIB_X1_DO5;6:UIB_X1_DO6;8:UIB_X1_DO8;
    修改导出参数值    ${用户参数文件}\\alarm_attr_para.csv    交流电压高_level=1    交流电压高_relay=4
    修改导出参数值    ${用户参数文件}\\alarm_attr_para.csv    电池温度高_level=1    电池温度高_relay=4    环境温度高_level=2    环境温度高_relay=5
    修改导出参数值    ${用户参数文件}\\alarm_attr_para.csv    直流电压高_level=1    直流电压高_relay=4    禁止所有告警_level=2    禁止所有告警_relay=6
    压缩文件夹    ${用户参数文件}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${用户参数文件}.zip
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    ${交流电压高告警级别获取}    获取web参数量    交流电压高_1
    should be equal    ${交流电压高告警级别获取}    严重
    ${交流电压高告警干接点获取}    获取web参数量    交流电压高_1干接点
    should be true    ${交流电压高告警干接点获取}==4
    ${温度高告警级别获取}    获取web参数量    电池温度高
    should be equal    ${温度高告警级别获取}    严重
    ${温度高告警干接点获取}    获取web参数量    电池温度高干接点
    should be true    ${温度高告警干接点获取}==4
    ${直流电压高级别设置值}    获取web参数量    直流电压高
    should be equal    ${直流电压高级别设置值}    严重
    ${直流电压高干接点设置值}    获取web参数量    直流电压高干接点
    should be true    ${直流电压高干接点设置值}==4
    ${环境高告警级别获取}    获取web参数量    环境温度高
    should be equal    ${环境高告警级别获取}    主要
    ${环境高告警干接点获取}    获取web参数量    环境温度高干接点
    should be true    ${环境高告警干接点获取}==5
    ${级别设置值}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    should be equal    ${级别设置值}    主要
    ${干接点设置值}    获取web参数量    <<禁止所有告警干接点~0x1001030010001>>
    should be true    ${干接点设置值}==6
    [Teardown]    #设置web设备参数量为默认值    交流电压高    交流电压高干接点    多个整流器模块告警    多个整流器模块告警干接点    电池温度高    # 电池温度高干接点    直流电压高    直流电压高干接点    环境温度高    环境温度高干接点    <<禁止所有告警~0x1001030010001>>    # <<禁止所有告警干接点~0x1001030010001>>

修改显示属性参数值导入测试
    连接CSU
    @{channel_config}    获取显示属性配置    CPU利用率    模拟量
    @{channel_config}    获取显示属性配置    <<禁止所有告警~0x1001040010001>>    控制量
    @{channel_config}    获取显示属性配置    站点名称    参数量
    @{channel_config}    获取显示属性配置    相电压UL1    模拟量
    @{channel_config}    获取显示属性配置    交流防雷器状态    数字量
    @{channel_config}    获取显示属性配置    市电输入状态    数字量
    @{channel_config}    获取显示属性配置    整流器输出电压    模拟量
    @{channel_config}    获取显示属性配置    电池组总电流    模拟量
    @{channel_config}    获取显示属性配置    直流电压    模拟量
    ${用户参数文件}    导出参数文件
    修改导出参数值    ${用户参数文件}\\show_attr_para.csv    禁止所有告警_控制量_web=1    禁止所有告警_控制量_gui=1    CPU利用率_模拟量_web=1
    修改导出参数值    ${用户参数文件}\\show_attr_para.csv    站点名称_参数量_web=1    相电压UL1_模拟量_web=1    相电压UL1_模拟量_gui=1    交流防雷器状态_数字量_gui=1
    修改导出参数值    ${用户参数文件}\\show_attr_para.csv    市电输入状态_数字量_web=1    市电输入状态_数字量_gui=1    整流器输出电压_模拟量_web=1    整流器输出电压_模拟量_gui=1
    修改导出参数值    ${用户参数文件}\\show_attr_para.csv    电池组总电流_模拟量_web=1    电池组总电流_模拟量_gui=1    直流电压_模拟量_web=1    直流电压_模拟量_gui=1
    压缩文件夹    ${用户参数文件}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${用户参数文件}.zip
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    @{channel_config}    获取显示属性配置    CPU利用率    模拟量
    should be equal    @{channel_config}[0]    OFF
    @{channel_config}    获取显示属性配置    <<禁止所有告警~0x1001040010001>>    控制量
    should be equal    @{channel_config}[0]    OFF
    should be equal    @{channel_config}[1]    OFF
    @{channel_config}    获取显示属性配置    站点名称    参数量
    should be equal    @{channel_config}[0]    OFF
    @{channel_config}    获取显示属性配置    相电压UL1    模拟量
    should be equal    @{channel_config}[0]    OFF
    should be equal    @{channel_config}[1]    OFF
    @{channel_config}    获取显示属性配置    交流防雷器状态    数字量
    should be equal    @{channel_config}[1]    OFF
    @{channel_config}    获取显示属性配置    市电输入状态    数字量
    should be equal    @{channel_config}[0]    OFF
    should be equal    @{channel_config}[1]    OFF
    @{channel_config}    获取显示属性配置    整流器输出电压    模拟量
    should be equal    @{channel_config}[0]    OFF
    should be equal    @{channel_config}[1]    OFF
    @{channel_config}    获取显示属性配置    电池组总电流    模拟量
    should be equal    @{channel_config}[0]    OFF
    should be equal    @{channel_config}[1]    OFF
    @{channel_config}    获取显示属性配置    直流电压    模拟量
    should be equal    @{channel_config}[0]    OFF
    should be equal    @{channel_config}[1]    OFF

显示属性参数值不合法导入测试
    连接CSU
    @{channel_config1}    获取显示属性配置    CPU利用率    模拟量
    @{channel_config2}    获取显示属性配置    <<禁止所有告警~0x1001040010001>>    控制量
    @{channel_config3}    获取显示属性配置    站点名称    参数量
    @{channel_config4}    获取显示属性配置    相电压UL1    模拟量
    @{channel_config5}    获取显示属性配置    交流防雷器状态    数字量
    @{channel_config6}    获取显示属性配置    市电输入状态    数字量
    @{channel_config7}    获取显示属性配置    整流器输出电压    模拟量
    @{channel_config8}    获取显示属性配置    电池组总电流    模拟量
    @{channel_config9}    获取显示属性配置    直流电压    模拟量
    ${用户参数文件}    导出参数文件
    修改导出参数值    ${用户参数文件}\\show_attr_para.csv    禁止所有告警_控制量_web=3    禁止所有告警_控制量_gui=4    CPU利用率_模拟量_web=2
    修改导出参数值    ${用户参数文件}\\show_attr_para.csv    站点名称_参数量_web=4    相电压UL1_模拟量_web=3    相电压UL1_模拟量_gui=2    交流防雷器状态_数字量_gui=2
    修改导出参数值    ${用户参数文件}\\show_attr_para.csv    市电输入状态_数字量_web=5    市电输入状态_数字量_gui=6    整流器输出电压_模拟量_web=1    整流器输出电压_模拟量_gui=1
    修改导出参数值    ${用户参数文件}\\show_attr_para.csv    电池组总电流_模拟量_web=7    电池组总电流_模拟量_gui=8    直流电压_模拟量_web=9    直流电压_模拟量_gui=10
    压缩文件夹    ${用户参数文件}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${用户参数文件}.zip
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    @{历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain    @{历史事件内容1}[0]    导入 失败
    should not be true    ${导入结果}
    @{channel_config}    获取显示属性配置    CPU利用率    模拟量
    should be equal    @{channel_config}[0]    @{channel_config1}[0]
    @{channel_config}    获取显示属性配置    <<禁止所有告警~0x1001040010001>>    控制量
    should be equal    @{channel_config}[0]    @{channel_config2}[0]
    should be equal    @{channel_config}[1]    @{channel_config2}[1]
    @{channel_config}    获取显示属性配置    站点名称    参数量
    should be equal    @{channel_config}[0]    @{channel_config3}[0]
    @{channel_config}    获取显示属性配置    相电压UL1    模拟量
    should be equal    @{channel_config}[0]    @{channel_config4}[0]
    should be equal    @{channel_config}[1]    @{channel_config4}[1]
    @{channel_config}    获取显示属性配置    交流防雷器状态    数字量
    should be equal    @{channel_config}[1]    @{channel_config5}[1]
    @{channel_config}    获取显示属性配置    市电输入状态    数字量
    should be equal    @{channel_config}[0]    @{channel_config6}[0]
    should be equal    @{channel_config}[1]    @{channel_config6}[1]
    @{channel_config}    获取显示属性配置    整流器输出电压    模拟量
    should be equal    @{channel_config}[0]    @{channel_config7}[0]
    should be equal    @{channel_config}[1]    @{channel_config7}[1]
    @{channel_config}    获取显示属性配置    电池组总电流    模拟量
    should be equal    @{channel_config}[0]    @{channel_config8}[0]
    should be equal    @{channel_config}[1]    @{channel_config8}[1]
    @{channel_config}    获取显示属性配置    直流电压    模拟量
    should be equal    @{channel_config}[0]    @{channel_config9}[0]
    should be equal    @{channel_config}[1]    @{channel_config9}[1]

删除显示属性参数值导入测试
    连接CSU
    @{channel_config1}    获取显示属性配置    CPU利用率    模拟量
    @{channel_config2}    获取显示属性配置    <<禁止所有告警~0x1001040010001>>    控制量
    @{channel_config3}    获取显示属性配置    站点名称    参数量
    @{channel_config4}    获取显示属性配置    相电压UL1    模拟量
    @{channel_config5}    获取显示属性配置    交流防雷器状态    数字量
    @{channel_config6}    获取显示属性配置    市电输入状态    数字量
    @{channel_config7}    获取显示属性配置    整流器输出电压    模拟量
    @{channel_config8}    获取显示属性配置    电池组总电流    模拟量
    @{channel_config9}    获取显示属性配置    直流电压    模拟量
    ${用户参数文件}    导出参数文件
    删除指定导出参数值    ${用户参数文件}\\show_attr_para.csv    禁止所有告警_0x1001040010001    CPU利用率_0x1001010010001    站点名称_0x2001050060001    相电压UL1_0x3001010030001
    删除指定导出参数值    ${用户参数文件}\\show_attr_para.csv    交流防雷器状态_0x4001020030001    市电输入状态_0x5001020010001    电池组总电流_0xc001010010001    直流电压_0xd001010010001
    压缩文件夹    ${用户参数文件}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${用户参数文件}.zip
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    @{channel_config}    获取显示属性配置    CPU利用率    模拟量
    should be equal    @{channel_config}[0]    @{channel_config1}[0]
    @{channel_config}    获取显示属性配置    <<禁止所有告警~0x1001040010001>>    控制量
    should be equal    @{channel_config}[0]    @{channel_config2}[0]
    should be equal    @{channel_config}[1]    @{channel_config2}[1]
    @{channel_config}    获取显示属性配置    站点名称    参数量
    should be equal    @{channel_config}[0]    @{channel_config3}[0]
    @{channel_config}    获取显示属性配置    相电压UL1    模拟量
    should be equal    @{channel_config}[0]    @{channel_config4}[0]
    should be equal    @{channel_config}[1]    @{channel_config4}[1]
    @{channel_config}    获取显示属性配置    交流防雷器状态    数字量
    should be equal    @{channel_config}[1]    @{channel_config5}[1]
    @{channel_config}    获取显示属性配置    市电输入状态    数字量
    should be equal    @{channel_config}[0]    @{channel_config6}[0]
    should be equal    @{channel_config}[1]    @{channel_config6}[1]
    @{channel_config}    获取显示属性配置    整流器输出电压    模拟量
    should be equal    @{channel_config}[0]    @{channel_config7}[0]
    should be equal    @{channel_config}[1]    @{channel_config7}[1]
    @{channel_config}    获取显示属性配置    电池组总电流    模拟量
    should be equal    @{channel_config}[0]    @{channel_config8}[0]
    should be equal    @{channel_config}[1]    @{channel_config8}[1]
    @{channel_config}    获取显示属性配置    直流电压    模拟量
    should be equal    @{channel_config}[0]    @{channel_config9}[0]
    should be equal    @{channel_config}[1]    @{channel_config9}[1]

删除修改显示属性参数值导入测试
    连接CSU
    @{channel_config1}    获取显示属性配置    CPU利用率    模拟量
    @{channel_config2}    获取显示属性配置    <<禁止所有告警~0x1001040010001>>    控制量
    @{channel_config3}    获取显示属性配置    站点名称    参数量
    @{channel_config4}    获取显示属性配置    相电压UL1    模拟量
    @{channel_config5}    获取显示属性配置    交流防雷器状态    数字量
    @{channel_config6}    获取显示属性配置    市电输入状态    数字量
    @{channel_config7}    获取显示属性配置    整流器输出电压    模拟量
    @{channel_config8}    获取显示属性配置    电池组总电流    模拟量
    @{channel_config9}    获取显示属性配置    直流电压    模拟量
    ${用户参数文件}    导出参数文件
    删除指定导出参数值    ${用户参数文件}\\show_attr_para.csv    禁止所有告警_0x1001040010001    CPU利用率_0x1001010010001    站点名称_0x2001050060001    相电压UL1_0x3001010030001
    修改导出参数值    ${用户参数文件}\\show_attr_para.csv    市电输入状态_数字量_web=0    市电输入状态_数字量_gui=0    整流器输出电压_模拟量_web=1    整流器输出电压_模拟量_gui=1
    修改导出参数值    ${用户参数文件}\\show_attr_para.csv    电池组总电流_模拟量_web=0    电池组总电流_模拟量_gui=0    直流电压_模拟量_web=0    直流电压_模拟量_gui=0
    压缩文件夹    ${用户参数文件}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${用户参数文件}.zip
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    @{channel_config}    获取显示属性配置    市电输入状态    数字量
    should be equal    @{channel_config}[0]    ON
    should be equal    @{channel_config}[1]    ON
    @{channel_config}    获取显示属性配置    整流器输出电压    模拟量
    should be equal    @{channel_config}[0]    @{channel_config7}[0]
    should be equal    @{channel_config}[1]    @{channel_config7}[1]
    @{channel_config}    获取显示属性配置    电池组总电流    模拟量
    should be equal    @{channel_config}[0]    ON
    should be equal    @{channel_config}[1]    ON
    @{channel_config}    获取显示属性配置    直流电压    模拟量
    should be equal    @{channel_config}[0]    ON
    should be equal    @{channel_config}[1]    ON

增加显示属性参数值导入测试
    连接CSU
    @{channel_config1}    获取显示属性配置    CPU利用率    模拟量
    @{channel_config2}    获取显示属性配置    <<禁止所有告警~0x1001040010001>>    控制量
    @{channel_config3}    获取显示属性配置    站点名称    参数量
    @{channel_config4}    获取显示属性配置    相电压UL1    模拟量
    @{channel_config5}    获取显示属性配置    交流防雷器状态    数字量
    @{channel_config6}    获取显示属性配置    市电输入状态    数字量
    @{channel_config7}    获取显示属性配置    整流器输出电压    模拟量
    @{channel_config8}    获取显示属性配置    电池组总电流    模拟量
    @{channel_config9}    获取显示属性配置    直流电压    模拟量
    ${用户参数文件}    导出参数文件
    删除指定导出参数值    ${用户参数文件}\\show_attr_para.csv    禁止所有告警_0x1001040010001    CPU利用率_0x1001010010001    站点名称_0x2001050060001    相电压UL1_0x3001010030001
    删除指定导出参数值    ${用户参数文件}\\show_attr_para.csv    交流防雷器状态_0x4001020030001    市电输入状态_0x5001020010001    电池组总电流_0xc001010010001    直流电压_0xd001010010001
    插入指定导出参数值    ${用户参数文件}\\show_attr_para.csv    0x1001010010001,CSU,CPU利用率,1,1,模拟量,0:显示;1:不显示;    0x1001040010001,CSU,禁止所有告警,0,0,控制量,0:显示;1:不显示;    0x3001010030001,系统交流输入,相电压UL1,0,0,模拟量,0:显示;1:不显示;
    插入指定导出参数值    ${用户参数文件}\\show_attr_para.csv    0x4001020030001,交流配电,交流防雷器状态,0,0,数字量,0:显示;1:不显示;    0x5001020010001,市电,市电输入状态,0,0,数字量,0:显示;1:不显示;    0x2001010040001,能源系统,电池组总电流,0,0,模拟量,0:显示;1:不显示;    0x2001010010001,能源系统,直流电压,0,0,模拟量,0:显示;1:不显示;
    压缩文件夹    ${用户参数文件}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${用户参数文件}.zip
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    @{channel_config}    获取显示属性配置    CPU利用率    模拟量
    should be equal    @{channel_config}[0]    OFF
    should be equal    @{channel_config}[1]    OFF
    @{channel_config}    获取显示属性配置    <<禁止所有告警~0x1001040010001>>    控制量
    should be equal    @{channel_config}[0]    ON
    should be equal    @{channel_config}[1]    ON
    Comment    @{channel_config}    获取显示属性配置    站点名称    参数量
    Comment    should be equal    @{channel_config}[0]    ON
    Comment    should be equal    @{channel_config}[1]    ON
    @{channel_config}    获取显示属性配置    相电压UL1    模拟量
    should be equal    @{channel_config}[0]    ON
    should be equal    @{channel_config}[1]    ON
    @{channel_config}    获取显示属性配置    交流防雷器状态    数字量
    should be equal    @{channel_config}[1]    ON
    should be equal    @{channel_config}[1]    ON
    @{channel_config}    获取显示属性配置    市电输入状态    数字量
    should be equal    @{channel_config}[0]    ON
    should be equal    @{channel_config}[1]    ON
    @{channel_config}    获取显示属性配置    整流器输出电压    模拟量
    should be equal    @{channel_config}[0]    @{channel_config7}[0]
    should be equal    @{channel_config}[1]    @{channel_config7}[1]
    @{channel_config}    获取显示属性配置    电池组总电流    模拟量
    should be equal    @{channel_config}[0]    ON
    should be equal    @{channel_config}[1]    ON
    @{channel_config}    获取显示属性配置    直流电压    模拟量
    should be equal    @{channel_config}[0]    ON
    should be equal    @{channel_config}[1]    ON

删除增加修改显示属性参数值导入测试
    连接CSU
    @{channel_config1}    获取显示属性配置    CPU利用率    模拟量
    @{channel_config2}    获取显示属性配置    <<禁止所有告警~0x1001040010001>>    控制量
    @{channel_config3}    获取显示属性配置    站点名称    参数量
    @{channel_config4}    获取显示属性配置    相电压UL1    模拟量
    @{channel_config5}    获取显示属性配置    交流防雷器状态    数字量
    @{channel_config6}    获取显示属性配置    市电输入状态    数字量
    @{channel_config7}    获取显示属性配置    整流器输出电压    模拟量
    @{channel_config8}    获取显示属性配置    电池组总电流    模拟量
    @{channel_config9}    获取显示属性配置    直流电压    模拟量
    ${用户参数文件}    导出参数文件
    删除指定导出参数值    ${用户参数文件}\\show_attr_para.csv    禁止所有告警_0x1001040010001    CPU利用率_0x1001010010001    相电压UL1_0x3001010030001
    删除指定导出参数值    ${用户参数文件}\\show_attr_para.csv    交流防雷器状态_0x4001020030001    市电输入状态_0x5001020010001    电池组总电流_0xc001010010001    直流电压_0xd001010010001
    插入指定导出参数值    ${用户参数文件}\\show_attr_para.csv    0x1001010010001,CSU,CPU利用率,1,1,模拟量,0:显示;1:不显示;    0x1001040010001,CSU,禁止所有告警,0,0,控制量,0:显示;1:不显示;    0x3001010030001,系统交流输入,相电压UL1,0,0,模拟量,0:显示;1:不显示;
    插入指定导出参数值    ${用户参数文件}\\show_attr_para.csv    0x4001020030001,交流配电,交流防雷器状态,0,0,数字量,0:显示;1:不显示;    0x5001020010001,市电,市电输入状态,0,0,数字量,0:显示;1:不显示;    0x2001010040001,能源系统,电池组总电流,0,0,模拟量,0:显示;1:不显示;    0x2001010010001,能源系统,直流电压,0,0,模拟量,0:显示;1:不显示;
    修改导出参数值    ${用户参数文件}\\show_attr_para.csv    市电输入状态_数字量_web=0    市电输入状态_数字量_gui=1    整流器输出电压_模拟量_web=1    整流器输出电压_模拟量_gui=1
    修改导出参数值    ${用户参数文件}\\show_attr_para.csv    电池组总电流_模拟量_web=1    电池组总电流_模拟量_gui=1    直流电压_模拟量_web=1    直流电压_模拟量_gui=0
    压缩文件夹    ${用户参数文件}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${用户参数文件}.zip
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    @{channel_config}    获取显示属性配置    CPU利用率    模拟量
    should be equal    @{channel_config}[0]    OFF
    should be equal    @{channel_config}[1]    OFF
    @{channel_config}    获取显示属性配置    <<禁止所有告警~0x1001040010001>>    控制量
    should be equal    @{channel_config}[0]    ON
    should be equal    @{channel_config}[1]    ON
    Comment    @{channel_config}    获取显示属性配置    站点名称    参数量
    Comment    should be equal    @{channel_config}[0]    ON
    Comment    should be equal    @{channel_config}[1]    ON
    @{channel_config}    获取显示属性配置    相电压UL1    模拟量
    should be equal    @{channel_config}[0]    ON
    should be equal    @{channel_config}[1]    ON
    @{channel_config}    获取显示属性配置    交流防雷器状态    数字量
    should be equal    @{channel_config}[1]    ON
    should be equal    @{channel_config}[1]    ON
    @{channel_config}    获取显示属性配置    市电输入状态    数字量
    should be equal    @{channel_config}[0]    ON
    should be equal    @{channel_config}[1]    OFF
    @{channel_config}    获取显示属性配置    整流器输出电压    模拟量
    should be equal    @{channel_config}[0]    @{channel_config7}[0]
    should be equal    @{channel_config}[1]    @{channel_config7}[1]
    @{channel_config}    获取显示属性配置    电池组总电流    模拟量
    should be equal    @{channel_config}[0]    OFF
    should be equal    @{channel_config}[1]    OFF
    @{channel_config}    获取显示属性配置    直流电压    模拟量
    should be equal    @{channel_config}[0]    OFF
    should be equal    @{channel_config}[1]    ON
    ${channel_config}    显示属性配置    市电输入状态    数字量    ON    ON
    @{channel_config}    获取显示属性配置    市电输入状态    数字量
    should be equal    @{channel_config}[0]    ON
    should be equal    @{channel_config}[1]    ON
    ${channel_config}    显示属性配置    电池组总电流    模拟量    ON    ON
    @{channel_config}    获取显示属性配置    电池组总电流    模拟量
    should be equal    @{channel_config}[0]    ON
    should be equal    @{channel_config}[1]    ON
    ${channel_config}    显示属性配置    直流电压    模拟量    ON    ON
    @{channel_config}    获取显示属性配置    直流电压    模拟量
    should be equal    @{channel_config}[0]    ON
    should be equal    @{channel_config}[1]    ON
