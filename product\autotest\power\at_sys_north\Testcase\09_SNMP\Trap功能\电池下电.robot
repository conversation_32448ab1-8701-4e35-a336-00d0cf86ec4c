*** Settings ***
Suite Setup       Run keywords     连接CSU
...               AND     备份参数并导入电池下电条件下参数文件
Suite Teardown    Run Keywords    导入参数文件压缩包    ${备份参数压缩文件名称}
...               AND     删除文件    download/${备份参数压缩文件名称}
Default Tags      T1-1
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
snmp_0008_电池下电分路断
    [Setup]
    实时告警刷新完成
    ${级别设置值}    获取web参数量    电池下电分路断_1
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池下电分路断_1    主要
    wait until keyword succeeds    1m    1    判断告警不存在    电池下电分路断
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    模拟数字量告警    下电告警    ON
    FOR    ${分路序号}    IN RANGE    4    1    -1
        wait until keyword succeeds    10m    1    判断告警存在    电池下电分路断_${分路序号}
        sleep    5
        ${snmp英文名}    获取snmp重构告警英文名    电池下电分路断_${分路序号}    直流配电
        ${告警产生}    判断Trap告警产生    ${snmp英文名}
        should be true    ${告警产生}
    END
    模拟数字量告警    下电告警    OFF
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池下电分路断
    FOR    ${分路序号}    IN RANGE    4    1    -1
        ${snmp英文名}    获取snmp重构告警英文名    电池下电分路断_${分路序号}    直流配电
        ${告警产生}    判断Trap告警消失    ${snmp英文名}
        should be true    ${告警产生}
    END
    [Teardown]    run keywords    设置web设备参数量为默认值    电池下电分路断_1
    ...    AND    模拟数字量告警    下电告警    OFF

snmp_0008_电池下电扩展分路断
    [Setup]
    实时告警刷新完成
    ${级别设置值}    获取web参数量    电池下电扩展分路断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池下电扩展分路断    主要
    wait until keyword succeeds    1m    1    判断告警不存在    电池下电扩展分路断
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    模拟数字量告警    下电告警    ON
    wait until keyword succeeds    1m    1    判断告警存在    电池下电扩展分路断
    sleep    5
    ${snmp英文名}    获取snmp单个告警英文名    电池下电扩展分路断
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    模拟数字量告警    下电告警    OFF
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池下电扩展分路断
    ${snmp英文名}    获取snmp单个告警英文名    电池下电扩展分路断
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    run keywords    设置web设备参数量为默认值    电池下电扩展分路断
    ...    AND    模拟数字量告警    下电告警    OFF