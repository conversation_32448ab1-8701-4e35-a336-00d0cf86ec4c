*** Settings ***
Suite Setup
Suite Teardown
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_获取智能空开参数量
    ${能源网管数据}    能源网管协议_获取数据    获取智能空开参数量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取智能空开参数量
    Should Be True    ${数据对比结果}

power_sm_0002_设置智能空开参数量
    [Setup]    Run Keywords    设置子工具个数    DMU_IntelAirSwit    2
    ...    AND    设置子工具值    DMU_IntelAirSwit    all    建链    空开额定电流    500
    ...    AND    设置web控制量    RS485总线设备统计
    ...    AND    sleep    5min
    ...    AND    设置WEB参数量    <<智能空开下电使能~0x2c001050010001>>    允许
    ...    AND    设置WEB参数量    <<智能空开定时下电使能~0x2c001050110001>>    允许
    ...    AND    设置WEB参数量    <<智能空开免责下电使能~0x2c001050160001>>    允许
    ...    AND    设置WEB参数量    <<智能空开过流保护使能~0x2c001050040001>>    允许

    ${能源网管数据}    能源网管协议_设置单个参数    设置智能空开参数    ${BOARD_IP}    ${SSH}
    ${对比结果}    能源网管协议_批量对比参数设置    ${能源网管数据}    设置智能空开参数
    Should Be True    ${对比结果}
    [Teardown]    Run Keywords    设置子工具个数    DMU_IntelAirSwit    ${SSW最大数目}
    ...    AND    设置子工具值    DMU_IntelAirSwit    all    建链    空开额定电流    125
    ...    AND    设置web控制量    RS485总线设备统计
    ...    AND    等待子设备工作正常    ${SSW最大数目}    智能空开通讯状态

power_sm_0003_获取智能空开统计量
    ${能源网管数据}    能源网管协议_获取数据    获取智能空开统计量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取智能空开统计量
    Should Be True    ${数据对比结果}

power_sm_0004_设置智能空开控制量
    ${sheet_name}    Set Variable    设置智能空开控制量
    ${能源网管数据}    能源网管协议_设置控制量    ${sheet_name}    ${BOARD_IP}    ${SSH}
    ${对比结果}    能源网管协议_批量对比参数设置    ${能源网管数据}    设置智能空开控制量
    Should Be True    ${对比结果}
    [Teardown]    自研智能空开控制后置条件