*** Settings ***
Default Tags      trap
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
snmp_0002_交流电表通讯断告警
    连接CSU
    实时告警刷新完成
    ###电表2告警
    wait until keyword succeeds    1m    1    设置web参数量    交流电表通讯断告警    主要
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    控制子工具运行停止    DMU_aemb    关闭
    wait until keyword succeeds    5min    1    判断告警存在    交流电表通讯断告警-1
    #产生
    ${snmp英文名2}    获取snmp单个告警英文名    交流电表通讯断告警_1
    ${告警产生2}    判断Trap告警产生    ${snmp英文名2}
    should be true    ${告警产生2}
    #消失
    控制子工具运行停止    DMU_aemb    启动
    wait until keyword succeeds    5min    1    判断告警不存在    交流电表通讯断告警
    ${告警消失}    判断Trap告警消失    ${snmp英文名2}
    should be true    ${告警消失}
    [Teardown]    Run Keywords    控制子工具运行停止    DMU_aemb    启动
    ...    AND    sleep    3min
