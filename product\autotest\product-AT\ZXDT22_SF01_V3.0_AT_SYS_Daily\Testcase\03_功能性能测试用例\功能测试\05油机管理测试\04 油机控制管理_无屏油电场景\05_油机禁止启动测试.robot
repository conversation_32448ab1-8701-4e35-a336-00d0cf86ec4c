*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
油机禁止运行测试
    油机管理初始化
    Wait Until Keyword Succeeds    10    1    设置系统时间    2021-01-29 8:00:00
    ${最短运行时间设置值}    获取web参数量    油机最短运行时间
    ${默认值}    获取web参数上下限范围    油机最短运行时间
    run keyword if    ${最短运行时间设置值} != @{默认值}[0]    Wait Until Keyword Succeeds    10    1    设置web参数量    油机最短运行时间    @{默认值}[0]
    ${最短运行时间设置值}    获取web参数量    油机最短运行时间
    Wait Until Keyword Succeeds    20    1    设置web控制量    油机开启
    run keyword and ignore error    wait until keyword succeeds    20    1    判断告警不存在    油机异常告警
    Wait Until Keyword Succeeds    4m    1    获取干接点状态直到变化为    ${油机干接点状态}    0
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    运行
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机禁止运行使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机禁止运行起始时刻_1    10:00
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机禁止运行终止时刻_1    10:20
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机禁止运行起始时刻_2    11:00
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机禁止运行终止时刻_2    12:20
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机禁止运行起始时刻_3    13:00
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机禁止运行终止时刻_3    14:20
    Wait Until Keyword Succeeds    4m    1    信号量数据值不为    油机状态    停止
    #起始时刻1
    ${起始时间}    获取web参数量    油机禁止运行起始时刻_1
    ${当前系统时间}    获取系统时间
    ${当前系统时间}    split string    ${当前系统时间}
    ${油机启动时间}    catenate    SEPARATOR=    ${起始时间}    :00
    ${设置系统时间}    catenate    @{当前系统时间}[0]    ${起始时间}
    ${油机启动时间0}    Subtract Time From Date    ${设置系统时间}    5s    #比1:00:00差5s
    Wait Until Keyword Succeeds    10    1    设置系统时间    ${油机启动时间0}
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    停止
    Wait Until Keyword Succeeds    4m    1    获取干接点状态直到变化为    ${油机干接点状态}    1    #干接点动作为1（断开），恢复为0（闭合）
    sleep    1m
    关闭交流源输出
    sleep    1m
    Wait Until Keyword Succeeds    20    1    设置失败的web控制量    油机开启
    ${起始时间}    获取web参数量    油机禁止运行终止时刻_1
    ${当前系统时间}    获取系统时间
    ${当前系统时间}    split string    ${当前系统时间}
    ${油机启动时间}    catenate    SEPARATOR=    ${起始时间}    :00
    ${设置系统时间}    catenate    @{当前系统时间}[0]    ${起始时间}
    ${油机启动时间0}    Subtract Time From Date    ${设置系统时间}    5s    #比1:00:00差5s
    Wait Until Keyword Succeeds    10    1    设置系统时间    ${油机启动时间0}
    Wait Until Keyword Succeeds    20    1    设置web控制量    油机开启
    run keyword and ignore error    wait until keyword succeeds    20    1    判断告警不存在    油机异常告警
    Wait Until Keyword Succeeds    4m    1    获取干接点状态直到变化为    ${油机干接点状态}    0
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    运行
    #起始时刻2
    ${起始时间}    获取web参数量    油机禁止运行起始时刻_2
    ${当前系统时间}    获取系统时间
    ${当前系统时间}    split string    ${当前系统时间}
    ${油机启动时间}    catenate    SEPARATOR=    ${起始时间}    :00
    ${设置系统时间}    catenate    @{当前系统时间}[0]    ${起始时间}
    ${油机启动时间0}    Subtract Time From Date    ${设置系统时间}    5s    #比1:00:00差5s
    Wait Until Keyword Succeeds    10    1    设置系统时间    ${油机启动时间0}
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    停止
    Wait Until Keyword Succeeds    4m    1    获取干接点状态直到变化为    ${油机干接点状态}    1    #干接点动作为1（断开），恢复为0（闭合）
    sleep    1m
    关闭交流源输出
    sleep    1m
    Wait Until Keyword Succeeds    20    1    设置失败的web控制量    油机开启
    ${起始时间}    获取web参数量    油机禁止运行终止时刻_2
    ${当前系统时间}    获取系统时间
    ${当前系统时间}    split string    ${当前系统时间}
    ${油机启动时间}    catenate    SEPARATOR=    ${起始时间}    :00
    ${设置系统时间}    catenate    @{当前系统时间}[0]    ${起始时间}
    ${油机启动时间0}    Subtract Time From Date    ${设置系统时间}    5s    #比1:00:00差5s
    Wait Until Keyword Succeeds    10    1    设置系统时间    ${油机启动时间0}
    Wait Until Keyword Succeeds    20    1    设置web控制量    油机开启
    run keyword and ignore error    wait until keyword succeeds    20    1    判断告警不存在    油机异常告警
    Wait Until Keyword Succeeds    4m    1    获取干接点状态直到变化为    ${油机干接点状态}    0
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    运行
    #起始时刻3
    ${起始时间}    获取web参数量    油机禁止运行起始时刻_3
    ${当前系统时间}    获取系统时间
    ${当前系统时间}    split string    ${当前系统时间}
    ${油机启动时间}    catenate    SEPARATOR=    ${起始时间}    :00
    ${设置系统时间}    catenate    @{当前系统时间}[0]    ${起始时间}
    ${油机启动时间0}    Subtract Time From Date    ${设置系统时间}    5s    #比1:00:00差5s
    Wait Until Keyword Succeeds    10    1    设置系统时间    ${油机启动时间0}
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    停止
    Wait Until Keyword Succeeds    4m    1    获取干接点状态直到变化为    ${油机干接点状态}    1    #干接点动作为1（断开），恢复为0（闭合）
    sleep    1m
    关闭交流源输出
    sleep    1m
    Wait Until Keyword Succeeds    20    1    设置失败的web控制量    油机开启
    ${起始时间}    获取web参数量    油机禁止运行终止时刻_3
    ${当前系统时间}    获取系统时间
    ${当前系统时间}    split string    ${当前系统时间}
    ${油机启动时间}    catenate    SEPARATOR=    ${起始时间}    :00
    ${设置系统时间}    catenate    @{当前系统时间}[0]    ${起始时间}
    ${油机启动时间0}    Subtract Time From Date    ${设置系统时间}    5s    #比1:00:00差5s
    Wait Until Keyword Succeeds    10    1    设置系统时间    ${油机启动时间0}
    Wait Until Keyword Succeeds    20    1    设置web控制量    油机开启
    run keyword and ignore error    wait until keyword succeeds    20    1    判断告警不存在    油机异常告警
    Wait Until Keyword Succeeds    4m    1    获取干接点状态直到变化为    ${油机干接点状态}    0
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    运行
    sleep    1m
    Wait Until Keyword Succeeds    5m    1    设置web控制量    油机关闭
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    停止
    Wait Until Keyword Succeeds    4m    1    获取干接点状态直到变化为    ${油机干接点状态}    1
    关闭交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    [Teardown]    Run keywords    同步系统时间
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    油机禁止运行起始时刻_1    油机禁止运行终止时刻_1    油机禁止运行起始时刻_2    油机禁止运行终止时刻_2    油机禁止运行起始时刻_3    油机禁止运行终止时刻_3
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    油机禁止运行使能
    ...    AND    关闭交流源输出

油机禁止运行测试(电池组无效）
    油机管理初始化
    Wait Until Keyword Succeeds    10    1    设置系统时间    2021-01-29 8:00:00
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    0
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动电压使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动SOC使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动时间使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动系统异常使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机定时启动使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机禁止运行使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机禁止运行起始时刻_1    10:00
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机禁止运行终止时刻_1    10:20
    #起始时刻1
    ${起始时间}    获取web参数量    油机禁止运行起始时刻_1
    ${当前系统时间}    获取系统时间
    ${当前系统时间}    split string    ${当前系统时间}
    ${油机启动时间}    catenate    SEPARATOR=    ${起始时间}    :00
    ${设置系统时间}    catenate    @{当前系统时间}[0]    ${起始时间}
    ${油机启动时间0}    Subtract Time From Date    ${设置系统时间}    5s    #比1:00:00差5s
    Wait Until Keyword Succeeds    10    1    设置系统时间    ${油机启动时间0}
    Wait Until Keyword Succeeds    4m    1    获取干接点状态直到变化为    ${油机干接点状态}    1    #干接点动作为1（断开），恢复为0（闭合）
    ${状态}    Run Keyword And Return Status    Wait Until Keyword Succeeds    10m    2    信号量数据值为    油机状态    运行
    should not be true    ${状态}
    ${起始时间}    获取web参数量    油机禁止运行终止时刻_1
    ${当前系统时间}    获取系统时间
    ${当前系统时间}    split string    ${当前系统时间}
    ${油机启动时间}    catenate    SEPARATOR=    ${起始时间}    :00
    ${设置系统时间}    catenate    @{当前系统时间}[0]    ${起始时间}
    ${油机启动时间0}    Subtract Time From Date    ${设置系统时间}    5s    #比1:00:00差5s
    Wait Until Keyword Succeeds    10    1    设置系统时间    ${油机启动时间0}
    run keyword and ignore error    wait until keyword succeeds    20    1    判断告警不存在    油机异常告警
    Wait Until Keyword Succeeds    4m    1    获取干接点状态直到变化为    ${油机干接点状态}    0
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    10m    1    信号量数据值为    油机状态    运行
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    100
    ${电池电压}    获取web实时数据    电池电压-1
    Wait Until Keyword Succeeds    2m    1    设置web控制量    油机关闭
    Wait Until Keyword Succeeds    4m    2    信号量数据值为    油机状态    停止
    Wait Until Keyword Succeeds    4m    1    获取干接点状态直到变化为    ${油机干接点状态}    1    #干接点动作为1（断开），恢复为0（闭合）
    [Teardown]    Run keywords    同步系统时间
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    油机禁止运行起始时刻_1    油机禁止运行终止时刻_1    油机禁止运行起始时刻_2    油机禁止运行终止时刻_2    油机禁止运行起始时刻_3    油机禁止运行终止时刻_3
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    油机禁止运行使能
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    100
    ...    AND    关闭交流源输出
