*** Settings ***
Documentation     AEMB 无参数量
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
获取和设置交流电表告警级别
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置交流电表告警级别~CID1=D2H>>    <<设置交流电表告警级别~CID2=82H>>    交流电表通讯断    ${VAR}    1    ${g_ver_1363}
        should be true    ${设置结果}
        Sleep    30
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取交流电表告警级别~CID1=D2H>>    <<获取交流电表告警级别~CID2=81H>>    交流电表通讯断-1    01    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    交流电表通讯断告警    ${VAR}
        ${交流电表通讯断告警级别}    获取web参数量    交流电表通讯断告警
        should be equal    ${1104告警级别}    ${交流电表通讯断告警级别}
    END
    [Teardown]    设置web参数量    交流电表通讯断告警    主要

设置交流电表参数
    ${1363数据}    1104批量参数设置测试    ${g_prtcl_1363}    <<设置交流电表参数~CID1=D2H>>    <<设置交流电表参数~CID2=49H>>    ${g_ver_1363}    01
    ${对比结果}    批量对比参数设置_1104    ${1363数据}    1363_交流电表_参数设置
    should be true    ${对比结果}

获取交流电表参数
    ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取交流电表参数量~CID1=D2H>>    <<获取交流电表参数量~CID2=47H>>    FF    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${对比结果}    批量对比数据_1104_WEB    ${协议数据}    ${web数据}    1363_交流电表_参数量
    should be true    ${对比结果}
