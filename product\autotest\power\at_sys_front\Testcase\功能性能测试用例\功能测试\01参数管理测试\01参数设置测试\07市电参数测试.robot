*** Settings ***
Suite Setup       run keywords    交流输入场景切换
...               AND    判断web参数是否存在    电池应用场景
...               AND    Wait Until Keyword Succeeds    10    2    设置web参数量    电池应用场景    循环场景
...               AND    判断web参数是否存在    市电电池优先级设置
...               AND    Wait Until Keyword Succeeds    10    2    设置web参数量    市电电池优先级设置    电池优先
Suite Teardown    Wait Until Keyword Succeeds    10    2    设置web参数量    电池应用场景    备电场景
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
市电额定有功功率测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    市电额定有功功率
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    市电额定有功功率
    ${缺省值}    获取web参数上下限范围    市电额定有功功率
    ${可设置范围}    获取web参数可设置范围    市电额定有功功率
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    市电额定有功功率    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    市电额定有功功率
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    市电额定有功功率    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    市电额定有功功率
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    市电额定有功功率

市电降额系数测试
    [Tags]    PMSA-NTest    Re
    [Setup]    run keywords    判断web参数是否存在    市电额定有功功率
    ...    AND    Wait Until Keyword Succeeds    10    2    设置web参数量    市电额定有功功率    15
    ...    AND    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    市电降额系数
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    市电降额系数
    ${缺省值}    获取web参数上下限范围    市电降额系数
    ${可设置范围}    获取web参数可设置范围    市电降额系数
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    市电降额系数    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    市电降额系数
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    市电降额系数    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    市电降额系数
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    市电降额系数

交流输入限功率预警阈值测试
    [Tags]    PMSA-NTest    Re
    [Setup]    run keywords    Wait Until Keyword Succeeds    10    2    设置web参数量    市电配置_1    有
    ...    AND    判断web参数是否存在    市电额定有功功率
    ...    AND    Wait Until Keyword Succeeds    10    2    设置web参数量    市电额定有功功率    15
    ...    AND    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    交流输入限功率预警阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    交流输入限功率预警阈值
    ${缺省值}    获取web参数上下限范围    交流输入限功率预警阈值
    ${可设置范围}    获取web参数可设置范围    交流输入限功率预警阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    交流输入限功率预警阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    交流输入限功率预警阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    交流输入限功率预警阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    交流输入限功率预警阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    交流输入限功率预警阈值

交流输入限功率告警阈值测试
    [Tags]    PMSA-NTest    Re
    [Setup]    run keywords    Wait Until Keyword Succeeds    10    2    设置web参数量    市电配置_1    有
    ...    AND    判断web参数是否存在    市电额定有功功率
    ...    AND    Wait Until Keyword Succeeds    10    2    设置web参数量    市电额定有功功率    15
    ...    AND    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    交流输入限功率告警阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    交流输入限功率告警阈值
    ${缺省值}    获取web参数上下限范围    交流输入限功率告警阈值
    ${可设置范围}    获取web参数可设置范围    交流输入限功率告警阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    交流输入限功率告警阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    交流输入限功率告警阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    交流输入限功率告警阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    交流输入限功率告警阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    交流输入限功率告警阈值

市电启动电压使能测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    市电启动电压使能
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    市电启动电压使能
    ${取值约定dict}    获取web参数的取值约定    市电启动电压使能
    ${取值约定values}    get dictionary values    ${取值约定dict}
    FOR    ${参数设置}    IN    @{取值约定values}
        设置web参数量    市电启动电压使能    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    市电启动电压使能
        should be equal    ${参数获取}    ${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    市电启动电压使能

市电启动SOC使能测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    市电启动SOC使能
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    市电启动SOC使能
    ${取值约定dict}    获取web参数的取值约定    市电启动SOC使能
    ${取值约定values}    get dictionary values    ${取值约定dict}
    FOR    ${参数设置}    IN    @{取值约定values}
        设置web参数量    市电启动SOC使能    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    市电启动SOC使能
        should be equal    ${参数获取}    ${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    市电启动SOC使能

市电启动时间使能测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    市电启动时间使能
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    市电启动时间使能
    ${取值约定dict}    获取web参数的取值约定    市电启动时间使能
    ${取值约定values}    get dictionary values    ${取值约定dict}
    FOR    ${参数设置}    IN    @{取值约定values}
        设置web参数量    市电启动时间使能    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    市电启动时间使能
        should be equal    ${参数获取}    ${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    市电启动时间使能

市电启动电压阈值测试
    [Tags]    PMSA-NTest    Re
    [Setup]    run keywords    判断web参数是否存在    市电启动电压使能
    ...    AND    Wait Until Keyword Succeeds    10    2    设置web参数量    市电启动电压使能    允许
    ...    AND    判断web参数是否存在    市电启动电压阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    市电启动电压阈值
    ${缺省值}    获取web参数上下限范围    市电启动电压阈值
    ${可设置范围}    获取web参数可设置范围    市电启动电压阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    市电启动电压阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    市电启动电压阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    市电启动电压阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    市电启动电压阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    市电启动电压阈值    市电启动电压使能

市电启动SOC阈值测试
    [Tags]    PMSA-NTest    Re
    [Setup]    run keywords    判断web参数是否存在    市电启动SOC使能
    ...    AND    Wait Until Keyword Succeeds    10    2    设置web参数量    市电启动SOC使能    允许
    ...    AND    判断web参数是否存在    市电启动SOC阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    市电启动SOC阈值
    ${缺省值}    获取web参数上下限范围    市电启动SOC阈值
    ${可设置范围}    获取web参数可设置范围    市电启动SOC阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    市电启动SOC阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    市电启动SOC阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    市电启动SOC阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    市电启动SOC阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    市电启动SOC阈值    市电启动SOC使能

市电启动放电时间阈值测试
    [Tags]    PMSA-NTest    Re
    [Setup]    run keywords    判断web参数是否存在    市电启动时间使能
    ...    AND    Wait Until Keyword Succeeds    10    2    设置web参数量    市电启动时间使能    允许
    ...    AND    判断web参数是否存在    市电启动放电时间阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    市电启动放电时间阈值
    ${缺省值}    获取web参数上下限范围    市电启动放电时间阈值
    ${可设置范围}    获取web参数可设置范围    市电启动放电时间阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    市电启动放电时间阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    市电启动放电时间阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    市电启动放电时间阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    市电启动放电时间阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    市电启动放电时间阈值    市电启动时间使能

市电停止电压使能测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    市电停止电压使能
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    市电停止电压使能
    ${取值约定dict}    获取web参数的取值约定    市电停止电压使能
    ${取值约定values}    get dictionary values    ${取值约定dict}
    FOR    ${参数设置}    IN    @{取值约定values}
        设置web参数量    市电停止电压使能    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    市电停止电压使能
        should be equal    ${参数获取}    ${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    市电停止电压使能

市电停止SOC使能测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    市电停止SOC使能
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    市电停止SOC使能
    ${取值约定dict}    获取web参数的取值约定    市电停止SOC使能
    ${取值约定values}    get dictionary values    ${取值约定dict}
    FOR    ${参数设置}    IN    @{取值约定values}
        设置web参数量    市电停止SOC使能    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    市电停止SOC使能
        should be equal    ${参数获取}    ${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    市电停止SOC使能

市电停止电流使能测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    市电停止电流使能
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    市电停止电流使能
    ${取值约定dict}    获取web参数的取值约定    市电停止电流使能
    ${取值约定values}    get dictionary values    ${取值约定dict}
    FOR    ${参数设置}    IN    @{取值约定values}
        设置web参数量    市电停止电流使能    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    市电停止电流使能
        should be equal    ${参数获取}    ${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    市电停止电流使能

市电停止电压测试
    [Tags]    PMSA-NTest    Re
    [Setup]    run keywords    判断web参数是否存在    市电停止电压使能
    ...    AND    Wait Until Keyword Succeeds    10    2    设置web参数量    市电停止电压使能    允许
    ...    AND    判断web参数是否存在    市电停止电压
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    市电停止电压
    ${缺省值}    获取web参数上下限范围    市电停止电压
    ${可设置范围}    获取web参数可设置范围    市电停止电压
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    市电停止电压    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    市电停止电压
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    市电停止电压    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    市电停止电压
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    市电停止电压    市电停止电压使能

市电关闭SOC阈值测试
    [Tags]    PMSA-NTest    Re
    [Setup]    run keywords    判断web参数是否存在    市电停止SOC使能
    ...    AND    Wait Until Keyword Succeeds    10    2    设置web参数量    市电停止SOC使能    允许
    ...    AND    判断web参数是否存在    市电关闭SOC阈值
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    市电关闭SOC阈值
    ${缺省值}    获取web参数上下限范围    市电关闭SOC阈值
    ${可设置范围}    获取web参数可设置范围    市电关闭SOC阈值
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    市电关闭SOC阈值    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    市电关闭SOC阈值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    市电关闭SOC阈值    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    市电关闭SOC阈值
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    市电关闭SOC阈值    市电停止SOC使能

市电关闭电池电流测试
    [Tags]    PMSA-NTest    Re
    [Setup]    run keywords    判断web参数是否存在    市电停止电流使能
    ...    AND    Wait Until Keyword Succeeds    10    2    设置web参数量    市电停止电流使能    允许
    ...    AND    判断web参数是否存在    市电关闭电池电流
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    市电关闭电池电流
    ${缺省值}    获取web参数上下限范围    市电关闭电池电流
    ${可设置范围}    获取web参数可设置范围    市电关闭电池电流
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    市电关闭电池电流    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    市电关闭电池电流
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    市电关闭电池电流    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    市电关闭电池电流
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    市电关闭电池电流    市电停止电流使能

市电最短运行时间测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    市电最短运行时间
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    市电最短运行时间
    ${缺省值}    获取web参数上下限范围    市电最短运行时间
    ${可设置范围}    获取web参数可设置范围    市电最短运行时间
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    市电最短运行时间    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    市电最短运行时间
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    市电最短运行时间    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    市电最短运行时间
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    市电最短运行时间

市电最长运行时间测试
    [Tags]    PMSA-NTest    Re
    [Setup]    判断web参数是否存在    市电最长运行时间
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    市电最长运行时间
    ${缺省值}    获取web参数上下限范围    市电最长运行时间
    ${可设置范围}    获取web参数可设置范围    市电最长运行时间
    ${超下限}    evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    evaluate    ${可设置范围}[1]+${缺省值}[4]
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        ${设置结果}    run keyword and return status    设置web参数量    市电最长运行时间    ${参数设置}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    市电最长运行时间
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        设置web参数量    市电最长运行时间    ${参数设置}
        sleep    1
        ${参数获取}    获取web参数量    市电最长运行时间
        should be true    ${参数获取}==${参数设置}
    END
    [Teardown]    设置web设备参数量为默认值    市电最长运行时间
