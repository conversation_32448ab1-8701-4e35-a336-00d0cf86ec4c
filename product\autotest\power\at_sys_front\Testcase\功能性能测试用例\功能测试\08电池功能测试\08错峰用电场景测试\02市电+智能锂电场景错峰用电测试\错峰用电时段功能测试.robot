*** Settings ***
Resource          ../错峰用电场景.robot


*** Test Cases ***
错峰用电时段功能测试
    [Setup]    错峰用电场景前置条件
    错峰设置初始化    智能锂电    1    0
    ${错峰启动电压}    获取错峰启动电压
    设置子工具值  smartli  1  只读  PACK电压  ${错峰启动电压}
    ${错峰类型列表}    Create List    尖峰    高峰    平期
    FOR    ${错峰类型}    IN    @{错峰类型列表}
        错峰用电时段功能测试初始化    ${错峰类型}
        错峰放电校验/维持/校验  智能锂电    ${错峰类型}
    END
    错峰用电时段功能测试初始化    谷期
    错峰谷期校验/校验/校验    智能锂电
    [Teardown]    错峰用电时段功能测试后置条件