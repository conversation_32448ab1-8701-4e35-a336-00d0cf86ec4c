*** Settings ***
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_获取时间
    [Tags]    pass
    ${能源网管数据}    能源网管协议_获取数据    获取时间    ${SSH}
    ${web数据}    获取系统时间
    ${对比结果}    能源网管协议_对比时间    ${能源网管数据}    ${web数据}    获取时间
    should be true    ${对比结果}

power_sm_0002_设置时间
    [Documentation]    系统时间 2020-02-22 16:52:01 设置时间
    [Tags]    4
    ${能源网管数据}    能源网管协议_设置数据    系统时间    2020-02-22 16:52:01    设置时间    ${g_energySC_addr}    ${SSH}
    should be true    ${能源网管数据}
    ${能源网管数据_time}    能源网管协议_获取数据    获取时间    ${SSH}
    ${web数据}    获取系统时间
    ${对比结果}    能源网管协议_对比时间    ${能源网管数据_time}    ${web数据}    获取时间
    should be true    ${对比结果}
    ${同步时间}    同步系统时间
