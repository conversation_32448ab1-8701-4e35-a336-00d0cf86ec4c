*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
获取温湿度传感器数字量
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    控制子工具运行停止    DMU_YD8779Y    关闭
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    温湿度传感器通讯状态    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    温湿度传感器在位状态    在位
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    温湿度传感器工作状态    通讯断
    ${snmp获取值1}    获取SNMP数据_单个    humitureSensorCommunicateStatusvalue
    should be equal as strings    ${snmp获取值1}    1
    ${snmp获取值1}    获取SNMP数据_单个    humitureSensorExistStatusvalue
    should be equal as strings    ${snmp获取值1}    1
    ${snmp获取值1}    获取SNMP数据_单个    humitureSensorWorkStatusvalue
    should be equal as strings    ${snmp获取值1}    3
    控制子工具运行停止    DMU_YD8779Y    开启
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    温湿度传感器通讯状态    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    温湿度传感器在位状态    在位
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    温湿度传感器工作状态    正常
    ${snmp获取值1}    获取SNMP数据_单个    humitureSensorCommunicateStatusvalue
    should be equal as strings    ${snmp获取值1}    0
    ${snmp获取值1}    获取SNMP数据_单个    humitureSensorExistStatusvalue
    should be equal as strings    ${snmp获取值1}    1
    ${snmp获取值1}    获取SNMP数据_单个    humitureSensorWorkStatusvalue
    should be equal as strings    ${snmp获取值1}    1
    [Teardown]    Run Keywords    控制子工具运行停止    DMU_YD8779Y    开启
    ...    AND    sleep    3min

snmp获取扩展湿度传感器状态
    [Setup]
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    扩展湿度传感器状态    正常
    ${snmp获取值1}    获取SNMP数据_单个    extendHumiditySensorStatusvalue
    should be equal as strings    ${snmp获取值1}    0
    设置子工具值    DMU_YD8779Y    all    只读    湿度    101
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    扩展湿度传感器状态    异常
    ${snmp获取值1}    获取SNMP数据_单个    extendHumiditySensorStatusvalue
    should be equal as strings    ${snmp获取值1}    1
    设置子工具值    DMU_YD8779Y    all    只读    湿度    60
    Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    湿度传感器    系统运行环境    无
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    扩展湿度传感器状态    未配置
    ${snmp获取值1}    获取SNMP数据_单个    extendHumiditySensorStatusvalue
    should be equal as strings    ${snmp获取值1}    2
    [Teardown]    Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    湿度传感器    系统运行环境    扩展湿度

snmp获取扩展温度传感器状态
    [Setup]
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    FOR    ${i}    IN RANGE    1    9
        Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_${i}
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    扩展温度传感器状态_${i}    正常
        ${snmp获取值1}    获取SNMP数据_单个    extendTemperatureSensorStatusvalue    ${i}
        should be equal as strings    ${snmp获取值1}    0
        设置子工具值    DMU_YD8779Y    all    只读    温度    101
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    扩展温度传感器状态_${i}    异常
        ${snmp获取值1}    获取SNMP数据_单个    extendTemperatureSensorStatusvalue    ${i}
        should be equal as strings    ${snmp获取值1}    1
        设置子工具值    DMU_YD8779Y    all    只读    温度    24
        Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    无
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    扩展温度传感器状态_${i}    未配置
        ${snmp获取值1}    获取SNMP数据_单个    extendTemperatureSensorStatusvalue    ${i}
        should be equal as strings    ${snmp获取值1}    2
    END
    [Teardown]    Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_4
