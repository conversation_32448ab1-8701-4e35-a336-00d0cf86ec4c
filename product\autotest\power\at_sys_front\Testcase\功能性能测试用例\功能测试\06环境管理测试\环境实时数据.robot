*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
环境温度和状态显示
    [Documentation]    0:正常/Normal;1:异常/Fault
    连接CSU
    ${环境温度}    Wait Until Keyword Succeeds    10    1    获取web实时数据    环境温度
    should be true    20<=${环境温度}<=35
    ${环境温度传感器状态}    获取web实时数据    环境温度传感器状态
    should be equal    '${环境温度传感器状态}'    '正常'

环境湿度和状态显示
    设置通道无效值/恢复通道原始值    ${plat.humity}    1
    连接CSU
    sleep    15
    ${环境湿度}    Wait Until Keyword Succeeds    10    1    获取web实时数据    环境湿度
    ${环境湿度获取}    create list    ${环境湿度}
    ${环境湿度确认}    create list    val_invalid    #无效：val_invalid
    should be true    ${环境湿度获取}==${环境湿度确认}
    ${环境湿度传感器状态}    获取web实时数据    环境湿度传感器状态
    should be equal    '${环境湿度传感器状态}'    '异常'
    设置通道无效值/恢复通道原始值    ${plat.humity}    0
