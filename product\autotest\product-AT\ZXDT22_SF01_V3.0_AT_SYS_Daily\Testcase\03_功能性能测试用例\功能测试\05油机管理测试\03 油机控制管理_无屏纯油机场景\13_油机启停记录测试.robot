*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
手动启停油机记录测试
    [Documentation]    油机手动启动完成后运行10分钟，手动关闭，等待7分钟，检查油机启停记录正确性。
    油机管理初始化
    ${开始数量}    获取web事件记录数量    油机启停记录
    run keyword if    ${开始数量}>995    删除历史记录    油机启停记录
    sleep    30
    ${开始数量}    获取web事件记录数量    油机启停记录
    @{记录内容old}    run keyword if    ${开始数量} != 0    获取web事件记录最新一条    油机启停记录
    ${最短运行时间设置值}    获取web参数量    油机最短运行时间
    ${默认值}    获取web参数上下限范围    油机最短运行时间
    run keyword if    ${最短运行时间设置值} != @{默认值}[0]    Wait Until Keyword Succeeds    10    1    设置web参数量    油机最短运行时间    @{默认值}[0]
    ${最短运行时间设置值}    获取web参数量    油机最短运行时间
    Wait Until Keyword Succeeds    20    1    设置web控制量    油机开启
    run keyword and ignore error    wait until keyword succeeds    20    1    判断告警不存在    油机异常告警
    Wait Until Keyword Succeeds    4m    1    获取干接点状态直到变化为    ${油机干接点状态}    0
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    运行
    ${起始时间}    获取系统时间
    ${起始发电量}    获取web实时数据    油机累计发电量
    ${起始运行时间}    获取web实时数据    油机累计运行时间
    ${起始手动运行时长}    获取web实时数据    手动油机运行时长
    ${起始手动发电量}    获取web实时数据    手动油机发电量
    sleep    2m
    Wait Until Keyword Succeeds    5m    1    设置web控制量    油机关闭
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    停止
    ${结束运行时间}    获取web实时数据    油机累计运行时间
    ${结束手动运行时长}    获取web实时数据    手动油机运行时长
    Wait Until Keyword Succeeds    4m    1    获取干接点状态直到变化为    ${油机干接点状态}    1
    ${结束时间}    获取系统时间
    ${结束发电量}    获取web实时数据    油机累计发电量
    ${起始手动发电量}    获取web实时数据    手动油机发电量
    sleep    1m
    关闭交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    sleep    4m
    ${结束数量}    获取web事件记录数量    油机启停记录
    should be true    ${结束数量} == ${开始数量}+1
    @{记录内容}    获取web事件记录最新一条    油机启停记录
    should be true    @{记录内容}[0] == ${结束数量}
    ${起始时间差}    subtract date from date    @{记录内容}[1]    ${起始时间}
    should be true    -40<=${起始时间差}<=40
    ${结束时间差}    subtract date from date    @{记录内容}[2]    ${结束时间}
    should be true    -40<=${结束时间差}<=40
    should be true    ${起始运行时间}-1<= @{记录内容}[3] <= ${起始运行时间}+1
    should be true    ${结束运行时间}-1<= @{记录内容}[4] <= ${结束运行时间}+1
    should be true    ${起始发电量}-0.4 <= @{记录内容}[5] <= ${起始发电量}+0.4
    should be true    ${结束发电量}-0.4<= @{记录内容}[6] <= ${结束发电量}+0.4
    should be true    ${起始手动运行时长}-1 <= @{记录内容}[7]<= ${起始手动运行时长}+1
    should be true    ${结束手动运行时长}-1<= @{记录内容}[8]<=${结束手动运行时长}+1
    should be true    ${起始手动发电量}-0.4<= \ @{记录内容}[9] <=${起始手动发电量}+0.4
    should be true    ${起始手动发电量}-0.4<= @{记录内容}[10]<= \ ${起始手动发电量}+0.4
    [Teardown]    Run keywords    同时设置三相电压频率    220    50
    ...    AND    打开交流源输出

油机启停记录正确性（自动模式）

油机启停记录删除功能
    连接CSU
    ${开始数量}    获取web事件记录数量    油机启停记录
    删除历史记录    油机启停记录
    sleep    30
    ${结束数量}    获取web事件记录数量    油机启停记录
    should be true    ${结束数量} == 0

油机启停记录导出

油机启停记录掉电保存功能
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    ${记录数量}    获取web事件记录数量    油机启停记录
    run keyword if    ${记录数量}>990    删除历史记录    油机启停记录
    测试台上下电操作    T1-1    OFF
    sleep    15
    测试台上下电操作    T1-1    ON
    实时告警刷新完成
    sleep    5
    ${记录数量1}    获取web事件记录数量    油机启停记录
    should be true    ${记录数量}==${记录数量1}

油机启停记录最大条数测试
    油机管理初始化
    ${开始数量}    获取web事件记录数量    油机启停记录
    ${最短运行时间设置值}    获取web参数量    油机最短运行时间
    ${默认值}    获取web参数上下限范围    油机最短运行时间
    run keyword if    ${最短运行时间设置值} != @{默认值}[0]    Wait Until Keyword Succeeds    10    1    设置web参数量    油机最短运行时间    @{默认值}[0]
    ${最短运行时间设置值}    获取web参数量    油机最短运行时间
    #产生启停记录
    FOR    ${k}    IN RANGE    1001
    Wait Until Keyword Succeeds    20    1    设置web控制量    油机开启
    run keyword and ignore error    wait until keyword succeeds    20    1    判断告警不存在    油机异常告警
    Wait Until Keyword Succeeds    4m    1    获取干接点状态直到变化为    ${油机干接点状态}    0
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    运行
    sleep    2m
    Wait Until Keyword Succeeds    5m    1    设置web控制量    油机关闭
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    停止
    Wait Until Keyword Succeeds    4m    1    获取干接点状态直到变化为    ${油机干接点状态}    1
    sleep    1m
    关闭交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    sleep    4m
    ${记录数量}    获取web事件记录数量    油机启停记录
    exit for loop if    ${记录数量}>=1002
    sleep    2m
    ${记录数量}    获取web事件记录数量    油机启停记录
    should be true    ${记录数量} == 1000
    #再产生一条记录
    Wait Until Keyword Succeeds    20    1    设置web控制量    油机开启
    run keyword and ignore error    wait until keyword succeeds    20    1    判断告警不存在    油机异常告警
    Wait Until Keyword Succeeds    4m    1    获取干接点状态直到变化为    ${油机干接点状态}    0
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    运行
    sleep    2m
    Wait Until Keyword Succeeds    5m    1    设置web控制量    油机关闭
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    停止
    Wait Until Keyword Succeeds    4m    1    获取干接点状态直到变化为    ${油机干接点状态}    1
    sleep    1m
    关闭交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    sleep    6m
    ${结束数量}    获取web事件记录数量    油机启停记录
    should be true    ${结束数量} == 1000
