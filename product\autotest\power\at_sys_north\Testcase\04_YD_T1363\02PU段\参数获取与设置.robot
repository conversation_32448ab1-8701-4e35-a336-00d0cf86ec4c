*** Settings ***
Suite Setup
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
1363_0002_PU参数量获取
    [Tags]    1
    ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取PU段参数量~CID1=D0H>>    <<获取PU段参数量~CID2=47H>>    None    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${对比结果}    批量对比数据_1104_WEB    ${协议数据}    ${web数据}    1363_PU段_参数量
    should be true    ${对比结果}

1363_0006_PU输出过压值设置
    [Setup]    #测试用例前置条件
    Comment    仅有市电条件上电
    连接CSU
    Comment    关闭交流源输出
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    PU输出过压保护值
    ${缺省值}    获取web参数上下限范围    PU输出过压保护值
    ${可设置范围}    获取web参数可设置范围    PU输出过压保护值
    ${超下限}    evaluate    ${可设置范围}[0]-1
    ${超上限}    evaluate    ${可设置范围}[1]+1
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        Comment    ${设置结果}    run keyword and return status    设置web参数量    PU输出过压保护值    ${参数设置}
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置PU段参数量~CID1=D0H>>    <<设置PU段参数量~CID2=49H>>    PU输出过压值    ${参数设置}    None    ${g_ver_1363}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    PU输出过压保护值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        Comment    设置web参数量    PU输出过压保护值    ${参数设置}
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置PU段参数量~CID1=D0H>>    <<设置PU段参数量~CID2=49H>>    PU输出过压值    ${参数设置}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    1
        ${参数获取}    获取web参数量    PU输出过压保护值
        should be true    ${参数获取}==${参数设置}
    END

1363_0008_PU默认输出电压值设置
    [Documentation]    ${协议数据} | ${表单名称} | ${命令名称} | ${参数名} | ${设置值} | ${屏号}=None | ${g_ver}=
    [Setup]    #测试用例前置条件
    Comment    仅有市电条件上电
    连接CSU
    Comment    关闭交流源输出
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    PU默认输出电压值
    ${缺省值}    获取web参数上下限范围    PU默认输出电压值
    ${可设置范围}    获取web参数可设置范围    PU默认输出电压值
    ${超下限}    evaluate    ${可设置范围}[0]-1
    ${超上限}    evaluate    ${可设置范围}[1]+1
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        Comment    ${设置结果}    run keyword and return status    设置web参数量    PU默认输出电压值    ${参数设置}
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置PU段参数量~CID1=D0H>>    <<设置PU段参数量~CID2=49H>>    PU默认输出电压值    ${参数设置}    None    ${g_ver_1363}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    PU默认输出电压值
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        Comment    设置web参数量    PU默认输出电压值    ${参数设置}
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置PU段参数量~CID1=D0H>>    <<设置PU段参数量~CID2=49H>>    PU默认输出电压值    ${参数设置}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    1
        ${参数获取}    获取web参数量    PU默认输出电压值
        should be true    ${参数获取}==${参数设置}
    END

1363_0008_叠光电源默认输出电压设置
    [Documentation]    ${协议数据} | ${表单名称} | ${命令名称} | ${参数名} | ${设置值} | ${屏号}=None | ${g_ver}=
    [Tags]    NA
    [Setup]    #测试用例前置条件
    Comment    仅有市电条件上电
    连接CSU
    Comment    关闭交流源输出
    ${原参数}    Wait Until Keyword Succeeds    10    2    获取web参数量    叠光电源默认输出电压
    ${缺省值}    获取web参数上下限范围    叠光电源默认输出电压
    ${可设置范围}    获取web参数可设置范围    叠光电源默认输出电压
    ${超下限}    evaluate    ${可设置范围}[0]-1
    ${超上限}    evaluate    ${可设置范围}[1]+1
    #超范围设置不成功
    FOR    ${参数设置}    IN    ${超上限}    ${超下限}
        Comment    ${设置结果}    run keyword and return status    设置web参数量    PU默认输出电压值    ${参数设置}
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置PU段参数量~CID1=D0H>>    <<设置PU段参数量~CID2=49H>>    叠光电源默认输出电压    ${参数设置}    None    ${g_ver_1363}
        should not be true    ${设置结果}
        ${参数获取}    获取web参数量    叠光电源默认输出电压
        should be true    ${参数获取}==${原参数}
    END
    #范围内设置成功
    FOR    ${参数设置}    IN    @{可设置范围}    ${缺省值}[0]
        Comment    设置web参数量    PU默认输出电压值    ${参数设置}
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置PU段参数量~CID1=D0H>>    <<设置PU段参数量~CID2=49H>>    叠光电源默认输出电压    ${参数设置}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    1
        ${参数获取}    获取web参数量    叠光电源默认输出电压
        should be true    ${参数获取}==${参数设置}
    END
