** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
液位传感器通讯断告警获取测试
    连接CSU
    控制子工具运行停止    llSensor    关闭
    wait until keyword succeeds    10m    1    判断告警存在    液位传感器通讯中断-1
    控制子工具运行停止    llSensor    开启
    wait until keyword succeeds    10m    1    判断告警不存在    液位传感器通讯中断-1
    [Teardown]    控制子工具运行停止    llSensor    开启

油机燃油泄漏告警
    [Tags]      notest
    连接CSU
    Wait Until Keyword Succeeds    10    2    设置web参数量    油箱形状-1    立方体
    Wait Until Keyword Succeeds    10    2    设置web参数量    油箱长度-1    2000
    Wait Until Keyword Succeeds    10    2    设置web参数量    油箱宽度-1    1200
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流输入场景   油电
    ${可设置范围}    获取web参数可设置范围    油箱高度-1
    Wait Until Keyword Succeeds    10    2    设置web参数量    油箱高度-1    ${可设置范围}[1]
    设置子工具个数    llSensor    1
    Wait Until Keyword Succeeds    10    2    设置web控制量    RS485总线设备统计
    sleep    1m
    wait until keyword succeeds    1m    1    判断告警不存在    液位传感器通讯中断-2
    ${可设置范围}    获取web参数可设置范围    燃油泄漏告警阈值-1
    ${漏油阈值}    evaluate    ${可设置范围}[0]+70
    Wait Until Keyword Succeeds    10    1    设置web参数量    燃油泄漏告警阈值-1    ${漏油阈值}
    ${设置值}    获取web参数量    燃油泄漏告警阈值-1
    ${告警级别}    获取web参数量    油机燃油泄漏告警
    run keyword if    '${告警级别}' == '屏蔽'    设置web参数量    油机燃油泄漏告警    主要
    设置子工具值    llSensor    all    只读    液位    65535995
    sleep    10m
    ${高度获取值}    获取web实时数据    液位传感器高度-1
    should be true    ${高度获取值}==1000
    ${油量起始值}    获取web实时数据    油机剩余油量
    sleep    90m
    ${起始时间}    获取系统时间
    设置子工具值    llSensor    all    只读    液位    45875195
    sleep    40
    ${新高度值}    获取web实时数据    液位传感器高度-1
    should be true    ${新高度值}==700
    ${油量结束值}    获取web实时数据    油机剩余油量
    ${漏油量}    evaluate    ${油量起始值}-${油量结束值}
    Comment    sleep    25m
    wait until keyword succeeds    35m    1    判断告警存在    油机燃油泄漏告警
    ${结束时间}    获取系统时间
    Comment    sleep    30m
    wait until keyword succeeds    35m    1    判断告警不存在    油机燃油泄漏告警
    [Teardown]    设置web设备参数量为默认值    油箱高度-1    燃油泄漏告警阈值-1
