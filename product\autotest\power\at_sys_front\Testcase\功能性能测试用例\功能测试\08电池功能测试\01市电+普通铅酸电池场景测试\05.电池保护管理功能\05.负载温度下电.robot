*** Settings ***
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
负载高温下电使能下电测试
    
    电池管理初始化
    #获取参数/默认值
    wait until keyword succeeds    10    1    设置web参数量    负载高温下电使能    允许
    ${告警级别}    获取web参数量    负载高温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    负载高温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    负载高温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    ${电池电压}    获取web实时数据    电池电压-1
    sleep    3
    ${可设范围}    获取web参数可设置范围    负载高温下电温度
    设置web参数量    负载高温下电温度    ${可设范围}[0]
    run keyword and ignore error    设置web参数量    下电控制延时    0
    ${可设范围}    获取web参数可设置范围    LHTD判断时间
    run keyword and ignore error    设置web参数量    LHTD判断时间    ${可设范围}[0]
    显示属性配置    负载高温下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    3
    #设置温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    10    2
    ...    系统运行环境    环境温度
    sleep    10
    ${环境温度}    获取web实时数据    环境温度
    设置负载电压电流    53.5    10
    打开负载输出
    ${设置值}    获取web参数量    LHTD判断时间
    ${告警等待时间}    evaluate    ${设置值}+1
    sleep    3m
    wait until keyword succeeds    ${告警等待时间}m    1    判断告警存在    负载高温下电
    Wait Until Keyword Succeeds    3m    2    信号量数据值为    负载高温下电控制状态    动作
    #恢复
    设置web参数量    负载高温下电使能    禁止
    wait until keyword succeeds    ${告警等待时间}m    2    判断告警不存在    负载高温下电
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    0    1
    ...    系统运行环境    环境温度
    设置web参数量    负载高温下电使能    允许
    sleep    3m
    Wait Until Keyword Succeeds    5    1    显示属性配置    负载高温下电控制状态    数字量    web_attr=Off
    ...    gui_attr=Off
    Wait Until Keyword Succeeds    3m    2    信号量数据值为    负载高温下电控制状态    恢复
    关闭负载输出
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}
    ...    0    1    系统运行环境    环境温度
    ...    AND    设置web设备参数量为默认值    负载高温下电温度    负载高温下电使能
    ...    AND    关闭负载输出

负载高温下电保护测试
    
    电池管理初始化
    #获取参数/默认值
    wait until keyword succeeds    10    1    设置web参数量    负载高温下电使能    允许
    ${告警级别}    获取web参数量    负载高温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    负载高温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    负载高温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    sleep    3
    ${可设范围}    获取web参数可设置范围    负载高温下电温度
    设置web参数量    负载高温下电温度    ${可设范围}[0]
    run keyword and ignore error    设置web参数量    下电控制延时    0
    ${可设范围}    获取web参数可设置范围    LHTD判断时间
    run keyword and ignore error    设置web参数量    LHTD判断时间    ${可设范围}[0]
    显示属性配置    负载高温下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    3
    #设置温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    10    2
    ...    系统运行环境    环境温度
    sleep    10
    ${环境温度}    获取web实时数据    环境温度
    设置负载电压电流    53.5    10
    打开负载输出
    ${设置值}    获取web参数量    LHTD判断时间
    ${告警等待时间}    evaluate    ${设置值}+1
    sleep    3m
    wait until keyword succeeds    ${告警等待时间}m    1    判断告警存在    负载高温下电
    Wait Until Keyword Succeeds    3m    2    信号量数据值为    负载高温下电控制状态    动作
    #恢复
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    -10    1.6
    ...    系统运行环境    环境温度
    sleep    10
    ${环境温度}    获取web实时数据    环境温度
    sleep    3m
    wait until keyword succeeds    ${告警等待时间}m    2    判断告警不存在    负载高温下电
    Wait Until Keyword Succeeds    3m    2    信号量数据值为    负载高温下电控制状态    恢复
    显示属性配置    负载高温下电控制状态    数字量    web_attr=Off    gui_attr=Off
    关闭负载输出
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}
    ...    0    1    系统运行环境    环境温度
    ...    AND    设置web设备参数量为默认值    负载高温下电温度    负载高温下电使能
    ...    AND    关闭负载输出

负载高温下电延时控制功能测试
    
    电池管理初始化
    #获取参数/默认值
    wait until keyword succeeds    10    1    设置web参数量    负载高温下电使能    允许
    ${告警级别}    获取web参数量    负载高温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    负载高温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    负载高温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    sleep    3
    ${可设范围}    获取web参数可设置范围    负载高温下电温度
    设置web参数量    负载高温下电温度    ${可设范围}[0]
    ${可设范围}    获取web参数可设置范围    下电控制延时
    ${延时设置值}    evaluate    ${可设范围}[0]+120
    run keyword and ignore error    设置web参数量    下电控制延时    ${延时设置值}
    ${可设范围}    获取web参数可设置范围    LHTD判断时间
    run keyword and ignore error    设置web参数量    LHTD判断时间    ${可设范围}[0]
    显示属性配置    负载高温下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    3
    #设置温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    10    2
    ...    系统运行环境    环境温度
    sleep    10
    ${环境温度}    获取web实时数据    环境温度
    设置负载电压电流    53.5    10
    打开负载输出
    ${设置值}    获取web参数量    LHTD判断时间
    ${告警等待时间}    evaluate    ${设置值}+1
    wait until keyword succeeds    ${告警等待时间}m    1    判断告警存在    负载高温下电
    ${动作等待时间1}    evaluate    ${延时设置值}-30
    ${状态}    Run Keyword And Return Status    Wait Until Keyword Succeeds    ${动作等待时间1}s    2    信号量数据值为    负载高温下电控制状态
    ...    动作
    should not be true    ${状态}
    sleep    3m
    Wait Until Keyword Succeeds    ${动作等待时间1}s    1    信号量数据值为    负载高温下电控制状态    动作
    #恢复
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    -10    1.6
    ...    系统运行环境    环境温度
    sleep    10
    ${环境温度}    获取web实时数据    环境温度
    sleep    3m
    wait until keyword succeeds    ${告警等待时间}m    2    判断告警不存在    负载高温下电
    ${动作等待时间}    evaluate    ${延时设置值}+60
    Wait Until Keyword Succeeds    ${动作等待时间}s    2    信号量数据值为    负载高温下电控制状态    恢复
    显示属性配置    负载高温下电控制状态    数字量    web_attr=Off    gui_attr=Off
    关闭负载输出
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}
    ...    0    1    系统运行环境    环境温度
    ...    AND    设置web设备参数量为默认值    负载高温下电温度    负载高温下电使能    下电控制延时
    ...    AND    关闭负载输出

负载高温下电禁止下电测试
    
    电池管理初始化
    wait until keyword succeeds    10    1    设置web参数量    负载高温下电使能    允许
    ${告警级别}    获取web参数量    负载高温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    负载高温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    负载高温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    sleep    3
    ${可设范围}    获取web参数可设置范围    负载高温下电温度
    设置web参数量    负载高温下电温度    ${可设范围}[0]
    ${可设范围}    获取web参数可设置范围    下电控制延时
    run keyword and ignore error    设置web参数量    下电控制延时    ${可设范围}[0]
    ${可设范围}    获取web参数可设置范围    LHTD判断时间
    run keyword and ignore error    设置web参数量    LHTD判断时间    ${可设范围}[0]
    ${设置值}    获取web参数量    LHTD判断时间
    ${告警等待时间}    evaluate    ${设置值}+3
    wait until keyword succeeds    10    1    设置web参数量    负载高温下电使能    禁止
    sleep    3
    #设置温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    10    2
    ...    系统运行环境    环境温度
    sleep    10
    ${环境温度}    获取web实时数据    环境温度
    设置负载电压电流    53.5    10
    打开负载输出
    ${状态}    Run Keyword And Return Status    wait until keyword succeeds    ${告警等待时间}m    1    判断告警存在    负载高温下电
    should not be true    ${状态}
    关闭负载输出
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}
    ...    0    1    系统运行环境    环境温度
    ...    AND    设置web参数量    负载高温下电使能    允许
    ...    AND    设置web设备参数量为默认值    负载高温下电温度    负载高温下电使能
    ...    AND    关闭负载输出

负载高温下电恢复上电测试（系统停电）
    
    电池管理初始化
    wait until keyword succeeds    10    1    设置web参数量    负载高温下电使能    允许
    ${告警级别}    获取web参数量    负载高温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    负载高温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    负载高温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    sleep    3
    ${可设范围}    获取web参数可设置范围    负载高温下电温度
    设置web参数量    负载高温下电温度    ${可设范围}[0]
    ${可设范围}    获取web参数可设置范围    下电控制延时
    run keyword and ignore error    设置web参数量    下电控制延时    ${可设范围}[0]
    ${设置值}    获取web参数量    下电控制延时
    ${等待时间}    evaluate    ${设置值}+120
    ${可设范围}    获取web参数可设置范围    LHTD判断时间
    run keyword and ignore error    设置web参数量    LHTD判断时间    ${可设范围}[0]
    ${设置值}    获取web参数量    LHTD判断时间
    ${告警等待时间}    evaluate    ${设置值}+1
    显示属性配置    负载高温下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    3
    #设置温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    10    2
    ...    系统运行环境    环境温度
    sleep    10
    ${环境温度}    获取web实时数据    环境温度
    设置负载电压电流    53.5    10
    打开负载输出
    sleep    3m
    wait until keyword succeeds    ${告警等待时间}m    1    判断告警存在    负载高温下电
    Wait Until Keyword Succeeds    ${等待时间}s    2    信号量数据值为    负载高温下电控制状态    动作
    关闭交流源输出
    ${状态}    Run Keyword And Return Status    Wait Until Keyword Succeeds    ${等待时间}s    2    信号量数据值为    负载高温下电控制状态
    ...    恢复
    should not be true    ${状态}
    #恢复
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    -10    1.6
    ...    系统运行环境    环境温度
    sleep    10
    ${环境温度}    获取web实时数据    环境温度
    sleep    3m
    wait until keyword succeeds    ${告警等待时间}m    2    判断告警不存在    负载高温下电
    Wait Until Keyword Succeeds    ${等待时间}s    2    信号量数据值为    负载高温下电控制状态    恢复
    显示属性配置    负载高温下电控制状态    数字量    web_attr=Off    gui_attr=Off
    关闭负载输出
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}
    ...    0    1    系统运行环境    环境温度
    ...    AND    设置web设备参数量为默认值    负载高温下电温度    负载高温下电使能
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出

电池无效时不恢复负载上电（高温下电）
    
    电池管理初始化
    wait until keyword succeeds    10    1    设置web参数量    负载高温下电使能    允许
    ${告警级别}    获取web参数量    负载高温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    负载高温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    负载高温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    sleep    3
    ${可设范围}    获取web参数可设置范围    负载高温下电温度
    设置web参数量    负载高温下电温度    ${可设范围}[0]
    ${可设范围}    获取web参数可设置范围    下电控制延时
    run keyword and ignore error    设置web参数量    下电控制延时    ${可设范围}[0]
    ${设置值}    获取web参数量    下电控制延时
    ${等待时间}    evaluate    ${设置值}+120
    ${可设范围}    获取web参数可设置范围    LHTD判断时间
    run keyword and ignore error    设置web参数量    LHTD判断时间    ${可设范围}[0]
    ${设置值}    获取web参数量    LHTD判断时间
    ${告警等待时间}    evaluate    ${设置值}+1
    显示属性配置    负载高温下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    3
    #设置温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    10    2
    ...    系统运行环境    环境温度
    sleep    10
    ${环境温度}    获取web实时数据    环境温度
    设置负载电压电流    53.5    10
    打开负载输出
    sleep    3m
    wait until keyword succeeds    ${告警等待时间}m    1    判断告警存在    负载高温下电
    Wait Until Keyword Succeeds    ${等待时间}s    2    信号量数据值为    负载高温下电控制状态    动作
    #恢复
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    0
    wait until keyword succeeds    4m    2    判断告警存在    负载高温下电
    显示属性配置    电池高温下电控制状态    数字量    web_attr=Off    gui_attr=Off
    关闭负载输出
    [Teardown]    run keywords    设置web参数量    电池组容量_1    100
    ...    AND    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}
    ...    0    1    系统运行环境    环境温度
    ...    AND    设置web设备参数量为默认值    负载高温下电温度    负载高温下电使能
    ...    AND    关闭负载输出

电池无效时负载高温下电
    
    电池管理初始化
    wait until keyword succeeds    10    1    设置web参数量    负载高温下电使能    允许
    ${告警级别}    获取web参数量    负载高温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    负载高温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    负载高温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    sleep    3
    ${可设范围}    获取web参数可设置范围    负载高温下电温度
    设置web参数量    负载高温下电温度    ${可设范围}[0]
    ${可设范围}    获取web参数可设置范围    下电控制延时
    run keyword and ignore error    设置web参数量    下电控制延时    ${可设范围}[0]
    ${设置值}    获取web参数量    下电控制延时
    ${等待时间}    evaluate    ${设置值}+120
    ${可设范围}    获取web参数可设置范围    LHTD判断时间
    run keyword and ignore error    设置web参数量    LHTD判断时间    ${可设范围}[0]
    ${设置值}    获取web参数量    LHTD判断时间
    ${告警等待时间}    evaluate    ${设置值}+1
    显示属性配置    负载高温下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    3
    #设置温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    10    2
    ...    系统运行环境    环境温度
    sleep    10
    ${环境温度}    获取web实时数据    环境温度
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    0
    设置负载电压电流    53.5    10
    打开负载输出
    sleep    3m
    wait until keyword succeeds    ${告警等待时间}m    1    判断告警存在    负载高温下电
    Wait Until Keyword Succeeds    ${等待时间}s    2    信号量数据值为    负载高温下电控制状态    动作
    关闭负载输出
    [Teardown]    run keywords    设置web参数量    电池组容量_1    100
    ...    AND    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}
    ...    0    1    系统运行环境    环境温度
    ...    AND    设置web设备参数量为默认值    负载高温下电温度    负载高温下电使能
    ...    AND    关闭负载输出

负载低温下电使能下电测试
    
    电池管理初始化
    #获取参数/默认值
    wait until keyword succeeds    10    1    设置web参数量    负载低温下电使能    允许
    ${告警级别}    获取web参数量    负载低温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    负载低温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    负载低温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    ${电池电压}    获取web实时数据    电池电压-1
    sleep    3
    ${可设范围}    获取web参数可设置范围    负载低温下电温度
    设置web参数量    负载低温下电温度    ${可设范围}[1]
    ${可设范围}    获取web参数可设置范围    下电控制延时
    run keyword and ignore error    设置web参数量    下电控制延时    ${可设范围}[0]
    ${设置值}    获取web参数量    下电控制延时
    ${等待时间}    evaluate    ${设置值}+120
    显示属性配置    负载低温下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    3
    #设置温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    -10    0.001
    ...    系统运行环境    环境温度
    sleep    10
    ${环境温度}    获取web实时数据    环境温度
    设置负载电压电流    53.5    10
    打开负载输出
    sleep    3m
    wait until keyword succeeds    3m    1    判断告警存在    负载低温下电
    Wait Until Keyword Succeeds    ${等待时间}s    2    信号量数据值为    负载低温下电控制状态    动作
    #恢复
    设置web参数量    负载低温下电使能    禁止
    wait until keyword succeeds    3m    2    判断告警不存在    负载低温下电
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    0    1
    ...    系统运行环境    环境温度
    设置web参数量    负载低温下电使能    允许
    Wait Until Keyword Succeeds    5    1    显示属性配置    负载低温下电控制状态    数字量    web_attr=Off
    ...    gui_attr=Off
    显示属性配置    负载低温下电控制状态    数字量    web_attr=Off    gui_attr=Off
    关闭负载输出
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}
    ...    0    1    系统运行环境    环境温度
    ...    AND    设置web设备参数量为默认值    负载低温下电温度    负载低温下电使能
    ...    AND    关闭负载输出

负载低温下电保护测试
    
    电池管理初始化
    wait until keyword succeeds    10    1    设置web参数量    负载低温下电使能    允许
    ${告警级别}    获取web参数量    负载低温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    负载低温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    负载低温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    ${电池电压}    获取web实时数据    电池电压-1
    sleep    3
    ${可设范围}    获取web参数可设置范围    负载低温下电温度
    设置web参数量    负载低温下电温度    ${可设范围}[1]
    ${可设范围}    获取web参数可设置范围    下电控制延时
    run keyword and ignore error    设置web参数量    下电控制延时    ${可设范围}[0]
    ${设置值}    获取web参数量    下电控制延时
    ${等待时间}    evaluate    ${设置值}+120
    显示属性配置    负载低温下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    3
    #设置温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    -10    0.001
    ...    系统运行环境    环境温度
    sleep    10
    ${环境温度}    获取web实时数据    环境温度
    设置负载电压电流    53.5    10
    打开负载输出
    sleep    3m
    wait until keyword succeeds    3m    1    判断告警存在    负载低温下电
    Wait Until Keyword Succeeds    ${等待时间}s    2    信号量数据值为    负载低温下电控制状态    动作
    #恢复
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    0    1
    ...    系统运行环境    环境温度
    sleep    10
    ${环境温度}    获取web实时数据    环境温度
    sleep    3m
    wait until keyword succeeds    3m    2    判断告警不存在    负载低温下电
    Wait Until Keyword Succeeds    ${等待时间}s    2    信号量数据值为    负载低温下电控制状态    恢复
    显示属性配置    负载低温下电控制状态    数字量    web_attr=Off    gui_attr=Off
    关闭负载输出
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}
    ...    0    1    系统运行环境    环境温度
    ...    AND    设置web设备参数量为默认值    负载低温下电温度    负载低温下电使能
    ...    AND    关闭负载输出

负载低温下电延时控制功能测试
    
    电池管理初始化
    #获取参数/默认值
    wait until keyword succeeds    10    1    设置web参数量    负载低温下电使能    允许
    ${告警级别}    获取web参数量    负载低温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    负载低温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    负载低温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    sleep    3
    ${可设范围}    获取web参数可设置范围    负载低温下电温度
    设置web参数量    负载低温下电温度    ${可设范围}[1]
    ${可设范围}    获取web参数可设置范围    下电控制延时
    ${延时设置值}    evaluate    ${可设范围}[0]+120
    run keyword and ignore error    设置web参数量    下电控制延时    ${延时设置值}
    显示属性配置    负载低温下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    3
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    -10    0.001
    ...    系统运行环境    环境温度
    sleep    10
    ${环境温度}    获取web实时数据    环境温度
    设置负载电压电流    53.5    10
    打开负载输出
    wait until keyword succeeds    10m    1    判断告警存在    负载低温下电
    ${动作等待时间1}    evaluate    ${延时设置值}-30
    ${状态}    Run Keyword And Return Status    Wait Until Keyword Succeeds    ${动作等待时间1}s    2    信号量数据值为    负载低温下电控制状态
    ...    动作
    should not be true    ${状态}
    sleep    3m
    Wait Until Keyword Succeeds    ${动作等待时间1}s    1    信号量数据值为    负载低温下电控制状态    动作
    #恢复
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    0    1
    ...    系统运行环境    环境温度
    sleep    10
    ${环境温度}    获取web实时数据    环境温度
    wait until keyword succeeds    10m    2    判断告警不存在    负载低温下电
    ${动作等待时间}    evaluate    ${延时设置值}+60
    sleep    3m
    Wait Until Keyword Succeeds    ${动作等待时间}s    2    信号量数据值为    负载低温下电控制状态    恢复
    显示属性配置    负载低温下电控制状态    数字量    web_attr=Off    gui_attr=Off
    关闭负载输出
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}
    ...    0    1    系统运行环境    环境温度
    ...    AND    设置web设备参数量为默认值    负载低温下电温度    负载低温下电使能    下电控制延时
    ...    AND    关闭负载输出

电池无效时不恢复负载上电（低温下电）
    
    电池管理初始化
    wait until keyword succeeds    10    1    设置web参数量    负载低温下电使能    允许
    ${告警级别}    获取web参数量    负载低温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    负载低温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    负载低温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    sleep    3
    ${可设范围}    获取web参数可设置范围    负载低温下电温度
    设置web参数量    负载低温下电温度    ${可设范围}[1]
    ${可设范围}    获取web参数可设置范围    下电控制延时
    run keyword and ignore error    设置web参数量    下电控制延时    ${可设范围}[0]
    ${设置值}    获取web参数量    下电控制延时
    ${等待时间}    evaluate    ${设置值}+120
    显示属性配置    负载低温下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    3
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    -10    0.001
    ...    系统运行环境    环境温度
    sleep    10
    ${环境温度}    获取web实时数据    环境温度
    设置负载电压电流    53.5    10
    打开负载输出
    sleep    3m
    wait until keyword succeeds    3m    1    判断告警存在    负载低温下电
    Wait Until Keyword Succeeds    ${等待时间}s    2    信号量数据值为    负载低温下电控制状态    动作
    #恢复
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    0
    wait until keyword succeeds    4m    2    判断告警存在    负载低温下电
    显示属性配置    负载低温下电控制状态    数字量    web_attr=Off    gui_attr=Off
    关闭负载输出
    [Teardown]    run keywords    设置web参数量    电池组容量_1    100
    ...    AND    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}
    ...    0    1    系统运行环境    环境温度
    ...    AND    设置web设备参数量为默认值    负载低温下电温度    负载低温下电使能
    ...    AND    关闭负载输出

电池无效时负载低温下电
    
    电池管理初始化
    wait until keyword succeeds    10    1    设置web参数量    负载低温下电使能    允许
    ${告警级别}    获取web参数量    负载低温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    负载低温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    负载低温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    sleep    3
    ${可设范围}    获取web参数可设置范围    负载低温下电温度
    设置web参数量    负载低温下电温度    ${可设范围}[1]
    ${可设范围}    获取web参数可设置范围    下电控制延时
    run keyword and ignore error    设置web参数量    下电控制延时    ${可设范围}[0]
    ${设置值}    获取web参数量    下电控制延时
    ${等待时间}    evaluate    ${设置值}+120
    显示属性配置    负载低温下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    3
    #设置温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    -10    0.001
    ...    系统运行环境    环境温度
    sleep    10
    ${环境温度}    获取web实时数据    环境温度
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    0
    设置负载电压电流    53.5    10
    打开负载输出
    sleep    3m
    wait until keyword succeeds    3m    1    判断告警存在    负载低温下电
    Wait Until Keyword Succeeds    ${等待时间}s    2    信号量数据值为    负载低温下电控制状态    动作
    显示属性配置    负载低温下电控制状态    数字量    web_attr=Off    gui_attr=Off
    关闭负载输出
    [Teardown]    run keywords    设置web参数量    电池组容量_1    100
    ...    AND    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}
    ...    0    1    系统运行环境    环境温度
    ...    AND    设置web设备参数量为默认值    负载低温下电温度    负载低温下电使能
    ...    AND    关闭负载输出

负载低温下电禁止下电测试
    
    电池管理初始化
    wait until keyword succeeds    10    1    设置web参数量    负载低温下电使能    允许
    ${告警级别}    获取web参数量    负载低温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    负载低温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    负载低温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    sleep    3
    ${可设范围}    获取web参数可设置范围    负载低温下电温度
    设置web参数量    负载低温下电温度    ${可设范围}[1]
    ${可设范围}    获取web参数可设置范围    下电控制延时
    run keyword and ignore error    设置web参数量    下电控制延时    ${可设范围}[0]
    wait until keyword succeeds    10    1    设置web参数量    负载低温下电使能    禁止
    sleep    3
    #设置温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    -10    0.001
    ...    系统运行环境    环境温度
    sleep    10
    ${环境温度}    获取web实时数据    环境温度
    设置负载电压电流    53.5    10
    打开负载输出
    ${状态}    Run Keyword And Return Status    wait until keyword succeeds    5m    1    判断告警存在    负载低温下电
    should not be true    ${状态}
    关闭负载输出
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}
    ...    0    1    系统运行环境    环境温度
    ...    AND    设置web参数量    负载低温下电使能    允许
    ...    AND    设置web设备参数量为默认值    负载低温下电温度    负载低温下电使能
    ...    AND    关闭负载输出

负载低温下电恢复上电测试（系统停电）
    
    电池管理初始化
    wait until keyword succeeds    10    1    设置web参数量    负载低温下电使能    允许
    ${告警级别}    获取web参数量    负载低温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    负载低温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    负载低温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    sleep    3
    ${可设范围}    获取web参数可设置范围    负载低温下电温度
    设置web参数量    负载低温下电温度    ${可设范围}[1]
    ${可设范围}    获取web参数可设置范围    下电控制延时
    run keyword and ignore error    设置web参数量    下电控制延时    ${可设范围}[0]
    ${设置值}    获取web参数量    下电控制延时
    ${等待时间}    evaluate    ${设置值}+120
    显示属性配置    负载低温下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    3
    #设置温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    -10    0.001
    ...    系统运行环境    环境温度
    sleep    10
    ${环境温度}    获取web实时数据    环境温度
    设置负载电压电流    53.5    10
    打开负载输出
    sleep    3m
    wait until keyword succeeds    3m    1    判断告警存在    负载低温下电
    Wait Until Keyword Succeeds    ${等待时间}s    2    信号量数据值为    负载低温下电控制状态    动作
    关闭交流源输出
    ${状态}    Run Keyword And Return Status    Wait Until Keyword Succeeds    ${等待时间}s    2    信号量数据值为    负载低温下电控制状态
    ...    恢复
    should not be true    ${状态}
    #恢复
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    0    1
    ...    系统运行环境    环境温度
    sleep    10
    ${环境温度}    获取web实时数据    环境温度
    sleep    3m
    wait until keyword succeeds    3m    2    判断告警不存在    负载低温下电
    Wait Until Keyword Succeeds    ${等待时间}s    2    信号量数据值为    负载低温下电控制状态    恢复
    显示属性配置    负载低温下电控制状态    数字量    web_attr=Off    gui_attr=Off
    关闭负载输出
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}
    ...    0    1    系统运行环境    环境温度
    ...    AND    设置web设备参数量为默认值    负载低温下电温度    负载低温下电使能
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出
