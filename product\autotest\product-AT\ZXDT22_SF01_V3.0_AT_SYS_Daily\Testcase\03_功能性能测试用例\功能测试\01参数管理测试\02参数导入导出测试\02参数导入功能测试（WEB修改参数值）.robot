*** Settings ***
Documentation     WEB上修改参数设置值，导入参数文件后，参数设置值恢复为参数文件中参数值。
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
显示属性参数导入功能测试
    [Setup]
    连接CSU
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    power_para.zip
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    ${channel_config}    显示属性配置    CPU利用率    模拟量    ON    ON    #初始值为OFF，不显示
    @{channel_config}    获取显示属性配置    CPU利用率    模拟量
    should be equal    @{channel_config}[0]    ON
    should be equal    @{channel_config}[1]    ON
    ${channel_config}    显示属性配置    <<禁止所有告警~0x1001040010001>>    控制量    OFF    OFF
    @{channel_config}    获取显示属性配置    <<禁止所有告警~0x1001040010001>>    控制量
    should be equal    @{channel_config}[0]    OFF
    should be equal    @{channel_config}[1]    OFF
    Comment    ${channel_config}    显示属性配置    站点名称    参数量    OFF    OFF    #站点名称需要继承
    Comment    @{channel_config}    获取显示属性配置    站点名称    参数量
    Comment    should be equal    @{channel_config}[0]    OFF
    Comment    should be equal    @{channel_config}[1]    OFF
    ${channel_config}    显示属性配置    相电压UL1    模拟量    OFF    OFF
    @{channel_config}    获取显示属性配置    相电压UL1    模拟量
    should be equal    @{channel_config}[0]    OFF
    should be equal    @{channel_config}[1]    OFF
    ${channel_config}    显示属性配置    交流防雷器状态    数字量    ON    ON
    @{channel_config}    获取显示属性配置    交流防雷器状态    数字量
    should be equal    @{channel_config}[0]    ON
    should be equal    @{channel_config}[1]    ON
    ${channel_config}    显示属性配置    市电输入状态    数字量    OFF    OFF
    @{channel_config}    获取显示属性配置    市电输入状态    数字量
    should be equal    @{channel_config}[0]    OFF
    should be equal    @{channel_config}[1]    OFF
    ${channel_config}    显示属性配置    整流器输出电压    模拟量    OFF    OFF
    @{channel_config}    获取显示属性配置    整流器输出电压    模拟量
    should be equal    @{channel_config}[0]    OFF
    should be equal    @{channel_config}[1]    OFF
    ${channel_config}    显示属性配置    电池组总电流    模拟量    OFF    OFF
    @{channel_config}    获取显示属性配置    电池组总电流    模拟量
    should be equal    @{channel_config}[0]    OFF
    should be equal    @{channel_config}[1]    OFF
    ${channel_config}    显示属性配置    直流电压    模拟量    OFF    OFF
    @{channel_config}    获取显示属性配置    直流电压    模拟量
    should be equal    @{channel_config}[0]    OFF
    should be equal    @{channel_config}[1]    OFF
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    power_para.zip
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    @{channel_config}    获取显示属性配置    CPU利用率    模拟量
    should be equal    @{channel_config}[0]    OFF
    should be equal    @{channel_config}[1]    OFF
    @{channel_config}    获取显示属性配置    <<禁止所有告警~0x1001040010001>>    控制量
    should be equal    @{channel_config}[0]    ON
    should be equal    @{channel_config}[1]    ON
    Comment    @{channel_config}    获取显示属性配置    站点名称    参数量    #站点名称需要继承
    Comment    should be equal    @{channel_config}[0]    ON
    Comment    should be equal    @{channel_config}[1]    ON
    @{channel_config}    获取显示属性配置    相电压UL1    模拟量
    should be equal    @{channel_config}[0]    ON
    should be equal    @{channel_config}[1]    ON
    @{channel_config}    获取显示属性配置    交流防雷器状态    数字量
    should be equal    @{channel_config}[0]    OFF
    should be equal    @{channel_config}[1]    OFF
    @{channel_config}    获取显示属性配置    市电输入状态    数字量
    should be equal    @{channel_config}[0]    ON
    should be equal    @{channel_config}[1]    ON
    @{channel_config}    获取显示属性配置    整流器输出电压    模拟量
    should be equal    @{channel_config}[0]    ON
    should be equal    @{channel_config}[1]    ON
    @{channel_config}    获取显示属性配置    电池组总电流    模拟量
    should be equal    @{channel_config}[0]    ON
    should be equal    @{channel_config}[1]    ON
    @{channel_config}    获取显示属性配置    直流电压    模拟量
    should be equal    @{channel_config}[0]    ON
    should be equal    @{channel_config}[1]    ON

告警参数导入功能测试
    [Setup]
    连接CSU    #要先恢复所有参数
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    power_para.zip
    sleep    4m
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    Wait Until Keyword Succeeds    60    2    设置web参数量    <<禁止所有告警~0x1001030010001>>    次要
    ${告警级别获取}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    60    2    设置web参数量    系统过载告警    次要
    ${告警级别获取}    获取web参数量    系统过载告警
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    60    2    设置web参数量    交流停电    次要
    ${告警级别获取}    获取web参数量    交流停电
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    60    2    设置web参数量    市电频率高    次要
    ${告警级别获取}    获取web参数量    市电频率高
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    60    2    设置web参数量    整流器告警    次要
    ${告警级别获取}    获取web参数量    整流器告警
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    60    2    设置web参数量    多个整流器模块告警    次要
    ${告警级别获取}    获取web参数量    多个整流器模块告警
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    60    2    设置web参数量    一次下电告警    次要
    ${告警级别获取}    获取web参数量    一次下电告警
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    60    2    设置web参数量    电池温度高    次要
    ${告警级别获取}    获取web参数量    电池温度高
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    60    2    设置web参数量    直流防雷器异常    次要
    ${告警级别获取}    获取web参数量    直流防雷器异常
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    60    2    设置web参数量    烟雾告警    次要
    ${告警级别获取}    获取web参数量    烟雾告警
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    10    2    设置web参数量    烟雾告警干接点    1
    Wait Until Keyword Succeeds    10    2    设置web参数量    直流防雷器异常干接点    2
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池温度高干接点    3
    Wait Until Keyword Succeeds    10    2    设置web参数量    多个整流器模块告警干接点    4
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流停电干接点    5
    Wait Until Keyword Succeeds    10    2    设置web参数量    系统过载告警干接点    6
    sleep    10
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    power_para.zip
    sleep    4m
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    ${告警级别获取}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    should be true    '${告警级别获取}'=='严重'
    ${告警级别获取}    获取web参数量    系统过载告警
    should be true    '${告警级别获取}'=='严重'
    ${告警级别获取}    获取web参数量    交流停电
    should be true    '${告警级别获取}'=='主要'
    ${告警级别获取}    获取web参数量    市电频率高
    should be true    '${告警级别获取}'=='主要'
    ${告警级别获取}    获取web参数量    整流器告警
    should be true    '${告警级别获取}'=='主要'
    ${告警级别获取}    获取web参数量    多个整流器模块告警
    should be true    '${告警级别获取}'=='主要'
    ${告警级别获取}    获取web参数量    一次下电告警
    should be true    '${告警级别获取}'=='严重'
    ${告警级别获取}    获取web参数量    电池温度高
    should be true    '${告警级别获取}'=='主要'
    ${告警级别获取}    获取web参数量    直流防雷器异常
    should be true    '${告警级别获取}'=='主要'
    ${告警级别获取}    获取web参数量    烟雾告警
    should be true    '${告警级别获取}'=='主要'
    ${告警干接点获取}    获取web参数量    烟雾告警干接点
    should be true    ${告警干接点获取}==0
    ${告警干接点获取}    获取web参数量    直流防雷器异常干接点
    should be true    ${告警干接点获取}==0
    ${告警干接点获取}    获取web参数量    电池温度高干接点
    should be true    ${告警干接点获取}==0
    ${告警干接点获取}    获取web参数量    多个整流器模块告警干接点
    should be true    ${告警干接点获取}==0
    ${告警干接点获取}    获取web参数量    交流停电干接点
    should be true    ${告警干接点获取}==0
    ${告警干接点获取}    获取web参数量    系统过载告警干接点
    should be true    ${告警干接点获取}==0

用户参数导入功能测试
    [Setup]
    连接CSU
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    power_para.zip
    sleep    4m
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    Wait Until Keyword Succeeds    10    2    设置web参数量    CPU利用率高阈值    10    #下限
    ${CPU利用率高阈值获取}    获取web参数量    CPU利用率高阈值
    should be true    ${CPU利用率高阈值获取}==10
    Wait Until Keyword Succeeds    10    2    设置web参数量    系统过载告警阈值    10    #下限
    ${系统过载告警阈值获取}    获取web参数量    系统过载告警阈值
    should be true    ${系统过载告警阈值获取}==10
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流电流高阈值    600    #上限
    ${交流电流高阈值获取}    获取web参数量    交流电流高阈值
    Should Be Equal    ${交流电流高阈值获取}    600.0
    Wait Until Keyword Succeeds    10    2    设置web参数量    节能带载率上限    1
    ${节能带载率上限}    获取web参数量    节能带载率上限
    should be equal as numbers    ${节能带载率上限}    1
    Wait Until Keyword Succeeds    10    2    设置web参数量    环境温度过高阈值    63
    ${环境温度过高阈值}    获取web参数量    环境温度过高阈值
    should be true    ${环境温度过高阈值}==63
    Wait Until Keyword Succeeds    10    2    设置web参数量    直流电压高阈值    58
    ${直流电压高阈值}    获取web参数量    直流电压高阈值
    should be true    ${直流电压高阈值}==58
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池电压低阈值    48
    ${电池电压低阈值获取}    获取web参数量    电池电压低阈值
    should be true    ${电池电压低阈值获取}==48.0
    Wait Until Keyword Succeeds    10    2    设置web参数量    浮充电压    53.8    #中间值
    ${浮充电压值获取}    获取web参数量    浮充电压
    should be true    ${浮充电压值获取}==53.8
    Comment    Wait Until Keyword Succeeds    10    2    设置web参数量    站点名称    Site-X#8    #站点名称需要继承
    Comment    ${站点名称获取}    获取web参数量    站点名称
    Comment    should be true    '${站点名称获取}'=='Site-X#8'
    Wait Until Keyword Succeeds    10    2    设置web参数量    市电配置_1    无
    ${获取值}    获取web参数量    市电配置_1
    should be true    '${获取值}'=='无'
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池组容量_1    200    #中间值
    ${电池组容量值获取}    获取web参数量    电池组容量_1
    ${电池组容量值获取}    create list    ${电池组容量值获取}
    ${电池组容量值确认}    create list    200
    should be true    ${电池组容量值获取}==${电池组容量值确认}
    Wait Until Keyword Succeeds    10    2    设置web参数量    <<电池启用日期-1~0xb001050010001>>    2000-01-01
    ${获取值}    获取web参数量    <<电池启用日期-1~0xb001050010001>>
    should be true    ${获取值}==2000-01-01
    Wait Until Keyword Succeeds    10    2    设置web参数量    整流器最小开机数量    1
    ${整流器最小开机数量}    获取web参数量    整流器最小开机数量
    should be equal    ${整流器最小开机数量}    1
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    power_para.zip
    sleep    4m
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    ${CPU利用率高阈值获取}    获取web参数量    CPU利用率高阈值
    ${缺省值}    获取web参数上下限范围    CPU利用率高阈值
    should be true    ${CPU利用率高阈值获取}==@{缺省值}[0]
    ${系统过载告警阈值获取}    获取web参数量    系统过载告警阈值
    ${缺省值}    获取web参数上下限范围    系统过载告警阈值
    should be true    ${系统过载告警阈值获取}==@{缺省值}[0]
    ${交流电流高阈值获取}    获取web参数量    交流电流高阈值
    ${缺省值}    获取web参数上下限范围    交流电流高阈值
    Should Be Equal    ${交流电流高阈值获取}    @{缺省值}[0]
    ${节能带载率上限}    获取web参数量    节能带载率上限
    ${缺省值}    获取web参数上下限范围    节能带载率上限
    should be equal as numbers    ${节能带载率上限}    @{缺省值}[0]
    ${环境温度过高阈值}    获取web参数量    环境温度过高阈值
    ${缺省值}    获取web参数上下限范围    环境温度过高阈值
    should be true    ${环境温度过高阈值}==@{缺省值}[0]
    ${直流电压高阈值}    获取web参数量    直流电压高阈值
    ${缺省值}    获取web参数上下限范围    直流电压高阈值
    should be true    ${直流电压高阈值}==@{缺省值}[0]
    ${浮充电压值获取}    获取web参数量    浮充电压
    ${缺省值}    获取web参数上下限范围    浮充电压
    should be true    ${浮充电压值获取}==@{缺省值}[0]
    Comment    ${站点名称获取}    获取web参数量    站点名称    #站点名称需要继承
    Comment    ${缺省值}    获取web参数上下限范围    站点名称
    Comment    Should Be Equal    ${站点名称获取}    @{缺省值}[0]
    ${获取值}    获取web参数量    市电配置_1
    Should Be Equal    ${获取值}    有
    ${电池组容量值获取}    获取web参数量    电池组容量_1
    ${缺省值}    获取web参数上下限范围    电池组容量
    should be true    ${电池组容量值获取}==@{缺省值}[0]
    ${获取值}    获取web参数量    <<电池启用日期-1~0xb001050010001>>
    ${缺省值}    获取web参数上下限范围    <<电池启用日期~0xb001050010001>>
    should be true    ${获取值}==@{缺省值}[0]
    ${整流器最小开机数量}    获取web参数量    整流器最小开机数量
    ${缺省值}    获取web参数上下限范围    整流器最小开机数量
    should be equal    ${整流器最小开机数量}    @{缺省值}[0]
    ${获取值}    获取web参数量    电池电压低阈值
    ${缺省值}    获取web参数上下限范围    电池电压低阈值
    should be true    ${获取值}==@{缺省值}[0]

各类参数文件导入功能测试
    [Setup]
    连接CSU
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    power_para.zip
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    Wait Until Keyword Succeeds    30    2    设置web参数量    <<禁止所有告警~0x1001030010001>>    次要
    ${告警级别获取}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    30    2    设置web参数量    系统过载告警    次要
    ${告警级别获取}    获取web参数量    系统过载告警
    should be true    '${告警级别获取}'=='次要'
    Comment    Wait Until Keyword Succeeds    30    2    设置web参数量    站点名称    Site-X#8    #站点名称需要继承
    Comment    ${站点名称获取}    获取web参数量    站点名称
    Comment    should be true    '${站点名称获取}'=='Site-X#8'
    Wait Until Keyword Succeeds    30    2    设置web参数量    市电配置_1    无
    ${市电配置获取}    获取web参数量    市电配置_1
    should be true    '${市电配置获取}'=='无'
    Wait Until Keyword Succeeds    30    2    设置web参数量    CPU利用率高阈值    10    #下限
    ${CPU占用率高阈值获取}    获取web参数量    CPU利用率高阈值
    should be true    ${CPU占用率高阈值获取}==10
    Wait Until Keyword Succeeds    30    2    设置web参数量    系统过载告警阈值    10    #下限
    ${系统过载告警阈值获取}    获取web参数量    系统过载告警阈值
    should be true    ${系统过载告警阈值获取}==10
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    power_para.zip
    sleep    10
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    10
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    ${告警级别获取}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    should be true    '${告警级别获取}'=='严重'
    ${告警级别获取}    获取web参数量    系统过载告警
    should be true    '${告警级别获取}'=='严重'
    Comment    ${站点名称获取}    获取web参数量    站点名称    #站点名称需要继承
    Comment    ${缺省值}    获取web参数上下限范围    站点名称
    Comment    Should Be Equal    ${站点名称获取}    @{缺省值}[0]
    ${获取值}    获取web参数量    市电配置_1
    Should Be Equal    ${获取值}    有
    ${CPU利用率高阈值获取}    获取web参数量    CPU利用率高阈值
    ${缺省值}    获取web参数上下限范围    CPU利用率高阈值
    should be true    ${CPU利用率高阈值获取}==@{缺省值}[0]
    ${系统过载告警阈值获取}    获取web参数量    系统过载告警阈值
    ${缺省值}    获取web参数上下限范围    系统过载告警阈值
    should be true    ${系统过载告警阈值获取}==@{缺省值}[0]
