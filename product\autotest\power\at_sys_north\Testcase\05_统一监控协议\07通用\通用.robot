*** Settings ***
Test Setup        Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
uniform_0002_时间信息√
    ${1104数据}    统一监控协议_获取数据    获取系统时间
    ${web数据}    获取系统时间
    ${对比结果}    uniform_protocol_Test.judge_time_is_equ    ${1104数据}    ${web数据}
    should be true    ${对比结果}
    ${设置时间}    Add Time To Date    ${web数据}    10m
    ${设置结果}    统一监控协议_设置数据    设置系统时间    系统时间    ${设置时间}
    ${1104数据}    统一监控协议_获取数据    获取系统时间
    ${web数据}    获取系统时间
    ${对比结果}    uniform_protocol_Test.judge_time_is_equ    ${1104数据}    ${web数据}
    should be true    ${对比结果}
    ${同步时间}    同步系统时间

uniform_0004_厂家信息√
    ${协议数据}    统一监控协议_获取数据    厂家信息
    ${web数据}    统一监控协议_获取web对应数据    ${协议数据}
    ${对比结果}    批量对比数据_统一监控协议_WEB    ${协议数据}    ${web数据}    厂家信息
    should be true    ${对比结果}

uniform_0006_远程复位√
    ${设置结果}    统一监控协议_设置数据    远程复位    远程复位    ${empty}    ctrl
    sleep    2m
    连接CSU
    @{历史事件内容}    获取web历史事件内容    ${empty}    ${empty}    所有    1    10
    ${是否复位}    Set Variable    False
    FOR    ${i}    IN    ${历史事件内容}
        ${是否复位}    Evaluate    "远程控制复位" in "${i}"
    END
    Should Be True    ${是否复位}

uniform_0008_获取议版本信息
    ${1104数据}    统一监控协议_获取数据    获取议版本信息
    Log    ${1104数据}
