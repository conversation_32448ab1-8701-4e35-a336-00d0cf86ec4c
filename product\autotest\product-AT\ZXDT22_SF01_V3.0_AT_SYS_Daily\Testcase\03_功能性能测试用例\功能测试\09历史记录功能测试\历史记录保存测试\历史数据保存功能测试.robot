*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
历史数据记录内容测试
    [Setup]    测试用例前置条件
    连接CSU
    ${数量}    获取web历史数据数量    ${empty}    ${empty}
    run keyword if    ${数量}>199900    删除历史记录    历史数据
    ${起始时间}    获取系统时间
    Wait Until Keyword Succeeds    10    2    设置web控制量    CAN总线设备统计
    Wait Until Keyword Succeeds    10    2    设置web控制量    RS485总线设备统计
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    10    1    设置web参数量    历史数据保存间隔    5
    sleep    8m
    ${结束时间}    获取系统时间
    ${数量}    获取web历史数据数量    ${empty}    ${empty}
    ${历史数据内容}    获取web历史数据内容_指定名称    ${empty}    ${empty}    所有    所有    1    200    整流器休眠状态
    @{历史数据内容1}    split string    ${历史数据内容}    ,
    should be true    @{历史数据内容1}[0] <= ${数量}
    should contain    @{历史数据内容1}[2]    整流器休眠状态
    should be true    '@{历史数据内容1}[3]'== '0' or '@{历史数据内容1}[3]'== '1'
    ${告警结束时间差}    subtract date from date    @{历史数据内容1}[1]    ${结束时间}
    should be true    -480<${告警结束时间差}<480
    ${历史数据内容2}    获取web历史数据内容_指定名称    ${empty}    ${empty}    所有    所有    1    200    电池电压
    @{历史数据内容2}    split string    ${历史数据内容2}    ,
    should be equal    @{历史数据内容2}[4]    V

历史数据增加功能测试（时间间隔到）
    [Setup]    测试用例前置条件
    连接CSU
    ${数量}    获取web历史数据数量    ${empty}    ${empty}
    run keyword if    ${数量}>199900    删除历史记录    历史数据
    Wait Until Keyword Succeeds    10    2    设置web控制量    CAN总线设备统计
    Wait Until Keyword Succeeds    10    2    设置web控制量    RS485总线设备统计
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
    设置web参数量    电池组容量_${i}    0
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    100
    ${起始时间}    获取系统时间
    Wait Until Keyword Succeeds    10    1    设置web参数量    历史数据保存间隔    5
    sleep    6m
    ${结束时间}    获取系统时间
    ${数量1}    获取web历史数据数量    ${起始时间}    ${结束时间}
    should be true    ${数量1} >= 119
    ${数量2}    获取web历史数据数量    ${empty}    ${empty}
    ${历史数据内容}    获取web历史数据内容_指定名称    ${empty}    ${empty}    所有    所有    1    200    整流器休眠状态
    @{历史数据内容1}    split string    ${历史数据内容}    ,
    should be true    @{历史数据内容1}[0] <= ${数量2}
    should contain    @{历史数据内容1}[2]    整流器休眠状态
    should be true    @{历史数据内容1}[3] >= 0
    ${告警结束时间差}    subtract date from date    @{历史数据内容1}[1]    ${结束时间}
    should be true    -480<${告警结束时间差}<480

历史数据增加功能测试（告警变化）
    [Setup]    测试用例前置条件
    连接CSU
    ${数量}    获取web历史数据数量    ${empty}    ${empty}
    run keyword if    ${数量}>199900    删除历史记录    历史数据
    Wait Until Keyword Succeeds    10    2    设置web控制量    CAN总线设备统计
    Wait Until Keyword Succeeds    10    2    设置web控制量    RS485总线设备统计
    ${状态}    获取web实时数据    电池管理状态
    Wait Until Keyword Succeeds    10    1    设置web参数量    历史数据保存间隔    360
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
    设置web参数量    电池组容量_${i}    0
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    99
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_2    100
    ${起始时间}    获取系统时间
    设置web参数量    电池温度无效    主要
    wait until keyword succeeds    1m    1    查询指定告警信息    电池温度无效-2
    sleep    2m
    ${结束时间}    获取系统时间
    ${数量1}    获取web历史数据数量    ${起始时间}    ${结束时间}
    should be true    ${数量1} >= 123
    ${数量2}    获取web历史数据数量    ${empty}    ${empty}
    ${历史数据内容}    获取web历史数据内容_指定名称    ${empty}    ${empty}    所有    所有    1    200    整流器休眠状态
    @{历史数据内容1}    split string    ${历史数据内容}    ,
    should be true    @{历史数据内容1}[0] <= ${数量2}
    should contain    @{历史数据内容1}[2]    整流器休眠状态
    should be true    @{历史数据内容1}[3] == 0 or @{历史数据内容1}[3] == 1
    ${告警结束时间差}    subtract date from date    @{历史数据内容1}[1]    ${结束时间}
    should be true    -480<${告警结束时间差}<480
    [Teardown]    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_2    0

历史数据增加功能测试（电池管理状态改变）
    [Setup]    测试用例前置条件
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    sleep    5
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
    设置web参数量    电池组容量_${i}    0
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    100
    ${数量}    获取web历史数据数量    ${empty}    ${empty}
    run keyword if    ${数量}>199900    删除历史记录    历史数据
    Wait Until Keyword Succeeds    10    2    设置web控制量    CAN总线设备统计
    Wait Until Keyword Succeeds    10    2    设置web控制量    RS485总线设备统计
    Wait Until Keyword Succeeds    10    1    设置web参数量    历史数据保存间隔    360
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    浮充    #浮充
    sleep    2m
    ${起始时间}    获取系统时间
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动均充
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    均充
    sleep    2m
    ${结束时间}    获取系统时间
    ${数量1}    获取web历史数据数量    ${起始时间}    ${结束时间}
    should be true    ${数量1} >= 119
    ${数量2}    获取web历史数据数量    ${empty}    ${empty}
    ${历史数据内容}    获取web历史数据内容_指定名称    ${empty}    ${empty}    所有    所有    1    200    整流器休眠状态
    @{历史数据内容1}    split string    ${历史数据内容}    ,
    should be true    @{历史数据内容1}[0] <= ${数量2}
    should contain    @{历史数据内容1}[2]    整流器休眠状态
    should be true    @{历史数据内容1}[3] == 0 or @{历史数据内容1}[3] == 1
    ${告警结束时间差}    subtract date from date    @{历史数据内容1}[1]    ${结束时间}
    should be true    -480<${告警结束时间差}<480
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    浮充    #浮充

系统时间更改对历史数据的影响
    [Setup]    测试用例前置条件
    连接CSU
    ${数量}    获取web历史数据数量    ${empty}    ${empty}
    run keyword if    ${数量}>199900    删除历史记录    历史数据
    Wait Until Keyword Succeeds    10    2    设置web控制量    CAN总线设备统计
    Wait Until Keyword Succeeds    10    2    设置web控制量    RS485总线设备统计
    ${状态}    获取web实时数据    电池管理状态
    Wait Until Keyword Succeeds    10    1    设置web参数量    历史数据保存间隔    360
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
    设置web参数量    电池组容量_${i}    0
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    99
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_2    100
    ${起始时间}    获取系统时间
    设置web参数量    电池温度无效    主要
    wait until keyword succeeds    1m    1    查询指定告警信息    电池温度无效-2
    sleep    2m
    ${结束时间}    获取系统时间
    ${数量1}    获取web历史数据数量    ${起始时间}    ${结束时间}
    should be true    ${数量1} >= 123
    ${数量2}    获取web历史数据数量    ${empty}    ${empty}
    ${历史数据内容}    获取web历史数据内容_指定名称    ${empty}    ${empty}    所有    所有    1    200    整流器休眠状态
    @{历史数据内容1}    split string    ${历史数据内容}    ,
    should be true    @{历史数据内容1}[0] <= ${数量2}
    should contain    @{历史数据内容1}[2]    整流器休眠状态
    should be true    @{历史数据内容1}[3] == 0 or @{历史数据内容1}[3] == 1
    ${告警结束时间差}    subtract date from date    @{历史数据内容1}[1]    ${结束时间}
    should be true    -480<${告警结束时间差}<480
    ${当前时间}    获取系统时间
    ${调整时间}    add time to date    ${当前时间}    20d    exclude_millis=yes
    sleep    5
    设置系统时间    ${调整时间}
    ${历史数据内容}    获取web历史数据内容_指定名称    ${empty}    ${empty}    所有    所有    1    200    整流器休眠状态
    @{历史数据内容3}    split string    ${历史数据内容}    ,
    should be equal    @{历史数据内容1}[2]    @{历史数据内容3}[2]
    should be equal    @{历史数据内容1}[1]    @{历史数据内容3}[1]
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_2    0
    ...    AND    同步系统时间

系统断电历史数据保存测试
    [Setup]    测试用例前置条件
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    ${数量}    获取web历史数据数量    ${empty}    ${empty}
    run keyword if    ${数量}>199900    删除历史记录    历史数据
    ${系统时间1}    获取系统时间
    Wait Until Keyword Succeeds    10    2    设置web控制量    CAN总线设备统计
    Wait Until Keyword Succeeds    10    2    设置web控制量    RS485总线设备统计
    ${起始时间}    subtract time from date    ${系统时间1}    30d    exclude_millis=yes    #30天以前的时间
    ${终止时间1}    获取系统时间
    ${历史数据数量1}    获取web历史数据数量    ${起始时间}    ${终止时间1}
    log    ${历史数据数量1}
    ${实时告警数量}    获取web告警数量
    关闭交流源输出
    关闭电池模拟器输出
    sleep    5
    打开交流源输出
    打开电池模拟器输出
    Comment    连接CSU
    实时告警刷新完成
    sleep    5
    ${终止时间2}    获取系统时间
    ${历史数据数量2}    获取web历史数据数量    ${起始时间}    ${终止时间2}
    should be true    ${历史数据数量2}>${历史数据数量1}

历史数据最大条数测试
    [Setup]    测试用例前置条件
    连接CSU
    sleep    20
    ${起始时间}    获取系统时间
    #产生WEB登录历史事件    #安全事件
    FOR    ${k}    IN RANGE    200001
    Wait Until Keyword Succeeds    30    1    设置web控制量    <<禁止所有告警~0x1001040010001>>
    Wait Until Keyword Succeeds    30    1    查询指定告警信息    禁止所有告警
    ${历史数据数量}    获取web历史数据数量    ${empty}    ${empty}
    Wait Until Keyword Succeeds    30    1    设置web控制量    允许所有告警
    exit for loop if    ${历史数据数量}>=200000
    sleep    2m
    ${历史数据数量1}    获取web历史数据数量    ${empty}    ${empty}
    should be true    ${历史数据数量1} == 200000

历史数据循环覆盖测试
    [Documentation]    前面一个用例将历史告警产生满。
    [Setup]    测试用例前置条件
    连接CSU
    ${起始时间}    获取系统时间
    ${历史告警数量}    获取web历史数据数量    ${empty}    ${empty}
    should be true    ${历史告警数量} >= 200000
    Wait Until Keyword Succeeds    10    2    设置web控制量    CAN总线设备统计
    Wait Until Keyword Succeeds    10    2    设置web控制量    RS485总线设备统计
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    10    1    设置web参数量    历史数据保存间隔    5
    sleep    8m
    ${结束时间}    获取系统时间
    ${数量}    获取web历史数据数量    ${empty}    ${empty}
    @{历史数据内容1}    获取web历史数据内容    ${empty}    ${empty}    all    all    1    1
    Comment    @{历史数据内容1}    split string    @{历史数据内容}[-1]    ,
    should be true    @{历史数据内容1}[0] == ${数量}
    should contain    @{历史数据内容1}[2]    整流器休眠状态
    should be true    @{历史数据内容1}[3] == 0 or @{历史数据内容1}[3] == 1
    ${告警结束时间差}    subtract date from date    @{历史数据内容1}[1]    ${结束时间}
    should be true    -480<${告警结束时间差}<480
    [Teardown]    设置通道配置    UIB_X7_T4    0    1    系统运行环境    环境温度
