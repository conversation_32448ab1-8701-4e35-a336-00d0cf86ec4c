*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
批量获取SDDU模拟量
    [Documentation]    智能直流配电单元 _1 直流电压 0.0
    ...    智能直流配电单元 _1 配电单元电流[1] 3000.0
    ...    智能直流配电单元 _1 配电单元电流[2] 3000.0
    ...    智能直流配电单元 _1 配电单元电量[1] 0.0
    ...    智能直流配电单元 _1 配电单元电量[2] 0.0
    ...    智能直流配电单元 _1 配电单元负载功率[1] 0.0
    ...    智能直流配电单元 _1 配电单元负载功率[2] 0.0
    ...    智能直流配电单元 _1 SDU2总电流 60000.0
    ...
    ...
    ...    直流电压~0x22001010010001
    写入CSV文档    SDU2模拟量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=智能直流配电单元
    @{排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SDU2排除模拟量信号}    ${排除列表}    1
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    智能直流配电单元    analog data    ${SDU2排除模拟量信号}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    # Log many    ${power_sm待测}
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    1    power_sm
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    power_sm    SDU2       呼叫    ${缺省值列表}    SDDU模拟量获取测试    获取SDDU模拟量    
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    2    power_sm
    Run Keyword And Continue On Failure   南向子设备模拟量获取值列表封装判断结果     power_sm    SDU2       呼叫    ${缺省值列表}    SDDU模拟量获取测试    获取SDDU模拟量    
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    0    power_sm
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    power_sm    SDU2       呼叫    ${缺省值列表}    SDDU模拟量获取测试    获取SDDU模拟量  




    


获取SDDU总电流
    连接CSU
    设置子工具值    SDU2    all    呼叫    SDU负载电流1    3000
    设置子工具值    SDU2    all    呼叫    SDU负载电流2    3000
    FOR    ${直流配电单元序号}    IN RANGE    1    3
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    SDDU总电流-${直流配电单元序号}    6000
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取SDDU模拟量    SDDU总电流-${直流配电单元序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    6000
    END
    设置子工具值    SDU2    all    呼叫    SDU负载电流1    0
    设置子工具值    SDU2    all    呼叫    SDU负载电流2    0
    FOR    ${直流配电单元序号}    IN RANGE    1    3
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    SDDU总电流-${直流配电单元序号}    0
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取SDDU模拟量    SDDU总电流-${直流配电单元序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    0
    END

获取配电单元负载功率
    连接CSU
    设置子工具值    SDU2    all    呼叫    SDU负载电流1    3000
    设置子工具值    SDU2    all    呼叫    SDU负载电流2    3000
    设置子工具值    SDU2    all    呼叫    SDU电压    60
    FOR    ${直流配电单元序号}    IN RANGE    1    3
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    配电单元负载功率_1-${直流配电单元序号}    180
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    配电单元负载功率_2-${直流配电单元序号}    180
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取SDDU模拟量    配电单元负载功率_1-${直流配电单元序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    180
    END
    设置子工具值    SDU2    all    呼叫    SDU负载电流1    0
    设置子工具值    SDU2    all    呼叫    SDU负载电流2    0
    FOR    ${直流配电单元序号}    IN RANGE    1    3
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    配电单元负载功率_1-${直流配电单元序号}    0
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    配电单元负载功率_2-${直流配电单元序号}    0
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取SDDU模拟量    配电单元负载功率_1-${直流配电单元序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    0
    END
