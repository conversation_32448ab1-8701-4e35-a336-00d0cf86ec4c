*** Settings ***
Default Tags      trap
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
snmp批量获取状态量和SMR告警测试
    写入CSV文档    整流器数字量和告警量获取测试    信号名称    信号值    结果
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${级别设置值}    获取web参数量    整流器告警
    run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    整流器告警    严重
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=整流器
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除告警量信号}    ${排除列表}    1    ${模拟整流器地址}    1
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    rectifier    alarm    True    0    1
    Comment    ${待测数据长度}    Get Length    ${snmp待测}
    Comment    ${待测}    Evaluate    ${待测数据长度}-1
    Comment    ${待测索引}    Evaluate    random.randint(0,${待测})    random
    FOR    ${i}    IN    @{snmp待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    signal_node
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        Run Keyword And Continue On Failure    整流器或PU告警量产生封装判断结果    snmp    SMR4000    ${信号名称}    数字量    ${告警产生}    ${模拟整流器地址}    整流器告警    整流器数字量和故障量获取测试    整流器    ${节点名称}    ${信号序号}    null    null    null
        Run Keyword And Continue On Failure    整流器或PU告警量恢复封装判断结果    snmp    SMR4000    ${信号名称}    数字量    ${告警恢复}    ${模拟整流器地址}    整流器告警    整流器数字量和故障量获取测试    整流器    ${节点名称}    ${信号序号}    null    null    null
    END
    断开连接SNMP

snmp批量获取状态量和SMR故障测试
    写入CSV文档    整流器数字量和故障量获取测试    信号名称    信号值    结果
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${级别设置值}    获取web参数量    整流器故障
    run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    整流器故障    严重
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=整流器
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除故障量信号}    ${排除列表}    1    ${模拟整流器地址}    1
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    rectifier    alarm    True    0    2
    Comment    ${待测数据长度}    Get Length    ${snmp待测}
    Comment    ${待测}    Evaluate    ${待测数据长度}-1
    Comment    ${待测索引}    Evaluate    random.randint(0,${待测})    random
    FOR    ${i}    IN    @{snmp待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    signal_node
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        Run Keyword And Continue On Failure    整流器或PU故障量产生封装判断结果    snmp    SMR4000    ${信号名称}    数字量    ${告警产生}    ${模拟整流器地址}    整流器故障    整流器数字量和故障量获取测试    整流器    ${节点名称}    ${信号序号}    null    null    null
        Run Keyword And Continue On Failure    整流器或PU故障量恢复封装判断结果    snmp    SMR4000    ${信号名称}    数字量    ${告警恢复}    ${模拟整流器地址}    整流器故障    整流器数字量和故障量获取测试    整流器    ${节点名称}    ${信号序号}    null    null    null
    END
    断开连接SNMP

snmp整流器通讯中断
    连接CSU
    ${级别设置值}    获取web参数量    整流器通讯中断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    整流器通讯中断    严重
    ${告警级别取值约定dict}    获取web参数的取值约定    整流器通讯中断
    ${模拟一个时的起始号}    evaluate    ${模拟整流器开始地址}+1
    ${整流中断个数}    evaluate    str(${北向协议SMR最大数}-${测试环境真实整流个数}-1)
    设置子工具个数    SMR4000    ${整流中断个数}
    wait until keyword succeeds    2m    1    判断告警存在    整流器通讯中断-${北向协议SMR最大数}
    ${snmp英文名}    获取snmp单个告警英文名    整流器通讯中断_${北向协议SMR最大数}
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    ${SMR通信恢复}    evaluate    str(${北向协议SMR最大数}-${测试环境真实整流个数})
    #告警消失
    设置子工具个数    SMR4000    ${SMR通信恢复}
    wait until keyword succeeds    2m    1    判断告警不存在    整流器通讯中断-${北向协议SMR最大数}
    ${snmp英文名}    获取snmp单个告警英文名    整流器通讯中断_${北向协议SMR最大数}
    ${告警消失}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警消失}
    wait until keyword succeeds    30m    2    查询指定告警信息不为    整流器通讯中断
    [Teardown]    #run keywords    设置子工具个数    SMR4000    45    # AND    sleep    3m

snmp整流器风扇故障
    连接CSU
    ${级别设置值}    获取web参数量    整流器风扇故障
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    整流器风扇故障    严重
    ${告警级别取值约定dict}    获取web参数的取值约定    整流器风扇故障
    @{SMR序号随机list}    从1-15个数中随机选n个不重复的单体    ${北向协议SMR最大数}    3    ${模拟整流器地址}
    FOR    ${SMR序号}    IN    @{SMR序号随机list}
        ${SMR地址}    Evaluate    ${SMR序号}-${模拟整流器地址}+1
        ${SMR地址}    Convert to string    ${SMR地址}
        设置子工具值    SMR4000    ${SMR地址}    数字量    整流器风扇故障状态    1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    整流器风扇故障状态-${SMR序号}    异常
        wait until keyword succeeds    2m    1    判断告警存在    整流器风扇故障-${SMR序号}
        ${snmp英文名}    获取snmp单个告警英文名    整流器风扇故障_${SMR序号}
        ${告警产生}    判断Trap告警产生    ${snmp英文名}
        should be true    ${告警产生}
        设置子工具值    SMR4000    ${SMR地址}    数字量    整流器风扇故障状态    0
        Wait Until Keyword Succeeds    5m    1    信号量数据值为    整流器风扇故障状态-${SMR序号}    正常
        wait until keyword succeeds    2m    1    判断告警不存在    整流器风扇故障-${SMR序号}
        ${snmp英文名}    获取snmp单个告警英文名    整流器风扇故障_${SMR序号}
        ${告警消失}    判断Trap告警消失    ${snmp英文名}
    END
