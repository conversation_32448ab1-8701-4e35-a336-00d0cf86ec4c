*** Settings ***
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
01 一次下电告警参数设置测试
    [Documentation]    200：操作日志。
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    一次下电告警
    ${一次下电告警级别获取原值}    获取web参数量    一次下电告警
    FOR    ${VAR}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    10    2    设置web参数量    一次下电告警    ${VAR}
        sleep    5
        ${一次下电告警级别获取}    获取web参数量    一次下电告警
        should be equal    ${一次下电告警级别获取}    ${VAR}
        ${起始时间}    ${结束时间}    获取起始结束时间段    9
        ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    一次下电告警
        run keyword if    '${一次下电告警级别获取原值}' !='${一次下电告警级别获取}'    should be true    6>=${获取历史事件数量}>=1
        ...    ELSE    should be true    ${获取历史事件数量} == 0
        ${一次下电告警级别获取原值}    获取web参数量    一次下电告警
    END
    ${一次下电告警干接点获取原值}    获取web参数量    一次下电告警干接点
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    一次下电告警干接点    ${VAR}
        sleep    5
        ${一次下电告警干接点获取}    获取web参数量    一次下电告警干接点
        should be true    ${一次下电告警干接点获取}==${VAR}
        ${起始时间}    ${结束时间}    获取起始结束时间段    9
        ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    一次下电告警
        run keyword if    ${一次下电告警干接点获取原值}!= ${一次下电告警干接点获取}    should be true    6>=${获取历史事件数量}>=1
        ...    ELSE    should be true    ${获取历史事件数量} == 0
        ${一次下电告警干接点获取原值}    获取web参数量    一次下电告警干接点
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    一次下电告警干接点    0
    ...    AND    设置web参数量    一次下电告警    严重

02 二次下电告警参数设置测试
    [Documentation]    200：操作日志。
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    二次下电告警
    ${二次下电告警级别获取原值}    获取web参数量    二次下电告警
    FOR    ${VAR}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    10    2    设置web参数量    二次下电告警    ${VAR}
        sleep    5
        ${二次下电告警级别获取}    获取web参数量    二次下电告警
        should be equal    ${二次下电告警级别获取}    ${VAR}
        ${起始时间}    ${结束时间}    获取起始结束时间段    9
        ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    二次下电告警
        run keyword if    '${二次下电告警级别获取原值}' !='${二次下电告警级别获取}'    should be true    6>=${获取历史事件数量}>=1
        ...    ELSE    should be true    ${获取历史事件数量} == 0
        ${二次下电告警级别获取原值}    获取web参数量    二次下电告警
    END
    ${二次下电告警干接点获取原值}    获取web参数量    二次下电告警干接点
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    二次下电告警干接点    ${VAR}
        sleep    5
        ${二次下电告警干接点获取}    获取web参数量    二次下电告警干接点
        should be true    ${二次下电告警干接点获取}==${VAR}
        ${起始时间}    ${结束时间}    获取起始结束时间段    9
        ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    二次下电告警
        run keyword if    ${二次下电告警干接点获取原值}!= ${二次下电告警干接点获取}    should be true    6>=${获取历史事件数量}>=1
        ...    ELSE    should be true    ${获取历史事件数量} == 0
        ${二次下电告警干接点获取原值}    获取web参数量    二次下电告警干接点
    END
    [Teardown]    run keywords    设置web参数量    二次下电告警干接点    0
    ...    AND    设置web参数量    二次下电告警    严重

03 电池下电告警参数设置测试
    [Documentation]    200：操作日志。
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    电池下电告警
    ${下电告警级别获取原值}    获取web参数量    电池下电告警
    FOR    ${VAR}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    10    2    设置web参数量    电池下电告警    ${VAR}
        sleep    5
        ${电池下电告警级别获取}    获取web参数量    电池下电告警
        should be equal    ${电池下电告警级别获取}    ${VAR}
        ${起始时间}    ${结束时间}    获取起始结束时间段    9
        ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    电池下电告警
        run keyword if    '${下电告警级别获取原值}' !='${电池下电告警级别获取}'    should be true    6>=${获取历史事件数量}>=1
        ...    ELSE    should be true    ${获取历史事件数量} == 0
        ${下电告警级别获取原值}    获取web参数量    电池下电告警
    END
    ${下电告警干接点获取原值}    获取web参数量    电池下电告警干接点
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    电池下电告警干接点    ${VAR}
        sleep    5
        ${电池下电告警干接点获取}    获取web参数量    电池下电告警干接点
        should be true    ${电池下电告警干接点获取}==${VAR}
        ${起始时间}    ${结束时间}    获取起始结束时间段    9
        ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    电池下电告警
        run keyword if    ${下电告警干接点获取原值}!= ${电池下电告警干接点获取}    should be true    6>=${获取历史事件数量}>=1
        ...    ELSE    should be true    ${获取历史事件数量} == 0
        ${下电告警干接点获取原值}    获取web参数量    电池下电告警干接点
    END
    [Teardown]    run keywords    设置web参数量    电池下电告警干接点    0
    ...    AND    设置web参数量    电池下电告警    严重

04 电池检测异常参数设置测试
    [Documentation]    200：操作日志。
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    电池检测异常
    ${告警级别获取原值}    获取web参数量    电池检测异常
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    电池检测异常    ${VAR}
        sleep    5
        ${电池检测异常级别获取}    获取web参数量    电池检测异常
        should be equal    ${电池检测异常级别获取}    ${VAR}
        ${起始时间}    ${结束时间}    获取起始结束时间段    9
        ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    电池检测异常
        run keyword if    '${告警级别获取原值}' !='${电池检测异常级别获取}'    should be true    6>=${获取历史事件数量}>=1
        ...    ELSE    should be true    ${获取历史事件数量} == 0
        ${告警级别获取原值}    获取web参数量    电池检测异常
    END
    ${告警干接点获取原值}    获取web参数量    电池检测异常干接点
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    电池检测异常干接点    ${VAR}
        sleep    5
        ${电池检测异常干接点获取}    获取web参数量    电池检测异常干接点
        should be true    ${电池检测异常干接点获取}==${VAR}
        ${起始时间}    ${结束时间}    获取起始结束时间段    9
        ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    电池检测异常
        run keyword if    ${告警干接点获取原值}!= ${电池检测异常干接点获取}    should be true    6>=${获取历史事件数量}>=1
        ...    ELSE    should be true    ${获取历史事件数量} == 0
        ${告警干接点获取原值}    获取web参数量    电池检测异常干接点
    END
    [Teardown]    run keywords    设置web参数量    电池检测异常干接点    0
    ...    AND    设置web参数量    电池检测异常    严重

05 电池测试告警参数设置测试
    [Documentation]    200：操作日志。
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    电池测试
    ${告警级别获取原值}    获取web参数量    电池测试
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    电池测试    ${VAR}
        sleep    5
        ${电池测试级别获取}    获取web参数量    电池测试
        should be equal    ${电池测试级别获取}    ${VAR}
        ${起始时间}    ${结束时间}    获取起始结束时间段    9
        ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    电池测试
        run keyword if    '${告警级别获取原值}' !='${电池测试级别获取}'    should be true    6>=${获取历史事件数量}>=1
        ...    ELSE    should be true    ${获取历史事件数量} == 0
        ${告警级别获取原值}    获取web参数量    电池测试
    END
    ${告警干接点获取原值}    获取web参数量    电池测试干接点
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    电池测试干接点    ${VAR}
        sleep    5
        ${电池测试干接点获取}    获取web参数量    电池测试干接点
        should be true    ${电池测试干接点获取}==${VAR}
        ${起始时间}    ${结束时间}    获取起始结束时间段    9
        ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    电池测试
        run keyword if    ${告警干接点获取原值}!= ${电池测试干接点获取}    should be true    6>=${获取历史事件数量}>=1
        ...    ELSE    should be true    ${获取历史事件数量} == 0
        ${告警干接点获取原值}    获取web参数量    电池测试干接点
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    电池测试干接点    0
    ...    AND    设置web参数量    电池测试    屏蔽

06 电池均充告警参数设置测试
    [Documentation]    200：操作日志。
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    电池均充
    ${告警级别获取原值}    获取web参数量    电池均充
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    电池均充    ${VAR}
        sleep    5
        ${电池均充级别获取}    获取web参数量    电池均充
        should be equal    ${电池均充级别获取}    ${VAR}
        ${起始时间}    ${结束时间}    获取起始结束时间段    9
        ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    电池均充
        run keyword if    '${告警级别获取原值}' !='${电池均充级别获取}'    should be true    6>=${获取历史事件数量}>=1
        ...    ELSE    should be true    ${获取历史事件数量} == 0
        ${告警级别获取原值}    获取web参数量    电池均充
    END
    ${告警干接点获取原值}    获取web参数量    电池均充干接点
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    电池均充干接点    ${VAR}
        sleep    5
        ${电池均充干接点获取}    获取web参数量    电池均充干接点
        should be true    ${电池均充干接点获取}==${VAR}
        ${起始时间}    ${结束时间}    获取起始结束时间段    9
        ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    电池均充
        run keyword if    ${告警干接点获取原值}!= ${电池均充干接点获取}    should be true    6>=${获取历史事件数量}>=1
        ...    ELSE    should be true    ${获取历史事件数量} == 0
        ${告警干接点获取原值}    获取web参数量    电池均充干接点
    END
    [Teardown]    run keywords    设置web参数量    电池均充干接点    0
    ...    AND    设置web参数量    电池均充    屏蔽

07 电池组丢失告警参数设置测试
    [Documentation]    200：操作日志。
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    电池组丢失
    ${告警级别获取原值}    获取web参数量    电池组丢失
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    电池组丢失    ${VAR}
        sleep    5
        ${告警级别获取}    获取web参数量    电池组丢失
        should be equal    ${告警级别获取}    ${VAR}
        ${起始时间}    ${结束时间}    获取起始结束时间段    9
        ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    电池组丢失
        run keyword if    '${告警级别获取原值}' !='${告警级别获取}'    should be true    6>=${获取历史事件数量}>=1
        ...    ELSE    should be true    ${获取历史事件数量} == 0
        ${告警级别获取原值}    获取web参数量    电池组丢失
    END
    ${告警干接点获取原值}    获取web参数量    电池组丢失干接点
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    电池组丢失干接点    ${VAR}
        sleep    5
        ${告警输出干接点获取}    获取web参数量    电池组丢失干接点
        should be true    ${告警输出干接点获取}==${VAR}
        ${起始时间}    ${结束时间}    获取起始结束时间段    9
        ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    电池组丢失
        run keyword if    ${告警干接点获取原值}!= ${告警输出干接点获取}    should be true    6>=${获取历史事件数量}>=1
        ...    ELSE    should be true    ${获取历史事件数量} == 0
        ${告警干接点获取原值}    获取web参数量    电池组丢失干接点
    END
    [Teardown]    run keywords    设置web参数量    电池组丢失干接点    0
    ...    AND    设置web参数量    电池组丢失    主要

08 电池温度高告警参数设置测试
    [Documentation]    200：操作日志。
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    电池温度高
    ${告警级别获取原值}    获取web参数量    电池温度高
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    电池温度高    ${VAR}
        sleep    5
        ${告警级别获取}    获取web参数量    电池温度高
        should be equal    ${告警级别获取}    ${VAR}
        ${起始时间}    ${结束时间}    获取起始结束时间段    9
        ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    电池温度高
        run keyword if    '${告警级别获取原值}' !='${告警级别获取}'    should be true    6>=${获取历史事件数量}>=1
        ...    ELSE    should be true    ${获取历史事件数量} == 0
        ${告警级别获取原值}    获取web参数量    电池温度高
    END
    ${告警干接点获取原值}    获取web参数量    电池温度高干接点
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    电池温度高干接点    ${VAR}
        sleep    5
        ${告警输出干接点获取}    获取web参数量    电池温度高干接点
        should be true    ${告警输出干接点获取}==${VAR}
        ${起始时间}    ${结束时间}    获取起始结束时间段    9
        ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    电池温度高
        run keyword if    ${告警干接点获取原值}!= ${告警输出干接点获取}    should be true    6>=${获取历史事件数量}>=1
        ...    ELSE    should be true    ${获取历史事件数量} == 0
        ${告警干接点获取原值}    获取web参数量    电池温度高干接点
    END
    [Teardown]    run keywords    设置web参数量    电池温度高干接点    0
    ...    AND    设置web参数量    电池温度高    主要

09 电池温度低告警参数设置测试
    [Documentation]    200：操作日志。
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    电池温度低
    ${告警级别获取原值}    获取web参数量    电池温度低
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    电池温度低    ${VAR}
        sleep    5
        ${告警级别获取}    获取web参数量    电池温度低
        should be equal    ${告警级别获取}    ${VAR}
        ${起始时间}    ${结束时间}    获取起始结束时间段    9
        ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    电池温度低
        run keyword if    '${告警级别获取原值}' !='${告警级别获取}'    should be true    6>=${获取历史事件数量}>=1
        ...    ELSE    should be true    ${获取历史事件数量} == 0
        ${告警级别获取原值}    获取web参数量    电池温度低
    END
    ${告警干接点获取原值}    获取web参数量    电池温度低干接点
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    电池温度低干接点    ${VAR}
        sleep    5
        ${告警输出干接点获取}    获取web参数量    电池温度低干接点
        should be true    ${告警输出干接点获取}==${VAR}
        ${起始时间}    ${结束时间}    获取起始结束时间段    9
        ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    电池温度低
        run keyword if    ${告警干接点获取原值}!= ${告警输出干接点获取}    should be true    6>=${获取历史事件数量}>=1
        ...    ELSE    should be true    ${获取历史事件数量} == 0
        ${告警干接点获取原值}    获取web参数量    电池温度低干接点
    END
    [Teardown]    run keywords    设置web参数量    电池温度低干接点    0
    ...    AND    设置web参数量    电池温度低    主要

10 电池回路断告警参数设置测试
    [Documentation]    200：操作日志。
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    电池回路断
    ${告警级别获取原值}    获取web参数量    电池回路断
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    电池回路断    ${VAR}
        sleep    5
        ${告警级别获取}    获取web参数量    电池回路断
        should be equal    ${告警级别获取}    ${VAR}
        ${起始时间}    ${结束时间}    获取起始结束时间段    9
        ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    电池回路断
        run keyword if    '${告警级别获取原值}' !='${告警级别获取}'    should be true    6>=${获取历史事件数量}>=1
        ...    ELSE    should be true    ${获取历史事件数量} == 0
        ${告警级别获取原值}    获取web参数量    电池回路断
    END
    ${告警干接点获取原值}    获取web参数量    电池回路断干接点
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    电池回路断干接点    ${VAR}
        sleep    5
        ${告警输出干接点获取}    获取web参数量    电池回路断干接点
        should be true    ${告警输出干接点获取}==${VAR}
        ${起始时间}    ${结束时间}    获取起始结束时间段    9
        ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    电池回路断
        run keyword if    ${告警干接点获取原值}!= ${告警输出干接点获取}    should be true    6>=${获取历史事件数量}>=1
        ...    ELSE    should be true    ${获取历史事件数量} == 0
        ${告警干接点获取原值}    获取web参数量    电池回路断干接点
    END
    [Teardown]    run keywords    设置web参数量    电池回路断干接点    0
    ...    AND    设置web参数量    电池回路断    主要

11 电池温度无效告警参数设置测试
    [Documentation]    200：操作日志。
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    电池温度无效
    ${告警级别获取原值}    获取web参数量    电池温度无效
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    电池温度无效    ${VAR}
        sleep    5
        ${告警级别获取}    获取web参数量    电池温度无效
        should be equal    ${告警级别获取}    ${VAR}
        ${起始时间}    ${结束时间}    获取起始结束时间段    9
        ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    电池温度无效
        run keyword if    '${告警级别获取原值}' !='${告警级别获取}'    should be true    6>=${获取历史事件数量}>=1
        ...    ELSE    should be true    ${获取历史事件数量} == 0
        ${告警级别获取原值}    获取web参数量    电池温度无效
    END
    ${告警干接点获取原值}    获取web参数量    电池温度无效干接点
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    电池温度无效干接点    ${VAR}
        sleep    5
        ${告警输出干接点获取}    获取web参数量    电池温度无效干接点
        should be true    ${告警输出干接点获取}==${VAR}
        ${起始时间}    ${结束时间}    获取起始结束时间段    9
        ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    电池温度无效
        run keyword if    ${告警干接点获取原值}!= ${告警输出干接点获取}    should be true    6>=${获取历史事件数量}>=1
        ...    ELSE    should be true    ${获取历史事件数量} == 0
        ${告警干接点获取原值}    获取web参数量    电池温度无效干接点
    END
    [Teardown]    run keywords    设置web参数量    电池温度无效干接点    0
    ...    AND    设置web参数量    电池温度无效    主要

12 电池电压低告警参数设置测试
    [Documentation]    200：操作日志。
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    电池电压低
    ${告警级别获取原值}    获取web参数量    电池电压低
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    电池电压低    ${VAR}
        sleep    5
        ${告警级别获取}    获取web参数量    电池电压低
        should be equal    ${告警级别获取}    ${VAR}
        ${起始时间}    ${结束时间}    获取起始结束时间段    9
        ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    电池电压低
        run keyword if    '${告警级别获取原值}' !='${告警级别获取}'    should be true    6>=${获取历史事件数量}>=1
        ...    ELSE    should be true    ${获取历史事件数量} == 0
        ${告警级别获取原值}    获取web参数量    电池电压低
    END
    ${告警干接点获取原值}    获取web参数量    电池电压低干接点
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    电池电压低干接点    ${VAR}
        sleep    5
        ${告警输出干接点获取}    获取web参数量    电池电压低干接点
        should be true    ${告警输出干接点获取}==${VAR}
        ${起始时间}    ${结束时间}    获取起始结束时间段    9
        ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    电池电压低
        run keyword if    ${告警干接点获取原值}!= ${告警输出干接点获取}    should be true    6>=${获取历史事件数量}>=1
        ...    ELSE    should be true    ${获取历史事件数量} == 0
        ${告警干接点获取原值}    获取web参数量    电池电压低干接点
    END
    [Teardown]    run keywords    设置web参数量    电池电压低干接点    0
    ...    AND    设置web参数量    电池电压低    主要

13 电池放电告警参数设置测试
    [Documentation]    200：操作日志。
    [Setup]    判断web参数是否存在    电池放电
    ${告警级别获取原值}    获取web参数量    电池放电
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    电池放电    ${VAR}
        sleep    5
        ${告警级别获取}    获取web参数量    电池放电
        should be equal    ${告警级别获取}    ${VAR}
        ${起始时间}    ${结束时间}    获取起始结束时间段    9
        ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    电池放电
        run keyword if    '${告警级别获取原值}' !='${告警级别获取}'    should be true    6>=${获取历史事件数量}>=1
        ...    ELSE    should be true    ${获取历史事件数量} == 0
        ${告警级别获取原值}    获取web参数量    电池放电
    END
    ${告警干接点获取原值}    获取web参数量    电池放电干接点
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    电池放电干接点    ${VAR}
        sleep    5
        ${告警输出干接点获取}    获取web参数量    电池放电干接点
        should be true    ${告警输出干接点获取}==${VAR}
        ${起始时间}    ${结束时间}    获取起始结束时间段    9
        ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    电池放电
        run keyword if    ${告警干接点获取原值}!= ${告警输出干接点获取}    should be true    6>=${获取历史事件数量}>=1
        ...    ELSE    should be true    ${获取历史事件数量} == 0
        ${告警干接点获取原值}    获取web参数量    电池放电干接点
    END
    [Teardown]    run keywords    设置web参数量    电池放电干接点    0
    ...    AND    设置web参数量    电池放电    主要

14 电池丢失告警参数测试
    [Documentation]    200：操作日志。
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    电池丢失
    ${告警级别获取原值}    获取web参数量    电池丢失
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    电池丢失    ${VAR}
        sleep    5
        ${告警级别获取}    获取web参数量    电池丢失
        should be equal    ${告警级别获取}    ${VAR}
        ${起始时间}    ${结束时间}    获取起始结束时间段    9
        ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    电池丢失
        run keyword if    '${告警级别获取原值}' !='${告警级别获取}'    should be true    6>=${获取历史事件数量}>=1
        ...    ELSE    should be true    ${获取历史事件数量} == 0
        ${告警级别获取原值}    获取web参数量    电池丢失
    END
    ${告警干接点获取原值}    获取web参数量    电池丢失干接点
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    电池丢失干接点    ${VAR}
        sleep    5
        ${告警输出干接点获取}    获取web参数量    电池丢失干接点
        should be true    ${告警输出干接点获取}==${VAR}
        ${起始时间}    ${结束时间}    获取起始结束时间段    9
        ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    电池丢失
        run keyword if    ${告警干接点获取原值}!= ${告警输出干接点获取}    should be true    6>=${获取历史事件数量}>=1
        ...    ELSE    should be true    ${获取历史事件数量} == 0
        ${告警干接点获取原值}    获取web参数量    电池丢失干接点
    END
    [Teardown]    run keywords    设置web参数量    电池丢失干接点    0
    ...    AND    设置web参数量    电池丢失    主要

15 电池测试失败告警参数设置测试
    [Documentation]    200：操作日志。
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    电池测试失败
    ${告警级别获取原值}    获取web参数量    电池测试失败
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    电池测试失败    ${VAR}
        sleep    5
        ${告警级别获取}    获取web参数量    电池测试失败
        should be equal    ${告警级别获取}    ${VAR}
        ${起始时间}    ${结束时间}    获取起始结束时间段    9
        ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    电池测试失败
        run keyword if    '${告警级别获取原值}' !='${告警级别获取}'    should be true    6>=${获取历史事件数量}>=1
        ...    ELSE    should be true    ${获取历史事件数量} == 0
        ${告警级别获取原值}    获取web参数量    电池测试失败
    END
    ${告警干接点获取原值}    获取web参数量    电池测试失败干接点
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    电池测试失败干接点    ${VAR}
        sleep    5
        ${告警输出干接点获取}    获取web参数量    电池测试失败干接点
        should be true    ${告警输出干接点获取}==${VAR}
        ${起始时间}    ${结束时间}    获取起始结束时间段    9
        ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    电池测试失败
        run keyword if    ${告警干接点获取原值}!= ${告警输出干接点获取}    should be true    6>=${获取历史事件数量}>=1
        ...    ELSE    should be true    ${获取历史事件数量} == 0
        ${告警干接点获取原值}    获取web参数量    电池测试失败干接点
    END
    [Teardown]    run keywords    设置web参数量    电池测试失败干接点    0
    ...    AND    设置web参数量    电池测试失败    主要

16 电池电压过低告警参数设置测试
    [Documentation]    200：操作日志。
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    电池电压过低
    ${告警级别获取原值}    获取web参数量    电池电压过低
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    5m    2    设置web参数量    电池电压过低    ${VAR}
        sleep    5
        ${告警级别获取}    获取web参数量    电池电压过低
        should be equal    ${告警级别获取}    ${VAR}
        ${起始时间}    ${结束时间}    获取起始结束时间段    9
        ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    电池电压过低
        run keyword if    '${告警级别获取原值}' !='${告警级别获取}'    should be true    6>=${获取历史事件数量}>=1
        ...    ELSE    should be true    ${获取历史事件数量} == 0
        ${告警级别获取原值}    获取web参数量    电池电压过低
    END
    ${告警干接点获取原值}    获取web参数量    电池电压过低干接点
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    5m    2    设置web参数量    电池电压过低干接点    ${VAR}
        sleep    5
        ${告警输出干接点获取}    获取web参数量    电池电压过低干接点
        should be true    ${告警输出干接点获取}==${VAR}
        ${起始时间}    ${结束时间}    获取起始结束时间段    9
        ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    电池电压过低
        run keyword if    ${告警干接点获取原值}!= ${告警输出干接点获取}    should be true    6>=${获取历史事件数量}>=1
        ...    ELSE    should be true    ${获取历史事件数量} == 0
        ${告警干接点获取原值}    获取web参数量    电池电压过低干接点
    END
    [Teardown]    run keywords    设置web参数量    电池电压过低干接点    0
    ...    AND    设置web参数量    电池电压过低    主要

17 电池中点电压不平衡告警参数设置测试
    [Documentation]    200：操作日志。
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    电池中点电压不平衡
    ${告警级别获取原值}    获取web参数量    电池中点电压不平衡
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    5m    2    设置web参数量    电池中点电压不平衡    ${VAR}
        sleep    5
        ${告警级别获取}    获取web参数量    电池中点电压不平衡
        should be equal    ${告警级别获取}    ${VAR}
        ${起始时间}    ${结束时间}    获取起始结束时间段    9
        ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    电池中点电压不平衡
        run keyword if    '${告警级别获取原值}' !='${告警级别获取}'    should be true    6>=${获取历史事件数量}>=1
        ...    ELSE    should be true    ${获取历史事件数量} == 0
        ${告警级别获取原值}    获取web参数量    电池中点电压不平衡
    END
    ${告警干接点获取原值}    获取web参数量    电池中点电压不平衡干接点
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    5m    2    设置web参数量    电池中点电压不平衡干接点    ${VAR}
        sleep    5
        ${告警输出干接点获取}    获取web参数量    电池中点电压不平衡干接点
        should be true    ${告警输出干接点获取}==${VAR}
        ${起始时间}    ${结束时间}    获取起始结束时间段    9
        ${获取历史事件数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    电池中点电压不平衡
        run keyword if    ${告警干接点获取原值}!= ${告警输出干接点获取}    should be true    6>=${获取历史事件数量}>=1
        ...    ELSE    should be true    ${获取历史事件数量} == 0
        ${告警干接点获取原值}    获取web参数量    电池中点电压不平衡干接点
    END
    [Teardown]    run keywords    设置web参数量    电池中点电压不平衡干接点    0
    ...    AND    设置web参数量    电池中点电压不平衡    屏蔽
