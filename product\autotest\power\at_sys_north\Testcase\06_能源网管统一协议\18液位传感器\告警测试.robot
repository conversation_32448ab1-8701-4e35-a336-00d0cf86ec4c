*** Settings ***
Suite Setup       主动告警测试前置条件    ${CSU_role}
Suite Teardown    设置web设备参数量为默认值    CSU主动告警使能
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0002_液位传感器通讯中断
    连接CSU
    ${级别设置值}    获取web参数量    液位传感器通讯中断
    run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    液位传感器通讯中断    严重
    Comment    run keyword if    '${级别设置值}'=='屏蔽'    设置web设备参数量为默认值    液位传感器通讯中断
    ${告警级别取值约定dict}    获取web参数的取值约定    液位传感器通讯中断-1
    ${告警级别}    获取web参数量    液位传感器通讯中断
    控制子工具运行停止    llSensor    关闭
    wait until keyword succeeds    5m    1    判断告警存在    液位传感器通讯中断-1
    Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client'    主动告警上送判断2    power_sm    液位传感器_1    液位传感器通讯中断
    sleep    30s
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    液位传感器通讯中断    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${告警级别}
    控制子工具运行停止    llSensor    开启
    wait until keyword succeeds    5m    1    判断告警不存在    液位传感器通讯中断-1
    sleep    30s
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    液位传感器通讯中断    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    控制子工具运行停止    llSensor    开启
    ...    AND    sleep    3min
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
