*** Settings ***
Documentation     不包含
...               开启、关闭WEB控制
Suite Setup       #解析本地MIB文件    # 解析本地MIB文件
Suite Teardown    #生成测试报告
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
snmp_0008_cSU1analogData√
    ${比较结果}    对比数据_V2C    cSU1analogData
    should be true    ${比较结果}
    Comment    ${比较结果}    对比数据_V3    cSU1analogData
    Comment    should be true    ${比较结果}

snmp_0010_cSU1digitalData√
    ${比较结果}    对比数据_V2C    cSU1digitalData
    should be true    ${比较结果}

snmp_0012_cSU1alarm_level√
    ${比较结果}    对比告警级别_V2C    cSU1alarm
    should be true    ${比较结果}

snmp_0014_cSU1alarm_level_write√
    Comment    ${比较结果}    批量修改告警级别_V2C    cSU1alarm
    Comment    should be true    ${比较结果}
    ${比较结果}    批量设置四种/五种告警级别    cSU1alarm
    should be true    ${比较结果}

snmp_0016_cSU1alarm_relay√
    ${比较结果}    对比告警干接点_V2C    cSU1alarm
    should be true    ${比较结果}

snmp_0018_cSU1alarm_relay_write√
    ${比较结果}    批量修改告警干接点_V2C    cSU1alarm
    should be true    ${比较结果}

snmp_0020_cSU1parameter√
    Comment    FOR    ${i}    IN RANGE    100000
    ${比较结果}    对比数据_V2C    cSU1parameter
    should be true    ${比较结果}
    Comment    END

snmp_0022_cSU1parameter_write√
    Comment    ${比较结果}    批量修改参数_V2C    cSU1parameter
    Comment    should be true    ${比较结果}
    ${比较结果}    SNMP批量参数设置    cSU1parameter
    should be true    ${比较结果}

snmp_0024_cSU1parameter_cPUUsageRateHighThresholdvalue√
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${初始值}    获取SNMP数据_单个    cPUUsageRateHighThresholdvalue
    ${设置SNMP参数1}    设置SNMP参数量_验证    cPUUsageRateHighThresholdvalue    81
    ${设置SNMP参数2}    设置SNMP参数量_验证    cPUUsageRateHighThresholdvalue    ${初始值}
    断开连接SNMP
    should be true    ${设置SNMP参数1}
    should be true    ${设置SNMP参数2}

snmp_0026_cSU1deviceInfo√
    ${比较结果}    对比数据_V2C    cSU1deviceInfo
    should be true    ${比较结果}

snmp_0028_cSU1control_totalAlarmDisable_totalAlarmEnable√
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${设置控制量}    设置SNMP控制量    totalAlarmEnablevalue    # totalAlarmDisablevalue
    Comment    should be true    ${设置控制量}
    sleep    5
    ###增加判断准确性
    ${存在结果}    判断web历史记录存在snmp控制内容    CSU    允许所有告警    20
    should be true    ${存在结果}
    ${设置控制量}    设置SNMP控制量    totalAlarmEnablevalue
    should be true    ${设置控制量}
    sleep    5
    ###增加判断准确性
    ${存在结果}    判断web历史记录存在snmp控制内容    CSU    允许所有告警    20
    should be true    ${存在结果}
    断开连接SNMP

snmp_0030_cSU1control_cANBusDeviceStatistic√
    [Documentation]    CAN总线设备统计
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${设置控制量}    设置SNMP控制量    cANBusDeviceStatisticvalue    # totalAlarmDisablevalue
    Comment    should be true    ${设置控制量}
    sleep    5
    ###增加判断准确性
    ${存在结果}    判断web历史记录存在snmp控制内容    CSU    CAN总线设备统计    20
    should be true    ${存在结果}
    断开连接SNMP

snmp_0032_cSU1control_rS485BusDeviceStatistic√
    [Documentation]    CAN总线设备统计
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${设置控制量}    设置SNMP控制量    rS485BusDeviceStatisticvalue    # totalAlarmDisablevalue
    Comment    should be true    ${设置控制量}
    sleep    5
    ###增加判断准确性
    ${存在结果}    判断web历史记录存在snmp控制内容    CSU    RS485总线设备统计    20
    should be true    ${存在结果}
    断开连接SNMP

snmp_0034_cSU1control_openSSH√
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${设置控制量}    设置SNMP控制量    openSSHvalue    # totalAlarmDisablevalue
    Comment    should be true    ${设置控制量}
    sleep    10
    Comment    ${起始时间}    ${结束时间}    获取起始结束时间段    60
    Comment    ${历史数据数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    开启SSH
    Comment    should be true    3>=${历史数据数量}>=1
    ###增加判断准确性
    ${存在结果}    判断web历史记录存在snmp控制内容    CSU    开启SSH    20
    should be true    ${存在结果}
    断开连接SNMP

snmp_0036_cSU1control_closeSSH√
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${设置控制量}    设置SNMP控制量    closeSSHvalue    # totalAlarmDisablevalue
    Comment    should be true    ${设置控制量}
    sleep    10
    Comment    ${起始时间}    ${结束时间}    获取起始结束时间段    60
    Comment    ${历史数据数量}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    200    关闭SSH
    Comment    should be true    3>=${历史数据数量}>=1
    ###增加判断准确性
    ${存在结果}    判断web历史记录存在snmp控制内容    CSU    关闭SSH    20
    should be true    ${存在结果}
    断开连接SNMP

snmp_0038_cSU1control_iEnergySSHSecurityCodeResetX
    [Documentation]    OK
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${设置控制量}    设置SNMP控制量    iEnergySSHSecurityCodeResetvalue    # totalAlarmDisablevalue
    Comment    should be true    ${设置控制量}
    sleep    10
    ${存在结果}    判断web历史记录存在snmp控制内容    CSU    能源网管SSH口令重置    20
    should be true    ${存在结果}
    断开连接SNMP
