*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
电池均充管理测试（铅酸和锂电混用）
    [Setup]    #测试用例前置条件
    Comment    电池管理初始化
    连接CSU
    设置web参数量    电池配置    铅酸&锂电混用
    sleep    3
    设置web参数量    均充使能    允许
    设置web参数量    电池周期均充使能    允许
    sleep    3
    设置web参数量    电池均充周期    1
    sleep    3
    ${均充末期维持时间范围}    获取web参数上下限范围_有单位    均充末期维持时间
    ${均充最短时间范围}    获取web参数上下限范围_有单位    均充最短时间
    ${均充最长时间范围}    获取web参数上下限范围_有单位    均充最长时间
    ${均充末期维持时间}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间范围}[1]+1
    ...    ELSE    evaluate    ${均充末期维持时间范围}[1]+3
    ${均充最短时间}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间范围}[1]+1
    ...    ELSE    evaluate    ${均充最短时间范围}[1]+3
    ${均充最长时间}    run keyword if    '${均充最长时间范围}[3]'== 'Hour'    evaluate    ${均充最长时间范围}[1]+1
    ...    ELSE    evaluate    ${均充最长时间范围}[1]+5
    设置web参数量    均充末期维持时间    ${均充末期维持时间}
    设置web参数量    均充最短时间    ${均充最短时间}
    设置web参数量    均充最长时间    ${均充最长时间}
    ${均充末期维持时间转换后}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间}*60
    ...    ELSE    set variable    ${均充末期维持时间}
    ${均充最短时间转换后}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间}*60
    ...    ELSE    set variable    ${均充最短时间}
    ${均充最长时间转换后}    run keyword if    '${均充最长时间范围}[3]'== 'Hour'    evaluate    ${均充最长时间}*60
    ...    ELSE    set variable    ${均充最长时间}
    ${电池均充周期}    获取web参数量    电池均充周期
    should be equal as numbers    ${电池均充周期}    1
    sleep    5
    ${下次均充时间}    获取web实时数据    下次均充时间
    ${下次均充时间0}    Subtract Time From Date    ${下次均充时间}    5s    #比16:00:00差5s
    设置系统时间    ${下次均充时间0}
    sleep    8
    ${修改后的系统时间}    获取系统时间
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    均充
    ${下次均充时间1}    获取web实时数据    下次均充时间
    Comment    Should Be Equal    ${下次均充时间1}    ${下次均充时间}
    ${等待时间}    evaluate    ${均充最长时间转换后}+3
    Wait Until Keyword Succeeds    ${等待时间}m    2    信号量数据值为    电池管理状态    浮充
    ${下次均充时间_cal}    add time to date    ${下次均充时间}    ${电池均充周期}d    exclude_millis=yes
    sleep    5
    ${下次均充时间_new}    获取web实时数据    下次均充时间
    should be equal    ${下次均充时间_new}    ${下次均充时间_cal}
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池均充周期    电池周期均充使能    均充使能    电池配置

电池测试周期为0（铅酸和锂电混用）
    [Setup]    #测试用例前置条件
    Comment    电池管理初始化
    连接CSU
    设置web参数量    电池配置    铅酸&锂电混用
    sleep    3
    设置web参数量    均充使能    允许
    设置web参数量    电池周期均充使能    允许
    sleep    3
    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池均充周期    60
    sleep    5
    ${下次均充时间}    获取web实时数据    下次均充时间
    ${下次均充时间0}    Subtract Time From Date    ${下次均充时间}    5s    #比16:00:00差5s
    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池均充周期    0
    sleep    30
    Wait Until Keyword Succeeds    1m    1    设置系统时间    ${下次均充时间0}
    sleep    1m
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    浮充
    ${系统时间}    Subtract Time From Date    ${下次均充时间0}    7 days
    Wait Until Keyword Succeeds    1m    1    设置系统时间    ${系统时间}
    sleep    5
    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池均充周期    60    #默认90
    sleep    30
    ${下次均充时间_new}    获取web实时数据    下次均充时间
    should be equal    ${下次均充时间_new}    ${下次均充时间}
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池均充周期    电池周期均充使能    均充使能    电池配置

电池周期均充使能有效性测试（禁止）
    [Setup]    #测试用例前置条件
    Comment    电池管理初始化
    连接CSU
    设置web参数量    电池配置    铅酸&锂电混用
    设置web控制量    启动浮充
    sleep    3
    设置web参数量    均充使能    允许
    设置web参数量    电池周期均充使能    禁止
    ${电池均充周期}    获取web参数量    电池均充周期
    Should Be Equal As Strings    ${电池均充周期}    ${EMPTY}
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    浮充
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池周期均充使能    均充使能    电池配置

电池均充周期设置与保存测试（无效值）
    [Setup]    #测试用例前置条件
    Comment    电池管理初始化
    连接CSU
    设置web参数量    电池配置    铅酸&锂电混用
    设置web参数量    均充使能    允许
    sleep    3
    设置web参数量    电池周期均充使能    允许
    Comment    设置web参数量    电池测试周期    0.1
    ${电池周期均充范围}    获取web参数可设置范围    电池均充周期
    ${无效值}    Create List    -0.1
    ${无效值_0}    evaluate    ${电池周期均充范围}[0]-1
    ${无效值_1}    evaluate    ${电池周期均充范围}[1]+1
    ${无效值_2}    evaluate    ${电池周期均充范围}[0]+0.1
    ${无效值_3}    evaluate    ${电池周期均充范围}[1]-0.1
    Append To List    ${无效值}    ${无效值_0}    ${无效值_1}    ${无效值_2}    ${无效值_3}    aaa
    FOR    ${ver}    IN    @{无效值}
        ${返回值}    设置web参数量_带返回值    电池均充周期    ${ver}
        should not be true    ${返回值}
    END
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池均充周期    电池周期均充使能    均充使能    电池配置

电池均充周期设置与保存测试（有效值）
    [Setup]    #测试用例前置条件
    Comment    电池管理初始化
    连接CSU
    设置web参数量    电池配置    铅酸&锂电混用
    设置web参数量    均充使能    允许
    sleep    3
    设置web参数量    电池周期均充使能    允许
    ${电池周期均充范围}    获取web参数可设置范围    电池均充周期
    Append To List    ${电池周期均充范围}    90
    FOR    ${val}    IN    @{电池周期均充范围}
        设置web参数量    电池均充周期    ${val}
        sleep    2
        ${电池均充周期}    获取web参数量    电池均充周期
        should be equal as numbers    ${电池均充周期}    ${val}
    END
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池均充周期    电池周期均充使能    均充使能    电池配置
