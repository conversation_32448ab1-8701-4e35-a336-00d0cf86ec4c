*** Settings ***
Documentation     告警级别
...               0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning; 0xFF：无效值
Suite Setup
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
1363_0006_1环境温度高
    [Documentation]    获取1104一条告警值并判断告警存在
    ...    输入
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${理应值}= | ${屏号}=None | ${g_ver}=
    ...
    ...
    ...
    ...    0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning; 0xFF：无效值
    ...
    ...    设置
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${var_name} | ${val} || ${屏号}=None | ${g_ver}=、
    ...
    ...    获取
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${屏号}=None | ${g_ver}=
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置环境段告警级别~CID1=91H>>    <<设置环境段告警级别~CID2=82H>>    环境温度高    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取环境段告警级别~CID1=91H>>    <<获取环境段告警级别~CID2=81H>>    环境温度高    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    环境温度高    ${VAR}
        ${环境温度高告警级别}    获取web参数量    环境温度高
        should be equal    ${1104告警级别}    ${环境温度高告警级别}
    END
    [Teardown]    设置web参数量    环境温度高    次要

1363_0008_2环境温度低
    [Documentation]    获取1104一条告警值并判断告警存在
    ...    输入
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${理应值}= | ${屏号}=None | ${g_ver}=
    ...
    ...
    ...
    ...    0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning; 0xFF：无效值
    ...
    ...    设置
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${var_name} | ${val} || ${屏号}=None | ${g_ver}=、
    ...
    ...    获取
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${屏号}=None | ${g_ver}=
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置环境段告警级别~CID1=91H>>    <<设置环境段告警级别~CID2=82H>>    环境温度低    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取环境段告警级别~CID1=91H>>    <<获取环境段告警级别~CID2=81H>>    环境温度低    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    环境温度低    ${VAR}
        ${环境温度低告警级别}    获取web参数量    环境温度低
        should be equal    ${1104告警级别}    ${环境温度低告警级别}
        Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    环境温度低    次要

1363_0010_3环境温度无效
    [Documentation]    获取1104一条告警值并判断告警存在
    ...    输入
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${理应值}= | ${屏号}=None | ${g_ver}=
    ...
    ...
    ...
    ...    0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning; 0xFF：无效值
    ...
    ...    设置
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${var_name} | ${val} || ${屏号}=None | ${g_ver}=、
    ...
    ...    获取
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${屏号}=None | ${g_ver}=
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置环境段告警级别~CID1=91H>>    <<设置环境段告警级别~CID2=82H>>    环境温度无效    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取环境段告警级别~CID1=91H>>    <<获取环境段告警级别~CID2=81H>>    环境温度无效    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    环境温度无效    ${VAR}
        ${环境温度无效告警级别}    获取web参数量    环境温度无效
        should be equal    ${1104告警级别}    ${环境温度无效告警级别}
    END
    [Teardown]    设置web参数量    环境温度无效    屏蔽

1363_0012_4环境湿度高
    [Documentation]    获取1104一条告警值并判断告警存在
    ...    输入
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${理应值}= | ${屏号}=None | ${g_ver}=
    ...
    ...
    ...
    ...    0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning; 0xFF：无效值
    ...
    ...    设置
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${var_name} | ${val} || ${屏号}=None | ${g_ver}=、
    ...
    ...    获取
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${屏号}=None | ${g_ver}=
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置环境段告警级别~CID1=91H>>    <<设置环境段告警级别~CID2=82H>>    环境湿度高    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取环境段告警级别~CID1=91H>>    <<获取环境段告警级别~CID2=81H>>    环境湿度高    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    环境湿度高    ${VAR}
        ${环境湿度高告警级别}    获取web参数量    环境湿度高
        should be equal    ${1104告警级别}    ${环境湿度高告警级别}
    END
    [Teardown]    设置web参数量    环境湿度高    次要

1363_0014_5环境湿度低
    [Documentation]    获取1104一条告警值并判断告警存在
    ...    输入
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${理应值}= | ${屏号}=None | ${g_ver}=
    ...
    ...
    ...
    ...    0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning; 0xFF：无效值
    ...
    ...    设置
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${var_name} | ${val} || ${屏号}=None | ${g_ver}=、
    ...
    ...    获取
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${屏号}=None | ${g_ver}=
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置环境段告警级别~CID1=91H>>    <<设置环境段告警级别~CID2=82H>>    环境湿度低    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取环境段告警级别~CID1=91H>>    <<获取环境段告警级别~CID2=81H>>    环境湿度低    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    环境湿度低    ${VAR}
        ${环境湿度低告警级别}    获取web参数量    环境湿度低
        should be equal    ${1104告警级别}    ${环境湿度低告警级别}
    END
    [Teardown]    设置web参数量    环境湿度低    次要

1363_0016_6环境湿度无效
    [Documentation]    获取1104一条告警值并判断告警存在
    ...    输入
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${理应值}= | ${屏号}=None | ${g_ver}=
    ...
    ...
    ...
    ...    0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning; 0xFF：无效值
    ...
    ...    设置
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${var_name} | ${val} || ${屏号}=None | ${g_ver}=、
    ...
    ...    获取
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${屏号}=None | ${g_ver}=
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置环境段告警级别~CID1=91H>>    <<设置环境段告警级别~CID2=82H>>    环境湿度无效    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取环境段告警级别~CID1=91H>>    <<获取环境段告警级别~CID2=81H>>    环境湿度无效    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    环境湿度无效    ${VAR}
        ${环境湿度无效告警级别}    获取web参数量    环境湿度无效
        should be equal    ${1104告警级别}    ${环境湿度无效告警级别}
    END
    [Teardown]    设置web参数量    环境湿度无效    屏蔽

1363_0018_7烟雾告警
    [Documentation]    获取1104一条告警值并判断告警存在
    ...    输入
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${理应值}= | ${屏号}=None | ${g_ver}=
    ...
    ...
    ...
    ...    0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning; 0xFF：无效值
    ...
    ...    设置
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${var_name} | ${val} || ${屏号}=None | ${g_ver}=、
    ...
    ...    获取
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${屏号}=None | ${g_ver}=
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置环境段告警级别~CID1=91H>>    <<设置环境段告警级别~CID2=82H>>    烟雾告警    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取环境段告警级别~CID1=91H>>    <<获取环境段告警级别~CID2=81H>>    烟雾告警    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    烟雾告警    ${VAR}
        ${烟雾告警告警级别}    获取web参数量    烟雾告警
        should be equal    ${1104告警级别}    ${烟雾告警告警级别}
    END
    [Teardown]    设置web参数量    烟雾告警    主要

1363_0020_8水淹告警
    [Documentation]    获取1104一条告警值并判断告警存在
    ...    输入
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${理应值}= | ${屏号}=None | ${g_ver}=
    ...
    ...
    ...
    ...    0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning; 0xFF：无效值
    ...
    ...    设置
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${var_name} | ${val} || ${屏号}=None | ${g_ver}=、
    ...
    ...    获取
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${屏号}=None | ${g_ver}=
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置环境段告警级别~CID1=91H>>    <<设置环境段告警级别~CID2=82H>>    水淹告警    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取环境段告警级别~CID1=91H>>    <<获取环境段告警级别~CID2=81H>>    水淹告警    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    水淹告警    ${VAR}
        ${水淹告警级别}    获取web参数量    水淹告警
        should be equal    ${1104告警级别}    ${水淹告警级别}
    END
    [Teardown]    设置web参数量    水淹告警    主要

1363_0022_9门磁告警
    [Documentation]    获取1104一条告警值并判断告警存在
    ...    输入
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${理应值}= | ${屏号}=None | ${g_ver}=
    ...
    ...
    ...
    ...    0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning; 0xFF：无效值
    ...
    ...    设置
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${var_name} | ${val} || ${屏号}=None | ${g_ver}=、
    ...
    ...    获取
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${屏号}=None | ${g_ver}=
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置环境段告警级别~CID1=91H>>    <<设置环境段告警级别~CID2=82H>>    门磁告警    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取环境段告警级别~CID1=91H>>    <<获取环境段告警级别~CID2=81H>>    门磁告警    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    门磁告警    ${VAR}
        ${门磁告警级别}    获取web参数量    门磁告警
        should be equal    ${1104告警级别}    ${门磁告警级别}
    END
    [Teardown]    设置web参数量    门磁告警    主要

1363_0024_10门禁告警
    [Documentation]    获取1104一条告警值并判断告警存在
    ...    输入
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${理应值}= | ${屏号}=None | ${g_ver}=
    ...
    ...
    ...
    ...    0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning; 0xFF：无效值
    ...
    ...    设置
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${var_name} | ${val} || ${屏号}=None | ${g_ver}=、
    ...
    ...    获取
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${屏号}=None | ${g_ver}=
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置环境段告警级别~CID1=91H>>    <<设置环境段告警级别~CID2=82H>>    门禁告警    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取环境段告警级别~CID1=91H>>    <<获取环境段告警级别~CID2=81H>>    门禁告警    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    门禁告警    ${VAR}
        ${门禁告警告警级别}    获取web参数量    门禁告警
        should be equal    ${1104告警级别}    ${门禁告警告警级别}
    END
    [Teardown]    设置web参数量    门禁告警    次要
