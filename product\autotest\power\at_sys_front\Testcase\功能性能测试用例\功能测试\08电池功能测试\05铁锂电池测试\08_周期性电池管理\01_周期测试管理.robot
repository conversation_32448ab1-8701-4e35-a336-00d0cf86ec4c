*** Settings ***
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
充电模式下周期测试功能
    
    铁锂电池管理初始化
    设置web参数量    电池测试周期    60    #默认0天
    设置web参数量    电池检测周期    0    #默认30
    sleep    5
    ${电池测试周期}    获取web参数量    电池测试周期
    ${下次测试时间}    获取web实时数据    下次测试时间
    ${下次测试时间0}    Subtract Time From Date    ${下次测试时间}    5s    #比10:00:00差5s
    设置系统时间    ${下次测试时间0}
    sleep    8
    ${修改时间后的系统时间}    获取系统时间
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    测试
    ${下次测试时间_cal}    add time to date    ${下次测试时间}    ${电池测试周期}d    exclude_millis=yes
    sleep    5
    ${下次测试时间_new}    获取web实时数据    下次测试时间
    should be equal    ${下次测试时间_new}    ${下次测试时间_cal}
    设置web控制量    启动充电
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    充电
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池测试周期    电池检测周期

测试模式下周期测试功能
    [Tags]    notest
    铁锂电池管理初始化
    设置web参数量    电池测试周期    60    #默认0天
    设置web参数量    电池检测周期    0    #默认30
    sleep    5
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动测试
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    测试
    ${电池测试周期}    获取web参数量    电池测试周期
    ${下次测试时间}    获取web实时数据    下次测试时间
    ${下次测试时间0}    Subtract Time From Date    ${下次测试时间}    5s    #比10:00:00差5s
    设置系统时间    ${下次测试时间0}
    sleep    8
    ${修改时间后的系统时间}    获取系统时间
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    测试
    ${下次测试时间_cal}    add time to date    ${下次测试时间}    ${电池测试周期}d    exclude_millis=yes
    sleep    5
    ${下次测试时间_new}    获取web实时数据    下次测试时间
    should be equal    ${下次测试时间_new}    ${下次测试时间_cal}
    设置web控制量    启动充电
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    充电
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池测试周期    电池检测周期

电池检测模式下周期测试功能
    
    铁锂电池管理初始化
    设置web参数量    电池测试周期    80
    设置web参数量    电池检测周期    0
    #设置电池检测持续时间
    ${缺省值}    获取web参数上下限范围    电池检测持续时间
    设置web参数量    电池检测持续时间    ${缺省值}[0]
    sleep    5
    ${设置值}    获取web参数量    电池检测持续时间
    ${等待时间}    evaluate    ${设置值} +2
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    10    1    信号量数据值为    电池管理状态    检测
    sleep    30
    ${电池测试周期}    获取web参数量    电池测试周期
    ${下次测试时间}    Wait Until Keyword Succeeds    30    1    获取web实时数据    下次测试时间
    ${下次测试时间0}    Subtract Time From Date    ${下次测试时间}    5s    #比10:00:00差5s
    设置系统时间    ${下次测试时间0}
    Wait Until Keyword Succeeds    10    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    ${等待时间}m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    10    1    信号量数据值为    电池管理状态    测试
    sleep    1m
    ${下次测试时间_cal}    add time to date    ${下次测试时间}    ${电池测试周期}d    exclude_millis=yes
    ${下次测试时间_new}    获取web实时数据    下次测试时间
    should be equal    ${下次测试时间_new}    ${下次测试时间_cal}
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池测试周期    电池检测周期

测试周期为0时禁止周期测试
    
    铁锂电池管理初始化
    设置web控制量    启动充电
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    充电
    设置web参数量    电池测试周期    60    #默认0天
    设置web参数量    电池检测周期    0    #默认0
    sleep    5
    ${电池测试周期}    获取web参数量    电池测试周期
    ${下次测试时间}    获取web实时数据    下次测试时间
    ${下次测试时间0}    Subtract Time From Date    ${下次测试时间}    5s    #比10:00:00差5s
    设置web参数量    电池测试周期    0
    sleep    5
    设置系统时间    ${下次测试时间0}
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    充电
    ${系统时间}    Subtract Time From Date    ${下次测试时间0}    7 days
    设置系统时间    ${系统时间}
    sleep    5
    设置web参数量    电池测试周期    60    #默认0天
    sleep    5
    ${下次测试时间_new}    获取web实时数据    下次测试时间
    should be equal    ${下次测试时间_new}    ${下次测试时间}
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池测试周期    电池检测周期

周期测试时设置测试周期为0
    
    铁锂电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池测试周期    60    #默认0天
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测周期    0    #默认30
    ${测试最长时间范围}    获取web参数上下限范围_有单位    测试最长时间
    ${测试最长时间}    run keyword if    '${测试最长时间范围}[3]'== 'Hour'    evaluate    ${测试最长时间范围}[1]+1
    ...    ELSE    evaluate    ${测试最长时间范围}[1]+5
    Wait Until Keyword Succeeds    10    1    设置web参数量    测试最长时间    ${测试最长时间}
    sleep    5
    ${电池测试周期}    获取web参数量    电池测试周期
    ${下次测试时间}    获取web实时数据    下次测试时间
    ${下次测试时间0}    Subtract Time From Date    ${下次测试时间}    5s    #比10:00:00差5s
    设置系统时间    ${下次测试时间0}
    sleep    8
    ${修改时间后的系统时间}    获取系统时间
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    测试
    ${下次测试时间_cal}    add time to date    ${下次测试时间}    ${电池测试周期}d    exclude_millis=yes
    sleep    5
    ${下次测试时间_new}    获取web实时数据    下次测试时间
    should be equal    ${下次测试时间_new}    ${下次测试时间_cal}
    ${测试时长l}    获取web实时数据    电池状态持续时间
    should be true    ${测试时长l} < 5
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池测试周期    0    #默认0天
    sleep    1m
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    测试
    ${测试时长newl}    获取web实时数据    电池状态持续时间
    should be true    ${测试时长l} <= ${测试时长newl}
    ${下次测试时间3}    run keyword and ignore error    获取web实时数据    下次测试时间
    should be true    '${下次测试时间3}[0]' == 'PASS' and '${下次测试时间3}[1]' == ''
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池测试周期    电池检测周期    测试最长时间

测试周期对下次测试时间影响
    
    铁锂电池管理初始化
    设置web参数量    电池测试周期    60    #默认0天
    设置web参数量    电池检测周期    0    #默认30
    sleep    5
    ${电池测试周期}    获取web参数量    电池测试周期
    ${下次测试时间}    获取web实时数据    下次测试时间
    ${下次测试时间}    Run Keyword If     '${下次测试时间}'=='0000-00-00 00:00:00.000000'    Run Keywords    sleep    5m
    ...    AND    获取web实时数据    下次测试时间
    ...    ELSE   set variable    ${下次测试时间}
    ${下次测试时间0}    Subtract Time From Date    ${下次测试时间}    5s    #比10:00:00差5s
    设置系统时间    ${下次测试时间0}
    sleep    8
    ${修改时间后的系统时间}    获取系统时间
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    测试
    ${下次测试时间_cal}    add time to date    ${下次测试时间}    ${电池测试周期}d    exclude_millis=yes
    sleep    5
    ${下次测试时间_new}    获取web实时数据    下次测试时间
    should be equal    ${下次测试时间_new}    ${下次测试时间_cal}
    设置web参数量    电池测试周期    70    #默认0天
    ${电池测试周期new}    获取web参数量    电池测试周期
    ${下次测试时间_cal1}    add time to date    ${下次测试时间}    ${电池测试周期new}d    exclude_millis=yes
    sleep    5
    ${下次测试时间_new1}    获取web实时数据    下次测试时间
    should be equal    ${下次测试时间_new1}    ${下次测试时间_cal1}
    设置web控制量    启动充电
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    充电
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池测试周期    电池检测周期
