*** Settings ***
Suite Setup       run keywords    连接CSU
...               AND             恢复默认值并重新登录
...               AND             开启所有的模拟子工具    #开启所有的模拟子工具
Suite Teardown    关闭所有的模拟子工具    #关闭所有的模拟子工具
Resource          ../../../../../测试用例关键字.robot


*** Test Cases ***
油电场景+普通铅酸+FB100B3
    [Tags]    PMSA-NTest
    [Setup]    测试用例前置条件
    写入CSV文档    普通铅酸参数测试    参数名称    结果
    连接CSU
    #将所有使能禁止的参数先设置为允许，以便隐藏的参数显示出来
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流输入场景    油电    #油电场景测试
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    铅酸&锂电混用
    Wait Until Keyword Succeeds    10    1    设置web参数量    铅酸类型    普通铅酸电池
    Wait Until Keyword Succeeds    10    1    设置web参数量    智能锂电类型    FB100B3电池
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池应用场景    备电场景
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流电表回路1配置    其他
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流电表回路2配置    其他
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流电表回路3配置    其他
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流电表回路4配置    其他
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流电表回路1配置-2    其他
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流电表回路2配置-2    其他
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流电表回路3配置-2    其他
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流电表回路4配置-2    其他
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流电表回路1配置-3    其他
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流电表回路2配置-3    其他
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流电表回路3配置-3    其他
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流电表回路4配置-3    其他
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_2    100
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_3    100
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_4    100
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_5    100
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_6    100
    Wait Until Keyword Succeeds    10    1    设置web参数量    风扇配置_1    有
    Wait Until Keyword Succeeds    10    1    设置web参数量    风扇配置_2    有
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    0    1    无    无
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp2}    0    1    无    无
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp3}    0    1    无    无
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    0    1    无    无
    显示属性配置    风扇PWM    参数量    web_attr=On    gui_attr=On
    #将参数的使能开关全部打开
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=parameter
    ${排除列表}    create list    INDEX_OPTIONAL_ATTRIBUTE=smart_li_batt    INDEX_OPTIONAL_ATTRIBUTE=nor_li_batt_pack    INDEX_OPTIONAL_ATTRIBUTE=nor_li_batt
    ${列表}    获取数据字典所有可设置允许的参数列表    ${参数列表}    ${排除设备}    ${铅酸电池排除信号}    ${排除列表}
    FOR    ${i}    IN    @{列表}
        Run Keyword And Continue On Failure    禁止类参数设置为允许    ${i}
    END
    #将默认值为0的参数设置为非0，使得相关屏蔽参数被显示。
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=parameter    INDEX_DEF_VALUE=0
    ${排除列表}    create list    INDEX_OPTIONAL_ATTRIBUTE=smart_li_batt    INDEX_OPTIONAL_ATTRIBUTE=nor_li_batt_pack    INDEX_OPTIONAL_ATTRIBUTE=nor_li_batt
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    ${排除设备}    ${铅酸电池排除信号}    ${排除列表}
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${设备名称}    Get From Dictionary    ${i}    device_name
        ${取值约定}    Get From Dictionary    ${i}    convention
        run keyword if    '${取值约定}'=='False'    Run Keyword And Continue On Failure    默认值为0的数值类参数设置为非0    ${信号名称}
    END
    #将字符串型的参数取出进行设置。
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=parameter    INDEX_DATA_TYPE=string32
    ${排除列表}    create list    INDEX_OPTIONAL_ATTRIBUTE=smart_li_batt    INDEX_OPTIONAL_ATTRIBUTE=nor_li_batt_pack    INDEX_OPTIONAL_ATTRIBUTE=nor_li_batt
    ${列表2}    获取数据字典指定查询的信号量列表    ${参数列表}    ${排除设备}    ${铅酸电池排除信号}    ${排除列表}
    FOR    ${i}    IN    @{列表2}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${设备名称}    Get From Dictionary    ${i}    device_name
        Run Keyword And Continue On Failure    32位字符串型参数设置封装判断结果    ${信号名称}    普通铅酸参数测试
    END
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=parameter    INDEX_DATA_TYPE=string64
    ${排除列表}    create list    INDEX_OPTIONAL_ATTRIBUTE=smart_li_batt    INDEX_OPTIONAL_ATTRIBUTE=nor_li_batt_pack    INDEX_OPTIONAL_ATTRIBUTE=nor_li_batt
    ${列表3}    获取数据字典指定查询的信号量列表    ${参数列表}    ${排除设备}    ${铅酸电池排除信号}    ${排除列表}
    FOR    ${i}    IN    @{列表3}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${设备名称}    Get From Dictionary    ${i}    device_name
        Run Keyword And Continue On Failure    64位字符串型参数设置封装判断结果    ${信号名称}    普通铅酸参数测试
    END
    #日期类型的参数设置
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=parameter    INDEX_DATA_TYPE=date
    ${排除列表}    create list    INDEX_OPTIONAL_ATTRIBUTE=smart_li_batt    INDEX_OPTIONAL_ATTRIBUTE=nor_li_batt_pack    INDEX_OPTIONAL_ATTRIBUTE=nor_li_batt
    ${列表4}    获取数据字典指定查询的信号量列表    ${参数列表}    ${排除设备}    ${铅酸电池排除信号}    ${排除列表}
    FOR    ${i}    IN    @{列表4}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${设备名称}    Get From Dictionary    ${i}    device_name
        Run Keyword And Continue On Failure    date型参数设置封装判断结果    ${信号名称}    普通铅酸参数测试
    END
    #hour_minute类型的参数设置
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=parameter    INDEX_DATA_TYPE=hour_minute
    ${排除列表}    create list    INDEX_OPTIONAL_ATTRIBUTE=smart_li_batt    INDEX_OPTIONAL_ATTRIBUTE=nor_li_batt_pack    INDEX_OPTIONAL_ATTRIBUTE=nor_li_batt
    ${列表5}    获取数据字典指定查询的信号量列表    ${参数列表}    ${排除设备}    ${铅酸电池排除信号}    ${排除列表}
    FOR    ${i}    IN    @{列表5}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${设备名称}    Get From Dictionary    ${i}    device_name
        Run Keyword And Continue On Failure    hour_minute型参数设置封装判断结果    ${信号名称}    普通铅酸参数测试
    END
    #参数类型的参数进行设置，无取值约定的参数设置完成后恢复为默认值，有取值约定的参数只设置，不恢复默认值
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=parameter    INDEX_DATA_TYPE=int
    ${排除列表}    create list    INDEX_OPTIONAL_ATTRIBUTE=smart_li_batt    INDEX_OPTIONAL_ATTRIBUTE=nor_li_batt_pack    INDEX_OPTIONAL_ATTRIBUTE=nor_li_batt
    ${列表}    获取数据字典指定查询的信号量列表    ${参数列表}    ${排除设备}    ${铅酸电池排除信号}    ${排除列表}
    FOR    ${i}    IN    @{列表}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${取值约定}    Get From Dictionary    ${i}    convention
        ${设备名称}    Get From Dictionary    ${i}    device_name
        有效设备进行参数设置    ${取值约定}    ${信号名称}    普通铅酸参数测试
    END
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=parameter    INDEX_DATA_TYPE=float
    ${排除列表}    create list    INDEX_OPTIONAL_ATTRIBUTE=smart_li_batt    INDEX_OPTIONAL_ATTRIBUTE=nor_li_batt_pack    INDEX_OPTIONAL_ATTRIBUTE=nor_li_batt
    ${列表}    获取数据字典指定查询的信号量列表    ${参数列表}    ${排除设备}    ${铅酸电池排除信号}    ${排除列表}
    FOR    ${i}    IN    @{列表}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${取值约定}    Get From Dictionary    ${i}    convention
        ${设备名称}    Get From Dictionary    ${i}    device_name
        有效设备进行参数设置    ${取值约定}    ${信号名称}    普通铅酸参数测试
    END
    #智能锂电场景告警参数进行设置
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm
    ${列表}    获取数据字典指定查询的信号量列表_无设备下标    ${参数列表}    ${智能锂电排除设备}    ${铅酸电池排除信号}
    FOR    ${i}    IN    @{列表}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${设备名称}    Get From Dictionary    ${i}    device_name
        Run Keyword And Continue On Failure    告警级别和输出干接点类参数设置封装判断结果    ${信号名称}    普通铅酸参数测试
        Run Keyword And Continue On Failure    告警级别和输出干接点恢复默认值    ${信号名称}
    END
    @{参数列表}    create list    INDEX_FULL_NAME_CH=输入干接点告警
    ${排除列表}    create list    INDEX_OPTIONAL_ATTRIBUTE=smart_li_batt    INDEX_OPTIONAL_ATTRIBUTE=nor_li_batt_pack    INDEX_OPTIONAL_ATTRIBUTE=nor_li_batt
    ${列表}    获取数据字典指定查询的信号量列表    ${参数列表}    ${智能锂电排除设备}    ${铅酸电池排除信号}    ${排除列表}
    FOR    ${i}    IN    @{列表}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${设备名称}    Get From Dictionary    ${i}    device_name
        Run Keyword And Continue On Failure    告警级别和输出干接点类参数设置封装判断结果    ${信号名称}    普通铅酸参数测试
        Run Keyword And Continue On Failure    告警级别和输出干接点恢复默认值    ${信号名称}
    END
    #将默认值为0的参数恢复默认值
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=parameter    INDEX_DEF_VALUE=0
    ${排除列表}    create list    INDEX_OPTIONAL_ATTRIBUTE=smart_li_batt    INDEX_OPTIONAL_ATTRIBUTE=nor_li_batt_pack    INDEX_OPTIONAL_ATTRIBUTE=nor_li_batt
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    ${排除设备}    ${铅酸电池排除信号}    ${排除列表}
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${取值约定}    Get From Dictionary    ${i}    convention
        run keyword if    '${取值约定}'=='False'    Run Keyword And Continue On Failure    设置web设备参数量为默认值    ${信号名称}
    END
    #有取值约定的参数恢复默认值
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=parameter
    ${排除列表}    create list    INDEX_OPTIONAL_ATTRIBUTE=smart_li_batt    INDEX_OPTIONAL_ATTRIBUTE=nor_li_batt_pack    INDEX_OPTIONAL_ATTRIBUTE=nor_li_batt
    ${列表}    获取数据字典所有不可设置允许的参数列表    ${参数列表}    ${排除设备}    ${铅酸电池排除信号}    ${排除列表}
    FOR    ${i}    IN    @{列表}
    Run Keyword And Continue On Failure    设置web设备参数量为默认值    ${i}
    END
    #将使能参数恢复默认值
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=parameter
    ${排除列表}    create list    INDEX_OPTIONAL_ATTRIBUTE=smart_li_batt    INDEX_OPTIONAL_ATTRIBUTE=nor_li_batt_pack    INDEX_OPTIONAL_ATTRIBUTE=nor_li_batt
    ${列表}    获取数据字典所有可设置允许的参数列表    ${参数列表}    ${排除设备}    ${铅酸电池排除信号}    ${排除列表}
    FOR    ${i}    IN    @{列表}
    Run Keyword And Continue On Failure    设置web设备参数量为默认值    ${i}
    END
    #常规锂电场景告警参数进行设置
    控制子工具运行停止    smartli    关闭
    控制子工具运行停止    fb100c2    启动
    ${取值约定}    获取web参数的取值约定    电池配置
    ${val}    Get From Dictionary    ${取值约定}    4
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    ${val}
    run keyword and ignore error    Wait Until Keyword Succeeds    10    1    设置web参数量    常规锂电类型    NFB15
    sleep    1m
    Wait Until Keyword Succeeds    10    2    设置web控制量    RS485总线设备统计
    sleep    5m
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_EN=NFBBMS
    ${列表}    获取数据字典指定查询的信号量列表_无设备下标    ${参数列表}    null    ${铅酸电池排除信号}
    FOR    ${i}    IN    @{列表}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${设备名称}    Get From Dictionary    ${i}    device_name
        Run Keyword And Continue On Failure    告警级别和输出干接点类参数设置封装判断结果    ${信号名称}    普通铅酸参数测试
        Run Keyword And Continue On Failure    告警级别和输出干接点恢复默认值    ${信号名称}
    END
    控制子工具运行停止    fb100c2    关闭
    [Teardown]    Run Keywords    控制子工具运行停止    fb100c2    关闭
    ...           AND    关闭所有的模拟子工具
    ...           AND    恢复默认值并重新登录    #SUPERMAN用户支持用户参数和站点配置参数恢复，


