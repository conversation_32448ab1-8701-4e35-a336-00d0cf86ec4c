*** Settings ***
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
各管理状态下设置充电
    
    铁锂电池管理初始化

系统停电时设置充电
    
    铁锂电池管理初始化
    关闭交流源输出
    设置负载电压电流    53.5    10
    打开负载输出
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    系统停电
    Wait Until Keyword Succeeds    5m    1    设置失败的Web控制量    启动充电
    [Teardown]    Run keywords    关闭负载输出
    ...    AND    打开交流源输出

电池组无效时设置充电
    
    铁锂电池管理初始化
    Wait Until Keyword Succeeds    5m    1s    设置web参数量    电池组容量_1    0
    sleep    3
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动充电
    [Teardown]    设置web参数量    电池组容量_1    100

充电保护时设置充电
    
    铁锂电池管理初始化
    显示属性配置    电池充电保护    数字量    web_attr=On    gui_attr=On
    设置子工具值    smartli    all    只读    电池充电保护    1
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    电池充电保护-1     是
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    电池充电保护-2     是
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    电池充电保护-3    是
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动充电
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    充电
    显示属性配置    电池充电保护    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    设置子工具值    smartli    all    只读    电池充电保护    0

放电保护时设置充电
    
    铁锂电池管理初始化
    显示属性配置    电池放电保护    数字量    web_attr=On    gui_attr=On
    设置子工具值    smartli    all    只读    电池放电保护    1
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    电池放电保护-1     是
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    电池放电保护-2     是
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    电池放电保护-3    是
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动充电
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    充电
    显示属性配置    电池充电保护    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    设置子工具值    smartli    all    只读    电池放电保护    0

BMS通讯断时设置充电
    
    铁锂电池管理初始化
    显示属性配置    BMS通信断状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    控制子工具运行停止    smartli    关闭
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    BMS通信断状态-1     异常
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    BMS通信断状态-2     异常
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    BMS通信断状态-3    异常
    ${级别设置值}    获取web参数量    BMS通信断告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    BMS通信断告警    严重
    wait until keyword succeeds    5m    2    查询指定告警信息    BMS通信断告警
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动充电
    重新启动FB100B3    3
    ${级别设置值}    获取web参数量    BMS通信断告警
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    BMS通信断状态-1     正常
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    BMS通信断状态-2     正常
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    BMS通信断状态-3    正常
    wait until keyword succeeds    5m    2    查询指定告警信息不为    BMS通信断告警
    显示属性配置    BMS通信断状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    重新启动FB100B3    3
