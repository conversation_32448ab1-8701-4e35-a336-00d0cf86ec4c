*** Settings ***
Suite Setup       #Run keywords    测试用例前置条件    # AND    仅有市电条件上电
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
PU输入过压告警测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具值    PU    all    数字量    PU输入过压告警    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入过压状态-1    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入过压状态-10    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入过压状态-20    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入过压状态-30    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入过压状态-40    异常
    wait until keyword succeeds    5m    2    查询指定内部告警信息    PU输入过压告警
    wait until keyword succeeds    5m    2    查询指定告警信息    PU告警
    设置子工具值    PU    all    数字量    PU输入过压告警    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入过压状态-1    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入过压状态-10    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入过压状态-20    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入过压状态-30    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入过压状态-40    正常
    wait until keyword succeeds    5m    1    判断内部告警不存在    PU输入过压告警
    wait until keyword succeeds    5m    1    判断告警不存在    PU告警
    [Teardown]    设置子工具告警量    all    数字量    PU输入过压告警    0

PU输出过压告警测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具值    PU    all    数字量    PU输出过压告警    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出过压状态-1    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出过压状态-10    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出过压状态-20    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出过压状态-30    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出过压状态-40    异常
    wait until keyword succeeds    5m    2    查询指定内部告警信息    PU输出过压告警
    wait until keyword succeeds    5m    2    查询指定告警信息    PU故障
    设置子工具值    PU    all    数字量    PU输出过压告警    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出过压状态-1    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出过压状态-10    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出过压状态-20    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出过压状态-30    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出过压状态-40    正常
    wait until keyword succeeds    5m    1    判断内部告警不存在    PU输出过压告警
    wait until keyword succeeds    5m    1    判断告警不存在    PU故障
    [Teardown]    设置子工具告警量    all    数字量    PU输出过压告警    0

PU过温告警测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具值    PU    all    数字量    PU过温告警    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU过温状态-1    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU过温状态-10    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU过温状态-20    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU过温状态-30    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU过温状态-40    异常
    wait until keyword succeeds    5m    2    查询指定内部告警信息    PU过温告警
    wait until keyword succeeds    5m    2    查询指定告警信息    PU告警
    设置子工具值    PU    all    数字量    PU过温告警    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU过温状态-1    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU过温状态-10    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU过温状态-20    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU过温状态-30    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU过温状态-40    正常
    wait until keyword succeeds    5m    1    判断内部告警不存在    PU过温告警
    wait until keyword succeeds    5m    1    判断告警不存在    PU告警
    [Teardown]    设置子工具告警量    all    数字量    PU过温告警    0

PU输出过流告警测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具值    PU    all    数字量    PU输出过流告警    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出过流状态-1    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出过流状态-10    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出过流状态-20    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出过流状态-30    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出过流状态-40    异常
    wait until keyword succeeds    5m    2    查询指定内部告警信息    PU输出过流
    wait until keyword succeeds    5m    2    查询指定告警信息    PU故障
    设置子工具值    PU    all    数字量    PU输出过流告警    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出过流状态-1    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出过流状态-10    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出过流状态-20    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出过流状态-30    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出过流状态-40    正常
    wait until keyword succeeds    5m    1    判断内部告警不存在    PU输出过流
    wait until keyword succeeds    5m    1    判断告警不存在    PU故障
    [Teardown]    设置子工具告警量    all    数字量    PU输出过流告警    0

PU输出熔丝断告警测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具值    PU    all    数字量    PU输出熔丝断告警    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出熔丝断状态-1    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出熔丝断状态-10    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出熔丝断状态-20    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出熔丝断状态-30    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出熔丝断状态-40    异常
    wait until keyword succeeds    5m    2    查询指定内部告警信息    PU输出熔丝断告警
    wait until keyword succeeds    5m    2    查询指定告警信息    PU告警
    设置子工具值    PU    all    数字量    PU输出熔丝断告警    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出熔丝断状态-1    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出熔丝断状态-10    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出熔丝断状态-20    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出熔丝断状态-30    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出熔丝断状态-40    正常
    wait until keyword succeeds    5m    1    判断内部告警不存在    PU输出熔丝断告警
    wait until keyword succeeds    5m    1    判断告警不存在    PU告警
    [Teardown]    设置子工具告警量    all    数字量    PU输出熔丝断告警    0

PU散热器过温告警测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具值    PU    all    数字量    PU散热器过温告警    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU散热器过温状态-1    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU散热器过温状态-10    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU散热器过温状态-20    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU散热器过温状态-30    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU散热器过温状态-40    异常
    wait until keyword succeeds    5m    2    查询指定内部告警信息    PU散热器过温告警
    wait until keyword succeeds    5m    2    查询指定告警信息    PU告警
    设置子工具值    PU    all    数字量    PU散热器过温告警    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU散热器过温状态-1    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU散热器过温状态-10    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU散热器过温状态-20    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU散热器过温状态-30    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU散热器过温状态-40    正常
    wait until keyword succeeds    5m    1    判断内部告警不存在    PU散热器过温告警
    wait until keyword succeeds    5m    1    判断告警不存在    PU告警
    [Teardown]    设置子工具告警量    all    数字量    PU散热器过温告警    0

PU风扇故障告警测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具值    PU    all    数字量    PU风扇故障    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU风扇故障状态-1    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU风扇故障状态-10    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU风扇故障状态-20    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU风扇故障状态-30    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU风扇故障状态-40    异常
    wait until keyword succeeds    5m    2    查询指定内部告警信息    PU风扇故障
    wait until keyword succeeds    5m    2    查询指定告警信息    PU故障
    设置子工具值    PU    all    数字量    PU风扇故障    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU风扇故障状态-1    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU风扇故障状态-10    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU风扇故障状态-20    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU风扇故障状态-30    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU风扇故障状态-40    正常
    wait until keyword succeeds    5m    1    判断内部告警不存在    PU风扇故障
    wait until keyword succeeds    5m    1    判断告警不存在    PU故障
    [Teardown]    设置子工具告警量    all    数字量    PU风扇故障    0

PU EEPROM异常告警测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具值    PU    all    数字量    PU EEPROM异常    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU EEPROM异常状态-1    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU EEPROM异常状态-10    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU EEPROM异常状态-20    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU EEPROM异常状态-30    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU EEPROM异常状态-40    异常
    wait until keyword succeeds    5m    2    查询指定内部告警信息    PU EEPROM异常
    wait until keyword succeeds    5m    2    查询指定告警信息    PU故障
    设置子工具值    PU    all    数字量    PU EEPROM异常    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU EEPROM异常状态-1    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU EEPROM异常状态-10    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU EEPROM异常状态-20    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU EEPROM异常状态-30    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU EEPROM异常状态-40    正常
    wait until keyword succeeds    5m    1    判断内部告警不存在    PU EEPROM异常
    wait until keyword succeeds    5m    1    判断告警不存在    PU故障
    [Teardown]    设置子工具告警量    all    数字量    PU EEPROM异常    0

PU输出欠压告警测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具值    PU    all    数字量    PU 输出欠压    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU 输出欠压状态-1    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU 输出欠压状态-10    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU 输出欠压状态-20    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU 输出欠压状态-30    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU 输出欠压状态-40    异常
    wait until keyword succeeds    5m    2    查询指定内部告警信息    PU 输出欠压
    wait until keyword succeeds    5m    2    查询指定告警信息    PU告警
    设置子工具值    PU    all    数字量    PU 输出欠压    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU 输出欠压状态-1    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU 输出欠压状态-10    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU 输出欠压状态-20    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU 输出欠压状态-30    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU 输出欠压状态-40    正常
    wait until keyword succeeds    5m    1    判断内部告警不存在    PU 输出欠压
    wait until keyword succeeds    5m    1    判断告警不存在    PU告警
    [Teardown]    设置子工具告警量    all    数字量    PU 输出欠压    0

光伏回路异常告警测试
    [Tags]    no test
    [Setup]
    连接CSU
    Comment    关闭交流源输出
    显示属性配置    PU 输出欠压状态    数字量    web_attr=On    gui_attr=On
    #子设备工具模拟
    设置子工具告警量    1    数字量    光伏回路异常状态    1
    设置子工具告警量    2    数字量    光伏回路异常状态    1
    设置子工具告警量    3    数字量    光伏回路异常状态    1
    sleep    10m
    ${PU1光伏回路异常状态}    获取web实时数据    光伏回路异常状态-1
    ${PU2光伏回路异常状态}    获取web实时数据    光伏回路异常状态-2
    ${PU3光伏回路异常状态}    获取web实时数据    光伏回路异常状态-3
    should be true    '${PU1光伏回路异常状态}' == '异常'
    should be true    '${PU2光伏回路异常状态}' == '异常'
    should be true    '${PU3光伏回路异常状态}' == '异常'
    ${级别设置值}    获取web参数量    光伏回路异常告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    光伏回路异常告警    次要
    wait until keyword succeeds    5m    2    查询指定告警信息    光伏回路异常告警
    设置子工具告警量    1    数字量    光伏回路异常状态    0
    设置子工具告警量    2    数字量    光伏回路异常状态    0
    设置子工具告警量    3    数字量    光伏回路异常状态    0
    sleep    5m
    ${PU1光伏回路异常状态}    获取web实时数据    光伏回路异常状态-1
    ${PU2光伏回路异常状态}    获取web实时数据    光伏回路异常状态-2
    ${PU3光伏回路异常状态}    获取web实时数据    光伏回路异常状态-3
    should be true    '${PU1光伏回路异常状态}' == '正常'
    should be true    '${PU2光伏回路异常状态}' == '正常'
    should be true    '${PU3光伏回路异常状态}' == '正常'
    wait until keyword succeeds    5m    1    判断告警不存在    光伏回路异常告警

PU序列号冲突告警测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具值    PU    all    数字量    PU序列号冲突    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU序列号冲突状态-1    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU序列号冲突状态-10    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU序列号冲突状态-20    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU序列号冲突状态-30    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU序列号冲突状态-40    异常
    wait until keyword succeeds    5m    2    查询指定内部告警信息    PU序列号冲突
    wait until keyword succeeds    5m    2    查询指定告警信息    PU告警
    设置子工具值    PU    all    数字量    PU序列号冲突    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU序列号冲突状态-1    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU序列号冲突状态-10    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU序列号冲突状态-20    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU序列号冲突状态-30    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU序列号冲突状态-40    正常
    wait until keyword succeeds    5m    1    判断内部告警不存在    PU序列号冲突
    wait until keyword succeeds    5m    1    判断告警不存在    PU告警
    [Teardown]    设置子工具告警量    all    数字量    PU序列号冲突    0

PU协议错误告警测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具值    PU    all    数字量    PU协议错误    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU协议错误状态-1    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU协议错误状态-10    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU协议错误状态-20    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU协议错误状态-30    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU协议错误状态-40    异常
    wait until keyword succeeds    5m    2    查询指定内部告警信息    PU协议错误
    wait until keyword succeeds    5m    2    查询指定告警信息    PU告警
    设置子工具值    PU    all    数字量    PU协议错误    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU协议错误状态-1    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU协议错误状态-10    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU协议错误状态-20    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU协议错误状态-30    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU协议错误状态-40    正常
    wait until keyword succeeds    5m    1    判断内部告警不存在    PU协议错误
    wait until keyword succeeds    5m    1    判断告警不存在    PU告警
    [Teardown]    设置子工具告警量    all    数字量    PU协议错误    0

PU输入过流告警测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具值    PU    all    数字量    PU输入过流告警    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入过流状态-1    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入过流状态-10    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入过流状态-20    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入过流状态-30    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入过流状态-40    异常
    wait until keyword succeeds    5m    2    查询指定内部告警信息    PU输入过流告警
    wait until keyword succeeds    5m    2    查询指定告警信息    PU告警
    设置子工具值    PU    all    数字量    PU输入过流告警    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入过流状态-1    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入过流状态-10    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入过流状态-20    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入过流状态-30    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入过流状态-40    正常
    wait until keyword succeeds    5m    1    判断内部告警不存在    PU输入过流告警
    wait until keyword succeeds    5m    1    判断告警不存在    PU告警
    [Teardown]    设置子工具告警量    all    数字量    PU输入过流告警    0

PU输入欠压告警测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具值    PU    all    数字量    PU输入欠压告警    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入欠压状态-1    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入欠压状态-10    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入欠压状态-20    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入欠压状态-30    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入欠压状态-40    异常
    设置子工具值    PU    all    数字量    PU输入欠压告警    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入欠压状态-1    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入欠压状态-10    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入欠压状态-20    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入欠压状态-30    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入欠压状态-40    正常
    [Teardown]    设置子工具告警量    all    数字量    PU输入欠压告警    0

PU无输入告警测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具值    PU    all    数字量    PU无输入告警    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU无输入状态-1    是
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU无输入状态-10    是
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU无输入状态-20    是
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU无输入状态-30    是
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU无输入状态-40    是
    设置子工具值    PU    all    数字量    PU无输入告警    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU无输入状态-1    否
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU无输入状态-10    否
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU无输入状态-20    否
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU无输入状态-30    否
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU无输入状态-40    否
    [Teardown]    设置子工具告警量    all    数字量    PU无输入告警    0

PU输出短路告警测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具值    PU    all    数字量    PU输出短路告警    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出短路状态-1    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出短路状态-10    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出短路状态-20    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出短路状态-30    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出短路状态-40    异常
    wait until keyword succeeds    5m    2    查询指定内部告警信息    PU输出短路告警
    wait until keyword succeeds    5m    2    查询指定告警信息    PU告警
    设置子工具值    PU    all    数字量    PU输出短路告警    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出短路状态-1    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出短路状态-10    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出短路状态-20    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出短路状态-30    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出短路状态-40    正常
    wait until keyword succeeds    5m    1    判断内部告警不存在    PU输出短路告警
    wait until keyword succeeds    5m    1    判断告警不存在    PU告警
    [Teardown]    设置子工具告警量    all    数字量    PU输出短路告警    0

PU输入倒灌告警测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具值    PU    all    数字量    PU输入防倒灌保护    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入倒灌状态-1    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入倒灌状态-10    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入倒灌状态-20    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入倒灌状态-30    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入倒灌状态-40    异常
    设置子工具值    PU    all    数字量    PU输入防倒灌保护    0
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入倒灌状态-1    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入倒灌状态-10    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入倒灌状态-20    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入倒灌状态-30    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输入倒灌状态-40    正常
    [Teardown]    设置子工具告警量    all    数字量    PU输入防倒灌保护    0

PU通讯中断告警测试
    [Setup]
    连接CSU
    #子设备工具模拟
    设置子工具个数    pu    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU通讯状态-10    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU通讯状态-20    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU通讯状态-30    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU通讯状态-40    异常
    ${级别设置值}    获取web参数量    PU通讯中断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    PU通讯中断    严重
    wait until keyword succeeds    5m    2    查询指定告警信息    PU通讯中断
    设置子工具个数    pu    48
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU通讯状态-1    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU通讯状态-10    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU通讯状态-20    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU通讯状态-30    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU通讯状态-40    正常
    wait until keyword succeeds    15m    2    查询指定告警信息不为    PU通讯中断
    [Teardown]    run keywords    设置子工具个数    pu    48
    ...    AND    sleep    3m
