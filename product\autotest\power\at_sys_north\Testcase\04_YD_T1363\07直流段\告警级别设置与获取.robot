*** Settings ***
Documentation     告警级别
...               0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning; 0xFF：无效值
Suite Setup
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
1363_0006_1直流电压高
    [Documentation]    获取1104一条告警值并判断告警存在
    ...    输入
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${理应值}= | ${屏号}=None | ${g_ver}=
    ...
    ...
    ...
    ...    0:屏蔽/Mask;1:严重/Critical;2:主要/Major;3:次要/Minor;4:警告/Warning; 0xFF：无效值
    ...
    ...    设置
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${var_name} | ${val} || ${屏号}=None | ${g_ver}=、
    ...
    ...    获取
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${屏号}=None | ${g_ver}=
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置直流段告警级别~CID1=42H>>    <<设置直流段告警级别~CID2=82H>>    直流电压高    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        Comment    sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流告警级别~CID1=42H>>    <<获取直流告警级别~CID2=81H>>    直流电压高    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    直流电压高    ${VAR}
        ${交流电压低告警级别}    获取web参数量    直流电压高
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    直流电压高    主要

1363_0008_2直流电压低
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置直流段告警级别~CID1=42H>>    <<设置直流段告警级别~CID2=82H>>    直流电压低    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流告警级别~CID1=42H>>    <<获取直流告警级别~CID2=81H>>    直流电压低    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    直流电压低    ${VAR}
        ${交流电压低告警级别}    获取web参数量    直流电压低
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    直流电压低    主要

1363_0010_3电池回路断
    [Documentation]    获取1104一条告警值并判断告警存在
    ...    输入
    ...    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${理应值}= | ${屏号}=None | ${g_ver}=
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置直流段告警级别~CID1=42H>>    <<设置直流段告警级别~CID2=82H>>    电池回路断    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流告警级别~CID1=42H>>    <<获取直流告警级别~CID2=81H>>    电池回路断    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    电池回路断    ${VAR}
        ${交流电压低告警级别}    获取web参数量    电池回路断
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    电池回路断    主要

1363_0012_4一次下电扩展分路断
    [Documentation]    注意从web上获取告警级别时，
    ...    是 交流防雷器异常 不是 交流防雷器异常_1
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置直流段告警级别~CID1=42H>>    <<设置直流段告警级别~CID2=82H>>    一次下电扩展分路断    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流告警级别~CID1=42H>>    <<获取直流告警级别~CID2=81H>>    一次下电扩展分路断    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    一次下电扩展分路断    ${VAR}
        ${交流电压低告警级别}    获取web参数量    一次下电扩展分路断
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    一次下电扩展分路断    主要

1363_0014_5一次下电分路断x
    [Documentation]    注意从web上获取告警级别时，
    ...    是 交流防雷器异常 不是 交流防雷器异常_1
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置直流段告警级别~CID1=42H>>    <<设置直流段告警级别~CID2=82H>>    一次下电分路断    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流告警级别~CID1=42H>>    <<获取直流告警级别~CID2=81H>>    一次下电分路断    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    一次下电分路断    ${VAR}
        ${交流电压低告警级别}    获取web参数量    一次下电分路断_1
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
    END
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置直流段告警级别~CID1=42H>>    <<设置直流段告警级别~CID2=82H>>    二次下电分路断    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流告警级别~CID1=42H>>    <<获取直流告警级别~CID2=81H>>    二次下电分路断    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    二次下电分路断    ${VAR}
        ${交流电压低告警级别}    获取web参数量    二次下电分路断_1
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    一次下电分路断_1    主要    # 设置web参数量 | 一次下电分路断 | 主要

1363_0016_6二次下电扩展分路断
    [Documentation]    注意从web上获取告警级别时，
    ...    是 交流防雷器异常 不是 交流防雷器异常_1
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置直流段告警级别~CID1=42H>>    <<设置直流段告警级别~CID2=82H>>    二次下电扩展分路断    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流告警级别~CID1=42H>>    <<获取直流告警级别~CID2=81H>>    二次下电扩展分路断    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    二次下电扩展分路断    ${VAR}
        ${交流电压低告警级别}    获取web参数量    二次下电扩展分路断
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    二次下电扩展分路断    主要

1363_0018_7二次下电分路断
    [Documentation]    注意从web上获取告警级别时，
    ...    是 交流防雷器异常 不是 交流防雷器异常_1
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置直流段告警级别~CID1=42H>>    <<设置直流段告警级别~CID2=82H>>    二次下电分路断    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流告警级别~CID1=42H>>    <<获取直流告警级别~CID2=81H>>    二次下电分路断    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    二次下电分路断    ${VAR}
        ${交流电压低告警级别}    获取web参数量    二次下电分路断_1
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    二次下电分路断_1    主要

1363_0020_8电池下电扩展分路断
    [Documentation]    注意从web上获取告警级别时，
    ...    是 交流防雷器异常 不是 交流防雷器异常_1
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置直流段告警级别~CID1=42H>>    <<设置直流段告警级别~CID2=82H>>    电池下电扩展分路断    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流告警级别~CID1=42H>>    <<获取直流告警级别~CID2=81H>>    电池下电扩展分路断    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    电池下电扩展分路断    ${VAR}
        ${交流电压低告警级别}    获取web参数量    电池下电扩展分路断
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    电池下电扩展分路断    主要

1363_0022_9电池下电分路断
    [Documentation]    注意从web上获取告警级别时，
    ...    是 交流防雷器异常 不是 交流防雷器异常_1
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置直流段告警级别~CID1=42H>>    <<设置直流段告警级别~CID2=82H>>    电池下电分路断    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流告警级别~CID1=42H>>    <<获取直流告警级别~CID2=81H>>    电池下电分路断    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    电池下电分路断    ${VAR}
        ${交流电压低告警级别}    获取web参数量    电池下电分路断_1
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    电池下电分路断_1    主要

1363_0024_10直流防雷器异常
    [Documentation]    注意从web上获取告警级别时，
    ...    是 交流防雷器异常 不是 交流防雷器异常_1
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置直流段告警级别~CID1=42H>>    <<设置直流段告警级别~CID2=82H>>    直流防雷器异常    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流告警级别~CID1=42H>>    <<获取直流告警级别~CID2=81H>>    直流防雷器异常    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    直流防雷器异常    ${VAR}
        ${交流电压低告警级别}    获取web参数量    直流防雷器异常
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    直流防雷器异常    主要

1363_0026_11电池电压低告警
    [Documentation]    注意从web上获取告警级别时，
    ...    是 交流防雷器异常 不是 交流防雷器异常_1
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置直流段告警级别~CID1=42H>>    <<设置直流段告警级别~CID2=82H>>    电池电压低告警    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流告警级别~CID1=42H>>    <<获取直流告警级别~CID2=81H>>    电池电压低告警    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    电池电压低    ${VAR}
        ${交流电压低告警级别}    获取web参数量    电池电压低
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    电池电压低    主要

1363_0028_12电池电流异常X
    [Documentation]    P1104WebKeyword_V30.py 1786 Err,没有找到该中文信号名的SID 电池电流异常
    ...    获取SID失败，可能有多个SID 电池电流异常
    ...
    ...
    ...    ！！！！数据字典中，该项在web和GUI均不显示，不做测试
    [Tags]    3
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置直流段告警级别~CID1=42H>>    <<设置直流段告警级别~CID2=82H>>    电池电流异常    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流告警级别~CID1=42H>>    <<获取直流告警级别~CID2=81H>>    电池电流异常    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    <<电池电流异常~0xb001030090001>>    ${VAR}
        Comment    ${1104告警级别}    获取1104告警级别取值约定    电池电流异常    ${VAR}
        ${交流电压低告警级别}    获取web参数量    电池电流异常
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    #<<电池电流异常~0xb001030090001>>    0xb001030090001
    [Teardown]    设置web参数量    电池电流异常    主要

1363_0030_13电池温度高告警X
    [Documentation]    注意从web上获取告警级别时，
    ...    是 交流防雷器异常 不是 交流防雷器异常_1
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置直流段告警级别~CID1=42H>>    <<设置直流段告警级别~CID2=82H>>    电池温度高告警    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流告警级别~CID1=42H>>    <<获取直流告警级别~CID2=81H>>    电池温度高告警    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    电池温度高    ${VAR}
        ${交流电压低告警级别}    获取web参数量    电池温度高
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    电池温度高    主要

1363_0032_14电池温度低告警X
    [Documentation]    注意从web上获取告警级别时，
    ...    是 交流防雷器异常 不是 交流防雷器异常_1
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置直流段告警级别~CID1=42H>>    <<设置直流段告警级别~CID2=82H>>    电池温度低告警    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流告警级别~CID1=42H>>    <<获取直流告警级别~CID2=81H>>    电池温度低告警    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    电池温度低    ${VAR}
        ${交流电压低告警级别}    获取web参数量    电池温度低
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    电池温度低    主要

1363_0034_15电池温度无效告警X
    [Documentation]    注意从web上获取告警级别时，
    ...    是 交流防雷器异常 不是 交流防雷器异常_1
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置直流段告警级别~CID1=42H>>    <<设置直流段告警级别~CID2=82H>>    电池温度无效告警    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流告警级别~CID1=42H>>    <<获取直流告警级别~CID2=81H>>    电池温度无效告警    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    电池温度无效    ${VAR}
        ${交流电压低告警级别}    获取web参数量    电池温度无效
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    电池温度无效    主要

1363_0036_16电池放电告警
    [Documentation]    注意从web上获取告警级别时，
    ...    是 交流防雷器异常 不是 交流防雷器异常_1
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置直流段告警级别~CID1=42H>>    <<设置直流段告警级别~CID2=82H>>    电池放电告警    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流告警级别~CID1=42H>>    <<获取直流告警级别~CID2=81H>>    电池放电告警    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    电池放电    ${VAR}
        ${交流电压低告警级别}    获取web参数量    电池放电
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    电池放电    主要

1363_0038_17电池测试失败
    [Documentation]    注意从web上获取告警级别时，
    ...    是 交流防雷器异常 不是 交流防雷器异常_1
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置直流段告警级别~CID1=42H>>    <<设置直流段告警级别~CID2=82H>>    电池测试失败    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流告警级别~CID1=42H>>    <<获取直流告警级别~CID2=81H>>    电池测试失败    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    电池测试失败    ${VAR}
        ${交流电压低告警级别}    获取web参数量    电池测试失败
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    电池测试失败    主要

1363_0040_18输入干接点告警1
    [Documentation]    注意从web上获取告警级别时，
    ...    是 交流防雷器异常 不是 交流防雷器异常_1
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置直流段告警级别~CID1=42H>>    <<设置直流段告警级别~CID2=82H>>    输入干接点告警1    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流告警级别~CID1=42H>>    <<获取直流告警级别~CID2=81H>>    输入干接点告警_1    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    输入干接点告警_1    ${VAR}
        ${交流电压低告警级别}    获取web参数量    输入干接点告警_1
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    输入干接点告警_1    次要

1363_0042_19输入干接点告警2
    [Documentation]    注意从web上获取告警级别时，
    ...    是 交流防雷器异常 不是 交流防雷器异常_1
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置直流段告警级别~CID1=42H>>    <<设置直流段告警级别~CID2=82H>>    输入干接点告警2    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流告警级别~CID1=42H>>    <<获取直流告警级别~CID2=81H>>    输入干接点告警_2    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    输入干接点告警_2    ${VAR}
        ${交流电压低告警级别}    获取web参数量    输入干接点告警_2
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    输入干接点告警_2    次要

1363_0044_20输入干接点告警3
    [Documentation]    注意从web上获取告警级别时，
    ...    是 交流防雷器异常 不是 交流防雷器异常_1
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置直流段告警级别~CID1=42H>>    <<设置直流段告警级别~CID2=82H>>    输入干接点告警3    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流告警级别~CID1=42H>>    <<获取直流告警级别~CID2=81H>>    输入干接点告警_3    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    输入干接点告警_3    ${VAR}
        ${交流电压低告警级别}    获取web参数量    输入干接点告警_3
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    输入干接点告警_3    次要

1363_0046_21输入干接点告警4
    [Documentation]    注意从web上获取告警级别时，
    ...    是 交流防雷器异常 不是 交流防雷器异常_1
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置直流段告警级别~CID1=42H>>    <<设置直流段告警级别~CID2=82H>>    输入干接点告警4    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流告警级别~CID1=42H>>    <<获取直流告警级别~CID2=81H>>    输入干接点告警_4    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    输入干接点告警_4    ${VAR}
        ${交流电压低告警级别}    获取web参数量    输入干接点告警_4
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    输入干接点告警_4    次要

1363_0048_22输入干接点告警5
    [Documentation]    注意从web上获取告警级别时，
    ...    是 交流防雷器异常 不是 交流防雷器异常_1
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置直流段告警级别~CID1=42H>>    <<设置直流段告警级别~CID2=82H>>    输入干接点告警5    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流告警级别~CID1=42H>>    <<获取直流告警级别~CID2=81H>>    输入干接点告警_5    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    输入干接点告警_5    ${VAR}
        ${交流电压低告警级别}    获取web参数量    输入干接点告警_5
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    输入干接点告警_5    次要

1363_0050_23输入干接点告警6
    [Documentation]    注意从web上获取告警级别时，
    ...    是 交流防雷器异常 不是 交流防雷器异常_1
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置直流段告警级别~CID1=42H>>    <<设置直流段告警级别~CID2=82H>>    输入干接点告警6    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流告警级别~CID1=42H>>    <<获取直流告警级别~CID2=81H>>    输入干接点告警_6    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    输入干接点告警_6    ${VAR}
        ${交流电压低告警级别}    获取web参数量    输入干接点告警_6
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    输入干接点告警_6    次要

1363_0052_24输入干接点告警7
    [Documentation]    注意从web上获取告警级别时，
    ...    是 交流防雷器异常 不是 交流防雷器异常_1
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置直流段告警级别~CID1=42H>>    <<设置直流段告警级别~CID2=82H>>    输入干接点告警7    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流告警级别~CID1=42H>>    <<获取直流告警级别~CID2=81H>>    输入干接点告警_7    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    输入干接点告警_7    ${VAR}
        ${交流电压低告警级别}    获取web参数量    输入干接点告警_7
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    输入干接点告警_7    次要

1363_0054_25输入干接点告警8
    [Documentation]    注意从web上获取告警级别时，
    ...    是 交流防雷器异常 不是 交流防雷器异常_1
    [Setup]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置直流段告警级别~CID1=42H>>    <<设置直流段告警级别~CID2=82H>>    输入干接点告警8    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流告警级别~CID1=42H>>    <<获取直流告警级别~CID2=81H>>    输入干接点告警_8    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    输入干接点告警_8    ${VAR}
        ${交流电压低告警级别}    获取web参数量    输入干接点告警_8
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    输入干接点告警_8    次要

1363_0056_26一次下电告警
    [Documentation]    不能屏蔽，默认严重
    [Setup]
    FOR    ${VAR}    IN RANGE    1    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置直流段告警级别~CID1=42H>>    <<设置直流段告警级别~CID2=82H>>    一次下电告警    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        Comment    sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流告警级别~CID1=42H>>    <<获取直流告警级别~CID2=81H>>    一次下电告警    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    一次下电告警    ${VAR}
        ${交流电压低告警级别}    获取web参数量    一次下电告警
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    一次下电告警    严重

1363_0058_27二次下电告警
    [Documentation]    不能屏蔽，默认严重
    [Setup]
    FOR    ${VAR}    IN RANGE    1    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置直流段告警级别~CID1=42H>>    <<设置直流段告警级别~CID2=82H>>    二次下电告警    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        Comment    sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流告警级别~CID1=42H>>    <<获取直流告警级别~CID2=81H>>    二次下电告警    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    二次下电告警    ${VAR}
        ${交流电压低告警级别}    获取web参数量    二次下电告警
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    二次下电告警    严重

1363_0060_28电池下电告警
    [Documentation]    注意从web上获取告警级别时，
    ...    是 交流防雷器异常 不是 交流防雷器异常_1
    [Setup]
    FOR    ${VAR}    IN RANGE    1    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置直流段告警级别~CID1=42H>>    <<设置直流段告警级别~CID2=82H>>    电池下电告警    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        Comment    sleep    3
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流告警级别~CID1=42H>>    <<获取直流告警级别~CID2=81H>>    电池下电告警    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    电池下电告警    ${VAR}
        ${交流电压低告警级别}    获取web参数量    电池下电告警
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    设置web参数量    电池下电告警    严重

1363_0062_29低温下电告警X
    [Documentation]    不能屏蔽，默认严重
    [Setup]    Wait Until Keyword Succeeds    10    2    设置web参数量    电池低温下电使能    允许    # run keywords | 测试用例前置条件 | AND | Wait Until Keyword Succeeds | 10 | 2 | 设置web参数量 | 电池低温下电使能 | 允许
    FOR    ${VAR}    IN RANGE    1    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置直流段告警级别~CID1=42H>>    <<设置直流段告警级别~CID2=82H>>    低温下电告警    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    5
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流告警级别~CID1=42H>>    <<获取直流告警级别~CID2=81H>>    低温下电告警    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    电池低温下电    ${VAR}
        ${交流电压低告警级别}    获取web参数量    电池低温下电
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    run keywords    Wait Until Keyword Succeeds    10    2    设置web参数量    电池低温下电    严重
    ...    AND    设置web参数量    电池低温下电使能    禁止    # run keywords | Wait Until Keyword Succeeds | 10 | 2 | 设置web参数量 | 电池低温下电使能 | 禁止 | AND | 设置web参数量 | 电池低温下电 | 严重

1363_0064_30高温下电告警
    [Documentation]    不能屏蔽，默认严重
    [Setup]    Wait Until Keyword Succeeds    10    2    设置web参数量    电池高温下电使能    允许
    FOR    ${VAR}    IN RANGE    1    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置直流段告警级别~CID1=42H>>    <<设置直流段告警级别~CID2=82H>>    高温下电告警    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        sleep    5
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流告警级别~CID1=42H>>    <<获取直流告警级别~CID2=81H>>    高温下电告警    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    电池高温下电    ${VAR}
        ${交流电压低告警级别}    获取web参数量    电池高温下电
        should be equal    ${1104告警级别}    ${交流电压低告警级别}
        Comment    Wait Until Keyword Succeeds    5m    2    设置web参数量    交流电压高    主要
    END
    [Teardown]    run keywords    Wait Until Keyword Succeeds    10    2    设置web参数量    电池高温下电    严重
    ...    AND    设置web参数量    电池高温下电使能    禁止    # run keywords | Wait Until Keyword Succeeds | 10 | 2 | 设置web参数量 | 电池高温下电使能 | 禁止 | AND | 设置web参数量 | 电池高温下电 | 严重
