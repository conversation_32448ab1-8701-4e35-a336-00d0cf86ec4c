*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
批量获取智能空开设备信息测试
    写入CSV文档    智能空开设备信息获取测试    信号名称    信号值    结果    备注
    连接CSU
    ${排除列表}    create list
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string32    INDEX_DEVICE_CH=智能空开
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SSW排除系统名称}    ${排除列表}    2
    @{信号名称列表1}   create_list
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${dict1}    Create Dictionary
        Set To Dictionary    ${dict1}     name     ${信号名称}
        Append To List       ${信号名称列表1}    ${dict1}
    END
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    DMU_IntelAirSwit    ${信号名称列表1}    建链    zte    智能空开设备信息获取测试
	
	
   ${列表2}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SSW排除软件版本}    ${排除列表}    2
    @{信号名称列表2}   create_list
    FOR    ${i}    IN    @{列表2}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${dict2}    Create Dictionary
        Set To Dictionary    ${dict2}     name     ${信号名称}
        Append To List       ${信号名称列表2}    ${dict2}
    END
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    DMU_IntelAirSwit    ${信号名称列表2}    建链    V12345    智能空开设备信息获取测试
	
	
	@{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=date    INDEX_DEVICE_CH=智能空开
    ${列表4}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    2
    @{信号名称列表4}   create_list
    FOR    ${i}    IN    @{列表4}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${dict4}    Create Dictionary
        Set To Dictionary    ${dict4}     name     ${信号名称}
        Append To List       ${信号名称列表4}    ${dict4}
    END
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    DMU_IntelAirSwit    ${信号名称列表4}    建链    2018-01-02     智能空开设备信息获取测试
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    DMU_IntelAirSwit    ${信号名称列表4}    建链    2999-12-31     智能空开设备信息获取测试
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    DMU_IntelAirSwit    ${信号名称列表4}    建链    2018-10-31     智能空开设备信息获取测试
	
	
	@{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=float    INDEX_DEVICE_CH=智能空开
    ${列表5}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    2
    @{信号名称列表5}   create_list
    FOR    ${i}    IN    @{列表5}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${dict5}    Create Dictionary
        Set To Dictionary    ${dict5}     name     ${信号名称}
        Append To List       ${信号名称列表5}    ${dict5}
    END
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    DMU_IntelAirSwit    ${信号名称列表5}    建链    0     智能空开设备信息获取测试
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    DMU_IntelAirSwit    ${信号名称列表5}    建链    0     智能空开设备信息获取测试
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    DMU_IntelAirSwit    ${信号名称列表5}    建链    125     智能空开设备信息获取测试

获取智能空开类型测试
    连接CSU
    FOR    ${i}    IN RANGE    40
        ${temp}    evaluate    1+${i}
        ${temp}    Convert to string    ${temp}
        ${智能空开类型}    获取web实时数据    智能空开类型-${temp}
        run keyword if    '${智能空开类型}'=='双向空开'    Wait Until Keyword Succeeds    10m    5    信号量数据值为(强制获取)    智能空开类型-${temp}    0
        run keyword if    '${智能空开类型}'=='单向空开'    Wait Until Keyword Succeeds    10m    5    信号量数据值为(强制获取)    智能空开类型-${temp}    1
    END
