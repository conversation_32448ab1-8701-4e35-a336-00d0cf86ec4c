*** Settings ***
Suite Teardown
Test Setup        Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
uniform_0002_锂电池告警量√
    [Setup]    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    @{锂电序号随机list}    从1-15个数中随机选n个不重复的单体    ${北向协议铁锂最大数}    2
    FOR    ${锂电池序号}    IN    @{锂电序号随机list}
        ${锂电池序号}    Convert To String    ${锂电池序号}
        设置子工具值    smartli    ${锂电池序号}    all    all    max
        sleep    10min
        ${协议数据}    统一监控协议_获取数据    锂电池告警量
        ${协议数据1}    统一监控协议_选择锂电类型对应SID    ${协议数据}    FBBMS
        ${web告警信息}    统一监控协议_获取web对应告警
        ${对比结果}    批量对比告警_统一监控协议_WEB    ${协议数据1}    ${web告警信息}    锂电池告警量
        should be true    ${对比结果}
        设置子工具值    smartli    ${锂电池序号}    all    all    def
        sleep    10min
        ${协议数据}    统一监控协议_获取数据    锂电池告警量
        ${获取结果}    Set Variable    ${协议数据}[0][result]
        ${获取值}    Set Variable    ${协议数据}[0][value]
        should be equal as strings    ${获取结果}    ok
        should be equal as strings    ${获取值}    0
    END
    [Teardown]    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接
