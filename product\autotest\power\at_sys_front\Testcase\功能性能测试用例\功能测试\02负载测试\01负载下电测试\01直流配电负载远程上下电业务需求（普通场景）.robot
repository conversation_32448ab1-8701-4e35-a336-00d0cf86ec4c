*** Settings ***
Resource          ../../../../../测试用例关键字.robot
Suite Setup       测试用例前置条件
Suite Teardown    Run Keywords     测试用例后置条件

*** Test Cases ***
远程下电使能设置测试
    ${取值范围}    获取web参数的取值约定    远程一次下电使能
    should be equal    ${取值范围}[0]    禁止
    should be equal    ${取值范围}[1]    允许
    ${下电使能}    获取web参数量    远程一次下电使能
    设置web参数量    远程一次下电使能    禁止
    ${远程下电}    获取web参数量    远程一次下电
    ${转换值}    Convert To Boolean    ${远程下电}
    should not be true    ${转换值}
    设置web参数量    远程一次下电使能    允许
    ${取值范围}    获取web参数的取值约定    远程一次下电
    should be equal    ${取值范围}[0]    上电
    should be equal    ${取值范围}[1]    下电
    设置web参数量    远程一次下电使能    ${下电使能}
    ${下电使能}    获取web参数量    远程二次下电使能
    设置web参数量    远程二次下电使能    禁止
    ${远程下电}    获取web参数量    远程二次下电
    ${转换值}    Convert To Boolean    ${远程下电}
    should not be true    ${转换值}
    设置web参数量    远程二次下电使能    允许
    ${取值范围}    获取web参数的取值约定    远程二次下电
    should be equal    ${取值范围}[0]    上电
    should be equal    ${取值范围}[1]    下电
    设置web参数量    远程二次下电使能    ${下电使能}

远程下电功能测试(CSU控制远程一次下电)
    [Setup]    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    Wait Until Keyword Succeeds    5m    10    查询指定告警信息不为    一次下电告警
    关闭交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    10    判断告警不存在    一次下电告警
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
        设置web参数量    电池组容量_${i}    0
    END
    Wait Until Keyword Succeeds    5m    10    设置web参数量    电池组容量_1    99
    sleep    2
    Wait Until Keyword Succeeds    5m    10    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    30    1    设置web参数量    下电模式    电池电压    #默认1电池电压
    Wait Until Keyword Succeeds    30    1    设置web参数量    下电控制延时    0    #默认0
    Wait Until Keyword Succeeds    30    1    设置web参数量    负载一次下电使能    允许    #默认1允许
    Wait Until Keyword Succeeds    30    1    设置web参数量    负载二次下电使能    禁止    #默认1允许
    ${可设置范围}    获取web参数可设置范围    一次下电恢复回差
    Wait Until Keyword Succeeds    10    1    设置web参数量    一次下电恢复回差    ${可设置范围}[0]
    显示属性配置    一次下电控制状态    数字量    web_attr=On    gui_attr=On
    ${恢复时间可设置范围}    获取web参数可设置范围    一次下电恢复时间
    run keyword and ignore error    设置web参数量    一次下电恢复时间    ${恢复时间可设置范围}[0]
    Comment    ${可设置范围}    获取web参数可设置范围    测试终止电压
    Comment    设置web参数量    测试终止电压    ${可设置范围}[1]
    Comment    ${电压低可设置范围}    获取web参数可设置范围    电池电压低阈值
    Comment    设置web参数量    电池电压低阈值    ${电压低可设置范围}[1]
    ${电池下电电压可设置范围}    获取web参数可设置范围    负载一次下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载一次下电电压    ${电池下电电压可设置范围}[1]
    设置负载电压电流    53.5    10
    打开负载输出
    ${一次下电电压设置值}    获取web参数量    负载一次下电电压
    ${一次下电电压}    evaluate    ${一次下电电压设置值}-0.5
    向下调节电池电压    ${一次下电电压}
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    一次下电控制状态    动作
    Wait Until Keyword Succeeds    5m    10    判断告警存在    一次下电告警
    设置web参数量    远程一次下电使能    允许
    设置web参数量    远程二次下电使能    禁止
    设置web参数量    远程一次下电    下电
    sleep    30
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    一次下电控制状态    动作
    Wait Until Keyword Succeeds    5m    10    判断告警存在    一次下电告警
    设置web参数量    远程一次下电    上电
    sleep    30
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    一次下电控制状态    动作
    Wait Until Keyword Succeeds    5m    10    判断告警存在    一次下电告警
    向上调节电池电压    53.5
    关闭负载输出
    打开交流源输出
    Wait Until Keyword Succeeds    8m    20    信号量数据值为    一次下电控制状态    恢复
    Wait Until Keyword Succeeds    8m    20    查询指定告警信息不为    一次下电告警
    显示属性配置    一次下电控制状态    数字量    web_attr=Off    gui_attr=Off
    ${开始时间}    获取系统时间
    设置web参数量    远程一次下电    下电
    sleep    30
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    一次下电控制状态    动作
    Wait Until Keyword Succeeds    5m    10    判断告警存在    一次下电告警
    设置web参数量    远程一次下电    上电
    sleep    30
    Wait Until Keyword Succeeds    8m    20    信号量数据值为    一次下电控制状态    恢复
    Wait Until Keyword Succeeds    8m    20    查询指定告警信息不为    一次下电告警
    ${结束时间}    获取系统时间
    ${告警内容}    获取web历史告警内容    ${开始时间}    ${结束时间}    1    10
    ${value}    Evaluate    False
    FOR    ${val}    IN    @{告警内容}
        ${array}    Split String    ${val}    ,
        ${value}=    set variable if    '电池组:一次下电告警' in '${array}[5]'    True    False
        Run keyword if    ${value}    Exit For Loop
    END
    should be true    ${value}
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    负载一次下电电压    测试终止电压    电池电压低阈值    负载一次下电使能    负载二次下电使能    一次下电恢复时间    远程一次下电使能    远程二次下电使能
    ...    AND    设置web参数量    一次下电告警干接点    0
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出
    ...    AND    重置电池模拟器输出

远程下电功能测试(CSU控制远程二次下电)
    [Setup]    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    Wait Until Keyword Succeeds    5m    10    查询指定告警信息不为    二次下电告警
    关闭交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    10    判断告警不存在    二次下电告警
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
        设置web参数量    电池组容量_${i}    0
    END
    Wait Until Keyword Succeeds    5m    10    设置web参数量    电池组容量_1    99
    sleep    2
    Wait Until Keyword Succeeds    5m    10    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    30    1    设置web参数量    下电模式    电池电压    #默认1电池电压
    Wait Until Keyword Succeeds    30    1    设置web参数量    下电控制延时    0    #默认0
    Wait Until Keyword Succeeds    30    1    设置web参数量    负载二次下电使能    允许    #默认1允许
    Wait Until Keyword Succeeds    30    1    设置web参数量    负载一次下电使能    禁止    #默认1允许
    ${可设置范围}    获取web参数可设置范围    二次下电恢复回差
    Wait Until Keyword Succeeds    10    1    设置web参数量    二次下电恢复回差    ${可设置范围}[0]
    显示属性配置    二次下电控制状态    数字量    web_attr=On    gui_attr=On
    ${恢复时间可设置范围}    获取web参数可设置范围    二次下电恢复时间
    run keyword and ignore error    设置web参数量    二次下电恢复时间    ${恢复时间可设置范围}[0]
    Comment    ${可设置范围}    获取web参数可设置范围    测试终止电压
    Comment    设置web参数量    测试终止电压    ${可设置范围}[1]
    Comment    ${电压低可设置范围}    获取web参数可设置范围    电池电压低阈值
    Comment    设置web参数量    电池电压低阈值    ${电压低可设置范围}[1]
    ${电池下电电压可设置范围}    获取web参数可设置范围    负载二次下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电电压    ${电池下电电压可设置范围}[1]
    设置负载电压电流    53.5    10
    打开负载输出
    ${二次下电电压设置值}    获取web参数量    负载二次下电电压
    ${二次下电电压}    evaluate    ${二次下电电压设置值}-0.5
    向下调节电池电压    ${二次下电电压}
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    二次下电控制状态    动作
    Wait Until Keyword Succeeds    5m    10    判断告警存在    二次下电告警
    设置web参数量    远程二次下电使能    允许
    设置web参数量    远程一次下电使能    禁止
    设置web参数量    远程二次下电    下电
    sleep    30
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    二次下电控制状态    动作
    Wait Until Keyword Succeeds    5m    10    判断告警存在    二次下电告警
    设置web参数量    远程二次下电    上电
    sleep    30
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    二次下电控制状态    动作
    Wait Until Keyword Succeeds    5m    10    判断告警存在    二次下电告警
    向上调节电池电压    53.5
    关闭负载输出
    打开交流源输出
    Wait Until Keyword Succeeds    8m    20    信号量数据值为    二次下电控制状态    恢复
    Wait Until Keyword Succeeds    8m    20    查询指定告警信息不为    二次下电告警
    ${开始时间}    获取系统时间
    设置web参数量    远程二次下电    下电
    sleep    30
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    二次下电控制状态    动作
    Wait Until Keyword Succeeds    5m    1    判断告警存在    二次下电告警
    设置web参数量    远程二次下电    上电
    sleep    30
    Wait Until Keyword Succeeds    8m    20    信号量数据值为    二次下电控制状态    恢复
    Wait Until Keyword Succeeds    8m    20    查询指定告警信息不为    二次下电告警
    ${结束时间}    获取系统时间
    ${告警内容}    获取web历史告警内容    ${开始时间}    ${结束时间}    1    10
    ${value}    Evaluate    False
    FOR    ${val}    IN    @{告警内容}
        ${array}    Split String    ${val}    ,
        ${value}=    set variable if    '电池组:二次下电告警' in '${array}[5]'    True    False
        Run keyword if    ${value}    Exit For Loop
    END
    should be true    ${value}
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    负载二次下电电压    测试终止电压    电池电压低阈值    负载一次下电使能    负载二次下电使能    二次下电恢复时间    远程一次下电使能    远程二次下电使能
    ...    AND    设置web参数量    二次下电告警干接点    0
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出
    ...    AND    重置电池模拟器输出

远程下电功能测试-系统重启(一次或者二次下电）
    [Setup]    设置web设备参数量为默认值    负载一次下电使能    负载二次下电使能
    设置web参数量    远程一次下电使能    允许
    设置web参数量    远程二次下电使能    禁止
    设置web参数量    远程一次下电    下电
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    一次下电控制状态    动作
    Wait Until Keyword Succeeds    5m    10    判断告警存在    一次下电告警
    系统复位    False
    连接CSU
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    一次下电控制状态    动作
    Wait Until Keyword Succeeds    5m    10    判断告警存在    一次下电告警
    Wait Until Keyword Succeeds    5m    10    判断告警不存在    二次下电告警
    设置web参数量    远程一次下电    上电
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    一次下电控制状态    恢复
    Wait Until Keyword Succeeds    5m    10    判断告警不存在    一次下电告警
    Wait Until Keyword Succeeds    5m    10    判断告警不存在    二次下电告警
    系统复位    False
    连接CSU
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    一次下电控制状态    恢复
    Wait Until Keyword Succeeds    5m    10    判断告警不存在    一次下电告警
    Wait Until Keyword Succeeds    5m    10    判断告警不存在    二次下电告警
    设置web参数量    远程一次下电使能    禁止
    设置web参数量    远程二次下电使能    允许
    设置web参数量    远程二次下电    下电
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    二次下电控制状态    动作
    Wait Until Keyword Succeeds    5m    10    判断告警存在    二次下电告警
    Wait Until Keyword Succeeds    5m    10    判断告警不存在    一次下电告警
    系统复位    False
    连接CSU
    Wait Until Keyword Succeeds    5m    10    判断告警不存在    一次下电告警
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    二次下电控制状态    动作
    Wait Until Keyword Succeeds    5m    10    判断告警存在    二次下电告警
    设置web参数量    远程二次下电    上电
    Wait Until Keyword Succeeds    5m    10    判断告警不存在    一次下电告警
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    二次下电控制状态    恢复
    Wait Until Keyword Succeeds    5m    10    判断告警不存在    二次下电告警
    系统复位    False
    连接CSU
    Wait Until Keyword Succeeds    5m    10    判断告警不存在    一次下电告警
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    二次下电控制状态    恢复
    Wait Until Keyword Succeeds    5m    10    判断告警不存在    二次下电告警
    [Teardown]    Run Keywords    设置web设备参数量为默认值    远程一次下电使能    远程二次下电使能    负载一次下电使能    负载二次下电使能
    ...           AND    重启CSU容器

远程下电功能测试-系统重启(一次和二次下电同时设置）
    [Setup]    设置web设备参数量为默认值    远程一次下电使能    远程二次下电使能
    设置web参数量    远程一次下电使能    允许
    设置web参数量    远程二次下电使能    允许
    设置web参数量    远程一次下电    下电
    设置web参数量    远程二次下电    上电
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    一次下电控制状态    动作
    Wait Until Keyword Succeeds    5m    10    判断告警存在    一次下电告警
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    二次下电控制状态    恢复
    Wait Until Keyword Succeeds    5m    10    判断告警不存在    二次下电告警
    系统复位    False
    连接CSU
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    一次下电控制状态    动作
    Wait Until Keyword Succeeds    5m    10    判断告警存在    一次下电告警
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    二次下电控制状态    恢复
    Wait Until Keyword Succeeds    5m    10    判断告警不存在    二次下电告警
    设置web参数量    远程一次下电    上电
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    一次下电控制状态    恢复
    Wait Until Keyword Succeeds    5m    10    判断告警不存在    一次下电告警
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    二次下电控制状态    恢复
    Wait Until Keyword Succeeds    5m    10    判断告警不存在    二次下电告警
    设置web参数量    远程二次下电    下电
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    二次下电控制状态    动作
    Wait Until Keyword Succeeds    5m    10    判断告警存在    二次下电告警
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    一次下电控制状态    恢复
    Wait Until Keyword Succeeds    5m    10    判断告警不存在    一次下电告警
    系统复位    False
    连接CSU
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    一次下电控制状态    恢复
    Wait Until Keyword Succeeds    5m    10    判断告警不存在    一次下电告警
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    二次下电控制状态    动作
    Wait Until Keyword Succeeds    5m    10    判断告警存在    二次下电告警
    设置web参数量    远程二次下电    上电
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    一次下电控制状态    恢复
    Wait Until Keyword Succeeds    5m    10    判断告警不存在    一次下电告警
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    二次下电控制状态    恢复
    Wait Until Keyword Succeeds    5m    10    判断告警不存在    二次下电告警
    设置web参数量    远程一次下电    下电
    设置web参数量    远程二次下电    下电
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    二次下电控制状态    动作
    Wait Until Keyword Succeeds    5m    10    判断告警存在    二次下电告警
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    二次下电控制状态    动作
    Wait Until Keyword Succeeds    5m    10    判断告警存在    二次下电告警
    系统复位    False
    连接CSU
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    二次下电控制状态    动作
    Wait Until Keyword Succeeds    5m    10    判断告警存在    二次下电告警
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    二次下电控制状态    动作
    Wait Until Keyword Succeeds    5m    10    判断告警存在    二次下电告警
    设置web参数量    远程一次下电    上电
    设置web参数量    远程二次下电    上电
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    二次下电控制状态    恢复
    Wait Until Keyword Succeeds    5m    10    查询指定告警信息不为    二次下电告警
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    一次下电控制状态    恢复
    Wait Until Keyword Succeeds    5m    10    判断告警不存在    一次下电告警
    系统复位    False
    连接CSU
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    二次下电控制状态    恢复
    Wait Until Keyword Succeeds    5m    10    查询指定告警信息不为    二次下电告警
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    一次下电控制状态    恢复
    Wait Until Keyword Succeeds    5m    10    判断告警不存在    一次下电告警
    [Teardown]    Run Keywords    设置web设备参数量为默认值    远程一次下电使能    远程二次下电使能
    ...           AND    重启CSU容器

负载下电禁用，远程下电测试功能
    Wait Until Keyword Succeeds    5m    10    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    30    1    设置web参数量    下电模式    电池电压    #默认1电池电压
    Wait Until Keyword Succeeds    30    1    设置web参数量    下电控制延时    0    #默认0
    Wait Until Keyword Succeeds    30    1    设置web参数量    负载一次下电使能    禁止    #默认1允许
    Wait Until Keyword Succeeds    30    1    设置web参数量    负载二次下电使能    禁止    #默认1允许
    设置web参数量    远程一次下电使能    允许
    设置web参数量    远程二次下电使能    禁止
    设置web参数量    远程一次下电    下电
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    一次下电控制状态    动作
    Wait Until Keyword Succeeds    5m    10    判断告警存在    一次下电告警
    设置web参数量    远程一次下电    上电
    Wait Until Keyword Succeeds    8m    20    信号量数据值为    一次下电控制状态    恢复
    Wait Until Keyword Succeeds    8m    20    查询指定告警信息不为    一次下电告警
    设置web参数量    远程一次下电使能    禁止
    设置web参数量    远程二次下电使能    允许
    设置web参数量    远程二次下电    下电
    Wait Until Keyword Succeeds    5m    10    信号量数据值为    二次下电控制状态    动作
    Wait Until Keyword Succeeds    5m    10    判断告警存在    二次下电告警
    设置web参数量    远程二次下电    上电
    Wait Until Keyword Succeeds    8m    20    信号量数据值为    二次下电控制状态    恢复
    Wait Until Keyword Succeeds    8m    20    查询指定告警信息不为    二次下电告警
    [Teardown]    设置web设备参数量为默认值    远程一次下电使能    远程二次下电使能    负载一次下电使能    负载二次下电使能
