*** Settings ***
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
批量获取设备信息
    写入CSV文档    FB100C2设备信息获取测试    信号名称    信号值    结果    备注
    连接CSU
    Comment    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string32    INDEX_DEVICE_CH=NFBBMS
    Comment    ${排除列表}    create list
    Comment    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100C2排除设备版本信息}    ${排除列表}    1
    Comment    : FOR    ${i}    IN    @{列表1}
    Comment    ${信号名称}    Get From Dictionary    ${i}    signal_name
    Comment    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    fb100c2    ${信号名称}    获取设备厂家信息    99.23    FB100C2设备信息获取测试
    Comment    Run Keyword And Continue On Failure   南向子设备字符型厂家信息列表封装判断结果    fb100c2    ${信号名称}    获取设备厂家信息    10.10    FB100C2设备信息获取测试
    Comment    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    fb100c2    ${信号名称}    获取设备厂家信息    1.81    FB100C2设备信息获取测试
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string32    INDEX_DEVICE_CH=NFBBMS
    ${排除列表}    create list
	@{信号名称列表1}    create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100C2排除设备名称信息}    ${排除列表}    1
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${dict1}    Create Dictionary
        Set To Dictionary    ${dict1}     name     ${信号名称}
		Append To List       ${信号名称列表1}    ${dict1}
    END
	Run Keyword And Continue On Failure   南向子设备字符型厂家信息列表封装判断结果    fb100c2    ${信号名称列表1}    获取设备厂家信息    ZTE-FB1003    FB100C2设备信息获取测试
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    fb100c2    ${信号名称列表1}    获取设备厂家信息    ZTEsmartli    FB100C2设备信息获取测试
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    fb100c2    ${信号名称列表1}    获取设备厂家信息    ZTEFB100C2    FB100C2设备信息获取测试
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string32    INDEX_DEVICE_CH=NFBBMS
    ${排除列表}    create list
	@{信号名称列表2}    create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100C2排除设备序列号信息}    ${排除列表}    1
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${dict2}    Create Dictionary
        Set To Dictionary    ${dict2}     name     ${信号名称}
		Append To List       ${信号名称列表2}    ${dict2}
    END
	Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    fb100c2    ${信号名称列表2}    获取设备厂家信息    210097205489    FB100C2设备信息获取测试
    Run Keyword And Continue On Failure        南向子设备字符型厂家信息列表封装判断结果    fb100c2    ${信号名称列表2}    获取设备厂家信息    210097205673    FB100C2设备信息获取测试
    Run Keyword And Continue On Failure        南向子设备字符型厂家信息列表封装判断结果    fb100c2    ${信号名称列表2}    获取设备厂家信息    210097205688    FB100C2设备信息获取测试
  



BMS软件版本获取测试
    连接CSU
    设置子工具值    fb100c2    all    获取设备厂家信息    BMS软件版本    9.99
    Wait Until Keyword Succeeds    18m   5    设备信息获取值   <<BMS软件版本-1~0x21001080020001>>    V9.99
    Wait Until Keyword Succeeds    18m   5    设备信息获取值    <<BMS软件版本-8~0x21001080020001>>   V9.99
    Wait Until Keyword Succeeds    18m   5    设备信息获取值  <<BMS软件版本-16~0x21001080020001>>    V9.99
    设置子工具值    fb100c2    all    获取设备厂家信息    BMS软件版本    1.21
    Wait Until Keyword Succeeds    18m   5    设备信息获取值   <<BMS软件版本-1~0x21001080020001>>     V1.21
    Wait Until Keyword Succeeds    18m   5    设备信息获取值   <<BMS软件版本-8~0x21001080020001>>     V1.21
    Wait Until Keyword Succeeds    18m   5    设备信息获取值   <<BMS软件版本-16~0x21001080020001>>    V1.21
    设置子工具值    fb100c2    all    获取设备厂家信息    BMS软件版本    1.11
    Wait Until Keyword Succeeds    18m   5    设备信息获取值   <<BMS软件版本-1~0x21001080020001>>     V1.11  
    Wait Until Keyword Succeeds    18m   5    设备信息获取值  <<BMS软件版本-8~0x21001080020001>>      V1.11
    Wait Until Keyword Succeeds    18m   5    设备信息获取值   <<BMS软件版本-16~0x21001080020001>>    V1.11
    测试台上下电操作    ${测试台编号}    OFF
    sleep    5
    测试台上下电操作    ${测试台编号}    ON
    连接CSU
    设置子工具值    fb100c2    all    获取设备厂家信息    BMS软件版本    9.99
    Wait Until Keyword Succeeds    18m   5    设备信息获取值   <<BMS软件版本-1~0x21001080020001>>    V9.99
    Wait Until Keyword Succeeds    18m   5    设备信息获取值    <<BMS软件版本-8~0x21001080020001>>   V9.99
    Wait Until Keyword Succeeds    18m   5    设备信息获取值  <<BMS软件版本-16~0x21001080020001>>    V9.99
    设置子工具值    fb100c2    all    获取设备厂家信息    BMS软件版本    1.21
    Wait Until Keyword Succeeds    18m   5    设备信息获取值   <<BMS软件版本-1~0x21001080020001>>     V1.21
    Wait Until Keyword Succeeds    18m   5    设备信息获取值   <<BMS软件版本-8~0x21001080020001>>     V1.21
    Wait Until Keyword Succeeds    18m   5    设备信息获取值   <<BMS软件版本-16~0x21001080020001>>    V1.21
    设置子工具值    fb100c2    all    获取设备厂家信息    BMS软件版本    1.11
    Wait Until Keyword Succeeds    18m   5    设备信息获取值   <<BMS软件版本-1~0x21001080020001>>     V1.11  
    Wait Until Keyword Succeeds    18m   5    设备信息获取值  <<BMS软件版本-8~0x21001080020001>>      V1.11
    Wait Until Keyword Succeeds    18m   5    设备信息获取值   <<BMS软件版本-16~0x21001080020001>>    V1.11
