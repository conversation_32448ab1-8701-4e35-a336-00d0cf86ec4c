*** Settings ***
Suite Setup
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_批量获取太阳能模块数字量
    [Documentation]    21min
    写入CSV文档    PU数字量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=digital data    INDEX_DEVICE_CH=太阳能模块
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除数字量信号}    ${排除列表}    1    ${模拟PU起始地址}
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    太阳能模块    digital data    ${PU排除数字量信号}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    Comment    ${指定数据1}    Create Dictionary    signal_name    <<PU输入欠压状态-5~0x10001020190001>>    convention    True    device_name    太阳能模块    data_type    int    unit    ''
    Comment    @{power_sm待测}    Create List    ${指定数据1}
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    1      power_sm
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    power_sm    pu       数字量    ${缺省值列表}   PU数字量获取测试    获取太阳能模块数字量    
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    2      power_sm
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    power_sm    pu       数字量    ${缺省值列表}    PU数字量获取测试    获取太阳能模块数字量   
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    0      power_sm
	Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    power_sm    pu       数字量    ${缺省值列表}    PU数字量获取测试    获取太阳能模块数字量   
    

power_sm_0002_批量获取PU特殊数字量
    写入CSV文档    PU特殊数字量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=太阳能模块
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除特殊数字量信号}    ${排除列表}    1     ${模拟PU起始地址}   1
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    太阳能模块    alarm    ${PU排除特殊数字量信号}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    FOR    ${i}    IN    @{power_sm待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        Run Keyword And Continue On Failure    告警量置位状态量封装判断结果    power_sm    pu    ${信号名称}    数字量    1    ${模拟PU起始地址}    PU特殊数字量获取测试    获取太阳能模块数字量    null    null    null    null    null    null
        Run Keyword And Continue On Failure    告警量置位状态量封装判断结果    power_sm    pu    ${信号名称}    数字量    0    ${模拟PU起始地址}    PU特殊数字量获取测试    获取太阳能模块数字量    null    null    null    null    null    null
    END   
    

