*** Settings ***
Suite Setup
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_获取整流器模拟量
    [Documentation]    SF01 R02 V3.05.00.00T4版本 \ 协议数据字典，整流器风扇转速数据类型定义有误
    ...    协议获取值与web获取值存在时间差，很多时候变化较大，已在对比关键字尽量放宽了条件。
    [Tags]    4
    ${能源网管数据}    能源网管协议_获取数据    获取整流器模拟量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取整流器模拟量
    Should Be True    ${数据对比结果}

power_sm_0002_获取整流器数字量
    [Documentation]    获取数据为80 0E 00 10 01 01 04 29 A8 AB AB 10 01 02 04 29 E9 AA AA 10 01 03 04 29 EA AA AA，状态量应该为1个字节，现在是4个字节，以bit方式进行传输，需要特殊考虑
    [Tags]    4
    ${能源网管数据}    能源网管协议_获取数据    获取整流器数字量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取整流器数字量
    Should Be True    ${数据对比结果}

power_sm_0003_获取整流器设备信息
    [Tags]    3
    ${能源网管数据}    能源网管协议_获取数据    获取整流器设备信息    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取整流器设备信息
    Should Be True    ${数据对比结果}

power_sm_0004_设置整流器控制量
    [Documentation]    交流节能模式需为自由
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流节能模式    自由
    FOR    ${真实整流器地址}    IN    1    4
        能源网管协议_设置单个设备单个控制量    整流器_${真实整流器地址}    2    整流器休眠    ${SSH}
        Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-${真实整流器地址}    是
        能源网管协议_设置单个设备单个控制量    整流器_${真实整流器地址}    2    整流器唤醒    ${SSH}
        Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-${真实整流器地址}    否
        能源网管协议_设置单个设备单个控制量    整流器_${真实整流器地址}    2    整流器风扇调速禁止    ${SSH}
        Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器风扇控制状态-${真实整流器地址}    全速
        能源网管协议_设置单个设备单个控制量    整流器_${真实整流器地址}    2    整流器风扇调速允许    ${SSH}
        Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器风扇控制状态-${真实整流器地址}    自动
        能源网管协议_设置单个设备单个控制量    整流器_${真实整流器地址}    2    整流器通讯中断告警清除    ${SSH}
    END
