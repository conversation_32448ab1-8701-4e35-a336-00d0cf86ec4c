*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
免责下电时段设置测试
    ${免责下电使能}    获取web参数量    <<配电单元免责下电使能_1~0xa0010502e0001>>
    设置web参数量    <<配电单元免责下电使能_1~0xa0010502e0001>>    允许
    FOR    ${val}    IN RANGE    1    6
        ${起始时刻}    Catenate    SEPARATOR=    <<配电单元免责下电起始时刻${val}_    1    ~0xa0010502f0001>>
        ${终止时刻}    Catenate    SEPARATOR=    <<配电单元免责下电终止时刻${val}_    1    ~0xa001050300001>>
        ${起始时刻范围}    获取web参数可设置范围    ${起始时刻}
        should be equal as strings    ${起始时刻范围}[0]    00:00
        should be equal as strings    ${起始时刻范围}[1]    23:59
        ${起始时刻范围}    获取web参数可设置范围    ${终止时刻}
        should be equal as strings    ${起始时刻范围}[0]    00:00
        should be equal as strings    ${起始时刻范围}[1]    23:59
        设置web参数的范围并校验设置成功    ${起始时刻}
        设置web参数的范围并校验设置成功    ${终止时刻}
    END
    ${免责下电使能}    获取web参数量    <<配电单元免责下电使能_2~0xa0010502e0001>>
    设置web参数量    <<配电单元免责下电使能_2~0xa0010502e0001>>    允许
    FOR    ${val}    IN RANGE    1    6
        ${起始时刻}    Catenate    SEPARATOR=    <<配电单元免责下电起始时刻${val}_    2    ~0xa001050310001>>
        ${终止时刻}    Catenate    SEPARATOR=    <<配电单元免责下电终止时刻${val}_    2    ~0xa001050320001>>
        ${起始时刻范围}    获取web参数可设置范围    ${起始时刻}
        should be equal as strings    ${起始时刻范围}[0]    00:00
        should be equal as strings    ${起始时刻范围}[1]    23:59
        ${起始时刻范围}    获取web参数可设置范围    ${终止时刻}
        should be equal as strings    ${起始时刻范围}[0]    00:00
        should be equal as strings    ${起始时刻范围}[1]    23:59
        设置web参数的范围并校验设置成功    ${起始时刻}
        设置web参数的范围并校验设置成功    ${终止时刻}
    END
    [Teardown]    设置web设备参数量为默认值    <<配电单元免责下电使能_1~0xa0010502e0001>>    <<配电单元免责下电使能_2~0xa0010502e0001>>

免责下电使能设置测试
    获取参数取值约定并比对取值范围    <<配电单元免责下电使能_1~0xa0010502e0001>>    禁止    允许
    设置web参数量    <<配电单元免责下电使能_1~0xa0010502e0001>>    禁止
    FOR    ${val}    IN RANGE    1    6
        ${起始时刻}    Catenate    SEPARATOR=    <<配电单元免责下电起始时刻${val}_    1    ~0xa0010502f0001>>
        ${终止时刻}    Catenate    SEPARATOR=    <<配电单元免责下电终止时刻${val}_    1    ~0xa001050300001>>
        ${起始时刻值}    获取web参数量    ${起始时刻}
        ${终止时刻值}    获取web参数量    ${终止时刻}
        ${转换值}    Convert To Boolean    ${起始时刻值}
        should not be true    ${转换值}
        ${转换值}    Convert To Boolean    ${终止时刻值}
        should not be true    ${转换值}
    END
    设置web参数量    <<配电单元免责下电使能_1~0xa0010502e0001>>    允许
    FOR    ${val}    IN RANGE    1    6
        ${起始时刻}    Catenate    SEPARATOR=    <<配电单元免责下电起始时刻${val}_    1    ~0xa0010502f0001>>
        ${终止时刻}    Catenate    SEPARATOR=    <<配电单元免责下电终止时刻${val}_    1    ~0xa001050300001>>
        ${起始时刻范围}    获取web参数可设置范围    ${起始时刻}
        should be equal as strings    ${起始时刻范围}[0]    00:00
        should be equal as strings    ${起始时刻范围}[1]    23:59
        ${起始时刻范围}    获取web参数可设置范围    ${终止时刻}
        should be equal as strings    ${起始时刻范围}[0]    00:00
        should be equal as strings    ${起始时刻范围}[1]    23:59
    END
    [Teardown]    设置web设备参数量为默认值    <<配电单元免责下电使能_1~0xa0010502e0001>>

免责下电时段设置容错测试-输入错误
    ${免责下电使能}    获取web参数量    <<配电单元免责下电使能_1~0xa0010502e0001>>
    设置web参数量    <<配电单元免责下电使能_1~0xa0010502e0001>>    允许
    ${起始默认值}    获取web参数量    <<配电单元免责下电起始时刻1_1~0xa0010502f0001>>
    ${终止默认值}    获取web参数量    <<配电单元免责下电终止时刻1_1~0xa001050300001>>
    设置web参数量    <<配电单元免责下电起始时刻1_1~0xa0010502f0001>>    00:00
    设置web参数量    <<配电单元免责下电终止时刻1_1~0xa001050300001>>    00:00
    设置web参数量    <<配电单元免责下电起始时刻1_1~0xa0010502f0001>>    01:00
    设置web参数量    <<配电单元免责下电终止时刻1_1~0xa001050300001>>    00:00
    @{无效值}    Create List    24:00    00:60    -00:00    0:00    00:1    时间:00    01:分钟    AA:01    02:cd    )!:""    01-02    01：01
    FOR    ${val}    IN    @{无效值}
        ${设置结果}    设置web参数量_带返回值    <<配电单元免责下电起始时刻1_1~0xa0010502f0001>>    ${val}
        should not be true    ${设置结果}
        ${设置结果}    设置web参数量_带返回值    <<配电单元免责下电起始时刻1_1~0xa001050300001>>    ${val}
        should not be true    ${设置结果}
    END
    设置web参数量    <<配电单元免责下电起始时刻1_1~0xa0010502f0001>>    ${起始默认值}
    设置web参数量    <<配电单元免责下电起始时刻1_1~0xa001050300001>>    ${终止默认值}
    [Teardown]    设置web设备参数量为默认值    <<配电单元免责下电使能_1~0xa0010502e0001>>