*** Settings ***
Test Teardown    Run Keywords     控制子工具运行停止    DMU_EC30HDNC1C    开启
...              AND    Wait Until keyword Succeeds    5m    5    信号量数据值为    交流空调通讯状态    正常
Resource          ../../../../../测试用例关键字.robot


*** Test Cases ***
批量获取交流空调数字量测试
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=digital data    INDEX_DEVICE_CH=交流空调
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${交流空调排除数字量信号}    ${排除列表}
    批量获取数字量测试  ${列表1}    DMU_EC30HDNC1C  只读  交流空调模拟量获取测试
    [Teardown]

交流空调通讯状态获取测试
    连接CSU
    显示属性配置    交流空调通讯状态    数字量    web_attr=On    gui_attr=On
    设置子工具个数    DMU_EC30HDNC1C    1
    控制子工具运行停止    DMU_EC30HDNC1C    启动
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流空调通讯状态     正常
    控制子工具运行停止    DMU_EC30HDNC1C    关闭
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流空调通讯状态     异常
    显示属性配置    交流空调通讯状态    数字量    web_attr=Off    gui_attr=Off

交流空调在位状态获取测试
    连接CSU
    显示属性配置    交流空调在位状态    数字量    web_attr=On    gui_attr=On
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流空调在位状态     在位
    控制子工具运行停止    DMU_EC30HDNC1C    关闭
    ${status}    Run Keyword And Return Status    Wait Until Keyword Succeeds    1m    1    信号量数据值不为    交流空调在位状态    在位
    Should Not Be True    ${status}
    设置web控制量    RS485总线设备统计
    sleep    1m
    ${交流空调在位状态}    获取web实时数据    交流空调在位状态
    should be equal    '${交流空调在位状态}'    'None'
    显示属性配置    交流空调在位状态    数字量    web_attr=Off    gui_attr=Off

交流空调工作状态获取测试
    连接CSU
    显示属性配置    交流空调工作状态    数字量    web_attr=On    gui_attr=On
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流空调工作状态     正常
    控制子工具运行停止    DMU_EC30HDNC1C    关闭
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流空调工作状态     通讯断
    显示属性配置    交流空调工作状态    数字量    web_attr=Off    gui_attr=Off
