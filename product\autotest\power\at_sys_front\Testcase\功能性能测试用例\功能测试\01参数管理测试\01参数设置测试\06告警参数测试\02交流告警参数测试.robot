*** Settings ***
Suite Setup       设置web参数量    市电配置_1    有
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
01 交流停电告警参数测试
    [Tags]    view    PMSA-NTest
    [Setup]    判断web参数是否存在    交流停电
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    交流停电    ${VAR}
        sleep    2
        ${交流停电告警级别获取}    获取web参数量    交流停电
        should be equal    ${交流停电告警级别获取}    ${VAR}
    END
    设置web参数量    交流停电    主要
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    交流停电干接点    ${VAR}
        ${交流停电告警干接点获取}    获取web参数量    交流停电干接点
        should be true    ${交流停电告警干接点获取}==${VAR}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    交流停电干接点    0
    ...    AND    设置web参数量    交流停电    主要

02 交流缺相告警参数测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    交流缺相
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    交流缺相_1    ${VAR}
        sleep    2
        ${交流缺相告警级别获取}    获取web参数量    交流缺相_1
        should be equal    ${交流缺相告警级别获取}    ${VAR}
    END
    设置web参数量    交流缺相_1    主要
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    交流缺相_1干接点    ${VAR}
        ${交流缺相告警干接点获取}    获取web参数量    交流缺相_1干接点
        should be true    ${交流缺相告警干接点获取}==${VAR}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    交流缺相_1干接点    0
    ...    AND    设置web参数量    交流缺相_1    主要

03 交流电压低告警参数测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    交流电压低
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    交流电压低_1    ${VAR}
        sleep    2
        ${交流电压低告警级别获取}    获取web参数量    交流电压低_1
        should be equal    ${交流电压低告警级别获取}    ${VAR}
    END
    设置web参数量    交流电压低_1    主要
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    交流电压低_1干接点    ${VAR}
        ${交流电压低告警干接点获取}    获取web参数量    交流电压低_1干接点
        should be true    ${交流电压低告警干接点获取}==${VAR}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    交流电压低_1干接点    0
    ...    AND    设置web参数量    交流电压低_1    主要

04 交流电压高告警参数测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    交流电压高
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    交流电压高_1    ${VAR}
        sleep    2
        ${交流电压高告警级别获取}    获取web参数量    交流电压高_1
        should be equal    ${交流电压高告警级别获取}    ${VAR}
    END
    设置web参数量    交流电压高_1    主要
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    交流电压高_1干接点    ${VAR}
        ${交流电压高告警干接点获取}    获取web参数量    交流电压高_1干接点
        should be true    ${交流电压高告警干接点获取}==${VAR}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    交流电压高_1干接点    0
    ...    AND    设置web参数量    交流电压高_1    主要

05 交流电流高告警参数测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    交流电流高
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    交流电流高_1    ${VAR}
        sleep    2
        ${交流电流高告警级别获取}    获取web参数量    交流电流高_1
        should be equal    ${交流电流高告警级别获取}    ${VAR}
    END
    设置web参数量    交流电流高_1    主要
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    交流电流高_1干接点    ${VAR}
        ${交流电流高告警干接点获取}    获取web参数量    交流电流高_1干接点
        should be true    ${交流电流高告警干接点获取}==${VAR}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    交流电流高_1干接点    0
    ...    AND    设置web参数量    交流电流高_1    主要

06 交流电压不平衡告警参数测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    交流电压不平衡
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    交流电压不平衡    ${VAR}
        sleep    2
        ${交流电压不平衡告警级别获取}    获取web参数量    交流电压不平衡
        should be equal    ${交流电压不平衡告警级别获取}    ${VAR}
    END
    设置web参数量    交流电压不平衡    主要
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    交流电压不平衡干接点    ${VAR}
        ${交流电压不平衡告警干接点获取}    获取web参数量    交流电压不平衡干接点
        should be true    ${交流电压不平衡告警干接点获取}==${VAR}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    交流电压不平衡干接点    0
    ...    AND    设置web参数量    交流电压不平衡    主要

07 交流电压过低告警参数测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    交流电压过低
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    交流电压过低_1    ${VAR}
        sleep    2
        ${交流电压过低告警级别获取}    获取web参数量    交流电压过低_1
        should be equal    ${交流电压过低告警级别获取}    ${VAR}
    END
    设置web参数量    交流电压过低_1    主要
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    交流电压过低_1干接点    ${VAR}
        ${交流电压过低告警干接点获取}    获取web参数量    交流电压过低_1干接点
        should be true    ${交流电压过低告警干接点获取}==${VAR}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    交流电压过低_1干接点    0
    ...    AND    设置web参数量    交流电压过低_1    主要

08 交流电压过高告警参数测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    交流电压过高
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    交流电压过高_1    ${VAR}
        sleep    2
        ${交流电压过高告警级别获取}    获取web参数量    交流电压过高_1
        should be equal    ${交流电压过高告警级别获取}    ${VAR}
    END
    设置web参数量    交流电压过高_1    主要
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    交流电压过高_1干接点    ${VAR}
        ${交流电压过高告警干接点获取}    获取web参数量    交流电压过高_1干接点
        should be true    ${交流电压过高告警干接点获取}==${VAR}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    交流电压过高_1干接点    0
    ...    AND    设置web参数量    交流电压过高_1    主要

09 交流防雷器异常参数测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    交流防雷器异常
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    交流防雷器异常    ${VAR}
        sleep    2
        ${交流防雷器异常告警级别获取}    获取web参数量    交流防雷器异常
        should be equal    ${交流防雷器异常告警级别获取}    ${VAR}
    END
    设置web参数量    交流防雷器异常    主要
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    交流防雷器异常干接点    ${VAR}
        ${交流防雷器异常告警干接点获取}    获取web参数量    交流防雷器异常干接点
        should be true    ${交流防雷器异常告警干接点获取}==${VAR}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    交流防雷器异常干接点    0
    ...    AND    设置web参数量    交流防雷器异常    主要

10 市电频率高参数测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    市电频率高
    设置web参数量    市电配置_1    有
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    市电频率高    ${VAR}
        sleep    2
        ${市电频率高告警级别获取}    获取web参数量    市电频率高
        should be equal    ${市电频率高告警级别获取}    ${VAR}
    END
    设置web参数量    市电频率高    主要
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    市电频率高干接点    ${VAR}
        ${市电频率高告警干接点获取}    获取web参数量    市电频率高干接点
        should be true    ${市电频率高告警干接点获取}==${VAR}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    市电频率高干接点    0
    ...    AND    设置web参数量    市电频率高    主要

11 市电相电压不平衡参数测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    市电相电压不平衡
    设置web参数量    市电配置_1    有
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    市电相电压不平衡    ${VAR}
        sleep    2
        ${市电相电压不平衡告警级别获取}    获取web参数量    市电相电压不平衡
        should be equal    ${市电相电压不平衡告警级别获取}    ${VAR}
    END
    设置web参数量    市电相电压不平衡    主要
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    市电相电压不平衡干接点    ${VAR}
        ${市电相电压不平衡告警干接点获取}    获取web参数量    市电相电压不平衡干接点
        should be true    ${市电相电压不平衡告警干接点获取}==${VAR}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    市电相电压不平衡干接点    0
    ...    AND    设置web参数量    市电相电压不平衡    主要

13 市电停电参数测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    市电停电
    设置web参数量    市电配置_1    有
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    市电停电    ${VAR}
        sleep    2
        ${市电停电告警级别获取}    获取web参数量    市电停电
        should be equal    ${市电停电告警级别获取}    ${VAR}
    END
    设置web参数量    市电停电    主要
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    市电停电干接点    ${VAR}
        ${市电停电告警干接点获取}    获取web参数量    市电停电干接点
        should be true    ${市电停电告警干接点获取}==${VAR}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    市电停电干接点    0
    ...    AND    设置web参数量    市电停电    主要

14 市电电压过压参数测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    市电电压过压
    设置web参数量    市电配置_1    有
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    市电电压过压    ${VAR}
        sleep    2
        ${市电电压过压告警级别获取}    获取web参数量    市电电压过压
        should be equal    ${市电电压过压告警级别获取}    ${VAR}
    END
    设置web参数量    市电电压过压    主要
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    市电电压过压干接点    ${VAR}
        ${市电电压过压告警干接点获取}    获取web参数量    市电电压过压干接点
        should be true    ${市电电压过压告警干接点获取}==${VAR}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    市电电压过压干接点    0
    ...    AND    设置web参数量    市电电压过压    主要

15 市电电压欠压参数测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    市电电压欠压
    设置web参数量    市电配置_1    有
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    市电电压欠压    ${VAR}
        sleep    2
        ${市电电压欠压告警级别获取}    获取web参数量    市电电压欠压
        should be equal    ${市电电压欠压告警级别获取}    ${VAR}
    END
    设置web参数量    市电电压欠压    主要
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    市电电压欠压干接点    ${VAR}
        ${市电电压欠压告警干接点获取}    获取web参数量    市电电压欠压干接点
        should be true    ${市电电压欠压告警干接点获取}==${VAR}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    市电电压欠压干接点    0
    ...    AND    设置web参数量    市电电压欠压    主要

16 市电相电流高参数测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    市电相电流高
    设置web参数量    市电配置_1    有
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    市电相电流高    ${VAR}
        sleep    2
        ${市电相电流高告警级别获取}    获取web参数量    市电相电流高
        should be equal    ${市电相电流高告警级别获取}    ${VAR}
    END
    设置web参数量    市电相电流高    主要
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    市电相电流高干接点    ${VAR}
        ${市电相电流高告警干接点获取}    获取web参数量    市电相电流高干接点
        should be true    ${市电相电流高告警干接点获取}==${VAR}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    市电相电流高干接点    0
    ...    AND    设置web参数量    市电相电流高    主要

17 市电频率低参数测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    市电频率低
    设置web参数量    市电配置_1    有
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    10    2    设置web参数量    市电频率低    ${VAR}
        sleep    2
        ${市电频率低告警级别获取}    获取web参数量    市电频率低
        should be equal    ${市电频率低告警级别获取}    ${VAR}
    END
    设置web参数量    市电频率低    主要
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    10    2    设置web参数量    市电频率低干接点    ${VAR}
        ${市电频率低告警干接点获取}    获取web参数量    市电频率低干接点
        should be true    ${市电频率低告警干接点获取}==${VAR}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    市电频率低干接点    0
    ...    AND    设置web参数量    市电频率低    主要

18 交流输入限功率预警参数测试
    [Tags]    PMSA-NTest
    [Setup]    run keywords    Wait Until Keyword Succeeds    10    2    设置web参数量    市电配置_1    有
    ...    AND    判断web参数是否存在    市电额定有功功率
    ...    AND    Wait Until Keyword Succeeds    10    2    设置web参数量    市电额定有功功率    15
    ...    AND    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    交流输入限功率预警
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    5m    2    设置web参数量    交流输入限功率预警    ${VAR}
        sleep    2
        ${市电频率低告警级别获取}    获取web参数量    交流输入限功率预警
        should be equal    ${市电频率低告警级别获取}    ${VAR}
    END
    设置web参数量    交流输入限功率预警    主要
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    5m    2    设置web参数量    交流输入限功率预警干接点    ${VAR}
        ${市电频率低告警干接点获取}    获取web参数量    交流输入限功率预警干接点
        should be true    ${市电频率低告警干接点获取}==${VAR}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    交流输入限功率预警干接点    0
    ...    AND    设置web参数量    交流输入限功率预警    主要

19 交流输入限功率告警参数测试
    [Tags]    PMSA-NTest
    [Setup]    run keywords    Wait Until Keyword Succeeds    10    2    设置web参数量    市电配置_1    有
    ...    AND    判断web参数是否存在    市电额定有功功率
    ...    AND    Wait Until Keyword Succeeds    10    2    设置web参数量    市电额定有功功率    15
    ...    AND    Wait Until Keyword Succeeds    10    2    判断web参数是否存在    交流输入限功率告警
    FOR    ${VAR}    IN    严重    主要    次要    警告    屏蔽
        Wait Until Keyword Succeeds    5m    2    设置web参数量    交流输入限功率告警    ${VAR}
        sleep    2
        ${市电频率低告警级别获取}    获取web参数量    交流输入限功率告警
        should be equal    ${市电频率低告警级别获取}    ${VAR}
    END
    设置web参数量    交流输入限功率告警    主要
    FOR    ${VAR}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${VAR}    evaluate    str(${VAR})
        Wait Until Keyword Succeeds    5m    2    设置web参数量    交流输入限功率告警干接点    ${VAR}
        ${市电频率低告警干接点获取}    获取web参数量    交流输入限功率告警干接点
        should be true    ${市电频率低告警干接点获取}==${VAR}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    交流输入限功率告警干接点    0
    ...    AND    设置web参数量    交流输入限功率告警    主要
