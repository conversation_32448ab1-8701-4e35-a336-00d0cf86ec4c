*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
直流配电负载分路配置获取测试
    连接CSU
    显示属性配置    一次下电分路状态    数字量    web_attr=On    gui_attr=On
    显示属性配置    一次下电扩展分路状态    数字量    web_attr=On    gui_attr=On
    显示属性配置    二次下电分路状态    数字量    web_attr=On    gui_attr=On
    显示属性配置    二次下电扩展分路状态    数字量    web_attr=On    gui_attr=On
    模拟数字量告警    下电告警    ON
    FOR    ${VAR}    IN RANGE    4    0    -1
        ${VAR}    evaluate    str(${VAR})
        ${状态}    获取web实时数据    一次下电分路状态_${VAR}
        should not be equal    '${状态}'    'None'
    END
    ${扩展分路状态}    获取web实时数据    一次下电扩展分路状态
    should not be equal    '${扩展分路状态}'    'None'
    FOR    ${VAR}    IN RANGE    4    0    -1
        ${VAR}    evaluate    str(${VAR})
        ${状态}    获取web实时数据    二次下电分路状态_${VAR}
        should not be equal    '${状态}'    'None'
    END
    ${扩展分路状态}    获取web实时数据    二次下电扩展分路状态
    should not be equal    '${扩展分路状态}'    'None'
    模拟数字量告警    下电告警    OFF
    显示属性配置    一次下电分路状态    数字量    web_attr=Off    gui_attr=Off
    显示属性配置    一次下电扩展分路状态    数字量    web_attr=Off    gui_attr=Off
    显示属性配置    二次下电分路状态    数字量    web_attr=Off    gui_attr=Off
    显示属性配置    二次下电扩展分路状态    数字量    web_attr=Off    gui_attr=Off
