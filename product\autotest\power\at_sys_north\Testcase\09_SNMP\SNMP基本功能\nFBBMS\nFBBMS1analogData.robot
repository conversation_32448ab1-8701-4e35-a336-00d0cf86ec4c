*** Settings ***
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp_test关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/Web关键字_V30.robot
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
# snmp批量获取nFBBMS模拟量
#     [Documentation]    1h13min
#     写入CSV文档    FB100C2模拟量获取测试    信号名称    信号值    结果    备注
#     连接CSU
#     进行SNMP_V2/V3连接    ${snmp连接方式}
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=NFBBMS
#     ${排除列表}    create list
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    2    1    0    2
#     ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    nFBBMS    analogData
#     Comment    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    FBBMS模块    analog data    ${PU排除模拟量信号}
#     Comment    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
#     Comment    FOR    ${i}    IN    ${snmp待测}[0]
#     FOR    ${i}    IN    @{snmp待测}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${节点名称}    Get From Dictionary    ${i}    snmp_name
#         ${信号序号}    Get From Dictionary    ${i}    Vindex
#         ${缺省值}    获取web参数上下限范围    ${信号名称}
#         Run Keyword And Continue On Failure    南向子设备模拟量获取值封装判断结果    snmp    fb100c2    ${信号名称}    获取模拟量    ${缺省值}[1]    FB100C2模拟量获取测试    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    南向子设备模拟量获取值封装判断结果    snmp    fb100c2    ${信号名称}    获取模拟量    ${缺省值}[2]    FB100C2模拟量获取测试    null    ${节点名称}    ${信号序号}
#         Run keyword IF    ${缺省值}[1]!=${缺省值}[0] and ${缺省值}[2]!=${缺省值}[0]    Run Keyword And Continue On Failure    南向子设备模拟量获取值封装判断结果    snmp    fb100c2    ${信号名称}    获取模拟量    ${缺省值}[0]    FB100C2模拟量获取测试    null    ${节点名称}    ${信号序号}
#     END
#     断开连接SNMP

snmp批量获取nFBBMS模拟量
    [Documentation]    1h13min
    写入CSV文档    FB100C2模拟量获取测试    信号名称    信号值    结果    备注
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=NFBBMS
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    2    1    0    2
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    nFBBMS    analogData
    Comment    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    FBBMS模块    analog data    ${PU排除模拟量信号}
    Comment    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    Comment    FOR    ${i}    IN    ${snmp待测}[0]
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    1    snmp
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    snmp    fb100c2    获取模拟量    ${缺省值列表}    FB100C2模拟量获取测试    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    2    snmp
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    snmp    fb100c2    获取模拟量    ${缺省值列表}    FB100C2模拟量获取测试    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    0    snmp
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    snmp    fb100c2    获取模拟量    ${缺省值列表}    FB100C2模拟量获取测试    null
    断开连接SNMP

# snmp批量获取nFBBMS实时告警和trap告警
#     [Documentation]    6h
#     写入CSV文档    FB100C2数字量和告警量获取测试    信号名称    信号值    结果
#     连接CSU
#     进行SNMP_V2/V3连接    ${snmp连接方式}
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=NFBBMS
#     ${排除列表}    create list
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100C2排除告警量信号}    ${排除列表}    3    1    0    2
#     ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    nFBBMS    alarm    True
#     Comment    FOR    ${i}    IN    ${snmp待测}[0]
#     Comment    FOR    ${i}    IN    @{snmp待测}
#     Comment    FOR    ${i}    IN    ${snmp待测}[0]
#     FOR    ${i}    IN    @{snmp待测}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${节点名称}    Get From Dictionary    ${i}    signal_node
#         ${信号序号}    Get From Dictionary    ${i}    Vindex
#         Run Keyword And Continue On Failure    南向RS485子设备告警量产生封装判断结果    snmp    fb100c2    ${信号名称}    获取告警量    ${告警产生}    FB100B3数字量和告警量获取测试    NFBBMS    ${节点名称}    ${信号序号}    null    null    null    null
#         Run Keyword And Continue On Failure    南向RS485子设备告警量恢复封装判断结果    snmp    fb100c2    ${信号名称}    获取告警量    ${告警恢复}    FB100B3数字量和告警量获取测试    NFBBMS    ${节点名称}    ${信号序号}    null    null    null    null
#     END
#     断开连接SNMP

snmp批量获取nFBBMS实时告警和trap告警
    [Tags]    trap
    [Documentation]    6h
    写入CSV文档    FB100C2数字量和告警量获取测试    信号名称    信号值    结果
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    @{信号名称列表1}  create list
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=NFBBMS
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100C2排除告警量信号}    ${排除列表}    3    1    0    2
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    nFBBMS    alarm    True
    Comment    FOR    ${i}    IN    ${snmp待测}[0]
    Comment    FOR    ${i}    IN    @{snmp待测}
    Comment    FOR    ${i}    IN    ${snmp待测}[0]
    FOR    ${i}    IN    @{snmp待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    signal_node
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
        Set To Dictionary    ${dict}     snmp_name     ${节点名称}
        Set To Dictionary    ${dict}     Vindex     ${信号序号}
        Set To Dictionary    ${dict}     device_name     NFBBMS
        Append To List       ${信号名称列表1}    ${dict}
    END
    Run Keyword And Continue On Failure    南向RS485子设备告警量列表产生封装判断结果    snmp    fb100c2    ${信号名称列表1}    
    ...    获取告警量    ${告警产生}    FB100C2数字量和告警量获取测试    电池    NFBBMS
    ...    null    null    null    null    null    null
    Run Keyword And Continue On Failure    南向RS485子设备告警量列表恢复封装判断结果    snmp    fb100c2    ${信号名称列表1}    
    ...    获取告警量    ${告警恢复}    FB100C2数字量和告警量获取测试    电池    NFBBMS
    ...    null    null    null    null    null    null
    断开连接SNMP

snmp批量获取合并的实时告警和trap告警
    [Tags]    trap
    [Documentation]    1h22min
    写入CSV文档    FB100C2数字量和告警量获取测试    信号名称    信号值    结果
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=NFBBMS
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100C2排除欠压低温的告警量信号}    ${排除列表}    2    1    0    2
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    nFBBMS    alarm    True
    FOR    ${i}    IN    @{snmp待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    signal_node
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        Run Keyword And Continue On Failure    南向RS485子设备告警量产生封装判断结果    snmp    fb100c2    ${信号名称}    获取告警量    ${告警产生}    FB100C2数字量和告警量获取测试    NFBBMS    ${节点名称}    ${信号序号}    null    null    null    null
        Run Keyword And Continue On Failure    南向RS485子设备告警量恢复封装判断结果    snmp    fb100c2    ${信号名称}    获取告警量    ${告警恢复}    FB100C2数字量和告警量获取测试    NFBBMS    ${节点名称}    ${信号序号}    null    null    null    null
    END
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=NFBBMS
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100C2排除过压高温的告警量信号}    ${排除列表}    2    1    0    2
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    nFBBMS    alarm    True
    FOR    ${i}    IN    @{snmp待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    signal_node
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        Run Keyword And Continue On Failure    南向RS485子设备告警量产生封装判断结果    snmp    fb100c2    ${信号名称}    获取告警量    2    FB100C2数字量和告警量获取测试    NFBBMS    ${节点名称}    ${信号序号}    null    null    null    null
        Run Keyword And Continue On Failure    南向RS485子设备告警量恢复封装判断结果    snmp    fb100c2    ${信号名称}    获取告警量    ${告警恢复}    FB100C2数字量和告警量获取测试    NFBBMS    ${节点名称}    ${信号序号}    null    null    null    null
    END
    断开连接SNMP
