*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
人工维护检测过程正确性测试
    [Tags]    notest
    

整流器轮换过程中增大负载测试
    [Tags]    notest
    

整流器轮换过程中减小负载测试
    [Tags]    notest
    

人工维护检测时电池检测异常测试
    
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    2s    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池检测异常    次要
    关闭电池模拟器输出
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    电池检测异常
    Wait Until Keyword Succeeds    2m    2s    设置web控制量    人工维护检测
    重置电池模拟器输出
    Wait Until Keyword Succeeds    5m    2s    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    电池检测异常
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    电池检测异常
    Wait Until Keyword Succeeds    2m    2s    设置web控制量    人工维护检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    人工维护检测
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3
    ...    AND    重置电池模拟器输出

人工维护检测时交流停电测试
    
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    人工维护检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    交流节能状态    人工维护检测
    关闭交流源输出
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    市电停电
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    0
    打开交流源输出
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    交流供电状态    市电1
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    停电
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    系统停电
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    过渡
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    系统停电
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3
    ...    AND    打开交流源输出

电池测试进入人工维护检测
    
    电池管理初始化
    系统复位
    #进入节能模式
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    10    1    设置web参数量    测试最长时间    2
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动测试
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    测试
    Wait Until Keyword Succeeds    1m    1    设置web控制量    人工维护检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    交流节能状态    人工维护检测
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    测试
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    测试
    Wait Until Keyword Succeeds    10m    1    信号量数据值不为    交流节能状态    人工维护检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3
    ...    AND    设置web设备参数量为默认值    测试最长时间

电池容量异常时人工维护检测测试
    
    电池管理初始化
    系统复位
    #进入节能模式
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    8
    Wait Until Keyword Succeeds    1m    1    设置web控制量    人工维护检测
    Wait Until Keyword Succeeds    3m    1    信号量数据值为    交流节能状态    自动非节能
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    100

电池回路断时人工维护检测测试（一组回路断一组回路正常）
    
    电池管理初始化
    系统复位
    #进入节能模式
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_3    100
    ${级别设置值}    获取web参数量    电池回路断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池回路断    严重
    wait until keyword succeeds    5m    2    查询指定告警信息    电池回路断
    Wait Until Keyword Succeeds    1m    1    设置web控制量    人工维护检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    交流节能状态    人工维护检测
    # Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    检测
    # Wait Until Keyword Succeeds    10m    2    信号量数据值为    交流节能状态    自动节能
    # Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    # Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_3    0
    # wait until keyword succeeds    5m    2    查询指定告警信息不为    电池回路断
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_3    0
    wait until keyword succeeds    5m    2    查询指定告警信息不为    电池回路断
    系统复位
    #进入节能模式
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_3    0
    ...    AND    设置web参数量    电池回路断    主要

电池回路断时人工维护检测测试（只有一组电池且回路断）
    
    电池管理初始化
    系统复位
    #进入节能模式
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_3    100
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    0
    sleep    5
    ${级别设置值}    获取web参数量    电池回路断-3
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池回路断    严重
    wait until keyword succeeds    5m    2    查询指定告警信息    电池回路断
    Wait Until Keyword Succeeds    1m    1    设置web控制量    人工维护检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    交流节能状态    人工维护检测
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_3    0
    wait until keyword succeeds    5m    2    查询指定告警信息不为    电池回路断
    系统复位
    #进入节能模式
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    100
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_3    0
    ...    AND    设置web参数量    电池回路断-1    主要

系统异常时人工维护检测测试
    
    电池管理初始化
    系统复位
    #进入节能模式
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    ${级别设置值}    获取web参数量    电池电压低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池电压低    严重
    ${可设置范围}    获取web参数可设置范围    电池电压低阈值
    设置web参数量    电池电压低阈值    ${可设置范围}[1]
    ${电池电压低设置值}    获取web参数量    电池电压低阈值
    向下调节电池电压    ${电池电压低设置值}-0.5
    wait until keyword succeeds    5m    2    查询指定告警信息    电池电压低
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置web控制量    人工维护检测
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    交流节能状态    人工维护检测
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    交流节能状态    自动非节能
    向上调节电池电压    ${电池电压低设置值}+1
    wait until keyword succeeds    5m    1    判断告警不存在    电池电压低
    系统复位
    #进入节能模式
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置web控制量    人工维护检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    2m10s    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    ...    AND    设置web设备参数量为默认值    电池电压低阈值
    ...    AND    设置web参数量    整流器最小开机数量    3
    ...    AND    重置电池模拟器输出

人工维护检测时整流器故障测试
    
    电池管理初始化
    系统复位
    #进入节能模式
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    人工维护检测
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    交流节能状态    人工维护检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    交流节能状态    自动节能
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    ${均充电压可设置范围}    获取web参数可设置范围    均充电压
    设置web参数量    均充电压    ${均充电压可设置范围}[0]
    ${输出高停机电压可设置范围}    获取web参数可设置范围    整流器输出高停机电压
    设置web参数量    整流器输出高停机电压    ${输出高停机电压可设置范围}[0]
    #使整流器输出过压保证无正常工作的整流器
    ${整流器输出高电压}    获取web参数量    整流器输出高停机电压
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动均充
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    均充
    向上调节电池电压    ${整流器输出高电压}+0.5
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    工作整流器数量    0
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    自动非节能
    Wait Until Keyword Succeeds    3m    1    信号量数据值为    电池管理状态    系统停电
    向下调节电池电压    53.5
    ${在线整流器数量}    获取web实时数据    在线整流器数量
    Wait Until Keyword Succeeds    5m10s    1    信号量数据值为    工作整流器数量    ${在线整流器数量}
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3
    ...    AND    重置电池模拟器输出
