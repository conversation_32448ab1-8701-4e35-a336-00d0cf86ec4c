*** Settings ***
Documentation     预约均充完成后，预约均充使能为禁止，预约均充时间为最大值
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
预约均充时间到均充功能
    [Tags]    notest
    [Documentation]    ==20190321-F：实际均充时长与预约均充设置时长不符。==20190417-F：预约时间到，没有进均充，仍然为浮充状态
    
    电池管理初始化
    设置web参数量    电池均充周期    180    #默认90
    设置web参数量    电池测试周期    0    #默认0天
    设置web参数量    电池检测周期    0    #默认0天
    设置web参数量    预约均充使能    允许    #默认0禁止
    Sleep    5
    ${预约均充时长范围}    获取web参数上下限范围_有单位    预约均充时长
    ${预约均充时长设置值}    run keyword if    '${预约均充时长范围}[3]'== 'Hour'    evaluate    ${预约均充时长范围}[1]+1
    ...    ELSE    evaluate    ${预约均充时长范围}[1]+2
    Wait Until Keyword Succeeds    10    1    设置web参数量    预约均充时长    ${预约均充时长设置值}    #默认0
    ${预约均充时长设置值转换后}    run keyword if    '${预约均充时长范围}[3]'== 'Hour'    evaluate    ${预约均充时长设置值}*60
    ...    ELSE    set variable    ${预约均充时长设置值}
    sleep    5
    ${预约均充时长}    获取web参数量    预约均充时长
    ${电池均充周期}    获取web参数量    电池均充周期
    ${下次均充日期}    获取web实时数据    下次均充时间    #比下次周期均充时间要提前，否则会进入周期均充，而无法判断是否没进预约均充
    ${设置预约均充时间}    Subtract Time From Date    ${下次均充日期}    50s    #比16:00:00差50s
    设置web参数量    预约均充日期    ${设置预约均充时间}    #默认2037-12-31
    设置系统时间    ${设置预约均充时间}
    sleep    10
    ${等待时间}    evaluate    ${预约均充时长设置值转换后}+3
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    均充
    ${预约均充时间开始}    get time
    Wait Until Keyword Succeeds    ${等待时间}m    1    信号量数据值为    电池管理状态    浮充
    ${预约均充时间结束}    get time
    ${实际预约均充时间}    Subtract Date From Date    ${预约均充时间结束}    ${预约均充时间开始}
    ${均充时间上限}    evaluate    ${预约均充时长设置值转换后}*60+50
    ${均充时间下限}    evaluate    ${预约均充时长设置值转换后}*60-50
    Comment    should be true    110 <=${实际预约均充时间}<=130
    should be true    ${均充时间下限}<=${实际预约均充时间}<=${均充时间上限}
    sleep    10    #延时判断预约均充禁止
    ${预约均充使能}    获取web参数量    预约均充使能
    Should Be Equal    ${预约均充使能}    禁止
    ${下次均充时间_cal}    add time to date    ${下次均充日期}    ${电池均充周期}d    exclude_millis=yes
    sleep    5
    ${下次均充时间_new}    获取web实时数据    下次均充时间
    should be equal    ${下次均充时间_new}    ${下次均充时间_cal}
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池测试周期    电池均充周期    电池检测周期    预约均充使能

预约均充使能禁止无均充
    
    电池管理初始化
    设置web参数量    电池均充周期    90    #默认90
    设置web参数量    电池测试周期    0    #默认0天
    设置web参数量    电池检测周期    0    #默认0天
    设置web参数量    预约均充使能    允许    #默认0禁止#预约均充完成，会自动禁止
    sleep    5
    ${预约均充时长范围}    获取web参数上下限范围_有单位    预约均充时长
    ${预约均充时长设置值}    run keyword if    '${预约均充时长范围}[3]'== 'Hour'    evaluate    ${预约均充时长范围}[1]+1
    ...    ELSE    evaluate    ${预约均充时长范围}[1]+5
    Wait Until Keyword Succeeds    10    1    设置web参数量    预约均充时长    ${预约均充时长设置值}    #默认0
    ${下次均充日期}    获取web实时数据    下次均充时间    #比下次周期均充时间要提前，否则会进入周期均充，而无法判断是否没进预约均充
    ${设置预约均充时间}    Subtract Time From Date    ${下次均充日期}    2d5s    #比16:00:00差5s
    设置web参数量    预约均充日期    ${设置预约均充时间}    #默认2037-12-31
    设置web参数量    预约均充使能    禁止
    sleep    5
    ${浮充持续时间初始值}    获取web实时数据    电池状态持续时间
    设置系统时间    ${设置预约均充时间}
    sleep    2m
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    浮充
    ${浮充持续时间当前值}    获取web实时数据    电池状态持续时间
    should be true    ${浮充持续时间当前值}>${浮充持续时间初始值}
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池测试周期    电池均充周期    电池检测周期    预约均充使能

均充使能禁止无预约均充
    
    电池管理初始化
    设置web参数量    电池均充周期    90    #默认90
    设置web参数量    电池测试周期    0    #默认0天
    设置web参数量    电池检测周期    0    #默认0天
    设置web参数量    预约均充使能    允许    #默认禁止
    Sleep    5
    ${预约均充时长范围}    获取web参数上下限范围_有单位    预约均充时长
    ${预约均充时长设置值}    run keyword if    '${预约均充时长范围}[3]'== 'Hour'    evaluate    ${预约均充时长范围}[1]+1
    ...    ELSE    evaluate    ${预约均充时长范围}[1]+5
    Wait Until Keyword Succeeds    10    1    设置web参数量    预约均充时长    ${预约均充时长设置值}    #默认0
    ${下次均充日期}    获取web实时数据    下次均充时间    #比下次周期均充时间要提前，否则会进入周期均充，而无法判断是否没进预约均充
    ${设置预约均充时间}    Subtract Time From Date    ${下次均充日期}    2d5s    #比16:00:00差5s
    设置web参数量    预约均充日期    ${设置预约均充时间}    #默认2037-12-31
    设置web参数量    均充使能    禁止
    sleep    5
    ${浮充持续时间初始值}    获取web实时数据    电池状态持续时间
    设置系统时间    ${设置预约均充时间}
    sleep    2m
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    浮充
    ${浮充持续时间当前值}    获取web实时数据    电池状态持续时间
    should be true    ${浮充持续时间当前值}>${浮充持续时间初始值}
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web参数量    均充使能    允许
    ...    AND    Wait Until Keyword Succeeds    30    2    设置web设备参数量为默认值    电池测试周期
    ...    电池均充周期    电池检测周期    预约均充使能

测试时预约均充时间到
    [Documentation]    0:浮充/Float;1:均充/Equal;2:测试/Test;3:放电/Discharge;4:检测/Detect;5:过渡/Transition;6:充电/Charge
    ...    =测试时预约均充时间到，等待测试完成，立即进入预约均充
    ...    ==20190321-F：进入预约均充，预约均充时间未到，就退出均充进入浮充
    ...    ==20190322-F：实际均充时长与预约均充设置时长不符
    
    电池管理初始化
    设置web参数量    电池均充周期    90    #默认90
    设置web参数量    电池测试周期    0    #默认0天
    设置web参数量    电池检测周期    0    #默认0天
    设置web参数量    预约均充使能    允许
    sleep    5
    ${预约均充时长范围}    获取web参数上下限范围_有单位    预约均充时长
    ${预约均充时长设置值}    run keyword if    '${预约均充时长范围}[3]'== 'Hour'    evaluate    ${预约均充时长范围}[1]+1
    ...    ELSE    evaluate    ${预约均充时长范围}[1]+2
    Wait Until Keyword Succeeds    10    1    设置web参数量    预约均充时长    ${预约均充时长设置值}    #默认0
    ${测试最长时间范围}    获取web参数上下限范围_有单位    测试最长时间
    ${测试最长时间}    run keyword if    '${测试最长时间范围}[3]'== 'Hour'    evaluate    ${测试最长时间范围}[1]+1
    ...    ELSE    evaluate    ${测试最长时间范围}[1]+5
    设置web参数量    测试最长时间    ${测试最长时间}
    ${测试最长时间转换后}    run keyword if    '${测试最长时间范围}[3]'== 'Hour'    evaluate    ${测试最长时间}*60
    ...    ELSE    set variable    ${测试最长时间}
    ${预约均充时长设置值转换后}    run keyword if    '${预约均充时长范围}[3]'== 'Hour'    evaluate    ${预约均充时长设置值}*60
    ...    ELSE    set variable    ${预约均充时长设置值}
    ${均充末期维持时间范围}    获取web参数上下限范围_有单位    均充末期维持时间
    ${均充最短时间范围}    获取web参数上下限范围_有单位    均充最短时间
    ${均充最长时间范围}    获取web参数上下限范围_有单位    均充最长时间
    ${均充末期维持时间}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间范围}[1]+1
    ...    ELSE    evaluate    ${均充末期维持时间范围}[1]+3
    ${均充最短时间}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间范围}[1]+1
    ...    ELSE    evaluate    ${均充最短时间范围}[1]+3
    ${均充最长时间}    run keyword if    '${均充最长时间范围}[3]'== 'Hour'    evaluate    ${均充最长时间范围}[1]+1
    ...    ELSE    evaluate    ${均充最长时间范围}[1]+5
    设置web参数量    均充末期维持时间    ${均充末期维持时间}
    设置web参数量    均充最短时间    ${均充最短时间}
    设置web参数量    均充最长时间    ${均充最长时间}
    ${均充末期维持时间转换后}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间}*60
    ...    ELSE    set variable    ${均充末期维持时间}
    ${均充最短时间转换后}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间}*60
    ...    ELSE    set variable    ${均充最短时间}
    ${均充最长时间转换后}    run keyword if    '${均充最长时间范围}[3]'== 'Hour'    evaluate    ${均充最长时间}*60
    ...    ELSE    set variable    ${均充最长时间}
    log    ===单位换算完成===
    ${下次均充日期}    获取web实时数据    下次均充时间    #比下次周期均充时间要提前，否则会进入周期均充，而无法判断是否没进预约均充
    ${设置预约均充时间}    Subtract Time From Date    ${下次均充日期}    5d5s    #比16:00:00差5s
    设置web参数量    预约均充日期    ${设置预约均充时间}    #默认2037-12-31
    ${预约均充时长}    获取web参数量    预约均充时长
    #测试结束转均充
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动测试
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    测试
    sleep    2m
    设置系统时间    ${设置预约均充时间}
    sleep    15
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    测试
    Wait Until Keyword Succeeds    ${测试最长时间转换后}m    1    信号量数据值不为    电池管理状态    测试
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    均充
    sleep    2m
    Wait Until Keyword Succeeds    ${均充最长时间转换后}m    2    信号量数据值为    电池状态持续时间    0
    ${预约均充使能}    获取web参数量    预约均充使能
    Should Be Equal    ${预约均充使能}    允许
    #均充结束进预约均充
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    均充
    ${预约均充时间开始}    get time
    ${退出均充等待时间}    evaluate    ${预约均充时长设置值转换后}+3
    Wait Until Keyword Succeeds    ${退出均充等待时间}m    1    信号量数据值为    电池管理状态    浮充
    ${预约均充时间结束}    get time
    ${实际预约均充时间}    Subtract Date From Date    ${预约均充时间结束}    ${预约均充时间开始}
    ${均充时间上限}    evaluate    ${预约均充时长设置值转换后}*60+50
    ${均充时间下限}    evaluate    ${预约均充时长设置值转换后}*60-50
    should be true    ${均充时间下限}<=${实际预约均充时间}<=${均充时间上限}
    sleep    10    #延时判断预约均充禁止
    ${预约均充使能}    获取web参数量    预约均充使能
    Should Be Equal    ${预约均充使能}    禁止
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池测试周期    电池均充周期    电池检测周期    测试最长时间
    ...    均充最长时间    均充最短时间    均充末期维持时间    预约均充使能

测试手动退出进行预约均充功能
    [Documentation]    0:浮充/Float;1:均充/Equal;2:测试/Test;3:放电/Discharge;4:检测/Detect;5:过渡/Transition;6:充电/Charge
    
    电池管理初始化
    设置web参数量    电池均充周期    90    #默认90
    设置web参数量    电池测试周期    0    #默认0天
    设置web参数量    电池检测周期    0    #默认0天
    设置web参数量    预约均充使能    允许
    ${预约均充时长范围}    获取web参数上下限范围_有单位    预约均充时长
    ${预约均充时长设置值}    run keyword if    '${预约均充时长范围}[3]'== 'Hour'    evaluate    ${预约均充时长范围}[1]+1
    ...    ELSE    evaluate    ${预约均充时长范围}[1]+2
    Wait Until Keyword Succeeds    10    1    设置web参数量    预约均充时长    ${预约均充时长设置值}    #默认0
    ${预约均充时长设置值转换后}    run keyword if    '${预约均充时长范围}[3]'== 'Hour'    evaluate    ${预约均充时长设置值}*60
    ...    ELSE    set variable    ${预约均充时长设置值}
    sleep    5
    ${测试最长时间范围}    获取web参数上下限范围_有单位    测试最长时间
    设置web参数量    测试最长时间    ${测试最长时间范围}[0]
    ${测试终止电压范围}    获取web参数上下限范围    测试终止电压
    ${测试终止容量范围}    获取web参数上下限范围    测试终止SOC阈值
    ${测试失败容量范围}    获取web参数上下限范围    测试失败SOC阈值
    设置web参数量    测试终止电压    ${测试终止电压范围}[0]    #默认46
    设置web参数量    测试终止SOC阈值    ${测试终止容量范围}[0]    #默认65
    设置web参数量    测试失败SOC阈值    ${测试失败容量范围}[0]    #默认85
    ${下次均充日期}    获取web实时数据    下次均充时间    #比下次周期均充时间要提前，否则会进入周期均充，而无法判断是否没进预约均充
    ${设置预约均充时间}    Subtract Time From Date    ${下次均充日期}    2d5s    #比16:00:00差5s
    设置web参数量    预约均充日期    ${设置预约均充时间}    #默认2037-12-31
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动测试
    Wait Until Keyword Succeeds    20    1    信号量数据值为    电池管理状态    测试
    sleep    1m
    设置系统时间    ${设置预约均充时间}
    sleep    10
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动浮充
    ${预约均充时间开始}    get time
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    均充
    ${等待时间}    evaluate    ${预约均充时长设置值转换后}+3
    Wait Until Keyword Succeeds    ${等待时间}m    2    信号量数据值为    电池管理状态    浮充
    ${预约均充时间结束}    get time
    ${实际预约均充时间}    Subtract Date From Date    ${预约均充时间结束}    ${预约均充时间开始}
    ${均充时间上限}    evaluate    ${预约均充时长设置值转换后}*60+50
    ${均充时间下限}    evaluate    ${预约均充时长设置值转换后}*60-50
    should be true    ${均充时间下限}<=${实际预约均充时间}<=${均充时间上限}
    sleep    10
    ${预约均充使能}    获取web参数量    预约均充使能
    Should Be Equal    ${预约均充使能}    禁止
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池测试周期    电池均充周期    电池检测周期    预约均充使能

检测时预约均充时间到
    [Documentation]    0:浮充/Float;1:均充/Equal;2:测试/Test;3:放电/Discharge;4:检测/Detect;5:过渡/Transition;6:充电/Charge
    
    电池管理初始化
    设置web参数量    电池均充周期    90
    设置web参数量    电池测试周期    0
    设置web参数量    电池检测周期    0
    设置web参数量    电池检测持续时间    2
    设置web参数量    预约均充使能    允许
    sleep    5
    ${预约均充时长范围}    获取web参数上下限范围_有单位    预约均充时长
    ${预约均充时长设置值}    run keyword if    '${预约均充时长范围}[3]'== 'Hour'    evaluate    ${预约均充时长范围}[1]+1
    ...    ELSE    evaluate    ${预约均充时长范围}[1]+2
    设置web参数量    预约均充时长    ${预约均充时长设置值}    #默认0
    ${预约均充时长设置值转换后}    run keyword if    '${预约均充时长范围}[3]'== 'Hour'    evaluate    ${预约均充时长设置值}*60
    ...    ELSE    set variable    ${预约均充时长设置值}
    ${下次均充日期}    获取web实时数据    下次均充时间    #比下次周期均充时间要提前，否则会进入周期均充，而无法判断是否没进预约均充
    ${设置预约均充时间}    Subtract Time From Date    ${下次均充日期}    2d5s    #比16:00:00差5s
    设置web参数量    预约均充日期    ${设置预约均充时间}    #默认2037-12-31
    Wait Until Keyword Succeeds    2m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    sleep    30
    Wait Until Keyword Succeeds    1m    1    设置系统时间    ${设置预约均充时间}
    sleep    10
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    检测
    Wait Until Keyword Succeeds    2m    1    信号量数据值不为    电池管理状态    检测
    sleep    15
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    均充
    ${等待时间}    evaluate    ${预约均充时长设置值转换后}+1
    Wait Until Keyword Succeeds    ${等待时间}m    2    信号量数据值为    电池管理状态    浮充
    sleep    20
    ${预约均充使能}    获取web参数量    预约均充使能
    Should Be Equal    ${预约均充使能}    禁止
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池测试周期    电池均充周期    电池检测周期    电池检测持续时间
    ...    预约均充使能

均充时预约均充时间到
    [Documentation]    ==20190321-F：未恢复预约均充日期
    ...    ==20190323-F：进入预约均充，预约均充时间未到，就退出均充进入浮充
    
    电池管理初始化
    设置web参数量    电池均充周期    90
    设置web参数量    电池测试周期    0
    设置web参数量    电池检测周期    0
    设置web参数量    预约均充使能    允许
    ${预约均充时长范围}    获取web参数上下限范围_有单位    预约均充时长
    ${预约均充时长设置值}    run keyword if    '${预约均充时长范围}[3]'== 'Hour'    evaluate    ${预约均充时长范围}[1]+1
    ...    ELSE    evaluate    ${预约均充时长范围}[1]+2
    Wait Until Keyword Succeeds    10    1    设置web参数量    预约均充时长    ${预约均充时长设置值}    #默认0
    ${预约均充时长设置值转换后}    run keyword if    '${预约均充时长范围}[3]'== 'Hour'    evaluate    ${预约均充时长设置值}*60
    ...    ELSE    set variable    ${预约均充时长设置值}
    ${均充末期维持时间范围}    获取web参数上下限范围_有单位    均充末期维持时间
    ${均充最短时间范围}    获取web参数上下限范围_有单位    均充最短时间
    ${均充最长时间范围}    获取web参数上下限范围_有单位    均充最长时间
    ${均充末期维持时间}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间范围}[1]+1
    ...    ELSE    evaluate    ${均充末期维持时间范围}[1]+3
    ${均充最短时间}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间范围}[1]+1
    ...    ELSE    evaluate    ${均充最短时间范围}[1]+3
    ${均充最长时间}    run keyword if    '${均充最长时间范围}[3]'== 'Hour'    evaluate    ${均充最长时间范围}[1]+1
    ...    ELSE    evaluate    ${均充最长时间范围}[1]+5
    设置web参数量    均充末期维持时间    ${均充末期维持时间}
    设置web参数量    均充最短时间    ${均充最短时间}
    设置web参数量    均充最长时间    ${均充最长时间}
    ${均充末期维持时间转换后}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间}*60
    ...    ELSE    set variable    ${均充末期维持时间}
    ${均充最短时间转换后}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间}*60
    ...    ELSE    set variable    ${均充最短时间}
    ${均充最长时间转换后}    run keyword if    '${均充最长时间范围}[3]'== 'Hour'    evaluate    ${均充最长时间}*60
    ...    ELSE    set variable    ${均充最长时间}
    log    ===单位换算完成===
    sleep    10
    ${下次均充日期}    获取web实时数据    下次均充时间    #比下次周期均充时间要提前，否则会进入周期均充，而无法判断是否没进预约均充
    ${设置预约均充时间}    Subtract Time From Date    ${下次均充日期}    2d5s    #比16:00:00差5s
    设置web参数量    预约均充日期    ${设置预约均充时间}    #默认2037-12-31
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动均充
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    均充
    sleep    1m
    设置系统时间    ${设置预约均充时间}
    sleep    8
    ${等待时间}    evaluate    ${均充最长时间转换后}+3
    Wait Until Keyword Succeeds    ${等待时间}m    1    信号量数据值为    电池状态持续时间    0
    ${预约均充使能}    获取web参数量    预约均充使能
    Should Be Equal    ${预约均充使能}    允许
    sleep    15
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    均充
    ${预约均充持续时间}    evaluate    ${预约均充时长设置值转换后}+1
    Wait Until Keyword Succeeds    ${预约均充持续时间}m    1    信号量数据值为    电池管理状态    浮充
    ${预约均充使能}    获取web参数量    预约均充使能
    Should Be Equal    ${预约均充使能}    禁止
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池测试周期    电池均充周期    电池检测周期    均充最长时间
    ...    均充最短时间    均充末期维持时间

停电时预约均充时间到
    [Documentation]    0:浮充/Float;1:均充/Equal;2:测试/Test;3:放电/Discharge;4:检测/Detect;5:过渡/Transition;6:充电/Charge
    ...    =预约时间到时正在放电，市电来电后立即进入预约均充
    ...    ==20190321-F：进入预约均充，预约均充时间未到，就退出均充进入浮充
    
    电池管理初始化
    设置web参数量    电池均充周期    90
    设置web参数量    电池测试周期    0
    设置web参数量    电池检测周期    0
    设置web参数量    预约均充使能    允许
    ${预约均充时长范围}    获取web参数上下限范围_有单位    预约均充时长
    ${预约均充时长设置值}    run keyword if    '${预约均充时长范围}[3]'== 'Hour'    evaluate    ${预约均充时长范围}[1]+1
    ...    ELSE    evaluate    ${预约均充时长范围}[1]+2
    Wait Until Keyword Succeeds    10    1    设置web参数量    预约均充时长    ${预约均充时长设置值}    #默认0
    ${预约均充时长设置值转换后}    run keyword if    '${预约均充时长范围}[3]'== 'Hour'    evaluate    ${预约均充时长设置值}*60
    ...    ELSE    set variable    ${预约均充时长设置值}
    ${均充末期维持时间范围}    获取web参数上下限范围    均充末期维持时间
    设置web参数量    均充末期维持时间    ${均充末期维持时间范围}[0]
    ${均充最短时间范围}    获取web参数上下限范围    均充最短时间
    设置web参数量    均充最短时间    ${均充最短时间范围}[0]
    ${均充最长时间范围}    获取web参数上下限范围    均充最长时间
    设置web参数量    均充最长时间    ${均充最长时间范围}[0]
    ${均充阈值容量范围}    获取web参数上下限范围    均充阈值SOC
    设置web参数量    均充阈值SOC    ${均充阈值容量范围}[0]
    ${均充阈值电压范围}    获取web参数上下限范围    均充阈值电压
    设置web参数量    均充阈值电压    ${均充阈值电压范围}[0]
    ${均充阈值放电时间范围}    获取web参数上下限范围    均充阈值放电时间
    设置web参数量    均充阈值放电时间    ${均充阈值放电时间范围}[0]
    sleep    10
    ${下次均充日期}    获取web实时数据    下次均充时间    #比下次周期均充时间要提前，否则会进入周期均充，而无法判断是否没进预约均充
    ${设置预约均充时间}    Subtract Time From Date    ${下次均充日期}    2d5s    #比16:00:00差5s
    设置web参数量    预约均充日期    ${设置预约均充时间}    #默认2037-12-31
    设置负载电压电流    53.5    10
    打开负载输出
    关闭交流源输出
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    系统停电
    Wait Until Keyword Succeeds    1m    1    设置系统时间    ${设置预约均充时间}
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    系统停电
    打开交流源输出
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    系统停电
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    过渡
    ${预约均充时间开始}    get time
    sleep    10
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    均充
    ${退出均充等待时间}    evaluate    ${预约均充时长设置值转换后}+3
    Wait Until Keyword Succeeds    ${退出均充等待时间}m    2    信号量数据值为    电池管理状态    浮充
    ${预约均充时间结束}    get time
    ${实际预约均充时间}    Subtract Date From Date    ${预约均充时间结束}    ${预约均充时间开始}
    ${均充时间上限}    evaluate    ${预约均充时长设置值转换后}*60+50
    ${均充时间下限}    evaluate    ${预约均充时长设置值转换后}*60-50
    should be true    ${均充时间下限}<=${实际预约均充时间}<=${均充时间上限}
    sleep    10
    ${预约均充使能}    获取web参数量    预约均充使能
    Should Be Equal    ${预约均充使能}    禁止
    关闭负载输出
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池测试周期    电池均充周期    电池检测周期
    ...    AND    打开交流源输出
    ...    AND    关闭负载输出

预约均充异常结束后不更新下次均充时间
    
    电池管理初始化
    设置web参数量    电池均充周期    90    #默认90
    设置web参数量    电池测试周期    0    #默认0天
    设置web参数量    电池检测周期    0    #默认0天
    设置web参数量    预约均充使能    允许    #默认0禁止
    Sleep    5
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池温度高
    ${预约均充时长范围}    获取web参数上下限范围_有单位    预约均充时长
    ${预约均充时长设置值}    run keyword if    '${预约均充时长范围}[3]'== 'Hour'    evaluate    ${预约均充时长范围}[1]+1
    ...    ELSE    evaluate    ${预约均充时长范围}[1]+2
    Wait Until Keyword Succeeds    10    1    设置web参数量    预约均充时长    ${预约均充时长设置值}    #默认0
    ${预约均充时长设置值转换后}    run keyword if    '${预约均充时长范围}[3]'== 'Hour'    evaluate    ${预约均充时长设置值}*60
    ...    ELSE    set variable    ${预约均充时长设置值}
    sleep    5
    ${预约均充时长}    获取web参数量    预约均充时长
    ${电池均充周期}    获取web参数量    电池均充周期
    ${下次均充日期}    获取web实时数据    下次均充时间    #比下次周期均充时间要提前，否则会进入周期均充，而无法判断是否没进预约均充
    ${设置预约均充时间}    Subtract Time From Date    ${下次均充日期}    2d5s    #比16:00:00差5s
    设置web参数量    预约均充日期    ${设置预约均充时间}    #默认2037-12-31
    设置系统时间    ${设置预约均充时间}
    sleep    10
    ${等待时间}    evaluate    ${预约均充时长设置值转换后}+3
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    均充
    ${预约均充时间开始}    get time
    #获取告警级别
    ${级别设置值}    获取web参数量    电池温度高
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池温度高    严重
    sleep    3
    ${级别设置值}    获取web参数量    电池温度高
    #（1）告警产生
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    10    1.6
    ...    电池_1    电池温度
    #产生电池温度高告警
    ${电池温度高阈值范围}    获取web参数上下限范围    电池温度高阈值
    ${电池温度高阈值可设置范围}    获取web参数可设置范围    电池温度高阈值
    设置web参数量    电池温度高阈值    ${电池温度高阈值可设置范围}[0]
    ${电池温度}    获取web实时数据    电池温度-1
    Wait Until Keyword Succeeds    10    1    判断告警存在    电池温度高
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    浮充
    ${预约均充时间结束}    get time
    ${实际预约均充时间}    Subtract Date From Date    ${预约均充时间结束}    ${预约均充时间开始}
    ${均充时间上限}    evaluate    ${预约均充时长设置值转换后}*60+50
    ${均充时间下限}    evaluate    ${预约均充时长设置值转换后}*60-50
    should be true    ${实际预约均充时间}<=${均充时间下限}
    sleep    1m    #延时判断预约均充禁止
    ${预约均充使能}    获取web参数量    预约均充使能
    Should Be Equal    ${预约均充使能}    禁止
    sleep    5
    ${下次均充时间_new}    获取web实时数据    下次均充时间
    should be equal    ${下次均充时间_new}    ${下次均充日期}
    #（2）告警恢复
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    0    1
    ...    电池_1    电池温度
    设置web参数量    电池温度高阈值    ${电池温度高阈值范围}[0]
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池温度高
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池测试周期    电池均充周期    电池检测周期    预约均充使能
    ...    电池温度高阈值
    ...    AND    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}
    ...    0    1    电池_1    电池温度
    ...    AND    设置web参数量    电池温度高    主要
