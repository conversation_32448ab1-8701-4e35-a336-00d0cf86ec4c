*** Settings ***
Suite Setup       Run Keywords    连接CSU
...               AND     wait until keyword succeeds    10    2    设置web控制量    开启SSH
...               AND     测试用例前置条件
Suite Teardown    Run Keywords    wait until keyword succeeds    10    2    设置web控制量    关闭SSH
...               AND     测试用例后置条件
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
电池测试记录最大条数测试
    
    电池管理初始化
    sleep    20
    ${起始时间}    获取系统时间
    #产生测试记录
    设置历史记录最大条数  his_test    3    100
    FOR    ${k}    IN RANGE    101
        Wait Until Keyword Succeeds    10    1    设置web控制量    启动测试
        Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    测试
        sleep    10
        Wait Until Keyword Succeeds    10    1    设置web控制量    启动浮充
        Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
        sleep    10
        ${记录数量}    获取web事件记录数量    电池测试记录
        exit for loop if    ${记录数量}>=100
    END
    sleep    2m
    ${记录数量}    获取web事件记录数量    电池测试记录
    should be true    ${记录数量}== 100
    Wait Until Keyword Succeeds    10    1    设置web控制量    启动测试
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    测试
    sleep    10
    Wait Until Keyword Succeeds    10    1    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    sleep    2m
    ${记录数量}    获取web事件记录数量    电池测试记录
    should be true    ${记录数量}== 100
   
    




电池充电记录最大条数测试
    
    电池管理初始化
    sleep    20
    ${电压设置值}    获取web参数量    浮充电压
    向上调节电池电压    ${电压设置值}+1
    ${电池电流1}    获取web实时数据    电池电流-1
    run keyword if    ${电池电流1}>0    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.battcurr1}    -${电池电流1}    1    直流配电    电池分流器电流_1
    sleep    1m
    ${电池电流调节后1}    获取web实时数据    电池电流-1
    #产生充电记录
    设置历史记录最大条数  his_chg    1    1000
    FOR    ${k}    IN RANGE    1001
        Wait Until Keyword Succeeds    10    1    设置web控制量    启动均充
        Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    均充
        ${电压}    获取web实时数据    直流电压
    #（1）产生充电电流
    #设置电池电流零点
        Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.battcurr1}    10    1    直流配电    电池分流器电流_1
        sleep    2m
        Wait Until Keyword Succeeds    5m    1    设置web控制量    启动浮充
        Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    #（1）产生充电电流
    #设置电池电流零点
        Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.battcurr1}    0    1    直流配电    电池分流器电流_1
        ${电池电流}    获取web实时数据    电池组总电流
        sleep    30
        ${记录数量}    获取web事件记录数量    电池充电记录
        exit for loop if    ${记录数量}>=1000
    END
    sleep    2m
    ${记录数量}    获取web事件记录数量    电池充电记录
    should be true    ${记录数量}== 1000
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动均充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    均充
    ${电压}    获取web实时数据    直流电压
    #（1）产生充电电流
    #设置电池电流零点
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.battcurr1}    10    1    直流配电    电池分流器电流_1
    sleep    2m
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    #（1）恢复充电电流
    #设置电池电流零点
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.battcurr1}    0    1    直流配电    电池分流器电流_1
    sleep    30
    ${记录数量}    获取web事件记录数量    电池充电记录
    should be true    ${记录数量}== 1000
    向下调节电池电压    ${电压设置值}
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.battcurr1}    0    1    直流配电    电池分流器电流_1
     ...    AND    重置电池模拟器输出  
    






电池放电记录最大条数测试
    
    电池管理初始化
    sleep    20
    ${电压设置值}    获取web参数量    浮充电压
    向上调节电池电压    ${电压设置值}+1
    ${电池电流1}    获取web实时数据    电池电流-1
    run keyword if    ${电池电流1}>0    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.battcurr1}    -${电池电流1}    1    直流配电    电池分流器电流_1
    sleep    1m
    ${电池电流调节后1}    获取web实时数据    电池电流-1
    #产生充电记录
    设置历史记录最大条数  his_dischg    4    1000
    FOR    ${k}    IN RANGE    1001
        Wait Until Keyword Succeeds    10    1    设置web控制量    启动均充
        Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    均充
        ${电压}    获取web实时数据    直流电压
    #（1）产生充电电流
    #设置电池电流零点
        Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.battcurr1}    -20    1    直流配电    电池分流器电流_1
        sleep    2m
        Wait Until Keyword Succeeds    5m    1    设置web控制量    启动浮充
        Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    #（1）产生充电电流
    #设置电池电流零点
        Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.battcurr1}    0    1    直流配电    电池分流器电流_1
        ${电池电流}    获取web实时数据    电池组总电流
        sleep    30
        ${记录数量}    获取web事件记录数量    电池放电记录
        exit for loop if    ${记录数量}>=1000
    END
    sleep    2m
    ${记录数量}    获取web事件记录数量    电池放电记录
    should be true    ${记录数量}== 1000
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动均充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    均充
    ${电压}    获取web实时数据    直流电压
    #（1）产生充电电流
    #设置电池电流零点
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.battcurr1}    -20    1    直流配电    电池分流器电流_1
    sleep    2m
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    #（1）恢复充电电流
    #设置电池电流零点
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.battcurr1}    0    1    直流配电    电池分流器电流_1
    sleep    30
    ${记录数量}    获取web事件记录数量    电池放电记录
    should be true    ${记录数量}== 1000
    向下调节电池电压    ${电压设置值}
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.battcurr1}    0    1    直流配电    电池分流器电流_1
    ...    AND    重置电池模拟器输出
   
    





电池均充记录最大条数测试
    
    电池管理初始化
    sleep    20
    ${起始时间}    获取系统时间
    #产生均充记录
    设置历史记录最大条数  his_equ    2    1000
    FOR    ${k}    IN RANGE    1001
        Wait Until Keyword Succeeds    10    1    设置web控制量    启动均充
        Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    均充
        sleep    10
        Wait Until Keyword Succeeds    10    1    设置web控制量    启动浮充
        Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
        sleep    10
        ${均充记录数量}    获取web事件记录数量    电池均充记录
        exit for loop if    ${均充记录数量}>=1000
    END
    sleep    2m
    ${均充记录数量}    获取web事件记录数量    电池均充记录
    should be true    ${均充记录数量}== 1000
    Wait Until Keyword Succeeds    10    1    设置web控制量    启动均充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    均充
    sleep    10
    Wait Until Keyword Succeeds    10    1    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    sleep    2m
    ${均充记录数量}    获取web事件记录数量    电池均充记录
    should be true    ${均充记录数量}== 1000
    







历史告警最大条数测试
    连接CSU
    ${起始时间}    获取系统时间
    #制造多个实时告警（电池温度无效），通过禁止和允许所有告警来增加历史告警记录
    #设置一组电池组
    设置通道无效值/恢复通道原始值    ${plat.batttemp1}    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池温度无效    主要
    sleep    60
    ${电池组数量}    获取web参数的数量    电池组容量
    ${历史告警数量}    获取web历史告警数量    ${empty}    ${empty}
    #制造10000条历史告警记录
    ${级别设置值}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    <<禁止所有告警~0x1001030010001>>    严重
    设置历史记录最大条数   history_alarm   0   20000
    FOR    ${k}    IN RANGE    20001
        Wait Until Keyword Succeeds    30    1    设置web控制量    <<禁止所有告警~0x1001040010001>>
        Wait Until Keyword Succeeds    30    1    查询指定告警信息    禁止所有告警
        ${历史告警数量}    获取web历史告警数量    ${empty}    ${empty}
        Wait Until Keyword Succeeds    30    1    设置web控制量    允许所有告警
        sleep    2
        exit for loop if    ${历史告警数量}>=20000
    END
    sleep    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    100
    ${终止时间1}    获取系统时间
    设置通道无效值/恢复通道原始值    ${plat.batttemp1}    0
    ${历史告警内容}    获取web历史告警内容    ${起始时间}    ${终止时间1}    1    10
    #should be equal    ${历史告警内容}[2]['full_name'][5:]    电池温度无效    #在满10000条历史告警记录后的一条应是“禁止所有告警”
    

历史告警循环覆盖测试
    [Documentation]    前面一个用例将历史告警产生满。
    连接CSU
    设置历史记录最大条数   history_alarm   0   20000
    ${起始时间}    获取系统时间
    ${历史告警数量}    获取web历史告警数量    ${empty}    ${empty}
    should be true    ${历史告警数量} == 20000
    #产生环境温度高告警
    ${级别设置值}    获取web参数量    环境温度高
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    环境温度高    次要
    ${告警级别}    获取web参数量    环境温度高
    FOR    ${零点}    IN RANGE    15    40    2
        设置通道配置    ${plat.envtemp}    ${零点}    1    系统运行环境    环境温度
        sleep    3
        ${环境温度获取}    获取web实时数据    环境温度
        ${环境温度设置值}    获取web参数量    环境温度高阈值
        exit for loop if    ${环境温度获取}>${ 环境温度设置值}
    END
    
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    环境温度高
    ${产生时间2}    获取系统时间
    #产生历史告警
    设置通道配置    ${plat.envtemp}    0    1    系统运行环境    环境温度
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息不为    环境温度高
    sleep    2m
    ${历史告警数量2}    获取web历史告警数量    ${empty}    ${empty}
    log    ${empty}
    should be true    ${历史告警数量2} == ${历史告警数量}
    @{历史告警内容1}    获取web历史告警内容    ${empty}    ${empty}    1    1
    @{历史告警内容1}    split string    ${历史告警内容1}[-1]    ,
    should contain    ${历史告警内容1}[5]    环境温度高
    should be equal    ${历史告警内容1}[3]    ${告警级别}
    ${告警结束时间差1}    subtract date from date    ${历史告警内容1}[1]    ${产生时间2}
    should be true    -10<${告警结束时间差1}<10
    #产生电池均充历史告警
    ${级别设置值}    获取web参数量    电池均充
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池均充    次要
    ${告警级别}    获取web参数量    电池均充
    Wait Until Keyword Succeeds    10    1    设置web控制量    启动均充
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息    电池均充
    ${终止时间1}    获取系统时间
    Wait Until Keyword Succeeds    10    1    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    电池均充
    sleep    2m
    ${历史告警数量1}    获取web历史告警数量    ${empty}    ${empty}
    should be true    ${历史告警数量} == ${历史告警数量1}
    @{历史告警内容}    获取web历史告警内容    ${empty}    ${empty}    1    1
    @{历史告警内容}    split string    ${历史告警内容}[-1]    ,
    should contain    ${历史告警内容}[5]    电池均充
    should be equal    ${历史告警内容}[3]    ${告警级别}
    ${告警结束时间差}    subtract date from date    ${历史告警内容}[2]    ${终止时间1}
    should be true    -10<${告警结束时间差}<10
    [Teardown]    设置通道配置    ${plat.envtemp}    0    1    系统运行环境    环境温度







历史事件最大条数测试
    
    连接CSU
    sleep    20
    ${起始时间}    获取系统时间
    #产生WEB登录历史事件    #安全事件
    设置历史记录最大条数   history_event   0   2000
    FOR    ${k}    IN RANGE    2001
        Wait Until Keyword Succeeds    30    1    设置web控制量    <<禁止所有告警~0x1001040010001>>
        Wait Until Keyword Succeeds    30    1    查询指定告警信息    禁止所有告警
        ${历史事件数量}    获取web历史事件数量    ${empty}    ${empty}
        Wait Until Keyword Succeeds    30    1    设置web控制量    允许所有告警
        exit for loop if    ${历史事件数量}>=2000
    END
    sleep    2m
    ${终止时间2}    获取系统时间
    @{历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间2}    所有    1    10
    should contain    ${历史事件内容1}[-1]    允许所有告警
    

历史事件循环覆盖测试
    [Documentation]    前面一个用例将历史事件记录产生满。
    
    连接CSU
    sleep    2m
    ${起始时间}    获取系统时间
    ${历史事件数量}    获取web历史事件数量    ${empty}    ${empty}    所有
    should be true    ${历史事件数量} == 2000
    #产生登录事件
    连接CSU
    ${终止时间1}    获取系统时间
    sleep    2m
    ${历史事件数量1}    获取web历史事件数量    ${empty}    ${empty}    所有
    should be true    ${历史事件数量1}==${历史事件数量}
    @{安全事件内容1}    获取web历史事件内容    ${empty}    ${empty}    所有    1    1
    should contain    ${安全事件内容1}[-1]    Web登录 成功
    @{操作记录内容1}    split string    ${安全事件内容1}[-1]    ,
    ${告警结束时间差}    subtract date from date    ${操作记录内容1}[2]    ${终止时间1}
    should be true    -10<${告警结束时间差}<10
    #产生整流器风扇调速禁止历史事件
    Wait Until Keyword Succeeds    2m    2    设置web控制量    整流器风扇调速禁止-1
    ${终止时间2}    获取系统时间
    sleep    2m
    ${历史事件数量2}    获取web历史事件数量    ${empty}    ${empty}    所有
    should be true    ${历史事件数量2}==${历史事件数量}
    @{安全事件内容1}    获取web历史事件内容    ${empty}    ${empty}    所有    1    1
    should contain    ${安全事件内容1}[-1]    整流器风扇调速禁止
    @{操作记录内容1}    split string    ${安全事件内容1}[-1]    ,
    ${告警结束时间差}    subtract date from date    ${操作记录内容1}[2]    ${终止时间2}
    should be true    -10<${告警结束时间差}<10





历史数据最大条数测试
    
    连接CSU
    sleep    20
    ${起始时间}    获取系统时间
    #产生WEB登录历史事件    #安全事件
    设置历史记录最大条数   history_data   0   200000
    FOR    ${k}    IN RANGE    200001
        Wait Until Keyword Succeeds    30    1    设置web控制量    <<禁止所有告警~0x1001040010001>>
        Wait Until Keyword Succeeds    30    1    查询指定告警信息    禁止所有告警
        ${历史数据数量}    获取web历史数据数量    ${empty}    ${empty}
        Wait Until Keyword Succeeds    30    1    设置web控制量    允许所有告警
        exit for loop if    ${历史数据数量}>=200000
    END
    sleep    2m
    ${历史数据数量1}    获取web历史数据数量    ${empty}    ${empty}
    should be true    ${历史数据数量1} ==200000
    
    

历史数据循环覆盖测试
    [Documentation]    前面一个用例将历史告警产生满。
    
    连接CSU
    ${起始时间}    获取系统时间
    ${历史告警数量}    获取web历史数据数量    ${empty}    ${empty}
    should be true    ${历史告警数量} == 200000
    Wait Until Keyword Succeeds    10    2    设置web控制量    CAN总线设备统计
    Wait Until Keyword Succeeds    10    2    设置web控制量    RS485总线设备统计
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    10    1    设置web参数量    历史数据保存间隔    5
    sleep    8m
    ${结束时间}    获取系统时间
    ${数量}    获取web历史数据数量    ${empty}    ${empty}
    @{历史数据内容1}    获取web历史数据内容    ${empty}    ${empty}    all    all    30    1
    @{历史数据内容1}    split string    ${历史数据内容1}[-1]    ,
    should be true    ${历史数据内容1}[0]+29 == ${数量}
    should contain    ${历史数据内容1}[2]    整流器休眠状态
    should be true    ${历史数据内容1}[3] == 0 or ${历史数据内容1}[3] == 1
    ${告警结束时间差}    subtract date from date    ${历史数据内容1}[1]    ${结束时间}
    should be true    -480<${告警结束时间差}<480
    [Teardown]    设置通道配置    ${plat.envtemp}    0    1    系统运行环境    环境温度





市电工作记录最大条数测试
    连接CSU
    sleep    20
    #产生市电工作记录
    设置历史记录最大条数   mains_work   6   1000
    FOR    ${k}    IN RANGE    1002
        ${开始数量}    获取web事件记录数量    市电工作记录
        Wait Until Keyword Succeeds    5m    2    信号量数据值大于    在线整流器数量    1
        同时设置三相电压频率    220    50
        打开交流源输出
        Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1
        Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流供电状态    市电1
        ${起始时间}    获取系统时间
        ${起始电量}    获取web实时数据    市电供电量
        ${起始工作时间}    获取web实时数据    市电工作时间
        sleep    3m
        关闭交流源输出
        Wait Until Keyword Succeeds    5m    2    信号量数据值不为    交流供电状态    市电1
        sleep    3m
        ${结束时间}    获取系统时间
        ${结束电量}    获取web实时数据    市电供电量
        ${结束工作时间}    获取web实时数据    市电工作时间
        ${结束数量}    获取web事件记录数量    市电工作记录
        #should be true    ${结束数量} == ${开始数量} + 1
        sleep    10
        ${市电工作记录数量}    获取web事件记录数量    市电工作记录
        exit for loop if    ${市电工作记录数量}>=1000
    END	
    sleep    2m
    ${市电工作记录数量}    获取web事件记录数量    市电工作记录
    should be true    ${市电工作记录数量}==1000
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    在线整流器数量    1
    同时设置三相电压频率    220    50
    打开交流源输出
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流供电状态    市电1
    ${起始时间}    获取系统时间
    ${起始电量}    获取web实时数据    市电供电量
    ${起始工作时间}    获取web实时数据    市电工作时间
    sleep    3m
    关闭交流源输出
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    交流供电状态    市电1
    sleep    8m
    ${市电工作记录数量}    获取web事件记录数量    市电工作记录
    should be true    ${市电工作记录数量}==1000
    


太阳能工作记录最大条数测试
    PU测试前置条件
    连接CSU
    sleep    20
    #产生太阳能工作记录
    设置历史记录最大条数  solar_work    5    1000
    FOR    ${k}    IN RANGE    1002
        ${开始数量}    获取web事件记录数量    太阳能工作记录
        设置子工具值    pu    all    模拟量    PU输出电流    10
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-4    10
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-10    10
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-20    10
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-30    10
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-40    10
        ${起始时间}    获取系统时间
        ${起始电量}    获取web实时数据    累计光伏发电量
        ${起始工作时间}    获取web实时数据    光伏工作时间
        sleep    6m
        设置子工具值    pu    all    模拟量    PU输出电流    1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-4    1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-10    1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-20    1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-30    1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-40    1
        sleep    6m
        ${结束时间}    获取系统时间
        ${结束电量}    获取web实时数据    累计光伏发电量
        ${结束工作时间}    获取web实时数据    光伏工作时间
        ${工作时间}    evaluate    ${结束工作时间}-${起始工作时间}
        ${结束数量}    获取web事件记录数量    太阳能工作记录
        @{记录内容}    获取web事件记录最新一条    太阳能工作记录
        should be true    ${记录内容}[0] == ${结束数量}
        #should be true    ${结束数量} == ${开始数量} + 1
        sleep    10
        ${太阳能工作记录数量}    获取web事件记录数量    太阳能工作记录
        exit for loop if    ${太阳能工作记录数量}>=1000
    END
    sleep    2m
    ${太阳能工作记录数量}    获取web事件记录数量    太阳能工作记录
    should be true    ${太阳能工作记录数量} == 1000
    设置子工具值    pu    all    模拟量    PU输出电流    10
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-4    10
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-10    10
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-20    10
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-30    10
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-40    10
    ${起始时间}    获取系统时间
    ${起始电量}    获取web实时数据    累计光伏发电量
    ${起始工作时间}    获取web实时数据    光伏工作时间
    sleep    6m
    设置子工具值    pu    all    模拟量    PU输出电流    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-4    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-10    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-20    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-30    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-40    1
    sleep    8m
    ${太阳能工作记录数量}    获取web事件记录数量    太阳能工作记录
    should be true    ${太阳能工作记录数量} == 1000
    PU测试结束条件
    
    


    




