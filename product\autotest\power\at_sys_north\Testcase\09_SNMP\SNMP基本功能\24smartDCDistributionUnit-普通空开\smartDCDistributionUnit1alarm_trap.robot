*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
snmp批量获取SDDU告警量trap测试
    写入CSV文档    SDDU数字量和告警量获取测试    信号名称    信号值    结果    备注
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=智能直流配电单元
    ${排除列表}    create list
    ${信号名称列表1}    Create List
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SDDU排除告警量信号}    ${排除列表}    2
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    smartDCDistributionUnit    alarm    True
    设置子工具值    SDU2    all    呼叫    SDU负载分路状态1    1
    设置子工具值    SDU2    all    呼叫    SDU负载分路状态2    1
    设置子工具值    SDU2    all    呼叫    SDU负载分路状态3    1
    设置子工具值    SDU2    all    呼叫    SDU负载分路状态4    1
    FOR    ${直流配电单元序号}    IN RANGE    1    ${智能直流配电单元数量}+1
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    SDDU工作状态-${直流配电单元序号}    正常
    END
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    signal_node
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
        Set To Dictionary    ${dict}     snmp_name     ${节点名称}
        Set To Dictionary    ${dict}     Vindex     ${信号序号}
        Set To Dictionary    ${dict}     device_name     直流空调
        Append To List       ${信号名称列表1}    ${dict}
    END
    Run Keyword And Continue On Failure    南向RS485子设备告警量列表产生封装判断结果    snmp    SDU2    ${信号名称列表1}    
    ...    告警    0    SDDU数字量和告警量获取测试    负载    智能直流配电单元    
    ...    null    null    null    null    null    null
    Run Keyword And Continue On Failure    南向RS485子设备告警量列表恢复封装判断结果    snmp    SDU2    ${信号名称列表1}    
    ...    告警    1    SDDU数字量和告警量获取测试    负载    智能直流配电单元
    ...    null    null    null    null    null    null

SDDU通讯中断告警&SDDU通讯状态获取
    连接CSU
    ${级别设置值}    获取web参数量    SDDU通讯中断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    SDDU通讯中断    严重
    FOR    ${直流配电单元序号}    IN RANGE    1    ${智能直流配电单元数量}+1
        Wait Until Keyword Succeeds    5m    1    判断告警不存在    SDDU通讯中断-${直流配电单元序号}
    END
    ${剩余空开数量}    Evaluate    int(${智能直流配电单元数量})-1
    ${剩余空开数量}    BuiltIn.Convert To String    ${剩余空开数量}
    设置子工具个数    SDU2    ${剩余空开数量}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    FOR    ${直流配电单元序号}    IN RANGE    2    ${智能直流配电单元数量}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    SDDU通讯状态-${直流配电单元序号}    异常
        wait until keyword succeeds    5m    1    判断告警存在    SDDU通讯中断-${直流配电单元序号}
        ${snmp获取值1}    获取SNMP数据_单个    sDDUCommunicationState${直流配电单元序号}value
        should be equal    ${snmp获取值1}    1
        ${snmp英文名}    获取snmp单个告警英文名    SDDU通讯中断_${直流配电单元序号}
        ${告警产生}    判断Trap告警产生    ${snmp英文名}
        should be true    ${告警产生}
    END
    设置子工具个数    SDU2    ${智能直流配电单元数量}
    FOR    ${直流配电单元序号}    IN RANGE    2    ${智能直流配电单元数量}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    SDDU通讯状态-${直流配电单元序号}    正常
        wait until keyword succeeds    5m    1    判断告警不存在    SDDU通讯中断-${直流配电单元序号}
        ${snmp获取值1}    获取SNMP数据_单个    sDDUCommunicationState${直流配电单元序号}value
        should be equal    ${snmp获取值1}    0
        ${snmp英文名}    获取snmp单个告警英文名    SDDU通讯中断_${直流配电单元序号}
        ${告警消失}    判断Trap告警消失    ${snmp英文名}
        should be true    ${告警消失}
    END
