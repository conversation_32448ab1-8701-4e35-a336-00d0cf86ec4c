*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
1363_0002_V_3.0_1363_通用命令_厂家信息1-6 (自定义)_1
    [Tags]    1
    ${1104数据}    通用命令_获取厂家信息1-6    ${g_prtcl_1363}    <<获取设备（监控模块）厂家信息1-6 (自定义)~CID1=40H>>    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${1104数据}
    ${对比结果}    批量对比数据_1104_WEB    ${1104数据}    ${web数据}    1363_自定义厂家信息_1
    should be true    ${对比结果}

1363_0004_V_3.0_1363_通用命令_厂家信息1-6 (自定义)_2
    [Tags]    1
    ${1104数据}    通用命令_获取厂家信息1-6    ${g_prtcl_1363}    <<获取设备（监控模块）厂家信息1-6 (自定义)~CID1=41H>>    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${1104数据}
    ${对比结果}    批量对比数据_1104_WEB    ${1104数据}    ${web数据}    1363_自定义厂家信息_2
    should be true    ${对比结果}

1363_0006_V_3.0_1363_通用命令_厂家信息1-6 (自定义)_3
    [Tags]    1
    ${1104数据}    通用命令_获取厂家信息1-6    ${g_prtcl_1363}    <<获取设备（监控模块）厂家信息1-6 (自定义)~CID1=42H>>    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${1104数据}
    ${对比结果}    批量对比数据_1104_WEB    ${1104数据}    ${web数据}    1363_自定义厂家信息_3
    should be true    ${对比结果}

1363_0008_V_3.0_1363_通用命令_厂家信息1-6 (自定义)_4
    [Documentation]    CID1=91H为环境模块
    [Tags]    1
    ${1104数据}    通用命令_获取厂家信息1-6    ${g_prtcl_1363}    <<获获取设备（监控模块）厂家信息1-6 (自定义)~CID1=91H>>    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${1104数据}
    ${对比结果}    批量对比数据_1104_WEB    ${1104数据}    ${web数据}    1363_自定义厂家信息_4
    should be true    ${对比结果}

1363_0010_V_3.0_1363_通用命令_厂家信息1-6 (自定义)_5
    [Documentation]    CID1=D0H为PU模块
    [Tags]    1
    [Setup]    #run keywords    控制子工具运行停止    pu    启动    # AND    sleep
    ...    #run keywords | 控制子工具运行停止 | pu | 启动 | # AND | sleep | 10m
    ${1104数据}    通用命令_获取厂家信息1-6    ${g_prtcl_1363}    <<获取设备（监控模块）厂家信息1-6 (自定义)~CID1=D0H>>    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${1104数据}
    ${对比结果}    批量对比数据_1104_WEB    ${1104数据}    ${web数据}    1363_自定义厂家信息_5
    should be true    ${对比结果}
    [Teardown]    #控制子工具运行停止    pu    停止

1363_0012_V_3.0_1363_通用命令_厂家信息1-6 (自定义)_6
    [Tags]    1
    ${1104数据}    通用命令_获取厂家信息1-6    ${g_prtcl_1363}    <<获取设备（监控模块）厂家信息1-6 (自定义)~CID1=46H>>    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${1104数据}
    ${对比结果}    批量对比数据_1104_WEB    ${1104数据}    ${web数据}    1363_自定义厂家信息_6
    should be true    ${对比结果}
