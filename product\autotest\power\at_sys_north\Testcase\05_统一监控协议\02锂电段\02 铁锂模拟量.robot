*** Settings ***
Suite Setup
Test Setup        Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
uniform批量获取FBBMS模拟量
    [Documentation]    21min
    写入CSV文档    FB100B3模拟量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=FBBMS
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除模拟量信号}    ${排除列表}    1    1
    ${协议数据}    统一监控协议_获取数据    锂电池模拟量
    ${协议数据1}    统一监控协议_选择锂电类型对应SID    ${协议数据}    FBBMS
    ${web数据}    统一监控协议_获取web对应数据    ${协议数据1}
    ${sm待测}    uniform_获取1104/1363指定设备指定信号量    ${协议数据1}    ${web数据}    ${列表1}
    Comment    ${指定数据1}    Create Dictionary    signal_name    <<锂电电压-12~0x17001010090001>>    convention    False    device_name    FBBMS    data_type    float    unit    V    sm_name    电池组电压( V )-12
    Comment    @{sm待测}    Create List    ${指定数据1}
    ${待测数据长度}    Get Length    ${sm待测}
    ${缺省值列表}   获取缺省值列表  ${sm待测}    1        sm
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    sm    smartli       只读    ${缺省值列表}    FB100B3模拟量获取测试    锂电池模拟量    
    ${缺省值列表}   获取缺省值列表  ${sm待测}    2        sm
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    sm    smartli       只读    ${缺省值列表}    FB100B3模拟量获取测试    锂电池模拟量    
    ${缺省值列表}   获取缺省值列表  ${sm待测}    0        sm
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    sm    smartli       只读    ${缺省值列表}    FB100B3模拟量获取测试    锂电池模拟量    

uniform批量获取FBBMS数字量
    写入CSV文档    FB100B3数字量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=digital data    INDEX_DEVICE_CH=FBBMS
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除数字量信号}    ${排除列表}    1    1    0    2
    ${协议数据}    统一监控协议_获取数据    锂电池状态量
    ${协议数据1}    统一监控协议_选择锂电类型对应SID    ${协议数据}    FBBMS
    ${web数据}    统一监控协议_获取web对应数据    ${协议数据1}
    ${sm待测}    uniform_获取1104/1363指定设备指定信号量    ${协议数据1}    ${web数据}    ${列表1}
    ${待测数据长度}    Get Length    ${sm待测}
    ${缺省值列表}   获取缺省值列表  ${sm待测}    1        sm
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    sm    smartli      只读    ${缺省值列表}    FB100B3数字量获取测试    锂电池状态量    
    ${缺省值列表}   获取缺省值列表  ${sm待测}    2        sm
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    sm    smartli      只读    ${缺省值列表}    FB100B3数字量获取测试    锂电池状态量   
    ${缺省值列表}   获取缺省值列表  ${sm待测}    0        sm
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    sm    smartli      只读    ${缺省值列表}    FB100B3数字量获取测试    锂电池状态量   

uniform_0002_电池电压
    [Documentation]    不能通过子工具设置
    ...
    ...    可选属性软件适用性 \ \ \ 为空
    [Tags]    3
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        ${获取值}    获取web实时数据    <<电池电压-${锂电序号}~0xb001010010001>>
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    锂电池模拟量    电池电压( V )-${锂电序号}
        should be true    ${获取值}==${uniform_获取值1}
    END

uniform_0004_电池电流
    [Documentation]    不能通过子工具设置
    ...
    ...    可选属性软件适用性 \ \ \ 为空
    [Tags]    3
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        ${获取值}    获取web实时数据    <<电池电流-${锂电序号}~0xb001010030001>>
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    锂电池模拟量    电池电流( A )-${锂电序号}
        should be true    ${获取值}==${uniform_获取值1}
    END

uniform_0006_机内环境温度√
    [Tags]    3
    连接CSU
    #最小值
    设置子工具值    smartli    all    只读    机内环境温度    0
    Comment    @{锂电序号lsit}    Create list    2    8    ${uniform锂电最大数}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    机内环境温度-${锂电序号}    0
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    锂电池模拟量    环境温度-${锂电序号}
        should be true    ${uniform_获取值1}==0
    END
    #最大值
    设置子工具值    smartli    all    只读    机内环境温度    100
    Comment    @{锂电序号lsit}    Create list    2    8    ${uniform锂电最大数}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    机内环境温度-${锂电序号}    100
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    锂电池模拟量    环境温度-${锂电序号}
        should be true    ${uniform_获取值1}==100
    END
    #默认值
    设置子工具值    smartli    all    只读    机内环境温度    50
    Comment    @{锂电序号lsit}    Create list    2    8    ${uniform锂电最大数}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    机内环境温度-${锂电序号}    50
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    锂电池模拟量    环境温度-${锂电序号}
        should be true    ${uniform_获取值1}==50
    END

uniform_0008_电SOC√
    [Tags]    3
    连接CSU
    #最小值
    设置子工具值    smartli    all    只读    电池SOC    0
    Comment    @{锂电序号lsit}    Create list    2    8    ${uniform锂电最大数}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    <<电池SOC-${锂电序号}~0x17001010060001>>    0
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    锂电池模拟量    电池组剩余容量(SOC)-${锂电序号}
        should be true    ${uniform_获取值1}==0
    END
    #最大值
    设置子工具值    smartli    all    只读    电池SOC    100
    Comment    @{锂电序号lsit}    Create list    2    8    ${uniform锂电最大数}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    <<电池SOC-${锂电序号}~0x17001010060001>>    100
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    锂电池模拟量    电池组剩余容量(SOC)-${锂电序号}
        should be true    ${uniform_获取值1}==100
    END
    #默认值
    设置子工具值    smartli    all    只读    电池SOC    2
    Comment    @{锂电序号lsit}    Create list    2    8    ${uniform锂电最大数}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    <<电池SOC-${锂电序号}~0x17001010060001>>    2
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    锂电池模拟量    电池组剩余容量(SOC)-${锂电序号}
        should be true    ${uniform_获取值1}==2
    END

uniform_0010_电池SOH√
    [Tags]    3
    连接CSU
    显示属性配置    电池SOH    数字量    web_attr=On    gui_attr=On
    #最小值
    设置子工具值    smartli    all    只读    电池SOH    0
    Comment    @{锂电序号lsit}    Create list    1    8    ${uniform锂电最大数}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    <<电池SOH-${锂电序号}~0x17001010070001>>    0
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    锂电池模拟量    电池组SOH-${锂电序号}
        should be true    ${uniform_获取值1}==0
    END
    #最大值
    设置子工具值    smartli    all    只读    电池SOH    100
    Comment    @{锂电序号lsit}    Create list    1    8    ${uniform锂电最大数}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    <<电池SOH-${锂电序号}~0x17001010070001>>    100
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    锂电池模拟量    电池组SOH-${锂电序号}
        should be true    ${uniform_获取值1}==100
    END
    #默认值
    设置子工具值    smartli    all    只读    电池SOH    2
    Comment    @{锂电序号lsit}    Create list    1    8    ${uniform锂电最大数}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    <<电池SOH-${锂电序号}~0x17001010070001>>    2
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    锂电池模拟量    电池组SOH-${锂电序号}
        should be true    ${uniform_获取值1}==2
    END
    显示属性配置    电池SOH    数字量    web_attr=Off    gui_attr=Off

uniform_0012_电池累计循环次数
    [Tags]    3
    连接CSU
    显示属性配置    电池累计循环次数    数字量    web_attr=On    gui_attr=On
    #最小值
    设置子工具值    smartli    all    只读    电池累计循环次数    0
    Comment    @{锂电序号lsit}    Create list    1    8    ${uniform锂电最大数}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    电池累计循环次数-${锂电序号}    0
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    锂电池模拟量    电池累计循环次数-${锂电序号}
        should be true    ${uniform_获取值1}==0
    END
    #最大值
    设置子工具值    smartli    all    只读    电池累计循环次数    100000
    Comment    @{锂电序号lsit}    Create list    1    8    ${uniform锂电最大数}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    电池累计循环次数-${锂电序号}    100000
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    锂电池模拟量    电池累计循环次数-${锂电序号}
        should be true    ${uniform_获取值1}==100000
    END
    #默认值
    设置子工具值    smartli    all    只读    电池累计循环次数    2
    Comment    @{锂电序号lsit}    Create list    1    8    ${uniform锂电最大数}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    电池累计循环次数-${锂电序号}    2
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    锂电池模拟量    电池累计循环次数-${锂电序号}
        should be true    ${uniform_获取值1}==2
    END
    显示属性配置    电池累计循环次数    数字量    web_attr=Off    gui_attr=Off

uniform_0014_PACK电压
    [Tags]    3
    连接CSU
    #最小值
    设置子工具值    smartli    all    只读    PACK电压    0
    Comment    @{锂电序号lsit}    Create list    1    8    ${uniform锂电最大数}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    PACK电压-${锂电序号}    0
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    锂电池模拟量    PACK电压-${锂电序号}
        should be true    ${uniform_获取值1}==0
    END
    #最大值
    设置子工具值    smartli    all    只读    PACK电压    60
    Comment    @{锂电序号lsit}    Create list    1    8    ${uniform锂电最大数}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    PACK电压-${锂电序号}    60
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    锂电池模拟量    PACK电压-${锂电序号}
        should be true    ${uniform_获取值1}==60
    END
    #默认值
    设置子工具值    smartli    all    只读    PACK电压    54.4
    Comment    @{锂电序号lsit}    Create list    1    8    ${uniform锂电最大数}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    PACK电压-${锂电序号}    54.4
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    锂电池模拟量    PACK电压-${锂电序号}
        should be true    ${uniform_获取值1}==54.4
    END

uniform_0016_PACK电流
    [Tags]    3
    连接CSU
    #最小值
    设置子工具值    smartli    all    只读    PACK电流    -300
    Comment    @{锂电序号lsit}    Create list    1    8    ${uniform锂电最大数}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    PACK电流-${锂电序号}    -300
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    锂电池模拟量    PACK电流-${锂电序号}
        should be true    ${uniform_获取值1}==-300
    END
    #最大值
    设置子工具值    smartli    all    只读    PACK电流    300
    Comment    @{锂电序号lsit}    Create list    1    8    ${uniform锂电最大数}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    PACK电流-${锂电序号}    300
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    锂电池模拟量    PACK电流-${锂电序号}
        should be true    ${uniform_获取值1}==300
    END
    #默认值
    设置子工具值    smartli    all    只读    PACK电流    2
    Comment    @{锂电序号lsit}    Create list    1    8    ${uniform锂电最大数}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    PACK电流-${锂电序号}    2
        ${uniform_获取值1}    统一监控协议_获取一个数据的值    锂电池模拟量    PACK电流-${锂电序号}
        should be true    ${uniform_获取值1}==2
    END
