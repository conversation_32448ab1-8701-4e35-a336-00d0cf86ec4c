*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0002_批量获取SDDU告警量
    [Documentation]    缺：配电单元次要负载免责下电告警~0x22001030160001
    ...    模拟子工具无该告警量
    ...
    写入CSV文档    SDDU数字量和告警量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=智能直流配电单元
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SDU2排除告警量信号}    ${排除列表}    1    1    0    2
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    智能直流配电单元    alarm    ${SDU2排除告警量信号}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    ${待测数据长度}    Get Length    ${power_sm待测}
    ${number}    Set Variable    1
    设置子工具值    SDU2    all    呼叫    SDU负载分路状态1    1
    设置子工具值    SDU2    all    呼叫    SDU负载分路状态2    1
    设置子工具值    SDU2    all    呼叫    SDU负载分路状态3    1
    设置子工具值    SDU2    all    呼叫    SDU负载分路状态4    1
    FOR    ${直流配电单元序号}    IN RANGE    1    ${智能直流配电单元数量}+1
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    SDDU工作状态-${直流配电单元序号}    正常
    END
    FOR    ${i}    IN    @{power_sm待测}
        Log    正在验证第${number}个数据，一共${待测数据长度}个数据
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${实时告警中的设备名称}    主动告警_组合实时告警中的设备名称    智能直流配电单元    ${信号名称}
        Run Keyword And Continue On Failure    南向RS485子设备告警量产生封装判断结果    power_sm    SDU2    ${信号名称}    呼叫    0    SDDU数字量和告警量获取测试    ${实时告警中的设备名称}    null    null    null    null    null
        Run Keyword And Continue On Failure    南向RS485子设备告警量恢复封装判断结果    power_sm    SDU2    ${信号名称}    呼叫    1    SDDU数字量和告警量获取测试    ${实时告警中的设备名称}    null    null    null    null    null
        ${number}    Evaluate    ${number}+1
    END


SDDU通讯中断
    连接CSU
    ${级别设置值}    获取web参数量    SDDU通讯中断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    SDDU通讯中断    严重
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    SDDU通讯中断-1
    设置子工具个数    SDU2    1
    FOR    ${直流配电单元序号}    IN RANGE    2    ${智能直流配电单元数量}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    SDDU工作状态-${直流配电单元序号}    通讯断
        wait until keyword succeeds    5m    1    判断告警存在    SDDU通讯中断-${直流配电单元序号}
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    SDDU通讯中断-${直流配电单元序号}    ${SSH}
        should be true    ${power_sm告警存在}[0]
    END
    设置子工具个数    SDU2    ${智能直流配电单元数量}
    FOR    ${直流配电单元序号}    IN RANGE    2    ${智能直流配电单元数量}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    SDDU工作状态-${直流配电单元序号}    正常
        wait until keyword succeeds    5m    1    判断告警不存在    SDDU通讯中断-${直流配电单元序号}
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    SDDU通讯中断-${直流配电单元序号}    ${SSH}
        should not be true    ${power_sm告警存在}[0]
    END
    Comment    显示属性配置    SPCU通讯状态    数字量    web_attr=Off    gui_attr=Off
