*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
1363_0002_PU休眠和唤醒
    [Documentation]    休眠状态 默认 否
    [Setup]
    #子设备工具模拟PU
    连接CSU
    #子设备工具模拟
    ###
    @{PU随机list}    从1-15个数中随机选n个不重复的单体    ${北向协议PU最大数}    3
    FOR    ${PU序号}    IN    @{PU随机list}
    #控制设置值为int会报错，必须为str!!!
        ${PU序号}    evaluate    str(${PU序号})
        Comment    ${序号类型}    evaluate    type(${PU序号})
        Comment    ${PU序号1}    evaluate    hex(${PU序号})
        Comment    ${PU序号2}    evaluate    '{:02X}'.format(${PU序号1})
        Wait Until Keyword Succeeds    10X    2    设置1104PU遥控数据    ${g_prtcl_1363}    PU模块唤醒    ${PU序号}    ${g_ver_1363}
        Wait Until Keyword Succeeds    2m    2    信号量数据值为    PU休眠状态-${PU序号}    否
    END
    FOR    ${PU序号}    IN    @{PU随机list}
        ${PU序号}    evaluate    str(${PU序号})
        Comment    ${PU序号1}    evaluate    hex(${PU序号})
        Comment    ${PU序号2}    evaluate    '{:02X}'.format(${PU序号1})
        Wait Until Keyword Succeeds    10X    2    设置1104PU遥控数据    ${g_prtcl_1363}    PU模块休眠    ${PU序号}    ${g_ver_1363}
        Wait Until Keyword Succeeds    2m    2    信号量数据值为    PU休眠状态-${PU序号}    是
    END

1363_0004_PU风扇调速允许和禁止
    [Documentation]    禁止0
    ...    允许1
    ...
    ...    状态：
    ...    自动0
    ...    全速1
    [Setup]
    #子设备工具模拟PU
    连接CSU
    @{PU随机list}    从1-15个数中随机选n个不重复的单体    ${北向协议PU最大数}    3
    FOR    ${PU序号}    IN    @{PU随机list}
        ${PU序号}    evaluate    str(${PU序号})
        Comment    ${PU序号1}    evaluate    hex(${PU序号})
        Comment    ${PU序号2}    evaluate    '{:02X}'.format(${PU序号1})
        Wait Until Keyword Succeeds    10X    2    设置1104PU遥控数据    ${g_prtcl_1363}    PU模块风扇调速允许    ${PU序号}    ${g_ver_1363}
        Wait Until Keyword Succeeds    2m    2    信号量数据值为    PU风扇控制状态-${PU序号}    自动
    END
    FOR    ${PU序号}    IN    @{PU随机list}
        ${PU序号}    evaluate    str(${PU序号})
        Comment    ${PU序号1}    evaluate    hex(${PU序号})
        Comment    ${PU序号2}    evaluate    '{:02X}'.format(${PU序号1})
        Wait Until Keyword Succeeds    10X    2    设置1104PU遥控数据    ${g_prtcl_1363}    PU模块风扇调速禁止    ${PU序号}    ${g_ver_1363}
        Wait Until Keyword Succeeds    2m    2    信号量数据值为    PU风扇控制状态-${PU序号}    全速
    END
