*** Settings ***
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
批量获取SDU2数字量测试
    写入CSV文档    SDU2数字量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=digital data    INDEX_DEVICE_CH=智能直流配电单元
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SDU2排除数字量信号}    ${排除列表}
    ${缺省值列表}   获取缺省值列表  ${列表1}    1
	Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    SDU2   呼叫    ${缺省值列表}    2    SDU2数字量获取测试
    ${缺省值列表}   获取缺省值列表  ${列表1}    2
	Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    SDU2    呼叫    ${缺省值列表}    2    SDU2数字量获取测试
    ${缺省值列表}   获取缺省值列表  ${列表1}    0
	Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    SDU2    呼叫    ${缺省值列表}    2    SDU2数字量获取测试