*** Settings ***
Suite Setup       设置历史记录最大条数    history_alarm    0    100
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
WEB导出历史告警测试
    连接CSU
    ${数据条数1}    ${第一条数据内容1}    ${最新一条数据内容1}    查询指定历史记录    所有历史告警
    ${导出路径}    导出指定历史记录    所有历史告警    ${datapassword}
    ${数据条数2}    ${第一条数据内容2}    ${最新一条数据内容2}    解析指定历史记录文件    所有历史告警    ${导出路径}
    should be equal    ${第一条数据内容1}[0]    ${第一条数据内容2}[3]
    should be equal    ${第一条数据内容1}[1]    ${第一条数据内容2}[4]
    should contain    ${第一条数据内容1}[2]    ${第一条数据内容2}[2]
    should be equal    ${数据条数1}    ${数据条数2}
    should be equal    ${最新一条数据内容1}[0]    ${最新一条数据内容2}[3]
    should be equal    ${最新一条数据内容1}[1]    ${最新一条数据内容2}[4]
    should contain    ${最新一条数据内容1}[2]    ${最新一条数据内容2}[2]

中文界面下WEB导出历史告警测试
    连接CSU
    设置启用设备    smr    10    3
    控制子工具运行停止    smr    启动
    电池管理初始化
    sleep    1m
    ${历史告警数量}    获取web历史告警数量    ${empty}    ${empty}
    run keyword if    ${历史告警数量}>1900    删除历史记录    历史告警
    Wait Until Keyword Succeeds    2m    2    设置web参数量    均充使能    允许
    ${起始时间}    获取系统时间
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池均充    严重
    ${告警级别}    获取web参数量    电池均充
    Wait Until Keyword Succeeds    2m    2    设置web控制量    启动均充
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    电池均充
    ${终止时间1}    获取系统时间
    ${历史告警数量1}    获取web历史告警数量    ${起始时间}    ${终止时间1}
    log    ${历史告警数量1}
    should be true    ${历史告警数量1}==0
    #产生电池均充历史告警
    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息不为    电池均充
    sleep    3m
    ${终止时间2}    获取系统时间
    ${历史告警数量2}    获取web历史告警数量    ${起始时间}    ${终止时间2}
    should be true    ${历史告警数量2}==1
    @{历史告警内容}    Web关键字_V30.获取web历史告警内容    ${起始时间}    ${终止时间2}    1    10
    should contain    ${历史告警内容}[-1]    电池均充
    @{历史告警内容}    split string    ${历史告警内容}[-1]    ,
    should contain    ${历史告警内容}[5]    电池均充
    should be equal    ${历史告警内容}[3]    ${告警级别}
    ${数据条数1}    ${第一条数据内容1}    ${最新一条数据内容1}    查询指定历史记录    所有历史告警
    ${导出路径}    导出指定历史记录    所有历史告警    ${datapassword}
    # ${记录列表}    读CSV文档    ${测试用例地址}\\file\\download\\${导出路径}\\history_alarm.csv
    ${记录列表}    读CSV文档    ${测试用例地址}/file/download/${导出路径}/history_alarm.csv
    log    ${记录列表}[0]
    @{记录列表new}    split string    ${记录列表}[0]    ,
    should be equal    ${记录列表new}[0]    信号量ID(sid)
    should be equal    ${记录列表new}[1]    设备名称(device_name)
    should be equal    ${记录列表new}[2]    信号量名称(signal_name)
    ${数据条数2}    ${第一条数据内容2}    ${最新一条数据内容2}    解析指定历史记录文件    所有历史告警    ${导出路径}
    should be equal    ${最新一条数据内容2}[2]    电池均充

英文界面下WEB导出历史告警测试
    连接CSU
    设置启用设备    smr    10    3
    控制子工具运行停止    smr    启动
    电池管理初始化
    sleep    1m
    ${历史告警数量}    获取web历史告警数量    ${empty}    ${empty}
    run keyword if    ${历史告警数量}>1900    删除历史记录    历史告警
    Wait Until Keyword Succeeds    2m    2    设置web参数量    均充使能    允许
    ${起始时间}    获取系统时间
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池均充    严重
    ${告警级别}    获取web参数量    电池均充
    Wait Until Keyword Succeeds    2m    2    设置web控制量    启动均充
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    电池均充
    ${终止时间1}    获取系统时间
    ${历史告警数量1}    获取web历史告警数量    ${起始时间}    ${终止时间1}
    log    ${历史告警数量1}
    should be true    ${历史告警数量1}==0
    #产生电池均充历史告警
    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息不为    电池均充
    sleep    3
    ${终止时间2}    获取系统时间
    ${历史告警数量2}    获取web历史告警数量    ${起始时间}    ${终止时间2}
    should be true    ${历史告警数量2}==1
    @{历史告警内容}    Web关键字_V30.获取web历史告警内容    ${起始时间}    ${终止时间2}    1    10
    should contain    ${历史告警内容}[-1]    电池均充
    @{历史告警内容}    split string    ${历史告警内容}[-1]    ,
    should contain    ${历史告警内容}[5]    电池均充
    should be equal    ${历史告警内容}[3]    ${告警级别}
    设置语言    EN
    连接CSU
    ${数据条数1}    ${第一条数据内容1}    ${最新一条数据内容1}    查询指定历史记录    所有历史告警
    ${导出路径}    导出指定历史记录    所有历史告警    ${datapassword}
    # ${记录列表}    读CSV文档    ${测试用例地址}\\file\\download\\${导出路径}\\history_alarm.csv
    ${记录列表}    读CSV文档    ${测试用例地址}/file/download/${导出路径}/history_alarm.csv
    log    ${记录列表}[0]
    @{记录列表new}    split string    ${记录列表}[0]    ,
    should be equal    ${记录列表new}[0]    sid
    should be equal    ${记录列表new}[1]    device_name
    should be equal    ${记录列表new}[2]    signal_name
    ${数据条数2}    ${第一条数据内容2}    ${最新一条数据内容2}    解析指定历史记录文件    所有历史告警    ${导出路径}
    should be equal    ${最新一条数据内容2}[2]    Battery Equalized Charge
    [Teardown]    run keywords    设置语言    CN
    ...    AND    连接CSU
