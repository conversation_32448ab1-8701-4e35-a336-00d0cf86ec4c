#!/usr/bin/env python2
# -*- coding: UTF-8 -*-
#####################################################################################
# File Name:    TDS_3034C.py
# Discription:  实现与示波器TDS 3034C的连接和数据读写
# Author:   Pengy；
# DevEnv:   winXP/7x86+python2.7
# Notes:               
# Log:      20171220Pengy 
#####################################################################################

#import serial  # 串口库
import socket  # 网络编程，socket(套接字）
import struct  # pack unpack
import time    # 时间库
import os
from struct import *
import req

####读取文件地址，使用RIDE时，改为ADD[1]；使用本地编译时改为ADD[0]####
ADD={0:'..\.',
     1:''
    }
add=ADD[0]

PROBE_SETUP='1000' #高压探头放大倍率设置

class TDS_3034C():
    def __init__(self):
        os.chdir(os.path.split(os.path.realpath(__file__))[0])#回到当前脚本目录

    
#创建测试文档=====================================
    def CreateTestDoc(self,name,headerList):
        str1 = ','.join(headerList)
        try:
            savef=open(add+r'.\TestRecord\\' +name+'.csv','a')
            print>>savef,str1.encode('gbk')
            savef.close()
        except:
            print u'打开文件出错！'
            savef.close()

#向测试文档写入内容=====================================
    def WriteTestDoc(self,name,ValueList):
        str1=''
        for i in ValueList:
##            print i,type(i),str(i)
            if type(i)==unicode:
                str1 =str1+ str(i.encode('gbk'))+','
            else:
                str1 =str1+ str(i)+','
##                print str1
        try:
            savef=open(add+r'.\TestRecord\\' +name+'.csv','a')
            print>>savef,str1
            savef.close()
        except:
            print u'打开文件出错！'
            savef.close()
#创建纹波测试文档========================================
    def config(self):
        try:
##            savef=open(add+r'.\python\WT3000\WT3000_eff.csv','a')
            print u'========创建纹波测试文档=========='
            savef=open(add+r'.\TestRecord\RippleTest.csv','a')
            effTmpStr=u'输入电压（V）,输出电压（V）,输出电流（A）,峰峰值（V）,有效值（V）,峰峰值纹波系数（%）,有效值纹波系数（%）,测试结论'
            print>>savef,effTmpStr.encode('gbk')
            savef.close()
        except:
            print u'打开文件出错！'
            savef.close()
#创建电压过冲测试文档========================================
    def CreateOverShootTestDoc(self):
        try:
            print u'========创建电压过冲测试文档=========='
            savef=open(add+r'.\TestRecord'+u'\电压过冲测试.csv','a')
            effTmpStr=u'输入电压（V）,输出电压（V）,输出电流（A）,过冲状态,过冲值（%）,测试结论'
            print>>savef,effTmpStr.encode('gbk')
            savef.close()
        except:
            print u'打开文件出错！'
            savef.close()

    def Connect_Oscilloscope(self,ip):
        """""""""""""""""""""""
        示波器连接检测
        """""""""""""""""""""""
        print u'##########示波器连接检测############'
        recStr=self.Communicate_TDS(ip,u'ID','Noprint')
        if str(recStr)=='ID TEK/TDS 3034C,CF:91.1CT,FV:v4.05 TDS3FFT:v1.00 TDS3TRG:v1.00':
            print u'与示波器连接正常！'
        else:
##            print u'与示波器连接失败！'
            return False
        print u'####################################'

    def VoltageOverShootTestSet(self,ip,XSCALE='1',YSCALE='200'):
        """""""""""""""""""""""
        电压过冲测试设置
        XSCALE:横坐标分辨率，单位s
        YSCALE纵坐标分辨率，单位V或者A
        TrigLev:触发高度，单位V或者A
        MaxCur：返回测量的上升时间
        """""""""""""""""""""""
        cnt=0
        print u'##########电压过冲测试设置############'
        self.Communicate_TDS(ip,u'波形自动运行','no')
        self.Communicate_TDS(ip,u'波形采集','no')
        self.Communicate_TDS(ip,u'打开CH1通道','no')
        self.Communicate_TDS(ip,u'设置CH1为DC模式','no') #设置为直流耦合模式
        self.ParameterSet(ip,u'设置分辨率',XSCALE,'no')
        self.ParameterSet(ip,u'设置CH1精细标度',YSCALE,'no')
        self.ParameterSet(ip,u'设置CH1位置','-3','no')
        self.ParameterSet(ip,u'设置水平触发延迟状态','OFF','no')
        self.ParameterSet(ip,u'设置水平触发位置','50','no')
        
        self.Communicate_TDS(ip,u'设置测量通道1为CH1','no')
        self.ParameterSet(ip,u'设置测量通道1的类型','NOVershoot','no')
        self.ParameterSet(ip,u'设置测量通道1的显示状态','ON','no') #要显示到屏幕上才能读数据

        self.Communicate_TDS(ip,u'设置测量通道2为CH1','no')
        self.ParameterSet(ip,u'设置测量通道2的类型','POVershoot','no')
        self.ParameterSet(ip,u'设置测量通道2的显示状态','ON','no')
        
        self.Communicate_TDS(ip,u'设置CH1探头为电压型','no')
        self.Communicate_TDS(ip,u'设置CH1为全带宽','no')
        self.ParameterSet(ip,u'设置CH1放大倍数',PROBE_SETUP,'no')
        self.Communicate_TDS(ip,u'波形单次运行','no')
        

    def VoltagePOverShootTest(self,ip,XSCALE='1',Vin='380',Vout='750',Cout='20',OSState=u'开机',rule='5'):
        """""""""""""""""""""""
        电压正过冲测试
        XSCALE:横坐标分辨率，单位s
        OverShootVal：返回测量的电压过冲值
        """""""""""""""""""""""
        print u'##########电压正过冲测试############'
        cnt=0        
        while( self.Communicate_TDS(ip,u'波形采集状态查询','no')=='1'):#等波形暂停了，再读数据
            time.sleep(1)
            cnt+=1
            if cnt>10*int(XSCALE):
                cnt=0
                print 'time out!'
                break
        OverShootVal=float(self.Communicate_TDS(ip,u'查询测量通道2的值','no'))
        print u'电压正过冲为：',OverShootVal
        if OverShootVal>float(rule):
            result='Fail'
        else:
            result='Pass'            
            
        try:
            savef=open(add+r'.\TestRecord'+u'\电压过冲测试.csv','a')
            effTmpStr=Vin+','+Vout+','+Cout+','+OSState+','+str(OverShootVal)+','+result+','
            print>>savef,effTmpStr.encode('gbk')
            savef.close()
        except:
            print u'打开文件出错！'
            savef.close()        
        return OverShootVal

    def VoltageNOverShootTest(self,ip,XSCALE='1',Vin='380',Vout='750',Cout='20',OSState=u'关机',rule='5'):
        """""""""""""""""""""""
        电压负过冲测试
        XSCALE:横坐标分辨率，单位s
        OverShootVal：返回测量的电压过冲值
        """""""""""""""""""""""
        print u'##########电压负过冲测试############'
        cnt=0        
        while( self.Communicate_TDS(ip,u'波形采集状态查询','no')=='1'):#等波形暂停了，再读数据
            time.sleep(1)
            cnt+=1            
            if cnt>10*int(XSCALE):
                cnt=0
                print 'time out!'
                break
        OverShootVal=float(self.Communicate_TDS(ip,u'查询测量通道1的值','no'))
        print u'电压负过冲为：',OverShootVal
        if OverShootVal>float(rule):
            result='Fail'
        else:
            result='Pass'            
            
        try:
            savef=open(add+r'.\TestRecord'+u'\电压过冲测试.csv','a')
            effTmpStr=Vin+','+Vout+','+Cout+','+OSState+','+str(OverShootVal)+','+result+','
            print>>savef,effTmpStr.encode('gbk')
            savef.close()
        except:
            print u'打开文件出错！'
            savef.close()        
        return OverShootVal
##电压过冲测试结果判断=============================================
    def VoltageOverShootTestJudge(self):
        """""""""""""""""""""""""""""""""""""""
        读取电压过冲测试数据文档，根据通过准则判断测试结果
        """""""""""""""""""""""""""""""""""""""
        f = open(add+r'.\TestRecord'+u'\电压过冲测试.csv','r')
        csvlist = []
        tmplist=[]
        savelist=[]
        csvlist = f.readlines()
        f.close()
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
##            print tmplist
            if tmplist[5].strip('\n')=='Fail':
                savelist.append(tmplist)

        if savelist!=[]:
            print u'有',len(savelist),u'条测试不通过'
            print savelist
            return False
        else:
            print u'测试通过！'
            return True

    def InrushCurrentTestSet(self,ip,XSCALE='1',YSCALE='20'):
        """""""""""""""""""""""
        启动冲击电流测试：将电流探头接入CH2进行测试
        XSCALE:横坐标分辨率，单位s
        YSCALE纵坐标分辨率，单位V或者A
        TrigLev:触发高度，单位V或者A
        MaxCur：返回测量的上升时间
        """""""""""""""""""""""
        print u'############冲击电流测试设置##############'  
        self.Communicate_TDS(ip,u'波形自动运行','no')
        self.Communicate_TDS(ip,u'波形采集','no')
        self.Communicate_TDS(ip,u'打开CH2通道','no')
        self.Communicate_TDS(ip,u'设置CH2为DC模式','no') #设置为直流耦合模式
        self.ParameterSet(ip,u'设置分辨率',XSCALE,'no')
        self.ParameterSet(ip,u'设置CH2精细标度',YSCALE,'no')
        self.ParameterSet(ip,u'设置CH2位置','0','no')
        self.ParameterSet(ip,u'设置水平触发延迟状态','OFF','no')
        self.ParameterSet(ip,u'设置水平触发位置','50','no')
        self.Communicate_TDS(ip,u'设置测量通道1为CH2','no')
        self.ParameterSet(ip,u'设置测量通道1的类型','MAXimum','no')
        self.ParameterSet(ip,u'设置测量通道1的显示状态','ON','no') #要显示到屏幕上才能读数据
        self.Communicate_TDS(ip,u'设置CH2探头为电流型','no')
        self.Communicate_TDS(ip,u'设置CH2为20MHz','no')
        self.ParameterSet(ip,u'设置CH2放大倍数','100','no')
        self.Communicate_TDS(ip,u'波形单次运行','no')

    def InrushCurrentTest(self,ip,XSCALE='1'):
        cnt=0
        print u'##########启动冲击电流测试############'        
        while( self.Communicate_TDS(ip,u'波形采集状态查询','no')=='1'):#等波形暂停了，再读数据
            time.sleep(1)
            cnt+=1
            MaxCur=float(self.Communicate_TDS(ip,u'查询测量通道1的值','no'))
            if cnt>10*int(XSCALE):
                cnt=0
                print 'time out!'
                break

        print u'最大电流值为：',MaxCur
        
        return MaxCur

    def RatedCurrentTest(self,ip,XSCALE='1',YSCALE='20'):#额定电流测试
        cnt=0
        print u'##########额定电流测试############'
        self.Communicate_TDS(ip,u'波形自动运行','no')
        self.Communicate_TDS(ip,u'波形采集','no')
        self.Communicate_TDS(ip,u'打开CH2通道','no')
        self.Communicate_TDS(ip,u'设置CH2为DC模式','no') #设置为直流耦合模式
        self.ParameterSet(ip,u'设置分辨率',XSCALE,'no')
        self.ParameterSet(ip,u'设置CH2精细标度',YSCALE,'no')
        self.ParameterSet(ip,u'设置CH2位置','0','no')
        self.ParameterSet(ip,u'设置水平触发延迟状态','OFF','no')
        self.ParameterSet(ip,u'设置水平触发位置','50','no')
        self.Communicate_TDS(ip,u'设置测量通道1为CH2','no')
        self.ParameterSet(ip,u'设置测量通道1的类型','MAXimum','no')
        self.ParameterSet(ip,u'设置测量通道1的显示状态','ON','no') #要显示到屏幕上才能读数据
        self.Communicate_TDS(ip,u'设置CH2探头为电流型','no')
        self.Communicate_TDS(ip,u'设置CH2为20MHz','no')
        self.ParameterSet(ip,u'设置CH2放大倍数','100','no')
        self.Communicate_TDS(ip,u'波形单次运行','no')
        while( self.Communicate_TDS(ip,u'波形采集状态查询','no')=='1'):#等波形暂停了，再读数据
            time.sleep(1)
            cnt+=1
            RatedCur=float(self.Communicate_TDS(ip,u'查询测量通道1的值','no'))
            if cnt>10*int(XSCALE):
                cnt=0
                print 'time out!'
                break

        print u'额定电流值为：',RatedCur
        
        return RatedCur

    def RiseTimeTestSet(self,ip,XSCALE='0.0000001',YSCALE='0.2',TrigLev='0.4'):
        """""""""""""""""""""""
        上升时间测试前对示波器进行设置
        XSCALE:横坐标分辨率，单位s
        YSCALE纵坐标分辨率，单位V或者A
        TrigLev:触发高度，单位V或者A
        risetime：返回测量的上升时间
        """""""""""""""""""""""
        print u'##########上升时间测试设置############'
        self.Communicate_TDS(ip,u'设置测量低参考电平为0','no')#测试上升时间是，低参考电平应该设置为0%
        self.Communicate_TDS(ip,u'波形自动运行','no')
        self.Communicate_TDS(ip,u'波形采集','no')
        self.Communicate_TDS(ip,u'打开CH1通道','no')
        self.Communicate_TDS(ip,u'设置CH1为DC模式','no') #设置为直流耦合模式
        self.ParameterSet(ip,u'设置分辨率',XSCALE,'no')
        self.ParameterSet(ip,u'设置CH1精细标度',YSCALE,'no')
        self.ParameterSet(ip,u'设置CH1位置','-3','no')
        self.ParameterSet(ip,u'设置水平触发延迟状态','OFF','no')
        self.ParameterSet(ip,u'设置水平触发位置','80','no')
        self.Communicate_TDS(ip,u'触发源CH1','no')
        self.ParameterSet(ip,u'设置触发高度',TrigLev,'no')
        self.Communicate_TDS(ip,u'设置测量通道1为CH1','no')
        self.ParameterSet(ip,u'设置测量通道1的类型','RISe','no')
        self.ParameterSet(ip,u'设置测量通道1的显示状态','ON','no') #要显示到屏幕上才能读数据
        self.Communicate_TDS(ip,u'设置CH1探头为电压型','no')
        self.Communicate_TDS(ip,u'设置CH1为全带宽','no')
        self.ParameterSet(ip,u'设置CH1放大倍数',PROBE_SETUP,'no')
        self.Communicate_TDS(ip,u'波形单次运行','no')

    
    def RiseTimeTest(self,ip):
        """""""""""""""""""""""
        上升时间测试：执行该函数前，需要先运行RiseTimeTestSet()
        risetime：返回测量的上升时间
        """""""""""""""""""""""
        cnt=0
        print u'##########上升时间测试############'
        
        while( self.Communicate_TDS(ip,u'波形采集状态查询','no')=='1'):#等波形暂停了，再读数据
            time.sleep(1)
            cnt+=1
            risetime=float(self.Communicate_TDS(ip,u'查询测量通道1的值','no'))
            if cnt>10:
                cnt=0
                print 'time out!'
                break
            
        if risetime<1e-6 and risetime>=1e-9 :
            risetimestr=str(risetime*1e9)+'ns'
        elif risetime<1e-3 and risetime>=1e-6 :
            risetimestr=str(risetime*1e6)+'us'
        elif risetime<1 and risetime>=1e-3 :
            risetimestr=str(risetime*1e3)+'ms'
        elif risetime>=1 :
            risetimestr=str(risetime)+'s'
        print u'上升时间为：',risetimestr
        
        return risetime
            
    def MaxVolTest(self,ip,step='0.1',WaitingTime='5',XSCALE='0.0001',YSCALE='0.4'):
        """""""""""""""""""""""
        最大电压值测试
        将探头接入CH1进行测量
        step:每次调节的步长，单位V或者A
        WaitingTime:等待触发时间,单位s
        XSCALE:横坐标分辨率，单位s
        YSCALE纵坐标分辨率，单位V或者A
        
        """""""""""""""""""""""
        print u'##########最大电压值测试############'
        self.Communicate_TDS(ip,u'波形自动运行','no')
        if self.Communicate_TDS(ip,u'波形采集状态查询','no')=='0':
            self.Communicate_TDS(ip,u'波形采集','no')           
        self.Communicate_TDS(ip,u'打开CH1通道','no')        
        self.Communicate_TDS(ip,u'触发源CH1','no')
        self.Communicate_TDS(ip,u'设置触发高度50%','no')
        self.Communicate_TDS(ip,u'正常触发','no')
        self.ParameterSet(ip,u'设置分辨率',XSCALE,'no')
        self.ParameterSet(ip,u'设置CH1精细标度',YSCALE,'no')
        self.ParameterSet(ip,u'设置水平触发位置','50','no')
        self.ParameterSet(ip,u'设置水平触发延迟状态','OFF','no')
        self.ParameterSet(ip,u'设置CH1位置','-3','no')
        self.Communicate_TDS(ip,u'设置测量通道1为CH1','no')
        self.ParameterSet(ip,u'设置测量通道1的类型','MAXimum','no')
        self.ParameterSet(ip,u'设置测量通道1的显示状态','ON','no') #要显示到屏幕上才能读数据
        self.Communicate_TDS(ip,u'设置CH1探头为电压型','no')
        self.Communicate_TDS(ip,u'设置CH1为DC模式','no') #设置为直流耦合模式
        self.Communicate_TDS(ip,u'设置CH1为150MHz','no')
        self.ParameterSet(ip,u'设置CH1放大倍数',PROBE_SETUP,'no')
        while(1):
            cnt=0
            exitFlag=0
            high=self.Communicate_TDS(ip,u'查询触发高度','no')
##            print 't1',high,str(float(high)+float(step))
            self.ParameterSet(ip,u'设置触发高度',str(float(high)+float(step)),'no')
            time.sleep(1)
            if(self.Communicate_TDS(ip,u'触发状态查询','no')=='READY'):

                time.sleep(2)
                while(1):
                    if(self.Communicate_TDS(ip,u'触发状态查询','no')=='READY'):
                        cnt=cnt+1
                        time.sleep(1)
                    elif(self.Communicate_TDS(ip,u'触发状态查询','no')=='TRIG'):
                        break
                    if cnt>int(WaitingTime):  #滤波深度                                 
                        print u'触发高度为：',float(self.Communicate_TDS(ip,u'查询触发高度','no'))
                        exitFlag=1
                        break
            if exitFlag==1:
                break
        time.sleep(1)
        maxValue=str(float(self.Communicate_TDS(ip,u'查询测量通道1的值','no')))
        
        print u'最大值为：',maxValue+(self.Communicate_TDS(ip,u'查询测量通道1的单位','no')[1:2])

        return maxValue
            

    def MaxCurTest(self,ip,step='0.1',WaitingTime='5',XSCALE='0.0001',YSCALE='1'):
        """""""""""""""""""""""
        最大电流值测试
        将探头接入CH1进行测量
        step:每次调节的步长
        WaitingTime:等待触发时间,单位s
        XSCALE:横坐标分辨率，单位s
        YSCALE纵坐标分辨率，单位V或者A
        
        """""""""""""""""""""""
        print u'##########最大电流值测试############'
        self.Communicate_TDS(ip,u'波形自动运行','no')
        if self.Communicate_TDS(ip,u'波形采集状态查询','no')=='0':
            self.Communicate_TDS(ip,u'波形采集','no')           
        self.Communicate_TDS(ip,u'打开CH1通道','no')        
        self.Communicate_TDS(ip,u'触发源CH1','no')
        self.Communicate_TDS(ip,u'设置触发高度50%','no')
        self.Communicate_TDS(ip,u'正常触发','no')
        self.ParameterSet(ip,u'设置分辨率',XSCALE,'no')
        self.ParameterSet(ip,u'设置CH1精细标度',YSCALE,'no')
        self.ParameterSet(ip,u'设置水平触发位置','50','no')
        self.ParameterSet(ip,u'设置水平触发延迟状态','OFF','no')
        self.ParameterSet(ip,u'设置CH1位置','-3','no')
        self.Communicate_TDS(ip,u'设置测量通道1为CH1','no')
        self.ParameterSet(ip,u'设置测量通道1的类型','MAXimum','no')
        self.ParameterSet(ip,u'设置测量通道1的显示状态','ON','no') #要显示到屏幕上才能读数据
        self.Communicate_TDS(ip,u'设置CH1探头为电流型','no')
        self.Communicate_TDS(ip,u'设置CH1为DC模式','no') #设置为直流耦合模式
        self.Communicate_TDS(ip,u'设置CH1为150MHz','no')
        self.ParameterSet(ip,u'设置CH1放大倍数',PROBE_SETUP,'no')
        while(1):
            cnt=0
            exitFlag=0
            high=self.Communicate_TDS(ip,u'查询触发高度','no')
##            print 't1',high,float(high)
            self.ParameterSet(ip,u'设置触发高度',str(float(high)+float(step)),'no')
            time.sleep(1)
            if(self.Communicate_TDS(ip,u'触发状态查询','no')=='READY'):
##               and time.sleep==5):
                time.sleep(2)
                while(1):
                    if(self.Communicate_TDS(ip,u'触发状态查询','no')=='READY'):
                        cnt=cnt+1
                        time.sleep(1)
                    elif(self.Communicate_TDS(ip,u'触发状态查询','no')=='TRIG'):
                        break
                    if cnt>int(WaitingTime):  #滤波深度                                 
                        print u'触发高度为：',float(self.Communicate_TDS(ip,u'查询触发高度','no'))
                        exitFlag=1
                        break
            if exitFlag==1:
                break
        time.sleep(1)            
        maxValue=str(float(self.Communicate_TDS(ip,u'查询测量通道1的值','no')))
        
        print u'最大值为：',maxValue+(self.Communicate_TDS(ip,u'查询测量通道1的单位','no')[1:2])

        return maxValue

    def RippleTest(self,ip,InPutVolt='380',OutPutVolt='750',OutPutCur='33',WaitingTime='8',YSCALE='1'):
        """""""""""""""""""""""
        纹波最大值测试
        将探头接入CH1进行测量
        WaitingTime:读取时间,单位s
        """""""""""""""""""""""
        print u'##########纹波最大值测试############'
        print u'输入电压：',InPutVolt,u'输出电压',OutPutVolt,u'输出电流',OutPutCur
        cnt=0
        pkList=[]
        RMSList=[]
        self.Communicate_TDS(ip,u'波形自动运行','no')
        if self.Communicate_TDS(ip,u'波形采集状态查询','no')=='0':
            self.Communicate_TDS(ip,u'波形采集','no')           
        self.Communicate_TDS(ip,u'打开CH1通道','no')        
        self.Communicate_TDS(ip,u'触发源CH1','no')
        self.Communicate_TDS(ip,u'设置触发高度50%','no')
        self.Communicate_TDS(ip,u'自动触发','no')
        self.ParameterSet(ip,u'设置水平触发延迟状态','OFF','no')
        self.ParameterSet(ip,u'设置CH1位置','0','no')
        self.Communicate_TDS(ip,u'设置CH1为20MHz','no')
        self.Communicate_TDS(ip,u'设置CH1探头为电压型','no')
        self.Communicate_TDS(ip,u'设置测量通道1为CH1','no')
        self.ParameterSet(ip,u'设置测量通道1的类型','PK2PK','no')
        self.ParameterSet(ip,u'设置测量通道1的显示状态','ON','no') #要显示到屏幕上才能读数据
        self.Communicate_TDS(ip,u'设置测量通道2为CH1','no')
        self.ParameterSet(ip,u'设置测量通道2的类型','RMS','no')
        self.ParameterSet(ip,u'设置测量通道2的显示状态','ON','no')
        
        self.Communicate_TDS(ip,u'设置CH1为AC模式','no') #设置为交流耦合模式
        self.ParameterSet(ip,u'设置分辨率','0.4','no')   #设置400ms/格
        self.ParameterSet(ip,u'设置CH1精细标度',YSCALE,'no') #设置1V/格
        self.ParameterSet(ip,u'设置CH1放大倍数',PROBE_SETUP,'no')
        while(1):
            pk=float(self.Communicate_TDS(ip,u'查询测量通道1的值','no'))
            RMS=float(self.Communicate_TDS(ip,u'查询测量通道2的值','no'))
            pkList.append(pk)
            RMSList.append(RMS)
            cnt=cnt+1
            time.sleep(0.5)
            if cnt >int(WaitingTime)-1:
                break
        PKCoefficient =round(max(pkList)/float(OutPutVolt)*100,2)#round函数为四舍五入
        RMSCoefficient =round(max(RMSList)/float(OutPutVolt)*100,2)#纹波系数
        print u'峰峰值:',max(pkList),'V',u'峰峰值纹波系数：',PKCoefficient
        print u'有效值:',max(RMSList),'V',u'有效值纹波系数：',RMSCoefficient

        if PKCoefficient>1 or RMSCoefficient>0.5:
            Result='fail'
        else:
            Result='Pass'
        
        try:
            savef=open(add+r'.\TestRecord\RippleTest.csv','a')
            effTmpStr=str(InPutVolt)+','+str(OutPutVolt)+','+str(OutPutCur)+','+str(max(pkList))+','+str(max(RMSList))+','+str(PKCoefficient)+','+str(RMSCoefficient)+','+Result+','
            print>>savef,effTmpStr.encode('gbk')
            savef.close()
        except:
            print u'打开文件出错！'
            savef.close()

        return str(PKCoefficient),str(RMSCoefficient)
##纹波测试结果判断=============================================
    def RippleJudge(self):
        """""""""""""""""""""""""""""""""""""""
        读取纹波测试数据文档，根据通过准则判断测试结果
        """""""""""""""""""""""""""""""""""""""
        f = open( add+r'.\TestRecord\RippleTest.csv', 'r')
        csvlist = []
        tmplist=[]
        addlist=[]
        savelist=[]
        calclist=[]
        outStr=''
        failtag=0
        csvlist = f.readlines()
        f.close()
        
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
##            print tmplist
            if tmplist[7].strip('\n')=='fail':
                savelist.append(tmplist)

        if savelist!=[]:
            print u'有',len(savelist),u'条测试不通过'
            print savelist
            return False
        else:
            print u'测试通过！'
            return True

##########整流器、HVDC缓启动时间测试######################
    def SlowStratTimeTestSet(self,ip):
        """""""""""""""""""""""
        缓启动时间测试前对示波器进行设置
        """""""""""""""""""""""
        print u'##########缓启动时间测试设置############'
        self.Communicate_TDS(ip,u'设置测量低参考电平为0','no')#测试上升时间是，低参考电平应该设置为0%
        self.Communicate_TDS(ip,u'波形自动运行','no')
        self.Communicate_TDS(ip,u'波形采集','no')
        self.Communicate_TDS(ip,u'打开CH1通道','no')
        self.Communicate_TDS(ip,u'打开CH2通道','no')
        self.Communicate_TDS(ip,u'设置CH1为DC模式','no') #设置为直流耦合模式
        self.Communicate_TDS(ip,u'设置CH2为DC模式','no') #设置为直流耦合模式
        self.Communicate_TDS(ip,u'设置CH1探头为电压型','no')
        self.Communicate_TDS(ip,u'设置CH2探头为电流型','no')
        self.ParameterSet(ip,u'设置分辨率','2','no')
        self.ParameterSet(ip,u'设置CH1精细标度','100','no')
        self.ParameterSet(ip,u'设置CH2精细标度','2','no')
        self.ParameterSet(ip,u'设置CH1位置','-3','no')
        self.ParameterSet(ip,u'设置CH2位置','0','no')
        self.ParameterSet(ip,u'设置水平触发延迟状态','OFF','no')
        self.ParameterSet(ip,u'设置水平触发位置','80','no')
        self.Communicate_TDS(ip,u'触发源CH1','no')
        self.ParameterSet(ip,u'设置触发高度','0.4','no')
        self.Communicate_TDS(ip,u'设置测量通道1为CH1','no')
        self.ParameterSet(ip,u'设置测量通道1的类型','RISe','no')
        self.ParameterSet(ip,u'设置测量通道1的显示状态','ON','no') #要显示到屏幕上才能读数据
        self.Communicate_TDS(ip,u'设置CH1为全带宽','no')
        self.ParameterSet(ip,u'设置CH1放大倍数',PROBE_SETUP,'no')
        self.Communicate_TDS(ip,u'波形单次运行','no')
        print u'设置完成！'
    
    def SlowStratTimeTest(self,ip,Vol='267.5'):
        """""""""""""""""""""""
        缓启动时间测试：执行该函数前，需要先运行SlowStratTimeTestSet()
        Vol：给定的电压
        SlowStratTime：返回测量的缓启动时间
        """""""""""""""""""""""
        cnt=0
        print u'##########缓启动时间测试############'
        
        while( self.Communicate_TDS(ip,u'波形采集状态查询','no')=='1'):#等波形暂停了，再读数据
            time.sleep(1)
        self.Communicate_TDS(ip,u'设置光标为垂直条','no')
        #调节光标到大于设定电压处
        self.ParameterSet(ip,u'设置垂直光标1的位置','-10','no')   
        self.Communicate_TDS(ip,u'打开CH1通道','no')
        self.Communicate_TDS(ip,u'显示光标1')
        i=-10
        time.sleep(0.5)
        while(abs(float(self.Communicate_TDS(ip,u'查询垂直光标1的值','no'))-float(Vol))>4.0):
            self.ParameterSet(ip,u'设置垂直光标1的位置',str(i),'no')
            i+=0.1
            time.sleep(0.1)
##        self.Communicate_TDS(ip,u'查询垂直光标1的值')
            if i>20:
                break
        #调节光标到大于0.5A处
        self.ParameterSet(ip,u'设置垂直光标2的位置','-10','no')
        self.Communicate_TDS(ip,u'打开CH2通道','no')
        self.Communicate_TDS(ip,u'显示光标2')
        i=-10
        time.sleep(0.5)
        while(abs(float(self.Communicate_TDS(ip,u'查询垂直光标2的值','no')))<0.5):
            self.ParameterSet(ip,u'设置垂直光标2的位置',str(i),'no')
            i+=0.04
            time.sleep(0.5)
            if i>20:
                break
##        self.Communicate_TDS(ip,u'查询垂直光标2的值')
        SlowStratTime=float(self.Communicate_TDS(ip,u'查询垂直光标位置差','no'))
        print u'缓启动时间测试完成，缓启动时间为：',SlowStratTime
        return SlowStratTime
##############负载动态性能测试###############
    def LoadDYNTest(self,ip,Vol='267.5',rule='0.5',load=u'加载',XSCALE='0.01',YSCALE='1'):
        """""""""""""""""""""""
        负载动态测试：
        在负载动态时，测试输出电压突变，调节为交流耦合模式，使用触发方式测试
        将探头接入CH1进行测量

        Vol:需要测试的电压
        rule:通过准则（%）
        load:可设置“加载”、“卸载”
        XSCALE:横坐标分辨率，单位s
        YSCALE纵坐标分辨率，单位V或者A
        
        """""""""""""""""""""""
        print u'##########负载动态性能测试############'
        MaxValue=float(Vol)*float(rule)/100
        print u'允许最大超调量：',MaxValue
        self.Communicate_TDS(ip,u'波形自动运行','no')
        if self.Communicate_TDS(ip,u'波形采集状态查询','no')=='0':
            self.Communicate_TDS(ip,u'波形采集','no')           
        self.Communicate_TDS(ip,u'打开CH1通道','no')        
        self.Communicate_TDS(ip,u'触发源CH1','no')
        self.Communicate_TDS(ip,u'设置触发高度50%','no')
        self.Communicate_TDS(ip,u'正常触发','no')
        self.ParameterSet(ip,u'设置分辨率',XSCALE,'no')
        self.ParameterSet(ip,u'设置CH1精细标度',YSCALE,'no')
        self.ParameterSet(ip,u'设置水平触发位置','50','no')
        self.ParameterSet(ip,u'设置水平触发延迟状态','OFF','no')
        self.ParameterSet(ip,u'设置CH1位置','0','no')
        self.Communicate_TDS(ip,u'设置测量通道1为CH1','no')
        self.ParameterSet(ip,u'设置测量通道1的类型','MAXimum','no')
        self.ParameterSet(ip,u'设置测量通道1的显示状态','ON','no') #要显示到屏幕上才能读数据
        self.Communicate_TDS(ip,u'设置测量通道2为CH1','no')
        self.ParameterSet(ip,u'设置测量通道2的类型','MINImum','no')
        self.ParameterSet(ip,u'设置测量通道2的显示状态','ON','no') #要显示到屏幕上才能读数据
        self.Communicate_TDS(ip,u'设置CH1探头为电压型','no')
        self.Communicate_TDS(ip,u'设置CH1为AC模式','no') #设置为交流耦合模式
        self.Communicate_TDS(ip,u'设置CH1为全带宽','no')
        self.ParameterSet(ip,u'设置CH1放大倍数',PROBE_SETUP,'no')
##        self.Communicate_TDS(ip,u'波形单次运行','no')

##        while( self.Communicate_TDS(ip,u'波形采集状态查询','no')=='1'):#等波形暂停了，再读数据
##            time.sleep(1)
        high=float(self.Communicate_TDS(ip,u'查询触发高度','no'))
        while(1):
            cnt=0
            exitFlag=0            
            self.ParameterSet(ip,u'设置触发高度',str(high),'no')
            time.sleep(1)
##            print 't1',high,str(float(high)+float(step))
            if load==u'加载':
                high=high-0.2
            elif load==u'卸载':
                high=high+0.2
            else:
                print u'没有这种负载类型，退出！'
                return False

            if(self.Communicate_TDS(ip,u'触发状态查询','no')=='READY'):
                time.sleep(2)
                while(1):
                    if(self.Communicate_TDS(ip,u'触发状态查询','no')=='READY'):
                        cnt=cnt+1
                        time.sleep(1)
                    elif(self.Communicate_TDS(ip,u'触发状态查询','no')=='TRIG'):
                        break
                    if cnt>int(5):  #滤波深度                                 
                        print u'触发高度为：',float(self.Communicate_TDS(ip,u'查询触发高度','no'))
                        exitFlag=1
                        break
            if exitFlag==1:
                break
        if load==u'加载':
            tmp=abs(float(self.Communicate_TDS(ip,u'查询测量通道2的值','no')))
            print u'最大值：',tmp
        elif load==u'卸载':
            tmp=abs(float(self.Communicate_TDS(ip,u'查询测量通道1的值','no')))
            print u'最大值：',tmp
        else:
            print u'没有这种负载类型，退出！'
            return False            
        self.Communicate_TDS(ip,u'波形停止','no')
        return float(tmp)


            


#################基础通讯函数########################

    def ParameterSet(self,ip,cmdName,Value,printFlag=''):
        """""""""""""""""""""""
        带值的参数设置
        ip:为示波器IP地址
        cmdName:为发送的命令，可查找说明书获得
        Value:为发送的值
        printFlag:打印收发信息标志
        """""""""""""""""""""""
        if printFlag=='':
            print u'##########',cmdName,Value,'###########'
        
        cmd=self.QueryCmd(cmdName)+' '+Value
        
        if printFlag=='':
            print u'发送的命令为：',cmd
        
        Recdata=req.reqtext(ip,cmd)
        if printFlag=='':        
            print u'收到的数据为：',Recdata
        return Recdata

    def Communicate_TDS(self,ip,cmdName,printFlag=''):
        """""""""""""""""""""
        与示波器TDS_3034C通讯
        ip:为示波器IP地址
        cmdName:为发送的命令，可查找说明书获得
        printFlag:打印收发信息标志
        """""""""""""""""""""
        cmd=self.QueryCmd(cmdName)
        if printFlag=='':
            print u'##########',cmdName,'###########'
            print u'发送的命令为：',cmd
        
        Recdata=req.reqtext(ip,cmd)
        if printFlag=='':        
            print u'收到的数据为：',Recdata
        return Recdata

    def download_pic(self,ip,name='Image'):
        """""""""""""""""""""
        获取示波器当前波形图片
        """""""""""""""""""""
        addr=add+r'.\TestRecord\\'
        sname=name+time.strftime("%Y%m%d%H%M%S")
        req.download_pic(ip,addr,sname)

        
    # 查询命令列表
    def QueryCmd(self, cmdName):     
        f = open(add+r'.\python\TDS_3034C\TDS_3034C_command_list.csv', 'r')
        csvlist = []
        csvlist = f.readlines()
        f.close()
        receiveData=''
        sCmd=''
        tmplist=[]
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
            if tmplist[0] == cmdName.encode('gbk'):
                sCmd = tmplist[1]   #命令
                sName= tmplist[0]   #名称
##                print sCmd
        return sCmd.strip('\n')
            

if __name__ == "__main__":
    test = TDS_3034C()
    ip='***********'
##    test.Communicate_TDS(ip,u'显示光标2')
##    test.Connect_Oscilloscope('***********')
##    test.Communicate_TDS('************',u'波形停止')
##    test.download_pic('***********')
##    test.Communicate_TDS(ip,u'查询触发高度')
##    test.ParameterSet(ip,u'设置CH1放大倍数','500')
##    test.ParameterSet(ip,u'设置水平触发延迟时间','2')
##    test.Communicate_TDS(ip,u'查询水平触发延迟状态')
##    test.Communicate_TDS(ip,u'查询垂直光标2的值')
##    test.Communicate_TDS(ip,u'查询垂直光标2的位置')
##    test.QueryCmd(u'波形停止')
##    test.ParameterSet(ip,u'设置水平光标1位置','0.2')
##    test.ParameterSet(ip,u'设置水平光标2位置','0.6')
##    test.Communicate_TDS(ip,u'波形自动运行')
##    test.config()
##    test.InrushCurrentTest(ip)
##    test.MaxVolTest(ip)
##    test.SlowStratTimeTestSet(ip)
##    test.SlowStratTimeTest(ip)
##    test.VoltagePOverShootTest(ip)
##    test.Communicate_TDS(ip,u'查询垂直光标值的单位')
    test.LoadDYNTest(ip)
##    test.VoltagePOverShootTest(ip)
##    test.VoltageOverShootTestSet(ip)
##    test.Communicate_TDS(ip,u'打开CH1通道','no')
##    test.Communicate_TDS(ip,u'打开CH2通道','no')
##    test.InrushCurrentTest(ip)
##    test.WriteTestDoc('name',[u'输入电压',u'输入电压2',u'输入电压3',15.5])
