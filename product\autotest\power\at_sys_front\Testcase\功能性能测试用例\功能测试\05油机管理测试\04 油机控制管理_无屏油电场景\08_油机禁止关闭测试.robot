*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
油机禁止关闭（定时启动时间内）
    油机管理初始化
    ${开始数量}    获取web事件记录数量    油机启停记录
    @{记录内容old}    run keyword if    ${开始数量} != 0    获取web事件记录最新一条    油机启停记录
    Wait Until Keyword Succeeds    10    1    设置系统时间    2021-01-29 8:00:00
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动电压使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动SOC使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动时间使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动系统异常使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机定时启动使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止电压使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止SOC使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止电流使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机定时启动时刻_1    10:00
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机定时关闭时刻_1    10:20
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机定时启动时刻_2    11:00
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机定时关闭时刻_2    12:20
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机定时启动时刻_3    13:00
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机定时关闭时刻_3    14:20
    ${最短运行时间设置值}    获取web参数量    油机最短运行时间
    ${默认值}    获取web参数上下限范围    油机最短运行时间
    run keyword if    ${最短运行时间设置值} != ${默认值}[0]    Wait Until Keyword Succeeds    10    1    设置web参数量    油机最短运行时间
    ...    ${默认值}[0]
    ${最短运行时间设置值}    获取web参数量    油机最短运行时间
    ${油机启动时间}    获取web参数量    油机定时启动时刻_1
    ${当前系统时间}    获取系统时间
    ${当前系统时间}    split string    ${当前系统时间}
    ${油机启动时间}    catenate    SEPARATOR=    ${油机启动时间}    :00
    ${设置系统时间}    catenate    ${当前系统时间}[0]    ${油机启动时间}
    ${油机启动时间0}    Subtract Time From Date    ${设置系统时间}    5s    #比1:00:00差5s
    Wait Until Keyword Succeeds    10    1    设置系统时间    ${油机启动时间0}
    ${可设置范围}    获取web参数可设置范围    油机最短运行时间
    ${时间设置值}    evaluate    ${可设置范围}[0]+5
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机最短运行时间    ${时间设置值}
    ${最短运行时间设置值}    获取web参数量    油机最短运行时间
    run keyword and ignore error    wait until keyword succeeds    20    1    判断告警不存在    油机异常告警
    Wait Until Keyword Succeeds    20m    1    获取干接点状态直到变化为    ${油机干接点状态}    0    #干接点动作为1（断开），恢复为0（闭合）
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    15m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    运行
    ${起始时间}    获取系统时间
    ${起始发电量}    获取web实时数据    油机累计发电量
    ${起始运行时间}    获取web实时数据    油机累计运行时间
    sleep    2m
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    <<油机关闭~0x19001040020001>>
    ${油机启动时间}    获取web参数量    油机定时关闭时刻_1
    ${当前系统时间}    获取系统时间
    ${当前系统时间}    split string    ${当前系统时间}
    ${油机启动时间}    catenate    SEPARATOR=    ${油机启动时间}    :00
    ${设置系统时间}    catenate    ${当前系统时间}[0]    ${油机启动时间}
    ${油机启动时间0}    Subtract Time From Date    ${设置系统时间}    5s    #比1:00:00差5s
    Wait Until Keyword Succeeds    10    1    设置系统时间    ${油机启动时间0}
    Wait Until Keyword Succeeds    10m    1    信号量数据值为    油机状态    停止
    ${结束运行时间}    获取web实时数据    油机累计运行时间
    Wait Until Keyword Succeeds    20m    1    获取干接点状态直到变化为    ${油机干接点状态}    1    #干接点动作为1（断开），恢复为0（闭合）
    ${结束时间}    获取系统时间
    ${结束发电量}    获取web实时数据    油机累计发电量
    sleep    1m
    关闭交流源输出
    sleep    4m
    ${结束数量}    获取web事件记录数量    油机启停记录
    should be true    ${结束数量} == ${开始数量}+1
    @{记录内容}    获取web事件记录最新一条    油机启停记录
    should be true    ${记录内容}[0] == ${结束数量}
    ${起始时间差}    subtract date from date    ${记录内容}[1]    ${起始时间}
    should be true    -40<=${起始时间差}<=40
    ${结束时间差}    subtract date from date    ${记录内容}[2]    ${结束时间}
    should be true    -40<=${结束时间差}<=40
    should be true    ${起始运行时间}-1<= ${记录内容}[3] <= ${起始运行时间}+1
    should be true    ${结束运行时间}-1<= ${记录内容}[4] <= ${结束运行时间}+1
    should be true    ${起始发电量}-0.4 <= ${记录内容}[5] <= ${起始发电量}+0.4
    should be true    ${结束发电量}-0.4<= ${记录内容}[6] <= ${结束发电量}+0.4
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[7] \ == ${记录内容old}[7]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[8] \ == ${记录内容old}[8]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[9] \ == ${记录内容old}[9]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[10] \ == ${记录内容old}[10]
    #启动时刻2启动油机
    ${开始数量}    获取web事件记录数量    油机启停记录
    @{记录内容old}    run keyword if    ${开始数量} != 0    获取web事件记录最新一条    油机启停记录
    ${油机启动时间}    获取web参数量    油机定时启动时刻_2
    ${当前系统时间}    获取系统时间
    ${当前系统时间}    split string    ${当前系统时间}
    ${油机启动时间}    catenate    SEPARATOR=    ${油机启动时间}    :00
    ${设置系统时间}    catenate    ${当前系统时间}[0]    ${油机启动时间}
    ${油机启动时间0}    Subtract Time From Date    ${设置系统时间}    5s    #比1:00:00差5s
    Wait Until Keyword Succeeds    10    1    设置系统时间    ${油机启动时间0}
    Wait Until Keyword Succeeds    20m    1    获取干接点状态直到变化为    ${油机干接点状态}    0    #干接点动作为1（断开），恢复为0（闭合）
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    15m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    运行
    ${起始时间}    获取系统时间
    ${起始发电量}    获取web实时数据    油机累计发电量
    ${起始运行时间}    获取web实时数据    油机累计运行时间
    sleep    2m
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    <<油机关闭~0x19001040020001>>
    #启动时刻2关闭油机
    ${油机启动时间}    获取web参数量    油机定时关闭时刻_2
    ${当前系统时间}    获取系统时间
    ${当前系统时间}    split string    ${当前系统时间}
    ${油机启动时间}    catenate    SEPARATOR=    ${油机启动时间}    :00
    ${设置系统时间}    catenate    ${当前系统时间}[0]    ${油机启动时间}
    ${油机启动时间0}    Subtract Time From Date    ${设置系统时间}    5s    #比1:00:00差5s
    Wait Until Keyword Succeeds    10    1    设置系统时间    ${油机启动时间0}
    Wait Until Keyword Succeeds    10m    1    信号量数据值为    油机状态    停止
    ${结束运行时间}    获取web实时数据    油机累计运行时间
    Wait Until Keyword Succeeds    20m    1    获取干接点状态直到变化为    ${油机干接点状态}    1    #干接点动作为1（断开），恢复为0（闭合）
    ${结束时间}    获取系统时间
    ${结束发电量}    获取web实时数据    油机累计发电量
    sleep    1m
    关闭交流源输出
    sleep    4m
    ${结束数量}    获取web事件记录数量    油机启停记录
    should be true    ${结束数量} == ${开始数量}+1
    @{记录内容}    获取web事件记录最新一条    油机启停记录
    should be true    ${记录内容}[0] == ${结束数量}
    ${起始时间差}    subtract date from date    ${记录内容}[1]    ${起始时间}
    should be true    -40<=${起始时间差}<=40
    ${结束时间差}    subtract date from date    ${记录内容}[2]    ${结束时间}
    should be true    -40<=${结束时间差}<=40
    should be true    ${起始运行时间}-1<= ${记录内容}[3] <= ${起始运行时间}+1
    should be true    ${结束运行时间}-1<= ${记录内容}[4] <= ${结束运行时间}+1
    should be true    ${起始发电量}-0.4 <= ${记录内容}[5] <= ${起始发电量}+0.4
    should be true    ${结束发电量}-0.4<= ${记录内容}[6] <= ${结束发电量}+0.4
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[7] \ == ${记录内容old}[7]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[8] \ == ${记录内容old}[8]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[9] \ == ${记录内容old}[9]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[10] \ == ${记录内容old}[10]
    #启动时刻3启动油机
    ${开始数量}    获取web事件记录数量    油机启停记录
    @{记录内容old}    run keyword if    ${开始数量} != 0    获取web事件记录最新一条    油机启停记录
    ${油机启动时间}    获取web参数量    油机定时启动时刻_3
    ${当前系统时间}    获取系统时间
    ${当前系统时间}    split string    ${当前系统时间}
    ${油机启动时间}    catenate    SEPARATOR=    ${油机启动时间}    :00
    ${设置系统时间}    catenate    ${当前系统时间}[0]    ${油机启动时间}
    ${油机启动时间0}    Subtract Time From Date    ${设置系统时间}    5s    #比1:00:00差5s
    Wait Until Keyword Succeeds    10    1    设置系统时间    ${油机启动时间0}
    Wait Until Keyword Succeeds    20m    1    获取干接点状态直到变化为    ${油机干接点状态}    0    #干接点动作为1（断开），恢复为0（闭合）
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    15m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    运行
    ${起始时间}    获取系统时间
    ${起始发电量}    获取web实时数据    油机累计发电量
    ${起始运行时间}    获取web实时数据    油机累计运行时间
    sleep    2m
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    <<油机关闭~0x19001040020001>>
    #启动时刻2关闭油机
    ${油机启动时间}    获取web参数量    油机定时关闭时刻_3
    ${当前系统时间}    获取系统时间
    ${当前系统时间}    split string    ${当前系统时间}
    ${油机启动时间}    catenate    SEPARATOR=    ${油机启动时间}    :00
    ${设置系统时间}    catenate    ${当前系统时间}[0]    ${油机启动时间}
    ${油机启动时间0}    Subtract Time From Date    ${设置系统时间}    5s    #比1:00:00差5s
    Wait Until Keyword Succeeds    10    1    设置系统时间    ${油机启动时间0}
    Wait Until Keyword Succeeds    10m    1    信号量数据值为    油机状态    停止
    ${结束运行时间}    获取web实时数据    油机累计运行时间
    Wait Until Keyword Succeeds    20m    1    获取干接点状态直到变化为    ${油机干接点状态}    1    #干接点动作为1（断开），恢复为0（闭合）
    ${结束时间}    获取系统时间
    ${结束发电量}    获取web实时数据    油机累计发电量
    sleep    1m
    关闭交流源输出
    sleep    4m
    ${结束数量}    获取web事件记录数量    油机启停记录
    should be true    ${结束数量} == ${开始数量}+1
    @{记录内容}    获取web事件记录最新一条    油机启停记录
    should be true    ${记录内容}[0] == ${结束数量}
    ${起始时间差}    subtract date from date    ${记录内容}[1]    ${起始时间}
    should be true    -40<=${起始时间差}<=40
    ${结束时间差}    subtract date from date    ${记录内容}[2]    ${结束时间}
    should be true    -40<=${结束时间差}<=40
    should be true    ${起始运行时间}-1<= ${记录内容}[3] <= ${起始运行时间}+1
    should be true    ${结束运行时间}-1<= ${记录内容}[4] <= ${结束运行时间}+1
    should be true    ${起始发电量}-0.4 <= ${记录内容}[5] <= ${起始发电量}+0.4
    should be true    ${结束发电量}-0.4<= ${记录内容}[6] <= ${结束发电量}+0.4
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[7] \ == ${记录内容old}[7]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[8] \ == ${记录内容old}[8]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[9] \ == ${记录内容old}[9]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[10] \ == ${记录内容old}[10]
    [Teardown]    Run keywords    同步系统时间
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    油机定时启动时刻_1
    ...    油机定时关闭时刻_1    油机定时启动时刻_2    油机定时关闭时刻_2    油机定时启动时刻_3    油机定时关闭时刻_3
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    油机定时启动使能
    ...    油机启动时间使能    油机停止电压使能    油机停止SOC使能    油机最短运行时间
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动系统异常使能
    ...    允许
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止电流使能
    ...    禁止
    ...    AND    关闭交流源输出

油机禁止关闭（市电异常）
    [Tags]    notest

油机禁止关闭（正常启动油机）
    [Tags]    notest

系统异常时油机禁止关闭
    [Tags]    notest

油机禁止关闭条件测试（无油机）
    [Tags]    notest
