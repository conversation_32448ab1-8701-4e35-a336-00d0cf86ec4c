*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
安全模式电池放电控制整流器开启测试
    
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-3
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    2

监控复位开启所有整流器测试
    
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-3
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    2
    系统复位
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    [Teardown]    设置web参数量    整流器最小开机数量    3

安全模式下电池检测异常测试
    [Documentation]    增加复位后的休眠，确保参数设置命令下发。
    
    电池管理初始化
    系统复位
    sleep    5m  
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-3
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    2
    Wait Until Keyword Succeeds    10    2s    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池检测异常    次要
    关闭电池模拟器输出
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    电池检测异常
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-3
    重置电池模拟器输出
    Wait Until Keyword Succeeds    5m    2s    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    电池检测异常
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    电池检测异常
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    # Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器休眠-1
    # Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-2
    # Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-3
    # Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    2
    [Teardown]    Run keywords    设置web参数量    整流器最小开机数量    3
    ...    AND    重置电池模拟器输出

安全模式下交流停电
    
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-3
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    2
    关闭交流源输出
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    市电停电
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    0
    打开交流源输出
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    交流供电状态    市电1
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    停电
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    系统停电
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    过渡
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    系统停电
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    [Teardown]    Run keywords    设置web参数量    整流器最小开机数量    3
    ...    AND    打开交流源输出

安全模式下电池测试
    
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-3
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    2
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动测试
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    测试
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    2
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    80    2
    打开负载输出
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    关闭负载输出
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    浮充
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-3
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    2
    [Teardown]    Run keywords    设置web参数量    整流器最小开机数量    3
    ...    AND    关闭负载输出

油机过渡阶段控制整流器休眠
    [Tags]    notest

电池无效控制整流器休眠
    
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-3
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    2
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    8
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-3
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-3
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    2
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    3
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    100

安全模式下直流电压低
    
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-3
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    2
    ${级别设置值}    获取web参数量    直流电压低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    直流电压低    主要
    ${可设置范围}    获取web参数可设置范围    直流电压低阈值
    设置web参数量    直流电压低阈值    ${可设置范围}[1]
    ${ 直流电压低阈值}    获取web参数量    直流电压低阈值
    向下调节电池电压    ${ 直流电压低阈值}-0.5
    wait until keyword succeeds    5m    2    查询指定告警信息    直流电压低
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-3
    向上调节电池电压    ${ 直流电压低阈值}+1
    wait until keyword succeeds    5m    1    判断告警不存在    直流电压低
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-3
    系统复位
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-3
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    2
    [Teardown]    Run keywords    直流电压参数恢复默认值
    ...    AND    设置web参数量    整流器最小开机数量    3
    ...    AND    重置电池模拟器输出

安全模式下电池大电流放电
    
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-3
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    2

安全模式下整流器故障
    
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-3
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    2
    设置web参数量    整流器告警-1    主要
    连接交流源
    关闭交流源输出
    分别设置各相电压频率    70    220    220    50
    打开交流源输出
    wait until keyword succeeds    1m    1    查询指定告警信息    整流器告警-1
    Wait Until Keyword Succeeds    2m    2    信号量数据值为    整流器休眠状态-1    否
    Wait Until Keyword Succeeds    2m    2    信号量数据值为    整流器休眠状态-2    否
    Wait Until Keyword Succeeds    2m    2    信号量数据值为    整流器休眠状态-3    否
    关闭交流源输出
    分别设置各相电压频率    220    220    70    50
    打开交流源输出
    Wait Until Keyword Succeeds    2m    2    信号量数据值为    整流器休眠状态-1    否
    Wait Until Keyword Succeeds    2m    2    信号量数据值为    整流器休眠状态-2    否
    Wait Until Keyword Succeeds    2m    2    信号量数据值为    整流器休眠状态-3    否
    关闭交流源输出
    分别设置各相电压频率    220    70    220    50
    打开交流源输出
    Wait Until Keyword Succeeds    2m    2    信号量数据值为    整流器休眠状态-1    否
    Wait Until Keyword Succeeds    2m    2    信号量数据值为    整流器休眠状态-2    否
    Wait Until Keyword Succeeds    2m    2    信号量数据值为    整流器休眠状态-3    否
    [Teardown]    run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    3
    ...    AND    同时设置三相电压频率    220    50

安全模式下电池电压低
    
    电池管理初始化
    系统复位
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    10    1    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-3
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    2
    ${电压设置值}    获取web参数量    浮充电压
    Wait Until Keyword Succeeds    10    2s    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    Comment    ${电压获取值}    获取web实时数据    直流电压
    Comment    ${电压差值}    evaluate    ${电压设置值}-${电压获取值}
    Comment    run keyword if    ${电压差值}>0.5 \ \ and ${电压获取值} < ${电压设置值}    向上调节电池电压    ${电压设置值} + 0.5
    Comment    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    1
    ${级别设置值}    获取web参数量    电池电压低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池电压低    主要
    ${可设置范围}    获取web参数可设置范围    电池电压低阈值
    设置web参数量    电池电压低阈值    ${可设置范围}[1]
    ${电池电压低设置值}    获取web参数量    电池电压低阈值
    向下调节电池电压    ${电池电压低设置值}-0.5
    wait until keyword succeeds    5m    2    查询指定告警信息    电池电压低
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-3
    向上调节电池电压    ${ 电池电压低设置值}+1
    wait until keyword succeeds    5m    1    判断告警不存在    电池电压低
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-3
    系统复位
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    交流节能状态    安全
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    3
    Wait Until Keyword Succeeds    1m    1    设置web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-2
    Wait Until Keyword Succeeds    1m    1    设置失败的web控制量    整流器休眠-3
    Wait Until Keyword Succeeds    8m    2    信号量数据值为    工作整流器数量    2
    [Teardown]    Run keywords    设置web设备参数量为默认值    电池电压低阈值
    ...    AND    设置web参数量    整流器最小开机数量    3
    ...    AND    重置电池模拟器输出
