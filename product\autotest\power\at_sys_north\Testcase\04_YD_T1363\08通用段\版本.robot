*** Settings ***
Suite Setup       #run keywords    连接CSU
...               # AND    系统复位
...               # AND    sleep    5m
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
1363_0002_V3.0_1363_通用命令_获取协议版本号_1
    [Tags]    1
    ${1363版本}    通用命令_获取协议版本号    ${g_prtcl_1363}    <<获取协议版本号1-4~CID1=40H>>    ${g_ver_1363}
    ${对比结果}    对比协议版本信息    ${1363版本}    ${g_ver_1363}    1363协议版本_1
    should be true    ${对比结果}
    Log    ${对比结果}

1363_0004_V3.0_1363_通用命令_获取协议版本号_2
    [Tags]    1
    ${1363版本}    通用命令_获取协议版本号    ${g_prtcl_1363}    <<获取协议版本号1-4~CID1=41H>>    ${g_ver_1363}
    ${对比结果}    对比协议版本信息    ${1363版本}    ${g_ver_1363}    1363协议版本_2
    should be true    ${对比结果}

1363_0006_V3.0_1363_通用命令_获取协议版本号_3
    [Tags]    1
    ${1363版本}    通用命令_获取协议版本号    ${g_prtcl_1363}    <<获取协议版本号1-4~CID1=42H>>    ${g_ver_1363}
    ${对比结果}    对比协议版本信息    ${1363版本}    ${g_ver_1363}    1363协议版本_3
    should be true    ${对比结果}

1363_0008_V3.0_1363_通用命令_获取协议版本号_4
    [Tags]    1
    ${1363版本}    通用命令_获取协议版本号    ${g_prtcl_1363}    <<获取协议版本号1-4~CID1=91H>>    ${g_ver_1363}
    ${对比结果}    对比协议版本信息    ${1363版本}    ${g_ver_1363}    1363协议版本_4
    should be true    ${对比结果}

1363_0010_V3.0_1363_通用命令_获取协议版本号_5
    [Tags]    1
    ${1363版本}    通用命令_获取协议版本号    ${g_prtcl_1363}    <<获取协议版本号1-4~CID1=D0H>>    ${g_ver_1363}
    ${对比结果}    对比协议版本信息    ${1363版本}    ${g_ver_1363}    1363协议版本_5
    should be true    ${对比结果}

1363_0012_V3.0_1363_通用命令_获取协议版本号_6
    [Tags]    1
    [Setup]    run keywords    连接CSU
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置
    ...    纯铅酸
    ...    AND    sleep    1m
    ${1363版本}    通用命令_获取协议版本号    ${g_prtcl_1363}    <<获取协议版本号1-4~CID1=46H>>    ${g_ver_1363}
    ${对比结果}    对比协议版本信息    ${1363版本}    ${g_ver_1363}    1363协议版本_6
    should be true    ${对比结果}
