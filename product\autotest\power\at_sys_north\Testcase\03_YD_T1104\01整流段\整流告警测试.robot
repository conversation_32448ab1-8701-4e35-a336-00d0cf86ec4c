*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
# 1104批量获取SMR数字量
#     [Documentation]    21min
#     [Setup]
#     写入CSV文档    整流器数字量获取测试    信号名称    信号值    结果    备注
#     连接CSU
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=digital data    INDEX_DEVICE_CH=整流器
#     ${排除列表}    create list
#     P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除数字量信号}    ${排除列表}    2    ${模拟整流器地址}
#     ${列表1}    处理1104/1363SMR或PU设备序号大于40的部分    ${列表1}    ${模拟整流器地址}
#     ${协议数据}    1104获取数据    ${g_prtcl_1104}    <<获取整流段数字量~CID1=41H>>    <<获取整流段数字量~CID2=43H>>    None    ${g_ver_1104}
#     ${web数据}    通过1104_data获取web数据    ${协议数据}
#     ${1104待测}    1104_获取1104/1363指定设备指定信号量    ${协议数据}    ${web数据}    ${列表1}
#     Log many    ${1104待测}
#     Comment    ${指定数据1}    Create Dictionary    signal_name    <<整流器关机状态-6~0x7001020010001>>    convention    True    device_name    整流器    data_type    int    unit    None    1104_name    整流器关机状态-6
#     Comment    ${指定数据2}    Create Dictionary    signal_name    <<整流器关机状态-18~0x7001020010001>>    convention    True    device_name    整流器    data_type    int    unit    None    1104_name    整流器关机状态-18
#     Comment    @{1104待测}    Create List    ${指定数据1}    ${指定数据2}
#     FOR    ${i}    IN    @{1104待测}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${1104协议名称}    Get From Dictionary    ${i}    1104_name
#         ${缺省值}    获取web参数上下限范围    ${信号名称}
#         Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    1104    SMR    ${信号名称}    数字量    ${缺省值}[1]    整流器数字量获取测试    null    null    null    <<获取整流段数字量~CID1=41H>>    <<获取整流段数字量~CID2=43H>>    ${1104协议名称}    None
#         Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    1104    SMR    ${信号名称}    数字量    ${缺省值}[2]    整流器数字量获取测试    null    null    null    <<获取整流段数字量~CID1=41H>>    <<获取整流段数字量~CID2=43H>>    ${1104协议名称}    None
#         Run Keyword IF    ${缺省值}[1]!=${缺省值}[0] and ${缺省值}[2]!=${缺省值}[0]    Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    1104    SMR    ${信号名称}    数字量    ${缺省值}[0]    整流器数字量获取测试    null    null    null    <<获取整流段数字量~CID1=41H>>    <<获取整流段数字量~CID2=43H>>    ${1104协议名称}
#         ...    None
#     END

1104批量获取SMR数字量
    [Documentation]    21min
    [Setup]    Wait Until Keyword Succeeds    10    2    设置web参数量    交流节能模式    自由
    写入CSV文档    整流器数字量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=digital data    INDEX_DEVICE_CH=整流器
    ${排除列表}    create list
    P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除数字量信号}    ${排除列表}    2    ${模拟整流器地址}
    ${列表1}    处理1104/1363SMR或PU设备序号大于40的部分    ${列表1}    ${模拟整流器地址}
    ${协议数据}    1104获取数据    ${g_prtcl_1104}    <<获取整流段数字量~CID1=41H>>    <<获取整流段数字量~CID2=43H>>    None    ${g_ver_1104}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${1104待测}    1104_获取1104/1363指定设备指定信号量    ${协议数据}    ${web数据}    ${列表1}
    
    Log many    ${1104待测}
    Comment    ${指定数据1}    Create Dictionary    signal_name    <<整流器关机状态-6~0x7001020010001>>    convention    True    device_name    整流器    data_type    int    unit    None    1104_name    整流器关机状态-6
    Comment    ${指定数据2}    Create Dictionary    signal_name    <<整流器关机状态-18~0x7001020010001>>    convention    True    device_name    整流器    data_type    int    unit    None    1104_name    整流器关机状态-18
    Comment    @{1104待测}    Create List    ${指定数据1}    ${指定数据2}
    # ${缺省值列表1}    获取缺省值列表    ${1104待测}    1
    # Run Keyword And Continue On Failure    1104南向子设备数字量列表获取值封装判断结果    1104    SMR    数字量    ${缺省值列表1}    整流器数字量获取测试    null    null    null    <<获取整流段数字量~CID1=41H>>    <<获取整流段数字量~CID2=43H>>    None
    # ${缺省值列表2}    获取缺省值列表    ${1104待测}    2
    # Run Keyword And Continue On Failure    1104南向子设备数字量列表获取值封装判断结果    1104    SMR    数字量    ${缺省值列表2}    整流器数字量获取测试    null    null    null    <<获取整流段数字量~CID1=41H>>    <<获取整流段数字量~CID2=43H>>    None
    # ${缺省值列表0}    获取缺省值列表    ${1104待测}    0
    # ${缺省值列表0}    1104检查缺省值列表    ${缺省值列表0}    ${缺省值列表1}    ${缺省值列表2} 
    # Run Keyword And Continue On Failure    1104南向子设备数字量列表获取值封装判断结果    1104    SMR    数字量    ${缺省值列表0}    整流器数字量获取测试    null    null    null    <<获取整流段数字量~CID1=41H>>    <<获取整流段数字量~CID2=43H>>    None

    ${缺省值列表1}    获取缺省值列表    ${1104待测}    1    1104
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    1104    SMR    数字量    ${缺省值列表1}    整流器数字量获取测试    null    <<获取整流段数字量~CID1=41H>>    <<获取整流段数字量~CID2=43H>>    None
    ${缺省值列表2}    获取缺省值列表    ${1104待测}    2    1104
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    1104    SMR    数字量    ${缺省值列表2}    整流器数字量获取测试    null    <<获取整流段数字量~CID1=41H>>    <<获取整流段数字量~CID2=43H>>    None
    ${缺省值列表0}    获取缺省值列表    ${1104待测}    0    1104
    # ${缺省值列表0}    1104检查缺省值列表    ${缺省值列表0}    ${缺省值列表1}    ${缺省值列表2} 
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    1104    SMR    数字量    ${缺省值列表0}    整流器数字量获取测试    null    <<获取整流段数字量~CID1=41H>>    <<获取整流段数字量~CID2=43H>>    None
    [Teardown]    Wait Until Keyword Succeeds    10    2    设置web参数量    交流节能模式   安全

# 1104批量获取SMR模拟量
#     [Documentation]    21min
#     [Setup]
#     写入CSV文档    整流器模拟量获取测试    信号名称    信号值    结果    备注
#     连接CSU
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=整流器
#     ${排除列表}    create list
#     P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除模拟量信号}    ${排除列表}    2    ${模拟整流器地址}
#     ${列表1}    处理1104/1363SMR或PU设备序号大于40的部分    ${列表1}    ${模拟整流器地址}
#     ${协议数据}    1104获取数据    ${g_prtcl_1104}    <<获取整流段模拟量~CID1=41H>>    <<获取整流段模拟量~CID2=42H>>    None    ${g_ver_1104}
#     ${web数据}    通过1104_data获取web数据    ${协议数据}
#     ${1104待测}    1104_获取1104/1363指定设备指定信号量    ${协议数据}    ${web数据}    ${列表1}
#     FOR    ${i}    IN    @{1104待测}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${1104协议名称}    Get From Dictionary    ${i}    1104_name
#         ${缺省值}    获取web参数上下限范围    ${信号名称}
#         Run Keyword And Continue On Failure    南向子设备模拟量获取值封装判断结果    1104    SMR    ${信号名称}    模拟量    ${缺省值}[1]    整流器模拟量获取测试    null    null    null    <<获取整流段模拟量~CID1=41H>>    <<获取整流段模拟量~CID2=42H>>    ${1104协议名称}    None
#         Run Keyword And Continue On Failure    南向子设备模拟量获取值封装判断结果    1104    SMR    ${信号名称}    模拟量    ${缺省值}[2]    整流器模拟量获取测试    null    null    null    <<获取整流段模拟量~CID1=41H>>    <<获取整流段模拟量~CID2=42H>>    ${1104协议名称}    None
#         Run Keyword IF    ${缺省值}[1]!=${缺省值}[0] and ${缺省值}[2]!=${缺省值}[0]    Run Keyword And Continue On Failure    南向子设备模拟量获取值封装判断结果    1104    SMR    ${信号名称}    模拟量    ${缺省值}[0]    整流器模拟量获取测试    null    null    null    <<获取整流段模拟量~CID1=41H>>    <<获取整流段模拟量~CID2=42H>>    ${1104协议名称}
#         ...    None
#     END

1104批量获取SMR模拟量
    [Documentation]    21min
    [Setup]
    写入CSV文档    整流器模拟量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=整流器
    ${排除列表}    create list
    P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除模拟量信号}    ${排除列表}    2    ${模拟整流器地址}
    ${列表1}    处理1104/1363SMR或PU设备序号大于40的部分    ${列表1}    ${模拟整流器地址}
    ${协议数据}    1104获取数据    ${g_prtcl_1104}    <<获取整流段模拟量~CID1=41H>>    <<获取整流段模拟量~CID2=42H>>    None    ${g_ver_1104}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${1104待测}    1104_获取1104/1363指定设备指定信号量    ${协议数据}    ${web数据}    ${列表1}
    # ${缺省值列表1}   获取缺省值列表  ${1104待测}    1
    # Run Keyword And Continue On Failure    1104南向子设备模拟量获取值封装判断结果    1104    SMR    模拟量    ${缺省值列表1}    整流器模拟量获取测试    null    null    null    <<获取整流段模拟量~CID1=41H>>    <<获取整流段模拟量~CID2=42H>>    None
    # ${缺省值列表2}   获取缺省值列表  ${1104待测}    2
    # Run Keyword And Continue On Failure    1104南向子设备模拟量获取值封装判断结果    1104    SMR    模拟量    ${缺省值列表2}    整流器模拟量获取测试    null    null    null    <<获取整流段模拟量~CID1=41H>>    <<获取整流段模拟量~CID2=42H>>    None
    # ${缺省值列表0}    获取缺省值列表    ${1104待测}    0
    # ${缺省值列表0}    1104检查缺省值列表    ${缺省值列表0}    ${缺省值列表1}    ${缺省值列表2} 
    # Run Keyword And Continue On Failure    1104南向子设备模拟量获取值封装判断结果    1104    SMR    数字量    ${缺省值列表0}    整流器数字量获取测试    null    null    null    <<获取整流段数字量~CID1=41H>>    <<获取整流段数字量~CID2=42H>>    None

    ${缺省值列表1}    获取缺省值列表    ${1104待测}    1    1104
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    1104    SMR    模拟量    ${缺省值列表1}    整流器模拟量获取测试    null     <<获取整流段模拟量~CID1=41H>>    <<获取整流段模拟量~CID2=42H>>    None
    ${缺省值列表2}    获取缺省值列表    ${1104待测}    2    1104
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    1104    SMR    模拟量    ${缺省值列表2}    整流器模拟量获取测试    null    <<获取整流段模拟量~CID1=41H>>    <<获取整流段模拟量~CID2=42H>>    None
    ${缺省值列表0}    获取缺省值列表    ${1104待测}    0    1104
    # ${缺省值列表0}    1104检查缺省值列表    ${缺省值列表0}    ${缺省值列表1}    ${缺省值列表2} 
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    1104    SMR    模拟量    ${缺省值列表0}    整流器数字量获取测试    null    <<获取整流段数字量~CID1=41H>>    <<获取整流段数字量~CID2=42H>>    None

1104批量获取状态量和SMR故障测试
    写入CSV文档    整流器数字量和故障量获取测试    信号名称    信号值    结果
    连接CSU
    ${级别设置值}    获取web参数量    整流器故障
    run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    整流器故障    严重
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=整流器
    ${排除列表}    create list
    P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除故障量信号}    ${排除列表}    1    ${模拟整流器地址}    1
    ${列表1}    处理1104/1363SMR或PU设备序号大于40的部分    ${列表1}    ${模拟整流器地址}
    #1363协议中只显示SMR告警/SMR故障/SMR通讯中断
    #所以此处无需从1363表中查找数据，以data_dict得到的为主
    Comment    ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取PU段告警~CID1=D0H>>    <<获取PU段告警~CID2=44H>>    None    ${g_ver_1363}
    Comment    ${web数据}    通过1104_data获取web数据    ${协议数据}
    Comment    ${1104待测}    1104_获取1104/1363指定设备指定信号量    ${协议数据}    ${web数据}    ${列表1}
    Comment    FOR    ${i}    IN    @{列表1}
    ${待测数据长度}    Get Length    ${列表1}
    ${待测}    Evaluate    ${待测数据长度}-1
    ${待测索引}    Evaluate    random.randint(0,${待测})    random
    FOR    ${i}    IN    ${列表1}[${待测索引}]
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        @{序号}    evaluate    re.split('[-~]','${信号名称}')    re
        ${1363告警名}    set variable    模块故障
        ${1363告警名}    Catenate    SEPARATOR=    ${1363告警名}    -    ${序号}[1]
        ${实时告警中的设备名称}    主动告警_组合实时告警中的设备名称    整流器    ${信号名称}
        Run Keyword And Continue On Failure    整流器或PU告警量产生封装判断结果    1104    SMR    ${信号名称}    数字量    ${告警产生}    ${模拟整流器地址}    整流器故障    整流器数字量和故障量获取测试    ${实时告警中的设备名称}    null    null    <<获取整流段告警量~CID1=41H>>    <<获取整流段告警量~CID2=44H>>    ${1363告警名}
        ...    None
        Run Keyword And Continue On Failure    整流器或PU告警量恢复封装判断结果    1104    SMR    ${信号名称}    数字量    ${告警恢复}    ${模拟整流器地址}    整流器故障    整流器数字量和故障量获取测试    null    null    null    <<获取整流段告警量~CID1=41H>>    <<获取整流段告警量~CID2=44H>>    ${1363告警名}
        ...    None
    END

# 1104批量获取状态量和SMR故障测试新
#     写入CSV文档    整流器数字量和故障量获取测试    信号名称    信号值    结果
#     连接CSU
#     ${级别设置值}    获取web参数量    整流器故障
#     run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    整流器故障    严重
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=整流器
#     ${排除列表}    create list
#     ${信号量列表}    create list
#     P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除故障量信号}    ${排除列表}    1    ${模拟整流器地址}    1
#     ${列表1}    处理1104/1363SMR或PU设备序号大于40的部分    ${列表1}    ${模拟整流器地址}
#     #1363协议中只显示SMR告警/SMR故障/SMR通讯中断
#     #所以此处无需从1363表中查找数据，以data_dict得到的为主
#     Comment    ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取PU段告警~CID1=D0H>>    <<获取PU段告警~CID2=44H>>    None    ${g_ver_1363}
#     Comment    ${web数据}    通过1104_data获取web数据    ${协议数据}
#     Comment    ${1104待测}    1104_获取1104/1363指定设备指定信号量    ${协议数据}    ${web数据}    ${列表1}
#     Comment    FOR    ${i}    IN    @{列表1}
#     ${待测数据长度}    Get Length    ${列表1}
#     ${待测}    Evaluate    ${待测数据长度}-1
#     ${待测索引}    Evaluate    random.randint(0,${待测})    random
#     FOR    ${i}    IN    ${列表1}[${待测索引}]
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         @{序号}    evaluate    re.split('[-~]','${信号名称}')    re
#         ${1363告警名}    set variable    模块故障
#         ${1363告警名}    Catenate    SEPARATOR=    ${1363告警名}    -    ${序号}[1]
#         ${实时告警中的设备名称}    主动告警_组合实时告警中的设备名称    整流器    ${信号名称}
#     #     Run Keyword And Continue On Failure    整流器或PU告警量产生封装判断结果    1104    SMR    ${信号名称}    数字量    ${告警产生}    ${模拟整流器地址}    整流器故障    整流器数字量和故障量获取测试    ${实时告警中的设备名称}    null    null    <<获取整流段告警量~CID1=41H>>    <<获取整流段告警量~CID2=44H>>    ${1363告警名}
#     #     ...    None
#     #     Run Keyword And Continue On Failure    整流器或PU告警量恢复封装判断结果    1104    SMR    ${信号名称}    数字量    ${告警恢复}    ${模拟整流器地址}    整流器故障    整流器数字量和故障量获取测试    null    null    null    <<获取整流段告警量~CID1=41H>>    <<获取整流段告警量~CID2=44H>>    ${1363告警名}
#     #     ...    None
#     # END
# 	    ${dict1}          Create Dictionary 
#         Set To Dictionary    ${dict1}     signal_name     ${信号名称}  
#         Set To Dictionary    ${dict1}     alarm_name    ${1363告警名} 
#         Set To Dictionary    ${dict1}     device_name    ${实时告警中的设备名称} 
# 	    Append To List        ${信号量列表}    ${dict1}
#     END
#     整流器或PU故障量批量产生    SMR    ${信号量列表}    数字量    ${告警产生}    ${模拟整流器地址}    整流器故障    整流器数字量和故障量获取测试    1104    null    null    <<获取整流段告警量~CID1=41H>>    <<获取整流段告警量~CID2=44H>>
#         ...    None
#     整流器或PU故障量批量恢复    SMR    ${信号量列表}    数字量    ${告警恢复}    ${模拟整流器地址}    整流器故障    整流器数字量和故障量获取测试    1104    null    null    <<获取整流段告警量~CID1=41H>>    <<获取整流段告警量~CID2=44H>>
#         ...    None
#     [Teardown]    整流器测试结束条件

1104批量获取状态量和SMR告警测试
    [Documentation]    协议子表无此告警
    [Tags]    3
    写入CSV文档    整流器数字量和告警量获取测试    信号名称    信号值    结果
    连接CSU
    ${级别设置值}    获取web参数量    整流器告警
    run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    整流器告警    严重
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=整流器
    ${排除列表}    create list
    P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除告警量信号}    ${排除列表}    1    ${模拟整流器地址}    1
    ${列表1}    处理1104/1363SMR或PU设备序号大于40的部分    ${列表1}    ${模拟整流器地址}
    #1363协议中只显示SMR告警/SMR故障/SMR通讯中断
    #所以此处无需从1363表中查找数据，以data_dict得到的为主
    Comment    ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取PU段告警~CID1=D0H>>    <<获取PU段告警~CID2=44H>>    None    ${g_ver_1363}
    Comment    ${web数据}    通过1104_data获取web数据    ${协议数据}
    Comment    ${1104待测}    1104_获取1104/1363指定设备指定信号量    ${协议数据}    ${web数据}    ${列表1}
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        @{序号}    evaluate    re.split('[-~]','${信号名称}')    re
        ${1363告警名}    set variable    整流器告警
        ${1363告警名}    Catenate    SEPARATOR=    ${1363告警名}    -    ${序号}[1]
        ${实时告警中的设备名称}    主动告警_组合实时告警中的设备名称    整流器    ${信号名称}
        Run Keyword And Continue On Failure    北向协议_整流器或PU告警/故障量产生封装判断结果    1104    SMR    ${信号名称}    数字量    ${告警产生}    ${模拟整流器地址}    整流器告警    整流器数字量和告警量获取测试    ${实时告警中的设备名称}    null    null    <<获取整流段告警量~CID1=41H>>    <<获取整流段告警量~CID2=44H>>    ${1363告警名}
        ...    None
        Run Keyword And Continue On Failure    北向协议_整流器或PU告警/故障量恢复封装判断结果    1104    SMR    ${信号名称}    数字量    ${告警恢复}    ${模拟整流器地址}    整流器告警    整流器数字量和告警量获取测试    null    null    null    <<获取整流段告警量~CID1=41H>>    <<获取整流段告警量~CID2=44H>>    ${1363告警名}
        ...    None
    END

# 1104批量获取状态量和SMR告警测试新
#     [Documentation]    协议子表无此告警
#     [Tags]    3
#     写入CSV文档    整流器数字量和告警量获取测试    信号名称    信号值    结果
#     连接CSU
#     ${级别设置值}    获取web参数量    整流器告警
#     run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    整流器告警    严重
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=整流器
#     ${排除列表}    create list
#     P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除告警量信号}    ${排除列表}    1    ${模拟整流器地址}    1
#     ${列表1}    处理1104/1363SMR或PU设备序号大于40的部分    ${列表1}    ${模拟整流器地址}
#     #1363协议中只显示SMR告警/SMR故障/SMR通讯中断
#     #所以此处无需从1363表中查找数据，以data_dict得到的为主
#     Comment    ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取PU段告警~CID1=D0H>>    <<获取PU段告警~CID2=44H>>    None    ${g_ver_1363}
#     Comment    ${web数据}    通过1104_data获取web数据    ${协议数据}
#     Comment    ${1104待测}    1104_获取1104/1363指定设备指定信号量    ${协议数据}    ${web数据}    ${列表1}
#     FOR    ${i}    IN    @{列表1}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         @{序号}    evaluate    re.split('[-~]','${信号名称}')    re 
#         ${1363告警名}    set variable    整流器告警
#         ${1363告警名}    Catenate    SEPARATOR=    ${1363告警名}    -    ${序号}[1]
#         Set To Dictionary    ${i}     1363warnname     ${1363告警名}  
#         ${实时告警中的设备名称}    主动告警_组合实时告警中的设备名称    整流器    ${信号名称}
#         Set To Dictionary    ${i}     devicename1     ${实时告警中的设备名称}  
#         Set To Dictionary    ${i}     devicename2     null 
#     END
#     Run Keyword And Continue On Failure    1104北向协议_整流器或PU告警/故障量产生封装判断结果    1104    SMR    ${列表1}    数字量    ${告警产生}    ${模拟整流器地址}    整流器告警    整流器数字量和告警量获取测试    null    null    <<获取整流段告警量~CID1=41H>>    <<获取整流段告警量~CID2=44H>>    None
#     Run Keyword And Continue On Failure    1104北向协议_整流器或PU告警/故障量恢复封装判断结果    1104    SMR    ${列表1}    数字量    ${告警产生}    ${模拟整流器地址}    整流器告警    整流器数字量和告警量获取测试    null    null    <<获取整流段告警量~CID1=41H>>    <<获取整流段告警量~CID2=44H>>    None
