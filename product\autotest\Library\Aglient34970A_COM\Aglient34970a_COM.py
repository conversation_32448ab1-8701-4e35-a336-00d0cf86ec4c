#-*-coding:utf-8 -*-
#####################################################################################
# File Name:    Aglient34970a_Gpib.py
# Discription:  实现与Aglient34970a的连接和数据读写
# Author:   刘万仓
# DevEnv:   win7x86  python2.7
# Notes:    使用GPIB与aglient34970a连接，读取和控制aglient34970a
# Log:      20180303
#说明：定义了一个Aglient34970A类，这个类里面包含了两个函数，Connect是连接GPIB的，QueryTemp是查询温度数据的
#参数要求：QueryTemp函数接受1-60或者101-120，201-220,301-320的通道列表
#返回值：以"通道号:温度测量值"的方式返回查询到的温度数据，如果没有查询到数据，则返回999
#备注：列表中的第一个通道号必须是有物理连接的，不然返回的会全部无效
################################################################################
import time
####读取文件地址，使用RIDE时，改为ADD[1]；使用本地编译时改为ADD[0]####
ADD={0:'..\.',
     1:''
    }
add=ADD[0]
class Aglient34970a_COM():
    import pyvisa
    import time
    mg=pyvisa.ResourceManager()
    print mg.list_resources()
    connect_flag=True #连接成功标志
#创建温度测试文档
    def CreateTemperatureDoc(self,nameList,InputVol='380',OutPutVol='750',OutPutCur='20'):
        sStr=''
        for i in nameList:
##            print i
            sStr+=i+','
        sStr=u'时间,'+sStr
        Str2=u'输入电压：,'+InputVol+u',输出电压：,'+OutPutVol+u',输出电流,'+OutPutCur
##        print sStr
        try:
            savef=open(r'..\..\TestRecord'+u'\温度测试数据.csv','a')
            print>>savef,Str2.encode('gbk')
            print>>savef,sStr[:-1].encode('gbk')
            savef.close()
        except:
            savef=open(r'.\TestRecord'+u'\温度测试数据.csv','a')
            print>>savef,Str2.encode('gbk')
            print>>savef,sStr[:-1].encode('gbk')
            savef.close()  

        print u'创建温度测试文档成功！'
            
    def ConnectAglient34970a(self,Address='2'):#default address is 9
##        self.ag=self.mg.open_resource("GPIB0::"+str(Address)+"::INSTR")     #ag=aglient34970A
        add='ASRL'+Address+'::INSTR'
        
        cnt=0
        while(1):
            try:
                self.ag=self.mg.open_resource('ASRL2::INSTR')     #ag=aglient34970A
                instr=self.ag.query("*IDN?")
                print instr.strip('\n')
                if "34970A" in instr:                
                    print u"连接Aglient34970a成功!"
                else:
                    print u"仪表型号读取失败！返回的仪表型号信息为： ",idnstr
                break
            except self.pyvisa.errors.VisaIOError:
                self.connect_flag=False
                print u"连接错误或者GPIB地址错误，请检查GPIB线缆和仪表GPIB地址设定！"
                self.CloseAglient34970a()
                cnt+=1
            if cnt>3:
                print u'连接超时！'
                break
        return self.connect_flag

    def CloseAglient34970a(self):#default address is 9
        if self.ag is not None:
            self.ag.close()
            self.ag=None
        print u'关闭与Aglient34970a的连接！'
        
    def QueryTemp(self, ChanNum='5'):#ChanList为1-60或者101-120，201-220,301-320的通道列表
        templist=[]
        ChanList=list(range(1, int(ChanNum)+1))
        chanlist=[]#用于存放转化后的通道列表
        chanlist_all=list(range(101,121))+list(range(201,221))+list(range(301,321))#可用通道列表集合
        tempdict={}#返回的测试数据字典
        tempStr=''

        for i in ChanList:
            if i<21:
                chanlist.append(i + 100)   #还原为101-120
            elif i<41:
                chanlist.append(i - 20 + 200) #还原为201-220
            elif i<61:
                chanlist.append(i - 40 + 300) #还原为301-320
            else:
                chanlist.append(i)
        ChanList=list(set(chanlist).intersection(set(chanlist_all)))#取chanlist与chanlist_all的交集作为待查询数据
        ChanList.sort()#排序
        addstr=str(ChanList).replace(" ", "").replace("[", "").replace("]", "")#addlist为一个形如"101,102,106..."的字符串
        #if __name__ == '__main__': print(addstr,"addstr")

        self.SetCmd("*RST",'no')
        time.sleep(0.5)
        self.SetCmd("CONF:TEMP TC,K,(@"+addstr+")",'no')#设定热电偶为K型
        time.sleep(0.5)
        self.SetCmd("FORM:READ:CHAN ON",'no')#返回的数据包含通道编号
        time.sleep(0.5)
        self.SetCmd("ROUTE:SCAN (@"+addstr+")",'no')
        time.sleep(0.5)
        self.SetCmd("INIT",'no')
        time.sleep(0.5)
        while(1):
            recstr=self.QueryCmd("SYSTem:ERRor?",'no').strip('\n')
##            print recstr
            if recstr[0:2]=='+0':
                break
        self.time.sleep(len(ChanList) * 0.2)
##        print 't2',self.ag.query("FETCH?")
        try:
            x=self.QueryCmd("FETCH?",'no')
            templist=x.split(',')
            for i in range(len(templist)):
                templist[i]=float(templist[i])
                if i%2!=0:
                    templist[i]=int(templist[i])
            tempdict=dict(zip(templist[1::2],templist[0::2]))
##            print 't3',templist
        except self.pyvisa.VisaIOError:
            tempdict=dict(zip(chanlist,[999]*len(chanlist)))
            print u'读取数据出错，请检查设置和插排数量！'
        for i in templist[0::2]:            
            tempStr+=str(i)+','
        
        tempStr=time.strftime("%H:%M:%S")+','+tempStr
##        print 'tt',tempStr[:-1]

        try:
            savef=open(r'..\..\TestRecord'+u'\温度测试数据.csv','a')
            print>>savef,tempStr[:-1].encode('gbk')
            savef.close()
        except:
            savef=open(r'.\TestRecord'+u'\温度测试数据.csv','a')
            print>>savef,tempStr[:-1].encode('gbk')
            savef.close()                   
        return templist[0::2]
##查询命令=============================================
    def QueryCmd(self, sComment=':BSR?',printflag=''):     
        returnStr=''
        flag=1
        while(flag!=0 and flag<3):
            try:
                returnStr=self.ag.query(sComment)#查询命令用query
                flag=0
            except :
                flag=flag+1  #出错了重发
                print u'通讯超时！'
                time.sleep(1)
##        print returnStr
        if printflag=='':
            print u'收到数据为：',returnStr.strip('\n')
        return returnStr.strip('\n')

##设置命令=============================================
    def SetCmd(self, sComment,printflag=''):     
##        print sComment,len(sComment),type(sComment)
        flag=1
        while(flag!=0 and flag<3):
            try:
                self.ag.write(sComment)#设置命令用write
                flag=0
            except:
                flag=flag+1  #出错了重发
                print u'通讯超时！'
                time.sleep(1)


if __name__ == '__main__':
    AG=Aglient34970a_COM()
    AG.ConnectAglient34970a('2')
##    AG.CreateTemperatureDoc([u'时间',u'地点',u'人物'])
    while(1):
        print AG.QueryTemp(10)
    AG.CloseAglient34970a()
##    print(x)
##    # x=AG.QueryTemp(ChanList=[201,202,203])
##    # print(x)
##    # x=AG.QueryTemp(ChanList=[101,102,103])
##    # print(x)
##    # x=AG.QueryTemp(ChanList=[301,302,303])
##    # print(x)
##    x=AG.QueryTemp(ChanList=list(range(301,321)))
##    print(x)
