# -*- coding: utf-8 -*-
import json
import sys
import time
import os
import urllib2
import ssl
import re

import requests
from requests.packages.urllib3.util import Retry
import robot

from RequestsLibrary.compat import urlencode, PY3
import chardet
import global_var as g_var
import xml.etree.ElementTree as ET

class WebKeyword(object):
    
    def __init__(self):
        self.fileName = sys._getframe().f_code.co_filename.split('\\')[-1] #sys._getframe().f_code.co_filename 获取本文件的文件名(包含全路径)
        self.curr_dir = os.path.split(os.path.realpath(__file__))[0]
        lst = self.curr_dir.split('\\')[:-2]
        lst.append( 'file' )
        lst.append('data_dictionary')
        self.recordPath = '\\'.join( lst )
        self.uploadPath= os.path.abspath(os.path.join(self.recordPath, ".."))
    #----------------
    def create_session(self, alias, url, headers={}, cookies=None,
                       auth=None, timeout=None, proxies=None,
                       verify=False, debug=0, max_retries=3, backoff_factor=0.10, disable_warnings=0):
        
        s = session = requests.Session()
        
        #默认值就是整型数据 3 , 下面if语句中的代码,屏蔽了也不影响本次应用 20181206
        if max_retries > 0:
            http = requests.adapters.HTTPAdapter(max_retries=Retry(total=max_retries, backoff_factor=backoff_factor))
            https = requests.adapters.HTTPAdapter(max_retries=Retry(total=max_retries, backoff_factor=backoff_factor))
            # Replace the session's original adapters
            s.mount('http://', http)
            s.mount('https://', https)

        # Disable requests warnings, useful when you have large number of testcase
        # you will observe drastical changes in Robot log.html and output.xml files size
        if disable_warnings:
            if not verify:
                requests.packages.urllib3.disable_warnings()

        s.verify = verify
        s.url = url
        
        return session
    #----------------
    def to_json(self, content):
##        print self.fileName, sys._getframe().f_lineno, PY3 #调试过程发现,PY3始终为 False
##        if PY3:
##            print self.fileName, sys._getframe().f_lineno
##            if isinstance(content, bytes):
##                print self.fileName, sys._getframe().f_lineno,
##                content = content.decode(encoding='utf-8')

        return json.loads(content)
    #----------------
    def post_request(
            self,
            alias,
            uri,
            data=None,
            params=None,
            headers=None,
            files=None,
            allow_redirects=None,
            timeout=None,
            session=None):
        #alias=u'pmsa',uri=u'/cgi-bin/main.fcgi',headers=header,data=para,files=file_dst,timeout=60,session=session
        #print 'post_request:', 'alias=',alias, 'uri=',uri,'data=', data, 'params=',params, 'headers=',headers,'files=',files
        if not files:
            data = self._format_data_according_to_header(session, data, headers)

        redir = True if allow_redirects is None else allow_redirects
        response = self._body_request(
            "post",
            session,
            uri,
            data,
            params,
            files,
            headers,
            redir,
            timeout)
        return response
    #----------------
    def _body_request(
            self,
            method_name,
            session,
            uri,
            data,
            params,
            files,
            headers,
            allow_redirects,
            timeout):
        method = getattr(session, method_name)
        
        resp = method(self._get_url(session, uri),
                      data=data,
                      params=self._utf8_urlencode(params),
                      files=files,
                      headers=headers,
                      allow_redirects=allow_redirects,
                      timeout=40,
                      cookies=None,
                      verify=False)
        # Store the last session object
        session.last_resp = resp

        #print 'test method_name=',method_name, resp

        return resp
    #----------------
    def _get_url(self, session, uri):
        """
        Helper method to get the full url
        """
        url = session.url
        if uri:
            slash = '' if uri.startswith('/') else '/'
            url = "%s%s%s" % (session.url, slash, uri)
        return url
    #----------------
    def _utf8_urlencode(self, data):

        if self._is_string_type(data):
            return data.encode('utf-8')

        if not isinstance(data, dict):
            return data

        utf8_data = {}
        for k, v in data.items():
            if self._is_string_type(v):
                v = v.encode('utf-8')
            utf8_data[k] = v
        return urlencode(utf8_data)
    #----------------
    def _format_data_according_to_header(self, session, data, headers):
        headers = self._merge_headers(session, headers)
        if data is not None and headers is not None and 'Content-Type' in headers and not self._is_json(data):
            if headers['Content-Type'].find("application/json") != -1:
                data = json.dumps(data)
            elif headers['Content-Type'].find("application/x-www-form-urlencoded") != -1:
                data = self._utf8_urlencode(data)
        else:
            data = self._utf8_urlencode(data)

        return data
    #----------------
    def _format_data_to_log_string_according_to_header(self, data, headers):
        dataStr = "<empty>"
        if data is not None and headers is not None and 'Content-Type' in headers:
            if (headers['Content-Type'].find("application/json") != -1) or \
                    (headers['Content-Type'].find("application/x-www-form-urlencoded") != -1):
                if isinstance(data, bytes):
                    dataStr = data.decode('utf-8')
                else:
                    dataStr = data
            else:
                dataStr = "<" + headers['Content-Type'] + ">"

        return dataStr

    @staticmethod
    def _merge_headers(session, headers):
        if headers is None:
            headers = {}
        else:
            headers = headers.copy()

        headers.update(session.headers)

        return headers

    @staticmethod
    def _is_json(data):
        try:
            json.loads(data)
        except (TypeError, ValueError):
            return False
        return True

    @staticmethod
    def _is_string_type(data):
        if PY3 and isinstance(data, str):
            return True
        elif not PY3 and isinstance(data, unicode):
            return True
        return False
    #------------------------
    #功能: PMSAV3\product\autotest\TranslationMap.csv 内容转换成 dict
    def CsvToDict(self, FileName):
        print u'转换.csv为dict'
        f = open(FileName,'r')
        csvlist = f.readlines()
        f.close()
        retDict = {}
        #print len(csvlist)
        for i in range(0,len(csvlist)):
            tempList = csvlist[i].split(',')
            retDict[tempList[0].decode("utf-8")]=tempList[1].replace('\n','') #.decode("utf-8").encode("gbk")
        dtrobot = robot.utils.DotDict(retDict) #该方法是dict转为robot格式的dict
        return  dtrobot
    #-------------------------------------
    def get_file_path(self):
        return self.recordPath #文件夹file的路径
    #-------------------------------------
    def init_dest_ip(self, dest_ip):
        g_var.variables.update( {'ip':dest_ip} )
    #-------------------------------------
    #参考 get_request_data/post_request_wrapped 改编的,
    #    为的就是上述2个函数没有必要覆盖不同的应用场景
    def get_login_request(self, my_session, my_cookie, obj_id, obj_type, paraval ):
        para = { u'objectid':obj_id,
                 u'type':obj_type,
                 u'paranum':str(len( paraval )),
                 u'paraval':json.dumps( paraval )}
        header = {u'Content-Type': u'application/x-www-form-urlencoded'}
        header[u'Cookie'] = my_cookie
        header[u'Referer']= u'https://'+g_var.variables.get(u'ip')
        try:
            resp = self.post_request(alias=u'pmsa',uri=u'/power/cgi-bin/main.fcgi',headers=header,data=para,timeout=60,session=my_session)
        except:
            print self.fileName, sys._getframe().f_lineno, u'login , time out'
            return None
        #----
        if resp.status_code != 200:
            print self.fileName, sys._getframe().f_lineno, u'login , status_code != 200'
            return None
        #----
        #if login: #仅仅在登录时,需要备份cookie
        my_cookie += (u'PMSA_SESSION_ID='+resp.cookies['PMSA_SESSION_ID'])
        #----
        context = self.to_json( resp.text )

        #-------------------------------
        if context.get(u'result') != u'ok':
            print self.fileName, sys._getframe().f_lineno, context
            print self.fileName, sys._getframe().f_lineno, u'login failure .1'
            return None
        if context.get(u'objectid') != obj_id:
            print self.fileName, sys._getframe().f_lineno, u'login failure .2'
            return None
##        if context.get(u'data') == [{u'userlevel': u'3'}]:
##            print self.fileName, sys._getframe().f_lineno, u'登录成功:', 
        #print self.fileName, sys._getframe().f_lineno, context.get(u'data')
        return my_cookie
        
    #-------------------------------------
    #paraval的格式 - [{},{},{}]
    #       是一个列表,元素是一个或多个字典
    #其他说明:关于函数中的变量 data
    #读取历史数据时,web抓包得到u'paranum'一直是0,实测验证：都用 u'paranum':str(len( paraval )),也可以
    #读取历史数据时,web抓包得到u'paranum'一直是1,实测验证：都用 u'paranum':str(len( paraval )),也可以
    #读取历史事件时,web抓包得到u'paranum',u'dataindex'一直是0,实测验证：都用 u'paranum':str(len( paraval )),也可以;没有u'dataindex':'0',也可以
    def get_request_data(self, obj_id, obj_type, paraval, time_out=180 ):
        data = { u'objectid':obj_id,
                 u'type':obj_type,
                 u'paranum':str(len( paraval )),
                 u'paraval':json.dumps( paraval )}
        
        context = self.post_request_wrapped( data, time_out=time_out )
        if context == None:
            return None
        if context.get(u'result') != u'ok':
            #print self.fileName, sys._getframe().f_lineno, u'result is not ok', context
            return None
        if context.get(u'objectid') != obj_id:
            print self.fileName, sys._getframe().f_lineno, u'object id is not equ'
            return None
        return context.get(u'data')  
    #-------------------------------------
    #入参类型: para - list
    #如果入参 time_out < 60, 可以默认是只执行一次while循环体中的代码
    def post_request_wrapped( self, para, time_out=180 ):
        now = time.time()
        while time.time() - now < time_out:
            header = {u'Content-Type': u'application/x-www-form-urlencoded; charset=UTF-8'}
            header[u'Cookie'] = g_var.variables.get('cookie')
            header[u'Referer']= u'https://'+g_var.variables.get(u'ip')
            try:
                resp = self.post_request(alias=u'pmsa',uri=u'/power/cgi-bin/main.fcgi',headers=header,data=para,timeout=60,session=g_var.variables.get('session'))
            except:
                print self.fileName, sys._getframe().f_lineno, u'INFO : post_request time out'
                self.login_web()
                continue
            if resp.status_code != 200:
                print self.fileName, sys._getframe().f_lineno, u'INFO : post_request status_code != 200'
                self.login_web()
                continue
            return self.to_json( resp.text )
        #----
        print self.fileName, sys._getframe().f_lineno, u'warning : post_request , time >',time_out,'s, return  None'
        return None
    #-------------------------------------
    def get_sig_type_by_name(self, sig_type_ch):
        sig_types = {u'模拟量':1,
                    u'数字量':2,
                    u'告警量':3, #功能:获取的是指定设备对应的、所有的 告警名称/id,不是当前的告警量
                    u'控制量':4,
                    u'参数量':5,
                    u'记录量':6,
                    u'统计量':7,
                    u'设备信息':8
                    }
        sig_type = sig_types.get( sig_type_ch )
        if sig_type == None:
            print self.fileName, sys._getframe().f_lineno, u'Err:信号类型=', sig_type_ch
            return 0
        return sig_type
    #-------------------------------------
    #功能:获取特殊参数(告警参数/存储属性)目标的英文字符串
    def get_dest_EN(self, dest_ch):
        CN_EN = {u'存储周期':u'period',
                  u'绝对阈值':u'absolute_threshold',
                  u'百分比阈值':u'percent_threshold',
                  u'告警级别':u'alm_level',
                  u'告警延时':u'alm_delay',
                  u'告警阈值':u'alm_threshold',
                  u'告警回差':u'alm_backlash',
                  u'告警干接点':u'alm_relay',
                  u'值':u'value'
                  }
        ret = CN_EN.get( dest_ch )
        if ret == None:
            print self.fileName, sys._getframe().f_lineno, u'Err:目标类型=', dest_ch
        return ret
    #-------------------------------------
    #功能:获取 所有设备的 SID
    #返回值类型:列表, 内容是:一个sid对应一个字典
    #返回值示例:[{u'sid': u'281543696187392'}, {u'sid': u'563018672898048'}]
    def get_lst_dev_id(self):
        paraval = [ {u'sid': u'0'} ]
        return self.get_request_data(u'device', u'list', paraval)
    #-------------------------------------
    #功能:获取 所有设备的 设备属性
    #返回值类型:列表
    #返回值示例:[{u'device type': u'CSU', u'device name': u'CSU', u'sid': u'281543696187392'},
    #          {u'device type': u'\u80fd\u6e90\u7cfb\u7edf', u'device name': u'\u80fd\u6e90\u7cfb\u7edf', u'sid': u'563018672898048'}]
    def get_lst_dev_attr(self):
        lst_dev_id = self.get_lst_dev_id( )
        if lst_dev_id == None:
            return None
        lst_dev_attr = self.get_request_data( u'device', u'attr_get', lst_dev_id)
        return lst_dev_attr    
    #-------------------------------------
    #功能:获取 指定设备&指定变量类型的 信号SID, 例如:整流器_1,模拟量 或 整流器_1,数字量
    #返回值格式: [ {u'sid':sig_sid} ]
    def get_sig_id_by_dev_sig(self, dev_name_ch, sig_type_ch):
        sig_type = self.get_sig_type_by_name( sig_type_ch )
        dev_sid  = self.get_dev_sid_by_dev_name( dev_name_ch )  
        sig_sid  = str( int(dev_sid) + ( int(sig_type)<<28 ) )
        return [ {u'sid':sig_sid} ]
    #-------------------------------------
    #功能:获取 指定设备&指定变量类型的 信号attr, 例如:整流器_1,模拟量 或 整流器_1,数字量
    #返回值格式: 列表,成员是字典,一个字典包含一条变量的好多信息(名称/id/值/单位 等)
    def get_sig_attr_by_dev_sig(self, dev_name_ch, sig_type_ch):
        lst_sig_sid = self.get_sig_id_by_dev_sig( dev_name_ch, sig_type_ch )
        return self.get_request_data( u'signal', u'val_get', lst_sig_sid)

    #----
    #功能:匹配字符串
    def match_string(self,src):
        lst_str_match = g_var.variables.get('str_match')
        if lst_str_match == None:
            lst_str_match = self.openCsvAsList( self.recordPath + '\\string_match.csv' )
            g_var.variables.update({'str_match':lst_str_match})
        for s in lst_str_match:
            lst = s.split(',')
            if lst[0] == src:
                return lst[1]
        return src
    #-------------------------
    #功能:获取设备SID
    #入参:指定的 设备名称,设备数量大于1时,示例:整流器_1 (web返回值就是这样的,下划线连接的)
    #返回值格式: u'xxxx'
    def get_dev_sid_by_dev_name(self, dev_name_ch):
        lst_dev_attr = self.get_lst_dev_attr( )
        if lst_dev_attr == None:
            return ''
        if '_' in dev_name_ch:
            lst  = dev_name_ch.split('_')
            name = self.match_string( lst[0] )
            dev_name_ch = name+'_'+lst[1]
        else:
            dev_name_ch = self.match_string( dev_name_ch )
        for item in lst_dev_attr:
            if item.get(u'device name') == dev_name_ch:
                return item.get(u'sid')
        print self.fileName, sys._getframe().f_lineno, u'Err,没有从CSU返回的设备列表中找到设备:', dev_name_ch
        return ''
    #-------------------------
    #功能:获取信号(指定 设备名称/信号类型/信号名称)的sid
    #入参:设备名称,信号类型,信号名称
    def get_sig_sid(self, dev_name_ch, sig_type_ch, sig_name_ch):
        lst_sig_attr = self.get_sig_attr_by_dev_sig(dev_name_ch, sig_type_ch)
        if lst_sig_attr == None:
            return ''
        for item in lst_sig_attr:
            if sig_name_ch == item.get(u'full_name'):
                sid = item.get(u'sid')
                break
        else:
            print self.fileName, sys._getframe().f_lineno, u'信号名称错误:', sig_name_ch
            return ''
        return sid
    #-------------------------
    #功能:获取信号的值 示例: 直流配电,直流电压 或 直流配电,ALL
    #返回值: sig_name_ch 是指定某一个信号时, 返回值是表示该信号的值的 字符串
    #       sig_name_ch in [u'ALL',u'all',u'All']时,返回值是个列表： [{sig_name:val},{sig_name:val}]
    def get_web_data_ana(self, dev_name_ch, sig_name_ch, sig_index=u'0'):
        lst_sig_attr = self.get_sig_attr_by_dev_sig( dev_name_ch, u'模拟量')
        if lst_sig_attr == None:
            return [] if sig_name_ch in [u'ALL',u'all',u'All'] else ''
        if sig_name_ch in [u'ALL',u'all',u'All']:
            return self.parse_web_data_mul( lst_sig_attr )
        else:
            return self.parse_web_data_single(  sig_name_ch, sig_index, lst_sig_attr )
    #-------------------------
    #功能:获取信号的值(状态量)
    #返回值是个列表： [{sig_name:val},{sig_name:val}]
    def get_web_data_dig(self, dev_name_ch, sig_name_ch, sig_index=u'0'):
        lst_sig_attr = self.get_sig_attr_by_dev_sig( dev_name_ch, u'数字量')
        if lst_sig_attr == None:
            return [] if sig_name_ch in [u'ALL',u'all',u'All'] else ''
        if sig_name_ch in [u'ALL',u'all',u'All']:
            return self.parse_web_data_mul( lst_sig_attr )
        else:
            return self.parse_web_data_single(  sig_name_ch, sig_index, lst_sig_attr )
    #-------------------------
    #功能:获取信号的值(记录量) -- 参考数据字典的K列,有record data类型
    #返回值是个列表： [{sig_name:val},{sig_name:val}]
    def get_web_data_record(self, dev_name_ch, sig_name_ch, sig_index=u'0'):
        lst_sig_attr = self.get_sig_attr_by_dev_sig( dev_name_ch, u'记录量')
        if lst_sig_attr == None:
            return [] if sig_name_ch in [u'ALL',u'all',u'All'] else ''
        if sig_name_ch in [u'ALL',u'all',u'All']:
            return self.parse_web_data_mul( lst_sig_attr )
        else:
            return self.parse_web_data_single(  sig_name_ch, sig_index, lst_sig_attr )
    #-------------------------
    #功能:获取信号的值(模拟量&状态量)
    #返回值是个列表： [{sig_name:val},{sig_name:val}]
    def get_web_data_real(self, dev_name_ch, sig_name_ch, sig_index=u'0'):
        lst_sig_sid  = self.get_sig_id_by_dev_sig( dev_name_ch, u'模拟量')
        lst_sig_sid.extend( self.get_sig_id_by_dev_sig( dev_name_ch, u'数字量' ) )
        lst_sig_sid.extend( self.get_sig_id_by_dev_sig( dev_name_ch, u'记录量' ) )
        lst_sig_attr = self.get_request_data( u'signal', u'val_get', lst_sig_sid)
        if lst_sig_attr == None:
            return [] if sig_name_ch in [u'ALL',u'all',u'All'] else ''
        if sig_name_ch in [u'ALL',u'all',u'All']:
            return self.parse_web_data_mul( lst_sig_attr )
        else:
            return self.parse_web_data_single(  sig_name_ch, sig_index, lst_sig_attr )
    #-------------------------
    #功能:获取信号的值 示例: 直流配电,直流电压 或 直流配电,ALL
    #返回值: sig_name_ch 是指定某一个信号时, 返回值是表示该信号的值的 字符串
    #       sig_name_ch in [u'ALL',u'all',u'All']时,返回值是个列表： [{sig_name:val},{sig_name:val}]
    #意外发现:当信号值的Vindex > 1时,入参有两种表达方式,以“市电配置[1]”为例:
    #方式1：sig_name_ch=u'市电配置[1]', sig_index=u'0'
    #方式2：sig_name_ch=u'市电配置',    sig_index=u'1'
    def get_web_data_para(self, dev_name_ch, sig_name_ch, dest=u'值', sig_index=u'0'):
        dst = self.get_dest_EN( dest )
        if dst == u'value':
            return self.get_web_para_common( dev_name_ch, sig_name_ch, sig_index, dst )
        elif dst in [u'alm_level',u'alm_delay',u'alm_backlash',u'alm_relay']:
            return self.get_web_para_alarm( dev_name_ch, sig_name_ch, sig_index, dst )
        elif dst in [u'period',u'absolute_threshold',u'percent_threshold']:
            return self.get_web_para_save( dev_name_ch, sig_name_ch, sig_index, dst )
        else:
            print self.fileName, sys._getframe().f_lineno, u'Err:dest=', dest
            return ''      
    #-------------------------
    #功能:获取信号的值 示例: 直流配电,直流电压 或 直流配电,ALL
    #返回值: sig_name_ch 是指定某一个信号时, 返回值是表示该信号的值的 字符串
    #       sig_name_ch in [u'ALL',u'all',u'All']时,返回值是个列表： [{sig_name:val},{sig_name:val}]
    def get_web_para_common(self, dev_name_ch, sig_name_ch, sig_index=u'0', dest=u'value' ):
        lst_sig_attr = self.get_sig_attr_by_dev_sig( dev_name_ch, u'参数量')
        if lst_sig_attr == None:
            return [] if sig_name_ch in [u'ALL',u'all',u'All'] else ''
        if sig_name_ch in [u'ALL',u'all',u'All']:
            return self.parse_web_data_mul( lst_sig_attr )
        else:
            return self.parse_web_data_single(  sig_name_ch, sig_index, lst_sig_attr )
    #-------------------------
    #功能:获取 告警参数 的值,一次一个值
    #返回值: 返回值是表示该信号的值的 字符串
    #注意：若目标信号(告警参数)对应 告警量有下标,则此时下标(sig_index)必须为1
    def get_web_para_alarm(self, dev_name_ch, sig_name_ch, sig_index=u'0', dest=u'alm_level' ):
        lst_sig_attr = self.get_sig_attr_by_dev_sig( dev_name_ch, u'告警量')
        if lst_sig_attr == None:
            return ''
        if dest not in [u'alm_level',u'alm_delay',u'alm_backlash',u'alm_relay']:
            print self.fileName, sys._getframe().f_lineno, 'Err:dest=', dest
            return ''
        return self.parse_web_para_single(  sig_name_ch, sig_index, dest, lst_sig_attr )
    #-------------------------
    #功能:获取信号(存储属性)的值,一次一个值
    #返回值: 返回值是表示该信号的值的 字符串
    #注意：若目标信号(模拟量/数字量)对应 实时量有下标,则此时下标(sig_index)必须为1
    def get_web_para_save(self, dev_name_ch, sig_name_ch, sig_index=u'0', dest=u'period' ):
        lst_sig_sid  = self.get_sig_id_by_dev_sig( dev_name_ch, u'模拟量')
        lst_sig_sid.extend( self.get_sig_id_by_dev_sig( dev_name_ch, u'数字量' ) )
        lst_sig_attr = self.get_request_data( u'signal', u'val_get', lst_sig_sid)
        if lst_sig_attr == None:
            return ''
        if dest not in [u'period',u'absolute_threshold',u'percent_threshold']:
            print self.fileName, sys._getframe().f_lineno, 'Err:dest=', dest
            return ''
        return self.parse_web_para_single(  sig_name_ch, sig_index, dest, lst_sig_attr )
    #-------------------------
    #功能:一次只能设置参数的一个值(针对:告警级别/告警干接点 时,需要分2次设置)
    #说明:告警参数/存储属性 的sig_index不为0时,必须为1
    #    告警参数中web内容的sig_index看上去都是0,实际上有的项对应的的sig_index取值可以是[1,3]
    #    但是 设置/获取 时,sig_index必须为1
    def set_web_data_para(self, dev_name_ch, sig_name_ch, sig_index=u'0', dest={} ):
        key, val = dest.items()[0]
        print u'设置参数量：',dev_name_ch, sig_name_ch, key, '=',val       
        dst = self.get_dest_EN( key )
        val = str(val)
        #----兼容参数名称中含有index信息,例如: 电池组容量[1] ,此时sig_index必须等于u'0'(可以采用不输入该参数来实现:)
        if sig_index == u'0':
            sig_name_ch,sig_index = self.split_sig_name( sig_name_ch )
        #----兼容end
        if dst == u'value':
            return self.set_web_para_common( dev_name_ch, sig_name_ch, sig_index, val )
        elif dst in [u'alm_level',u'alm_delay',u'alm_backlash',u'alm_relay']:
            return self.set_web_para_alarm(  dev_name_ch, sig_name_ch, sig_index, dst, val )
        elif dst in [u'period',u'absolute_threshold',u'percent_threshold']:
            return self.set_web_para_save(  dev_name_ch, sig_name_ch, sig_index, dst, val )
        else:
            print self.fileName, sys._getframe().f_lineno, u'Err:key=', key
            return False
    #-------------------------
    #功能:设置告警参数, 只能设置 ‘告警级别/告警延时/告警回差/告警干接点’
    def set_web_para_alarm(self, dev_name_ch, sig_name_ch, sig_index, dst, val ):
        #----特殊处理:告警干接点 当前为0时,此时还设置为0,web返回值是error
        if dst == u'alm_relay' and val == u'0':
            if val == self.get_web_data_para( dev_name_ch, sig_name_ch, dest=u'告警干接点', sig_index=sig_index):
                #print self.fileName, sys._getframe().f_lineno, u'告警干接点的当前值与目标值相同,且为0', dev_name_ch, sig_name_ch
                return True
        
        #----
        lst_sig_attr = self.get_sig_attr_by_dev_sig( dev_name_ch, u'告警量')
        if lst_sig_attr == None:
            return False
        #----
        sig_name_ch = self.connect_sig_name_index( sig_name_ch, sig_index )
        for item in lst_sig_attr:
            if sig_name_ch == item.get(u'full_name'):
                paraval = [ { dst:val, u'sid':item.get(u'sid') } ]
                break
        else:
            print self.fileName, sys._getframe().f_lineno, u'Err,匹配参数名称:', sig_name_ch
            return False
        #----
        lst_data = self.get_request_data( u'signal', u'val_set', paraval)
        #成功 lst_data==[], 失败 lst_data==None
        return lst_data==[]
    #-------------------------
    #功能:设置存储属性, 只能设置 ‘存储周期/绝对阈值/百分比阈值’
    def set_web_para_save(self, dev_name_ch, sig_name_ch, sig_index, dst, val ):
        lst_sig_sid  = self.get_sig_id_by_dev_sig( dev_name_ch, u'模拟量')
        lst_sig_sid.extend( self.get_sig_id_by_dev_sig( dev_name_ch, u'数字量' ) )
        lst_sig_attr = self.get_request_data( u'signal', u'val_get', lst_sig_sid)
        if lst_sig_attr == None:
            return False
        #----
        sig_name_ch = self.connect_sig_name_index( sig_name_ch, sig_index )
        for item in lst_sig_attr:
            if sig_name_ch == item.get(u'full_name'):
                paraval = [ { dst:val, u'sid':item.get(u'sid') } ]
                break
        else:
            print self.fileName, sys._getframe().f_lineno, u'Err,匹配参数名称:', sig_name_ch
            return False
        #----
        lst_data = self.get_request_data( u'signal', u'val_set', paraval)
        #成功 lst_data==[], 失败 lst_data==None
        return lst_data==[]
    #-------------------------
    #功能:设置普通参数, 只能设置 值
    def set_web_para_common(self, dev_name_ch, sig_name_ch, sig_index, val ):
        lst_sig_sid  = self.get_sig_id_by_dev_sig( dev_name_ch, u'参数量')
        lst_sig_attr = self.get_request_data( u'signal', u'val_get', lst_sig_sid )
        if lst_sig_attr == None:
            return False
        #----
        sig_name_ch = self.connect_sig_name_index( sig_name_ch, sig_index )
        for item in lst_sig_attr:
            if sig_name_ch == item.get(u'full_name'):
                paraval = [ { u'value':val, u'sid':item.get(u'sid') } ]
                break
        else:
            print self.fileName, sys._getframe().f_lineno, u'Err,匹配参数名称:', sig_name_ch
            return False
        #----
        lst_data = self.get_request_data( u'signal', u'val_set', paraval)
        #成功 lst_data==[], 失败 lst_data==None
        return lst_data==[]
    #-------------------------
    #功能:判断告警是否存在,不存在就返回 [] ;  存在就返回告警字符串(包括dev_name)列表: [ 'alm_str', 'alm_str' ]
    #功能:输入指定告警的名称（可以是告警名称的几个关键字，例如“整流器输入电压低”，可描述为“输入电压低”）
    def get_web_alarm_special(self, alm_name):
        print u'查询指定告警：',alm_name
        alm_name_List=re.split('[-_]',alm_name.strip()) #使用正则分割多个分隔符
##        print alm_name_List
        lst_alm = self.get_real_alm_list_all_dev( )
        ret = []
        almNameList=[]
        for alm in lst_alm:
            if alm_name_List[0] in alm:
                almList=alm.split(':')
                if '-' in alm_name and len(alm_name_List)==2 and (alm_name_List[1] in almList[0]):
                    ret.append( alm )
                elif '_' in alm_name and len(alm_name_List)==2 and (alm_name_List[1] in almList[1]):
                    ret.append( alm )
                elif (len(alm_name_List)==3
                      and (alm_name_List[1] in almList[1])
                      and (alm_name_List[2] in almList[0])):
                    ret.append( alm )
                elif ('-' not in alm_name) and ('_' not in alm_name):
                    ret.append( alm )
        for alm in ret:
            almList=alm.split(':')
            if '_' in almList[0] and ']' not in almList[1]:
                dev_num=almList[0].split('_')[1]
                almName=almList[1]+'-'+dev_num
                almNameList.append(almName)
            elif ']' in almList[1] and '_' not in almList[0]:
                sig_num=almList[1].strip(']').split('[')[1]
                almName=almList[1]+'_'+sig_num
                almNameList.append(almName)
            elif '_' not in almList[0] and ']' not in almList[1]:
                almNameList.append(almList[1])
            else:
                dev_num=almList[0].split('_')[1]
                sig_num=almList[1].strip(']').split('[')[1]
                almName=almList[1]+'_'+sig_num+'-'+dev_num
                almNameList.append(almName)                
        return almNameList
    #-------------------------
    #功能:获取实时告警列表-系统所有告警
    #返回值:[ 'alm_str', 'alm_str' ]
    #      其中 alm_str 的格式:  dev_name_ch:alm_name , 与获取单个设备告警的区别是 有 dev_name_ch:
    #      示例:  [ 单组电池_1:电池回路断, 单组电池_1:电池温度无效 ]
    def get_real_alm_list_all_dev(self):
        lst_real_alm_attr = []
        lst_dev_id = self.get_lst_dev_id( )
        if lst_dev_id == None:
            return []
        for dev_id in lst_dev_id:
            alarm_sids  = self.get_request_data( u'real_alarm', u'list', [dev_id] )
            if alarm_sids == None:
                return []
            for alm_sim in alarm_sids:
                one_alm_info = self.get_request_data( u'real_alarm', u'attr_get', [alm_sim] )
                if one_alm_info == None:
                    continue                         
                lst_real_alm_attr.append( one_alm_info )
        #lst_real_alm_attr内容格式: [ [{}],[{}] ]  - 2条告警的情况
        ret = []
        for item in lst_real_alm_attr:
            ret.append( item[0].get('full_name') )
        return ret
    #-------------------------
    #功能:获取实时告警列表-指定设备
    #返回值:[ 'alm_str', 'alm_str' ]
    #      其中 alm_str 的格式:  alm_name , 与获取所有设备告警的区别是没有 dev_name_ch:
    #      示例:  [ 电池回路断, 电池温度无效 ]
    def get_real_alm_list_one_dev(self, dev_name_ch):
        lst_real_alm_attr = []
        dev_sid     = self.get_dev_sid_by_dev_name(dev_name_ch)
        alarm_sids  = self.get_request_data( u'real_alarm', u'list', [{u'sid':dev_sid}] )
        if alarm_sids == None:
            return []
        for alm_sim in alarm_sids:
            one_alm_info = self.get_request_data( u'real_alarm', u'attr_get', [alm_sim] )
            if one_alm_info == None:
                continue                         
            lst_real_alm_attr.append( one_alm_info )
        #----
        #lst_real_alm_attr内容格式: [ [{}],[{}] ]  - 2条告警的情况
        ret = []
        for item in lst_real_alm_attr:
            alm_str = item[0].get(u'full_name')
            ret.append( alm_str.split(u':')[1] )
        return ret
    #-------------------------
    #功能:返回指定 设备名&告警名 的 告警级别or告警干接点or告警时间
    #指定告警不存在时的返回值:  u''
    def get_real_alm_val_one_dev(self, dev_name_ch, alm_name_ch, dest=u'告警级别'):
        lst_real_alm_attr = []
        dev_sid     = self.get_dev_sid_by_dev_name(dev_name_ch)
        alarm_sids  = self.get_request_data( u'real_alarm', u'list', [{u'sid':dev_sid}] )
        if alarm_sids == None:
            return u''
        for alm_sim in alarm_sids:
            one_alm_info = self.get_request_data( u'real_alarm', u'attr_get', [alm_sim] )
            if one_alm_info == None:
                continue                         
            lst_real_alm_attr.append( one_alm_info )
        #lst_real_alm_attr内容格式: [ [{}],[{}] ]  - 2条告警的情况
        ret = []
        for item in lst_real_alm_attr:
            alm_name  = item[0].get(u'full_name')
            alm_level = item[0].get(u'alm_level' )
            alm_relay = item[0].get(u'alm_relay' )
            alm_time  = item[0].get(u'start_time')
            if alm_name == dev_name_ch+u':'+alm_name_ch:
                if dest == u'告警干接点':
                    return alm_relay
                elif dest == u'告警级别':
                    return alm_level
                elif dest == u'告警时间':
                    return alm_time
                else:
                    print self.fileName, sys._getframe().f_lineno, u'Err,入参:', dest
                    return u''
        #----
        print self.fileName, sys._getframe().f_lineno, u'Err,告警不存在:', dev_name_ch, alm_name_ch
        return u''
    #-------------------------
    #功能:获取实时告警列表-系统所有告警
    #返回值:[ 'alm_str', 'alm_str' ]
    #      其中 alm_str 的格式:  dev_name_ch:alm_name , 与获取单个设备告警的区别是 有 dev_name_ch:
    #      示例:  [ 单组电池_1:电池回路断, 单组电池_1:电池温度无效 ]
    def get_real_alm_info_list_all_dev(self):
        lst_real_alm_attr = []
        lst_dev_id = self.get_lst_dev_id( )
        if lst_dev_id == None:
            print self.fileName, sys._getframe().f_lineno, u'Err'
            return []
        for dev_id in lst_dev_id:
            alarm_sids  = self.get_request_data( u'real_alarm', u'list', [dev_id] )
            if alarm_sids == None:
                print self.fileName, sys._getframe().f_lineno, u'Err'
                return []
            for alm_sim in alarm_sids:
                one_alm_info = self.get_request_data( u'real_alarm', u'attr_get', [alm_sim] )
                if one_alm_info == None:
                    continue                         
                lst_real_alm_attr.append( one_alm_info )
        #lst_real_alm_attr内容格式: [ [{}],[{}] ]  - 2条告警的情况
        ret = []
        for item in lst_real_alm_attr:
            alm_name  = item[0].get(u'full_name')
            alm_level = item[0].get(u'alm_level' )
            alm_relay = item[0].get(u'alm_relay' )
            alm_time  = item[0].get(u'start_time')
            ret.append( ','.join( [alm_time, alm_level, alm_relay, alm_name] ) )
        return ret
    #-------------------------
    def get_real_alm_cnt(self):
        lst_dev_id = self.get_lst_dev_id( )
        if lst_dev_id == None:
            print self.fileName, sys._getframe().f_lineno, u'Err'
            return 0
        cnt = 0
        for dev_id in lst_dev_id:
            alarm_sids  = self.get_request_data( u'real_alarm', u'list', [dev_id] )
            if alarm_sids == None:
                print self.fileName, sys._getframe().f_lineno, u'Err'
                return 0
            for alm_sim in alarm_sids:
                one_alm_info = self.get_request_data( u'real_alarm', u'attr_get', [alm_sim] )
                if one_alm_info == None:
                    continue                         
                cnt += 1
        return cnt
    #-------------------------
    #功能:获取实时告警列表-指定设备
    #返回值:[ 'alm_str', 'alm_str' ]
    #      其中 alm_str 的格式:  alm_name , 与获取所有设备告警的区别是没有 dev_name_ch:
    #      示例:  [ 电池回路断, 电池温度无效 ]
    def get_real_alm_info_list_one_dev(self, dev_name_ch):
        lst_real_alm_attr = []
        dev_sid     = self.get_dev_sid_by_dev_name(dev_name_ch)
        alarm_sids  = self.get_request_data( u'real_alarm', u'list', [{u'sid':dev_sid}] )
        if alarm_sids == None:
            return []
        for alm_sim in alarm_sids:
            one_alm_info = self.get_request_data( u'real_alarm', u'attr_get', [alm_sim] )
            if one_alm_info == None:
                continue                         
            lst_real_alm_attr.append( one_alm_info )
        #----
        #lst_real_alm_attr内容格式: [ [{}],[{}] ]  - 2条告警的情况
        ret = []
        for item in lst_real_alm_attr:
            #alm_name  = item[0].get(u'full_name')
            alm_name  = item[0].get(u'full_name').split(u':')[1]
            alm_level = item[0].get(u'alm_level' )
            alm_relay = item[0].get(u'alm_relay' )
            alm_time  = item[0].get(u'start_time')
            ret.append( ','.join( [alm_time, alm_level, alm_relay, alm_name] ) )
        return ret
    #-------------------------
    #功能:返回一条实时告警信息-指定设备&告警名称,
    #返回值格式：列表 [alm_time, alm_level, alm_relay, alm_name]
    #返回值示例：
    def get_one_real_alm_info(self, dev_name_ch, sig_name_ch ):
        lst_real_alm_attr = []
        dev_sid     = self.get_dev_sid_by_dev_name(dev_name_ch)
        alarm_sids  = self.get_request_data( u'real_alarm', u'list', [{u'sid':dev_sid}] )
        if alarm_sids == None:
            return []
        for alm_sim in alarm_sids:
            one_alm_info = self.get_request_data( u'real_alarm', u'attr_get', [alm_sim] )
            if one_alm_info == None:
                continue                         
            lst_real_alm_attr.append( one_alm_info )
        #----
        #lst_real_alm_attr内容格式: [ [{}],[{}] ]  - 2条告警的情况
        for item in lst_real_alm_attr:
            #alm_name  = item[0].get(u'full_name')
            alm_name  = item[0].get(u'full_name').split(u':')[1]
            alm_level = item[0].get(u'alm_level' )
            alm_relay = item[0].get(u'alm_relay' )
            alm_time  = item[0].get(u'start_time')
            if alm_name == sig_name_ch:
                return [ alm_time, alm_level, alm_relay ]
        #----
        return []    
##    #-------------------------
##    #功能:返回指定 告警名(含设备名) 的 告警级别or告警干接点or告警时间
##    #指定告警不存在时的返回值:  u''
##    def get_real_alm_val(self, alm_name_ch, dest=u'告警级别'):
##        lst_real_alm_attr = []
##        lst_dev_id = self.get_lst_dev_id( )
##        if lst_dev_id == None:
##            return u''
##        for dev_id in lst_dev_id:
##            alarm_sids  = self.get_request_data( u'real_alarm', u'list', [dev_id] )
##            if alarm_sids == None:
##                continue
##            for alm_sim in alarm_sids:
##                one_alm_info = self.get_request_data( u'real_alarm', u'attr_get', [alm_sim] )
##                if one_alm_info == None:
##                    continue                         
##                lst_real_alm_attr.append( one_alm_info )
##        #----
##        if lst_real_alm_attr == []:
##            return u''
##        #lst_real_alm_attr内容格式: [ [{}],[{}] ]  - 2条告警的情况
##        ret = []
##        for item in lst_real_alm_attr:
##            alm_name  = item[0].get(u'full_name')
##            alm_level = item[0].get(u'alm_level' )
##            alm_relay = item[0].get(u'alm_relay' )
##            alm_time  = item[0].get(u'start_time')
##            if alm_name == alm_name_ch:
##                if dest == u'告警干接点':
##                    return alm_relay
##                elif dest == u'告警级别':
##                    return alm_level
##                elif dest == u'告警时间':
##                    return alm_time
##                else:
##                    print self.fileName, sys._getframe().f_lineno, u'Err,入参:', dest
##                    return u''
##        #----
##        print self.fileName, sys._getframe().f_lineno, u'Err,告警不存在:', alm_name_ch
##        return u''
    #-------------------------
    def judge_real_data_equ(self, dev_name_ch, sig_name_ch, sig_index=u'0', value=u'0'):
        real_val = self.get_web_data_real( dev_name_ch, sig_name_ch, sig_index)
        print u'信号量数据值=', real_val
        return abs(float(real_val) - float(value)) < 0.0001
    #-------------------------
    def judge_real_data_more_than(self, dev_name_ch, sig_name_ch, sig_index=u'0', value=u'0'):
        real_val = self.get_web_data_real( dev_name_ch, sig_name_ch, sig_index)
        print u'信号量数据值=', real_val
        return float(real_val) > float(value)
    #-------------------------
    def judge_real_data_more_than_or_equ(self, dev_name_ch, sig_name_ch, sig_index=u'0', value=u'0'):
        real_val = self.get_web_data_real( dev_name_ch, sig_name_ch, sig_index)
        print u'信号量数据值=', real_val
        return float(real_val) >= float(value)
    #-------------------------
    def judge_real_data_less_than(self, dev_name_ch, sig_name_ch, sig_index=u'0', value=u'0'):
        real_val = self.get_web_data_real( dev_name_ch, sig_name_ch, sig_index)
        print u'信号量数据值=', real_val
        return float(real_val) < float(value)
    #-------------------------
    def judge_real_data_less_than_or_equ(self, dev_name_ch, sig_name_ch, sig_index=u'0', value=u'0'):
        real_val = self.get_web_data_real( dev_name_ch, sig_name_ch, sig_index)
        print u'信号量数据值=', real_val
        return float(real_val) <= float(value)
    #-------------------------
    #功能:判断指定设备的指定告警是否存在
    #说明:参数alm_name_ch,内容是web上的告警名称,如果涉及到signal_index,则以web显示内容为准
    #Pengy2019/12/24:修改为只匹配告警名称，支持模糊匹配
    def judge_alm_exist(self, alm_name_ch ):
        return self.get_web_alarm_special( alm_name_ch) !=[]
    #-------------------------
    #功能:判断指定设备的指定告警是否存在
    #说明:参数alm_name_ch,内容是web上的告警名称,如果涉及到signal_index,则以web显示内容为准
    #Pengy2019/12/24:修改为只匹配告警名称，支持模糊匹配
    def judge_alm_not_exist(self, alm_name_ch ):
        return self.get_web_alarm_special( alm_name_ch)==[]
    #-------------------------
    #功能:获取信号的值(资产信息)
    def get_web_data_factory(self, dev_name_ch, sig_name_ch, sig_index=u'0'):
        lst_sig_attr = self.get_sig_attr_by_dev_sig( dev_name_ch, u'设备信息')
        if lst_sig_attr == None:
            return [] if sig_name_ch in [u'ALL',u'all',u'All'] else ''
        if sig_name_ch in [u'ALL',u'all',u'All']:
            return self.parse_web_data_mul( lst_sig_attr )
        else:
            return self.parse_web_data_single(  sig_name_ch, sig_index, lst_sig_attr )
    #-------------------------
    #功能:设置web控制量
    def set_web_ctrl(self, dev_name_ch, sig_name_ch, sig_index=u'0'):
        print u'设置控制量：',dev_name_ch, sig_name_ch
        lst_sig_attr = self.get_sig_attr_by_dev_sig( dev_name_ch, u'控制量')
        if lst_sig_attr == None:
            return False
        for item in lst_sig_attr:
            if item.get(u'full_name') == sig_name_ch:
                paraval = [ { u'value':u'1', u'sid':item.get(u'sid') } ]
                break
        else:
            print self.fileName, sys._getframe().f_lineno, u'Err,匹配命令名称:', sig_name_ch
            return False
        lst_data = self.get_request_data( u'signal', u'val_set', paraval)
        #控制命令 成功 lst_data==[], 失败 lst_data==None
        return lst_data==[]
    #-------------------------------------
    #u'paranum'始终是1,实测验证：都用 u'paranum':str(len( paraval )),也可以
    def request_config_channel(self, obj_id, obj_type, paraval ):
        lst_data = self.get_request_data( obj_id, obj_type, paraval )
        #与'控制命令'相同:成功 lst_data==[], 失败 lst_data==None
        return lst_data==[]  
    #------------------------
    #功能:处理web的返回值(不包含参数量)
    #     获取多个变量的数据时,返回值格式: [{},{}] ,每个变量的数据内容是一个字典,非常详细的信息
    #     获取单个变量的数据时,返回值格式: [{},{}] ,竟然和获取多个是一样一样的(实测,后续可跟踪他们的入参是否一样)
    #输出:     
    #     多个数据： [{key:val},{key:val}]
    def parse_web_data_mul(self, lst_fr_web):
        lst = []        
        for item in lst_fr_web:
            dct = {}
            key = item.get(u'full_name')
            val = item.get(u'value')
            dct[key] = val
            lst.append( dct )
        return lst
    #------------------------
    #功能:处理原关键字 Get Request Data 的返回值(不包含参数量)
    #     获取多个变量的数据时,返回值格式: [{},{}] ,每个变量的数据内容是一个字典,非常详细的信息
    #     获取单个变量的数据时,返回值格式: [{},{}] ,竟然和获取多个是一样一样的(实测,后续可跟踪他们的入参是否一样)
    #输出:     
    #     单个数据: ${val}
    #入参:signal_name示例  整流器机内温度 / 交流输入电压[1]
    def parse_web_data_single(self, signal_name, sig_index, lst_fr_web ):
        signal_name = self.connect_sig_name_index( signal_name, sig_index )
        for item in lst_fr_web:
            if signal_name == item.get(u'full_name'):
                return item.get(u'value')
        print self.fileName, sys._getframe().f_lineno, u'Err:signal_name=', signal_name
        return ''
    #------------------------
    #功能:特殊场景-获取一条 告警参数/历史数据参数 , 其它场景是获取value,这里可以获取 告警干接点/告警延时 等等, 所示是特殊场景
    #说明:该场景,signal_index只能是0或1,跟当前web的实际情况有关（以后根据web的更改再行处理） 20181210
    def parse_web_para_single(self, signal_name, sig_index, dest, lst_fr_web ):
        signal_name = self.connect_sig_name_index( signal_name, sig_index )
        for item in lst_fr_web:
            if signal_name == item.get(u'full_name'):
                return item.get( dest )
        print self.fileName, sys._getframe().f_lineno, u'Err:signal_name=', signal_name
        return ''
    #------------------------
    #返回值示例:2018-12-10 15:19:00
    def get_sys_time(self, cnt=5):
        while cnt > 0:
            lst_data = self.get_request_data( u'system_time', u'val_get', [{}])
            if lst_data == None:
                cnt -= 1
                continue
            else:
                return lst_data[0].get( u'time' )
        return ''
    #------------------------
    #功能:计算now之前interval秒的时间
    #入参&返回值格式:字符串
    #入参&返回值示例:2018-12-10 15:19:00
    def get_sys_time_dec(self, now, interval):
        ISOTIME    = '%Y-%m-%d %X'
        abs_second = time.mktime( time.strptime( now, ISOTIME ) ) #now对应'1970-01-01 08:00:00'的秒数
        return time.strftime( ISOTIME, time.localtime( abs_second-interval ) )
    #------------------------
    #获取时间字符串,2个时间,历史数据用;
    #入参:interval - 时间差,单位:秒
    #返回值:2个表示时间的字符串,示例:'1970-01-01 08:00:00'
    #返回值:tmr_end = csu的当前时间, tmr_start = tmr_end - interval
    def get_tmr_for_his(self, interval):
        ISOTIME = '%Y-%m-%d %H:%M:%S'
        tmr_end    = self.get_sys_time( )
        abs_second = time.mktime( time.strptime( tmr_end, ISOTIME ) ) #tmr_end对应'1970-01-01 08:00:00'的秒数
        tmr_start  = time.strftime( ISOTIME, time.localtime( abs_second-int(interval) ) )
        return tmr_start, tmr_end
    #------------------------
    #sTime示例:2018-12-10 15:19:00
    def set_sys_time(self, sTime):
        lst_data = self.get_request_data( u'system_time', u'val_set', [{u'time':sTime}])
        #成功 lst_data==[], 失败 lst_data==None
        return lst_data==[]
    #------------------------
    #同步系统时间
    def sync_sys_time(self):
        #ISOTIME = '%Y-%m-%d %X'
        #sTime = time.strftime(ISOTIME,time.localtime(time.time()))
        sTime = time.strftime('%Y-%m-%d %H:%M:%S') #当前时间时,此行可替代上面2行
        return self.set_sys_time( sTime )
    #------------------------
    #时间减小xxx秒
    def set_sys_time_ago(self, second):
        ISOTIME = '%Y-%m-%d %X'
        dest = time.time() - float(second)
        sTime = time.strftime(ISOTIME,time.localtime( dest ) )
        return self.set_sys_time( sTime )
    #------------------------
    #时间增加xxx秒
    def set_sys_time_later(self, second):
        ISOTIME = '%Y-%m-%d %X'
        dest = time.time() + float(second)
        sTime = time.strftime(ISOTIME,time.localtime( dest ) )
        return self.set_sys_time( sTime )
    #------------------------
    def get_session(self,ip):
        return self.create_session( alias=u'pmsa', url=u'https://'+ip, disable_warnings=1)
    #------------------------
    #功能:连接到服务器,登录
    def login_web(self, timeout=300):
        start_time = time.time()
##        print self.fileName, sys._getframe().f_lineno, time.time(), g_var.variables.get('ip')
        para   = [{"username": "Superman", "pswd": "5885c57ccca5472558e9cd29f73cc87f1b2e9ad3c4f8a29902538965e08d2146"}]
        while time.time() - start_time <= timeout:
            cookie     = 'lan=zh_CN.UTF-8;user=Superman;'
            session    = self.get_session( g_var.variables.get('ip') )
            new_cookie = self.get_login_request( session, cookie, u'login', u'val_set', para )
##            print self.fileName, sys._getframe().f_lineno, time.time(),cookie
            if new_cookie == None:
                time.sleep( 10 ) #login失败后,稍候片刻再login
                continue
            else:
                g_var.variables.update( {'session':session} )
                g_var.variables.update( {'cookie':new_cookie} )
                break
        print 'login web:', new_cookie != None, g_var.variables.get('ip')
        return new_cookie != None
    #------------------------
    def login_web_again(self, time_out=300):
        start_time = time.time()
        para   = [{"username": "Superman", "pswd": "5885c57ccca5472558e9cd29f73cc87f1b2e9ad3c4f8a29902538965e08d2146"}]
        while time.time()-start_time < time_out:
            cookie     = 'lan=zh_CN.UTF-8;user=Superman;'
            session    = self.get_session( g_var.variables.get('ip') )
            new_cookie = self.get_login_request( session, cookie, u'login', u'val_set', para )
            if new_cookie == None:
                time.sleep( 15 ) #login失败后,稍候片刻再login
                continue
            else:
                time.sleep( 10 ) #login成功后,液晶还没有刷新,稍候一下
                g_var.variables.update( {'session':session} )
                g_var.variables.update( {'cookie':new_cookie} )
                break
        print 'login web:', new_cookie != None
        return new_cookie != None
    #------------------------
    #功能:干接点强制控制关闭
    def do_ctrl_relay_OFF(self):
        paraval = [ {u'instid':u'', u'ctrl out relay':u'0'} ]
        lst = self.get_request_data( u'plat.outrelay_ctrl', u'val_set', paraval )
        return lst == []   
    #------------------------
    #功能:干接点强制控制开启
    def do_ctrl_relay_ON(self):
        paraval = [ {u'instid':u'', u'ctrl out relay':u'1'} ]
        lst = self.get_request_data( u'plat.outrelay_ctrl', u'val_set', paraval )
        return lst == []        
    #------------------------
    #action:0表示恢复（控制断开），1表示动作（控制闭合）
    #tag:   0表示是下电干接点，1表示是输出干接点
    #index: 输出干接点的序号，从1开始
    #返回值:status为控制后输出干接点的状态，为0表示闭合，为1表示断开。(监控平台提供的关键字,这里优化掉了)
    def do_relay_out(self, index, tag, action):
        paraval = [ {u'action':action, u'do_tag':tag, u'io_index':index} ]
        lst = self.get_request_data( u'di_do', u'val_set', paraval )
        return lst == []
    #------------------------
    #功能:控制输出干接点动作 - 不是下电干接点
    #index:从1开始
    def do_relay_out_ON(self,index):
        tag = u'1'
        action = u'1'
        return self.do_relay_out(index,tag,action)
    #------------------------
    #功能:控制输出干接点恢复 - 不是下电干接点
    #index:从1开始
    def do_relay_out_OFF(self,index):
        tag = u'1'
        action = u'0'
        return self.do_relay_out(index,tag,action)
    #------------------------
    #功能:蜂鸣器开启
    def do_buzzer_ON(self):
        paraval = [ {u'action':u'0'} ]
        lst = self.get_request_data( u'buzzer', u'val_set', paraval )
        return lst == []
    #------------------------
    #功能:蜂鸣器关闭
    def do_buzzer_OFF(self):
        paraval = [ {u'action':u'1'} ]
        lst = self.get_request_data( u'buzzer', u'val_set', paraval )
        return lst == []
    #------------------------
    #功能:获取输入/输出干接点状态(自测时发现,应该是输入干接点的状态20190104)
    #入参realay_name为输入/输出干接点的名称,参考web内容: 系统维护-干接点状态-干接点名称
    #返回值:为干接点的状态，为0表示闭合，为1表示断开。
    def get_relay_status(self,relay_name):
        paraval = [{}]
        lst = self.get_request_data( u'di_do', u'val_get', paraval ) #所有的输出干接点信息
        if lst == None:
            return u''
        for item in lst:
            if item.get(u'full_name') == relay_name:
                return item.get(u'value')
        print self.fileName, sys._getframe().f_lineno, 'Err: not find:', relay_name
        return u''
    #------------------------
    #功能:系统复位
    def sys_reboot(self):
        paraval = [{u'reboot':u'1'}]
        lst = self.get_request_data( u'reboot', u'val_set', paraval )
        #lst == None: 表明timeout内没有收到CSU的响应,即CSU重新启动了
        return True if lst == None else False
    #-------------------------
    #功能:获取系统心跳
    # 没有调试通过,
    def get_heart_beat(self):
        paraval = [{u'type':u'1'}]
        lst = self.get_request_data( u'heart_beat', u'val_get', paraval, time_out=60 )
        if lst == None: #time out,系统重启?
            return u'-1'
        return lst[0].get('userlevel') #失败,返回 -1, 成功,返回的值 >= 0, 数值越大,级别越高
    #=========================    
    #-------------------------
    #历史告警 start
    def get_web_data_his_alm_cnt(self, start_time, end_time):
        paraval = [{u'start_time':start_time,u'end_time':end_time}]
        lst_all_data = self.get_request_data( u'history_alarm', u'list', paraval=paraval)
        return 0 if lst_all_data==None else int(lst_all_data[0].get(u'counts'))    
    #--获取历史告警内容,已自测通过
    def get_web_data_his_alm(self, start_time, end_time, page='1', count='10'): #page从1开始
        nums   = self.get_web_data_his_alm_cnt( start_time, end_time)
        #--判断page是否超出范围
        if (int(page)-1)*int(count) > nums:
            print self.fileName, sys._getframe().f_lineno, 'Err: page is out of range.'
            return []
        offset = nums+1 - int(page)*int(count)
        #--判断是否是最后一页
        if int(page)*int(count) >= nums:
            rd_count = str( nums - (int(page)-1)*int(count) )
        else:
            rd_count = count
            
        paraval = [{"start_time":start_time,"end_time":end_time,"offset":str(offset),"counts":rd_count}]
        lst_all_data = self.get_request_data( u'history_alarm', u'val_get', paraval=paraval)
        if lst_all_data == None:
            return []
        #return lst_all_data #下面的代码是为了返回列表,并增加序号        
        ret = []
        if nums > int(page)*int(count):
            index = nums - int(page)*int(count)
        else:
            index = 0
        #--
        for dct in lst_all_data:
            full_name  = dct.get(u'full_name')
            start_time = dct.get(u'start_time')
            end_time   = dct.get(u'end_time')
            level      = dct.get(u'level')
            level_name = dct.get(u'level_name')
            if full_name == None:
                continue
            index += 1
            #ret.append( [str(index),full_name,start_time,end_time,level_name,level] )
            ret.append( ','.join( [str(index),start_time,end_time,level_name,level,full_name] ) )
        return ret
    #---------------------
    #功能:获取历史告警-指定告警名称最新的那条-默认从1天内最新的50条记录中查找
    def get_web_data_his_alm_one(self, dev_name, sig_name):
        start_time, end_time = self.get_tmr_for_his( 86400 ) #86400=24*60*60
        lst = self.get_web_data_his_alm( start_time, end_time, page='1',count='50')
        ret = []
        sig_name_ch = dev_name + ':' + sig_name
        for s in lst:
            tmp = s.split(',')            
            if tmp[5] == sig_name_ch:
                ret = tmp
        return ret
    #-------------------------
    #功能:获取指定的字符串(event_name)在指定时间段(start_time, end_time)的历史告警中出现的次数(在该时间段中的最新50条记录中查找)
    def get_web_data_special_time_range_alm_cnt(self, start_time, end_time, alm_name):
        lst = self.get_web_data_his_alm( start_time, end_time, page='1',count='50')
        cnt = self.get_web_data_his_event_cnt_special( alm_name, lst)
        return cnt
    #历史告警 end
    #-------------------------
    #历史事件 start
    def get_event_type(self, event_type):
        if event_type in [u'0',u'所有']:
            return u'0'
        if event_type in [u'100',u'运行记录']:
            return u'100'
        if event_type in [u'200',u'操作记录']:
            return u'200'
        if event_type in [u'201',u'整流器操作']:
            return u'201'
        if event_type in [u'300',u'安全事件']:
            return u'300'
        #--
        print self.fileName, sys._getframe().f_lineno, u'事件记录的类型错误:', event_type
        return None   
    #event_type: 0-所有,100-运行记录,200-操作记录,201-整流器操作,300-安全事件
    def get_web_data_his_event_cnt(self, start_time, end_time, event_type='0'):
        event_type = self.get_event_type( event_type )
        if event_type == None:
            return 0
        paraval = [{u'start_time':start_time,u'end_time':end_time, u'hisevent_type':event_type}]
        lst_all_data = self.get_request_data( u'history_event', u'list', paraval=paraval)
        return 0 if lst_all_data==None else int(lst_all_data[0].get(u'counts'))    
    #--获取历史事件内容,已自测通过
    def get_web_data_his_event(self, start_time, end_time, event_type='0', page='1', count='10'): #page从1开始
        event_type = self.get_event_type( event_type )
        if event_type == None:
            return []
        nums   = self.get_web_data_his_event_cnt( start_time, end_time, event_type=event_type)
        #--判断page是否超出范围
        if (int(page)-1)*int(count) > nums:
            print self.fileName, sys._getframe().f_lineno, 'Err: page is out of range.'
            return []
        offset = nums+1 - int(page)*int(count)
        #--判断是否是最后一页
        if int(page)*int(count) >= nums:
            rd_count = str( nums - (int(page)-1)*int(count) )
        else:
            rd_count = count
        paraval = [{"start_time":start_time,"end_time":end_time,"hisevent_type":event_type,"offset":str(offset),"counts":rd_count}]
        lst_all_data = self.get_request_data( u'history_event', u'val_get', paraval=paraval)        
        #return ['null'] if lst_all_data==None else lst_all_data    
        if lst_all_data == None:
            return []
        #return lst_all_data #下面的代码是为了返回列表,并增加序号        
        ret = []
        if nums > int(page)*int(count):
            index = nums - int(page)*int(count)
        else:
            index = 0
        #--
        for dct in lst_all_data:
            operator  = dct.get(u'operator')
            save_time = dct.get(u'save_time')
            value     = dct.get(u'value')
            if operator == None:
                continue
            index += 1
            #ret.append( [str(index),operator,save_time,value] )
            ret.append( ','.join( [str(index),operator,save_time,value] ) )
        return ret
    #------------------------
    #功能:获取指定的字符串(str_name)在历史数据列表(lst)中出现的次数
    #说明:入参lst是函数get_web_data_his_event/get_web_data_his_alm的返回值
    #    历史数据不需要这个类似的功能,因为查询历史数据可以精确到具体的 设备&信号
    def get_web_data_his_event_cnt_special(self, str_name, lst):
        cnt = 0
        for s in lst:
            print 's',s
            if str_name in s:
                cnt += 1
        return cnt
    #-------------------------
    #功能:获取指定的字符串(event_name)在指定时间段(start_time, end_time)的历史事件(event_type)中出现的次数(在该时间段中的最新50条记录中查找)
    def get_web_data_special_time_range_event_cnt(self, start_time, end_time, event_type, event_name):
        lst = self.get_web_data_his_event( start_time, end_time, event_type=event_type,page='1',count='50')
        cnt = self.get_web_data_his_event_cnt_special( event_name, lst)
        return cnt
    #-------------------------
    #--获取历史数据
    def get_web_data_his_data_cnt(self, start_time, end_time, dev_type=u'所有',sig_type=u'所有'):
        if dev_type in [u'所有','ALL','all','All']:
            paraval = [{u'start_time':start_time,u'end_time':end_time, u'sid':u'0'}]
        else:
            dev_sid = self.get_dev_sid_by_dev_name( dev_type )
            if sig_type in [u'所有','ALL','all','All']:
                paraval = [{u'start_time':start_time,u'end_time':end_time, u'sid':dev_sid}]
            else:
                lst_sig_sid     = [ {u'sid':str( int(dev_sid) + (1<<28) )} ] #1 == ANA,没有调用相关函数,是为了少调用一次dev_sid,提高效率
                lst_sig_sid.append( {u'sid':str( int(dev_sid) + (2<<28) )} ) #2 == DIG
                lst_sig_attr = self.get_request_data( u'signal', u'val_get', lst_sig_sid)
                if lst_sig_attr == None:
                    return 0
                for real_data in lst_sig_attr:
                    if real_data.get('full_name') == sig_type:
                        data_sid = real_data.get('sid')
                        break
                else:
                    return 0
                #--
                paraval = [{u'start_time':start_time,u'end_time':end_time, u'sid':data_sid}]
        #--
        lst_all_data = self.get_request_data(  u'history_data', u'list', paraval=paraval)
        return 0 if lst_all_data==None else int(lst_all_data[0].get(u'counts'))
    #----
    def get_web_data_his_data(self, start_time, end_time, dev_type=u'所有',sig_type=u'所有', page='1',count='10'):
        nums = self.get_web_data_his_data_cnt(  start_time, end_time, dev_type=dev_type,sig_type=sig_type)
        #--判断page是否超出范围
        if (int(page)-1)*int(count) > nums:
            print self.fileName, sys._getframe().f_lineno, 'Err: page is out of range.'
            return []
        offset = nums+1 - int(page)*int(count)
        #--判断是否是最后一页
        if int(page)*int(count) >= nums:
            rd_count = str( nums - (int(page)-1)*int(count) )
        else:
            rd_count = count
        #--
        if dev_type in [u'所有','ALL','all','All']:
            paraval = [{"start_time":start_time,"end_time":end_time,"sid":'0',"offset":str(offset),"counts":rd_count}]
        else:
            dev_sid = self.get_dev_sid_by_dev_name( dev_type )
            if sig_type in [u'所有','ALL','all','All']:                
                paraval = [{"start_time":start_time,"end_time":end_time,"sid":dev_sid,"offset":str(offset),"counts":rd_count}]
            else:
                lst_sig_sid     = [ {u'sid':str( int(dev_sid) + (1<<28) )} ] #1 == ANA,没有调用相关函数,是为了少调用一次dev_sid,提高效率
                lst_sig_sid.append( {u'sid':str( int(dev_sid) + (2<<28) )} ) #2 == DIG
                lst_sig_attr = self.get_request_data( u'signal', u'val_get', lst_sig_sid)
                if lst_sig_attr == None:
                    return []
                for real_data in lst_sig_attr:
                    if real_data.get('full_name') == sig_type:
                        data_sid = real_data.get('sid')
                        break
                else:
                    print self.fileName, sys._getframe().f_lineno, 'Err:', sig_type, 'is not exist.'
                    return []
                #--
                paraval = [{"start_time":start_time,"end_time":end_time,"sid":data_sid,"offset":str(offset),"counts":rd_count}]
        #----
        lst_all_data = self.get_request_data( u'history_data', u'val_get', paraval=paraval)
        if lst_all_data==None:
            return []
        #return lst_all_data #下面的代码是为了返回列表,并增加序号        
        ret = []
        if nums > int(page)*int(count):
            index = nums - int(page)*int(count)
        else:
            index = 0
        #--
        for dct in lst_all_data:
            tmr  = dct.get(u'save_time')
            name = dct.get(u'full_name')
            val  = dct.get(u'value')
            unit = dct.get(u'unit')
            if tmr == None:
                continue
            index += 1
            #ret.append( [str(index),tmr,name,val,unit] )
            ret.append( ','.join( [str(index),tmr,name,val,unit] ) )
        return ret
    #----------------------------
    def ask_download(self, obj_id, obj_type, paraval=[] ):
        data = { u'objectid':obj_id,
                 u'type':obj_type,
                 u'paraval':json.dumps( paraval )}
        context = self.post_request_wrapped( data )
        if context == None:
            return False
        if context.get(u'result') != u'ok':
            print self.fileName, sys._getframe().f_lineno, u'result is not ok', context
            return False
        if context.get(u'objectid') != obj_id:
            print self.fileName, sys._getframe().f_lineno, u'object id is not equ'
            return False
        return True

    def wait_file_zipped(self, obj_id, obj_type, paraval=[] ):
        data = { u'objectid':obj_id,
                 u'type':obj_type,
                 u'paraval':json.dumps( paraval )}
        context = self.post_request_wrapped( data )
        if context == None:
            return False
        if context.get(u'result') != u'ok':
            print self.fileName, sys._getframe().f_lineno, u'result is not ok', context
            return False
        if context.get(u'objectid') != obj_id:
            print self.fileName, sys._getframe().f_lineno, u'object id is not equ'
            return False
        return context.get(u'data')
    #--
    #系统维护 - 参数维护 - 导出参数文件
    def download_para(self):
        para_type = 'paramaintain'
        paraval = [{"id":"0"},{"id":"1"},{"id":"2"},{"id":"3"}]
        ask = self.ask_download( para_type, u'val_get', paraval=paraval)
        if not ask:
            return ask
        #----
        cnt = 0
        bCheck = True #检查要下载的文件是否准备好
        while bCheck:
            if cnt > 10:
                return ''
            paraval = [{}]
            zipped = self.wait_file_zipped( para_type, u'attr_get', paraval=paraval)
            if not zipped:
                cnt += 1
                print self.fileName, sys._getframe().f_lineno, u'wait file zipped:', cnt
            #----
            for dct in zipped:
                file_name = dct.get( u'filename' )
                file_uri  = dct.get( u'fileurl' )
                if file_name != None:
                    bCheck = False #退出for循环,然后退出while循环
                    break
            else: #参考2.1平台的代码,增加了判断,实际应用中不会出现,可能是web那里处理了
                #print 'sleep ~~~~~~~~~~~'
                cnt += 1
                print self.fileName, sys._getframe().f_lineno, u'file zipped fail:', cnt
                time.sleep( 1 )
        #--        
        url = 'https://' + g_var.variables.get('ip') + file_uri
        try:
            open( self.recordPath + '/download/'+file_name,'wb').write(urllib2.urlopen(url,context=ssl._create_unverified_context()).read())
            return file_name
        except:
            return ''        
##    #--
##    #对应web的 系统维护-参数维护-参数文件-导出参数文件
##    def download_config_para(self, ip):
##        para_type = u'configmaintain'
##        paraval   = []
##        return self.download_para( ip, para_type, paraval )        
##    #--
##    #对应web的 系统维护-参数维护-配置文件-导出配置文件
##    def download_maintain_para(self, ip, lst_maintain_para_type):
##        maintain_para_types = [u'显示属性参数',u'告警属性参数',u'存储属性参数',u'站点参数',u'用户参数',u'出厂参数'] 
##        paraval = []
##        for maintain_para_type in lst_maintain_para_type:
##            if maintain_para_type in maintain_para_types:
##                paraval.append( {u'id':str(maintain_para_types.index( maintain_para_type ))} )
##            else:
##                print u'下载参数文件的入参不合法:',maintain_para_type
##        para_type = u'paramaintain'
##        if len(paraval) == 0:
##            print u'下载参数文件的合法入参格式为0.'
##            return False
##        return self.download_para( ip, para_type, paraval ) 
#----------------------------------
    #通道配置
    #获取 DI通道配置 的所有信息
    def get_config_DI_infos(self ):
        lst_DI_list = self.get_request_data( u'plat.di', u'list', [{}] ) #DI内容列表,返回值:[{u'instid': u'plat.Inrelay1Status'}, {u'instid': u'plat.Inrelay2Status'}]
        lst_DI_val  = self.get_request_data( u'plat.di', u'val_get', lst_DI_list )
        return lst_DI_val
    #通道配置:DI
    def config_channel_DI(self, channel_name, dev_name_ch, sig_name_ch , alm_level):
        levels = [u'闭合',u'断开']
        if alm_level in levels:
            level = str(levels.index(alm_level))
        else:
            print self.fileName, sys._getframe().f_lineno,u'告警/置位电平错误', alm_level
            return False
        #----
        if dev_name_ch==u'无':
            SID=''
        else:
            SID=self.calc_SID_with_devName_sigName(dev_name_ch,sig_name_ch) 
        if SID == None and dev_name_ch!=u'无':
            return False
        lst_DI_infos = self.get_config_DI_infos( )

        for dct in lst_DI_infos:
            if dct.get(u'Channel Name') == channel_name:
                dct[u'Preset Status'] = level.decode('utf8')
                dct[u'SID'] = SID.decode('utf8')
                paraval=[dct]
                self.get_request_data( u'plat.di', u'val_set', paraval)
        #----
        get_config_channel_DI=self.get_config_channel_DI(channel_name)
        if (get_config_channel_DI[0] !=dev_name_ch or get_config_channel_DI[1] !=sig_name_ch
            or get_config_channel_DI[2] != alm_level):
            print u"设置失败!",dev_name_ch,sig_name_ch,alm_level
            return False
        else:
            return True
    #获取DI通道配置信息Pengy2019/12/24
    def get_config_channel_DI(self, name):
        lst_data = self.get_config_DI_infos()
        values = [u'闭合',u'断开']

        for dct in lst_data:
            if dct.get('Channel Name') == name:
                val = values[int(dct.get('Preset Status'))]
                SID = dct.get('SID')
                if SID == '':
                    return [u'无',u'无',val]
                
                dictList=self.Conversion_data_dict_format()        
                for s in dictList:
                    lst = s.split(',')
                    if (SID[:5] in lst[7]) and (SID[6:-1] in lst[7]) :
                        dimension=lst[17]
                        if len(dimension)>2:
                            signalName=lst[13]+'_'+SID[-1]
                        else:
                            signalName=lst[13]
                        #获取设备名称和设备数量
                        devicesList=self.Conversion_data_dictionary_devices_format()
                        for i in devicesList:
                            lst1=i.split(',')
                            if lst[1] in lst1:
                                if int(lst1[2])>1:#设备最大数量
                                    dev_name_ch=lst[1]+'_'+SID[5]
                                else:
                                    dev_name_ch=lst[1]
                            
##                        print dev_name_ch,signalName,val,SID
                        return [dev_name_ch,signalName,val]
        return []



#----------------------------------
    #获取 DO通道配置 的所有信息
    def get_config_DO_infos(self):
        lst_DO_list = self.get_request_data( u'plat.do', u'list', [{}] )
        lst_DO_val  = self.get_request_data( u'plat.do', u'val_get', lst_DO_list )
        return lst_DO_val
    #通道配置:DI
    def config_channel_DO(self, channel_name, dev_name_ch, sig_name_ch,alm_level):
        levels = [u'常闭',u'常开']
        if alm_level in levels:
            level = str(levels.index(alm_level))
        else:
            print self.fileName, sys._getframe().f_lineno,u'告警/置位电平错误', alm_level
            return False
        #----
        if dev_name_ch==u'无':
            SID=''
        else:
            SID=self.calc_SID_with_devName_sigName(dev_name_ch,sig_name_ch) 
        if SID == None and dev_name_ch!=u'无':
            return False
        #----
        lst_DO_infos = self.get_config_DO_infos( )

        for dct in lst_DO_infos:
            if dct.get(u'Channel Name') == channel_name:
                dct[u'Preset Status'] = level.decode('utf8')
                dct[u'SID'] = SID.decode('utf8')
                paraval=[dct]
                self.get_request_data( u'plat.do', u'val_set', paraval)
        #----
        get_config_channel_DO=self.get_config_channel_DO(channel_name)
        if (get_config_channel_DO[0] !=dev_name_ch or get_config_channel_DO[1] !=sig_name_ch
            or get_config_channel_DO[2] != alm_level):
            print u"设置失败!",dev_name_ch,sig_name_ch,alm_level
            return False
        else:
            return True
    #获取DO通道配置 Pengy2019/12/23
    def get_config_channel_DO(self, name):
        lst_data=self.get_config_DO_infos()
        values = [u'常闭',u'常开']
        for dct in lst_data:
            if dct.get('Channel Name') == name:
                val = values[int(dct.get('Preset Status'))]
                SID = dct.get('SID')
                if SID=='':
                    return [u'无',u'无',val]
                
                dictList=self.Conversion_data_dict_format()        
                for s in dictList:
                    lst = s.split(',')
                    if (SID[:5] in lst[7]) and (SID[6:-1] in lst[7]) :
                        dimension=lst[17]

                        if len(dimension)>2:
                            signalName=lst[13]+'_'+SID[-1]
                        else:
                            signalName=lst[13]
                        #获取设备名称和设备数量
                        devicesList=self.Conversion_data_dictionary_devices_format()
                        for i in devicesList:
                            lst1=i.split(',')
                            if lst[1] in lst1:
                                if int(lst1[2])>1:#设备最大数量
                                    dev_name_ch=lst[1]+'_'+SID[5]
                                else:
                                    dev_name_ch=lst[1]
                            
##                        print dev_name_ch,signalName,val,SID
                        return [dev_name_ch,signalName,val]
        return []
#----------------------------------
    #获取 AI通道配置 的所有信息
    def get_config_AI_infos(self):
        lst_AI_list = self.get_request_data( u'plat.ai', u'list', [{}] )
        lst_AI_val  = self.get_request_data( u'plat.ai', u'val_get', lst_AI_list )
        return lst_AI_val
    #通道配置:AI
    def config_channel_AI(self, channel_name, offset, slope, dev_name_ch, sig_name_ch):
        if dev_name_ch==u'无':
            SID=''
        else:
            SID=self.calc_SID_with_devName_sigName(dev_name_ch,sig_name_ch) 
        if SID == None and dev_name_ch!=u'无':
            return False
        lst_AI_infos = self.get_config_AI_infos( )
        for dct in lst_AI_infos:
            if dct.get(u'Channel Name') == channel_name:
                dct[u'SID']    = sid
                dct[u'Offset'] = str(offset)
                dct[u'Slope']  = str(slope)
                paraval=[dct]
                return self.request_config_channel( u'plat.ai', u'val_set', paraval)
        #----
        print self.fileName, sys._getframe().f_lineno, u'通道名称错误:', channel_name
        return False

    #获取AI通道配置信息    
    def get_config_channel_AI(self,name):
        lst_data=self.get_config_AI_infos()
        for dct in lst_data:
            if dct.get('Channel Name') == name:
                Offset = dct.get('Offset')#零点
                Slope=dct.get('Slope')#斜率                
                SID = dct.get('SID')
##                print Offset,Slope,SID
                if SID=='':
                    return [Offset,Slope,u'无',u'无']
                
                dictList=self.Conversion_data_dict_format()
                
                for s in dictList:
                    lst = s.split(',')
                    if (SID[:5] in lst[7]) and (SID[6:-1] in lst[7]) :
                        dimension=lst[17]
                        if len(dimension)>2:
                            signalName=lst[13]+'_'+SID[-1]
                        else:
                            signalName=lst[13]
                        #获取设备名称和设备数量
                        devicesList=self.Conversion_data_dictionary_devices_format()
                        for i in devicesList:
                            lst1=i.split(',')
                            if lst[1] in lst1:
                                if int(lst1[2])>1:#设备最大数量
                                    dev_name_ch=lst[1]+'_'+SID[5]
                                else:
                                    dev_name_ch=lst[1]
##                        print dev_name_ch,signalName
                        return [Offset,Slope,dev_name_ch,signalName]
#----------------------------------
    #获取 AIDI通道配置 的所有信息:objID=plat.ai, sig_type=ANA
    def get_config_AIDI_infos(self ):
        lst_AIDI_list = self.get_request_data( u'plat.ai', u'list', [{}] )
        lst_AIDI_val  = self.get_request_data( u'plat.ai', u'val_get', lst_AIDI_list )
        return lst_AIDI_val
    #通道配置:AIDI
    def config_channel_AIDI(self, channel_name, dev_name_ch, sig_name_ch, alm_level):

        if dev_name_ch==u'无':
            SID=''
        else:
            SID=self.calc_SID_with_devName_sigName(dev_name_ch,sig_name_ch) 
        if SID == None and dev_name_ch!=u'无':
            return False
        
        #转换告警/异常状态
        levels = [u'闭合',u'断开']
        if alm_level in levels:
            level = str(levels.index(alm_level)).decode('utf8')
        else:
            print self.fileName, sys._getframe().f_lineno,u'告警/置位电平错误', alm_level
            return False

##
##        get_paraval=self.get_config_channel_AIDI(channel_name)[3]

        lst_AIDI_infos = self.get_config_AIDI_infos( )

        for dct in lst_AIDI_infos:
            if dct.get(u'Channel Name') == channel_name:
        
                a={u'Preset Status': level,u'SID': SID}
                dct.update(a)

                self.request_config_channel( u'plat.ai', u'val_set', paraval=[dct])
                break

        get_config_channel_AIDI=self.get_config_channel_AIDI(channel_name)
        if (get_config_channel_AIDI[0] !=dev_name_ch or get_config_channel_AIDI[1] !=sig_name_ch
            or get_config_channel_AIDI[2] != alm_level):
            print u"设置失败!",dev_name_ch,sig_name_ch,alm_level
            return False
        else:
            return True


    #获取AIDI通道配置信息    
    def get_config_channel_AIDI(self,name):
        levels = [u'闭合',u'断开']
        lst_data=self.get_config_AIDI_infos()
        for dct in lst_data:
            if dct.get('Channel Name') == name:
                Alm_Status=dct.get('Preset Status')#告警状态
                Alm_Status=levels[int(Alm_Status)]
                SID = dct.get('SID')
                if SID=='':
                    return [u'无',u'无',Alm_Status]
                
                dictList=self.Conversion_data_dict_format()
                
                for s in dictList:
                    lst = s.split(',')
                    if (SID[:5] in lst[7]) and (SID[6:-1] in lst[7]) :
                        dimension=lst[17]
                        if len(dimension)>2:
                            signalName=lst[13]+'_'+SID[-1]
                        else:
                            signalName=lst[13]
                        #获取设备名称和设备数量
                        devicesList=self.Conversion_data_dictionary_devices_format()
                        for i in devicesList:
                            lst1=i.split(',')
                            if lst[1] in lst1:
                                if int(lst1[2])>1:#设备最大数量
                                    dev_name_ch=lst[1]+'_'+SID[5]
                                else:
                                    dev_name_ch=lst[1]
##                        print dev_name_ch,signalName,Alm_Status
                        return [dev_name_ch,signalName,Alm_Status]
    #----------------------------------
    #北向协议配置 相关
    #SNMP参数
    def get_SNMP_para(self):
        lst_SNMP_para = self.get_request_data( u'plat.snmp', u'val_get', [{"instid":""}] )
        notification_modes = ['inform V2','Trap V1','Trap V2','Trap V3']
        read_community    = lst_SNMP_para[0].get('SNMP Read Community')
        wirte_community   = lst_SNMP_para[0].get('SNMP Write Community')
        notification_mode = notification_modes[ int(lst_SNMP_para[0].get('SNMP Notification Mode') ) ]
        v3_notification_name = lst_SNMP_para[0].get('SNMP V3 Notification Name')
        return [read_community, wirte_community, notification_mode, v3_notification_name]
    def set_SNMP_para(self, read_community, wirte_community, notification_mode, v3_notification_name ):
        notification_modes = ['inform V2','Trap V1','Trap V2','Trap V3']
        paraval = [{"SNMP Notification Mode":str(notification_modes.index(notification_mode)),
                    "instid":"",
                    "SNMP V3 Notification Name":v3_notification_name,
                    "SNMP V3 Notification Severity Level":"1",
                    "SNMP Read Community":read_community,
                    "SNMP Write Community":wirte_community
                    }]
        lst_SNMP_para = self.get_request_data( u'plat.snmp', u'val_set', paraval )
        return lst_SNMP_para == []
    #V3用户参数
    def _get_v3_user_id(self): #中间函数
        lst_data = self.get_request_data( u'plat.snmp_v3user', u'list', [{}] )
        lst_instid = []
        for dct in lst_data:
            lst_instid.append( dct.get('instid') )
        return lst_instid
    
    def get_v3_user_info(self):
        lst_user_id = self._get_v3_user_id( )
        if len(lst_user_id) == 0:
            return []
        paraval = []
        for user_id in lst_user_id:
            paraval.append( {'instid':user_id} )
        lst_data = self.get_request_data( u'plat.snmp_v3user', u'val_get', paraval )
        Auth_Prtcls = ['MD5','SHA']
        Pri_Prtcls  = ['DES','AES']
        ret = []
        for dct in lst_data:
            name = dct.get('SNMP User Name')
            auth_prtcl = Auth_Prtcls[ int(dct.get('SNMP Authentication Protocol')) ]
            auth_key   = dct.get( 'SNMP Authentication Key' )
            pri_prtcl  = Pri_Prtcls[ int(dct.get('SNMP Privacy Protocol')) ]
            pri_key    = dct.get( 'SNMP Privacy Key' )
            ret.append( ','.join( [name,auth_prtcl,auth_key,pri_prtcl,pri_key] ) )
        return ret

    def add_v3_user(self,name,auth_prtcl,auth_key,pri_prtcl,pri_key):
        Auth_Prtcls = ['MD5','SHA']
        Pri_Prtcls  = ['DES','AES']
        paraval = [{"SNMP User Name":name,
                    "SNMP Authentication Protocol":str(Auth_Prtcls.index(auth_prtcl)),
                    "SNMP Authentication Key":auth_key,
                    "SNMP Privacy Protocol":str(Pri_Prtcls.index(pri_prtcl)),
                    "SNMP Privacy Key":pri_key
                    }]
        lst_data = self.get_request_data( u'plat.snmp_v3user', u'inst_add', paraval )
        return lst_data == []

    def del_v3_user(self,name):
        lst_user_id = self._get_v3_user_id( )
        if len(lst_user_id) == 0:
            return False
        paraval = []
        for user_id in lst_user_id:
            paraval.append( {'instid':user_id} )
        lst_data = self.get_request_data( u'plat.snmp_v3user', u'val_get', paraval )
        
        for dct in lst_data:
            if name == dct.get('SNMP User Name'):
                user_id = dct.get('instid')
                break
        else:
            return False
        lst_data = self.get_request_data( u'plat.snmp_v3user', u'inst_delete', [{'instid':user_id}] )
        return lst_data == []
    #管理者配置
    def _get_manager_id(self): #中间函数
        lst_data = self.get_request_data( u'plat.snmp_manager', u'list', [{}] )
        lst_instid = []
        for dct in lst_data:
            lst_instid.append( dct.get('instid') )
        return lst_instid
    
    def get_manager_info(self):
        lst_user_id = self._get_manager_id( )
        if len(lst_user_id) == 0:
            return []
        paraval = []
        for user_id in lst_user_id:
            paraval.append( {'instid':user_id} )
        lst_data = self.get_request_data( u'plat.snmp_manager', u'val_get', paraval )
        ret = []
        for dct in lst_data:
            name = dct.get('SNMP Manager Name')
            ip   = dct.get('SNMP Manager IP')
            port = dct.get('SNMP Notification Port')
            ret.append( ','.join( [name, ip, port] ) )
        return ret
    
    def add_manager(self,name,ip,port):
        paraval = [{"SNMP Manager Name":name,
                    "SNMP Manager IP":ip,
                    "SNMP Notification Port":port
                    }]
        lst_data = self.get_request_data( u'plat.snmp_manager', u'inst_add', paraval )
        return lst_data == []

    def del_manager(self,name):
        lst_user_id = self._get_manager_id( )
        if len(lst_user_id) == 0:
            return False
        paraval = []
        for user_id in lst_user_id:
            paraval.append( {'instid':user_id} )
        lst_data = self.get_request_data( u'plat.snmp_manager', u'val_get', paraval )
        
        for dct in lst_data:
            if name == dct.get('SNMP Manager Name'):
                user_id = dct.get('instid')
                break
        else:
            return False
        lst_data = self.get_request_data( u'plat.snmp_manager', u'inst_delete', [{'instid':user_id}] )
        return lst_data == []

#----------------------------------
    def get_config_COM_attr(self, channel_name):
        checks = [u'无','odd','even']
        bauds  = ['1200','2400','4800','9600','19200','38400','57600','115200']
        lst_COM_infos = self.get_config_COM_infos( )
        for dct in lst_COM_infos:
            if dct.get(u'Serial Name') == channel_name:
                baud = bauds[ int(dct.get(u'Baud Rate')) ]
                bits = dct.get(u'Data Bits')
                stop = dct.get(u'Stop Bits')
                Parity=checks[ int(dct.get(u'Parity Bits')) ]
                return [channel_name,baud,bits,stop,Parity]
        #----
        print self.fileName, sys._getframe().f_lineno, u'通道名称错误:', channel_name
        return [channel_name,'','','','']
    #获取 COM通道配置 的所有信息:objID=plat.com
    def get_config_COM_infos(self):
        lst_channel_list = self.get_request_data( u'plat.com', u'list', [{}] )
        lst_channel_val  = self.get_request_data( u'plat.com', u'val_get', lst_channel_list )
        return lst_channel_val
    #通道配置:DI
    def config_channel_COM(self, channel_name, baud, bits, stop, check ):
        bauds = ['1200','2400','4800','9600','19200','38400','57600','115200']
        if baud in bauds:
            baud_idx = str(bauds.index( baud ) )
        else:
            print self.fileName, sys._getframe().f_lineno,u'波特率错误', baud
            return False
        #----
        if bits not in ['7','8']:
            print self.fileName, sys._getframe().f_lineno,u'数据位错误', bits
            return False
        #----
        if stop not in ['1','2']:
            print self.fileName, sys._getframe().f_lineno,u'停止位错误', stop
            return False
        #----
        checks = [u'无','odd','even']
        if check in checks:
            check_idx = str( checks.index(check) )
        else:
            print self.fileName, sys._getframe().f_lineno,u'奇偶校验错误', check
            return False
        #----
        lst_COM_infos = self.get_config_COM_infos( )
        for dct in lst_COM_infos:
            if dct.get(u'Serial Name') == channel_name:
                dct[u'Baud Rate'] = baud_idx
                dct[u'Data Bits'] = bits
                dct[u'Stop Bits'] = stop
                dct[u'Parity Bits'] = check_idx
                paraval=[dct]
                return self.request_config_channel( u'plat.com', u'val_set', paraval=paraval)
        #----
        print self.fileName, sys._getframe().f_lineno, u'通道名称错误:', channel_name
        return False
#--------
    def config_channel_disp( self, dev_name_ch, sig_type_ch, sig_name_ch, web=None, gui=None):
        sid = self.get_sig_sid( dev_name_ch, sig_type_ch, sig_name_ch)
##        obj_id   = u'classify_data'
##        obj_type = u'val_set'
        if sid == '':
            print self.fileName, sys._getframe().f_lineno,u'sid为空:', dev_name_ch, sig_type_ch, sig_name_ch
            return False
        dct = {u'sid':sid}
        if web != None:
            dct[u'web'] = '0' if web == u'ON' else '1'
        if gui != None:
            dct[u'gui'] = '0' if gui == u'ON' else '1'
        paraval = [dct]
        return self.request_config_channel( u'classify_data', u'val_set', paraval=paraval)
#--------
    def get_disp_attr( self, dev_name_ch, sig_type_ch, sig_name_ch):
        comment = {'1':'OFF', '0':'ON'}
        lst_sig_sid = self.get_sig_id_by_dev_sig( dev_name_ch, sig_type_ch ) #lst_sig_sid内容示例:[{u'sid': '281543964622848'}]
        lst_sig_sid[0]['medium'] = 'web'
        paraval = lst_sig_sid
        lst_disp_attr = self.get_request_data( u'classify_data', u'val_get', lst_sig_sid)
        for dct in lst_disp_attr:
            if dct.get('full_name') == sig_name_ch:
                return [ comment.get( dct.get('web') ), comment.get( dct.get('gui') ) ]
        print self.fileName, sys._getframe().f_lineno,u'failure:', dev_name_ch, sig_type_ch, sig_name_ch
        return '',''    
#--------
    #系统维护 - 参数维护 - 导入参数文件
    #文件必须在 file/download 中, 注意：不是 file/upload, 是 download  download  download
    def upload_para( self, file_name):
        para = { u'objectid':u'paramaintain',
                 u'type':u'val_set'
                 }
        
        header = {u'Cookie':g_var.variables.get('cookie')}
        file_dst = {u'uploadfile':(u'data_para.zip',open(self.recordPath + '/download/'+file_name, 'rb'), 'application/x-zip-compressed')} 
        #/power/cgi-bin/main.fcgi #与平台2.1相比,多了一个/zte
        try:
            resp = self.post_request(alias=u'pmsa',uri=u'/power/cgi-bin/main.fcgi',headers=header,data=para,files=file_dst,timeout=20,session=g_var.variables.get('session'))
        except:
            return True #导入参数后,系统立即重启了,所以必须是time_out
        
        return False
    #文件必须在 file/upload 中
    #升级包上传成功后不会立即重启,由其它维护进程,定期检测到有新版本了才升级...,所以,不方便自动化测试,或自动化测试要足够多的延时
    def upload_file_update( self, file_name ):
        para = { u'objectid':u'update',
                 u'type':u'val_set'
                 }
        
        header = {u'Cookie':g_var.variables.get('cookie'),
                  u'Referer':g_var.variables.get(u'ip')
                    }
##        print 'header',header
        print 'file_name=',file_name
        file_dst = {u'uploadfile':(u'powernew.tar.gz',open(file_name, 'rb'), 'application/gzip')}
##        print 'file_dst',file_dst
        #/power/cgi-bin/main.fcgi #与平台2.1相比,多了一个/zte
        resp = self.post_request(alias=u'pmsa',uri=u'/power/cgi-bin/main.fcgi',headers=header,data=para,files=file_dst,timeout=60,session=g_var.variables.get('session'))
##        print 'resp.status_code',resp.status_code
##        print 'resp.text',self.to_json( resp.text ).get('result')
        if resp.status_code != 200: #升级包上传成功后不会立即重启,所以还是可以执行这个代码的
            print u'upload_para failure.',resp.status_code
            return False
        if self.to_json( resp.text ).get('result')!='ok':
            print u'upload_para failure.',self.to_json( resp.text ).get('result')
            return False        
        return True
    #判断系统是否重启,用于上传了升级包之后的判断
    def CSU_is_restarting( self, time_out=300): #验证300秒内,系统必定重启
        now = time.time()
        while time.time()-now < time_out:
            user_level = self.get_heart_beat( )
            if float(user_level) < 0:
                print self.fileName, sys._getframe().f_lineno, u'没有心跳了,CSU重启了'
                return True
            #print self.fileName, sys._getframe().f_lineno, user_level, int(time.time() - now) ##验证结果: 能重启,时间:65s/120s/126s 等,估计要向开发人员求证了
            time.sleep( 5 )
        return False
    #--------
    #功能:为了兼容参数名称中含有index信息,例如: 电池组容量[1]
    def split_sig_name(self, sig_name):
        sig_index = u'0'
        if '[' in sig_name:
            s = sig_name.strip(']') #strip - 去掉字符串两端的字符
            sig_name, sig_index = s.split('[')
        return sig_name, sig_index
    #--------
    #组合sig_name和sig_index
    #调用前需要确保 sig_index != u'0'
    def connect_sig_name_index(self, sig_name, sig_index):
        sig_name = self.match_string( sig_name )
        if sig_index != u'0':
            return sig_name + u'[' + sig_index + u']'
        else:
            return sig_name
    #--------
    #系统维护 - 参数维护 - 恢复备份值
    def resume_para_all(self):
        paraval=[{"id":'0'}]
        lst = self.get_request_data( u'parabakrecover', u'val_set', paraval, time_out=50) #time_out=50
        #lst == None: 表明timeout内没有收到CSU的响应,即CSU重新启动了
        return True if lst == None else False
    #----
    #系统维护 - 参数维护 - 恢复默认值
    def resume_para_default(self):
        paraval=[{"id":'1'}]
        lst = self.get_request_data( u'parabakrecover', u'attr_set', paraval, time_out=50)
        #lst == None: 表明timeout内没有收到CSU的响应,即CSU重新启动了
        return True if lst == None else False
    #----
    #系统维护 - 参数维护 - 参数备份
    def bak_para_all(self):
        paraval=[{}]
        lst = self.get_request_data( u'parabakrecover', u'inst_add', paraval)
        return lst == []
    #--------
    #删除历史记录, 对应按键所在页面： 系统维护-系统维护
    #删除历史数据
    def del_his_rec(self, dest):
        dct_1 = {u'所有':u'allsql',
                 u'历史数据':u'history_data',
                 u'历史事件':u'history_event',
                 u'历史告警':u'history_alarm' }
        
        dct_2 = {u'市电有电记录':u'0',
                 u'市电停电记录':u'1',
                 u'电池充电记录':u'2',
                 u'电池测试记录':u'3',
                 u'电池放电记录':u'4',
                 u'电池均充记录':u'5' }
        arg = dct_1.get( dest )
        if arg != None:
            objID  = u'del_hisrecord'
            paraval= [{"sqlname":arg}]
        else:
            arg = dct_2.get( dest )
            if arg != None:
                objID   = u'records'
                paraval = [{"record_type":arg}]
            else:
                print self.fileName, sys._getframe().f_lineno, u'入参错误', dest
                return False
        lst = self.get_request_data( objID, u'inst_delete', paraval)
        return lst == []
    #================
    def set_para_default_2_dev_para(self, dev_1, para_1, dev_2, para_2):
        print u'恢复默认值：',dev_1, para_1, dev_2, para_2
        if g_var.variables.get('data_dict') == None:
            g_var.variables.update({'data_dict':self.openCsvAsList( self.recordPath + '\\data_dictionary.csv' )})
        #----
        lst_default_val = []
        devs  = [dev_1, dev_2]
        paras = [para_1, para_2]
        for i in range(2):
##            for s in self.lst_data_dct:
            for s in g_var.variables.get('data_dict'):
                lst = s.split(',')
                if len(lst) < 20: #个别约束关系的描述中,有换行符
                    continue
                if devs[i] == lst[1] and paras[i] == lst[13]:
                    lst_default_val.append( lst[19] )
                    break
            else:
                print self.fileName, sys._getframe().f_lineno, u'参数名称错误:', devs[i], paras[i]
                lst_default_val.append( '0' )
        #----
        print self.fileName, sys._getframe().f_lineno, 'default:', lst_default_val
        para_num = 2
        loop_cnt = 0
        while loop_cnt < para_num + 2: #多尝试2次
            ok_cnt = 0
            for i in range( para_num ):
                if abs(float(lst_default_val[i]) - float(self.get_web_data_para( devs[i], paras[i] ) ) ) < 0.0001:
                    ok_cnt += 1
                else:
                    self.set_web_data_para( devs[i], paras[i], dest={u'值':lst_default_val[i]} )
            if ok_cnt == para_num:
                return True
            loop_cnt += 1
            print self.fileName, sys._getframe().f_lineno, u'loop_cnt:', loop_cnt
        return False
        #----
    def set_para_default_3_dev_para(self, dev_1, para_1, dev_2, para_2, dev_3, para_3):
        print u'恢复默认值：',dev_1, para_1, dev_2, para_2, dev_3, para_3
        if g_var.variables.get('data_dict') == None:
            g_var.variables.update({'data_dict':self.openCsvAsList( self.recordPath + '\\data_dictionary.csv' )})
        #----
        lst_default_val = []
        devs  = [dev_1, dev_2, dev_3]
        paras = [para_1, para_2, para_3]
        for i in range( 3 ):
            #for s in self.lst_data_dct:
            for s in g_var.variables.get('data_dict'):
                lst = s.split(',')
                if len(lst) < 20: #个别约束关系的描述中,有换行符
                    continue
                if devs[i] == lst[1] and paras[i] == lst[13]:
                    lst_default_val.append( lst[19] )
                    break
            else:
                print self.fileName, sys._getframe().f_lineno, u'参数名称错误:', devs[i], paras[i]
                lst_default_val.append( '0' )
        #----
        #print self.fileName, sys._getframe().f_lineno, 'default:', lst_default_val
        para_num = 3
        loop_cnt = 0
        while loop_cnt < para_num + 2: #多尝试2次
            ok_cnt = 0
            for i in range( para_num ):
                #print lst_default_val[i],devs[i],paras[i]
                if abs(float(lst_default_val[i]) - float(self.get_web_data_para( devs[i], paras[i] ) ) ) < 0.0001:
                    ok_cnt += 1
                else:
                    self.set_web_data_para( devs[i], paras[i], dest={u'值':lst_default_val[i]} )
            if ok_cnt == para_num:
                return True
            loop_cnt += 1
            #print self.fileName, sys._getframe().f_lineno, u'loop_cnt:', loop_cnt
        return False
        #----
    #
    def set_para_default(self, dev_name, lst_para_name):
        print u'恢复默认值：',dev_name, ','.join(lst_para_name)
        if g_var.variables.get('data_dict') == None:
            g_var.variables.update({'data_dict':self.openCsvAsList( self.recordPath + '\\data_dictionary.csv' )})
        #----
        lst_default_val = []
        for para_name in lst_para_name:
##            for s in self.lst_data_dct:
            for s in g_var.variables.get('data_dict'):
                lst = s.split(',')
                if len(lst) < 20: #个别约束关系的描述中,有换行符
                    continue
                if dev_name == lst[1] and para_name == lst[13]:
                    lst_default_val.append( lst[19] )
                    break
            else:
                print self.fileName, sys._getframe().f_lineno, u'参数名称错误:', para_name
                lst_default_val.append( '0' )
        #----
        #print self.fileName, sys._getframe().f_lineno, 'default:', lst_default_val
        para_num = len(lst_para_name)
        loop_cnt = 0
        while loop_cnt < para_num + 2: #多尝试2次
            ok_cnt = 0
            for i in range( para_num ):
                if abs(float(lst_default_val[i]) - float(self.get_web_data_para( dev_name, lst_para_name[i] ) ) ) < 0.0001:
                    ok_cnt += 1
                else:
                    self.set_web_data_para( dev_name, lst_para_name[i], dest={u'值':lst_default_val[i]} )
            if ok_cnt == para_num:
                return True
            loop_cnt += 1
            #print self.fileName, sys._getframe().f_lineno, u'loop_cnt:', loop_cnt
        return False
    #--------
    #返回值格式:
    def openCsvAsList( self, file_absPath ):
        if not os.path.exists( file_absPath ):
            print( sys._getframe().f_lineno,'Err: file is not exist,', file_absPath)
            return []
        f = open(file_absPath,'r')
        lst = f.readlines()
        f.close()
        #---
        code_language,code_name = self.test_file_code( file_absPath )
##        print self.fileName, sys._getframe().f_lineno,'lan=',code_language,'code=',code_name
        if code_name == 'utf-8':
            lstOut = []
            for s in lst:
                ret = s.decode( code_name )
                lstOut.append( ret )
            return lstOut
    
        if code_language != 'Chinese':
            return lst
        lstOut = []
        for s in lst:
            ret = s.decode( code_name )
            lstOut.append( ret )
        return lstOut
    #------------------------
    def test_file_code(self,fileName):
        f = open(fileName,'r')
        data = f.read()
        code_info = chardet.detect( data ) #<type 'dict'>
        f.close()
        return code_info.get('language'), code_info.get( 'encoding' )
    #================
    #下面的函数是用于本文件的自测代码中了
    #获取指定 设备名称、信号类型 的所有信号名称
    def get_lsg_sig_name(self,dev_name_ch,sig_type_ch):
        lst_sig_name = []
        lst_sig_attr = self.get_sig_attr_by_dev_sig( dev_name_ch, sig_type_ch)
        if lst_sig_attr == None:
            return []
        for item in lst_sig_attr:
            sig_name = item.get(u'full_name')
            if sig_name == None:
                continue
            lst_sig_name.append( sig_name )
        return lst_sig_name
    #--
    #获取参数的上下限
    #返回值: [ min, max ]   , 字符串格式,使用时需要转换
    def get_para_range(self,dev_name_ch,sig_name_ch, sig_index=u'0'):
        lst_sig_attr = self.get_sig_attr_by_dev_sig( dev_name_ch, u'参数量')
        if lst_sig_attr == None:
            return ['0','0']
        if sig_index != u'0':
            sig_name_ch += ('['+sig_index+']')
        for item in lst_sig_attr:
            if sig_name_ch == item.get(u'full_name'):
                min_value = item.get(u'min_value')
                max_value = item.get(u'max_value')
                break
        else:
            print self.fileName, sys._getframe().f_lineno, u'信号名称错误:', sig_name_ch
            return [ '0','0']
        return [min_value, max_value]
    #----
    #temp
    def test(self): #打印系统所有的 设备名称
        lst_dev_attr = self.get_lst_dev_attr( )
        if lst_dev_attr == None:
            return ''
        for item in lst_dev_attr:
            name = item.get(u'device name')
            print self.fileName, sys._getframe().f_lineno, name
        return True
##============================以下内容为Pengy新增加=================================================
    ##将数据字典转换为列表格式，并去掉数据字典内错误的换行
    def Conversion_data_dict_format(self):
        lst_new=[]
        newStr=''
        if g_var.variables.get('data_dict') == None:
            g_var.variables.update({'data_dict':self.openCsvAsList( self.recordPath + '\\data_dictionary.csv' )})
        #将数据字典中约束关系有换行的条目去掉换行符，组成一条完整数据
        for s in g_var.variables.get('data_dict'):
            if s.count(',')>=54:
                lst_new.append(s)
            else:
                newStr+=s.strip('\n')

            if newStr.count(',')>=54:
##                print newStr
                lst_new.append(newStr)
                newStr=''
        return lst_new

    ##将data_dictionary_devices转换为列表格式，并去掉数据字典内错误的换行
    def Conversion_data_dictionary_devices_format(self):
        lst_new=[]
        newStr=''
        if g_var.variables.get('data_dict_devices') == None:
            g_var.variables.update({'data_dict_devices':self.openCsvAsList( self.recordPath + '\\data_dictionary_devices.csv' )})
        #将数据字典中约束关系有换行的条目去掉换行符，组成一条完整数据
        for s in g_var.variables.get('data_dict_devices'):
            lst_new.append(s)
        return lst_new
    #通过参数量返回设备名称
    def get_dev_name(self, para_name=u'厂家名称'):
        dictList=self.Conversion_data_dict_format()

        for s in dictList:
            lst = s.split(',')
            if para_name == lst[13]:
                return lst[1]

    #通过数据字典获取信号名称对应的数据字典信息的列表 Pengy
    def get_SID_from_data_dict(self, sig_name_ch):
        dictList=self.Conversion_data_dict_format()        

        for s in dictList:
            lst = s.split(',')
            if sig_name_ch == lst[13] and lst[6]!=u'映射参数：目的参数':
##                print 'lst',lst
                return lst
        return None

    #通过计算dev_name_ch,sig_name_ch数量，得到实际的SID Pengy
    def calc_SID_with_devName_sigName(self, dev_name_ch,sig_name_ch):
        devList=dev_name_ch.split('_')
        sigList=sig_name_ch.split('_')
        if len(devList)>1:
            dev_name_ch=devList[0]
            dev_Index=int(devList[1])
        else:
            dev_Index=0
        if len(sigList)>1:
            sig_name_ch=sigList[0]
            sig_Index=int(sigList[1])
        else:
            sig_Index=0            
        dictList=self.Conversion_data_dict_format()
        
        for s in dictList:
            lst = s.split(',')
            if sig_name_ch == lst[13] and dev_name_ch == lst[1]:
                SID=lst[7]
                if sig_Index>0:
                    sig_Index=sig_Index-1                    
                    SID=str(hex(int(SID[2:],16)+int(sig_Index)).replace('L','')).decode('utf8') #hex( sid ) 得到的是 ‘0xXXXXXXXXL’, 最后有个L,会出错
                if dev_Index>0:
                    dev_Index=dev_Index-1
                    SID=str(hex(int(SID[2:6],16)+int(dev_Index)).replace('L','')).decode('utf8')+SID[6:]   
                return SID
        return None

    #功能：获取宏定义对应的值
    #入参：参数名称-宏定义全称
    #返回值：宏定义值
    def get_dict_define(self, dict_define):      
        if g_var.variables.get('dict_define') == None:
            g_var.variables.update({'dict_define':self.openCsvAsList( self.recordPath + '\\data_dictionary_macros.csv' )})
        for s in g_var.variables.get('dict_define'):
            lst = s.split(',')
            if dict_define == lst[0]:
                dict_define_value=int(lst[1])
                return dict_define_value
        print self.fileName, sys._getframe().f_lineno, u'没有该宏定义:', dict_define
        return None
    
    #转换sig_name_ch格式，并获取dev_name_ch, sig_index
    #将带有下划线的信号名称分解为无下划线的信号名称和sig_index，
    #将带有‘-’的信号名称分解为无‘-’的信号名称，并通过信号名称和‘-’的数字产生dev_name_ch
    #返回dev_name_ch,sig_name_ch_new,sig_index
    def transform_sig_name_ch(self,sig_name_ch):
        sig_name_ch_List=re.split('[-_]',sig_name_ch.strip()) #使用正则分割多个分隔符
        if len(sig_name_ch_List)==1:#如果没有多个设备名称和多个信号名称
            sig_name_ch_new=sig_name_ch
            sig_index=u'0'
            dev_name_ch=self.get_dev_name(sig_name_ch_new)#用参数名在数据字典中找到设备名称
        elif len(sig_name_ch_List)==2:
            if '_' in sig_name_ch:#如果有多个信号名称
                sig_name_ch_new=sig_name_ch_List[0]
                sig_index=sig_name_ch_List[1]
                dev_name_ch=self.get_dev_name(sig_name_ch_new)#用参数名在数据字典中找到设备名称
            elif '-' in sig_name_ch:#如果有多个设备名称
                sig_name_ch_new=sig_name_ch_List[0]
                sig_index=u'0'        
                dev_name_ch=self.get_dev_name(sig_name_ch_new)+'_'+sig_name_ch_List[1]
            else:
                print self.fileName, sys._getframe().f_lineno, u'Err,类型错误',sig_name_ch
                return False
        elif len(sig_name_ch_List)==3:#如果有多个设备名称和多个信号名称
                sig_name_ch_new=sig_name_ch_List[0]
                sig_index=sig_name_ch_List[1]
                dev_name_ch=self.get_dev_name(sig_name_ch_new)+'_'+sig_name_ch_List[2]
        else:
            print self.fileName, sys._getframe().f_lineno, u'Err,类型错误',sig_name_ch
            return False            
        return dev_name_ch,sig_name_ch_new,sig_index

    #实现参数值计算
    def calc_params_value(self,sig_name_ch,params):
        params=str(params)
        if '+' in params:
            paramsList=params.split('+')
            if paramsList[0]!='':
                params=float(paramsList[0])+float(paramsList[1])
            else:
                params=float(paramsList[1])
        elif '-' in params:
            paramsList=params.split('-')
            if paramsList[0]!='':
                params=float(paramsList[0])-float(paramsList[1])
            else:
                params=0-float(paramsList[1])
        data_type=self.get_SID_from_data_dict(sig_name_ch)[16]#获取该参数的数据类型，并将入参转换为相同类型
        if data_type=='int':
            params=str(int(params))
        elif data_type=='float':
            params=str(float(params))
        return params
    
    #设置参数关键字 Pengy:2019/11/26
    def set_web_data_para_KeyWord(self, sig_name_ch, params ):
        get_para=self.get_web_data_para_KeyWord11(sig_name_ch)  #用原方法设置需要先查询参数是否存在
        if get_para=='':
            print self.fileName, sys._getframe().f_lineno, u'Err,没有查到该参数,sig_name_ch=',sig_name_ch
            return False
        
        params=str(params)
        if '+' in params:
            paramsList=params.split('+')
            try:
                print float(paramsList[0]),float(paramsList[1])   #判断加号后面是不是数字         
                params=float(paramsList[0])+float(paramsList[1])
            except:
                params=str(params)            
        elif '-' in params:
            paramsList=params.split('-')
            try:
                print float(paramsList[0]),float(paramsList[1])            
                params=float(paramsList[0])-float(paramsList[1])
            except:
                params=str(params)
                
        
        dryFlag=0
        if sig_name_ch[-3:]==u'干接点':
            dryFlag=1
            sig_name_ch1=sig_name_ch[:-3]
        else:
            sig_name_ch1=sig_name_ch
            
        dev_name_ch, sig_name_ch_new, sig_index=self.transform_sig_name_ch(sig_name_ch1)#处理有多个信号量的
        
        data_type=self.get_SID_from_data_dict(sig_name_ch_new)[16]#获取该参数的数据类型，并将入参转换为相同类型
        if data_type=='int':
            params=str(int(params))
        elif data_type=='float':
            params=str(float(params))
            
##        print u'设置',sig_name_ch,u'参数为：',params
        if int(sig_index)>0: 
            sig_sid=str(int(self.get_SID_from_data_dict(sig_name_ch_new)[7],16)+int(sig_index)-1)
        else:
            sig_sid=self.get_SID_from_data_dict(sig_name_ch_new)[7]

        if sig_sid==None:
            print self.fileName, sys._getframe().f_lineno, u'Err,没有查到该参数的SID，可能该参数不存在',sig_name_ch
            return False

        if dryFlag==1:#设置告警干接点
##            print sig_sid,params
            set_para=self.get_request_data('signal','val_set',[{"sid":sig_sid,"alm_relay":params}])
##            print 'set_para',set_para
            time.sleep(0.5)
            get_para=self.get_web_data_para_KeyWord(sig_name_ch)
            if float(params)==float(get_para):
                return True
            else:
                return False
        if self.get_SID_from_data_dict(sig_name_ch_new)[10]==u'parameter':            
            set_para=self.get_request_data('signal','val_set',[{"sid":sig_sid,"value":params}])
##            print 'set_para',set_para
            time.sleep(0.5)
            get_para=self.get_web_data_para_KeyWord(sig_name_ch)
            try:    #处理数字
                if float(params)==float(get_para):
                    return True
                else:
                    return False
            except: #处理字符
                if (u'电池启用日期' in sig_name_ch) or (u'预约均充日期' in sig_name_ch):    #电池启用日期特殊处理，因为收到的发送的不一致
                    get_para =str(get_para).replace('-','/') 
                if str(params)==str(get_para):
                    return True
                else:
                    return False
        if self.get_SID_from_data_dict(sig_name_ch_new)[10]==u'alarm':#设置告警级别
            set_para=self.get_request_data('signal','val_set',[{"sid":sig_sid,"alm_level":params}])
##            print 'set_para',set_para
            time.sleep(0.5)
            get_para=self.get_web_data_para_KeyWord(sig_name_ch)
            if float(params)==float(get_para):
                return True
            else:
                return False
        return False

    #查询参数关键字 Pengy:2019/11/26
    def get_web_data_para_KeyWord(self, sig_name_ch):
        dryFlag=0
        if sig_name_ch[-3:]==u'干接点':
            dryFlag=1
            sig_name_ch=sig_name_ch[:-3]

        dev_name_ch, sig_name_ch_new, sig_index=self.transform_sig_name_ch(sig_name_ch)#处理有多个信号量的

        if int(sig_index)>0: 
            sig_sid=str(int(self.get_SID_from_data_dict(sig_name_ch_new)[7],16)+int(sig_index)-1)
        else:
            sig_sid=self.get_SID_from_data_dict(sig_name_ch_new)[7]
##        print 'sig_sid',sig_sid
        if sig_sid==None:
            print self.fileName, sys._getframe().f_lineno, u'Err,没有查到该参数的SID，可能该参数不存在',sig_name_ch
            return False        
        get_para=self.get_request_data('signal','val_get',[{"sid":sig_sid}])
##        print 'get_para',get_para
        if dryFlag==1:
            return get_para[0].get('alm_relay') #返回告警干接点
        if get_para[0].get('value')==None:
            return get_para[0].get('alm_level') #返回告警级别
        return get_para[0].get('value') #返回参数值

    #查询参数关键字 Pengy:2019/12/29
    #使用原方法查询参数
    def get_web_data_para_KeyWord11(self, sig_name_ch):
        dryFlag=0
        if sig_name_ch[-3:]==u'干接点':
            dst=u'告警干接点'
            dryFlag=1
            sig_name_ch=sig_name_ch[:-3]

        dev_name_ch, sig_name_ch_new, sig_index=self.transform_sig_name_ch(sig_name_ch)#处理有多个信号量的

        signal_type=self.get_SID_from_data_dict(sig_name_ch_new)[10]
        dimension=self.get_SID_from_data_dict(sig_name_ch_new)[17]  #信号数量定义
        
        if signal_type=='alarm' and dryFlag!=1:
            dst=u'告警级别'
        elif signal_type=='parameter':
            dst=u'值'

        #解决告警参数的dev_name_ch没有下标的问题    
        if dst==u'告警级别' or dst==u'告警干接点':
            devicesList=self.Conversion_data_dictionary_devices_format()
            for i in devicesList:
                lst1=i.split(',')
                if lst1[0]==dev_name_ch and int(lst1[2])>1:
                    dev_name_ch=dev_name_ch+'_1'

        #解决告警参数的sig_name_ch没有下标的问题    
        if dst==u'告警级别' or dst==u'告警干接点':
            if dimension !='1' and dimension !='':
                sig_index='1'
                    
##        print dev_name_ch, sig_name_ch_new, sig_index
##        print 'signal_type',signal_type,dst
        get_para=self.get_web_data_para(dev_name_ch, sig_name_ch_new, dst, sig_index)
        return get_para

 
    #查询实时数据关键字 Pengy
    #通过该关键字能查询指定数据名称的任一analog data、digital data、device info、record data中的一个数据
    def get_web_data_real_KeyWord(self, sig_name_ch):
        signal_type=''
        dev_name_ch, sig_name_ch_new, sig_index=self.transform_sig_name_ch(sig_name_ch)
        dictList=self.Conversion_data_dict_format()    
        for s in dictList:
            lst = s.split(',')
            if sig_name_ch_new == lst[13]:
                signal_type = lst[10]
                break
        if signal_type=='analog data':
##            print 'signal_type',signal_type,dev_name_ch, sig_name_ch_new, sig_index
            return self.get_web_data_ana(dev_name_ch, sig_name_ch_new, sig_index)
        elif signal_type=='digital data':
            return self.get_web_data_dig(dev_name_ch, sig_name_ch_new, sig_index)
        elif signal_type=='device info':
            return self.get_web_data_factory(dev_name_ch, sig_name_ch_new, sig_index)
        elif signal_type=='record data':
            return self.get_web_data_record(dev_name_ch, sig_name_ch_new, sig_index)
        else:
            sid=self.calc_SID_with_devName_sigName(dev_name_ch,sig_name_ch_new)
            get_data_real=self.get_request_data('signal','val_get',[{"sid":sid}])
            return get_data_real[0].get('value')
##        print u'没有该类型数据！' ##Pengy20200609:产品安全治理
##        return ''



    #功能：获取参数的上下限范围
    #入参：参数名称-中文全称
    #返回值：[缺省值,下限,上限]
    def get_para_scope(self, sig_name_ch):
        dev_name_ch, sig_name_ch_new, sig_index=self.transform_sig_name_ch(sig_name_ch)
        dictList=self.Conversion_data_dict_format()
        for s in dictList:
            lst = s.split(',')
##            print 'sig_name_ch_new',sig_name_ch_new,sig_name_ch_new == lst[13],lst
            if sig_name_ch_new == lst[13]:
                pre = 0 if lst[18] == '' else int(lst[18])
                val_devObj=lst[1]
                val_optional_attribute=lst[5]
                val_min=lst[20]
                val_def=lst[19]
                val_max=lst[21]
                val_data_type=lst[16]
                attr=lst[4] #attr.n$所属属性
##                print 'pre',pre
##                print 'val_devObj',val_devObj
##                print 'val_optional_attribute',val_optional_attribute                 
##                print 'val_def',val_def
##                print 'val_min',val_min                
##                print 'val_max',val_max
##                print 'val_data_type',val_data_type
##                print 'attr',attr

                if attr==u'基本属性':
                    if val_data_type=='float':
                        try:
                            val_def = float(val_def)
                        except:
                            val_def = float(self.get_dict_define(val_def))
                        try:
                            val_min = float(val_min)
                        except:
                            val_min = float(self.get_dict_define(val_min))
                        try:
                            val_max = float(val_max)
                        except:
                            val_max = float(self.get_dict_define(val_max))                                            
                        return val_def, val_min, val_max
                    elif val_data_type=='int' :
                        try:
                            val_def = int(val_def)/pow(10,pre)
                        except:
                            val_def = int(self.get_dict_define(val_def))/pow(10,pre)
                        try:
                            val_min = int(val_min)/pow(10,pre)
                        except:
                            val_min = int(self.get_dict_define(val_min))/pow(10,pre)
                        try:
                            val_max = int(val_max)/pow(10,pre)
                        except:
                            val_max = int(self.get_dict_define(val_max))/pow(10,pre)                                            
                        return val_def, val_min, val_max
                    elif val_data_type=='string32' or val_data_type=='date' :
                        return val_def, val_min, val_max
                    else:
                        return False

                elif attr==u'扩展属性':                    
                    xList=[]
                    ##找到该参数对应的设备对象是哪个参数的选项，然后查询设备对象对应的参数目前的值是什么，以此来判断该参数是否有效
                    for d in  g_var.variables.get('data_dict'):
                        dlst = d.split(',')
                        if len(dlst) < 20: #个别约束关系的描述中,有换行符
                            continue
                        if val_devObj+'/' in dlst[23]:#如果该设备对象在参数的选项中：
##                            print dlst[13]
                            para_Value=self.get_web_data_para_KeyWord(dlst[13])
                            if len(para_Value)>0:#如果查到了该值，继续处理
                                xxlst= dlst[23].split(';')#比如将：“0:普通铅酸电池/VRLA Batt;1:循环电池/Cycling Batt;”分成列表
    ##                            print 'xxlst',xxlst
                                for i in xxlst:
                                    iList=i.split(':')  #再用‘：’将“0:普通铅酸电池/VRLA Batt”分开，得到0对应的是普通铅酸电池/VRLA Batt
                                    if iList[0]==para_Value:
    ##                                    print iList[1]
                                        xList=iList[1].split('/')   #将“普通铅酸电池/VRLA Batt”分开，得到普通铅酸电池
##                                        print xList[0]
                                    if xList==[]:
                                        return False                                        
                                    if xList[0]==val_devObj:
                                        if val_data_type=='float':
                                            try:
                                                val_def = float(val_def)
                                            except:
                                                val_def = float(self.get_dict_define(val_def))
                                            try:
                                                val_min = float(val_min)
                                            except:
                                                val_min = float(self.get_dict_define(val_min))
                                            try:
                                                val_max = float(val_max)
                                            except:
                                                val_max = float(self.get_dict_define(val_max))                                            
                                            return val_def, val_min, val_max
                                        elif  val_data_type=='int' :
                                            try:
                                                val_def = int(val_def)/pow(10,pre)
                                            except:
                                                val_def = int(self.get_dict_define(val_def))/pow(10,pre)
                                            try:
                                                val_min = int(val_min)/pow(10,pre)
                                            except:
                                                val_min = int(self.get_dict_define(val_min))/pow(10,pre)
                                            try:
                                                val_max = int(val_max)/pow(10,pre)
                                            except:
                                                val_max = int(self.get_dict_define(val_max))/pow(10,pre)                                           
                                            return val_def, val_min, val_max
                                        else:
                                            return False
        #处理是扩展属性，但该参数的设备对象不是其它参数的选项的情况
        for s in dictList:
            lst = s.split(',')

            if sig_name_ch_new == lst[13]:
                pre = 0 if lst[18] == '' else int(lst[18])
                val_devObj=lst[1]
                val_optional_attribute=lst[5]
                val_min=lst[20]
                val_def=lst[19]
                val_max=lst[21]
                val_data_type=lst[16]
                attr=lst[4] #attr.n$所属属性
##                print 'pre',pre
##                print 'val_devObj',val_devObj
##                print 'val_optional_attribute',val_optional_attribute                 
##                print 'val_def',val_def
##                print 'val_min',val_min                
##                print 'val_max',val_max
##                print 'val_data_type',val_data_type
##                print 'attr',attr

                if val_data_type=='float':
                    try:
                        val_def = float(val_def)
                    except:
                        val_def = float(self.get_dict_define(val_def))
                    try:
                        val_min = float(val_min)
                    except:
                        val_min = float(self.get_dict_define(val_min))
                    try:
                        val_max = float(val_max)
                    except:
                        val_max = float(self.get_dict_define(val_max))                                            
                    return val_def, val_min, val_max
                elif val_data_type=='int' :
                    try:
                        val_def = int(val_def)/pow(10,pre)
                    except:
                        val_def = int(self.get_dict_define(val_def))/pow(10,pre)
                    try:
                        val_min = int(val_min)/pow(10,pre)
                    except:
                        val_min = int(self.get_dict_define(val_min))/pow(10,pre)
                    try:
                        val_max = int(val_max)/pow(10,pre)
                    except:
                        val_max = int(self.get_dict_define(val_max))/pow(10,pre)                                            
                    return val_def, val_min, val_max
                else:
                    return False                            
    #解析参数约束关系字符串，获取约束关系列表，如[u'>=', u'+', u'Battery Group', u'Float Charge Voltage', u'1']
    #入参：去掉引号的参数约束关系字符串
    #返回值：取约束关系列表
    def get_para_relationList(self, relation):                                        
        if relation[-1]==';':#去掉最后一个分号
            relation=relation[:-1]
        
        relationList = relation.split(';') #将每个约束关系分割成列表

        outLst=[]
        Symbol=''
        for i in relationList:
            relationSymbol=i[:2]    #获得关系符号，>=或者<=
            if '+' in i:            #获得+或者-关系符号
                Symbol=u'+'
            elif '-' in i:
                Symbol=u'-'
            else:
                Symbol=u''
##            print i ,relationSymbol,Symbol
            relationSymbolList=[relationSymbol,Symbol]  #例[u'>=', u'+']
            for j in [u'A',u'V',u'%',u'℃']:#去掉单位
                if j == i[-1]:
                    i=i[:-1]
                    break

            #去掉关系符号，>=或者<=和设备名称
            if '|' in i:
                i=i.split('|')[1]
            else:
                i=i[2:]

            singleRelationList=re.split('[+-]',i.strip()) #使用正则分割多个分隔符
            singleRelationList=relationSymbolList+singleRelationList#例[u'>=', u'+', u'Battery Group', u'Float Charge Voltage', u'1']
##            print 't1',singleRelationList
            outLst.append(singleRelationList)
##        print 'outLst',outLst
        return outLst


    #功能：获取参数的可设置范围
    #入参：参数名称-中文全称
    #返回值：[下限,上限]
    def get_para_set_scope(self, sig_name_ch):
        dictList=self.Conversion_data_dict_format() 
        dev_name_ch, sig_name_ch_new, sig_index=self.transform_sig_name_ch(sig_name_ch)
        sig_name_ch_new_Value=self.get_web_data_para_KeyWord(sig_name_ch_new)
        if sig_name_ch_new_Value=='':
            print self.fileName, sys._getframe().f_lineno, u'Err,没有查到该参数值，可能该参数不存在',sig_name_ch_new
            return [ None, None ]

        #获取到取值约定,并去掉前面的引号
        for x in dictList:
            lst=x.split(',')
            if sig_name_ch_new == lst[13]:
                if lst[24]!='' and lst[24][0]=='"':
                    relation=lst[24][1:-1]
                else:
                    relation=lst[24]
                break
##        print 'relation',relation,lst[24]
        try:
            val_def,val_min,val_max = self.get_para_scope( sig_name_ch_new )#获取参数的上下限
        except:
            print self.fileName, sys._getframe().f_lineno, u'Err,没有获取到参数的上下限',sig_name_ch_new
            return [ None, None ]
##        print val_def,val_min,val_max
    
        #没有约束关系，直接取该参数的上下限
        if relation == '':  
            print self.fileName, sys._getframe().f_lineno, sig_name_ch_new, val_min, val_max
            return [ val_min, val_max ]

        relationLst=self.get_para_relationList(relation)    #解析参数约束关系字符串，获取约束关系列表
        maxValList=[]
        minValList=[]
        for i in relationLst:
##            print i,i[2],len(i[2])
            if i[2][-1]==' ':   #有的约束关系中，+/-号前面有空格，需要去掉空格
                i[2]=i[2][:-1]
            for x in dictList:
                lst=x.split(',')
                if i[2]==lst[11]:
                    para=lst[13]
                    break

##            print 'para',para
            try:
                para_Value=self.get_web_data_para_KeyWord(para)
##                print 'para_Value',para_Value
                #处理加减约束关系
                if len(i)==4:
                    if i[1]==u'-':
                        para_Value=float(para_Value)-float(i[3])
                    elif i[1]==u'+':
                        para_Value=float(para_Value)+float(i[3])
                #处理大小约束关系
                if i[0] == '<=':
                    maxValList.append(float(para_Value))
                if i[0] == '>=':
                    minValList.append(float(para_Value))              
            except:
                print self.fileName, sys._getframe().f_lineno, u'Err,没有查到该参数值，可能该参数不存在',para

        
        maxValList.append(val_max)       
        minValList.append(val_min)
        print sig_name_ch_new,u'可设置范围为：',[max(minValList),min(maxValList)] 
##        print 'maxValList',maxValList.
##        print 'minValList',minValList

        return [max(minValList),min(maxValList)] 
  
    #直接获取Web上的参数设置范围
    #返回：下限值，上限值
    def get_web_para_set_scope(self, sig_name_ch):
        lst_new=[]
        newStr=''
        dev_name_ch, sig_name_ch_new, sig_index=self.transform_sig_name_ch(sig_name_ch)
        sig_name_ch_new_Value=self.get_web_data_para_KeyWord(sig_name_ch_new)
        if sig_name_ch_new_Value=='':
            print self.fileName, sys._getframe().f_lineno, u'Err,没有查到该参数值，可能该参数不存在',sig_name_ch_new
            return [ None, None ]
        lst_sig_attr = self.get_sig_attr_by_dev_sig( dev_name_ch, u'参数量')
        signal_name = self.connect_sig_name_index( sig_name_ch_new, sig_index )
        for item in lst_sig_attr:
##            print 'item',item
            if signal_name == item.get(u'full_name'):
                try:
                    return [float(item.get(u'min_value')),float(item.get(u'max_value'))]
                except:
                    return [item.get(u'min_value'),item.get(u'max_value')]
        print self.fileName, sys._getframe().f_lineno, u'Err:signal_name=', signal_name
        return [ None, None ]


    #----编写测试用例过程中的特殊需求----
    #功能：获取参数的数量
    #入参：参数名称-中文全称
    #返回值：数量
    def get_para_count(self, sig_name_ch):
        dev_name_ch, sig_name_ch_new, sig_index=self.transform_sig_name_ch(sig_name_ch)
        dictList=self.Conversion_data_dict_format() 
        for s in dictList:
            lst = s.split(',')

            if sig_name_ch_new == lst[13]:
                try:
                    value = int(lst[17])
                    return value
                except:
                    return self.get_dict_define(lst[17])

        print self.fileName, sys._getframe().f_lineno, u'没有找到参数:',sig_name_ch_new
        return None                

    #功能：获取工作状态的整流器地址
    #返回值：列表,整流器地址
    def get_smr_addr_working(self):
        ret=[]
        SmrListDict=self.get_request_data('business_object_dev','list',[{"bo_id":"2"}])#获取整流器列表
        for i in SmrListDict:
##            print i.get(u'device name')
            if '_' in i.get(u'device name'):
                smrState=i.get(u'device name').split('_')[1]#提取整流器地址
                if self.get_web_data_real_KeyWord(u'整流器关机状态-'+smrState)=='0':#获取整流器关机状态
                    ret.append(smrState)
        return ret

    #功能：设置Web上所有可视的参数为默认值
    def set_All_para_default(self):
        Faild_data_para=[]
        get_Business_obj_list=self.get_request_data('business_object','val_get',[{"service":"2"}])
##        print get_Business_obj_list
        for i in get_Business_obj_list:
            bo_id=i.get("bo_id")
            print bo_id
            get_Business_obj_paraset=self.get_request_data('paraset','val_get',[{"bo_id":bo_id}])
            for j in get_Business_obj_paraset:
##                print j.get("full_name")                
                if j.get("value") != None:
                    if j.get("full_name")[-1] == ']':
                        StrIndex=j.get("full_name").index('[')
##                        print j.get("full_name").index('[')
                        sig_name_ch_new = j.get("full_name")[:StrIndex]+'_'+j.get("full_name")[StrIndex+1]
##                        print 'sig_name_ch_new',sig_name_ch_new
                    else:
                        sig_name_ch_new=j.get("full_name")
                    default_value=self.get_para_scope(sig_name_ch_new)
##                    print 'sig_name_ch_new',sig_name_ch_new
##                    print "full_name",j.get("full_name"),default_value[0]
                    set_web_data_para = self.set_web_data_para_KeyWord(sig_name_ch_new,default_value[0])
                    if set_web_data_para== False:
                        print sig_name_ch_new,default_value[0]
                        Faild_data_para.append({"sig_name_ch_new":sig_name_ch_new,"default_value":default_value[0]})
        print Faild_data_para
        if len(Faild_data_para)>0:#恢复默认值失败的再试一次
            for i in Faild_data_para:
                set_web_data_para = self.set_web_data_para_KeyWord(i.get("sig_name_ch_new"),i.get("default_value"))
                if set_web_data_para== False:
                    print u'恢复默认值失败！',i.get("sig_name_ch_new"),i.get("default_value")
                    return False
            return True
        return True
                        
    #功能：设置Web上所有可视的告警干接点为默认值
    def set_All_AlmRelay_default(self):
        Faild_data_para=[]
        get_Business_obj_list=self.get_request_data('business_object','val_get',[{"service":"2"}])
##        print get_Business_obj_list
        for i in get_Business_obj_list:
            bo_id=i.get("bo_id")
            print bo_id
            get_Business_obj_paraset=self.get_request_data('paraset','val_get',[{"bo_id":bo_id}])
            for j in get_Business_obj_paraset:              
                if j.get("alm_relay") != None:
                    if j.get("full_name")[-1] == ']':
                        StrIndex=j.get("full_name").index('[')
##                        print j.get("full_name").index('[')
                        sig_name_ch_new = j.get("full_name")[:StrIndex]+'_'+j.get("full_name")[StrIndex+1]
                        sig_name_ch_no_Index = j.get("full_name")[:StrIndex]
##                        print 'sig_name_ch_new',sig_name_ch_new
                    else:
                        sig_name_ch_new=j.get("full_name")
                        sig_name_ch_no_Index=j.get("full_name")
##                    default_value=self.get_para_scope(sig_name_ch_new)
##                    print 'sig_name_ch_new',sig_name_ch_new
                    alm_delay=self.get_SID_from_data_dict(sig_name_ch_no_Index)[34] #获取告警干接点默认值
##                    print "full_name",j.get("full_name"),j.get("alm_relay"),alm_delay
                    sig_name_ch_new=sig_name_ch_new+u'干接点'
                    set_web_data_para = self.set_web_data_para_KeyWord(sig_name_ch_new,alm_delay)   #设置告警干接点默认值

                    if set_web_data_para== False:
                        print sig_name_ch_new,alm_delay
                        Faild_data_para.append({"sig_name_ch_new":sig_name_ch_new,"default_value":alm_delay})
        print Faild_data_para
        if len(Faild_data_para)>0:#恢复默认值失败的再试一次
            for i in Faild_data_para:
                set_web_data_para = self.set_web_data_para_KeyWord(i.get("sig_name_ch_new"),i.get("default_value"))
                if set_web_data_para== False:
                    print u'恢复告警干接点默认值失败！',i.get("sig_name_ch_new"),i.get("default_value")
                    return False
            return True
        return True
    
    #已知配置通道名称，找到配置通道类型
    def get_channel_type_from_channel_name(self,channel_name='MIB_X1_DO1'):
        DIList = self.get_config_DI_infos()
        AIList = self.get_config_AI_infos()
        AIDIList = self.get_config_AIDI_infos()
        DOList = self.get_config_DO_infos()
        ChannelList=DIList+AIList+AIDIList+DOList
        for i in DIList:
            if i.get('Channel Name') ==channel_name:
                channel_type='DI'
                return channel_type
        for i in AIDIList:
            if i.get('Channel Name') ==channel_name and i.get('Channel Type')!='2':
                channel_type='AI'
                return channel_type
        for i in AIDIList:
            if i.get('Channel Name') ==channel_name and i.get('Channel Type')=='2':
                channel_type='AI/DI'
                return channel_type
        for i in DOList:
            if i.get('Channel Name') ==channel_name:
                channel_type='DO'
                return channel_type

    #获取通道列表
    def get_config_channel_List(self,channel_type='DO'):
        channel_List=[]
        if channel_type=='DI':
            DIList = self.get_config_DI_infos()
            for i in DIList:
                channel_List.append(i.get('Channel Name'))
            return channel_List

        elif channel_type=='AI':
            DIList = self.get_config_AI_infos()
            for i in DIList:
                if i.get('Channel Type')!='2':
                    channel_List.append(i.get('Channel Name'))
            return channel_List
        elif channel_type=='AI/DI':
            DIList = self.get_config_AIDI_infos()
            for i in DIList:
                if i.get('Channel Type') =='2':
                    channel_List.append(i.get('Channel Name'))
            return channel_List
        elif channel_type=='DO':
            DIList = self.get_config_DO_infos()
            for i in DIList:
                if i.get('class_type') =='2':
                    channel_List.append(i.get('Channel Name'))  #只保留Web上有的
            return channel_List      
        else:
            print u'没有该类型通道',channel_type
            return False


    #获取通道配置
    def get_config_channel_KeyWord(self,channel_name='CIB_X5_IL2'):
        channel_type=self.get_channel_type_from_channel_name(channel_name)
        if channel_type=='DI':
            return [channel_type]+self.get_config_channel_DI(channel_name)
        elif channel_type=='AI':
            return [channel_type]+self.get_config_channel_AI(channel_name)
        elif channel_type=='AI/DI':
            return [channel_type]+self.get_config_channel_AIDI(channel_name)
        elif channel_type=='DO':
            return [channel_type]+self.get_config_channel_DO(channel_name)        
        else:
            print u'没有该类型通道',channel_type
            return False

    #设置通道配置
    def set_config_channel_KeyWord(self,dataList):
        channel_type=self.get_channel_type_from_channel_name(dataList[0])
        print u'设置通道类型：',channel_type,u'//设置通道名称：',dataList[0]
        if channel_type=='DI' and len(dataList)==4:
            channel_name=dataList[0]
            dev_name_ch=dataList[1]
            sig_name_ch=dataList[2]
            alm_level=dataList[3]
            return self.config_channel_DI(channel_name, dev_name_ch, sig_name_ch , alm_level)
        elif channel_type=='AI' and len(dataList)==5:
            channel_name=dataList[0]
            offset=dataList[1]
            slope=dataList[2]
            dev_name_ch=dataList[3]
            sig_name_ch=dataList[4]
            return self.config_channel_AI(channel_name, offset, slope, dev_name_ch, sig_name_ch)
        elif channel_type=='AI/DI' and len(dataList)==4:
            channel_name=dataList[0]
            dev_name_ch=dataList[1]
            sig_name_ch=dataList[2]
            alm_level=dataList[3]         
            return self.config_channel_AIDI(channel_name, dev_name_ch, sig_name_ch, alm_level)
        elif channel_type=='DO' and len(dataList)==4:
            channel_name=dataList[0]
            dev_name_ch=dataList[1]
            sig_name_ch=dataList[2]
            alm_level=dataList[3]         
            return self.config_channel_DO(channel_name, dev_name_ch, sig_name_ch,alm_level)        
        else:
            print u'没有该类型通道',channel_type
            return False
    def test_get_para_set_scope(self):     
        if g_var.variables.get('data_dict') == None:
            g_var.variables.update({'data_dict':self.openCsvAsList( self.recordPath + '\\data_dictionary.csv' )})
        errList=[]
        weblist=[]
        userlist=[]
        print len(g_var.variables.get('data_dict'))
        for s in g_var.variables.get('data_dict'):
            lst = s.split(',')
            if len(lst) < 20: #个别约束关系的描述中,有换行符
                continue
            if lst[10]=='parameter' and lst[13]!=u'测试终止SOC阈值':
                print u'测试：',lst[13]
                try:
                    weblist = self.get_web_para_set_scope(lst[13])
                    userlist= self.get_para_set_scope(lst[13])                    
                except:
                    print u'该参数上下限异常：',lst[13]
                    errList.append(lst[13])
                print weblist,userlist
                if weblist[0]!=userlist[0] or weblist[1]!=userlist[1]  :
                    return False
        for i in errList:
            print i
##                value=self.get_para_set_scope(lst[13])
##                print value

    def get_web_data_para11(self, sig_name_ch=u'', dest=u'值'):
##        dev_name_ch, sig_name_ch_new, sig_index=self.transform_sig_name_ch(sig_name_ch)
##        if g_var.variables.get('data_dict_business_object') == None:
##            g_var.variables.update({'data_dict_business_object':self.openCsvAsList( self.recordPath + '\\data_dictionary_business_object.csv' )})
##        for s in g_var.variables.get('data_dict_business_object'):
##            lst = s.split(',')
##            if len(lst) < 6: #个别约束关系的描述中,有换行符
##                continue
##            if lst[6]==u'能源系统':
##                print u'测试：',lst[0]
##                break
        valst=self.get_request_data('devinfo','val_get',[{"bo_id":"0"}])
        print valst
        for i in valst:
            print i.get(u'full_name')
            print i.get(u'value')

    def read_XML(self):
        tree = ET.parse(self.recordPath + '\\config.xml')
        root = tree.getroot()

##        for neighbor in root.iter():    #该方法能找到指定配置参数的值
##            if neighbor.get('name') == 'SNMP Notification Mode':
##                print neighbor.text

        for cfgobject in root.findall('cfgobject'):
            for row in cfgobject.findall('row'):
                for para in row.findall('para'):
                    if para.get('name') == 'SNMP User Name':
                        print para.text         #text是用来获取元素的值
                        print para.get('name')  #get是用来获取元素属性
                        print cfgobject.get('id')
                        print cfgobject.get('name')

    #判断电压、电流数据在多少次内稳定（在1V或者1A内）
    def jude_real_data_sta(self, var_name, second):
        real_data_sta_lst = []
        for sec in range( int(second) ):
            val_curr = self.get_web_data_real_KeyWord( var_name )
            print u'当前值信息:',var_name,val_curr
            #print self.fileName, sys._getframe().f_lineno, 'Info:',type(val_curr), val_curr 
            real_data_sta_lst.append(float(val_curr))
            time.sleep(2)
        val_min = min(real_data_sta_lst)
        val_max = max(real_data_sta_lst)

        return (val_max-val_min) <= 1              

            
if __name__=="__main__":
    demo = WebKeyword()
##    demo.read_XML()
    #连接到服务器
    demo.init_dest_ip( u'***********' )
    demo.login_web( )
    print u'----------'
##    print type(demo.calc_params_value(u'交流停电','2+11'))
    
    print demo.get_para_set_scope(u'浮充电压')
    demo.calc_params_value(u'浮充电压',-15)
##    print demo.CSU_is_restarting()
##    print demo.set_web_data_para_KeyWord(u'市电配置_1','0')
##    print demo.get_web_data_para_KeyWord11(u'市电配置_1')
##    for i in demo.get_web_alarm_special(u'电池'):
##        print i
##    print demo.set_config_channel_KeyWord('AI/DI',['UIB_X3_DI3',u'市电_2',u'市电频率高',u'断开'])
##    print demo.calc_SID_with_devName_sigName(u'系统交流输入_1',u'交流缺相_3')
##    print demo.config_channel_AIDI('UIB_X3_DI3',u'市电_1',u'市电频率高',u'断开')
##    get = demo.get_config_channel_KeyWord('UIB_X3_DI3')
##    print get
##    print demo.set_config_channel_KeyWord(['UIB_X3_DI3',u'市电_1',u'市电频率高',u'闭合'])
##    print demo.get_config_channel_DI('UIB_X3_DI1')
##    print demo.get_web_alarm_special(u'电池回路断-3')
##    print demo.config_channel_DO('UIB_X2_DO2',u'CSU',u'输入干接点状态_2',u'常开')
##    for i in demo.get_config_channel_DO('UIB_X2_DO2'):
##        print i
##    print demo.get_config_channel_DO(u'UIB_X2_DO2')
##    print demo.get_config_channel_AI(u'CIB_X9_IB1')
##    print demo.get_config_channel_AIDI(u'CIB_X11_DCFL')    
##    print demo.get_para_scope(u'站点名称')
##    print demo.get_web_data_para_KeyWord(u'电池启用日期-1')
##    print demo.get_web_data_para_KeyWord11(u'电池启用日期-1')
##    print demo.get_web_data_para_KeyWord(u'电池启用日期-1')
##    print demo.set_web_data_para_KeyWord(u'交流电压高','1+1')
##    print demo.get_web_data_para_KeyWord(u'交流电压高')
##    print demo.set_web_data_para_KeyWord(u'电池检测异常','3')
##    print demo.get_web_data_para_KeyWord(u'电池检测异常')    
##    print demo.set_web_data_para_KeyWord(u'交流电压高阈值','283')
##    print demo.set_web_data_para_KeyWord(u'市电配置_1','1')
##    print demo.get_smr_addr_working()
##    print demo.set_web_data_para_KeyWord(u'市电配置_2','0')
##    print demo.get_web_data_para_KeyWord11(u'直流备用输入配置')
##    print demo.set_web_data_para_KeyWord(u'直流备用输入配置','0')
##    print demo.get_web_data_real_KeyWord(u'电池电压-1')
##    print demo.get_web_data_real_KeyWord(u'电池管理状态')
##    print demo.get_web_data_real_KeyWord(u'下次均充时间')
##    print demo.set_web_data_para_KeyWord(u'系统过载告警阈值','10')
##    print demo.get_web_data_para_KeyWord(u'系统过载告警阈值')
##    print demo.get_para_scope(u'站点名称')
##    print demo.test_get_para_set_scope()
##    print demo.get_web_para_set_scope(u'浮充电压')
##    print demo.get_request_data('paraset','val_get',[{"bo_id":"4"}])
##    print demo.get_request_data('business_object','val_get',[{"service":"2"}])    
##    print demo.get_web_para_set_scope(u'市电状况')
##    print demo.get_para_set_scope(u'整流器地址')
##    print demo.get_web_para_set_scope(u'节能带载率上限')
##    print demo.get_web_para_set_scope(u'节能带载率下限')
##    print demo.get_web_para_set_scope(u'电流缓启动使能')
##    print demo.get_web_para_set_scope(u'电流缓启动时间')
##    print demo.get_web_para_set_scope(u'电池启用日期')
##    print demo.get_para_count(u'输入干接点状态')
    
##    print demo.get_para_scope(u'直流备用输入配置')
##    print demo.get_para_scope(u'电池充电电流系数')
##    print demo.get_para_set_scope(u'电池充电电流系数')
##    print u'----------'
##    print demo.get_para_set_scope(u'均充电压')
##    print u'----------'
##    print demo.get_para_set_scope(u'环境温度高阈值')


##    demo.get_para_set_scope()
##    print demo.get_Ext_para_scope(u'浮充电压')
##    print demo.get_Basic_para_scope(u'PU最大数量')
##    now_time = demo.get_sys_time()
##    print 'get_sys_time', now_time
##    new_time = demo.get_sys_time_dec( now_time, 1000 )
##    print 'new_time', new_time
##    print 'set_sys_time',demo.set_sys_time( new_time )
##    print '-end time-', demo.get_sys_time()
    
##    dev_name = u'电池组'
##    para_1 = u'浮充电压' #default=53.5
##    para_2 = u'均充电压' #default=56.4
##    print u'dbg0000', demo.set_web_data_para( dev_name, para_1, dest={u'值':u'50'} ) 
##    print u'dbg0001', demo.set_web_data_para( dev_name, para_2, dest={u'值':u'52'} )
##    print u'dbg0002', demo.get_web_data_para( dev_name, para_1 ), demo.get_web_data_para( dev_name, para_2 )
##    print u'dbg0003', demo.set_para_default( u'电池组', [u'浮充电压'] )
##    print u'dbg0004', demo.get_web_data_para( dev_name, para_1 ), demo.get_web_data_para( dev_name, para_2 )
##
##    print u'dbg0000', demo.set_web_data_para( dev_name, para_2, dest={u'值':u'57.5'} ) 
##    print u'dbg0001', demo.set_web_data_para( dev_name, para_1, dest={u'值':u'57'} )
##    print u'dbg0002', demo.get_web_data_para( dev_name, para_1 ), demo.get_web_data_para( dev_name, para_2 )
##    print u'dbg0003', demo.set_para_default( u'电池组', [u'浮充电压', u'均充电压'] )
##    print u'dbg0004', demo.get_web_data_para( dev_name, para_1 ), demo.get_web_data_para( dev_name, para_2 )
##
##    print 'dbg set_web_data_para:', demo.set_web_data_para( u'整流器组',u'整流器输出高停机电压', dest={u'值':'62'} )
##    print 'dbg set_web_data_para:', demo.set_web_data_para( u'电池组',u'均充电压', dest={u'值':'56.5'} )
##    print 'dbg set_web_data_para:', demo.set_web_data_para( u'电池组',u'浮充电压', dest={u'值':'53.6'} )
##    print 'dbg set_para_default_3_dev_para:', demo.set_para_default_3_dev_para( u'整流器组',u'整流器输出高停机电压',u'电池组',u'均充电压',u'电池组',u'浮充电压')

##    print 'dbg com1 config', demo.get_config_COM_attr( 'COM1')
##    print 'dbg com2 config', demo.get_config_COM_attr( 'COM2')
##    print 'dbg set snmp para', demo.set_SNMP_para( 'PowerPub!', 'PowerPri!', 'Trap V1', 'user' )
##    print 'dbg get snmp para', demo.get_SNMP_para( )
##    print 'deb add snmp v3 user inst', demo.add_v3_user( 'test1','SHA', 'test2', 'AES', 'test3')
##    print 'dbg get snmp v3 user info', demo.get_v3_user_info( )
##    print 'dbg del snmp v3 user', demo.del_v3_user( 'add1' )
##    print 'dbg get_manager_info:', demo.get_manager_info( )
##    print 'dbg add_manager:', demo.add_manager( 'name','10.9.86.199','1620')
##    print 'dbg del_manager:', demo.del_manager( 'name')
##    print 'dbg get_config_channel_DI:', demo.get_config_channel_DI( 'UIB_X3_DI2')
    
##    print 'tttt1',demo.get_web_data_para( 'CSU', u'CPU利用率', dest=u'存储周期' )
##    print 'tttt2',demo.get_web_data_para( 'CSU', u'CPU利用率', dest=u'绝对阈值' )
##    
##    print u'dbg1100', demo.get_disp_attr( 'CSU', u'模拟量', u'CPU利用率' )
##    print u'dbg1101', demo.get_disp_attr( 'CSU', u'模拟量', u'内存利用率' )
    #--------
##    file_name = demo.download_para( )
##    print u'download_para：', file_name
##    print u'upload_para：', demo.upload_para( file_name )
##    print u'CSU_is_restarting：', demo.CSU_is_restarting( )
##    for dest in [u'市电有电记录', u'市电停电记录', u'电池充电记录', u'电池测试记录', u'电池放电记录', u'电池均充记录',  u'历史数据', u'历史事件', u'历史告警', u'所有']:
##        print u'1108', demo.del_his_rec(  dest)
##    print u'resume_para_default:', demo.resume_para_default( )
##    obj_id = u'0' #恢复备份参数
##    print u'resume_para_all', demo.resume_para_all( )
##    #--------
##    #下面两行的效果是一样的,其中第一行的用法在RF环境中,sig_index可以不输入任何值
##    print 'DBG00',demo.set_web_data_para(u'电池组',u'电池组容量[1]','0',{u'值':100})
##    print 'DBG01',demo.set_web_data_para(u'电池组',u'电池组容量',   '1',{u'值':81})
##    #--------
##    #dbg 设备属性
##    #运行条件:无
##    #关联的其他调试内容: dbg010
##    lst_dev_name = []
##    for item in demo.get_lst_dev_attr( ):
##        dev_type = item.get(u'device type')
##        dev_name = item.get(u'device name')
##        #print u'dbg000 设备属性: device type=', dev_type, u'device name=',dev_name, u'sid=', item.get(u'sid')
##        if dev_name in lst_dev_name:
##            print u'dbg001 Err:有重复的设备名称.', dev_name, 'x'*10
##        else:
##            lst_dev_name.append( dev_name )
##    #--------
##    print 't00', demo.get_web_data_his_alm_one( u'整流器_35', u'整流器通讯中断' ) #整流器_35:整流器通讯中断
    
#历史事件:指定起始时间,且不变化的情况,多次运行(类似在web上多次刷新),会发生总条数变少的现象
#系统升级:升级包上传成功后不会立即重启,由其它维护进程,定期检测到有新版本了才升级,最大多久才重启,要向开发人员求证了
#设置整流器 告警参数/存储属性参数 的时候,在没有整理器在线的情况下(有整流器的情况尚未验证), CSU反应速度很慢, 有时候会time_out
