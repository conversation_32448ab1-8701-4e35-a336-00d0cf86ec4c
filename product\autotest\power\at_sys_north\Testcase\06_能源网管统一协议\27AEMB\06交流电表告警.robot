*** Settings ***
Suite Setup       主动告警测试前置条件    ${CSU_role}
Suite Teardown    设置web设备参数量为默认值    CSU主动告警使能
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_交流电表通讯断告警
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    控制子工具运行停止    DMU_aemb    关闭
    wait until keyword succeeds    5m    1    判断告警存在    交流电表通讯断告警-1
    sleep    15
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    交流电表通讯断告警-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    控制子工具运行停止    DMU_aemb    启动
    wait until keyword succeeds    5m    1    判断告警不存在    交流电表通讯断告警
    sleep    15
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    交流电表通讯断告警-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    sleep    1m
    [Teardown]    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
