*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
直流输出电压范围（输入上限输出满载）测试
    [Documentation]    调整输出电压范围按照正负1%。
    [Setup]    测试用例前置条件
    电池管理初始化
    仅有市电条件上电
    连接CSU
    同时设置三相电压频率    295    50
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-1    否    #为保证整流器数据获取正常
    设置web参数量    温度补偿模式    禁止    #关闭温度补偿
    设置web参数量    均充电压    58
    设置web参数量    浮充电压    58
    sleep    2
    Wait Until Keyword Succeeds    5m    2    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    10m    2    信号量数据值大于    整流器输出电压-1    57.8
    Wait Until Keyword Succeeds    2m    2    设置web参数量    电池组容量_1    0
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    整流器设定限流点比率    900
    ${工作整流器数量}    获取web实时数据    工作整流器数量
    ${设置负载电流值}    evaluate    ${工作整流器数量}*50+3    #负载不准，补偿3A
    缓慢设置负载电压电流    58    ${设置负载电流值}    5
    打开负载输出
    sleep    5
    Wait Until Keyword Succeeds    10m    2    信号量数据值大于    整流器输出电压-1    57.8
    sleep    10
    ${整流器1输入电压}    获取web实时数据    整流器输入电压-1
    ${整流器总输出电流}    获取web实时数据    整流器总输出电流
    ${整流器1输出电压}    获取web实时数据    整流器输出电压-1
    should be true    57.42<=${整流器1输出电压}<=58.58
    should be true    ${工作整流器数量}*50-4<=${整流器总输出电流}<=${工作整流器数量}*50+3
    设置web参数量    浮充电压    53.5
    设置web控制量    启动浮充
    sleep    5
    缓慢设置负载电压电流    53.5    ${设置负载电流值}    5
    sleep    5
    Wait Until Keyword Succeeds    10m    2    信号量数据值大于    整流器输出电压-1    53.2
    Wait Until Keyword Succeeds    10m    2    信号量数据值小于    整流器输出电压-1    53.7
    sleep    10
    ${整流器1输入电压}    获取web实时数据    整流器输入电压-1
    ${整流器总输出电流}    获取web实时数据    整流器总输出电流
    ${整流器1输出电压}    获取web实时数据    整流器输出电压-1
    should be true    52.965<=${整流器1输出电压}<=54.035
    should be true    ${工作整流器数量}*50-4<=${整流器总输出电流}<=${工作整流器数量}*50+3
    设置web参数量    浮充电压    42
    sleep    5
    缓慢设置负载电压电流    42    ${设置负载电流值}    5
    sleep    5
    Wait Until Keyword Succeeds    10m    2    信号量数据值大于    整流器输出电压-1    41.8
    Wait Until Keyword Succeeds    10m    2    信号量数据值小于    整流器输出电压-1    42.2
    sleep    10
    ${整流器1输入电压}    获取web实时数据    整流器输入电压-1
    ${整流器总输出电流}    获取web实时数据    整流器总输出电流
    ${整流器1输出电压}    获取web实时数据    整流器输出电压-1
    should be true    41.58<=${整流器1输出电压}<=42.42
    should be true    ${工作整流器数量}*50-5<=${整流器总输出电流}<=${工作整流器数量}*50+4
    [Teardown]    Run keywords    设置web参数量    电池组容量_1    100
    ...    AND    设置web参数量    浮充电压    53.5
    ...    AND    关闭负载输出

直流输出电压范围（输入上限5%载）测试
    [Documentation]    调整输出电压范围按照正负1%。
    [Setup]    测试用例前置条件
    电池管理初始化
    仅有市电条件上电
    连接CSU
    同时设置三相电压频率    295    50
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-1    否    #为保证整流器数据获取正常
    设置web参数量    温度补偿模式    禁止
    设置web参数量    均充电压    58
    设置web参数量    浮充电压    58
    sleep    2
    Wait Until Keyword Succeeds    5m    2    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    10m    2    信号量数据值大于    整流器输出电压-1    57.8
    设置负载电压电流    58    5
    打开负载输出
    sleep    5
    Wait Until Keyword Succeeds    10m    2    信号量数据值大于    整流器输出电压-1    57.8
    sleep    10
    ${整流器1输入电压}    获取web实时数据    整流器输入电压-1
    ${整流器总输出电流}    获取web实时数据    整流器总输出电流
    ${整流器1输出电压}    获取web实时数据    整流器输出电压-1
    should be true    57.42<=${整流器1输出电压}<=58.58
    should be true    4<=${整流器总输出电流}<=6
    设置web参数量    浮充电压    53.5
    设置web控制量    启动浮充
    设置负载电压电流    53.5    5
    sleep    5
    Wait Until Keyword Succeeds    10m    2    信号量数据值大于    整流器输出电压-1    53.3
    Wait Until Keyword Succeeds    10m    2    信号量数据值小于    整流器输出电压-1    53.7
    sleep    10
    ${整流器1输入电压}    获取web实时数据    整流器输入电压-1
    ${整流器总输出电流}    获取web实时数据    整流器总输出电流
    ${整流器1输出电压}    获取web实时数据    整流器输出电压-1
    should be true    52.965<=${整流器1输出电压}<=54.035
    should be true    4<=${整流器总输出电流}<=6
    设置web参数量    浮充电压    42
    sleep    5
    设置负载电压电流    42    5
    sleep    5
    Wait Until Keyword Succeeds    10m    2    信号量数据值大于    整流器输出电压-1    41.8
    Wait Until Keyword Succeeds    10m    2    信号量数据值小于    整流器输出电压-1    42.2
    sleep    10
    ${整流器1输入电压}    获取web实时数据    整流器输入电压-1
    ${整流器总输出电流}    获取web实时数据    整流器总输出电流
    ${整流器1输出电压}    获取web实时数据    整流器输出电压-1
    should be true    41.58<=${整流器1输出电压}<=42.42
    should be true    4<=${整流器总输出电流}<=6
    [Teardown]    Run keywords    设置web参数量    浮充电压    53.5
    ...    AND    关闭负载输出

直流输出电压范围（输入下限输出满载）测试
    [Documentation]    调整输出电压范围按照正负1%。
    [Setup]    测试用例前置条件
    电池管理初始化
    仅有市电条件上电
    连接CSU
    同时设置三相电压频率    180    50
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-1    否    #为保证整流器数据获取正常
    设置web参数量    温度补偿模式    禁止    #关闭温度补偿
    设置web参数量    均充电压    58
    设置web参数量    浮充电压    58
    sleep    2
    Wait Until Keyword Succeeds    5m    2    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    10m    2    信号量数据值大于    整流器输出电压-1    57.8
    Wait Until Keyword Succeeds    2m    2    设置web参数量    电池组容量_1    0
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    整流器设定限流点比率    900
    ${工作整流器数量}    获取web实时数据    工作整流器数量
    ${设置负载电流值}    evaluate    ${工作整流器数量}*50+3    #负载不准，补偿3A
    缓慢设置负载电压电流    58    ${设置负载电流值}    5
    打开负载输出
    sleep    5
    Wait Until Keyword Succeeds    10m    2    信号量数据值大于    整流器输出电压-1    57.8
    sleep    10
    ${整流器1输入电压}    获取web实时数据    整流器输入电压-1
    ${整流器总输出电流}    获取web实时数据    整流器总输出电流
    ${整流器1输出电压}    获取web实时数据    整流器输出电压-1
    should be true    57.42<=${整流器1输出电压}<=58.58
    should be true    ${工作整流器数量}*50-4<=${整流器总输出电流}<=${工作整流器数量}*50+3
    设置web参数量    浮充电压    53.5
    设置web控制量    启动浮充
    sleep    5
    设置负载电压电流    53.5    ${设置负载电流值}
    sleep    5
    Wait Until Keyword Succeeds    10m    2    信号量数据值大于    整流器输出电压-1    53.2
    Wait Until Keyword Succeeds    10m    2    信号量数据值小于    整流器输出电压-1    53.7
    sleep    10
    ${整流器1输入电压}    获取web实时数据    整流器输入电压-1
    ${整流器总输出电流}    获取web实时数据    整流器总输出电流
    ${整流器1输出电压}    获取web实时数据    整流器输出电压-1
    should be true    52.965<=${整流器1输出电压}<=54.035
    should be true    ${工作整流器数量}*50-4<=${整流器总输出电流}<=${工作整流器数量}*50+3
    设置web参数量    浮充电压    42
    sleep    5
    设置负载电压电流    42    ${设置负载电流值}
    sleep    5
    Wait Until Keyword Succeeds    10m    2    信号量数据值大于    整流器输出电压-1    41.8
    Wait Until Keyword Succeeds    10m    2    信号量数据值小于    整流器输出电压-1    42.2
    sleep    10
    ${整流器1输入电压}    获取web实时数据    整流器输入电压-1
    ${整流器总输出电流}    获取web实时数据    整流器总输出电流
    ${整流器1输出电压}    获取web实时数据    整流器输出电压-1
    should be true    41.58<=${整流器1输出电压}<=42.42
    should be true    ${工作整流器数量}*50-5<=${整流器总输出电流}<=${工作整流器数量}*50+4
    [Teardown]    Run keywords    设置web参数量    电池组容量_1    100
    ...    AND    设置web参数量    浮充电压    53.5
    ...    AND    关闭负载输出

直流输出电压范围（输入下限5%载）测试
    [Documentation]    调整输出电压范围按照正负1%。
    [Setup]    测试用例前置条件
    电池管理初始化
    仅有市电条件上电
    连接CSU
    同时设置三相电压频率    180    50
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-1    否    #为保证整流器数据获取正常
    设置web参数量    温度补偿模式    禁止    #关闭温度补偿
    设置web参数量    均充电压    58
    设置web参数量    浮充电压    58
    sleep    2
    Wait Until Keyword Succeeds    5m    2    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    10m    2    信号量数据值大于    整流器输出电压-1    57.8
    设置负载电压电流    58    5
    打开负载输出
    sleep    5
    Wait Until Keyword Succeeds    10m    2    信号量数据值大于    整流器输出电压-1    57.8
    sleep    10
    ${整流器1输入电压}    获取web实时数据    整流器输入电压-1
    ${整流器总输出电流}    获取web实时数据    整流器总输出电流
    ${整流器1输出电压}    获取web实时数据    整流器输出电压-1
    should be true    57.42<=${整流器1输出电压}<=58.58
    should be true    4<=${整流器总输出电流}<=6
    设置web参数量    浮充电压    53.5
    设置web控制量    启动浮充
    设置负载电压电流    53.5    5
    sleep    5
    Wait Until Keyword Succeeds    10m    2    信号量数据值大于    整流器输出电压-1    53.3
    Wait Until Keyword Succeeds    10m    2    信号量数据值小于    整流器输出电压-1    53.7
    sleep    10
    ${整流器1输入电压}    获取web实时数据    整流器输入电压-1
    ${整流器总输出电流}    获取web实时数据    整流器总输出电流
    ${整流器1输出电压}    获取web实时数据    整流器输出电压-1
    should be true    52.965<=${整流器1输出电压}<=54.035
    should be true    4<=${整流器总输出电流}<=6
    设置web参数量    浮充电压    42
    sleep    2
    设置负载电压电流    42    5
    sleep    5
    Wait Until Keyword Succeeds    10m    2    信号量数据值大于    整流器输出电压-1    41.8
    Wait Until Keyword Succeeds    10m    2    信号量数据值小于    整流器输出电压-1    42.2
    sleep    10
    ${整流器1输入电压}    获取web实时数据    整流器输入电压-1
    ${整流器总输出电流}    获取web实时数据    整流器总输出电流
    ${整流器1输出电压}    获取web实时数据    整流器输出电压-1
    should be true    41.58<=${整流器1输出电压}<=42.42
    should be true    4<=${整流器总输出电流}<=6
    [Teardown]    Run keywords    设置web参数量    浮充电压    53.5
    ...    AND    关闭负载输出

直流输出电压范围（输入额定输出满载）测试
    [Documentation]    调整输出电压范围按照正负1%。
    [Setup]    测试用例前置条件
    电池管理初始化
    仅有市电条件上电
    连接CSU
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-1    否    #为保证整流器数据获取正常
    设置web参数量    温度补偿模式    禁止    #关闭温度补偿
    设置web参数量    均充电压    58
    设置web参数量    浮充电压    58
    sleep    2
    Wait Until Keyword Succeeds    5m    2    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    10m    2    信号量数据值大于    整流器输出电压-1    57.8
    Wait Until Keyword Succeeds    2m    2    设置web参数量    电池组容量_1    0
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    整流器设定限流点比率    900
    ${工作整流器数量}    获取web实时数据    工作整流器数量
    ${设置负载电流值}    evaluate    ${工作整流器数量}*50+3    #负载不准，补偿3A
    缓慢设置负载电压电流    58    ${设置负载电流值}    5
    打开负载输出
    sleep    5
    Wait Until Keyword Succeeds    10m    2    信号量数据值大于    整流器输出电压-1    57.8
    sleep    10
    ${整流器1输入电压}    获取web实时数据    整流器输入电压-1
    ${整流器总输出电流}    获取web实时数据    整流器总输出电流
    ${整流器1输出电压}    获取web实时数据    整流器输出电压-1
    should be true    57.42<=${整流器1输出电压}<=58.58
    should be true    ${工作整流器数量}*50-4<=${整流器总输出电流}<=${工作整流器数量}*50+3
    设置web参数量    浮充电压    53.5
    设置web控制量    启动浮充
    sleep    5
    设置负载电压电流    53.5    ${设置负载电流值}
    sleep    5
    Wait Until Keyword Succeeds    10m    2    信号量数据值大于    整流器输出电压-1    53.2
    Wait Until Keyword Succeeds    10m    2    信号量数据值小于    整流器输出电压-1    53.7
    sleep    10
    ${整流器1输入电压}    获取web实时数据    整流器输入电压-1
    ${整流器总输出电流}    获取web实时数据    整流器总输出电流
    ${整流器1输出电压}    获取web实时数据    整流器输出电压-1
    should be true    52.965<=${整流器1输出电压}<=54.035
    should be true    ${工作整流器数量}*50-4<=${整流器总输出电流}<=${工作整流器数量}*50+3
    设置web参数量    浮充电压    42
    sleep    5
    设置负载电压电流    42    ${设置负载电流值}
    sleep    5
    Wait Until Keyword Succeeds    10m    2    信号量数据值大于    整流器输出电压-1    41.8
    Wait Until Keyword Succeeds    10m    2    信号量数据值小于    整流器输出电压-1    42.2
    sleep    10
    ${整流器1输入电压}    获取web实时数据    整流器输入电压-1
    ${整流器总输出电流}    获取web实时数据    整流器总输出电流
    ${整流器1输出电压}    获取web实时数据    整流器输出电压-1
    should be true    41.58<=${整流器1输出电压}<=42.42
    should be true    ${工作整流器数量}*50-5<=${整流器总输出电流}<=${工作整流器数量}*50+4
    [Teardown]    Run keywords    设置web参数量    电池组容量_1    100
    ...    AND    设置web参数量    浮充电压    53.5
    ...    AND    关闭负载输出

直流输出电压范围（输入额定5%载）测试
    [Documentation]    调整输出电压范围按照正负1%。
    [Setup]    测试用例前置条件
    电池管理初始化
    仅有市电条件上电
    连接CSU
    同时设置三相电压频率    220    50
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-1    否    #为保证整流器数据获取正常
    设置web参数量    温度补偿模式    禁止    #关闭温度补偿
    设置web参数量    均充电压    58
    设置web参数量    浮充电压    58
    sleep    2
    Wait Until Keyword Succeeds    5m    2    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    10m    2    信号量数据值大于    整流器输出电压-1    57.8
    设置负载电压电流    58    5
    打开负载输出
    sleep    5
    Wait Until Keyword Succeeds    10m    2    信号量数据值大于    整流器输出电压-1    57.8
    sleep    10
    ${整流器1输入电压}    获取web实时数据    整流器输入电压-1
    ${整流器总输出电流}    获取web实时数据    整流器总输出电流
    ${整流器1输出电压}    获取web实时数据    整流器输出电压-1
    should be true    57.42<=${整流器1输出电压}<=58.58
    should be true    4<=${整流器总输出电流}<=6
    设置web参数量    浮充电压    53.5
    设置web控制量    启动浮充
    设置负载电压电流    53.5    5
    sleep    5
    Wait Until Keyword Succeeds    10m    2    信号量数据值大于    整流器输出电压-1    53.3
    Wait Until Keyword Succeeds    10m    2    信号量数据值小于    整流器输出电压-1    53.7
    sleep    10
    ${整流器1输入电压}    获取web实时数据    整流器输入电压-1
    ${整流器总输出电流}    获取web实时数据    整流器总输出电流
    ${整流器1输出电压}    获取web实时数据    整流器输出电压-1
    should be true    52.965<=${整流器1输出电压}<=54.035
    should be true    4<=${整流器总输出电流}<=6
    设置web参数量    浮充电压    42
    sleep    5
    设置负载电压电流    42    5
    sleep    5
    Wait Until Keyword Succeeds    10m    2    信号量数据值大于    整流器输出电压-1    41.8
    Wait Until Keyword Succeeds    10m    2    信号量数据值小于    整流器输出电压-1    42.2
    sleep    10
    ${整流器1输入电压}    获取web实时数据    整流器输入电压-1
    ${整流器总输出电流}    获取web实时数据    整流器总输出电流
    ${整流器1输出电压}    获取web实时数据    整流器输出电压-1
    should be true    41.58<=${整流器1输出电压}<=42.42
    should be true    4<=${整流器总输出电流}<=6
    [Teardown]    Run keywords    设置web参数量    浮充电压    53.5
    ...    AND    关闭负载输出
