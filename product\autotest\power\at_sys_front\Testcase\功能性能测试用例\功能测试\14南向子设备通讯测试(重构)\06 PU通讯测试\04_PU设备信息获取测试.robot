*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
批量获取PU设备信息测试
    [Tags]    PMSA-NTest
    写入CSV文档    PU设备信息获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string64    INDEX_DEVICE_CH=太阳能模块
    ${排除列表}    create list
	@{信号名称列表1}   create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除设备版本信息}    ${排除列表}    1    ${模拟PU起始地址}
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     name     ${信号名称}
		Append To List       ${信号名称列表1}    ${dict}
    END
	Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    pu    ${信号名称列表1}    版本    V99.23    PU设备信息获取测试
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    pu    ${信号名称列表1}    版本    V10.10    PU设备信息获取测试
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    pu    ${信号名称列表1}    版本    V1.81    PU设备信息获取测试


    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string32    INDEX_DEVICE_CH=太阳能模块
    ${排除列表}    create list
	@{信号名称列表2}    create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除设备名称信息}    ${排除列表}    2    ${模拟PU起始地址}
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     name     ${信号名称}
		Append To List       ${信号名称列表2}    ${dict}
    END
	Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    pu    ${信号名称列表2}    厂家信息    VZXDU48 FB100B3    PU设备信息获取测试
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    pu    ${信号名称列表2}    厂家信息    ZTE-smartli    PU设备信息获取测试
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    pu    ${信号名称列表2}    厂家信息    VZXDU48 FB100C2    PU设备信息获取测试


    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=float    INDEX_DEVICE_CH=太阳能模块
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    2    ${模拟PU起始地址}
    ${缺省值列表}   获取缺省值列表  ${列表1}    1
	Run Keyword And Continue On Failure    南向子设备数值型厂家信息列表封装判断结果    pu    ${缺省值列表}    版本    PU设备信息获取测试
    ${缺省值列表}   获取缺省值列表  ${列表1}    2
    Run Keyword And Continue On Failure    南向子设备数值型厂家信息列表封装判断结果    pu    ${缺省值列表}    版本    PU设备信息获取测试
    ${缺省值列表}   获取缺省值列表  ${列表1}    0
    Run Keyword And Continue On Failure    南向子设备数值型厂家信息列表封装判断结果    pu    ${缺省值列表}    版本    PU设备信息获取测试


    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=int    INDEX_DEVICE_CH=太阳能模块
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    2    ${模拟PU起始地址}
    ${缺省值列表}   获取缺省值列表  ${列表1}    1
	Run Keyword And Continue On Failure    南向子设备数值型厂家信息列表封装判断结果    pu    ${缺省值列表}    资产管理信息    PU设备信息获取测试
    ${缺省值列表}   获取缺省值列表  ${列表1}    2
    Run Keyword And Continue On Failure    南向子设备数值型厂家信息列表封装判断结果    pu    ${缺省值列表}    资产管理信息    PU设备信息获取测试
    ${缺省值列表}   获取缺省值列表  ${列表1}    0
    Run Keyword And Continue On Failure    南向子设备数值型厂家信息列表封装判断结果    pu    ${缺省值列表}    资产管理信息    PU设备信息获取测试

    
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=date    INDEX_DEVICE_CH=太阳能模块
    ${排除列表}    create list
	@{信号名称列表3}   create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除设备日期信息}    ${排除列表}    2    ${模拟整流器地址}
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     name     ${信号名称}
		Append To List       ${信号名称列表3}    ${dict}
    END
	Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    pu    ${信号名称列表3}    版本    2018-11-15    PU设备信息获取测试
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    pu    ${信号名称列表3}    版本    2021-08-23    PU设备信息获取测试
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    pu    ${信号名称列表3}    版本    2018-07-28    PU设备信息获取测试

PU条码获取测试
    [Tags]    PMSA-NTest
    连接CSU
    #子设备工具模拟
    设置子工具值    pu    all    资产管理信息    PU条码3    12522
    设置子工具值    pu    all    资产管理信息    PU条码4    50416
    设置子工具值    pu    all    资产管理信息    PU条码5g    178
    Wait Until Keyword Succeeds    10m   5    设备信息获取值    <<PU条码-1~0x10001080080001>>        210097205426
    Wait Until Keyword Succeeds    10m   5    设备信息获取值    <<PU条码-10~0x10001080080001>>        210097205426
    Wait Until Keyword Succeeds    10m   5    设备信息获取值    <<PU条码-20~0x10001080080001>>      210097205426
    Wait Until Keyword Succeeds    10m   5    设备信息获取值    <<PU条码-30~0x10001080080001>>        210097205426
    Wait Until Keyword Succeeds    10m   5    设备信息获取值    <<PU条码-48~0x10001080080001>>      210097205426
    # 测试台上下电操作    ${测试台编号}    OFF
    # sleep    5
    # 测试台上下电操作    ${测试台编号}    ON
    # 连接CSU
    # #子设备工具模拟
    # 设置子工具值    pu    all    资产管理信息    PU条码3    12522
    # 设置子工具值    pu    all    资产管理信息    PU条码4    50416
    # 设置子工具值    pu    all    资产管理信息    PU条码5g    178
    # Wait Until Keyword Succeeds    10m   5    设备信息获取值    <<PU条码-1~0x10001080080001>>        210097205426
    # Wait Until Keyword Succeeds    10m   5    设备信息获取值    <<PU条码-10~0x10001080080001>>        210097205426
    # Wait Until Keyword Succeeds    10m   5    设备信息获取值    <<PU条码-20~0x10001080080001>>      210097205426
    # Wait Until Keyword Succeeds    10m   5    设备信息获取值    <<PU条码-30~0x10001080080001>>        210097205426
    # Wait Until Keyword Succeeds    10m   5    设备信息获取值    <<PU条码-48~0x10001080080001>>      210097205426

PU生产日期获取测试
    [Tags]    PMSA-NTest
    连接CSU
    #子设备工具模拟
    设置子工具值    pu    all    资产管理信息    生产日期年5d6g    2020
    设置子工具值    pu    all    资产管理信息    生产日期月6d    11
    设置子工具值    pu    all    资产管理信息    生产日期日7g    19
    Wait Until Keyword Succeeds    10m   5    设备信息获取值    <<生产日期-1~0x10001080090001>>        2020-11-19
    Wait Until Keyword Succeeds    10m   5    设备信息获取值    <<生产日期-10~0x10001080090001>>        2020-11-19
    Wait Until Keyword Succeeds    10m   5    设备信息获取值    <<生产日期-20~0x10001080090001>>      2020-11-19
    Wait Until Keyword Succeeds    10m   5    设备信息获取值    <<生产日期-30~0x10001080090001>>        2020-11-19
    Wait Until Keyword Succeeds    10m   5    设备信息获取值    <<生产日期-48~0x10001080090001>>      2020-11-19
    测试台上下电操作    ${测试台编号}    OFF
    sleep    5
    测试台上下电操作    ${测试台编号}    ON
    连接CSU
    设置子工具值    pu    all    资产管理信息    生产日期年5d6g    2020
    设置子工具值    pu    all    资产管理信息    生产日期月6d    11
    设置子工具值    pu    all    资产管理信息    生产日期日7g    19
    Wait Until Keyword Succeeds    10m   5    设备信息获取值    <<生产日期-1~0x10001080090001>>        2020-11-19
    Wait Until Keyword Succeeds    10m   5    设备信息获取值    <<生产日期-10~0x10001080090001>>        2020-11-19
    Wait Until Keyword Succeeds    10m   5    设备信息获取值    <<生产日期-20~0x10001080090001>>      2020-11-19
    Wait Until Keyword Succeeds    10m   5    设备信息获取值    <<生产日期-30~0x10001080090001>>        2020-11-19
    Wait Until Keyword Succeeds    10m   5    设备信息获取值    <<生产日期-48~0x10001080090001>>      2020-11-19

