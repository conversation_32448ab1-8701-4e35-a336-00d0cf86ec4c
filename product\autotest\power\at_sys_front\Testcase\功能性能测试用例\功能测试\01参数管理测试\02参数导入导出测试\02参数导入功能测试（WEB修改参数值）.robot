*** Settings ***
Documentation     WEB上修改参数设置值，导入参数文件后，参数设置值恢复为参数文件中参数值。
Resource          ../../../../../测试用例关键字.robot
Suite Setup     测试用例前置条件
Suite Teardown  Run Keywords    测试用例后置条件
...             AND    重启CSU容器

*** Test Cases ***
显示属性参数导入功能测试
    [Tags]    PMSA-NTest
    连接CSU
    ${起始时间}    获取系统时间
    ${用户参数文件}    导出参数文件    ${datapassword}
    @{channel_config1}    获取显示属性配置    CPU利用率    模拟量
    @{channel_config2}    获取显示属性配置    <<禁止所有告警~0x1001040010001>>    控制量
    @{channel_config3}    获取显示属性配置    相电压UL1    模拟量
    @{channel_config4}    获取显示属性配置    站点名称    参数量    #站点名称需要继承
    @{channel_config5}    获取显示属性配置    交流防雷器状态    数字量
    @{channel_config6}    获取显示属性配置    市电输入状态    数字量
    @{channel_config7}    获取显示属性配置    整流器输出电压    模拟量
    @{channel_config8}    获取显示属性配置    电池组总电流    模拟量
    @{channel_config9}    获取显示属性配置    直流电压    模拟量
    #WEB修改显示属性
    ${channel_config}    显示属性配置    CPU利用率    模拟量    ON    ON    #初始值为OFF，不显示
    @{channel_config}    获取显示属性配置    CPU利用率    模拟量
    should be equal    ${channel_config}[0]    ON
    should be equal    ${channel_config}[1]    ON
    ${channel_config}    显示属性配置    <<禁止所有告警~0x1001040010001>>    控制量    OFF    OFF
    @{channel_config}    获取显示属性配置    <<禁止所有告警~0x1001040010001>>    控制量
    should be equal    ${channel_config}[0]    OFF
    should be equal    ${channel_config}[1]    OFF
    ${channel_config}    显示属性配置    相电压UL1    模拟量    OFF    OFF
    @{channel_config}    获取显示属性配置    相电压UL1    模拟量
    should be equal    ${channel_config}[0]    OFF
    should be equal    ${channel_config}[1]    OFF
    ${channel_config}    显示属性配置    站点名称    参数量    OFF    OFF
    @{channel_config}    获取显示属性配置    站点名称    参数量    #站点名称需要继承
    should be equal    ${channel_config}[0]    OFF
    should be equal    ${channel_config}[1]    OFF
    ${channel_config}    显示属性配置    交流防雷器状态    数字量    ON    ON
    @{channel_config}    获取显示属性配置    交流防雷器状态    数字量
    should be equal    ${channel_config}[0]    ON
    should be equal    ${channel_config}[1]    ON
    ${channel_config}    显示属性配置    市电输入状态    数字量    OFF    OFF
    @{channel_config}    获取显示属性配置    市电输入状态    数字量
    should be equal    ${channel_config}[0]    OFF
    should be equal    ${channel_config}[1]    OFF
    ${channel_config}    显示属性配置    整流器输出电压    模拟量    OFF    OFF
    @{channel_config}    获取显示属性配置    整流器输出电压    模拟量
    should be equal    ${channel_config}[0]    OFF
    should be equal    ${channel_config}[1]    OFF
    ${channel_config}    显示属性配置    电池组总电流    模拟量    OFF    OFF
    @{channel_config}    获取显示属性配置    电池组总电流    模拟量
    should be equal    ${channel_config}[0]    OFF
    should be equal    ${channel_config}[1]    OFF
    ${channel_config}    显示属性配置    直流电压    模拟量    OFF    OFF
    @{channel_config}    获取显示属性配置    直流电压    模拟量
    should be equal    ${channel_config}[0]    OFF
    should be equal    ${channel_config}[1]    OFF
    压缩文件夹    ${用户参数文件}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${用户参数文件}.zip    ${datapassword}
    sleep    4m
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    100
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    @{channel_config}    获取显示属性配置    CPU利用率    模拟量
    should be equal    ${channel_config}[0]    ${channel_config1}[0]
    should be equal    ${channel_config}[1]    ${channel_config1}[1]
    @{channel_config}    获取显示属性配置    <<禁止所有告警~0x1001040010001>>    控制量
    should be equal    ${channel_config}[0]    ${channel_config2}[0]
    should be equal    ${channel_config}[1]    ${channel_config2}[1]
    @{channel_config}    获取显示属性配置    站点名称    参数量
    should be equal    ${channel_config}[0]    ON
    should be equal    ${channel_config}[1]    ON
    @{channel_config}    获取显示属性配置    相电压UL1    模拟量
    should be equal    ${channel_config}[0]    ${channel_config3}[0]
    should be equal    ${channel_config}[1]    ${channel_config3}[1]
    @{channel_config}    获取显示属性配置    交流防雷器状态    数字量
    should be equal    ${channel_config}[0]    ${channel_config5}[0]
    should be equal    ${channel_config}[1]    ${channel_config5}[1]
    @{channel_config}    获取显示属性配置    市电输入状态    数字量
    should be equal    ${channel_config}[0]    ${channel_config6}[0]
    should be equal    ${channel_config}[1]    ${channel_config6}[1]
    @{channel_config}    获取显示属性配置    整流器输出电压    模拟量
    should be equal    ${channel_config}[0]    ${channel_config7}[0]
    should be equal    ${channel_config}[1]    ${channel_config7}[1]
    @{channel_config}    获取显示属性配置    电池组总电流    模拟量
    should be equal    ${channel_config}[0]    ${channel_config8}[0]
    should be equal    ${channel_config}[1]    ${channel_config8}[1]
    @{channel_config}    获取显示属性配置    直流电压    模拟量
    should be equal    ${channel_config}[0]    ${channel_config9}[0]
    should be equal    ${channel_config}[1]    ${channel_config9}[1]
    删除文件夹以及文件夹下所有文件    download//${用户参数文件}
    删除文件    download//${用户参数文件}.zip

告警参数导入功能测试
    [Tags]    PMSA-NTest
    连接CSU
    强制恢复默认值并重新登录
    sleep    1m
    系统复位
    ${起始时间}    获取系统时间
    ${用户参数文件}    导出参数文件    ${datapassword}
    ${告警级别获取1}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    ${告警级别获取2}    获取web参数量    系统过载告警
    ${告警级别获取3}    获取web参数量    交流停电
    ${告警级别获取4}    获取web参数量    市电频率高
    ${告警级别获取5}    获取web参数量    整流器告警
    ${告警级别获取6}    获取web参数量    多个整流器模块告警
    ${告警级别获取7}    获取web参数量    一次下电告警
    ${告警级别获取8}    获取web参数量    电池温度高
    ${告警级别获取9}    获取web参数量    直流防雷器异常
    ${告警级别获取10}    获取web参数量    烟雾告警
    ${告警干接点获取1}    获取web参数量    烟雾告警干接点
    ${告警干接点获取2}    获取web参数量    直流防雷器异常干接点
    ${告警干接点获取3}    获取web参数量    电池温度高干接点
    ${告警干接点获取4}    获取web参数量    多个整流器模块告警干接点
    ${告警干接点获取5}    获取web参数量    交流停电干接点
    ${告警干接点获取6}    获取web参数量    系统过载告警干接点
    Wait Until Keyword Succeeds    60    2    设置web参数量    <<禁止所有告警~0x1001030010001>>    次要
    ${告警级别获取}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    60    2    设置web参数量    系统过载告警    次要
    ${告警级别获取}    获取web参数量    系统过载告警
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    60    2    设置web参数量    交流停电    次要
    ${告警级别获取}    获取web参数量    交流停电
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    60    2    设置web参数量    市电频率高    次要
    ${告警级别获取}    获取web参数量    市电频率高
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    60    2    设置web参数量    整流器告警    次要
    ${告警级别获取}    获取web参数量    整流器告警
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    60    2    设置web参数量    多个整流器模块告警    次要
    ${告警级别获取}    获取web参数量    多个整流器模块告警
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    60    2    设置web参数量    一次下电告警    次要
    ${告警级别获取}    获取web参数量    一次下电告警
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    60    2    设置web参数量    电池温度高    次要
    ${告警级别获取}    获取web参数量    电池温度高
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    60    2    设置web参数量    直流防雷器异常    次要
    ${告警级别获取}    获取web参数量    直流防雷器异常
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    60    2    设置web参数量    烟雾告警    次要
    ${告警级别获取}    获取web参数量    烟雾告警
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    10    2    设置web参数量    烟雾告警干接点    1
    Wait Until Keyword Succeeds    10    2    设置web参数量    直流防雷器异常干接点    2
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池温度高干接点    3
    Wait Until Keyword Succeeds    10    2    设置web参数量    多个整流器模块告警干接点    4
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流停电干接点    5
    Wait Until Keyword Succeeds    10    2    设置web参数量    系统过载告警干接点    6
    sleep    10
    压缩文件夹    ${用户参数文件}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${用户参数文件}.zip    ${datapassword}
    sleep    4m
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    100
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    ${告警级别获取}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    should be true    '${告警级别获取}'=='${告警级别获取1}'
    ${告警级别获取}    获取web参数量    系统过载告警
    should be true    '${告警级别获取}'=='${告警级别获取2}'
    ${告警级别获取}    获取web参数量    交流停电
    should be true    '${告警级别获取}'=='${告警级别获取3}'
    ${告警级别获取}    获取web参数量    市电频率高
    should be true    '${告警级别获取}'=='${告警级别获取4}'
    ${告警级别获取}    获取web参数量    整流器告警
    should be true    '${告警级别获取}'=='${告警级别获取5}'
    ${告警级别获取}    获取web参数量    多个整流器模块告警
    should be true    '${告警级别获取}'=='${告警级别获取6}'
    ${告警级别获取}    获取web参数量    一次下电告警
    should be true    '${告警级别获取}'=='${告警级别获取7}'
    ${告警级别获取}    获取web参数量    电池温度高
    should be true    '${告警级别获取}'=='${告警级别获取8}'
    ${告警级别获取}    获取web参数量    直流防雷器异常
    should be true    '${告警级别获取}'=='${告警级别获取9}'
    ${告警级别获取}    获取web参数量    烟雾告警
    should be true    '${告警级别获取}'=='${告警级别获取10}'
    ${告警干接点获取}    获取web参数量    烟雾告警干接点
    should be true    ${告警干接点获取}==${告警干接点获取1}
    ${告警干接点获取}    获取web参数量    直流防雷器异常干接点
    should be true    ${告警干接点获取}==${告警干接点获取2}
    ${告警干接点获取}    获取web参数量    电池温度高干接点
    should be true    ${告警干接点获取}==${告警干接点获取3}
    ${告警干接点获取}    获取web参数量    多个整流器模块告警干接点
    should be true    ${告警干接点获取}==${告警干接点获取4}
    ${告警干接点获取}    获取web参数量    交流停电干接点
    should be true    ${告警干接点获取}==${告警干接点获取5}
    ${告警干接点获取}    获取web参数量    系统过载告警干接点
    should be true    ${告警干接点获取}==${告警干接点获取6}
    删除文件夹以及文件夹下所有文件    download//${用户参数文件}
    删除文件    download//${用户参数文件}.zip


用户参数导入功能测试
    [Tags]    PMSA-NTest
    连接CSU
    强制恢复默认值并重新登录
    sleep    1m
    系统复位
    ${起始时间}    获取系统时间
    ${用户参数文件}    导出参数文件    ${datapassword}
    ${CPU利用率高阈值获取old}    获取web参数量    CPU利用率高阈值
    ${系统过载告警阈值获取old}    获取web参数量    系统过载告警阈值
    ${交流电流高阈值获取old}    获取web参数量    交流电流高阈值
    ${节能带载率上限old}    获取web参数量    节能带载率上限
    ${环境温度过高阈值old}    获取web参数量    环境温度过高阈值
    ${直流电压高阈值old}    获取web参数量    直流电压高阈值
    ${电池电压低阈值获取old}    获取web参数量    电池电压低阈值
    ${浮充电压值获取old}    获取web参数量    浮充电压
    ${市电配置获取值old}    获取web参数量    市电配置_1
    ${电池组容量值获取old}    获取web参数量    电池组容量_1
    ${电池启用日期获取值old}    获取web参数量    <<电池启用日期-1~0xb001050010001>>
    ${整流器最小开机数量old}    获取web参数量    整流器最小开机数量
    Wait Until Keyword Succeeds    10    2    设置web参数量    CPU利用率高阈值    10    #下限
    ${CPU利用率高阈值获取}    获取web参数量    CPU利用率高阈值
    should be true    ${CPU利用率高阈值获取}==10
    Wait Until Keyword Succeeds    10    2    设置web参数量    系统过载告警阈值    10    #下限
    ${系统过载告警阈值获取}    获取web参数量    系统过载告警阈值
    should be true    ${系统过载告警阈值获取}==10
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流电流高阈值    600    #上限
    ${交流电流高阈值获取}    获取web参数量    交流电流高阈值
    Should Be Equal    ${交流电流高阈值获取}    600.0
    Wait Until Keyword Succeeds    10    2    设置web参数量    节能带载率上限    1
    ${节能带载率上限}    获取web参数量    节能带载率上限
    should be equal as numbers    ${节能带载率上限}    1
    Wait Until Keyword Succeeds    10    2    设置web参数量    环境温度过高阈值    63
    ${环境温度过高阈值}    获取web参数量    环境温度过高阈值
    should be true    ${环境温度过高阈值}==63
    Wait Until Keyword Succeeds    10    2    设置web参数量    直流电压高阈值    58
    ${直流电压高阈值}    获取web参数量    直流电压高阈值
    should be true    ${直流电压高阈值}==58
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池电压低阈值    48
    ${电池电压低阈值获取}    获取web参数量    电池电压低阈值
    should be true    ${电池电压低阈值获取}==48.0
    Wait Until Keyword Succeeds    10    2    设置web参数量    浮充电压    53.8    #中间值
    ${浮充电压值获取}    获取web参数量    浮充电压
    should be true    ${浮充电压值获取}==53.8
    Wait Until Keyword Succeeds    10    2    设置web参数量    市电配置_1    无
    ${获取值}    获取web参数量    市电配置_1
    should be true    '${获取值}'=='无'
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池组容量_1    200    #中间值
    ${电池组容量值获取}    获取web参数量    电池组容量_1
    ${电池组容量值获取}    create list    ${电池组容量值获取}
    ${电池组容量值确认}    create list    200
    should be true    ${电池组容量值获取}==${电池组容量值确认}
    Wait Until Keyword Succeeds    10    2    设置web参数量    <<电池启用日期-1~0xb001050010001>>    2000-01-01
    ${获取值}    获取web参数量    <<电池启用日期-1~0xb001050010001>>
    Should Be Equal As Strings    ${获取值}    2000-01-01
    Wait Until Keyword Succeeds    10    2    设置web参数量    整流器最小开机数量    1
    ${整流器最小开机数量}    获取web参数量    整流器最小开机数量
    should be equal    ${整流器最小开机数量}    1
    压缩文件夹    ${用户参数文件}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${用户参数文件}.zip    ${datapassword}
    sleep    4m
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    100
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    ${CPU利用率高阈值获取}    获取web参数量    CPU利用率高阈值
    should be true    ${CPU利用率高阈值获取}==${CPU利用率高阈值获取old}
    ${系统过载告警阈值获取}    获取web参数量    系统过载告警阈值
    should be true    ${系统过载告警阈值获取}==${系统过载告警阈值获取old}
    ${交流电流高阈值获取}    获取web参数量    交流电流高阈值
    Should Be Equal    ${交流电流高阈值获取}    ${交流电流高阈值获取old}
    ${节能带载率上限}    获取web参数量    节能带载率上限
    should be equal as numbers    ${节能带载率上限}    ${节能带载率上限old}
    ${环境温度过高阈值}    获取web参数量    环境温度过高阈值
    should be true    ${环境温度过高阈值}==${环境温度过高阈值old}
    ${直流电压高阈值}    获取web参数量    直流电压高阈值
    should be true    ${直流电压高阈值}==${直流电压高阈值old}
    ${电池电压低阈值获取}    获取web参数量    电池电压低阈值
    should be true    ${电池电压低阈值获取}==${电池电压低阈值获取old}
    ${浮充电压值获取}    获取web参数量    浮充电压
    should be true    ${浮充电压值获取}==${浮充电压值获取old}
    ${获取值}    获取web参数量    市电配置_1
    Should Be Equal    ${获取值}    ${市电配置获取值old}
    ${电池组容量值获取}    获取web参数量    电池组容量_1
    should be true    ${电池组容量值获取}==${电池组容量值获取old}
    ${获取值}    获取web参数量    <<电池启用日期-1~0xb001050010001>>
    Should Be Equal As Strings    ${获取值}    ${电池启用日期获取值old}
    ${整流器最小开机数量}    获取web参数量    整流器最小开机数量
    should be equal    ${整流器最小开机数量}    ${整流器最小开机数量old}
    删除文件夹以及文件夹下所有文件    download//${用户参数文件}
    删除文件    download//${用户参数文件}.zip


各类参数文件导入功能测试
    [Tags]    PMSA-NTest
    连接CSU
    强制恢复默认值并重新登录
    sleep    1m
    系统复位
    ${起始时间}    获取系统时间
    ${用户参数文件}    导出参数文件    ${datapassword}
    ${告警级别获取1}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    ${告警级别获取2}    获取web参数量    系统过载告警
    ${市电配置获取1}    获取web参数量    市电配置_1
    ${CPU占用率高阈值获取1}    获取web参数量    CPU利用率高阈值
    ${系统过载告警阈值获取1}    获取web参数量    系统过载告警阈值
    @{channel_config1}    获取显示属性配置    整流器输出电压    模拟量
    ${告警干接点获取1}    获取web参数量    烟雾告警干接点
    Wait Until Keyword Succeeds    30    2    设置web参数量    <<禁止所有告警~0x1001030010001>>    次要
    ${告警级别获取}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    30    2    设置web参数量    系统过载告警    次要
    ${告警级别获取}    获取web参数量    系统过载告警
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    30    2    设置web参数量    市电配置_1    无
    ${市电配置获取}    获取web参数量    市电配置_1
    should be true    '${市电配置获取}'=='无'
    Wait Until Keyword Succeeds    30    2    设置web参数量    CPU利用率高阈值    10    #下限
    ${CPU占用率高阈值获取}    获取web参数量    CPU利用率高阈值
    should be true    ${CPU占用率高阈值获取}==10
    Wait Until Keyword Succeeds    30    2    设置web参数量    系统过载告警阈值    10    #下限
    ${系统过载告警阈值获取}    获取web参数量    系统过载告警阈值
    should be true    ${系统过载告警阈值获取}==10
    Wait Until Keyword Succeeds    10    2    设置web参数量    烟雾告警干接点    2
    ${channel_config}    显示属性配置    整流器输出电压    模拟量    OFF    OFF
    @{channel_config}    获取显示属性配置    整流器输出电压    模拟量
    should be equal    ${channel_config}[0]    OFF
    should be equal    ${channel_config}[1]    OFF
    压缩文件夹    ${用户参数文件}
    ${起始时间}    获取系统时间
    ${导入结果}    导入参数文件    ${用户参数文件}.zip    ${datapassword}
    sleep    4m
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    100
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    ${告警级别获取}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    should be true    '${告警级别获取}'=='${告警级别获取1}'
    ${告警级别获取}    获取web参数量    系统过载告警
    should be true    '${告警级别获取}'=='${告警级别获取2}'
    ${获取值}    获取web参数量    市电配置_1
    Should Be Equal    ${获取值}    ${市电配置获取1}
    ${CPU利用率高阈值获取}    获取web参数量    CPU利用率高阈值
    should be true    ${CPU利用率高阈值获取}==${CPU占用率高阈值获取1}
    ${系统过载告警阈值获取}    获取web参数量    系统过载告警阈值
    should be true    ${系统过载告警阈值获取}==${系统过载告警阈值获取1}
    ${告警干接点获取}    获取web参数量    烟雾告警干接点
    should be true    ${告警干接点获取}==${告警干接点获取1}
    @{channel_config}    获取显示属性配置    整流器输出电压    模拟量
    should be equal    ${channel_config}[0]    ${channel_config1}[0]
    should be equal    ${channel_config}[1]    ${channel_config1}[1]
    删除文件夹以及文件夹下所有文件    download//${用户参数文件}
    删除文件    download//${用户参数文件}.zip


英文界面下各类参数文件导入功能测试
    [Tags]    PMSA-NTest
    连接CSU
    强制恢复默认值并重新登录
    sleep    1m
    系统复位
    ${起始时间}    获取系统时间
    ${用户参数文件}    导出参数文件    ${datapassword}
    ${告警级别获取1}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    ${告警级别获取2}    获取web参数量    系统过载告警
    ${市电配置获取1}    获取web参数量    市电配置_1
    ${CPU占用率高阈值获取1}    获取web参数量    CPU利用率高阈值
    ${系统过载告警阈值获取1}    获取web参数量    系统过载告警阈值
    @{channel_config1}    获取显示属性配置    整流器输出电压    模拟量
    ${告警干接点获取1}    获取web参数量    烟雾告警干接点
    Wait Until Keyword Succeeds    30    2    设置web参数量    <<禁止所有告警~0x1001030010001>>    次要
    ${告警级别获取}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    30    2    设置web参数量    系统过载告警    次要
    ${告警级别获取}    获取web参数量    系统过载告警
    should be true    '${告警级别获取}'=='次要'
    Wait Until Keyword Succeeds    30    2    设置web参数量    市电配置_1    无
    ${市电配置获取}    获取web参数量    市电配置_1
    should be true    '${市电配置获取}'=='无'
    Wait Until Keyword Succeeds    30    2    设置web参数量    CPU利用率高阈值    10    #下限
    ${CPU占用率高阈值获取}    获取web参数量    CPU利用率高阈值
    should be true    ${CPU占用率高阈值获取}==10
    Wait Until Keyword Succeeds    30    2    设置web参数量    系统过载告警阈值    10    #下限
    ${系统过载告警阈值获取}    获取web参数量    系统过载告警阈值
    should be true    ${系统过载告警阈值获取}==10
    Wait Until Keyword Succeeds    10    2    设置web参数量    烟雾告警干接点    2
    ${channel_config}    显示属性配置    整流器输出电压    模拟量    OFF    OFF
    @{channel_config}    获取显示属性配置    整流器输出电压    模拟量
    should be equal    ${channel_config}[0]    OFF
    should be equal    ${channel_config}[1]    OFF
    压缩文件夹    ${用户参数文件}
    ${起始时间}    获取系统时间
    设置语言    EN
    连接CSU
    ${导入结果}    导入参数文件    ${用户参数文件}.zip    ${datapassword}
    sleep    4m
    设置语言    CN
    连接CSU
    ${终止时间}    获取系统时间
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间}    所有    1    100
    should contain match    ${历史事件内容1}    *导入 配置和参数成功*
    should be true    ${导入结果}
    ${告警级别获取}    获取web参数量    <<禁止所有告警~0x1001030010001>>
    should be true    '${告警级别获取}'=='${告警级别获取1}'
    ${告警级别获取}    获取web参数量    系统过载告警
    should be true    '${告警级别获取}'=='${告警级别获取2}'
    ${获取值}    获取web参数量    市电配置_1
    Should Be Equal    ${获取值}    ${市电配置获取1}
    ${CPU利用率高阈值获取}    获取web参数量    CPU利用率高阈值
    should be true    ${CPU利用率高阈值获取}==${CPU占用率高阈值获取1}
    ${系统过载告警阈值获取}    获取web参数量    系统过载告警阈值
    should be true    ${系统过载告警阈值获取}==${系统过载告警阈值获取1}
    ${告警干接点获取}    获取web参数量    烟雾告警干接点
    should be true    ${告警干接点获取}==${告警干接点获取1}
    @{channel_config}    获取显示属性配置    整流器输出电压    模拟量
    should be equal    ${channel_config}[0]    ${channel_config1}[0]
    should be equal    ${channel_config}[1]    ${channel_config1}[1]
    删除文件夹以及文件夹下所有文件    download//${用户参数文件}
    删除文件    download//${用户参数文件}.zip
