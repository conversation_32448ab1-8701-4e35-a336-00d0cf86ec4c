*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
温湿度传感器通讯断告警1
    [Tags]    trap
    [Setup]
    连接CSU
    ${级别设置值}    获取web参数量    <<温湿度传感器通讯断告警~0x30001030010001>>
    run keyword if    '${级别设置值}'=='屏蔽'    wait until keyword succeeds    10    2    设置web参数量    <<温湿度传感器通讯断告警~0x30001030010001>>    严重
    进行SNMP_V2/V3连接    ${snmp连接方式}
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    控制子工具运行停止    DMU_WS312M1    关闭
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    温湿度传感器通讯状态    异常
    wait until keyword succeeds    5m    2    查询指定告警信息    温湿度传感器通讯断告警
    ${snmp英文名}    获取snmp单个告警英文名    温湿度传感器通讯断告警
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    控制子工具运行停止    DMU_WS312M1    开启
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    温湿度传感器通讯状态    正常
    wait until keyword succeeds    5m    1    判断告警不存在    温湿度传感器通讯断告警
    ${snmp英文名}    获取snmp单个告警英文名    温湿度传感器通讯断告警
    ${告警消失}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警消失}
    [Teardown]    Run Keywords    控制子工具运行停止    DMU_WS312M1    开启
    ...    AND    sleep    3min
