*** Settings ***
Suite Setup       智能空开参数测试前置条件
Resource          ../../../测试用例关键字.robot


*** Test Cases ***
获取和设置智能空开告警级别
    [Documentation]    ${协议数据} | ${表单名称} | ${命令名称} | ${单个数据在子表中的中文名称} | ${屏号}=None | ${g_ver}=
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=智能空开
    ${排除列表}    create list
    P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    1
    ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取智能空开告警级别~CID1=D3H>>    <<获取智能空开告警级别~CID2=81H>>    None    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${1104待测}    1104_获取1104/1363指定设备指定信号量    ${协议数据}    ${web数据}    ${列表1}
    FOR    ${i}    IN    @{1104待测}
    ${告警全称}    Get From Dictionary    ${i}    signal_name
    @{告警名称}    Evaluate    '${告警全称}'.split('-')
    ${告警名称}    Set Variable    ${告警名称}[0]
    ${告警名称}    Set Variable    ${告警名称}[2:]
    FOR    ${VAR}    IN RANGE    0    5
        ${VAR}    evaluate    str(${VAR})
        ${设置结果}    1104设置单个参数    ${g_prtcl_1363}    <<设置智能空开告警级别~CID1=D3H>>    <<设置智能空开告警级别~CID2=82H>>    ${告警名称}    ${VAR}    None    ${g_ver_1363}
        should be true    ${设置结果}
        Sleep    30
        ${获取值}    获取一个数据的值    ${g_prtcl_1363}    <<获取智能空开告警级别~CID1=D3H>>    <<获取智能空开告警级别~CID2=81H>>    ${告警名称}    None    ${g_ver_1363}
        should be equal    '${获取值}'    '${VAR}'
        ${1104告警级别}    获取1104告警级别取值约定    ${告警全称}    ${VAR}
        ${WEB告警级别}    获取web参数量    ${告警全称}
        should be equal    ${1104告警级别}    ${WEB告警级别}
    END
    END

获取智能空开参数(1-10)
    ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取智能空开参数(1-10)~CID1=D3H>>    <<获取智能空开参数(1-10)~CID2=A1H>>    None    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${对比结果}    批量对比数据_1104_WEB    ${协议数据}    ${web数据}    1363_智能空开_参数量1-10
    should be true    ${对比结果}

获取智能空开参数(11-20)
    ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取智能空开参数(11-20)~CID1=D3H>>    <<获取智能空开参数(11-20)~CID2=A2H>>    None    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${对比结果}    批量对比数据_1104_WEB    ${协议数据}    ${web数据}    1363_智能空开_参数量11-20
    should be true    ${对比结果}

获取智能空开参数(21-30)
    ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取智能空开参数(21-30)~CID1=D3H>>    <<获取智能空开参数(21-30)~CID2=A6H>>    None    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${对比结果}    批量对比数据_1104_WEB    ${协议数据}    ${web数据}    1363_智能空开_参数量21-30
    should be true    ${对比结果}

获取智能空开参数(31-40)
    ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取智能空开参数(31-40)~CID1=D3H>>    <<获取智能空开参数(31-40)~CID2=A7H>>    None    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${对比结果}    批量对比数据_1104_WEB    ${协议数据}    ${web数据}    1363_智能空开_参数量31-40
    should be true    ${对比结果}

设置智能空开参数量
    设置子工具值    DMU_IntelAirSwit    1    建链    空开额定电流    500
    sleep    5m
    ${1363数据}    1104批量参数设置测试    ${g_prtcl_1363}    <<设置智能空开参数~CID1=D3H>>    <<设置智能空开参数~CID2=49H>>    ${g_ver_1363}    01
    ${对比结果}    批量对比参数设置_1104    ${1363数据}    1363_智能空开_参数设置
    should be true    ${对比结果}
    设置子工具值    DMU_IntelAirSwit    1    建链    空开额定电流    125
    sleep    5m
