*** Settings ***
Force Tags
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
电池高温下电使能下电测试
    
    铁锂电池管理初始化
    #获取参数/默认值
    wait until keyword succeeds    10    1    设置web参数量    电池高温下电使能    允许
    ${告警级别}    获取web参数量    电池高温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    电池高温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    电池高温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    ${电池电压}    获取web实时数据    电池电压-1
    sleep    3
    ${可设范围}    获取web参数可设置范围    电池高温下电温度
    设置web参数量    电池高温下电温度    ${可设范围}[0]
    run keyword and ignore error    设置web参数量    下电控制延时    0
    显示属性配置    电池高温下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    3
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    10    2
    ...    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    设置负载电压电流    53.5    10
    打开负载输出
    sleep    5m
    wait until keyword succeeds    3m    1    判断告警存在    电池高温下电
    Wait Until Keyword Succeeds    3m    2    信号量数据值为    电池高温下电控制状态    动作
    #恢复
    设置web参数量    电池高温下电使能    禁止
    Comment    Wait Until Keyword Succeeds    7m    2    信号量数据值为    电池高温下电控制状态    恢复
    wait until keyword succeeds    3m    2    判断告警不存在    电池高温下电
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    0    1
    ...    电池_1    电池温度
    设置web参数量    电池高温下电使能    允许
    Wait Until Keyword Succeeds    5    1    显示属性配置    电池高温下电控制状态    数字量    web_attr=Off
    ...    gui_attr=Off
    关闭负载输出
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}
    ...    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池高温下电温度    电池高温下电使能
    ...    AND    关闭负载输出

电池高温下电保护测试
    
    铁锂电池管理初始化
    #获取参数/默认值
    wait until keyword succeeds    10    1    设置web参数量    电池高温下电使能    允许
    ${告警级别}    获取web参数量    电池高温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    电池高温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    电池高温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    sleep    3
    ${可设范围}    获取web参数可设置范围    电池高温下电温度
    设置web参数量    电池高温下电温度    ${可设范围}[0]
    run keyword and ignore error    设置web参数量    下电控制延时    0
    显示属性配置    电池高温下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    3
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    10    2
    ...    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    设置负载电压电流    53.5    10
    打开负载输出
    sleep    5m
    wait until keyword succeeds    10m    1    判断告警存在    电池高温下电
    Wait Until Keyword Succeeds    3m    2    信号量数据值为    电池高温下电控制状态    动作
    #恢复
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    0    1
    ...    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    sleep    5m
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    电池高温下电控制状态    恢复
    wait until keyword succeeds    4m    2    判断告警不存在    电池高温下电
    显示属性配置    电池高温下电控制状态    数字量    web_attr=Off    gui_attr=Off
    关闭负载输出
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}
    ...    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池高温下电温度    电池高温下电使能
    ...    AND    关闭负载输出

电池高温下电延时控制功能测试
    
    铁锂电池管理初始化
    #获取参数/默认值
    wait until keyword succeeds    10    1    设置web参数量    电池高温下电使能    允许
    ${告警级别}    获取web参数量    电池高温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    电池高温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    电池高温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    sleep    3
    ${可设范围}    获取web参数可设置范围    电池高温下电温度
    设置web参数量    电池高温下电温度    ${可设范围}[0]
    run keyword and ignore error    设置web参数量    下电控制延时    300
    显示属性配置    电池高温下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    3
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    10    2
    ...    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    设置负载电压电流    53.5    10
    打开负载输出
    sleep    5m
    wait until keyword succeeds    10m    1    判断告警存在    电池高温下电
    ${状态}    Run Keyword And Return Status    Wait Until Keyword Succeeds    4m    2    信号量数据值为    电池高温下电控制状态
    ...    动作
    should not be true    ${状态}
    Wait Until Keyword Succeeds    10m    1    信号量数据值为    电池高温下电控制状态    动作
    #恢复
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    0    1
    ...    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    sleep    5m
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    电池高温下电控制状态    恢复
    wait until keyword succeeds    4m    2    判断告警不存在    电池高温下电
    显示属性配置    电池高温下电控制状态    数字量    web_attr=Off    gui_attr=Off
    关闭负载输出
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}
    ...    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池高温下电温度    电池高温下电使能    下电控制延时
    ...    AND    关闭负载输出

电池无效时恢复电池上电（高温下电）
    
    铁锂电池管理初始化
    #获取参数/默认值
    wait until keyword succeeds    10    1    设置web参数量    电池高温下电使能    允许
    ${告警级别}    获取web参数量    电池高温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    电池高温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    电池高温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    sleep    3
    ${可设范围}    获取web参数可设置范围    电池高温下电温度
    设置web参数量    电池高温下电温度    ${可设范围}[0]
    run keyword and ignore error    设置web参数量    下电控制延时    0
    显示属性配置    电池高温下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    3
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    10    2
    ...    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    设置负载电压电流    53.5    10
    打开负载输出
    sleep    5m
    wait until keyword succeeds    6m    1    判断告警存在    电池高温下电
    Wait Until Keyword Succeeds    6m    2    信号量数据值为    电池高温下电控制状态    动作
    #恢复
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    0
    wait until keyword succeeds    4m    2    判断告警不存在    电池高温下电
    显示属性配置    电池高温下电控制状态    数字量    web_attr=Off    gui_attr=Off
    关闭负载输出
    [Teardown]    run keywords    设置web参数量    电池组容量_1    100
    ...    AND    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}
    ...    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池高温下电温度    电池高温下电使能
    ...    AND    关闭负载输出

电池无效时禁止高温下电
    
    铁锂电池管理初始化
    #获取参数/默认值
    wait until keyword succeeds    10    1    设置web参数量    电池高温下电使能    允许
    ${告警级别}    获取web参数量    电池高温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    电池高温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    电池高温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    sleep    3
    ${可设范围}    获取web参数可设置范围    电池高温下电温度
    设置web参数量    电池高温下电温度    ${可设范围}[0]
    run keyword and ignore error    设置web参数量    下电控制延时    0
    sleep    3
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    10    2
    ...    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    0
    设置负载电压电流    53.5    10
    打开负载输出
    ${状态}    Run Keyword And Return Status    wait until keyword succeeds    6m    1    判断告警存在    电池高温下电
    should not be true    ${状态}
    关闭负载输出
    [Teardown]    run keywords    设置web参数量    电池组容量_1    100
    ...    AND    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}
    ...    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池高温下电温度    电池高温下电使能
    ...    AND    关闭负载输出

电池高温下电禁止下电测试
    
    铁锂电池管理初始化
    #获取参数/默认值
    wait until keyword succeeds    10    1    设置web参数量    电池高温下电使能    允许
    ${告警级别}    获取web参数量    电池高温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    电池高温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    电池高温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    ${电池电压}    获取web实时数据    电池电压-1
    sleep    3
    ${可设范围}    获取web参数可设置范围    电池高温下电温度
    设置web参数量    电池高温下电温度    ${可设范围}[0]
    run keyword and ignore error    设置web参数量    下电控制延时    0
    wait until keyword succeeds    10    1    设置web参数量    电池高温下电使能    禁止
    sleep    3
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    10    2
    ...    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    设置负载电压电流    53.5    10
    打开负载输出
    ${状态}    Run Keyword And Return Status    wait until keyword succeeds    6m    1    判断告警存在    电池高温下电
    should not be true    ${状态}
    关闭负载输出
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}
    ...    0    1    电池_1    电池温度
    ...    AND    设置web参数量    电池高温下电使能    允许
    ...    AND    Wait Until Keyword Succeeds    30    1    设置web设备参数量为默认值    电池高温下电温度
    ...    AND    设置web参数量    电池高温下电使能    禁止
    ...    AND    关闭负载输出

电池高温下电恢复上电测试（系统停电）
    
    铁锂电池管理初始化
    #获取参数/默认值
    wait until keyword succeeds    10    1    设置web参数量    电池高温下电使能    允许
    ${告警级别}    获取web参数量    电池高温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    电池高温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    电池高温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    sleep    3
    ${可设范围}    获取web参数可设置范围    电池高温下电温度
    设置web参数量    电池高温下电温度    ${可设范围}[0]
    run keyword and ignore error    设置web参数量    下电控制延时    0
    显示属性配置    电池高温下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    3
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    10    2
    ...    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    设置负载电压电流    53.5    10
    打开负载输出
    sleep    5m
    wait until keyword succeeds    3m    1    判断告警存在    电池高温下电
    Wait Until Keyword Succeeds    3m    2    信号量数据值为    电池高温下电控制状态    动作
    关闭交流源输出
    ${状态}    Run Keyword And Return Status    Wait Until Keyword Succeeds    4m    2    信号量数据值为    电池高温下电控制状态
    ...    恢复
    should not be true    ${状态}
    #恢复
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    0    1
    ...    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    sleep    5m
    Wait Until Keyword Succeeds    3m    2    信号量数据值为    电池高温下电控制状态    恢复
    wait until keyword succeeds    4m    2    判断告警不存在    电池高温下电
    显示属性配置    电池高温下电控制状态    数字量    web_attr=Off    gui_attr=Off
    关闭负载输出
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}
    ...    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池高温下电温度    电池高温下电使能
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出

电池低温下电使能下电测试
    
    铁锂电池管理初始化
    #获取参数/默认值
    wait until keyword succeeds    10    1    设置web参数量    电池低温下电使能    允许
    ${告警级别}    获取web参数量    电池低温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    电池低温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    电池低温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    sleep    3
    ${可设范围}    获取web参数可设置范围    电池低温下电温度
    设置web参数量    电池低温下电温度    ${可设范围}[1]
    run keyword and ignore error    设置web参数量    下电控制延时    0
    显示属性配置    电池低温下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    3
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    -10    0.001
    ...    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    设置负载电压电流    53.5    10
    打开负载输出
    sleep    5m
    wait until keyword succeeds    6m    1    判断告警存在    电池低温下电
    Wait Until Keyword Succeeds    6m    2    信号量数据值为    电池低温下电控制状态    动作
    #恢复
    设置web参数量    电池低温下电使能    禁止
    wait until keyword succeeds    4m    2    判断告警不存在    电池低温下电
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    0    1
    ...    电池_1    电池温度
    设置web参数量    电池低温下电使能    允许
    Wait Until Keyword Succeeds    5    1    显示属性配置    电池低温下电控制状态    数字量    web_attr=Off
    ...    gui_attr=Off
    关闭负载输出
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}
    ...    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池低温下电温度    电池低温下电使能
    ...    AND    关闭负载输出

电池低温下电保护测试
    [Documentation]    无下电恢复电压设置，下电恢复：一次下电恢复：直流电压>=下电电压+5V或者直流电压>=浮充电压-1V；电池下电恢复：直流电压>=浮充电压-1V
    
    铁锂电池管理初始化
    #获取参数/默认值
    wait until keyword succeeds    10    1    设置web参数量    电池低温下电使能    允许
    ${告警级别}    获取web参数量    电池低温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    电池低温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    电池低温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    sleep    3
    ${可设范围}    获取web参数可设置范围    电池低温下电温度
    设置web参数量    电池低温下电温度    ${可设范围}[1]
    run keyword and ignore error    设置web参数量    下电控制延时    0
    显示属性配置    电池低温下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    3
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    -10    0.001
    ...    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    设置负载电压电流    53.5    10
    打开负载输出
    sleep    5m
    wait until keyword succeeds    3m    1    判断告警存在    电池低温下电
    Wait Until Keyword Succeeds    3m    2    信号量数据值为    电池低温下电控制状态    动作
    #恢复
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    0    1
    ...    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    sleep    5m
    Wait Until Keyword Succeeds    3m    2    信号量数据值为    电池低温下电控制状态    恢复
    wait until keyword succeeds    4m    2    判断告警不存在    电池低温下电
    显示属性配置    电池低温下电控制状态    数字量    web_attr=Off    gui_attr=Off
    关闭负载输出
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}
    ...    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池低温下电温度    电池低温下电使能
    ...    AND    关闭负载输出

电池低温下电延时控制功能测试
    
    铁锂电池管理初始化
    #获取参数/默认值
    wait until keyword succeeds    10    1    设置web参数量    电池低温下电使能    允许
    ${告警级别}    获取web参数量    电池低温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    电池低温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    电池低温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    sleep    3
    ${可设范围}    获取web参数可设置范围    电池低温下电温度
    设置web参数量    电池低温下电温度    ${可设范围}[1]
    run keyword and ignore error    设置web参数量    下电控制延时    300
    显示属性配置    电池低温下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    3
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    -10    0.001
    ...    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    设置负载电压电流    53.5    10
    打开负载输出
    sleep    5m
    wait until keyword succeeds    6m    1    判断告警存在    电池低温下电
    ${状态}    Run Keyword And Return Status    Wait Until Keyword Succeeds    4m    2    信号量数据值为    电池低温下电控制状态
    ...    动作
    should not be true    ${状态}
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池低温下电控制状态    动作
    #恢复
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    0    1
    ...    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    sleep    5m
    Wait Until Keyword Succeeds    3m    2    信号量数据值为    电池低温下电控制状态    恢复
    wait until keyword succeeds    4m    2    判断告警不存在    电池低温下电
    显示属性配置    电池低温下电控制状态    数字量    web_attr=Off    gui_attr=Off
    关闭负载输出
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}
    ...    0    1    电池_1    电池温度
    ...    AND    Wait Until Keyword Succeeds    30    1    设置web设备参数量为默认值    电池低温下电温度
    ...    电池低温下电使能    下电控制延时
    ...    AND    关闭负载输出

电池无效时恢复电池上电（低温下电）
    
    铁锂电池管理初始化
    #获取参数/默认值
    wait until keyword succeeds    10    1    设置web参数量    电池低温下电使能    允许
    ${告警级别}    获取web参数量    电池低温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    电池低温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    电池低温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    sleep    3
    ${可设范围}    获取web参数可设置范围    电池低温下电温度
    设置web参数量    电池低温下电温度    ${可设范围}[1]
    run keyword and ignore error    设置web参数量    下电控制延时    0
    显示属性配置    电池低温下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    3
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    -10    0.001
    ...    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    设置负载电压电流    53.5    10
    打开负载输出
    sleep    5m
    wait until keyword succeeds    3m    1    判断告警存在    电池低温下电
    Wait Until Keyword Succeeds    3m    2    信号量数据值为    电池低温下电控制状态    动作
    #恢复
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    0
    wait until keyword succeeds    4m    2    判断告警不存在    电池低温下电
    显示属性配置    电池低温下电控制状态    数字量    web_attr=Off    gui_attr=Off
    关闭负载输出
    [Teardown]    run keywords    设置web参数量    电池组容量_1    100
    ...    AND    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}
    ...    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池低温下电温度    电池低温下电使能
    ...    AND    关闭负载输出

电池无效时禁止低温下电
    
    铁锂电池管理初始化
    #获取参数/默认值
    wait until keyword succeeds    10    1    设置web参数量    电池低温下电使能    允许
    ${告警级别}    获取web参数量    电池低温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    电池低温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    电池低温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    sleep    3
    ${可设范围}    获取web参数可设置范围    电池低温下电温度
    设置web参数量    电池低温下电温度    ${可设范围}[1]
    run keyword and ignore error    设置web参数量    下电控制延时    0
    sleep    3
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    -10    0.001
    ...    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    0
    设置负载电压电流    53.5    10
    打开负载输出
    sleep    5m
    ${状态}    Run Keyword And Return Status    wait until keyword succeeds    6m    2    判断告警存在    电池低温下电
    should not be true    ${状态}
    关闭负载输出
    [Teardown]    run keywords    设置web参数量    电池组容量_1    100
    ...    AND    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}
    ...    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池低温下电温度    电池低温下电使能
    ...    AND    关闭负载输出

电池低温下电禁止下电测试
    
    铁锂电池管理初始化
    #获取参数/默认值
    wait until keyword succeeds    10    1    设置web参数量    电池低温下电使能    允许
    ${告警级别}    获取web参数量    电池低温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    电池低温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    电池低温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    sleep    3
    ${可设范围}    获取web参数可设置范围    电池低温下电温度
    设置web参数量    电池低温下电温度    ${可设范围}[1]
    run keyword and ignore error    设置web参数量    下电控制延时    0
    wait until keyword succeeds    10    1    设置web参数量    电池低温下电使能    禁止
    sleep    3
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    -10    0.001
    ...    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    设置负载电压电流    53.5    10
    打开负载输出
    sleep    5m
    ${状态}    Run Keyword And Return Status    wait until keyword succeeds    6m    1    判断告警存在    电池低温下电
    should not be true    ${状态}
    关闭负载输出
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}
    ...    0    1    电池_1    电池温度
    ...    AND    设置web参数量    电池低温下电使能    允许
    ...    AND    Wait Until Keyword Succeeds    30    1    设置web设备参数量为默认值    电池低温下电温度
    ...    AND    设置web参数量    电池低温下电使能    禁止
    ...    AND    关闭负载输出

电池低温下电恢复上电测试（系统停电）
    [Documentation]    无下电恢复电压设置，下电恢复：一次下电恢复：直流电压>=下电电压+5V或者直流电压>=浮充电压-1V；电池下电恢复：直流电压>=浮充电压-1V
    
    铁锂电池管理初始化
    #获取参数/默认值
    wait until keyword succeeds    10    1    设置web参数量    电池低温下电使能    允许
    ${告警级别}    获取web参数量    电池低温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    电池低温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    电池低温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    sleep    3
    ${可设范围}    获取web参数可设置范围    电池低温下电温度
    设置web参数量    电池低温下电温度    ${可设范围}[1]
    run keyword and ignore error    设置web参数量    下电控制延时    0
    显示属性配置    电池低温下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    3
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    -10    0.001
    ...    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    设置负载电压电流    53.5    10
    打开负载输出
    sleep    5m
    wait until keyword succeeds    6m    1    判断告警存在    电池低温下电
    Wait Until Keyword Succeeds    3m    2    信号量数据值为    电池低温下电控制状态    动作
    关闭交流源输出
    ${状态}    Run Keyword And Return Status    Wait Until Keyword Succeeds    4m    2    信号量数据值为    电池低温下电控制状态
    ...    恢复
    should not be true    ${状态}
    #恢复
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    0    1
    ...    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    sleep    5m
    Wait Until Keyword Succeeds    3m    2    信号量数据值为    电池低温下电控制状态    恢复
    wait until keyword succeeds    4m    2    判断告警不存在    电池低温下电
    显示属性配置    电池低温下电控制状态    数字量    web_attr=Off    gui_attr=Off
    关闭负载输出
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}
    ...    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池低温下电温度    电池低温下电使能
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出
