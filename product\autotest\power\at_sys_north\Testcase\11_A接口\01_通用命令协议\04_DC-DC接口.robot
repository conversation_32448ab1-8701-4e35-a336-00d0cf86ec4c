*** Settings ***
Resource          ../A接口关键字_V30.robot

*** Test Cases ***
获取DC-DC模块的模拟量
    ${协议数据}    A接口获取数据    <<获取DC-DC段模拟量~CID1=44H>>    <<获取DC-DC段模拟量~CID2=41H>>    None
    判断返回异常结果    ${协议数据}    7
    ${协议数据}    A接口获取数据    <<获取DC-DC段模拟量~CID1=44H>>    <<获取DC-DC段模拟量~CID2=41H>>    01H
    判断返回异常结果    ${协议数据}    7
    ${协议数据}    A接口获取数据    <<获取DC-DC段模拟量~CID1=44H>>    <<获取DC-DC段模拟量~CID2=41H>>    FFH
    判断返回异常结果    ${协议数据}    7
    ${协议数据}    A接口获取数据    <<获取DC-DC段模拟量~CID1=44H>>    <<获取DC-DC段模拟量~CID2=41H>>    00H
    判断返回异常结果    ${协议数据}    7

获取DC-DC模块的开关量
    ${协议数据}    A接口获取数据    <<获取DC-DC段开关量~CID1=44H>>    <<获取DC-DC段开关量~CID2=43H>>    None
    判断返回异常结果    ${协议数据}    7
    ${协议数据}    A接口获取数据    <<获取DC-DC段开关量~CID1=44H>>    <<获取DC-DC段开关量~CID2=43H>>    01H
    判断返回异常结果    ${协议数据}    7
    ${协议数据}    A接口获取数据    <<获取DC-DC段开关量~CID1=44H>>    <<获取DC-DC段开关量~CID2=43H>>    FFH
    判断返回异常结果    ${协议数据}    7
    ${协议数据}    A接口获取数据    <<获取DC-DC段开关量~CID1=44H>>    <<获取DC-DC段开关量~CID2=43H>>    00H
    判断返回异常结果    ${协议数据}    7
    ${协议数据}    A接口获取数据    <<获取DC-DC段开关量~CID1=44H>>    <<获取DC-DC段开关量~CID2=43H>>    02H
    判断返回异常结果    ${协议数据}    7

获取DC-DC模块的告警量
    ${协议数据}    A接口获取数据    <<获取DC-DC段告警量~CID1=44H>>    <<获取DC-DC段告警量~CID2=44H>>    None
    判断返回异常结果    ${协议数据}    7
    ${协议数据}    A接口获取数据    <<获取DC-DC段告警量~CID1=44H>>    <<获取DC-DC段告警量~CID2=44H>>    01H
    判断返回异常结果    ${协议数据}    7
    ${协议数据}    A接口获取数据    <<获取DC-DC段告警量~CID1=44H>>    <<获取DC-DC段告警量~CID2=44H>>    FFH
    判断返回异常结果    ${协议数据}    7
    ${协议数据}    A接口获取数据    <<获取DC-DC段告警量~CID1=44H>>    <<获取DC-DC段告警量~CID2=44H>>    02H
    判断返回异常结果    ${协议数据}    7
    ${协议数据}    A接口获取数据    <<获取DC-DC段告警量~CID1=44H>>    <<获取DC-DC段告警量~CID2=44H>>    FEH
    判断返回异常结果    ${协议数据}    7
    ${协议数据}    A接口获取数据    <<获取DC-DC段告警量~CID1=44H>>    <<获取DC-DC段告警量~CID2=44H>>    00H
    判断返回异常结果    ${协议数据}    7

遥控开DC-DC模块
    ${协议数据}    A接口遥控    <<遥控DC-DC模块~CID1=44H>>    <<遥控DC-DC模块~CID2=45H>>    开DC-DC模块    1
    should not be true    ${协议数据}
    ${协议数据}    A接口遥控    <<遥控DC-DC模块~CID1=44H>>    <<遥控DC-DC模块~CID2=45H>>    开DC-DC模块    2
    should not be true    ${协议数据}
    ${协议数据}    A接口遥控    <<遥控DC-DC模块~CID1=44H>>    <<遥控DC-DC模块~CID2=45H>>    开DC-DC模块    254
    should not be true    ${协议数据}
    ${协议数据}    A接口遥控    <<遥控DC-DC模块~CID1=44H>>    <<遥控DC-DC模块~CID2=45H>>    开DC-DC模块    255
    should not be true    ${协议数据}
    ${协议数据}    A接口遥控    <<遥控DC-DC模块~CID1=44H>>    <<遥控DC-DC模块~CID2=45H>>    开DC-DC模块    0
    should not be true    ${协议数据}
    Comment    ${协议数据}    A接口遥控    <<遥控DC-DC模块~CID1=44H>>    <<遥控DC-DC模块~CID2=45H>>    开DC-DC模块    None
    Comment    should not be true    ${协议数据}

遥控关DC-DC模块
    ${协议数据}    A接口遥控    <<遥控DC-DC模块~CID1=44H>>    <<遥控DC-DC模块~CID2=45H>>    关DC-DC模块    1
    should not be true    ${协议数据}
    ${协议数据}    A接口遥控    <<遥控DC-DC模块~CID1=44H>>    <<遥控DC-DC模块~CID2=45H>>    关DC-DC模块    2
    should not be true    ${协议数据}
    ${协议数据}    A接口遥控    <<遥控DC-DC模块~CID1=44H>>    <<遥控DC-DC模块~CID2=45H>>    关DC-DC模块    254
    should not be true    ${协议数据}
    ${协议数据}    A接口遥控    <<遥控DC-DC模块~CID1=44H>>    <<遥控DC-DC模块~CID2=45H>>    关DC-DC模块    255
    should not be true    ${协议数据}
    ${协议数据}    A接口遥控    <<遥控DC-DC模块~CID1=44H>>    <<遥控DC-DC模块~CID2=45H>>    关DC-DC模块    0
    should not be true    ${协议数据}
    Comment    ${协议数据}    A接口遥控    <<遥控DC-DC模块~CID1=44H>>    <<遥控DC-DC模块~CID2=45H>>    关DC-DC模块    None
    Comment    should not be true    ${协议数据}

获取DC-DC模块的版本内容
    ${协议数据}    A接口获取数据    <<获取DC-DC段版本内容~CID1=44H>>    <<获取DC-DC段版本内容~CID2=87H>>    FFH
    判断返回异常结果    ${协议数据}    7
    ${协议数据}    A接口获取数据    <<获取DC-DC段版本内容~CID1=44H>>    <<获取DC-DC段版本内容~CID2=87H>>    01H
    判断返回异常结果    ${协议数据}    7
    ${协议数据}    A接口获取数据    <<获取DC-DC段版本内容~CID1=44H>>    <<获取DC-DC段版本内容~CID2=87H>>    02H
    判断返回异常结果    ${协议数据}    7
    ${协议数据}    A接口获取数据    <<获取DC-DC段版本内容~CID1=44H>>    <<获取DC-DC段版本内容~CID2=87H>>    00H
    判断返回异常结果    ${协议数据}    7
    ${协议数据}    A接口获取数据    <<获取DC-DC段版本内容~CID1=44H>>    <<获取DC-DC段版本内容~CID2=87H>>    FEH
    判断返回异常结果    ${协议数据}    7
    ${协议数据}    A接口获取数据    <<获取DC-DC段版本内容~CID1=44H>>    <<获取DC-DC段版本内容~CID2=87H>>    None
    判断返回异常结果    ${协议数据}    7
