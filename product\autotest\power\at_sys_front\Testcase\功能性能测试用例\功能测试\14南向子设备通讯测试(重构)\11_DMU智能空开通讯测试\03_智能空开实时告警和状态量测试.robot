*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
批量获取智能空开告警量和状态量测试
    写入CSV文档    智能空开数字量和告警量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=智能空开
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SSW排除告警量信号}    ${排除列表}    2
    @{告警列表}    create list
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${dict1}          Create Dictionary
        Set To Dictionary    ${dict1}     name     ${信号名称}
        Append To List       ${告警列表}    ${dict1}
    END
    Run Keyword And Continue On Failure    南向RS485子设备告警量列表产生封装判断结果    DMU_IntelAirSwit    ${告警列表}    呼叫    ${告警产生}    智能空开数字量和告警量获取测试
    Run Keyword And Continue On Failure    南向RS485子设备告警量列表恢复封装判断结果    DMU_IntelAirSwit    ${告警列表}    呼叫    ${告警恢复}    智能空开数字量和告警量获取测试

智能空开通讯断告警测试
    连接CSU
    控制子工具运行停止    DMU_IntelAirSwit    关闭
    FOR    ${i}    IN RANGE    40
        ${temp}    evaluate    1+${i}
        ${temp}    Convert to string    ${temp}
        Wait Until Keyword Succeeds    10m    5    信号量数据值为    智能空开通讯状态-${temp}    异常
    END
    ${级别设置值}    获取web参数量    智能空开通讯断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    智能空开通讯断    严重
    wait until keyword succeeds    5m    2    查询指定告警信息    智能空开通讯断
    控制子工具运行停止    DMU_IntelAirSwit     启动
    FOR    ${i}    IN RANGE    40
        ${temp}    evaluate    1+${i}
        ${temp}    Convert to string    ${temp}
        Wait Until Keyword Succeeds    10m    5    信号量数据值为    智能空开通讯状态-${temp}    正常
    END
    wait until keyword succeeds    10m    2    查询指定告警信息不为    智能空开通讯断
