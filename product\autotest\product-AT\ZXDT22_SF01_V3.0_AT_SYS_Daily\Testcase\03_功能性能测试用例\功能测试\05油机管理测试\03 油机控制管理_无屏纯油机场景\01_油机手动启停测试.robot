*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
手动启动油机测试
    [Documentation]    1、DI7正常，有交流，手动启动失败。
    ...    2、DI7异常，无交流，手动启动成功。
    油机管理初始化
    Wait Until Keyword Succeeds    20    1    设置web控制量    油机开启
    run keyword and ignore error    wait until keyword succeeds    20    1    判断告警不存在    油机异常告警
    Wait Until Keyword Succeeds    4m    1    获取干接点状态直到变化为    ${油机干接点状态}    0
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    运行
    [Teardown]    Run keywords    同时设置三相电压频率    220    50
    ...    AND    打开交流源输出

手动关闭油机测试
    [Documentation]    1、手动启动，手动关闭成功（直流电压大于油机启动电压+0.5）
    ...    2、手动启动，手动关闭关闭失败（直流电压小于油机启动电压+0.5，大于油机启动电压）
    设置电池模拟器模式    铅酸电池    24    5    50
    电池模拟器接触器分合    ON
    打开电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    连接CSU
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流输入场景    纯油机
    ${最短运行时间设置值}    获取web参数量    油机最短运行时间
    ${默认值}    获取web参数上下限范围    油机最短运行时间
    run keyword if    ${最短运行时间设置值} != @{默认值}[0]    Wait Until Keyword Succeeds    10    1    设置web参数量    油机最短运行时间    @{默认值}[0]
    ${最短运行时间设置值}    获取web参数量    油机最短运行时间
    Wait Until Keyword Succeeds    5m    1    设置web控制量    油机关闭
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    停止
    Wait Until Keyword Succeeds    4m    1    获取干接点状态直到变化为    ${油机干接点状态}    1
    关闭交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    [Teardown]    关闭交流源输出
