*** Settings ***
Suite Setup
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_批量获取NFBBMS数字量
    [Documentation]    21min
    写入CSV文档    NFBBMS数字量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=digital data    INDEX_DEVICE_CH=NFBBMS
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除数字量信号}    ${排除列表}    1
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    NFBBMS    digital data    ${FB100B3排除数字量信号}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    ${待测数据长度}    Get Length    ${power_sm待测}
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    1       power_sm
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    power_sm    fb100c2       获取开关量    ${缺省值列表}   NFBBMS数字量获取测试    获取NFBBMS数字量    
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    2       power_sm 
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    power_sm    fb100c2       获取开关量    ${缺省值列表}    NFBBMS数字量获取测试    获取NFBBMS数字量    
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    0        power_sm
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    power_sm    fb100c2       获取开关量    ${缺省值列表}    NFBBMS数字量获取测试    获取NFBBMS数字量    
      

power_sm_0002_BMS工作状态
    [Documentation]    0:无/Null;1:正常/Normal;2:告警/Alarm;3:通讯断/CommFail;4:升级/Update
    ...    3.06.00.00版本取消该量
    [Tags]    3
    连接CSU
    控制子工具运行停止    fb100c2    开启
    @{锂电序号随机list}    从1-15个数中随机选n个不重复的单体    ${北向协议NFBBMS最大数}    4
    FOR    ${锂电序号}    IN    @{锂电序号随机list}
        Wait Until Keyword Succeeds    15m    5    信号量数据值为    BMS工作状态-${锂电序号}    正常
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取NFBBMS数字量    BMS工作状态-${锂电序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    1
    END
    控制子工具运行停止    fb100c2    关闭
    FOR    ${锂电序号}    IN    @{锂电序号随机list}
        Wait Until Keyword Succeeds    15m    5    信号量数据值为    BMS工作状态-${锂电序号}    通讯断
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取NFBBMS数字量    BMS工作状态-${锂电序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    3
    END
    设置子工具值    smartli    all    只读    电池充电过流告警    1
    ${级别设置值}    获取web参数量    <<电池充电过流告警~0x21001030070001>>
    run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    <<电池充电过流告警~0x21001030070001>>    严重
    FOR    ${锂电序号}    IN    @{锂电序号随机list}
        Wait Until Keyword Succeeds    15m    5    信号量数据值为    电池充电过流告警状态-${锂电序号}    异常
        wait until keyword succeeds    15m    2    查询指定告警信息    电池充电过流告警-${锂电序号}
        Wait Until Keyword Succeeds    15m    5    信号量数据值为    BMS工作状态-${锂电序号}    告警
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取NFBBMS数字量    BMS工作状态-${锂电序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    2
    END
    设置子工具值    smartli    all    只读    电池充电过流告警    0
    FOR    ${锂电序号}    IN    @{锂电序号随机list}
        Wait Until Keyword Succeeds    15m    5    信号量数据值为    电池充电过流告警状态-${锂电序号}    正常
        wait until keyword succeeds    15m    2    查询指定告警信息    电池充电过流告警-${锂电序号}
        Wait Until Keyword Succeeds    15m    5    信号量数据值为    BMS工作状态-${锂电序号}    正常
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取NFBBMS数字量    BMS工作状态-${锂电序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    1
    END
    [Teardown]
