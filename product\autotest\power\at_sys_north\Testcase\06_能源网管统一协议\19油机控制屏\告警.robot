*** Settings ***
Suite Setup       主动告警测试前置条件    ${CSU_role}
Suite Teardown    设置web设备参数量为默认值    CSU主动告警使能
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_油机控制屏通讯中断1
    连接CSU
    ${级别设置值}    获取web参数量    油机控制屏通讯中断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机控制屏通讯中断    主要
    ${告警级别取值约定dict}    获取web参数的取值约定    油机控制屏通讯中断
    ${告警级别}    获取web参数量    油机控制屏通讯中断
    ${告警不存在}    判断告警存在_带返回值    油机控制屏通讯中断
    should not be true    ${告警不存在}
    控制子工具运行停止    oileng    关闭
    wait until keyword succeeds    5m    2    查询指定告警信息    油机控制屏通讯中断-1
    ${web实时告警名}    由子工具获取web实时告警名称    油机控制屏通讯中断-1
    Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client'    主动告警上送判断2    power_sm    油机控制屏    ${web实时告警名}
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机控制屏通讯中断-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${告警级别}
    控制子工具运行停止    oileng    开启
    wait until keyword succeeds    5m    2    查询指定告警信息不为    油机控制屏通讯中断
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机控制屏通讯中断-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
    ...    AND    控制子工具运行停止    oileng    开启

power_sm_0002_油机合闸失败1
    连接CSU
    ${级别设置值}    获取web参数量    油机合闸失败
    ${告警级别取值约定dict}    获取web参数的取值约定    油机合闸失败
    ${告警级别}    获取web参数量    油机合闸失败
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机合闸失败    主要
    设置子工具值    oileng    all    告警    备用9    2
    wait until keyword succeeds    5m    5    查询指定告警信息    油机合闸失败-1
    ${web实时告警名}    由子工具获取web实时告警名称    油机合闸失败-1
    Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client'    主动告警上送判断2    power_sm    油机控制屏    ${web实时告警名}
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机合闸失败-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${告警级别}
    设置子工具值    oileng    all    告警    备用9    0
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机合闸失败
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机合闸失败-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
    ...    AND    设置子工具值    oileng    all    告警    备用9    0

power_sm_0003_油机分闸失败2
    连接CSU
    ${级别设置值}    获取web参数量    油机分闸失败
    ${告警级别取值约定dict}    获取web参数的取值约定    油机分闸失败
    ${告警级别}    获取web参数量    油机分闸失败
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机分闸失败    主要
    ${告警不存在}    判断告警存在_带返回值    油机分闸失败
    should not be true    ${告警不存在}
    设置子工具值    oileng    all    告警    预报警代码    28
    设置子工具值    oileng    all    告警    报警代码    28
    wait until keyword succeeds    10m    2    查询指定告警信息    油机分闸失败-1
    ${web实时告警名}    由子工具获取web实时告警名称    油机分闸失败-1
    Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client'    主动告警上送判断2    power_sm    油机控制屏    ${web实时告警名}
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机分闸失败-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${告警级别}
    设置子工具值    oileng    all    告警    预报警代码    0
    设置子工具值    oileng    all    告警    报警代码    0
    wait until keyword succeeds    10m    2    查询指定告警信息不为    油机分闸失败
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    油机分闸失败-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    设置子工具值    oileng    all    告警    预报警代码    0
    ...    AND    设置子工具值    oileng    all    告警    报警代码    0
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0004_市电分闸失败3
    连接CSU
    ${级别设置值}    获取web参数量    市电分闸失败
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    市电分闸失败    主要
    ${告警级别取值约定dict}    获取web参数的取值约定    市电分闸失败
    ${告警级别}    获取web参数量    市电分闸失败
    ${告警不存在}    判断告警存在_带返回值    市电分闸失败
    should not be true    ${告警不存在}
    设置子工具值    oileng    all    告警    预报警代码    30
    设置子工具值    oileng    all    告警    报警代码    30
    wait until keyword succeeds    5m    2    查询指定告警信息    市电分闸失败-1
    ${web实时告警名}    由子工具获取web实时告警名称    市电分闸失败-1
    Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client'    主动告警上送判断2    power_sm    油机控制屏    ${web实时告警名}
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    市电分闸失败-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${告警级别}
    设置子工具值    oileng    all    告警    预报警代码    0
    设置子工具值    oileng    all    告警    报警代码    0
    wait until keyword succeeds    5m    2    查询指定告警信息不为    市电分闸失败
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    市电分闸失败-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    设置子工具值    oileng    all    告警    预报警代码    0
    ...    AND    设置子工具值    oileng    all    告警    报警代码    0
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0005_市电合闸失败4
    连接CSU
    ${级别设置值}    获取web参数量    市电合闸失败
    ${告警级别取值约定dict}    获取web参数的取值约定    市电合闸失败
    ${告警级别}    获取web参数量    市电合闸失败
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    市电合闸失败    主要
    ${告警不存在}    判断告警存在_带返回值    市电合闸失败
    should not be true    ${告警不存在}
    设置子工具值    oileng    all    告警    备用9    1
    wait until keyword succeeds    5m    2    查询指定告警信息    市电合闸失败-1
    ${web实时告警名}    由子工具获取web实时告警名称    市电合闸失败-1
    Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client'    主动告警上送判断2    power_sm    油机控制屏    ${web实时告警名}
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    市电合闸失败-1    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${告警级别}
    设置子工具值    oileng    all    告警    备用9    0
    wait until keyword succeeds    5m    2    查询指定告警信息不为    市电合闸失败
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    市电合闸失败-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
    ...    AND    设置子工具值    oileng    all    告警    备用9    0
