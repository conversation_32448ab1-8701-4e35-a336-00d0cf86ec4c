*** Settings ***
Suite Setup       测试用例前置条件
Suite Teardown    测试用例后置条件
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
单个历史事件产生的新增序号测试（操作记录）
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    ${历史事件数量}    获取web历史事件数量    ${empty}    ${empty}    所有
    run keyword if    ${历史事件数量}>1900    删除历史记录    操作记录
    ${系统时间1}    获取系统时间
    ${起始时间}    subtract time from date    ${系统时间1}    30d    exclude_millis=yes    #30天以前的时间
    ${终止时间1}    获取系统时间
    ${历史事件数量1}    获取web历史事件数量    ${起始时间}    ${终止时间1}    所有
    log    ${历史事件数量1}
    ${操作记录数量1}    获取web历史事件数量    ${起始时间}    ${终止时间1}    200
    log    ${操作记录数量1}
    #操作记录
    ${可设置范围}    获取web参数可设置范围    电池组容量_1
    Wait Until Keyword Succeeds    2m    2    设置web参数量    电池组容量_1    ${可设置范围}[0]
    sleep    2
    ${终止时间2}    获取系统时间
    Comment    ${历史事件数量2}    获取web历史事件数量    ${起始时间}    ${终止时间2}    所有
    Comment    should be true    ${历史事件数量2}==${历史事件数量1}+1
    ${操作记录数量2}    获取web历史事件数量    ${起始时间}    ${终止时间2}    200
    should be true    ${操作记录数量2}==${操作记录数量1}+1
    #产生电池浮充历史事件
    设置web参数量    电池组容量_1    ${可设置范围}[1]
    sleep    2
    ${终止时间3}    获取系统时间
    Comment    ${历史事件数量3}    获取web历史事件数量    ${起始时间}    ${终止时间3}    所有
    Comment    should be true    ${历史事件数量3}==${历史事件数量2}+1
    ${操作记录数量3}    获取web历史事件数量    ${起始时间}    ${终止时间3}    200
    should be true    ${操作记录数量3}==${操作记录数量2}+1
    #产生整流器风扇调速禁止历史事件
    Wait Until Keyword Succeeds    2m    2    设置web控制量    整流器风扇调速禁止-1
    sleep    5
    ${终止时间4}    获取系统时间
    Comment    ${历史事件数量4}    获取web历史事件数量    ${起始时间}    ${终止时间4}    所有
    Comment    should be true    ${历史事件数量4}==${历史事件数量3}+1
    ${操作记录数量4}    获取web历史事件数量    ${起始时间}    ${终止时间4}    200
    should be true    ${操作记录数量4}==${操作记录数量3}+1
    #产生整流器风扇调速允许历史事件
    Wait Until Keyword Succeeds    2m    2    设置web控制量    整流器风扇调速允许-1
    sleep    5
    ${终止时间5}    获取系统时间
    Comment    ${历史事件数量5}    获取web历史事件数量    ${起始时间}    ${终止时间5}    所有
    Comment    should be true    ${历史事件数量5}==${历史事件数量4}+1
    ${操作记录数量5}    获取web历史事件数量    ${起始时间}    ${终止时间5}    200
    should be true    ${操作记录数量5}==${操作记录数量4}+1
    [Teardown]    设置web设备参数量为默认值    电池组容量_1

单个历史事件产生的新增序号测试（运行记录）
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    ${历史事件数量}    获取web历史事件数量    ${empty}    ${empty}    所有
    log    ${empty}
    run keyword if    ${历史事件数量}>1900    删除历史记录    操作记录
    ${系统时间1}    获取系统时间
    ${起始时间}    subtract time from date    ${系统时间1}    30d    exclude_millis=yes    #30天以前的时间
    ${终止时间1}    获取系统时间
    ${历史事件数量1}    获取web历史事件数量    ${起始时间}    ${终止时间1}    所有
    log    ${历史事件数量1}
    ${运行记录数量1}    获取web历史事件数量    ${起始时间}    ${终止时间1}    运行记录
    log    ${运行记录数量1}
    #产生系统复位掉电历史事件    #运行记录
    测试台上下电操作    ${测试台编号}    OFF
    sleep    5
    测试台上下电操作    ${测试台编号}    ON
    连接CSU
    实时告警刷新完成
    ${终止时间2}    获取系统时间
    sleep    10
    ${历史事件数量2}    获取web历史事件数量    ${起始时间}    ${终止时间2}    所有
    should be true     ${历史事件数量2}>=${历史事件数量1}+3     #掉电1次、登录2次，共3次
    ${运行记录数量2}    获取web历史事件数量    ${起始时间}    ${终止时间2}    运行记录
    should be true    ${运行记录数量2}==${运行记录数量1}+1
    [Teardown]    打开交流源输出

单个历史事件产生的新增序号测试（安全事件）
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    ${历史事件数量}    获取web历史事件数量    ${empty}    ${empty}    所有
    run keyword if    ${历史事件数量}>1900    删除历史记录    操作记录
    ${系统时间1}    获取系统时间
    ${起始时间}    subtract time from date    ${系统时间1}    30d    exclude_millis=yes    #30天以前的时间
    ${终止时间1}    获取系统时间
    ${历史事件数量1}    获取web历史事件数量    ${起始时间}    ${终止时间1}    所有
    log    ${历史事件数量1}
    ${安全事件数量1}    获取web历史事件数量    ${起始时间}    ${终止时间1}    安全事件
    log    ${安全事件数量1}
    #产生WEB登录历史事件    #安全事件
    连接CSU
    sleep    2
    ${终止时间2}    获取系统时间
    ${历史事件数量2}    获取web历史事件数量    ${起始时间}    ${终止时间2}    所有
    should be true    ${历史事件数量2}==${历史事件数量1}+1    #登录1次
    ${安全事件数量2}    获取web历史事件数量    ${起始时间}    ${终止时间2}    安全事件
    should be true    ${安全事件数量2}==${安全事件数量1}+1    #登录1次

多个历史事件产生的新增序号测试（操作记录）
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    ${历史事件数量}    获取web历史事件数量    ${empty}    ${empty}    所有
    run keyword if    ${历史事件数量}>1900    删除历史记录    操作记录
    ${系统时间1}    获取系统时间
    ${起始时间}    subtract time from date    ${系统时间1}    30d    exclude_millis=yes    #30天以前的时间
    ${终止时间1}    获取系统时间
    ${历史事件数量1}    获取web历史事件数量    ${起始时间}    ${终止时间1}    所有
    log    ${历史事件数量1}
    ${操作记录数量1}    获取web历史事件数量    ${起始时间}    ${终止时间1}    200
    log    ${操作记录数量1}
    #产生电池组容量设置历史事件    #操作记录
    ${可设置范围}    获取web参数可设置范围    电池组容量_1
    Wait Until Keyword Succeeds    2m    2    设置web参数量    电池组容量_1    ${可设置范围}[0]
    sleep    2
    #产生电池组容量设置历史事件
    Wait Until Keyword Succeeds    2m    2    设置web参数量    电池组容量_1    ${可设置范围}[1]
    #产生整流器风扇调速禁止历史事件
    Wait Until Keyword Succeeds    2m    2    设置web控制量    整流器风扇调速禁止-1
    sleep    5
    #产生整流器风扇调速允许历史事件
    Wait Until Keyword Succeeds    2m    2    设置web控制量    整流器风扇调速允许-1
    sleep    5
    ${终止时间2}    获取系统时间
    Comment    ${历史事件数量2}    获取web历史事件数量    ${起始时间}    ${终止时间2}    所有
    Comment    should be true    ${历史事件数量2}==${历史事件数量1}+4
    ${操作记录数量2}    获取web历史事件数量    ${起始时间}    ${终止时间2}    200
    should be true    ${操作记录数量2}==${操作记录数量1}+4
    [Teardown]    设置web设备参数量为默认值    电池组容量_1

多个历史事件产生的新增序号测试（所有）
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    ${历史事件数量}    获取web历史事件数量    ${empty}    ${empty}    所有
    run keyword if    ${历史事件数量}>1900    删除历史记录    操作记录
    ${系统时间1}    获取系统时间
    ${起始时间}    subtract time from date    ${系统时间1}    30d    exclude_millis=yes    #30天以前的时间
    ${终止时间1}    获取系统时间
    ${历史事件数量1}    获取web历史事件数量    ${起始时间}    ${终止时间1}    所有
    log    ${历史事件数量1}
    ${操作记录数量1}    获取web历史事件数量    ${起始时间}    ${终止时间1}    200
    log    ${操作记录数量1}
    ${运行记录数量1}    获取web历史事件数量    ${起始时间}    ${终止时间1}    运行记录
    log    ${运行记录数量1}
    ${安全事件数量1}    获取web历史事件数量    ${起始时间}    ${终止时间1}    安全事件
    log    ${安全事件数量1}
    #产生电池均充历史事件    #操作记录
    ${可设置范围}    获取web参数可设置范围    系统过载告警阈值
    Wait Until Keyword Succeeds    2m    2    设置web参数量    系统过载告警阈值    ${可设置范围}[0]
    sleep    2
    #产生系统复位掉电历史事件    #运行记录
    测试台上下电操作    ${测试台编号}    OFF
    sleep    5
    测试台上下电操作    ${测试台编号}    ON
    实时告警刷新完成
    sleep    10
    连接CSU
    #产生WEB登录历史事件    #安全事件
    sleep    20
    ${终止时间2}    获取系统时间
    ${历史事件数量2}    获取web历史事件数量    ${起始时间}    ${终止时间2}    所有
    should be true     ${历史事件数量2}>=${历史事件数量1}+4      #登录2+掉电1+修改参数1
    ${操作记录数量2}    获取web历史事件数量    ${起始时间}    ${终止时间2}    200
    should be true    ${操作记录数量2}==${操作记录数量1}+1
    ${运行记录数量2}    获取web历史事件数量    ${起始时间}    ${终止时间2}    运行记录
    should be true    ${运行记录数量2}==${运行记录数量1}+1    #掉电1次
    ${安全事件数量2}    获取web历史事件数量    ${起始时间}    ${终止时间2}    安全事件
    should be true    ${安全事件数量1}+2<=${安全事件数量2}==${安全事件数量1}+3    #登录2次
    [Teardown]    Run keywords    设置web设备参数量为默认值    系统过载告警阈值
    ...    AND    打开交流源输出

获取最新历史事件的内容测试（设置参数）
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    ${历史事件数量}    获取web历史事件数量    ${empty}    ${empty}    所有
    run keyword if    ${历史事件数量}>1900    删除历史记录    操作记录
    ${起始时间}    获取系统时间
    sleep    10
    ${终止时间1}    获取系统时间
    ${历史事件数量1}    获取web历史事件数量    ${起始时间}    ${终止时间1}    所有
    should be true    ${历史事件数量1}==0
    ${操作记录数量1}    获取web历史事件数量    ${起始时间}    ${终止时间1}    200
    should be true    ${操作记录数量1}==0
    #产生电池均充历史事件    #操作记录
    ${可设置范围}    获取web参数可设置范围    电池组容量_1
    Wait Until Keyword Succeeds    2m    2    设置web参数量    电池组容量_1    ${可设置范围}[0]
    sleep    2
    ${终止时间2}    获取系统时间
    ${历史事件数量2}    获取web历史事件数量    ${起始时间}    ${终止时间2}    所有
    should be true    ${历史事件数量2}==${历史事件数量1}+1
    @{历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间2}    所有    1    10
    Comment    @{历史事件内容1}    split string    ${历史事件内容1}[-1]    ,
    Comment    log    ${历史事件内容1}[3]
    should contain    @{历史事件内容1}    电池组容量
    ${操作记录数量2}    获取web历史事件数量    ${起始时间}    ${终止时间2}    200
    should be true    ${操作记录数量2}==${操作记录数量1}+1
    @{操作记录内容1}    获取web历史事件内容    ${起始时间}    ${终止时间2}    200    1    10
    @{操作记录内容1}    split string    ${操作记录内容1}[-1]    ,
    log    ${操作记录内容1}[3]
    should contain    ${操作记录内容1}[3]    电池组容量
    #产生电池浮充历史事件
    Wait Until Keyword Succeeds    2m    2    设置web参数量    电池组容量_1    ${可设置范围}[1]
    sleep    2
    ${终止时间3}    获取系统时间
    ${历史事件数量3}    获取web历史事件数量    ${起始时间}    ${终止时间3}    所有
    should be true    ${历史事件数量3}==${历史事件数量2}+1
    @{历史事件内容2}    获取web历史事件内容    ${起始时间}    ${终止时间3}    所有    1    10
    @{历史事件内容2}    split string    ${历史事件内容2}[-1]    ,
    log    ${历史事件内容2}[3]
    should contain    ${历史事件内容2}[3]    电池组容量
    ${操作记录数量3}    获取web历史事件数量    ${起始时间}    ${终止时间3}    200
    should be true    ${操作记录数量3}==${操作记录数量2}+1
    @{操作记录内容2}    获取web历史事件内容    ${起始时间}    ${终止时间3}    200    1    10
    @{操作记录内容2}    split string    ${操作记录内容2}[-1]    ,
    log    ${操作记录内容2}[3]
    should contain    ${操作记录内容2}[3]    电池组容量
    [Teardown]    设置web设备参数量为默认值    电池组容量_1

获取最新历史事件的内容测试（整流器风扇调速）
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    ${历史事件数量}    获取web历史事件数量    ${empty}    ${empty}    所有
    run keyword if    ${历史事件数量}>1900    删除历史记录    操作记录
    ${起始时间}    获取系统时间
    sleep    10
    ${终止时间1}    获取系统时间
    ${历史事件数量1}    获取web历史事件数量    ${起始时间}    ${终止时间1}    所有
    log    ${历史事件数量1}
    should be true    ${历史事件数量1}==0
    ${操作记录数量1}    获取web历史事件数量    ${起始时间}    ${终止时间1}    200
    log    ${操作记录数量1}
    should be true    ${操作记录数量1}==0
    #产生整流器风扇调速禁止历史事件    #操作记录
    Wait Until Keyword Succeeds    2m    2    设置web控制量    整流器风扇调速禁止-1
    sleep    5
    ${终止时间2}    获取系统时间
    ${历史事件数量2}    获取web历史事件数量    ${起始时间}    ${终止时间2}    所有
    should be true    ${历史事件数量2}==${历史事件数量1}+1
    @{历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间2}    所有    1    10
    should contain    ${历史事件内容1}[-1]    整流器风扇调速禁止
    ${操作记录数量2}    获取web历史事件数量    ${起始时间}    ${终止时间2}    200
    should be true    ${操作记录数量2}==${操作记录数量1}+1
    @{操作记录内容1}    获取web历史事件内容    ${起始时间}    ${终止时间2}    200    1    10
    should contain    ${操作记录内容1}[-1]    整流器风扇调速禁止
    #产生整流器风扇调速允许历史事件
    Wait Until Keyword Succeeds    2m    2    设置web控制量    整流器风扇调速允许-1
    sleep    5
    ${终止时间3}    获取系统时间
    ${历史事件数量3}    获取web历史事件数量    ${起始时间}    ${终止时间3}    所有
    should be true    ${历史事件数量3}==${历史事件数量2}+1
    @{历史事件内容2}    获取web历史事件内容    ${起始时间}    ${终止时间3}    所有    1    10
    should contain    ${历史事件内容2}[-1]    整流器风扇调速允许
    ${操作记录数量3}    获取web历史事件数量    ${起始时间}    ${终止时间3}    200
    should be true    ${操作记录数量3}==${操作记录数量2}+1
    @{操作记录内容2}    获取web历史事件内容    ${起始时间}    ${终止时间3}    200    1    10
    should contain    ${操作记录内容2}[-1]    整流器风扇调速允许
    [Teardown]    设置web控制量    整流器风扇调速允许-1

获取最新历史事件的内容测试（设置系统过载告警阈值）
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    ${历史事件数量}    获取web历史事件数量    ${empty}    ${empty}    所有
    run keyword if    ${历史事件数量}>1900    删除历史记录    操作记录
    Wait Until Keyword Succeeds    2m    2    设置web参数量    系统过载告警阈值    80
    sleep    2
    ${起始时间}    获取系统时间
    sleep    10
    ${终止时间1}    获取系统时间
    ${历史事件数量1}    获取web历史事件数量    ${起始时间}    ${终止时间1}    所有
    should be true    ${历史事件数量1}==0
    ${操作记录数量1}    获取web历史事件数量    ${起始时间}    ${终止时间1}    200
    should be true    ${操作记录数量1}==0
    #产生设置系统过载告警阈值历史事件    #操作记录
    设置web参数量    系统过载告警阈值    90
    sleep    5
    ${终止时间2}    获取系统时间
    ${历史事件数量2}    获取web历史事件数量    ${起始时间}    ${终止时间2}    所有
    should be true    ${历史事件数量2}==${历史事件数量1}+1
    @{历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间2}    所有    1    10
    should contain    ${历史事件内容1}[-1]    系统过载告警阈值
    ${操作记录数量2}    获取web历史事件数量    ${起始时间}    ${终止时间2}    200
    should be true    ${操作记录数量2}    ${操作记录数量1}+1
    @{操作记录内容1}    获取web历史事件内容    ${起始时间}    ${终止时间2}    200    1    10
    should contain    ${操作记录内容1}[-1]    系统过载告警阈值
    [Teardown]    设置web设备参数量为默认值    系统过载告警阈值

获取最新历史事件的内容测试（导出配置参数）
    
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    ${历史事件数量}    获取web历史事件数量    ${empty}    ${empty}    所有
    run keyword if    ${历史事件数量}>1900    删除历史记录    操作记录
    ${起始时间}    获取系统时间
    sleep    10
    ${终止时间1}    获取系统时间
    ${历史事件数量1}    获取web历史事件数量    ${起始时间}    ${终止时间1}    所有
    should be true    ${历史事件数量1}==0
    ${操作记录数量1}    获取web历史事件数量    ${起始时间}    ${终止时间1}    200
    should be true    ${操作记录数量1}==0    0
    #产生导出配置参数历史事件    #操作记录
    ${导出路径}    导出参数文件
    sleep    10
    ${终止时间2}    获取系统时间
    ${历史事件数量2}    获取web历史事件数量    ${起始时间}    ${终止时间2}    所有
    should be true    ${历史事件数量2}==${历史事件数量1}+1
    @{历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间2}    所有    1    10
    should contain    ${历史事件内容1}[-1]    导出 配置和参数成功
    ${操作记录数量2}    获取web历史事件数量    ${起始时间}    ${终止时间2}    200
    should be true    ${操作记录数量2}==${操作记录数量1}+1
    @{操作记录内容1}    获取web历史事件内容    ${起始时间}    ${终止时间2}    200    1    10
    should contain    ${操作记录内容1}[-1]    导出 配置和参数成功
    关闭负载输出

获取最新历史事件的内容测试（系统复位 掉电）
    
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    ${历史事件数量}    获取web历史事件数量    ${empty}    ${empty}    所有
    run keyword if    ${历史事件数量}>1900    删除历史记录    操作记录
    sleep    20
    ${起始时间}    获取系统时间
    sleep    10
    ${终止时间1}    获取系统时间
    ${历史事件数量1}    获取web历史事件数量    ${起始时间}    ${终止时间1}    所有
    should be true    ${历史事件数量1}==0    0
    ${运行记录数量1}    获取web历史事件数量    ${起始时间}    ${终止时间1}    运行记录
    should be true    ${运行记录数量1}==0    0
    #产生系统复位 掉电历史事件    #运行记录
    测试台上下电操作    ${测试台编号}    OFF
    sleep    5
    测试台上下电操作    ${测试台编号}    ON
    实时告警刷新完成
    Comment    连接CSU
    ${终止时间2}    获取系统时间
    ${历史事件数量2}    获取web历史事件数量    ${起始时间}    ${终止时间2}    所有
    should be true    ${历史事件数量2}>=${历史事件数量1}+2    #掉电1次、登录1次
    @{历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间2}    所有    1    10
    should contain    ${历史事件内容1}[-1]    Web登录 成功
    ${运行记录数量2}    获取web历史事件数量    ${起始时间}    ${终止时间2}    运行记录
    should be true    ${运行记录数量2}==${运行记录数量1}+1
    @{运行记录内容1}    获取web历史事件内容    ${起始时间}    ${终止时间2}    运行记录    1    10
    should contain    ${运行记录内容1}[-1]    系统复位 掉电
    [Teardown]    打开交流源输出

获取最新历史事件的内容测试（web登录 成功）
    
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    ${历史事件数量}    获取web历史事件数量    ${empty}    ${empty}    所有
    run keyword if    ${历史事件数量}>1900    删除历史记录    操作记录
    sleep    20
    ${起始时间}    获取系统时间
    sleep    20
    ${终止时间1}    获取系统时间
    ${历史事件数量1}    获取web历史事件数量    ${起始时间}    ${终止时间1}    所有
    should be true    ${历史事件数量1}==0    0
    ${安全事件数量1}    获取web历史事件数量    ${起始时间}    ${终止时间1}    安全事件
    should be true    ${安全事件数量1}==0    0
    #产生WEB登录历史事件    #安全事件
    连接CSU
    sleep    5
    ${终止时间2}    获取系统时间
    ${历史事件数量2}    获取web历史事件数量    ${起始时间}    ${终止时间2}    所有
    should be true    ${历史事件数量2}==${历史事件数量1}+1
    @{历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间2}    所有    1    10
    should contain    ${历史事件内容1}[-1]    Web登录 成功
    ${安全事件数量2}    获取web历史事件数量    ${起始时间}    ${终止时间2}    安全事件
    should be true    ${安全事件数量2}==${安全事件数量1}+1
    @{安全事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间2}    安全事件    1    10
    should contain    ${安全事件内容1}[-1]    Web登录 成功

系统断电历史事件保存测试
    [Documentation]    200：操作日志，因为此处变更过名称，所以直接改为写代码。
    
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    ${历史事件数量}    获取web历史事件数量    ${empty}    ${empty}    所有
    run keyword if    ${历史事件数量}>1900    删除历史记录    操作记录
    ${系统时间1}    获取系统时间
    ${起始时间}    subtract time from date    ${系统时间1}    30d    exclude_millis=yes    #30天以前的时间
    ${终止时间1}    获取系统时间
    ${历史事件数量1}    获取web历史事件数量    ${起始时间}    ${终止时间1}    所有
    log    ${历史事件数量1}
    ${操作记录数量1}    获取web历史事件数量    ${起始时间}    ${终止时间1}    200
    log    ${操作记录数量1}
    ${运行记录数量1}    获取web历史事件数量    ${起始时间}    ${终止时间1}    运行记录
    log    ${运行记录数量1}
    ${安全事件数量1}    获取web历史事件数量    ${起始时间}    ${终止时间1}    安全事件
    log    ${安全事件数量1}
    #产生系统复位 掉电历史事件
    测试台上下电操作    ${测试台编号}    OFF
    sleep    5
    测试台上下电操作    ${测试台编号}    ON
    实时告警刷新完成
    ${终止时间2}    获取系统时间
    ${历史事件数量2}    获取web历史事件数量    ${起始时间}    ${终止时间2}    所有
    should be true    ${历史事件数量1}+1<=${历史事件数量2}<=${历史事件数量1}+4
    ${操作记录数量2}    获取web历史事件数量    ${起始时间}    ${终止时间2}    200
    should be true    ${操作记录数量2}==${操作记录数量1}
    ${运行记录数量2}    获取web历史事件数量    ${起始时间}    ${终止时间2}    运行记录
    should be true    ${运行记录数量2}==${运行记录数量1}+1
    ${安全事件数量2}    获取web历史事件数量    ${起始时间}    ${终止时间2}    安全事件
    should be true    ${安全事件数量1}+1<=${安全事件数量2}<=${安全事件数量1}+2
    

SSH打开和关闭保存操作记录测试
    [Setup]
    实时告警刷新完成
    ${历史事件数量}    获取web历史事件数量    ${empty}    ${empty}    所有
    run keyword if    ${历史事件数量}>1900    删除历史记录    操作记录
    ${系统时间1}    获取系统时间
    ${起始时间}    subtract time from date    ${系统时间1}    30d    exclude_millis=yes    #30天以前的时间
    ${终止时间1}    获取系统时间
    ${历史事件数量1}    获取web历史事件数量    ${起始时间}    ${终止时间1}    所有
    log    ${历史事件数量1}
    ${操作日志数量1}    获取web历史事件数量    ${起始时间}    ${终止时间1}    操作日志
    log    ${操作日志数量1}
    wait until keyword succeeds    10    2    设置web控制量    开启SSH
    ${终止时间2}    获取系统时间
    ${历史事件数量2}    获取web历史事件数量    ${起始时间}    ${终止时间2}    所有
    should be true    ${历史事件数量2}==${历史事件数量1}+1    #操作记录1次
    ${操作日志数量2}    获取web历史事件数量    ${起始时间}    ${终止时间2}    操作日志
    should be true    ${操作日志数量2} ==${操作日志数量1}+1    #操作记录1次
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间2}    操作日志    1    10
    should contain match    ${历史事件内容1}    *开启SSH*
    wait until keyword succeeds    10    2    设置web控制量    关闭SSH
    ${终止时间3}    获取系统时间
    ${历史事件数量3}    获取web历史事件数量    ${起始时间}    ${终止时间3}    所有
    should be true    ${历史事件数量3}==${历史事件数量2}+1    #操作记录1次
    ${操作日志数量3}    获取web历史事件数量    ${起始时间}    ${终止时间3}    操作日志
    should be true    ${操作日志数量3}==${操作日志数量2}+1     #操作记录1次
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间3}    操作日志    1    10
    should contain match    ${历史事件内容1}    *关闭SSH*

禁止所有告警操作记录测试
    [Setup]
    实时告警刷新完成
    ${历史事件数量}    获取web历史事件数量    ${empty}    ${empty}    所有
    run keyword if    ${历史事件数量}>1900    删除历史记录    操作记录
    ${系统时间1}    获取系统时间
    ${起始时间}    subtract time from date    ${系统时间1}    30d    exclude_millis=yes    #30天以前的时间
    设置web参数量    环境湿度无效    次要
    设置web参数量    <<禁止所有告警~0x1001030010001>>    严重
    ${终止时间1}    获取系统时间
    ${历史事件数量1}    获取web历史事件数量    ${起始时间}    ${终止时间1}    所有
    log    ${历史事件数量1}
    ${操作日志数量1}    获取web历史事件数量    ${起始时间}    ${终止时间1}    操作日志
    log    ${操作日志数量1}
    设置通道无效值/恢复通道原始值    ${plat.humity}    1
    wait until keyword succeeds    1m    1    查询指定告警信息    环境湿度无效
    wait until keyword succeeds    10    2    设置web控制量    <<禁止所有告警~0x1001040010001>>
    Wait Until Keyword Succeeds    1m    1    判断告警不存在    环境湿度无效
    wait until keyword succeeds    1m    1    查询指定告警信息    禁止所有告警
    ${终止时间2}    获取系统时间
    ${历史事件数量2}    获取web历史事件数量    ${起始时间}    ${终止时间2}    所有
    should be true    ${历史事件数量2}>=${历史事件数量1}+1    #操作记录1次
    ${操作日志数量2}    获取web历史事件数量    ${起始时间}    ${终止时间2}    操作日志
    should be true    ${操作日志数量2} ==${操作日志数量1}+1    #操作记录1次
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间2}    操作日志    1    10
    should contain match    ${历史事件内容1}    *禁止所有告警*
    wait until keyword succeeds    10    2    设置web控制量    允许所有告警
    ${终止时间3}    获取系统时间
    ${历史事件数量3}    获取web历史事件数量    ${起始时间}    ${终止时间3}    所有
    should be true    ${历史事件数量3}==${历史事件数量2}+1    #操作记录1次
    ${操作日志数量3}    获取web历史事件数量    ${起始时间}    ${终止时间3}    操作日志
    should be true    ${操作日志数量3}==${操作日志数量2}+1     #操作记录1次
    ${历史事件内容1}    获取web历史事件内容    ${起始时间}    ${终止时间3}    操作日志    1    10
    should contain match    ${历史事件内容1}    *允许所有告警*
    wait until keyword succeeds    1m    1    查询指定告警信息    环境湿度无效
    Wait Until Keyword Succeeds    1m    1    判断告警不存在    禁止所有告警
        设置通道无效值/恢复通道原始值    ${plat.humity}    0
    [Teardown]    Run keywords    设置web参数量    环境湿度无效    屏蔽
    ...    AND    wait until keyword succeeds    10    2    设置web控制量    允许所有告警




