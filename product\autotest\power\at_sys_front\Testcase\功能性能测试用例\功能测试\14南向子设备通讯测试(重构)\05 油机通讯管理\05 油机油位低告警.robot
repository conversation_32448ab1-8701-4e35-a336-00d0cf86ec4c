*** Settings ***
Suite Setup       液位传感器测试前置条件    #液位传感器测试前置条件
Suite Teardown    液位传感器测试结束条件    #液位传感器测试结束条件
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
油机油位低告警
    [Setup]    判断web参数是否存在    油机油位低告警阈值
    连接CSU
    控制子工具运行停止    llSensor    关闭
    wait until keyword succeeds    10m    1    判断告警存在    液位传感器通讯中断
    控制子工具运行停止    llSensor    开启
    wait until keyword succeeds    10m    1    判断告警不存在    液位传感器通讯中断
    Wait Until Keyword Succeeds    10    2    设置web参数量    油箱形状-1    立方体
    wait until keyword succeeds    2m    1    判断告警存在    油机油位低告警
    
    Wait Until Keyword Succeeds    10    2    设置web参数量    油机油位低告警    屏蔽
    sleep    5
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    油机油位低告警
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    油机油位低告警    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    油机油位低告警
        sleep    3
        ${油机油位低告警告警级别}    获取web告警属性    油机油位低告警-1    告警级别
        should be equal    ${告警级别设置}    ${油机油位低告警告警级别}
    END

    Wait Until Keyword Succeeds    10    2    设置web参数量    油箱形状-1    未配置
    [Teardown]    控制子工具运行停止    llSensor    开启