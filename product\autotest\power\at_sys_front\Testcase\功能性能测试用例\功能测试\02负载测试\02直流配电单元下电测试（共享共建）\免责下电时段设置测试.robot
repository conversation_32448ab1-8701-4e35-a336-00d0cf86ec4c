*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
免责下电时段设置测试
    # ${免责下电使能}    获取web参数量    <<配电单元免责下电使能_1~0xa0010502e0001>>
    # 设置web参数量    <<配电单元免责下电使能_1~0xa0010502e0001>>    允许
    # FOR    ${val}    IN RANGE    1    6
    #     ${起始时刻}    Catenate    SEPARATOR=    <<配电单元免责下电起始时刻${val}_    1    ~0xa0010502f0001>>
    #     ${终止时刻}    Catenate    SEPARATOR=    <<配电单元免责下电终止时刻${val}_    1    ~0xa001050300001>>
    #     设置web参数量    ${终止时刻}    23:58
    #     设置web参数量    ${起始时刻}    23:59
    # END
    # ${免责下电使能}    获取web参数量    <<配电单元免责下电使能_2~0xa0010502e0001>>
    # 设置web参数量    <<配电单元免责下电使能_2~0xa0010502e0001>>    允许
    # FOR    ${val}    IN RANGE    1    6
    #     ${起始时刻}    Catenate    SEPARATOR=    <<配电单元免责下电起始时刻${val}_    2    ~0xa001050310001>>
    #     ${终止时刻}    Catenate    SEPARATOR=    <<配电单元免责下电终止时刻${val}_    2    ~0xa001050320001>>
    #     设置web参数量    ${终止时刻}    23:58
    #     设置web参数量    ${起始时刻}    23:59
    # END
    # #[Teardown]    设置web设备参数量为默认值    <<配电单元免责下电使能_1~0xa0010502e0001>>    <<配电单元免责下电使能_2~0xa0010502e0001>>
    ${免责下电使能}    获取web参数量    <<配电单元免责下电使能_1~0xa0010502e0001>>
    设置web参数量    <<配电单元免责下电使能_1~0xa0010502e0001>>    允许
    FOR    ${val}    IN RANGE    1    6
        ${起始时刻}    Catenate    SEPARATOR=    <<配电单元免责下电起始时刻${val}_    1    ~0xa0010502f0001>>
        ${终止时刻}    Catenate    SEPARATOR=    <<配电单元免责下电终止时刻${val}_    1    ~0xa001050300001>>
        设置web参数量    ${起始时刻}    23:59
        设置web参数量    ${终止时刻}    23:58
        sleep    10
        ${起始时刻值}    获取web参数量    ${起始时刻}
        ${终止时刻值}    获取web参数量    ${终止时刻}
        should not be equal as strings    ${终止时刻值}    23:58
    END
    ${免责下电使能}    获取web参数量    <<配电单元免责下电使能_2~0xa0010502e0001>>
    设置web参数量    <<配电单元免责下电使能_2~0xa0010502e0001>>    允许
    FOR    ${val}    IN RANGE    1    6
        ${起始时刻}    Catenate    SEPARATOR=    <<配电单元免责下电起始时刻${val}_    2    ~0xa001050310001>>
        ${终止时刻}    Catenate    SEPARATOR=    <<配电单元免责下电终止时刻${val}_    2    ~0xa001050320001>>
        设置web参数量    ${起始时刻}    23:59
        设置web参数量    ${终止时刻}    23:58
        ${起始时刻值}    获取web参数量    ${起始时刻}
        ${终止时刻值}    获取web参数量    ${终止时刻}
        should not be equal as strings    ${终止时刻值}    23:58
    END
    [Teardown]    设置web设备参数量为默认值    <<配电单元免责下电使能_1~0xa0010502e0001>>    <<配电单元免责下电使能_2~0xa0010502e0001>>
定时下电时段设置测试
    获取参数取值约定并比对取值范围    <<配电单元定时下电使能_1~0xa001050170001>>    禁止    允许
    设置web参数量    <<配电单元定时下电使能_1~0xa001050170001>>    允许
    FOR    ${val}    IN RANGE    1    6
        ${起始时刻}    Catenate    SEPARATOR=    <<配电单元定时下电起始时刻${val}_    1    ~0xa001050180001>>
        ${终止时刻}    Catenate    SEPARATOR=    <<配电单元定时下电终止时刻${val}_    1    ~0xa001050190001>>
        设置web参数量    ${起始时刻}    23:59
        设置web参数量    ${终止时刻}    23:58
        sleep    10
        ${起始时刻值}    获取web参数量    ${起始时刻}
        ${终止时刻值}    获取web参数量    ${终止时刻}
        should not be equal as strings    ${终止时刻值}    23:58
    END
    [Teardown]    设置web设备参数量为默认值  <<配电单元定时下电使能_1~0xa001050170001>>
