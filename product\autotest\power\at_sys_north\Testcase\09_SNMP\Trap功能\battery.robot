*** Settings ***
Suite Setup       测试用例前置条件
Suite Teardown    测试用例后置条件
Default Tags      T1-1
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
snmp_0002_电池温度高√
    同时设置三相电压频率    220    50
    打开交流源输出
    连接CSU
    Wait Until Keyword Succeeds    15m    1    判断告警不存在    电池温度高
    #获取告警级别
    ${级别设置值}    获取web参数量    电池温度高
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池温度高    主要
    sleep    3
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    10    1.6    电池_1    电池温度
    #产生电池温度高告警
    ${电池温度高阈值范围}    获取web参数上下限范围    电池温度高阈值
    ${电池温度高阈值可设置范围}    获取web参数可设置范围    电池温度高阈值
    设置web参数量    电池温度高阈值    ${电池温度高阈值可设置范围}[0]
    ${电池温度}    获取web实时数据    电池温度-1
    wait until keyword succeeds    13m    2    判断告警存在    电池温度高
    #产生
    ${snmp英文名}    获取snmp单个告警英文名    电池温度高_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    设置web参数量    电池温度高阈值    ${电池温度高阈值范围}[0]
    Wait Until Keyword Succeeds    12m    1    判断告警不存在    电池温度高
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池温度高阈值
    ...    AND    设置web参数量    电池温度高    主要

snmp_0004_电池温度低√
    同时设置三相电压频率    220    50
    打开交流源输出
    连接CSU
    Wait Until Keyword Succeeds    15m    1    判断告警不存在    电池温度低
    #获取告警级别
    ${级别设置值}    获取web参数量    电池温度低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池温度低    主要
    sleep    3
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    -10    0.001    电池_1    电池温度
    #产生电池温度高告警
    ${电池温度低阈值范围}    获取web参数上下限范围    电池温度低阈值
    ${电池温度低阈值可设置范围}    获取web参数可设置范围    电池温度低阈值
    设置web参数量    电池温度低阈值    ${电池温度低阈值可设置范围}[1]
    ${电池温度}    获取web实时数据    电池温度-1
    wait until keyword succeeds    13m    2    判断告警存在    电池温度低
    #产生
    ${snmp英文名}    获取snmp单个告警英文名    电池温度低_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    设置web参数量    电池温度低阈值    ${电池温度低阈值范围}[0]
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池温度低
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池温度低阈值
    ...    AND    设置web参数量    电池温度低    主要

snmp_0006_电池回路断_无
    [Tags]    3

snmp_0008_电池温度无效√
    [Setup]    run keywords    连接CSU
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    纯铅酸    #测试用例前置条件
    #电池2不接入传感器
    设置通道无效值/恢复通道原始值    ${plat.batttemps}[0]    1
    sleep    60
    实时告警刷新完成
    设置web参数量    电池温度无效    屏蔽
    wait until keyword succeeds    10m    1    判断告警不存在    电池温度无效
    ${电池温度无效告警}    判断告警存在_带返回值    电池温度无效
    should not be true    ${电池温度无效告警}
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    wait until keyword succeeds    30    1    设置web参数量    电池温度无效    主要
    wait until keyword succeeds    10m    1    查询指定告警信息    电池温度无效
    sleep    20
    #产生
    ${snmp英文名}    获取snmp单个告警英文名    电池温度无效_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    设置web参数量    电池温度无效    屏蔽
    sleep    30
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    run keywords    设置web参数量    电池温度无效    主要
    ...    AND    设置通道无效值/恢复通道原始值    ${plat.batttemps}[0]    0

snmp_0010_电池电压低X
    [Setup]    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    纯铅酸
    连接交流源
    连接电池模拟器
    重置电池模拟器输出
    打开电池模拟器输出
    sleep    10
    关闭交流源输出
    实时告警刷新完成
    #获取告警级别
    ${级别设置值}    获取web参数量    电池电压低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池电压低    主要
    ${电压低告警}    判断告警不存在_带返回值    电池电压低
    run keyword if    '${电压低告警}' != '[]'    向上调节电池电压    53.5
    ${可设置范围}    获取web参数可设置范围    电池电压低阈值
    设置web参数量    电池电压低阈值    ${可设置范围}[1]
    ${ 电压低阈值}    获取web参数量    电池电压低阈值
    向下调节电池电压    ${ 电压低阈值}-0.5
    wait until keyword succeeds    15m    2    查询指定告警信息    电池电压低
    ###以上告警已产生
    wait until keyword succeeds    30    1    设置web参数量    电池电压低    屏蔽
    wait until keyword succeeds    10m    1    判断告警不存在    电池电压低
    ${电压低告警}    判断告警存在_带返回值    电池电压低
    should not be true    ${电压低告警}
    #已产生，先屏蔽
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    wait until keyword succeeds    30    1    设置web参数量    电池电压低    主要
    wait until keyword succeeds    3m    2    判断告警存在    电池电压低
    #产生
    ${snmp英文名}    获取snmp单个告警英文名    电池电压低_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    #无告警后查询判断
    向上调节电池电压    ${ 电压低阈值}+1
    wait until keyword succeeds    15m    1    判断告警不存在    电池电压低
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    Run keywords    设置web设备参数量为默认值    电池电压低阈值    电池电压低
    ...    AND    设置web参数量    电池电压低    主要
    ...    AND    设置web参数量    电池电压低干接点    0
    ...    AND    打开交流源输出

snmp_0012_电池放电√
    [Documentation]    电池放电阈值，def、min、max:-6,-50,-3
    电池管理初始化
    #获取告警级别
    ${电池放电级别}    获取web参数量    电池放电
    run keyword if    '${电池放电级别}'=='屏蔽'    设置web参数量    电池放电    严重
    sleep    3
    ${电池放电级别}    获取web参数量    电池放电
    ${放电阈值获取范围}    获取web参数上下限范围    电池放电阈值
    设置web参数量    电池放电阈值    ${放电阈值获取范围}[0]
    ###
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    关闭交流源输出
    设置负载电压电流    53.5    20
    打开负载输出
    #电池放电
    run keyword and ignore error    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    系统停电
    ${电池电流}    获取web实时数据    电池电流-1
    wait until keyword succeeds    13m    2    判断告警存在    电池放电
    #产生
    ${snmp英文名}    获取snmp单个告警英文名    电池放电_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    打开交流源输出
    关闭负载输出
    Wait Until Keyword Succeeds    30    1    信号量数据值不为    电池管理状态    系统停电
    wait until keyword succeeds    13m    2    判断告警不存在    电池放电
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    Run keywords    设置web设备参数量为默认值    电池放电阈值
    ...    AND    设置web参数量    电池放电    主要
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出

snmp_0014_电池丢失√
    连接CSU
    #获取参数/默认值
    #获取告警级别
    ${级别设置值}    获取web参数量    电池丢失
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池丢失    严重
    sleep    3
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    #设置通道配置
    ${DI2通道配置}    获取通道配置    ${plat.Inrelay2Status}
    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay2Status}    电池_1    电池丢失    断开
    wait until keyword succeeds    13m    2    判断告警存在    电池丢失
    #产生
    ${snmp英文名}    获取snmp单个告警英文名    电池丢失_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    wait until keyword succeeds    30    1    设置web参数量    电池丢失    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay2Status}    ${DI2通道配置}[1]    ${DI2通道配置}[2]    ${DI2通道配置}[3]
    Wait Until Keyword Succeeds    12m    1    判断告警不存在    电池丢失
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    Run keywords    设置web参数量    电池丢失    主要
    ...    AND    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay2Status}    ${DI2通道配置}[1]    ${DI2通道配置}[2]    ${DI2通道配置}[3]

snmp_0016_电池测试失败√
    [Documentation]    Run keywords | 设置web设备参数量为默认值 | 测试最长时间 | AND | 设置web参数量 | 电池测试失败 | 主要 | AND | 关闭负载输出 | AND | 打开交流源输出 | AND | 重置电池模拟器输出
    电池管理初始化
    #获取告警级别
    ${电池测试失败级别}    获取web参数量    电池测试失败
    run keyword if    '${电池测试失败级别}'=='屏蔽'    设置web参数量    电池测试失败    严重
    ###排除其他告警的影响
    wait until keyword succeeds    30    1    设置web参数量    交流停电    屏蔽
    sleep    3
    ${电池测试失败级别}    获取web参数量    电池测试失败
    ${缺省值}    获取web参数上下限范围_有单位    测试最长时间
    ${可设置范围}    获取web参数可设置范围    测试最长时间
    ${测试最长时间设置值}    run keyword if    '${缺省值}[3]'== 'Hour'    evaluate    ${可设置范围}[0]+1
    ...    ELSE    evaluate    ${可设置范围}[0]+5
    ${测试最长时间转换后}    run keyword if    '${缺省值}[3]'== 'Hour'    evaluate    ${测试最长时间设置值}*60
    ...    ELSE    set variable    ${测试最长时间设置值}
    设置web参数量    测试最长时间    ${测试最长时间设置值}
    #断开电池，以便电池检测异常
    设置负载电压电流    53.5    15
    打开负载输出
    断开电池模拟器
    连接电池模拟器
    关闭电池模拟器输出
    sleep    3
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    #启动电池测试
    Wait Until Keyword Succeeds    12m    1    设置web控制量    启动测试
    run keyword and ignore error    Wait Until Keyword Succeeds    10    1    信号量数据值为    电池管理状态    测试
    wait until keyword succeeds    13m    2    判断告警存在    电池测试失败
    sleep    10
    #产生
    ${snmp英文名}    获取snmp单个告警英文名    电池测试失败_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    关闭负载输出
    仅电池模拟器供电
    同时设置三相电压频率    220    50
    打开交流源输出
    连接CSU
    Wait Until Keyword Succeeds    15m    1    信号量数据值为    交流供电状态    市电1
    Wait Until Keyword Succeeds    15m    1    判断告警不存在    停电
    Wait Until Keyword Succeeds    15m    2    信号量数据值大于    工作整流器数量    0
    设置web参数量    均充使能    允许
    Wait Until Keyword Succeeds    15m    1    设置web控制量    启动测试
    Wait Until Keyword Succeeds    12m    1    信号量数据值为    电池管理状态    测试
    ${等待时间}    evaluate    ${测试最长时间转换后}+2
    Wait Until Keyword Succeeds    ${等待时间}m    1    信号量数据值为    电池管理状态    均充
    wait until keyword succeeds    13m    2    判断告警不存在    电池测试失败
    ##trap消失
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    Wait Until Keyword Succeeds    12m    1    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    12m    1    信号量数据值为    电池管理状态    浮充
    [Teardown]    Run keywords    设置web设备参数量为默认值    测试最长时间
    ...    AND    设置web参数量    电池测试失败    主要
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出
    ...    AND    重置电池模拟器输出

snmp_0018_电池电压过低√
    [Setup]    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    纯铅酸
    连接交流源
    连接电池模拟器
    重置电池模拟器输出
    打开电池模拟器输出
    sleep    10
    关闭交流源输出
    实时告警刷新完成
    #获取告警级别
    ${级别设置值}    获取web参数量    电池电压过低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池电压过低    严重
    ${电压低告警}    判断告警不存在_带返回值    电池电压过低
    run keyword if    '${电压低告警}' != '[]'    向上调节电池电压    53.5
    ${可设置范围}    获取web参数可设置范围    电池电压过低阈值
    设置web参数量    电池电压过低阈值    ${可设置范围}[1]
    ${ 电压低阈值}    获取web参数量    电池电压过低阈值
    向下调节电池电压    ${ 电压低阈值}-0.5
    wait until keyword succeeds    15m    2    查询指定告警信息    电池电压过低
    ###以上告警已产生
    wait until keyword succeeds    30    1    设置web参数量    电池电压过低    屏蔽
    wait until keyword succeeds    10m    1    判断告警不存在    电池电压过低
    ${电压低告警}    判断告警存在_带返回值    电池电压过低
    should not be true    ${电压低告警}
    #已产生，先屏蔽
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    wait until keyword succeeds    30    1    设置web参数量    电池电压过低    主要
    wait until keyword succeeds    15m    1    判断告警存在    电池电压过低
    #产生
    ${snmp英文名}    获取snmp单个告警英文名    电池电压过低_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    #无告警后查询判断
    向上调节电池电压    ${ 电压低阈值}+1
    wait until keyword succeeds    15m    1    判断告警不存在    电池电压过低
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    Run keywords    设置web设备参数量为默认值    电池电压过低阈值
    ...    AND    设置web参数量    电池电压过低    主要
    ...    AND    打开交流源输出

snmp_0020_电池中点电压不平衡_无
    [Tags]    3
    Log    硬件台不支持模拟此告警信息
    Log    暂不自动化
