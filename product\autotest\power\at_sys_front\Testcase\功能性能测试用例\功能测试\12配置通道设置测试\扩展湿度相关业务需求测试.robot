*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
扩展湿度传感器状态测试（温湿度传感器通道配置）
    [Setup]    温湿度传感器通讯测试前置条件
    ${温湿度传感器设备}    Run Keyword If    '${温湿度传感器型号}'=='0'    Set Variable    DMU_WS312M1
    ${温湿度传感器设备}    Run Keyword If    '${温湿度传感器型号}'=='1'    Set Variable    DMU_YD8779Y
    #设置温湿度传感器通道
    设置温湿度传感器通道    湿度传感器    系统运行环境    扩展湿度
    设置子工具值    ${温湿度传感器设备}    all    只读    湿度    55
    Wait Until Keyword Succeeds    10m    5    信号量数据值为(强制获取)    扩展湿度    55
    Wait Until Keyword Succeeds    10m    5    信号量数据值为(强制获取)    扩展湿度传感器状态    正常
    #使湿度超出有效范围
    设置子工具值    ${温湿度传感器设备}    all    只读    湿度    101
    sleep    1m
    ${扩展湿度}    Wait Until Keyword Succeeds    10m    5    获取web实时数据    扩展湿度
    Should Be True    '${扩展湿度}'=='val_invalid'
    Wait Until Keyword Succeeds    10m    5    信号量数据值为(强制获取)    扩展湿度传感器状态    异常
    #断开温湿度传感器通讯
    控制子工具运行停止    ${温湿度传感器设备}    启动
    sleep    3m
    设置子工具值    ${温湿度传感器设备}    all    只读    湿度    50
    Wait Until Keyword Succeeds    10m    5    信号量数据值为(强制获取)    扩展湿度    50
    Wait Until Keyword Succeeds    10m    5    信号量数据值为(强制获取)    扩展湿度传感器状态    正常
    控制子工具运行停止    ${温湿度传感器设备}    关闭
    sleep    3m
    ${扩展湿度}    Wait Until Keyword Succeeds    10m    5    获取web实时数据    扩展湿度
    Should Be True    '${扩展湿度}'=='val_invalid'
    Wait Until Keyword Succeeds    10m    5    信号量数据值为(强制获取)    扩展湿度传感器状态    异常
    设置温湿度传感器通道    湿度传感器    系统运行环境    无
    [Teardown]    温湿度传感器通讯测试结束条件

扩展湿度无效告警级别默认值测试
    连接CSU
    强制恢复默认值并重新登录
    #采用AI通道设置
    #将温度传感器通道设置成无
    设置温湿度传感器通道    湿度传感器    系统运行环境    无
    #采用温湿度传感器通道
    wait until keyword succeeds    30    1    设置温湿度传感器通道    湿度传感器    系统运行环境    扩展湿度
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    扩展湿度无效    屏蔽
    wait until keyword succeeds    30    1    设置温湿度传感器通道    湿度传感器    系统运行环境    无

扩展湿度高低告警级别默认值测试
    连接CSU
    强制恢复默认值并重新登录
    #采用AI通道设置
    #将温度传感器通道设置成无
    设置温湿度传感器通道    湿度传感器    系统运行环境    无
    #采用温湿度传感器通道
    wait until keyword succeeds    30    1    设置温湿度传感器通道    湿度传感器    系统运行环境    扩展湿度
    sleep    30
    ${扩展湿度高告警}    Wait Until Keyword Succeeds    5m    5    获取web参数量    扩展湿度高
    Should Be True    '${扩展湿度高告警}'=='次要'
    ${扩展湿度低告警}    Wait Until Keyword Succeeds    5m    5    获取web参数量    扩展湿度低
    Should Be True    '${扩展湿度低告警}'=='次要'
    wait until keyword succeeds    30    1    设置温湿度传感器通道    湿度传感器    系统运行环境    无

扩展湿度高低告警阈值默认值测试
    连接CSU
    强制恢复默认值并重新登录
    #采用AI通道设置
    #将温度传感器通道设置成无
    设置温湿度传感器通道    湿度传感器    系统运行环境    无
    #采用温湿度传感器通道
    wait until keyword succeeds    30    1    设置温湿度传感器通道    湿度传感器    系统运行环境    扩展湿度
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    扩展湿度高阈值    90
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    扩展湿度低阈值    20
    wait until keyword succeeds    30    1    设置温湿度传感器通道    湿度传感器    系统运行环境    无

扩展湿度传感器未配置测试
    [Documentation]    无温度采样通道配置时，扩展湿度传感器状态为未配置
    连接CSU
    #将温度传感器通道设置成无
    设置温湿度传感器通道    温度传感器    系统运行环境    无
    @{扩展温度AI通道}    Create List    SPB_J4_T1    SPB_J4_T2    SPB_J13_T3    SPB_J12_T4
    FOR    ${j}    IN RANGE    0    4
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[${j}]    0    1    无    无
    END
    设置温湿度传感器通道    湿度传感器    系统运行环境    无
    sleep    30
    ${扩展湿度}    获取web实时数据    扩展湿度
    Should Be True    '${扩展湿度}'=='val_invalid'
    信号量数据值为(强制获取)    扩展湿度传感器状态    未配置
    判断告警不存在    扩展湿度无效
    判断告警不存在    扩展湿度低
    判断告警不存在    扩展湿度高

扩展湿度高告警阈值设置测试
    连接CSU
    强制恢复默认值并重新登录
    #采用AI通道设置
    #将温度传感器通道设置成无
    设置温湿度传感器通道    湿度传感器    系统运行环境    无
    #采用温湿度传感器通道
    wait until keyword succeeds    30    1    设置温湿度传感器通道    湿度传感器    系统运行环境    扩展湿度
    sleep    30
    ${设置结果}    Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展湿度高阈值    100
    Should Be True    ${设置结果}
    ${设置结果}    Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展湿度高阈值    70
    Should Be True    ${设置结果}
    #设置取值范围外的值
    ${设置结果}    Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展湿度高阈值    101
    Should not Be True    ${设置结果}
    ${设置结果}    Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展湿度高阈值    69
    Should not Be True    ${设置结果}
    Wait Until Keyword Succeeds    5m    5    设置web参数量    扩展湿度高阈值    90
    wait until keyword succeeds    30    1    设置温湿度传感器通道    湿度传感器    系统运行环境    无

扩展湿度低告警阈值设置测试
    连接CSU
    强制恢复默认值并重新登录
    #采用AI通道设置
    #将温度传感器通道设置成无
    设置温湿度传感器通道    湿度传感器    系统运行环境    无
    #采用温湿度传感器通道
    wait until keyword succeeds    30    1    设置温湿度传感器通道    湿度传感器    系统运行环境    扩展湿度
    sleep    30
    ${设置结果}    Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展湿度低阈值    50
    Should Be True    ${设置结果}
    ${设置结果}    Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展湿度低阈值    10
    Should Be True    ${设置结果}
    #设置取值范围外的值
    ${设置结果}    Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展湿度低阈值    51
    Should not Be True    ${设置结果}
    ${设置结果}    Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展湿度低阈值    9
    Should not Be True    ${设置结果}
    Wait Until Keyword Succeeds    5m    5    设置web参数量    扩展湿度低阈值    20
    wait until keyword succeeds    30    1    设置温湿度传感器通道    湿度传感器    系统运行环境    无

扩展湿度高告警测试
    [Setup]    温湿度传感器通讯测试前置条件
    ${温湿度传感器设备}    Run Keyword If    '${温湿度传感器型号}'=='0'    Set Variable    DMU_WS312M1
    ${温湿度传感器设备}    Run Keyword If    '${温湿度传感器型号}'=='1'    Set Variable    DMU_YD8779Y
    ${级别设置值}    set Variable
    ${告警阈值}    set Variable
    #先将温湿度通道配置设置成未配置
    设置温湿度传感器通道    湿度传感器    系统运行环境    无
    sleep    30
    Wait Until Keyword Succeeds    5m    5    判断告警不存在    扩展湿度高
    设置温湿度传感器通道    湿度传感器    系统运行环境    扩展湿度
    sleep    30
    #将扩展湿度高阈值改成70
    Wait Until Keyword Succeeds    5m    5    设置web参数量    扩展湿度高阈值    70
    设置子工具值    ${温湿度传感器设备}    all    只读    湿度    71
    ${级别设置值}    获取web参数量    扩展湿度高
    ${告警阈值}    获取web参数量    扩展湿度高阈值
    run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    扩展湿度高    主要
    Wait Until Keyword Succeeds    5m    5    判断告警存在    扩展湿度高
    Wait Until Keyword Succeeds    10    2    设置web参数量    扩展湿度高    屏蔽
    Wait Until Keyword Succeeds    5m    5    判断告警不存在    扩展湿度高
    Wait Until Keyword Succeeds    10    2    设置web参数量    扩展湿度高    次要
    Wait Until Keyword Succeeds    5m    5    判断告警存在    扩展湿度高
    设置子工具值    ${温湿度传感器设备}    all    只读    湿度    66
    Wait Until Keyword Succeeds    5m    5    判断告警不存在    扩展湿度高
    设置子工具值    ${温湿度传感器设备}    all    只读    湿度    72
    Wait Until Keyword Succeeds    5m    5    判断告警存在    扩展湿度高
    设置温湿度传感器通道    湿度传感器    系统运行环境    无
    Wait Until Keyword Succeeds    5m    5    判断告警不存在    扩展湿度高
    [Teardown]    温湿度传感器通讯测试结束条件

扩展湿度低告警测试
    [Setup]    温湿度传感器通讯测试前置条件
    ${温湿度传感器设备}    Run Keyword If    '${温湿度传感器型号}'=='0'    Set Variable    DMU_WS312M1
    ${温湿度传感器设备}    Run Keyword If    '${温湿度传感器型号}'=='1'    Set Variable    DMU_YD8779Y
    ${级别设置值}    set Variable
    ${告警阈值}    set Variable
    #先将温湿度通道配置设置成未配置
    设置温湿度传感器通道    湿度传感器    系统运行环境    无
    sleep    30
    Wait Until Keyword Succeeds    5m    5    判断告警不存在    扩展湿度低
    设置温湿度传感器通道    湿度传感器    系统运行环境    扩展湿度
    sleep    30
    #将扩展湿度低阈值为10~50
    设置子工具值    ${温湿度传感器设备}    all    只读    湿度    9
    ${级别设置值}    获取web参数量    扩展湿度低
    ${告警阈值}    获取web参数量    扩展湿度低阈值
    Should Be True    10<=${告警阈值}<=50
    run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    扩展湿度低    主要
    Wait Until Keyword Succeeds    5m    5    判断告警存在    扩展湿度低
    Wait Until Keyword Succeeds    10    2    设置web参数量    扩展湿度低    屏蔽
    Wait Until Keyword Succeeds    5m    5    判断告警不存在    扩展湿度低
    Wait Until Keyword Succeeds    10    2    设置web参数量    扩展湿度低    次要
    Wait Until Keyword Succeeds    5m    5    判断告警存在    扩展湿度低
    设置子工具值    ${温湿度传感器设备}    all    只读    湿度    54
    Wait Until Keyword Succeeds    5m    5    判断告警不存在    扩展湿度低
    设置子工具值    ${温湿度传感器设备}    all    只读    湿度    8
    Wait Until Keyword Succeeds    5m    5    判断告警存在    扩展湿度低
    设置温湿度传感器通道    湿度传感器    系统运行环境    无
    Wait Until Keyword Succeeds    5m    5    判断告警不存在    扩展湿度低
    [Teardown]    温湿度传感器通讯测试结束条件

扩展湿度无效告警测试
    [Setup]    温湿度传感器通讯测试前置条件
    ${温湿度传感器设备}    Run Keyword If    '${温湿度传感器型号}'=='0'    Set Variable    DMU_WS312M1
    ${温湿度传感器设备}    Run Keyword If    '${温湿度传感器型号}'=='1'    Set Variable    DMU_YD8779Y
    ${级别设置值}    set Variable
    #先将温湿度通道配置设置成未配置
    设置温湿度传感器通道    湿度传感器    系统运行环境    无
    sleep    30
    Wait Until Keyword Succeeds    5m    5    判断告警不存在    扩展湿度无效
    设置温湿度传感器通道    湿度传感器    系统运行环境    扩展湿度
    sleep    30
    #将扩展湿度设置成无效值
    设置子工具值    ${温湿度传感器设备}    all    只读    湿度    101
    ${级别设置值}    获取web参数量    扩展湿度无效
    run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    扩展湿度无效    主要
    Wait Until Keyword Succeeds    5m    5    判断告警存在    扩展湿度无效
    Wait Until Keyword Succeeds    10    2    设置web参数量    扩展湿度无效    屏蔽
    Wait Until Keyword Succeeds    5m    5    判断告警不存在    扩展湿度无效
    Wait Until Keyword Succeeds    10    2    设置web参数量    扩展湿度无效    次要
    Wait Until Keyword Succeeds    5m    5    判断告警存在    扩展湿度无效
    设置温湿度传感器通道    湿度传感器    系统运行环境    无
    sleep    30
    判断告警不存在    扩展湿度无效
    [Teardown]    温湿度传感器通讯测试结束条件

扩展湿度采样通道默认配置测试
    [Documentation]    扩展湿度默认配置为扩展温度1#
    连接CSU
    强制恢复默认值并重新登录
    Wait Until Keyword Succeeds    10m    5    信号量数据值为(强制获取)    扩展湿度传感器状态    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    扩展湿度高阈值    90
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    扩展湿度低阈值    20
    ${设置结果}    wait until keyword succeeds    30    1    设置通道配置    SPB_J15_HUM    0    1    系统运行环境    扩展湿度
    Should Not Be True    ${设置结果}
