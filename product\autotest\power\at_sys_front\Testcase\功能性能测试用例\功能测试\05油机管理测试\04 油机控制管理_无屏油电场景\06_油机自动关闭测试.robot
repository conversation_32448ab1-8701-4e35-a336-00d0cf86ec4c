*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
直流电压关闭测试
    [Setup]
    油机管理初始化
    ${开始数量}    获取web事件记录数量    油机启停记录
    @{记录内容old}    run keyword if    ${开始数量} != 0    获取web事件记录最新一条    油机启停记录
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止电压使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止SOC使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止电流使能    禁止
    ${停止电压}    获取web参数量    油机停止电压
    ${可设置范围}    获取web参数可设置范围    油机最短运行时间
    ${时间设置值}    evaluate    ${可设置范围}[0]+1
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机最短运行时间    ${时间设置值}
    ${最短运行时间设置值}    获取web参数量    油机最短运行时间
    Wait Until Keyword Succeeds    15m    1    设置web控制量    <<油机开启~0x19001040010001>>
    Wait Until Keyword Succeeds    20m    1    获取干接点状态直到变化为    ${油机干接点状态}    0    #干接点动作为1（断开），恢复为0（闭合）
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    运行
    ${起始时间}    获取系统时间
    ${起始发电量}    获取web实时数据    油机累计发电量
    ${起始运行时间}    获取web实时数据    油机累计运行时间
    sleep    2m
    ${设置电压}    evaluate    ${停止电压}+1
    设置电池模拟器电压模式    铅酸电池    1    1    ${设置电压}
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    停止
    ${结束运行时间}    获取web实时数据    油机累计运行时间
    Wait Until Keyword Succeeds    20m    1    获取干接点状态直到变化为    ${油机干接点状态}    1    #干接点动作为1（断开），恢复为0（闭合）
    ${结束时间}    获取系统时间
    ${结束发电量}    获取web实时数据    油机累计发电量
    sleep    1m
    关闭交流源输出
    sleep    4m
    ${结束数量}    获取web事件记录数量    油机启停记录
    should be true    ${结束数量} == ${开始数量}+1
    @{记录内容}    获取web事件记录最新一条    油机启停记录
    should be true    ${记录内容}[0] == ${结束数量}
    ${起始时间差}    subtract date from date    ${记录内容}[1]    ${起始时间}
    should be true    -40<=${起始时间差}<=40
    ${结束时间差}    subtract date from date    ${记录内容}[2]    ${结束时间}
    should be true    -40<=${结束时间差}<=40
    should be true    ${起始运行时间}-1<= ${记录内容}[3] <= ${起始运行时间}+1
    should be true    ${结束运行时间}-1<= ${记录内容}[4] <= ${结束运行时间}+1
    should be true    ${起始发电量}-0.4 <= ${记录内容}[5] <= ${起始发电量}+0.4
    should be true    ${结束发电量}-0.4<= ${记录内容}[6] <= ${结束发电量}+0.4
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[7] \ == ${记录内容old}[7]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[8] \ == ${记录内容old}[8]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[9] \ == ${记录内容old}[9]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[10] \ == ${记录内容old}[10]
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    油机停止电压使能
    ...    油机停止SOC使能    油机最短运行时间
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止电流使能
    ...    禁止
    ...    AND    关闭交流源输出

电池SOC关闭测试
    油机管理初始化
    ${开始数量}    获取web事件记录数量    油机启停记录
    @{记录内容old}    run keyword if    ${开始数量} != 0    获取web事件记录最新一条    油机启停记录
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    20
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止电压使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止SOC使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止电流使能    禁止
    ${可设置范围}    获取web参数可设置范围    油机最短运行时间
    ${时间设置值}    evaluate    ${可设置范围}[0]+1
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机最短运行时间    ${时间设置值}
    ${最短运行时间设置值}    获取web参数量    油机最短运行时间
    ${可设置范围}    获取web参数可设置范围    油机关闭SOC阈值
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机关闭SOC阈值    ${可设置范围}[0]
    ${关闭SOC设置值}    获取web参数量    油机关闭SOC阈值
    Wait Until Keyword Succeeds    15m    1    设置web控制量    油机开启
    Wait Until Keyword Succeeds    20m    1    获取干接点状态直到变化为    ${油机干接点状态}    0    #干接点动作为1（断开），恢复为0（闭合）
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    运行
    ${起始时间}    获取系统时间
    ${起始发电量}    获取web实时数据    油机累计发电量
    ${起始运行时间}    获取web实时数据    油机累计运行时间
    sleep    2m
    Wait Until Keyword Succeeds    1m    2s    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    浮充    #浮充
    #设置电池电流
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.battcurr1}     20    1.6
    ...    直流配电    电池分流器电流_1
    Wait Until Keyword Succeeds    32m    1    信号量数据值大于    电池组当前容量比率-1    ${关闭SOC设置值}
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    停止
    ${结束运行时间}    获取web实时数据    油机累计运行时间
    Wait Until Keyword Succeeds    20m    1    获取干接点状态直到变化为    ${油机干接点状态}    1    #干接点动作为1（断开），恢复为0（闭合）
    ${结束时间}    获取系统时间
    ${结束发电量}    获取web实时数据    油机累计发电量
    sleep    1m
    关闭交流源输出
    sleep    4m
    ${结束数量}    获取web事件记录数量    油机启停记录
    should be true    ${结束数量} == ${开始数量}+1
    @{记录内容}    获取web事件记录最新一条    油机启停记录
    should be true    ${记录内容}[0] == ${结束数量}
    ${起始时间差}    subtract date from date    ${记录内容}[1]    ${起始时间}
    should be true    -600<=${起始时间差}<=600
    ${结束时间差}    subtract date from date    ${记录内容}[2]    ${结束时间}
    should be true    -600<=${结束时间差}<=600
    should be true    ${起始运行时间}-1<= ${记录内容}[3] <= ${起始运行时间}+1
    should be true    ${结束运行时间}-1<= ${记录内容}[4] <= ${结束运行时间}+1
    should be true    ${起始发电量}-0.4 <= ${记录内容}[5] <= ${起始发电量}+0.4
    should be true    ${结束发电量}-0.4<= ${记录内容}[6] <= ${结束发电量}+0.4
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[7] \ == ${记录内容old}[7]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[8] \ == ${记录内容old}[8]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[9] \ == ${记录内容old}[9]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[10] \ == ${记录内容old}[10]
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    油机停止电压使能
    ...    油机关闭SOC阈值    油机停止SOC使能    油机最短运行时间
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止电流使能
    ...    禁止
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1
    ...    100
    ...    AND    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.battcurr1} 
    ...    0    1    直流配电    电池分流器电流_1
    ...    AND    关闭负载输出
    ...    AND    关闭交流源输出

电池电流关闭测试
    油机管理初始化
    ${开始数量}    获取web事件记录数量    油机启停记录
    @{记录内容old}    run keyword if    ${开始数量} != 0    获取web事件记录最新一条    油机启停记录
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    500
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止电压使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止SOC使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止电流使能    允许
    ${可设置范围}    获取web参数可设置范围    油机最短运行时间
    ${时间设置值}    evaluate    ${可设置范围}[0]+1
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机最短运行时间    ${时间设置值}
    ${最短运行时间设置值}    获取web参数量    油机最短运行时间
    ${可设置范围}    获取web参数可设置范围    油机关闭电池电流
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机关闭电池电流    ${可设置范围}[1]
    ${关闭电流设置值}    获取web参数量    油机关闭电池电流
    ${电池容量}    获取web参数量    电池组容量_1
    ${关闭电流}    evaluate    ${电池容量}*${关闭电流设置值}
    Wait Until Keyword Succeeds    15m    1    设置web控制量    <<油机开启~0x19001040010001>>
    Wait Until Keyword Succeeds    20m    1    获取干接点状态直到变化为    ${油机干接点状态}    0    #干接点动作为1（断开），恢复为0（闭合）
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    运行
    ${起始时间}    获取系统时间
    ${起始发电量}    获取web实时数据    油机累计发电量
    ${起始运行时间}    获取web实时数据    油机累计运行时间
    sleep    2m
    ${系统异常}    获取指定量的调测信息    system_abnormal
    run keyword if    ${系统异常} != 0    系统复位
    #设置电池电流
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.battcurr1}     5    1
    ...    直流配电    电池分流器电流_1
    Wait Until Keyword Succeeds    32m    1    信号量数据值小于    电池电流-1    ${关闭电流}
    Wait Until Keyword Succeeds    20m    1    信号量数据值为    油机状态    停止
    ${结束运行时间}    获取web实时数据    油机累计运行时间
    Wait Until Keyword Succeeds    20m    1    获取干接点状态直到变化为    ${油机干接点状态}    1    #干接点动作为1（断开），恢复为0（闭合）
    ${结束时间}    获取系统时间
    ${结束发电量}    获取web实时数据    油机累计发电量
    sleep    1m
    关闭交流源输出
    sleep    4m
    ${结束数量}    获取web事件记录数量    油机启停记录
    should be true    ${结束数量} == ${开始数量}+1
    @{记录内容}    获取web事件记录最新一条    油机启停记录
    should be true    ${记录内容}[0] == ${结束数量}
    ${起始时间差}    subtract date from date    ${记录内容}[1]    ${起始时间}
    should be true    -400<=${起始时间差}<=400
    ${结束时间差}    subtract date from date    ${记录内容}[2]    ${结束时间}
    should be true    -400<=${结束时间差}<=400
    should be true    ${起始运行时间}-1<= ${记录内容}[3] <= ${起始运行时间}+1
    should be true    ${结束运行时间}-1<= ${记录内容}[4] <= ${结束运行时间}+1
    should be true    ${起始发电量}-0.4 <= ${记录内容}[5] <= ${起始发电量}+0.4
    should be true    ${结束发电量}-0.4<= ${记录内容}[6] <= ${结束发电量}+0.4
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[7] \ == ${记录内容old}[7]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[8] \ == ${记录内容old}[8]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[9] \ == ${记录内容old}[9]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[10] \ == ${记录内容old}[10]
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    油机停止电压使能
    ...    油机停止SOC使能    油机关闭电池电流
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1
    ...    100
    ...    AND    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.battcurr1} 
    ...    0    1    直流配电    电池分流器电流_1
    ...    AND    关闭交流源输出

最长时间关闭测试
    油机管理初始化
    ${开始数量}    获取web事件记录数量    油机启停记录
    @{记录内容old}    run keyword if    ${开始数量} != 0    获取web事件记录最新一条    油机启停记录
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止电压使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止SOC使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止电流使能    禁止
    ${可设置范围}    获取web参数可设置范围    油机最短运行时间
    ${时间设置值}    evaluate    ${可设置范围}[0]+1
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机最短运行时间    ${时间设置值}
    ${最短运行时间设置值}    获取web参数量    油机最短运行时间
    ${可设置范围}    获取web参数可设置范围    油机最长运行时间
    ${时间设置值}    evaluate    ${可设置范围}[0]+6
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机最长运行时间    ${时间设置值}
    ${最长运行时间设置值}    获取web参数量    油机最长运行时间
    Wait Until Keyword Succeeds    15m    1    设置web控制量    <<油机开启~0x19001040010001>>
    Wait Until Keyword Succeeds    20m    1    获取干接点状态直到变化为    ${油机干接点状态}    0    #干接点动作为1（断开），恢复为0（闭合）
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    14m    1    信号量数据值为    油机状态    运行
    ${起始时间}    获取系统时间
    ${起始发电量}    获取web实时数据    油机累计发电量
    ${起始运行时间}    获取web实时数据    油机累计运行时间
    ${等待时间}    evaluate    ${最长运行时间设置值}+2
    Wait Until Keyword Succeeds    ${等待时间}m    1    信号量数据值为    油机状态    停止
    ${结束运行时间}    获取web实时数据    油机累计运行时间
    Wait Until Keyword Succeeds    20m    1    获取干接点状态直到变化为    ${油机干接点状态}    1    #干接点动作为1（断开），恢复为0（闭合）
    ${结束时间}    获取系统时间
    ${结束发电量}    获取web实时数据    油机累计发电量
    sleep    1m
    关闭交流源输出
    sleep    4m
    ${结束数量}    获取web事件记录数量    油机启停记录
    should be true    ${结束数量} == ${开始数量}+1
    @{记录内容}    获取web事件记录最新一条    油机启停记录
    should be true    ${记录内容}[0] == ${结束数量}
    ${起始时间差}    subtract date from date    ${记录内容}[1]    ${起始时间}
    should be true    -400<=${起始时间差}<=400
    ${结束时间差}    subtract date from date    ${记录内容}[2]    ${结束时间}
    should be true    -400<=${结束时间差}<=400
    should be true    ${起始运行时间}-1<= ${记录内容}[3] <= ${起始运行时间}+1
    should be true    ${结束运行时间}-1<= ${记录内容}[4] <= ${结束运行时间}+1
    should be true    ${起始发电量}-0.4 <= ${记录内容}[5] <= ${起始发电量}+0.4
    should be true    ${结束发电量}-0.4<= ${记录内容}[6] <= ${结束发电量}+0.4
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[7] \ == ${记录内容old}[7]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[8] \ == ${记录内容old}[8]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[9] \ == ${记录内容old}[9]
    run keyword if    ${开始数量} != 0    should be true    ${记录内容}[10] \ == ${记录内容old}[10]
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10m    1    设置web设备参数量为默认值    油机停止电压使能
    ...    油机停止SOC使能    油机最短运行时间    油机最长运行时间
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    油机停止电流使能
    ...    禁止
    ...    AND    关闭交流源输出
