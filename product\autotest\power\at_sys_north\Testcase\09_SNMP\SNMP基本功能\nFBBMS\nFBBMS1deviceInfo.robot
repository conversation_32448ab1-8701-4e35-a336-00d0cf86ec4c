*** Settings ***
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp_test关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/Web关键字_V30.robot
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
# snmp批量获取nFBBMS设备信息
#     写入CSV文档    FB100C2设备信息获取测试    信号名称    信号值    结果    备注
#     连接CSU
#     进行SNMP_V2/V3连接    ${snmp连接方式}
#     #版本子工具只能设置99.23，但是显示v99.23
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string32    INDEX_DEVICE_CH=NFBBMS
#     ${排除列表}    create list
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100C2排除设备版本信息}    ${排除列表}    2    1    0    2
#     ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    nFBBMS    deviceInfo
#     FOR    ${i}    IN    ${snmp待测}[0]
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${节点名称}    Get From Dictionary    ${i}    snmp_name
#         ${信号序号}    Get From Dictionary    ${i}    Vindex
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    fb100c2    ${信号名称}    获取设备厂家信息    99.23    FB100C2设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    fb100c2    ${信号名称}    获取设备厂家信息    10.10    FB100C2设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    fb100c2    ${信号名称}    获取设备厂家信息    1.81    FB100C2设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
#     END
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string32    INDEX_DEVICE_CH=NFBBMS
#     ${排除列表}    create list
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100C2排除设备名称信息}    ${排除列表}    2    1    0    2
#     ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    nFBBMS    deviceInfo
#     FOR    ${i}    IN    @{snmp待测}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${节点名称}    Get From Dictionary    ${i}    snmp_name
#         ${信号序号}    Get From Dictionary    ${i}    Vindex
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    fb100c2    ${信号名称}    获取设备厂家信息    FB100B3    FB100C2设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    fb100c2    ${信号名称}    获取设备厂家信息    ZTE-smartl    FB100C2设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    fb100c2    ${信号名称}    获取设备厂家信息    FB100C2    FB100C2设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
#     END
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string32    INDEX_DEVICE_CH=NFBBMS
#     ${排除列表}    create list
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100C2排除设备序列号信息}    ${排除列表}    2    1    0    2
#     ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    nFBBMS    deviceInfo
#     FOR    ${i}    IN    @{snmp待测}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${节点名称}    Get From Dictionary    ${i}    snmp_name
#         ${信号序号}    Get From Dictionary    ${i}    Vindex
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    fb100c2    ${信号名称}    获取设备厂家信息    210097205489    FB100C2设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    fb100c2    ${信号名称}    获取设备厂家信息    210097205673    FB100C2设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    fb100c2    ${信号名称}    获取设备厂家信息    210097205688    FB100C2设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
#     END
#     断开连接SNMP

snmp批量获取nFBBMS设备信息
    写入CSV文档    FB100C2设备信息获取测试    信号名称    信号值    结果    备注
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    #版本子工具只能设置99.23，但是显示v99.23
    # @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string32    INDEX_DEVICE_CH=NFBBMS
    # ${排除列表}    create list
    # @{信号名称列表1}   create_list
    # ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100C2排除设备版本信息}    ${排除列表}    2    1    0    2
    # ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    nFBBMS    deviceInfo
    # FOR    ${i}    IN    ${snmp待测}[0]
    #     ${信号名称}    Get From Dictionary    ${i}    signal_name
    #     ${节点名称}    Get From Dictionary    ${i}    snmp_name
    #     ${信号序号}    Get From Dictionary    ${i}    Vindex
    #     ${dict}    Create Dictionary
    #     Set To Dictionary    ${dict}     signal_name     ${信号名称}
    #     Set To Dictionary    ${dict}     snmp_name     ${节点名称}
    #     Set To Dictionary    ${dict}     Vindex     ${信号序号}
	# 	Append To List       ${信号名称列表1}    ${dict}
    # END
    # Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    fb100c2    ${信号名称列表1}    获取设备厂家信息    V99.23    FB100C2设备信息获取测试    字符    null
    # Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    fb100c2    ${信号名称列表1}    获取设备厂家信息    V10.10    FB100C2设备信息获取测试    字符    null
    # Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    fb100c2    ${信号名称列表1}    获取设备厂家信息    V1.81    FB100C2设备信息获取测试    字符    null

    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string32    INDEX_DEVICE_CH=NFBBMS
    ${排除列表}    create list
    @{信号名称列表2}    create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100C2排除设备名称信息}    ${排除列表}    2    1    0    2
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    nFBBMS    deviceInfo
    FOR    ${i}    IN    @{snmp待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    snmp_name
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
        Set To Dictionary    ${dict}     snmp_name     ${节点名称}
        Set To Dictionary    ${dict}     Vindex     ${信号序号}
		Append To List       ${信号名称列表2}    ${dict}
    END
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    fb100c2    ${信号名称列表2}    获取设备厂家信息    FB100B3    FB100C2设备信息获取测试    字符    null
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    fb100c2    ${信号名称列表2}    获取设备厂家信息    ZTE-smartl    FB100C2设备信息获取测试    字符    null
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    fb100c2    ${信号名称列表2}    获取设备厂家信息    FB100C2    FB100C2设备信息获取测试    字符    null

    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string32    INDEX_DEVICE_CH=NFBBMS
    ${排除列表}    create list
    @{信号名称列表3}    create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100C2排除设备序列号信息}    ${排除列表}    2    1    0    2
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    nFBBMS    deviceInfo
    FOR    ${i}    IN    @{snmp待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    snmp_name
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
        Set To Dictionary    ${dict}     snmp_name     ${节点名称}
        Set To Dictionary    ${dict}     Vindex     ${信号序号}
		Append To List       ${信号名称列表3}    ${dict}
    END
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    fb100c2    ${信号名称列表3}    获取设备厂家信息    210097205489    FB100C2设备信息获取测试    字符    null
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    fb100c2    ${信号名称列表3}    获取设备厂家信息    210097205673    FB100C2设备信息获取测试    字符    null
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    snmp    fb100c2    ${信号名称列表3}    获取设备厂家信息    210097205688    FB100C2设备信息获取测试    字符    null
    断开连接SNMP

snmp获取nFBBMS软件版本
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    设置子工具值    fb100c2    all    获取设备厂家信息    BMS软件版本    9.99
    @{锂电随机list}    从1-15个数中随机选n个不重复的单体    16    4
    FOR    ${锂电序号}    IN    @{锂电随机list}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<BMS软件版本-${锂电序号}~0x21001080020001>>    V9.99
        ${snmp获取值}    获取SNMP数据_单个    nFBBMSBMSSoftwareVersion${锂电序号}value
        should be equal    '${snmp获取值}'    'V9.99'
    END
    设置子工具值    fb100c2    all    获取设备厂家信息    BMS软件版本    1.21
    FOR    ${锂电序号}    IN    @{锂电随机list}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<BMS软件版本-${锂电序号}~0x21001080020001>>    V1.21
        ${snmp获取值}    获取SNMP数据_单个    nFBBMSBMSSoftwareVersion${锂电序号}value
        should be equal    '${snmp获取值}'    'V1.21'
    END
    设置子工具值    fb100c2    all    获取设备厂家信息    BMS软件版本    1.11
    FOR    ${锂电序号}    IN    @{锂电随机list}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<BMS软件版本-${锂电序号}~0x21001080020001>>    V1.11
        ${snmp获取值}    获取SNMP数据_单个    nFBBMSBMSSoftwareVersion${锂电序号}value
        should be equal    '${snmp获取值}'    'V1.11'
    END
    断开连接SNMP
