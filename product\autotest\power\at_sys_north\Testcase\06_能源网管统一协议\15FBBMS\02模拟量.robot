*** Settings ***
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_批量获取FBBMS模拟量
    [Documentation]    21min
    写入CSV文档    FB100B3模拟量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=FBBMS
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除模拟量信号}    ${排除列表}    1    1
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    FBBMS    analog data    ${FB100B3排除模拟量信号}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    Comment    ${指定数据1}    Create Dictionary    signal_name    <<当前设定充电电压-6~0x170010100d0001>>    convention    False    device_name    FBBMS    data_type    float    unit    V
    Comment    @{power_sm待测}    Create List    ${指定数据1}
    ${待测数据长度}    Get Length    ${power_sm待测}
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    1        power_sm
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    power_sm    smartli       只读    ${缺省值列表}    FB100B3模拟量获取测试    获取FBBMS模拟量    
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    2        power_sm
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    power_sm    smartli       只读   ${缺省值列表}    FB100B3模拟量获取测试    获取FBBMS模拟量    
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    0        power_sm
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    power_sm    smartli       只读    ${缺省值列表}    FB100B3模拟量获取测试    获取FBBMS模拟量    
        





   

