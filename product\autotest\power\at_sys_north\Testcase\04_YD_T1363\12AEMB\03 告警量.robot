*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
交流电表通讯断告警
    [Setup]
    连接CSU
    控制子工具运行停止    DMU_aemb    关闭
    wait until keyword succeeds    5min    1    判断告警存在    交流电表通讯断告警-1
    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流电表告警量~CID1=D2H>>    <<获取交流电表告警量~CID2=44H>>    交流电表通讯断-1    1    FF    ${g_ver_1363}
    Should be true    ${1104告警结果}
    控制子工具运行停止    DMU_aemb    启动
    wait until keyword succeeds    5min    1    判断告警不存在    交流电表通讯断告警
    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流电表告警量~CID1=D2H>>    <<获取交流电表告警量~CID2=44H>>    交流电表通讯断-1    0    FF    ${g_ver_1363}
    Should be true    ${1104告警结果}
    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流电表告警量~CID1=D2H>>    <<获取交流电表告警量~CID2=44H>>    交流电表通讯断-1    0    01    ${g_ver_1363}
    Should be true    ${1104告警结果}
    [Teardown]    Run Keywords    控制子工具运行停止    DMU_aemb    启动
    ...    AND    sleep    3min
