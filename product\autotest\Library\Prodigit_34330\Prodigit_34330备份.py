#-*-coding:utf-8 -*-
#####################################################################################
# File Name:    Prodigit_34330.py
# Discription:  实现与PRODIGIT_34330的连接和数据读写
# Author:   Pengy；
# DevEnv:   winXP/7x86+python2.7
# Notes:    使用GPIB与PRODIGIT_34330连接，控制电子负载 
# Log:      20180120Pengy 
################################################################################
import sys
import time
import visa
import os

####读取文件地址，使用RIDE时，改为ADD[1]；使用本地编译时改为ADD[0]####
ADD={0:'..\.',
     1:''
    }
add=ADD[1]
########################################
#设置一个时间的格式
ISOTIME = '%Y-%m-%d %X'
########################################
class Prodigit_34330():
    def __init__(self):     
##        self.bSerialIsOpen = False
##        self.ser = serial.Serial()
        os.chdir(os.path.split(os.path.realpath(__file__))[0])#回到当前脚本目录
        self.rm = visa.ResourceManager()               
        print(self.rm.list_resources())
##        self.inst = rm.open_resource('GPIB0::2::INSTR')
##        print self.inst.read(':FNC:CH1:VLT?')
##        self.inst.write(':FNC:CH1:VLT?')
##        print self.inst.read()
##打开GPIB====================================================================================================================
    def Open_GPIB(self,add,printflag=''):
        """
        根据仪器设置的地址进行设置
        例如：GPIB0::1::INSTR
        """
        add='GPIB0::'+add+'::INSTR'
        try:
            self.inst = self.rm.open_resource(add)
##            time.sleep(1)
            if printflag!='':
                print u'打开GPIB：',add
##            print self.inst.write('CR:High 10')
##            print self.inst.write('PRESet 1')
##            print self.inst.write('LEVel Low')
##            time.sleep(2)
##            print self.inst.write('LOAD ON')
##            print self.inst.query('MEASure:POW?')            
        except:
            print u'打开GPIB失败'

        
##查询输出电压电流=============================================
    def QueryVolCur(self,printflag=''):
        """""""""""""""""""""""""""""""""""""""
        查询输出电压电流
        返回一个列表，内容如下：
        输出电压、输出电流、输出功率

        """""""""""""""""""""""""""""""""""""""
        ReadStr=str(float(self.QueryCmd(u'读输出电压','no').strip('\r\n')))+','
        ReadStr+=str(float(self.QueryCmd(u'读输出电流','no').strip('\r\n')))+','
        ReadStr+=str(float(self.QueryCmd(u'读输出功率','no').strip('\r\n')))
        ReadList=ReadStr.split(',') #转为列表
        if printflag=='':
            print u'输出电压电流功率为:',ReadStr
##        print 'Watts:',float(ReadList[0])
        return ReadList
##设置输出电压电流=============================================
    def SetVolCur(self,vol='300',cur='24'):
        """""""""""""""""""""""""""""""""""""""
        设置输出电压电流
        可设置输出的电压电流，入参为：电压、电流
        返回布尔量
        """""""""""""""""""""""""""""""""""""""
        print u'===设置负载电压电流为：',vol,'V',cur,'A==='
        if self.QueryCmd(u'读操作模式','no')!='1':
            self.SetCmd(u'设置操作模式','1','no')
        if self.QueryCmd(u'读Level设置','no')!='1':
            self.SetCmd(u'设置Level','High','no')
        if float(cur)==0.0:
            Res='4000'
        else:
            Res=str(round(float(vol)/(float(cur)+0.5),2))
##        print u'计算电阻值为：',Res
        self.SetCmd(u'设置CR模式电阻高值',Res,'no')
        self.SetCmd(u'设置是否显示瓦特表','1','no')
        if (float(self.QueryCmd(u'查询CR模式电阻高值','no'))>float(Res)+2
            or float(self.QueryCmd(u'查询CR模式电阻高值','no'))<float(Res)-2):
            print u'实际设置的电阻值为：',float(self.QueryCmd(u'查询CR模式电阻高值','no'))
            print u'设置失败！'
            return False
        else:
            print u'实际设置的电阻值为：',float(self.QueryCmd(u'查询CR模式电阻高值','no'))
            time.sleep(0.5)
##            print u'实际输出电流值为：',float(self.QueryCmd(u'读输出电流','no'))
            return True
##限制输出电压=============================================
    def LimVol(self,vol='600'):
        """""""""""""""""""""""""""""""""""""""
        限制输出电压
        可设置限制的电压，入参为：电压
        范围：150-800V可设置
        返回布尔量
        """""""""""""""""""""""""""""""""""""""
        SetRes=40.0
        queryRes=float(self.QueryCmd(u'查询CR模式电阻高值','no'))
        if queryRes<=SetRes:
            SetRes=queryRes
        
        print u'===设置负载限制输出电压为：',vol,'V==='
        if float(vol)>=800.0 or float(vol)<=50.0:
            print u'设置电压超范围，无法设置！',vol,'V'
            return
        if self.QueryCmd(u'读操作模式','no')!='1':
            self.SetCmd(u'设置操作模式','1','no')
        if self.QueryCmd(u'读Level设置','no')!='1':
            self.SetCmd(u'设置Level','High','no')
        self.SetCmd(u'设置是否显示瓦特表','1','no')
        self.SetCmd(u'设置CR模式电阻高值',str(SetRes),'no')
        time.sleep(2)
        readvol=self.QueryVolCur('noprint')[0]
        if float(vol)>=350.0:         
    #####步长10欧姆调节
            while(abs(float(readvol)-float(vol))>=100.0):#步长10欧姆调节
                if float(readvol)>float(vol):
                    SetRes=SetRes-10.0
    ##                print 'tt1',abs(float(readvol)-float(vol)),SetRes
                    self.SetCmd(u'设置CR模式电阻高值',str(SetRes),'no')
                    time.sleep(1)
                    readvol=self.QueryVolCur('noprint')[0]
                    if float(readvol)<=float(vol):
                        SetRes=SetRes+5.0
    ##                    print 'tt1',abs(float(readvol)-float(vol)),SetRes
                        self.SetCmd(u'设置CR模式电阻高值',str(SetRes),'no')
                        time.sleep(1)
                        readvol=self.QueryVolCur('noprint')[0]
                        break
                    if abs(float(readvol)-float(vol))<=2.0:
                        break
                elif float(readvol)<float(vol):
                    SetRes=SetRes+10.0
    ##                print 'tt1',abs(float(readvol)-float(vol)),SetRes
                    self.SetCmd(u'设置CR模式电阻高值',str(SetRes),'no')
                    time.sleep(1)
                    readvol=self.QueryVolCur('noprint')[0]
                    if float(readvol)>=float(vol):
                        SetRes=SetRes-5.0
    ##                    print 'tt1',abs(float(readvol)-float(vol)),SetRes
                        self.SetCmd(u'设置CR模式电阻高值',str(SetRes),'no')
                        time.sleep(1)
                        readvol=self.QueryVolCur('noprint')[0]
                        break
                    if abs(float(readvol)-float(vol))<=2.0:
                        break
                if abs(float(readvol)-float(vol))<100.0 or SetRes<=1.0:
                    break
    #####步长1欧姆调节
            while(abs(float(readvol)-float(vol))<100.0 and abs(float(readvol)-float(vol))>20.0):#步长1欧姆调节
                if float(readvol)>float(vol):
                    SetRes=SetRes-1.0
    ##                print 'tt21',abs(float(readvol)-float(vol)),SetRes
                    self.SetCmd(u'设置CR模式电阻高值',str(SetRes),'no')
                    time.sleep(1)
                    readvol=self.QueryVolCur('noprint')[0]
                    if float(readvol)<=float(vol):
                        SetRes=SetRes+0.5
    ##                    print 'tt22',abs(float(readvol)-float(vol)),SetRes
                        self.SetCmd(u'设置CR模式电阻高值',str(SetRes),'no')
                        time.sleep(1)
                        readvol=self.QueryVolCur('noprint')[0]
                        break
                    if abs(float(readvol)-float(vol))<=2.0:
    ##                    print 'tt23'
                        break
                elif float(readvol)<float(vol):
                    SetRes=SetRes+1.0
    ##                print 'tt24',abs(float(readvol)-float(vol)),SetRes
                    self.SetCmd(u'设置CR模式电阻高值',str(SetRes),'no')
                    time.sleep(1)
                    readvol=self.QueryVolCur('noprint')[0]
                    if float(readvol)>=float(vol):
                        SetRes=SetRes-0.5
    ##                    print 'tt25',abs(float(readvol)-float(vol)),SetRes
                        self.SetCmd(u'设置CR模式电阻高值',str(SetRes),'no')
                        time.sleep(1)
                        readvol=self.QueryVolCur('noprint')[0]
                        break
                    if abs(float(readvol)-float(vol))<=2.0:
                        break
                if abs(float(readvol)-float(vol))<=20.0 or SetRes<=1.0:
                    break
    #####步长0.1欧姆调节    
            while(abs(float(readvol)-float(vol))>=2.0 and abs(float(readvol)-float(vol))<=20.0):
                if float(readvol)>float(vol):
                    SetRes=SetRes-0.1
    ##                print 'tt3',abs(float(readvol)-float(vol)),SetRes
                    self.SetCmd(u'设置CR模式电阻高值',str(SetRes),'no')
                    time.sleep(1)
                    readvol=self.QueryVolCur('noprint')[0]
                    if abs(float(readvol)-float(vol))<=2.0:
                        break
                elif float(readvol)<float(vol):
                    SetRes=SetRes+0.1
    ##                print 'tt3',abs(float(readvol)-float(vol)),SetRes
                    self.SetCmd(u'设置CR模式电阻高值',str(SetRes),'no')
                    time.sleep(1)
                    readvol=self.QueryVolCur('noprint')[0]
                    if abs(float(readvol)-float(vol))<=2.0:
                        break
                if abs(float(readvol)-float(vol))<=2.0 or SetRes<=1.0:
                    break            

        elif float(vol)<350.0:
    #####步长2欧姆调节
            while(abs(float(readvol)-float(vol))>=100.0):#步长2欧姆调节
                if float(readvol)>float(vol):
                    SetRes=SetRes-2.0
    ##                print 'tt1',abs(float(readvol)-float(vol)),SetRes
                    self.SetCmd(u'设置CR模式电阻高值',str(SetRes),'no')
                    time.sleep(1)
                    readvol=self.QueryVolCur('noprint')[0]
                    if float(readvol)<=float(vol):
                        SetRes=SetRes+1.0
    ##                    print 'tt1',abs(float(readvol)-float(vol)),SetRes
                        self.SetCmd(u'设置CR模式电阻高值',str(SetRes),'no')
                        time.sleep(1)
                        readvol=self.QueryVolCur('noprint')[0]
                        break
                    if abs(float(readvol)-float(vol))<=2.0:
                        break
                elif float(readvol)<float(vol):
                    SetRes=SetRes+2.0
    ##                print 'tt1',abs(float(readvol)-float(vol)),SetRes
                    self.SetCmd(u'设置CR模式电阻高值',str(SetRes),'no')
                    time.sleep(1)
                    readvol=self.QueryVolCur('noprint')[0]
                    if float(readvol)>=float(vol):
                        SetRes=SetRes-1.0
    ##                    print 'tt1',abs(float(readvol)-float(vol)),SetRes
                        self.SetCmd(u'设置CR模式电阻高值',str(SetRes),'no')
                        time.sleep(1)
                        readvol=self.QueryVolCur('noprint')[0]
                        break
                    if abs(float(readvol)-float(vol))<=2.0:
                        break
                if abs(float(readvol)-float(vol))<100.0 or SetRes<=1.0:
                    break
    #####步长0.5欧姆调节
            while(abs(float(readvol)-float(vol))<100.0 and abs(float(readvol)-float(vol))>20.0):#步长1欧姆调节
                if float(readvol)>float(vol):
                    SetRes=SetRes-0.5
##                    print 'tt21',abs(float(readvol)-float(vol)),SetRes,readvol,vol
                    self.SetCmd(u'设置CR模式电阻高值',str(SetRes),'no')
                    time.sleep(1)
                    readvol=self.QueryVolCur('noprint')[0]
                    if float(readvol)<=float(vol):
                        SetRes=SetRes+0.25
##                        print 'tt22',abs(float(readvol)-float(vol)),SetRes,readvol,vol
                        self.SetCmd(u'设置CR模式电阻高值',str(SetRes),'no')
                        time.sleep(1)
                        readvol=self.QueryVolCur('noprint')[0]
                        break
                    if abs(float(readvol)-float(vol))<=2.0:
                        print 'tt23'
                        break
                elif float(readvol)<float(vol):
                    SetRes=SetRes+0.5
                    self.SetCmd(u'设置CR模式电阻高值',str(SetRes),'no')
                    time.sleep(1)
                    readvol=self.QueryVolCur('noprint')[0]
##                    print 'tt24',abs(float(readvol)-float(vol)),SetRes,readvol,vol
                    if float(readvol)>=float(vol):
                        SetRes=SetRes-0.25
##                        print 'tt25',abs(float(readvol)-float(vol)),SetRes,readvol,vol
                        self.SetCmd(u'设置CR模式电阻高值',str(SetRes),'no')
                        time.sleep(1)
                        readvol=self.QueryVolCur('noprint')[0]
                        break
                    if abs(float(readvol)-float(vol))<=2.0:
                        break
                if abs(float(readvol)-float(vol))<=20.0 or SetRes<=1.0:
                    break
    #####步长0.05欧姆调节    
            while(abs(float(readvol)-float(vol))>=2.0 and abs(float(readvol)-float(vol))<=20.0):
                if float(readvol)>float(vol):
                    SetRes=SetRes-0.05                    
                    self.SetCmd(u'设置CR模式电阻高值',str(SetRes),'no')
                    time.sleep(1)
                    readvol=self.QueryVolCur('noprint')[0]
##                    print 'tt31',abs(float(readvol)-float(vol)),SetRes,readvol,vol
                    if float(readvol)<=float(vol):
                        SetRes=SetRes+0.02                        
                        self.SetCmd(u'设置CR模式电阻高值',str(SetRes),'no')
                        time.sleep(1)
                        readvol=self.QueryVolCur('noprint')[0]
##                        print 'tt32',abs(float(readvol)-float(vol)),SetRes,readvol,vol
                        break                    
                    if abs(float(readvol)-float(vol))<=2.0:
                        break
                elif float(readvol)<float(vol):
                    SetRes=SetRes+0.05
##                    print 'tt33',abs(float(readvol)-float(vol)),SetRes,readvol,vol
                    self.SetCmd(u'设置CR模式电阻高值',str(SetRes),'no')
                    time.sleep(1)
                    readvol=self.QueryVolCur('noprint')[0]
##                    print 'tt331',abs(float(readvol)-float(vol)),SetRes,readvol,vol
                    if float(readvol)>=float(vol):
                        SetRes=SetRes-0.02
##                        print 'tt34',abs(float(readvol)-float(vol)),SetRes,readvol,vol
                        self.SetCmd(u'设置CR模式电阻高值',str(SetRes),'no')
                        time.sleep(1)
                        readvol=self.QueryVolCur('noprint')[0]
##                        print 'tt341',abs(float(readvol)-float(vol)),SetRes,readvol,vol
                        break
                    if abs(float(readvol)-float(vol))<=2.0:
                        break
                if abs(float(readvol)-float(vol))<=2.0 or SetRes<=1.0:
                    break   
            

        if (float(self.QueryCmd(u'查询CR模式电阻高值','no'))>float(SetRes)+2
            or float(self.QueryCmd(u'查询CR模式电阻高值','no'))<float(SetRes)-2):
            print u'实际设置的电阻值为：',float(self.QueryCmd(u'查询CR模式电阻高值','no'))
            self.QueryVolCur()
            print u'设置失败！设置目标电阻值：',SetRes
            return False
        else:
            print u'实际设置的电阻值为：',float(self.QueryCmd(u'查询CR模式电阻高值','no'))
            self.QueryVolCur()
            time.sleep(0.5)
##            print u'实际输出电流值为：',float(self.QueryCmd(u'读输出电流','no'))
            return True
##打开输出=============================================
    def OpenLoad(self):
        """""""""""""""""""""""""""""""""""""""
        闭合输出
        """""""""""""""""""""""""""""""""""""""
        if self.QueryCmd(u'查询是否带载','no')!='1':
            self.SetCmd(u'设置是否带载','1','no')
            if self.QueryCmd(u'查询是否带载','no')!='1':
                print u'设置失败！'
                return False
            else:
                print u'打开负载输出！'
                return True
        else:
            print u'已打开负载输出！'
            return True
##关闭输出=============================================
    def CloseLoad(self):
        """""""""""""""""""""""""""""""""""""""
        关闭输出
        """""""""""""""""""""""""""""""""""""""
        if self.QueryCmd(u'查询是否带载','no')!='0':
            self.SetCmd(u'设置是否带载','0','no')
            if self.QueryCmd(u'查询是否带载','no')!='0':
                print u'设置失败！'
                return False
            else:
                print u'关闭负载输出！'
                return True   
        else:
            print u'已关闭负载输出！'
            return True
##保存负载状态=============================================
    def SaveLoadState(self,Group='4',Vol='300.0',Cur='25.0',LoadState='OFF',Mode='1'):

        """""""""""""""""""""""""""""""""""""""
        保存负载状态,可保存150组
        Mode:负载模式，可设置0:CC;1:CR;2:CV;3:CP
        """""""""""""""""""""""""""""""""""""""
        Res=float(Vol)/float(Cur)
        self.SetCmd(u'设置操作模式',Mode,'no')
        if self.QueryCmd(u'读Level设置','no')!='1':
            self.SetCmd(u'设置Level','High','no')
        if float(Cur)==0.0:
            Res='4000'
        if self.QueryCmd(u'读操作模式','no')=='1':
            self.SetCmd(u'设置CR模式电阻高值',str(Res),'no')
        elif self.QueryCmd(u'读操作模式','no')=='0':##CC模式
            self.SetCmd(u'设置CC模式高值',Cur,'no')
        self.SetCmd(u'设置是否显示瓦特表','1','no')
        self.SetCmd(u'设置是否带载',LoadState,'no')       
        self.SetCmd(u'保存负载状态',Group,'no')
        print u'保存负载状态成功！第',Group,u'组电压：',Vol,u'电流：',Cur,u'负载状态',LoadState,u'负载模式',Mode
        
##动态负载切换=============================================
    def SEQTest(self,Vol1='750.0',Vol2='300.0',Cur1='20.0',Cur2='25.0',time1='3000',time2='3000',times='10'):
        time.sleep(1)
        """""""""""""""""""""""""""""""""""""""
        关闭输出
        """""""""""""""""""""""""""""""""""""""
        Res1=float(Vol1)/float(Cur1)
        Res2=float(Vol2)/float(Cur2)
##        print Res1,Res2
        if self.QueryCmd(u'读操作模式','no')!='1':
            self.SetCmd(u'设置操作模式','1','no')
        if self.QueryCmd(u'读Level设置','no')!='1':
            self.SetCmd(u'设置Level','High','no')
        if float(Cur1)==0.0 or float(Cur2)==0.0:
            Res1='4000'
            Res2='4000'
        self.SetCmd(u'设置是否带载','0','no') 
        self.SetCmd(u'设置CR模式电阻高值',str(Res1),'no')
        self.SetCmd(u'设置是否显示瓦特表','1','no')
        self.SetCmd(u'设置是否带载','1','no')       
        self.SetCmd(u'保存负载状态','1','no')

        self.SetCmd(u'设置CR模式电阻高值',str(Res2),'no')
        self.SetCmd(u'设置是否显示瓦特表','1','no')
        self.SetCmd(u'设置是否带载','1','no')       
        self.SetCmd(u'保存负载状态','2','no')

        self.SetCmd(u'设置是否带载','0','no')
        time.sleep(1)
        
        self.SetCmd(u'选择组','1','no')
        self.SetCmd(u'选择步骤','1','no')
        self.SetCmd(u'设置负载状态','1','no')
        self.SetCmd(u'设置负载时间',time1,'no')

        self.SetCmd(u'选择步骤','2','no')
        self.SetCmd(u'设置负载状态','2','no')
        self.SetCmd(u'设置负载时间',time2,'no')
        
        self.SetCmd(u'设置总步骤','2','no')
        self.SetCmd(u'设置运行次数',times,'no')
        self.SetCmd(u'保存负载动态设置',' ','no')

        time.sleep(1)
        
        self.SetCmd(u'运行负载动态','F1','no')
        print u'运行负载动态测试中！'
##多态负载切换=============================================
    def SomeSEQTest(self,GroupList=['1','2','3','4'],timeList=['3000','3000','3000','3000'],times='100'):
        time.sleep(1)
        runtime=0
        """""""""""""""""""""""""""""""""""""""
        多态负载切换
        """""""""""""""""""""""""""""""""""""""
        self.SetCmd(u'选择组','1','no')
        for i in range(len(GroupList)):
##            print i,GroupList[i],timeList[i],str(len(GroupList))
            self.SetCmd(u'选择步骤',str(i+1),'no')
            self.SetCmd(u'设置负载状态',GroupList[i],'no')
            self.SetCmd(u'设置负载时间',timeList[i],'no')
##        
        self.SetCmd(u'设置总步骤',str(len(GroupList)),'no')
        self.SetCmd(u'设置运行次数',str(int(times)-1),'no')
        self.SetCmd(u'保存负载动态设置',' ','no')
        
        time.sleep(1)        
        self.SetCmd(u'运行负载动态','F1','no')
        for i in timeList:
            runtime+=int(i)
        runtime=runtime/1000*int(times)-4
##        print runtime
        print u'运行负载动态测试中！大概需要运行',runtime,'s'        
     
##查询命令=============================================
    def QueryCmd(self, sComment,printflag=''):
        os.chdir(os.path.split(os.path.realpath(__file__))[0])#回到当前脚本目录
        try:
            f = open( r'PRODIGIT_34330_command_list.csv', 'r')
        except:
            print u'打开PRODIGIT_34330_command_list出错！'   
        csvlist = []
        csvlist = f.readlines()
        f.close()
        receiveData=''
        returnStr=''
        tmplist=[]
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
            if tmplist[0] == sComment.encode('gbk'):
                sCmd = tmplist[1].strip('\n')   #命令
##                returnStr=self.inst.query('LEVel?')#查询命令用query
                returnStr=self.inst.query(sCmd)#查询命令用query
                if printflag=='':
                    print u'=======',tmplist[0],'========='
                    print returnStr.strip('\r\n')
        return returnStr.strip('\r\n')

##设置命令=============================================
    def SetCmd(self, sComment,value,printflag=''):
        os.chdir(os.path.split(os.path.realpath(__file__))[0])#回到当前脚本目录
        try:
            f = open( r'PRODIGIT_34330_command_list.csv', 'r')
        except:
            print u'打开PRODIGIT_34330_command_list出错！'     
        csvlist = []
        csvlist = f.readlines()
        f.close()
        receiveData=''
        returnList=[]
        tmplist=[]
        flag=1
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
            if tmplist[0] == sComment.encode('gbk'):
                sCmd = tmplist[1].strip('\n')   #命令
                if printflag=='':
                    print u'=======',tmplist[0],'========='
                while(flag!=0 and flag<5):
                    try:
                        self.inst.write(sCmd+' '+value)#设置命令用write
##                        self.inst.write('PRESet 1')
                        flag=0
                    except:
                        flag=flag+1  #出错了重发




#======================================================================================================    
if __name__ == "__main__":
    myapp = Prodigit_34330()
    myapp.Open_GPIB('1')
    myapp.QueryVolCur()
##    print myapp.inst.query('PROTect?\r')
##    print myapp.inst.write('STOP\r')
##    print myapp.inst.write('RUN F1\r')
##    print myapp.SetCmd(u'调用负载状态','3')
##    myapp.ConnectCom_PM3000(2,19200)
##    myapp.SaveLoadState()
##    time.sleep(10)
####    myapp.SetCmd(u'保存负载状态',Group,'no')()
##    myapp.SetCmd(u'调用负载状态','1','no')

##    myapp.SomeSEQTest()
####    myapp.sendBySerial('\n')
##    myapp.SetCmd(u'设置CR模式电阻高值','23.5')
##    myapp.SetCmd(u'设置是否显示瓦特表','1','no')
##    myapp.QueryVolCur()
##    myapp.SetVolCur()
##    time.sleep(1)
##    myapp.CloseLoad()
##    myapp.OpenLoad()
##    myapp.LimVol('205')
##    myapp.OpenLoad()
##    myapp.LimVol()
##    time.sleep(1)
##    myapp.QueryVolCur()
##    myapp.CloseLoad()
##    myapp.SetVolCur()
##    myapp.SetCmd(u'设置操作模式','2')
######    myapp.QueryCmd(u'读操作模式')
####    while(1):
####        myapp.SetVolCur('267.5','14')
####        time.sleep(3)
####        myapp.SetVolCur('267.5','40')
####        time.sleep(3)
        
        
    
##    myapp.QueryCmd(u'读CH1电压')
##    myapp.ReadCH('CH1')

##    myapp.receiveData()
