*** Settings ***
Suite Setup       测试用例前置条件
Suite Teardown    测试用例后置条件
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
获取系统版本信息
    
    连接CSU
    ${数量}    获取web参数的数量    系统名称
    ${参数名称}    run keyword if    '${数量}'=='1'    set variable    系统名称
    ...    ELSE    set variable    系统名称_1
    ${系统名称}    获取web实时数据    ${参数名称}
    ${数量}    获取web参数的数量    序列号
    ${参数名称}    run keyword if    '${数量}'=='1'    set variable    序列号
    ...    ELSE    set variable    序列号_1
    ${序列号}    获取web实时数据    ${参数名称}
    ${数量}    获取web参数的数量    CSU软件所属平台版本
    ${参数名称}    run keyword if    '${数量}'=='1'    set variable    CSU软件所属平台版本
    ...    ELSE    set variable    CSU软件所属平台版本_1
    ${CSU软件所属平台版本}    获取web实时数据    ${参数名称}
    ${数量}    获取web参数的数量    软件名称
    ${参数名称}    run keyword if    '${数量}'=='1'    set variable    软件名称
    ...    ELSE    set variable    软件名称_1
    ${软件名称}    获取web实时数据    ${参数名称}
    ${数量}    获取web参数的数量    BOOT版本
    ${参数名称}    run keyword if    '${数量}'=='1'    set variable    BOOT版本
    ...    ELSE    set variable    BOOT版本_1
    ${BOOT版本}    获取web实时数据    ${参数名称}
    ${数量}    获取web参数的数量    内核版本
    ${参数名称}    run keyword if    '${数量}'=='1'    set variable    内核版本
    ...    ELSE    set variable    内核版本_1
    ${内核版本}    获取web实时数据    ${参数名称}
    ${数量}    获取web参数的数量    软件版本
    ${参数名称}    run keyword if    '${数量}'=='1'    set variable    软件版本
    ...    ELSE    set variable    软件版本_1
    ${软件版本}    获取web实时数据    ${参数名称}
    ${数量}    获取web参数的数量    厂家名称
    ${参数名称}    run keyword if    '${数量}'=='1'    set variable    厂家名称
    ...    ELSE    set variable    厂家名称_1
    ${厂家名称}    获取web实时数据    ${参数名称}
    ${数量}    获取web参数的数量    软件发布日期
    ${参数名称}    run keyword if    '${数量}'=='1'    set variable    软件发布日期
    ...    ELSE    set variable    软件发布日期_1
    ${软件发布日期}    获取web实时数据    ${参数名称}
    ${数量}    获取web参数的数量    平台发布日期
    ${参数名称}    run keyword if    '${数量}'=='1'    set variable    平台发布日期
    ...    ELSE    set variable    平台发布日期_1
    ${平台发布日期}    获取web实时数据    ${参数名称}
    ${数量}    获取web参数的数量    MAC地址
    ${参数名称}    run keyword if    '${数量}'=='1'    set variable    MAC地址
    ...    ELSE    set variable    MAC地址_1
    ${MAC地址}    获取web实时数据    ${参数名称}
    ${数量}    获取web参数的数量    UIB版本信息
    ${参数名称}    run keyword if    '${数量}'=='1'    set variable    UIB版本信息
    ...    ELSE    set variable    UIB版本信息_1
    ${uib版本信息}    获取web实时数据    ${参数名称}
    ${数量}    获取web参数的数量    IDDB版本信息
    ${参数名称}    run keyword if    '${数量}'=='1'    set variable    IDDB版本信息
    ...    ELSE    set variable    IDDB版本信息_1
    ${iddb版本信息}    获取web实时数据    ${参数名称}
    ${数量}    获取web参数的数量    UIB发布日期
    ${参数名称}    run keyword if    '${数量}'=='1'    set variable    UIB发布日期
    ...    ELSE    set variable    UIB发布日期_1
    ${UIB发布日期}    获取web实时数据    ${参数名称}
    ${数量}    获取web参数的数量    IDDB发布日期
    ${参数名称}    run keyword if    '${数量}'=='1'    set variable    IDDB发布日期
    ...    ELSE    set variable    IDDB发布日期_1
    ${IDDB发布日期}    获取web实时数据    ${参数名称}
    ${数量}    获取web参数的数量    UIB条码
    ${参数名称}    run keyword if    '${数量}'=='1'    set variable    UIB条码
    ...    ELSE    set variable    UIB条码_1
    ${UIB条码}    获取web实时数据    ${参数名称}
    ${数量}    获取web参数的数量    IDDB条码
    ${参数名称}    run keyword if    '${数量}'=='1'    set variable    IDDB条码
    ...    ELSE    set variable    IDDB条码_1
    ${IDDB条码}    获取web实时数据    ${参数名称}
    ${数量}    获取web参数的数量    UIB类型
    ${参数名称}    run keyword if    '${数量}'=='1'    set variable    UIB类型
    ...    ELSE    set variable    UIB类型_1
    ${UIB类型}    获取web实时数据    ${参数名称}
    should be equal    ${软件名称}    ZXDT22 SF01 V3.0

交流节能状态显示测试
    [Documentation]    整流器组-交流节能模式：0:安全/Safe;1:节能/Save;2:自由/Free。默认0=====
    ...    能源系统-交流节能状态：0:安全/Safe;1:自由/Free Mode;2:自动非节能/Auto NonSave;3:自动节能/Auto Save;4:暂时非节能/Temp NonSave;5:永久非节能/Perm NonSave;6:人工维护检测/Manual Detect。默认0
    
    连接CSU
    ${原系统工作模式}    Wait Until Keyword Succeeds    5m    2    获取web参数量    交流节能模式
    @{节能}    create list    自动非节能    自动节能    暂时非节能    永久非节能    人工维护检测
    @{安全}    create list    安全    安全模式    #有效版本状态显示为安全、有的为安全模式
    @{自由}    create list    自由    自由模式    #有效版本状态显示为自由、有的为自由模式
    @{工作模式}    create list    ${安全}    ${自由}    ${节能}
    @{工作模式参数}    create list    安全    自由    节能
    FOR    ${参数设置}    ${对应的工作模式}    IN ZIP    ${工作模式参数}    ${工作模式}
        设置web参数量    交流节能模式    ${参数设置}
        sleep    3
        ${参数获取}    获取web参数量    交流节能模式
        should be equal    ${参数获取}    ${参数设置}
        ${实际获取}    获取web实时数据    交流节能状态
        should contain    ${对应的工作模式}    ${实际获取}
    END

工作模式显示测试
    
    同时设置三相电压频率    220    50
    打开交流源输出
    连接CSU
    Wait Until Keyword Succeeds    3m    2    信号量数据值不为    电池管理状态    系统停电
    wait until keyword succeeds    1m    1    信号量数据值大于    工作整流器数量    0
    sleep    5
    ${当前数据}    Wait Until Keyword Succeeds    10    2    获取web实时数据    交流节能状态
    #安全和自由模式
    @{节能}    create list    自动非节能    自动节能    暂时非节能    永久非节能    人工维护检测
    @{安全}    create list    安全    安全模式    #有效版本状态显示为安全、有的为安全模式
    @{自由}    create list    自由    自由模式    #有效版本状态显示为自由、有的为自由模式
    @{工作模式}    create list    ${安全}    ${自由}    ${节能}
    ${工作模式参数}    create list    安全    自由    节能
    FOR    ${参数设置}    ${对应的工作模式}    IN ZIP    ${工作模式参数}    ${工作模式}
        设置web参数量    交流节能模式    ${参数设置}
        sleep    5
        ${参数获取}    获取web参数量    交流节能模式
        should be equal    ${参数获取}    ${参数设置}
        ${实际获取}    获取web实时数据    交流节能状态
        should contain    ${对应的工作模式}    ${实际获取}
    END
    #节能模式
    设置web参数量    交流节能模式    节能
    sleep    5
    ${节能控制命令}    create list    暂时非节能控制    永久非节能控制    自动节能控制    人工维护检测
    ${节能工作模式}    create list    暂时非节能    永久非节能    [自动非节能,自动节能]    人工维护检测
    FOR    ${下发控制命令}    ${节能模式选取}    IN ZIP    ${节能控制命令}    ${节能工作模式}
        Wait Until Keyword Succeeds    5X    1    设置web控制量    ${下发控制命令}
        sleep    5
        ${实际获取}    获取web实时数据    交流节能状态
        should contain    ${节能模式选取}    ${实际获取}
    END
    [Teardown]    设置web参数量    交流节能模式    安全

电池管理模式
    
    同时设置三相电压频率    220    50
    打开交流源输出
    连接CSU
    Wait Until Keyword Succeeds    3m    2    信号量数据值不为    电池管理状态    系统停电
    wait until keyword succeeds    1m    1    信号量数据值大于    工作整流器数量    0
    sleep    5
    ${当前数据}    Wait Until Keyword Succeeds    5m    2    获取web实时数据    电池管理状态
    #确认当前状态是否存在电池检测异常告警(如上次检测异常出现告警后，将不会取消除非再次检测成功)，如有在消除（CSU复位可取消）
    ${是否存在电池检测异常告警}    判断告警存在_带返回值    电池检测异常
    run keyword if    '${是否存在电池检测异常告警}'=='True'    系统复位
    run keyword if    '${是否存在电池检测异常告警}'=='True'    连接CSU
    #均充浮充测试检测
    设置web参数量    均充使能    允许
    ${控制命令}    create list    启动浮充    启动均充    启动测试    启动电池检测
    ${电池管理模式}    create list    浮充    均充    测试    检测
    Comment    ${测试记录数量}    获取web历史记录数量    测试记录
    FOR    ${下发控制命令}    ${电池管理模式选取}    IN ZIP    ${控制命令}    ${电池管理模式}
        wait until keyword succeeds    30    1    设置web控制量    ${下发控制命令}
        ${是否成功}    run keyword and return status    wait until keyword succeeds    30    1    信号量数据值为    电池管理状态    ${电池管理模式选取}
        ${实际获取}    获取web实时数据    电池管理状态
        should be equal    ${电池管理模式选取}    ${实际获取}
    END
    设置web控制量    启动浮充
    #确认当前状态是否存在电池检测异常告警，如有则消除
    ${是否电池检测异常}    run keyword and return status    wait until keyword succeeds    5    1    判断告警存在    电池检测异常
    run keyword if    '${是否电池检测异常}'=='True'    系统复位
    run keyword if    '${是否电池检测异常}'=='True'    连接CSU
    #电池测试、检测后，电池电压会下降，等待电池电压恢复
    wait until keyword succeeds    5m    2    信号量数据值大于    直流电压    52

整流器数量显示测试
    
    连接CSU
    同时设置三相电压频率    220    50
    打开交流源输出
    设置电池模拟器模式    铅酸电池    24    5    50
    电池模拟器接触器分合    ON
    打开电池模拟器输出
    Wait Until Keyword Succeeds    3m    2    信号量数据值不为    电池管理状态    系统停电
    wait until keyword succeeds    30    1    信号量数据值大于    工作整流器数量    0
    sleep    5
    ${工作整流器}    获取web实时数据    工作整流器数量
    ${在线整流器}    获取web实时数据    在线整流器数量
    ${正常整流器}    run keyword and ignore error    获取web实时数据    正常整流器数量
    #有些版本无正常整流器数量
    run keyword if    '${正常整流器}[0]'!='FAIL'    should be true    ${在线整流器}>=${正常整流器}[1]>=${工作整流器}
    ...    ELSE    should be true    ${在线整流器}>=${工作整流器}
    关闭交流源输出
    Wait Until Keyword Succeeds    3m    2    信号量数据值小于    工作整流器数量    ${工作整流器}
    sleep    5
    ${工作整流器_停电}    获取web实时数据    工作整流器数量
    ${在线整流器_停电}    获取web实时数据    在线整流器数量
    ${正常整流器_停电}    run keyword and ignore error    获取web实时数据    正常整流器数量
    should be true    ${工作整流器_停电}==0
    should be true    ${在线整流器_停电}==${在线整流器}
    run keyword if    '${正常整流器}[0]'!='FAIL'    should be true    ${正常整流器_停电}[1]==0
    打开交流源输出
    sleep    5

交流供电状态显示测试
    [Documentation]    0:无/Null;2:油机1/DG1;3:油机2/DG2;4:市电1/AC1;5:市电2/AC2
    
    同时设置三相电压频率    220    50
    打开交流源输出
    连接CSU
    Wait Until Keyword Succeeds    3m    2    信号量数据值不为    电池管理状态    系统停电
    wait until keyword succeeds    1m    1    信号量数据值大于    工作整流器数量    0
    sleep    5
    #交流供电状态
    @{状态}    create list    无    油机1    油机2    市电1    市电2
    wait until keyword succeeds    10    1    设置web参数量    交流输入场景    市电
    wait until keyword succeeds    10    1    设置web参数量    市电配置_1    有
    wait until keyword succeeds    10    1    设置web参数量    市电配置_2    无
    sleep    5
    ${实际获取}    获取web实时数据    交流供电状态
    should be equal    ${实际获取}    市电1
    wait until keyword succeeds    10    1    设置web参数量    市电配置_1    无
    wait until keyword succeeds    10    1    设置web参数量    市电配置_2    有
    sleep    5
    ${实际获取1}    获取web实时数据    交流供电状态
    should be equal    ${实际获取1}    市电2
    wait until keyword succeeds    10    1    设置web参数量    交流输入场景    纯油机
    sleep    5
    ${油机功率}    获取web参数量    柴油发电机额定功率_1
    ${实际获取2}    获取web实时数据    交流供电状态
    run keyword if    ${油机功率} != 0    should be equal    ${实际获取2}    油机1
    #市电干接点闭合
    ${强制控制状态}    获取干接点强制控制状态
    run keyword if    ${强制控制状态}!=1    干接点强制控制开启
    ${强制控制状态}    获取干接点强制控制状态
    Wait Until Keyword Succeeds    10    2    干接点恢复    5
    获取控制输出干接点状态    5    #输出干接点的状态
    干接点强制控制关闭
    wait until keyword succeeds    10    1    设置web参数量    交流输入场景    油电
    sleep    5
    wait until keyword succeeds    10    1    设置web参数量    市电配置_1    有
    wait until keyword succeeds    10    1    设置web参数量    市电配置_2    无
    sleep    5
    ${实际获取3}    获取web实时数据    交流供电状态
    should be equal    ${实际获取3}    油机1
    wait until keyword succeeds    10    1    设置web参数量    交流输入场景    油电
    sleep    5
    wait until keyword succeeds    10    1    设置web参数量    市电配置_1    无
    wait until keyword succeeds    10    1    设置web参数量    市电配置_2    有
    sleep    5
    ${实际获取3}    获取web实时数据    交流供电状态
    should be equal    ${实际获取3}    油机1
    [Teardown]    Run keywords    设置web参数量    交流输入场景    市电
    ...    AND    设置web参数量    市电配置_1    有
    ...    AND    设置web参数量    市电配置_2    无

交流电压测试
    
    同时设置三相电压频率    220    50
    打开交流源输出
    连接CSU
    ${缺省值}    获取web参数上下限范围    交流制式
    ${取值约定}    获取web参数的取值约定    交流制式
    ${val}    Get From Dictionary    ${取值约定}    ${缺省值}[0]
    Wait Until Keyword Succeeds    10    1    设置web参数量    交流制式    ${val}
    Wait Until Keyword Succeeds    3m    2    信号量数据值为    交流供电状态    市电1
    wait until keyword succeeds    1m    1    信号量数据值大于    工作整流器数量    0
    sleep    30
    ${获取数据1}    获取web实时数据    系统交流电压_1
    ${获取数据2}    获取web实时数据    系统交流电压_2
    ${获取数据3}    获取web实时数据    系统交流电压_3
    #2v交流电压稳压精度    #可能为0，如缺相    #如果为无效，认为失败
    run keyword if    ${获取数据1}!=0    should be true    218<=${获取数据1}<=222
    ...    ELSE    should be true    ${获取数据1}==0
    run keyword if    ${获取数据2}!=0    should be true    218<=${获取数据2}<=222
    ...    ELSE    should be true    ${获取数据2}==0
    run keyword if    ${获取数据3}!=0    should be true    218<=${获取数据3}<=222
    ...    ELSE    should be true    ${获取数据3}==0
    同时设置三相电压频率    240    50
    sleep    30
    Comment    Wait Until Keyword Succeeds    30    1    信号量数据值大于    系统交流电压_1    238
    Comment    Wait Until Keyword Succeeds    30    1    信号量数据值大于    系统交流电压_2    238
    Comment    Wait Until Keyword Succeeds    30    1    信号量数据值大于    系统交流电压_3    238
    ${获取数据1}    获取web实时数据    系统交流电压_1
    ${获取数据2}    获取web实时数据    系统交流电压_2
    ${获取数据3}    获取web实时数据    系统交流电压_3
    #2v交流电压稳压精度    #可能为0，如缺相    #如果为无效，认为失败
    run keyword if    ${获取数据1}!=0    should be true    238<=${获取数据1}<=242
    ...    ELSE    should be true    ${获取数据1}==0
    run keyword if    ${获取数据2}!=0    should be true    238<=${获取数据2}<=242
    ...    ELSE    should be true    ${获取数据2}==0
    run keyword if    ${获取数据3}!=0    should be true    238<=${获取数据3}<=242
    ...    ELSE    should be true    ${获取数据3}==0
    [Teardown]    同时设置三相电压频率    220    50

交流电流测试
    
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    3m    2    信号量数据值不为    电池管理状态    系统停电
    wait until keyword succeeds    30    1    信号量数据值大于    工作整流器数量    0
    sleep    5
    设置web参数量    交流制式    L1L2L3N-220V
    run keyword and ignore error    设置web参数量    限流点比率    1000
    设置web参数量    浮充电压    53.5
    设置web参数量    电池组容量_1    0    #将电池容量均设为0，电压调节会更快速
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    直流电压    52.6
    设置web参数量    电池组容量_1    300    #有电池，电压控制更精准
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    直流电压    53
    ${工作整流器地址}    获取工作整流器地址
    ${工作整流器数量}    获取web实时数据    工作整流器数量
    ${电压}    获取web实时数据    直流电压
    ${整流器最大输出电流}    获取web实时数据    整流器最大输出电流_${工作整流器地址}[0]
    ${电流}    evaluate    ${工作整流器数量}*${整流器最大输出电流}*0.6
    Comment    ${电流}    evaluate    ${工作整流器数量}*40
    缓慢设置负载电压电流    ${电压}    ${电流}    5
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    直流电压    50
    Wait Until Keyword Succeeds    5m    2    判断信号量数据值稳定    整流器总输出电流    3
    ${L1电流}    获取web实时数据    系统交流电流_1
    ${L2电流}    获取web实时数据    系统交流电流_2
    ${L3电流}    获取web实时数据    系统交流电流_3
    ${平均电流}    evaluate    (${L1电流}+${L2电流}+${L3电流})/${工作整流器数量}*1.00
    FOR    ${地址}    IN    @{工作整流器地址}
        ${获取数据}    获取web实时数据    整流器输入电流-${地址}
        should be true    ${平均电流}-1<${获取数据}<${平均电流}+1
    END
    关闭负载输出
    [Teardown]    run keywords    关闭负载输出
    ...    AND    仅电池模拟器供电

电池温度显示测试
    连接CSU
    ${电池温度}    Wait Until Keyword Succeeds    10    1    获取web实时数据    电池温度-1
    should be true    20<=${电池温度}<=35
    ${温度传感器状态}    获取web实时数据    电池温度传感器状态
    should be equal    '${温度传感器状态}'    '正常'

电池SOH显示测试
    连接CSU
    ${电池组当前容量比率}    获取web实时数据    电池组当前容量比率-1
    ${电池SOH}    获取web实时数据    电池SOH-1
    should be true    0<${电池SOH}<=100

电池SOC显示测试
    连接CSU
    ${电池组当前容量比率}    获取web实时数据    电池组当前容量比率-1
    ${参数获取}    获取web参数量    电池组容量_1
    ${电池SOC}    evaluate    ${电池组当前容量比率}*${参数获取}/100
    should be true    ${电池SOC}<=${参数获取}
