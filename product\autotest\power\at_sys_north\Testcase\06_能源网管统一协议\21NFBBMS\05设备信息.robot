*** Settings ***
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_批量获取NFBBMS设备信息
    写入CSV文档    FB100C2设备信息获取测试    信号名称    信号值    结果    备注
    连接CSU
    Comment    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string32    INDEX_DEVICE_CH=NFBBMS
    Comment    ${排除列表}    create list
    Comment    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除设备版本信息}    ${排除列表}    1
    Comment    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    NFBBMS    device info    ${FB100B3排除设备版本信息}
    Comment    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    Comment    FOR    ${i}    IN    @{power_sm待测}
    Comment    \    ${信号名称}    Get From Dictionary    ${i}    signal_name
    Comment    \    Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    power_sm    fb100c2    ${信号名称}    获取设备厂家信息    99.23    FB100C2设备信息获取测试    字符    获取NFBBMS设备信息    null    null
    Comment    \    Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    power_sm    fb100c2    ${信号名称}    获取设备厂家信息    10.10    FB100C2设备信息获取测试    字符    获取NFBBMS设备信息    null    null
    Comment    \    Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    power_sm    fb100c2    ${信号名称}    获取设备厂家信息    1.81    FB100C2设备信息获取测试    字符    获取NFBBMS设备信息    null    null
    Comment    END
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string32    INDEX_DEVICE_CH=NFBBMS
    ${排除列表}    create list
	@{信号名称列表1}    create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除设备名称信息}    ${排除列表}    1
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    NFBBMS    device info    ${FB100B3排除设备名称信息}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    FOR    ${i}    IN    @{power_sm待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
		${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
		Append To List       ${信号名称列表1}    ${dict}
    END
	Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    fb100c2    ${信号名称列表1}    获取设备厂家信息    FB100B3    FB100C2设备信息获取测试    字符    获取NFBBMS设备信息    
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    fb100c2    ${信号名称列表1}    获取设备厂家信息    ZTE-smartl    FB100C2设备信息获取测试    字符    获取NFBBMS设备信息    
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    fb100c2    ${信号名称列表1}    获取设备厂家信息    FB100C2    FB100C2设备信息获取测试    字符    获取NFBBMS设备信息    
   
	
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string32    INDEX_DEVICE_CH=NFBBMS
    ${排除列表}    create list
	@{信号名称列表2}    create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除设备序列号信息}    ${排除列表}    1
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    NFBBMS    device info    ${FB100B3排除设备序列号信息}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    FOR    ${i}    IN    @{power_sm待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
		${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
		Append To List       ${信号名称列表2}    ${dict}
    END
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    fb100c2    ${信号名称列表2}    获取设备厂家信息    210097205489    FB100C2设备信息获取测试    字符    获取NFBBMS设备信息    
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    fb100c2    ${信号名称列表2}    获取设备厂家信息    210097205673    FB100C2设备信息获取测试    字符    获取NFBBMS设备信息   
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    fb100c2    ${信号名称列表2}    获取设备厂家信息    210097205688    FB100C2设备信息获取测试    字符    获取NFBBMS设备信息   
   

power_sm_0002_获取NFBBMS软件版本
    连接CSU
    设置子工具值    fb100c2    all    获取设备厂家信息    BMS软件版本    9.99
    @{锂电随机list}    从1-15个数中随机选n个不重复的单体    ${北向协议NFBBMS最大数}    3
    FOR    ${锂电序号}    IN    @{锂电随机list}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<BMS软件版本-${锂电序号}~0x21001080020001>>    V9.99
        ${power_sm获取值}    power_sm_获取一个数据的值    获取NFBBMS设备信息    BMS软件版本-${锂电序号}    ${SSH}
        should be equal    '${power_sm获取值}'    'V9.99'
    END
    设置子工具值    fb100c2    all    获取设备厂家信息    BMS软件版本    1.21
    FOR    ${锂电序号}    IN    @{锂电随机list}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<BMS软件版本-${锂电序号}~0x21001080020001>>    V1.21
        ${power_sm获取值}    power_sm_获取一个数据的值    获取NFBBMS设备信息    BMS软件版本-${锂电序号}    ${SSH}
        should be equal    '${power_sm获取值}'    'V1.21'
    END
    设置子工具值    fb100c2    all    获取设备厂家信息    BMS软件版本    1.11
    FOR    ${锂电序号}    IN    @{锂电随机list}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<BMS软件版本-${锂电序号}~0x21001080020001>>    V1.11
        ${power_sm获取值}    power_sm_获取一个数据的值    获取NFBBMS设备信息    BMS软件版本-${锂电序号}    ${SSH}
        should be equal    '${power_sm获取值}'    'V1.11'
    END
