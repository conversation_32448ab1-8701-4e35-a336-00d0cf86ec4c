*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
批量获取状态量和PU告警测试
    [Tags]    PMSA-NTest
    写入CSV文档    PU数字量和告警量获取测试    信号名称    信号值    结果    备注
    连接CSU
    ${级别设置值}    获取web参数量    PU告警
    run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    PU告警    严重
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=太阳能模块
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除告警量信号}    ${排除列表}    1    ${模拟PU起始地址}    1
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        Run Keyword And Continue On Failure    整流器或PU告警量产生封装判断结果    pu    ${信号名称}    数字量    ${告警产生}    ${模拟PU起始地址}    PU告警    PU数字量和告警量获取测试
        Run Keyword And Continue On Failure    整流器或PU告警量恢复封装判断结果    pu    ${信号名称}    数字量    ${告警恢复}    ${模拟PU起始地址}    PU告警    PU数字量和告警量获取测试
    END

批量获取状态量和PU故障测试
    [Tags]    PMSA-NTest
    写入CSV文档    PU数字量和故障量获取测试    信号名称    信号值    结果    备注
    连接CSU
    ${级别设置值}    获取web参数量    PU故障
    run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    PU故障    严重
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=太阳能模块
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除故障量信号}    ${排除列表}    2    ${模拟PU起始地址}    1
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        Run Keyword And Continue On Failure    整流器或PU故障量产生封装判断结果    pu    ${信号名称}    数字量    ${告警产生}    ${模拟PU起始地址}    PU故障    PU数字量和故障量获取测试
        Run Keyword And Continue On Failure    整流器或PU故障量恢复封装判断结果    pu    ${信号名称}    数字量    ${告警恢复}    ${模拟PU起始地址}    PU故障    PU数字量和故障量获取测试
    END

PU通讯中断告警测试
    [Tags]    PMSA-NTest
    连接CSU
    #子设备工具模拟
    设置子工具个数    pu    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU通讯状态-10    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU通讯状态-20    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU通讯状态-30    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU通讯状态-48    异常
    ${级别设置值}    获取web参数量    PU通讯中断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    PU通讯中断    严重
    wait until keyword succeeds    5m    2    查询指定告警信息    PU通讯中断
    设置子工具个数    pu    48
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU通讯状态-1    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU通讯状态-10    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU通讯状态-20    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU通讯状态-30    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU通讯状态-48    正常
    wait until keyword succeeds    5m    2    查询指定告警信息不为    PU通讯中断
    [Teardown]    run keywords    设置子工具个数    pu    48
    ...    AND    sleep    3m

所有PU模块通讯断告警测试
    [Tags]    PMSA-NTest
    连接CSU
    #子设备工具模拟
    控制子工具运行停止    pu    关闭
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    PU通讯状态-10    异常
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    PU通讯状态-20    异常
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    PU通讯状态-30    异常
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    PU通讯状态-48    异常
    ${级别设置值}    获取web参数量    所有PU模块通讯断告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    所有PU模块通讯断告警    严重
    wait until keyword succeeds    5m    2    查询指定告警信息    所有PU模块通讯断告警
    控制子工具运行停止    pu    开启
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    PU通讯状态-1    正常
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    PU通讯状态-10    正常
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    PU通讯状态-20    正常
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    PU通讯状态-30    正常
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    PU通讯状态-48    正常
    wait until keyword succeeds    10m    2    查询指定告警信息不为    PU通讯中断
    wait until keyword succeeds    10m    2    查询指定告警信息不为    所有PU模块通讯断告警
    [Teardown]    run keywords    控制子工具运行停止    pu    开启
    ...    AND    sleep    3m
