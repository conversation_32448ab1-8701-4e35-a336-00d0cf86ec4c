*** Settings ***
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_液位传感器高度
    [Documentation]    0、0、100000
    连接CSU
    设置子工具值    llSensor    all    只读    液位    131065
    sleep    30s
    ${获取值1}    获取web实时数据    液位传感器高度-1
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取液位传感器模拟量    液位传感器高度    ${SSH}
    should be equal as numbers    ${获取值1}    2
    should be equal as numbers    ${power_sm获取值1}    2
    ###超范围设置
    设置子工具值    llSensor    all    只读    液位    6553595
    sleep    30s
    ${获取值1}    获取web实时数据    液位传感器高度-1
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取液位传感器模拟量    液位传感器高度    ${SSH}
    should be equal as numbers    ${获取值1}    100
    should be equal as numbers    ${power_sm获取值1}    100
    ###最小值、默认值
    设置子工具值    llSensor    all    只读    液位    655294459
    sleep    30s
    ${获取值1}    获取web实时数据    液位传感器高度-1
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取液位传感器模拟量    液位传感器高度    ${SSH}
    should be equal as numbers    ${获取值1}    9999
    should be equal as numbers    ${power_sm获取值1}    9999
    设置子工具值    llSensor    all    只读    液位    65531
    sleep    30s
    ${获取值1}    获取web实时数据    液位传感器高度-1
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取液位传感器模拟量    液位传感器高度    ${SSH}
    should be equal as numbers    ${获取值1}    1
    should be equal as numbers    ${power_sm获取值1}    1
