*** Settings ***
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
周期检测时间到进检测
    [Documentation]    0:浮充/Float;1:均充/Equal;2:测试/Test;3:放电/Discharge;4:检测/Detect;5:过渡/Transition;6:充电/Charge
    
    电池管理初始化
    设置web参数量    电池均充周期    0    #默认90
    设置web参数量    电池测试周期    0    #默认0天
    设置web参数量    电池检测周期    30    #默认0
    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    sleep    2m
    Wait Until Keyword Succeeds    5m10s    2    信号量数据值不为    电池管理状态    检测
    ${电池检测周期}    获取web参数量    电池检测周期
    ${下次检测时间}    获取web实时数据    下次检测时间
    ${下次检测时间0}    Subtract Time From Date    ${下次检测时间}    5s    #比1:00:00差5s
    Wait Until Keyword Succeeds    10    1    设置系统时间    ${下次检测时间0}
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    检测
    ${下次检测时间_cal}    add time to date    ${下次检测时间}    ${电池检测周期}d    exclude_millis=yes
    sleep    5
    ${下次检测时间_new}    获取web实时数据    下次检测时间
    should be equal    ${下次检测时间_new}    ${下次检测时间_cal}
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池均充周期    电池测试周期    电池检测周期    电池检测持续时间

检测周期为0时无周期检测
    
    电池管理初始化
    设置web参数量    电池均充周期    0    #默认90
    设置web参数量    电池测试周期    0    #默认0天
    设置web参数量    电池检测周期    60    #默认0天
    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    sleep    2m
    Wait Until Keyword Succeeds    5m10s    2    信号量数据值不为    电池管理状态    检测
    ${下次检测时间}    获取web实时数据    下次检测时间
    ${下次检测时间0}    Subtract Time From Date    ${下次检测时间}    5s    #比1:00:00差5s
    设置web参数量    电池检测周期    0
    sleep    5
    设置系统时间    ${下次检测时间0}
    sleep    1m
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    浮充
    ${系统时间}    Subtract Time From Date    ${下次检测时间0}    7 days
    设置系统时间    ${系统时间}
    sleep    5
    设置web参数量    电池检测周期    60    #默认0天
    sleep    5
    ${下次检测时间_new}    获取web实时数据    下次检测时间
    should be equal    ${下次检测时间_new}    ${下次检测时间}
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池均充周期    电池测试周期    电池检测周期    电池检测持续时间

检测时周期检测时间到
    
    电池管理初始化
    设置web参数量    电池均充周期    0    #默认90
    设置web参数量    电池测试周期    0    #默认0天
    设置web参数量    电池检测周期    30    #默认0天
    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    10    1    信号量数据值为    电池管理状态    检测
    sleep    10
    ${检测持续时间1}    获取web实时数据    电池状态持续时间
    ${电池检测周期}    获取web参数量    电池检测周期
    ${下次检测时间}    获取web实时数据    下次检测时间
    ${下次检测时间0}    Subtract Time From Date    ${下次检测时间}    5s    #比1:00:00差5s
    设置系统时间    ${下次检测时间0}
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    ${检测持续时间2}    获取web实时数据    电池状态持续时间
    should be true    ${检测持续时间2}>=${检测持续时间1}
    Wait Until Keyword Succeeds    2m10s    2    信号量数据值不为    电池管理状态    检测
    Wait Until Keyword Succeeds    2m    2    信号量数据值为    电池状态持续时间    0
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    检测
    ${下次检测时间_cal}    add time to date    ${下次检测时间}    ${电池检测周期}d    exclude_millis=yes
    sleep    5
    ${下次检测时间_new}    获取web实时数据    下次检测时间
    should be equal    ${下次检测时间_new}    ${下次检测时间_cal}
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池均充周期    电池测试周期    电池检测周期    电池检测持续时间

测试时周期检测时间到
    
    电池管理初始化
    设置web参数量    电池均充周期    0    #默认90
    设置web参数量    电池测试周期    0    #默认0天
    设置web参数量    电池检测周期    30    #默认0
    设置web参数量    电池检测持续时间    2
    ${均充末期维持时间范围}    获取web参数上下限范围_有单位    均充末期维持时间
    ${均充最短时间范围}    获取web参数上下限范围_有单位    均充最短时间
    ${均充最长时间范围}    获取web参数上下限范围_有单位    均充最长时间
    ${均充末期维持时间}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间范围}[1]+1
    ...    ELSE    evaluate    ${均充末期维持时间范围}[1]+3
    ${均充最短时间}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间范围}[1]+1
    ...    ELSE    evaluate    ${均充最短时间范围}[1]+3
    ${均充最长时间}    run keyword if    '${均充最长时间范围}[3]'== 'Hour'    evaluate    ${均充最长时间范围}[1]+1
    ...    ELSE    evaluate    ${均充最长时间范围}[1]+5
    设置web参数量    均充末期维持时间    ${均充末期维持时间}
    设置web参数量    均充最短时间    ${均充最短时间}
    设置web参数量    均充最长时间    ${均充最长时间}
    ${均充末期维持时间转换后}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间}*60
    ...    ELSE    set variable    ${均充末期维持时间}
    ${均充最短时间转换后}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间}*60
    ...    ELSE    set variable    ${均充最短时间}
    ${均充最长时间转换后}    run keyword if    '${均充最长时间范围}[3]'== 'Hour'    evaluate    ${均充最长时间}*60
    ...    ELSE    set variable    ${均充最长时间}
    ${测试最长时间范围}    获取web参数上下限范围_有单位    测试最长时间
    ${测试最长时间}    run keyword if    '${测试最长时间范围}[3]'== 'Hour'    evaluate    ${测试最长时间范围}[1]+1
    ...    ELSE    evaluate    ${测试最长时间范围}[1]+5
    设置web参数量    测试最长时间    ${测试最长时间}
    log    ===单位换算完成===
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动测试
    Wait Until Keyword Succeeds    10    1    信号量数据值为    电池管理状态    测试
    sleep    30
    ${电池检测周期}    获取web参数量    电池检测周期
    ${下次检测时间}    获取web实时数据    下次检测时间
    ${下次检测时间0}    Subtract Time From Date    ${下次检测时间}    5s    #比1:00:00差5s
    设置系统时间    ${下次检测时间0}
    Wait Until Keyword Succeeds    6m    2    信号量数据值不为    电池管理状态    测试
    Wait Until Keyword Succeeds    10    1    信号量数据值为    电池管理状态    均充
    Wait Until Keyword Succeeds    7m    2    信号量数据值不为    电池管理状态    均充
    Wait Until Keyword Succeeds    10    1    信号量数据值为    电池管理状态    检测
    sleep    5
    ${下次检测时间_cal}    add time to date    ${下次检测时间}    ${电池检测周期}d    exclude_millis=yes
    ${下次检测时间_new}    获取web实时数据    下次检测时间
    should be equal    ${下次检测时间_new}    ${下次检测时间_cal}
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池测试周期    电池均充周期    电池检测周期    测试最长时间
    ...    均充最长时间    均充最短时间    均充末期维持时间    电池检测持续时间

均充时周期检测时间到
    
    电池管理初始化
    设置web参数量    电池均充周期    0    #默认90
    设置web参数量    电池测试周期    0    #默认0天
    设置web参数量    电池检测周期    30    #默认0天
    设置web参数量    电池检测持续时间    2
    ${均充末期维持时间范围}    获取web参数上下限范围_有单位    均充末期维持时间
    ${均充最短时间范围}    获取web参数上下限范围_有单位    均充最短时间
    ${均充最长时间范围}    获取web参数上下限范围_有单位    均充最长时间
    ${均充末期维持时间}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间范围}[1]+1
    ...    ELSE    evaluate    ${均充末期维持时间范围}[1]+3
    ${均充最短时间}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间范围}[1]+1
    ...    ELSE    evaluate    ${均充最短时间范围}[1]+3
    ${均充最长时间}    run keyword if    '${均充最长时间范围}[3]'== 'Hour'    evaluate    ${均充最长时间范围}[1]+1
    ...    ELSE    evaluate    ${均充最长时间范围}[1]+5
    设置web参数量    均充末期维持时间    ${均充末期维持时间}
    设置web参数量    均充最短时间    ${均充最短时间}
    设置web参数量    均充最长时间    ${均充最长时间}
    ${均充末期维持时间转换后}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间}*60
    ...    ELSE    set variable    ${均充末期维持时间}
    ${均充最短时间转换后}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间}*60
    ...    ELSE    set variable    ${均充最短时间}
    ${均充最长时间转换后}    run keyword if    '${均充最长时间范围}[3]'== 'Hour'    evaluate    ${均充最长时间}*60
    ...    ELSE    set variable    ${均充最长时间}
    log    ===单位换算完成===
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动均充
    Wait Until Keyword Succeeds    10    1    信号量数据值为    电池管理状态    均充
    sleep    1m
    ${电池检测周期}    获取web参数量    电池检测周期
    ${下次检测时间}    获取web实时数据    下次检测时间
    ${下次检测时间0}    Subtract Time From Date    ${下次检测时间}    5s    #比1:00:00差5s
    设置系统时间    ${下次检测时间0}
    sleep    1m
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    均充
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    均充
    Wait Until Keyword Succeeds    10    1    信号量数据值为    电池管理状态    检测
    ${下次检测时间_cal}    add time to date    ${下次检测时间}    ${电池检测周期}d    exclude_millis=yes
    sleep    2m
    ${下次检测时间_new}    获取web实时数据    下次检测时间
    should be equal    ${下次检测时间_new}    ${下次检测时间_cal}
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池测试周期    电池均充周期    电池检测周期    均充最长时间
    ...    均充最短时间    均充末期维持时间    电池检测持续时间

停电时被动检测
    
    电池管理初始化
    设置web参数量    电池均充周期    0    #默认90
    设置web参数量    电池测试周期    0    #默认0天
    设置web参数量    电池检测周期    30    #默认0天
    设置web参数量    电池检测持续时间    2
    sleep    5
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    sleep    2m
    Wait Until Keyword Succeeds    5m10s    2    信号量数据值不为    电池管理状态    检测
    ${下次检测时间1}    获取web实时数据    下次检测时间
    ${电池检测周期}    获取web参数量    电池检测周期
    ${设置系统时间}    Subtract Time From Date    ${下次检测时间1}    15 days
    设置系统时间    ${设置系统时间}
    sleep    10
    关闭交流源输出
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    系统停电
    sleep    10
    ${下次检测时间_cal}    add time to date    ${设置系统时间}    ${电池检测周期}d    exclude_millis=yes
    ${下次检测时间_new}    获取web实时数据    下次检测时间
    should be equal    ${下次检测时间_new}    ${下次检测时间_cal}
    打开交流源输出
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池均充周期    电池测试周期    电池检测周期    电池检测持续时间
    ...    AND    打开交流源输出

检测周期对下次检测时间影响
    
    电池管理初始化
    设置web参数量    电池均充周期    0    #默认90
    设置web参数量    电池测试周期    0    #默认0天
    设置web参数量    电池检测周期    30    #默认0
    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    sleep    2m
    Wait Until Keyword Succeeds    5m10s    2    信号量数据值不为    电池管理状态    检测
    ${电池检测周期}    获取web参数量    电池检测周期
    ${下次检测时间}    获取web实时数据    下次检测时间
    ${下次检测时间0}    Subtract Time From Date    ${下次检测时间}    5s    #比1:00:00差5s
    Wait Until Keyword Succeeds    10    1    设置系统时间    ${下次检测时间0}
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    检测
    ${下次检测时间_cal}    add time to date    ${下次检测时间}    ${电池检测周期}d    exclude_millis=yes
    sleep    5
    ${下次检测时间_new}    获取web实时数据    下次检测时间
    should be equal    ${下次检测时间_new}    ${下次检测时间_cal}
    设置web参数量    电池检测周期    40    #默认0
    ${电池检测周期1}    获取web参数量    电池检测周期
    ${下次检测时间_cal1}    add time to date    ${下次检测时间}    ${电池检测周期1}d    exclude_millis=yes
    sleep    5
    ${下次检测时间_new1}    获取web实时数据    下次检测时间
    should be equal    ${下次检测时间_new1}    ${下次检测时间_cal1}
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池均充周期    电池测试周期    电池检测周期    电池检测持续时间

周期检测时设置检测周期为0
    
    电池管理初始化
    设置web参数量    电池均充周期    0    #默认90
    设置web参数量    电池测试周期    0    #默认0天
    设置web参数量    电池检测周期    30    #默认0
    设置web参数量    电池检测持续时间    2
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    sleep    2m
    Wait Until Keyword Succeeds    5m10s    2    信号量数据值不为    电池管理状态    检测
    ${电池检测周期}    获取web参数量    电池检测周期
    ${下次检测时间}    获取web实时数据    下次检测时间
    ${下次检测时间0}    Subtract Time From Date    ${下次检测时间}    5s    #比1:00:00差5s
    Wait Until Keyword Succeeds    10    1    设置系统时间    ${下次检测时间0}
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    检测
    ${下次检测时间_cal}    add time to date    ${下次检测时间}    ${电池检测周期}d    exclude_millis=yes
    sleep    5
    ${下次检测时间_new}    获取web实时数据    下次检测时间
    should be equal    ${下次检测时间_new}    ${下次检测时间_cal}
    ${检测时长l}    获取web实时数据    电池状态持续时间
    should be true    ${检测时长l} < 2
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池检测周期    0    #默认0天
    sleep    1m
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    检测
    ${检测时长newl}    获取web实时数据    电池状态持续时间
    should be true    ${检测时长l} <= ${检测时长newl}
    ${下次检测时间3}    run keyword and ignore error    获取web实时数据    下次检测时间
    should be true    '${下次检测时间3}[0]' == 'PASS' and '${下次检测时间3}[1]' == ''
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池均充周期    电池测试周期    电池检测周期    电池检测持续时间
