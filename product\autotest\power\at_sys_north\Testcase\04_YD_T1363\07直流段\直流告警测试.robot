*** Settings ***
Suite Setup       run keywords    连接CSU
...               AND    主动告警测试前置条件    ${CSU_role}
...               AND    测试用例前置条件
Suite Teardown    run keywords    设置web设备参数量为默认值    CSU主动告警使能
...               AND    测试用例后置条件
Default Tags      T1-1
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
1363_0006_1直流电压低告警
    [Documentation]    直流电压高范围：53-59，默认58.5；>=直流电压低+1，<=直流电压过高。。。
    ...    直流电压低范围：44-55，默认46.5；<=直流电压高-1，>=直流电压过低。。。
    ...    直流电压过高：53-59，默认59。。。
    ...    直流电压过低：44-55，默认46.。。
    [Setup]    重置电池模拟器输出
    关闭交流源输出
    实时告警刷新完成
    #获取参数/默认值
    #获取告警级别和输出干接点设置值
    ${级别设置值}    获取web参数量    直流电压低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    直流电压低    严重
    ${直流电压低告警}    判断告警不存在_带返回值    直流电压低
    run keyword if    '${直流电压低告警}' != '[]'    向上调节电池电压    53.5
    ${可设置范围}    获取web参数可设置范围    直流电压低阈值
    设置web参数量    直流电压低阈值    ${可设置范围}[1]
    ${ 直流电压低阈值}    获取web参数量    直流电压低阈值
    向下调节电池电压    ${ 直流电压低阈值}-0.5
    wait until keyword succeeds    5m    2    查询指定告警信息    直流电压低
    wait until keyword succeeds    30    1    设置web参数量    直流电压低    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    直流电压低
    ${直流电压低告警}    判断告警存在_带返回值    直流电压低
    should not be true    ${直流电压低告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    直流电压低    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    直流电压低
        sleep    5
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=44H>>    直流电压告警    1    None    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${直流电压低告警级别}    获取web告警属性    直流电压低    告警级别
        should be equal    ${告警级别}    ${直流电压低告警级别}
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=81H>>    直流电压低    None    ${g_ver_1363}
        ${1104告警级别}    获取1104告警级别取值约定    直流电压低    ${1104告警级别}
        should be equal    ${告警级别}    ${1104告警级别}
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别}'=='主要'    主动告警上送判断2    1363    直流配电    直流电压低
    END
    设置web参数量    直流电压低    主要
    向上调节电池电压    ${ 直流电压低阈值}+1
    wait until keyword succeeds    5m    1    判断告警不存在    直流电压低
    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=44H>>    直流电压告警    1    None    ${g_ver_1363}
    should not be true    ${1104告警结果}
    [Teardown]    Run keywords    直流电压参数恢复默认值
    ...    AND    设置web参数量    直流电压低    主要
    ...    AND    重置电池模拟器输出

1363_0008_2直流电压过低告警
    [Documentation]    直流电压高范围：53-59，默认58.5；>=直流电压低+1，<=直流电压过高。。。
    ...    直流电压低范围：44-55，默认46.5；<=直流电压高-1，>=直流电压过低。。。
    ...    直流电压过高：53-59，默认59。。。
    ...    直流电压过低：44-55，默认46.。。
    ...
    ...    子表无此告警级别
    [Setup]    重置电池模拟器输出
    关闭交流源输出
    实时告警刷新完成
    #获取告警级别和输出干接点设置值
    ${级别设置值}    获取web参数量    直流电压过低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    直流电压过低    严重
    Comment    wait until keyword succeeds    5m    1    查询指定告警信息不为    直流电压高
    ${直流电压过高告警}    判断告警不存在_带返回值    直流电压过低
    run keyword if    '${直流电压过高告警}' != '[]'    向上调节电池电压    53.5
    ${可设置范围}    获取web参数可设置范围    直流电压低阈值
    设置web参数量    直流电压低阈值    ${可设置范围}[1]
    ${可设置范围}    获取web参数可设置范围    直流电压过低阈值
    设置web参数量    直流电压过低阈值    ${可设置范围}[1]
    ${ 直流电压过低阈值}    获取web参数量    直流电压过低阈值
    向下调节电池电压    ${ 直流电压过低阈值}-0.5
    wait until keyword succeeds    5m    2    查询指定告警信息    直流电压过低
    wait until keyword succeeds    30    1    设置web参数量    直流电压过低    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    直流电压过低
    ${直流电压过低告警}    判断告警存在_带返回值    直流电压过低
    should not be true    ${直流电压过低告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    直流电压过低    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    直流电压过低
        sleep    5
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=44H>>    直流电压告警    1    None    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${直流电压低告警级别}    获取web告警属性    直流电压过低    告警级别
        should be equal    ${告警级别}    ${直流电压低告警级别}
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别}'=='主要'    主动告警上送判断2    1363    直流配电    直流电压过低
    END
    wait until keyword succeeds    30    1    设置web参数量    直流电压过低    主要
    向上调节电池电压    ${ 直流电压过低阈值}+1
    wait until keyword succeeds    5m    1    判断告警不存在    直流电压过低
    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=44H>>    直流电压告警    1    None    ${g_ver_1363}
    should not be true    ${1104告警结果}
    [Teardown]    Run keywords    直流电压参数恢复默认值
    ...    AND    设置web参数量    直流电压过低    主要
    ...    AND    重置电池模拟器输出

1363_0010_3直流电压高告警
    [Setup]    重置电池模拟器输出
    关闭交流源输出
    实时告警刷新完成
    #获取参数/默认值
    #获取告警级别和输出干接点设置值
    ${级别设置值}    获取web参数量    直流电压高
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    直流电压高    严重
    ${直流电压高告警}    判断告警不存在_带返回值    直流电压高
    run keyword if    '${直流电压高告警}' != '[]'    向下调节电池电压    52
    ${可设置范围}    获取web参数可设置范围    直流电压高阈值
    设置web参数量    直流电压高阈值    ${可设置范围}[0]
    ${ 直流电压高阈值}    获取web参数量    直流电压高阈值
    向上调节电池电压    ${ 直流电压高阈值}+0.5
    Comment    sleep    3m
    Comment    ${直流电压}    获取web实时数据    直流电压
    Comment    ${ 直流电压高阈值}    获取web参数量    直流电压高阈值
    wait until keyword succeeds    3m    2    查询指定告警信息    直流电压高
    wait until keyword succeeds    30    1    设置web参数量    直流电压高    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    直流电压高
    ${直流电压高告警}    判断告警存在_带返回值    直流电压高
    should not be true    ${直流电压高告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    直流电压高    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    直流电压高
        sleep    5
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=44H>>    直流电压告警    2    None    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${直流电压高告警级别}    获取web告警属性    直流电压高    告警级别
        should be equal    '${告警级别}'    '${直流电压高告警级别}'
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=81H>>    直流电压高    None    ${g_ver_1363}
        ${1104告警级别}    获取1104告警级别取值约定    直流电压高    ${1104告警级别}
        should be equal    ${告警级别}    ${1104告警级别}
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别}'=='主要'    主动告警上送判断2    1363    直流配电    直流电压高
    END
    wait until keyword succeeds    30    1    设置web参数量    直流电压高    主要
    向下调节电池电压    ${ 直流电压高阈值}-0.5
    wait until keyword succeeds    5m    1    判断告警不存在    直流电压高
    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=44H>>    直流电压告警    2    None    ${g_ver_1363}
    should not be true    ${1104告警结果}
    [Teardown]    Run keywords    直流电压参数恢复默认值
    ...    AND    设置web参数量    直流电压高干接点    0
    ...    AND    设置web参数量    直流电压高    主要
    ...    AND    重置电池模拟器输出
    ...    AND    打开交流源输出

1363_0012_4直流电压过高告警
    [Documentation]    子表无此告警级别
    [Setup]    重置电池模拟器输出
    关闭交流源输出
    实时告警刷新完成
    #获取参数/默认值
    #获取告警级别和输出干接点设置值
    ${级别设置值}    获取web参数量    直流电压过高
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    直流电压过高    严重
    ${直流电压过高告警}    判断告警不存在_带返回值    直流电压过高
    run keyword if    '${直流电压过高告警}' != '[]'    向下调节电池电压    52
    ${可设置范围}    获取web参数可设置范围    直流电压高阈值
    设置web参数量    直流电压高阈值    ${可设置范围}[0]
    ${可设置范围}    获取web参数可设置范围    直流电压过高阈值
    设置web参数量    直流电压过高阈值    ${可设置范围}[0]
    ${ 直流电压过高阈值}    获取web参数量    直流电压过高阈值
    向上调节电池电压    ${ 直流电压过高阈值}+0.5
    wait until keyword succeeds    5m    2    查询指定告警信息    直流电压过高
    wait until keyword succeeds    30    1    设置web参数量    直流电压过高    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    直流电压过高
    ${直流电压过高告警}    判断告警存在_带返回值    直流电压过高
    should not be true    ${直流电压过高告警}
    FOR    ${告警级别}    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    直流电压过高    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    直流电压过高
        sleep    5
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=44H>>    直流电压告警    2    None    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${直流电压低告警级别}    获取web告警属性    直流电压过高    告警级别
        should be equal    ${告警级别}    ${直流电压低告警级别}
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别}'=='主要'    主动告警上送判断2    1363    直流配电    直流电压过高
    END
    设置web参数量    直流电压过高    主要
    向下调节电池电压    ${ 直流电压过高阈值}-0.5
    wait until keyword succeeds    5m    1    判断告警不存在    直流电压过高
    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=44H>>    直流电压告警    2    None    ${g_ver_1363}
    should not be true    ${1104告警结果}
    [Teardown]    Run keywords    直流电压参数恢复默认值
    ...    AND    设置web参数量    直流电压过高    主要
    ...    AND    重置电池模拟器输出

1363_0014_6电池温度低告警
    [Tags]    3
    [Documentation]    与“1363_0002_1电池温度低告警-1”重复
    同时设置三相电压频率    220    50
    打开交流源输出
    连接CSU
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    电池温度低
    #获取告警级别和输出干接点设置值
    ${级别设置值}    获取web参数量    电池温度低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池温度低    严重
    sleep    3
    ${级别设置值}    获取web参数量    电池温度低
    #（1）告警产生
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    -10    0.001    电池_1    电池温度
    #产生电池温度低告警
    ${电池温度低阈值范围}    获取web参数上下限范围    电池温度低阈值
    ${电池温度低阈值可设置范围}    获取web参数可设置范围    电池温度低阈值
    设置web参数量    电池温度低阈值    ${电池温度低阈值可设置范围}[1]
    ${电池温度}    获取web实时数据    电池温度-1
    Wait Until Keyword Succeeds    10    1    判断告警存在    电池温度低
    sleep    3
    #只是通过调节温度零点来改变温度，如果室温较高时，无法达到告警点，视为正常
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    电池温度低    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池温度低
        sleep    5
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=44H>>    电池温度告警-1    1    None    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${电池温度低告警级别}    获取web告警属性    电池温度低-1    告警级别
        should be equal    ${告警级别设置}    ${电池温度低告警级别}
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=81H>>    电池温度低告警    None    ${g_ver_1363}
        ${1104告警级别}    获取1104告警级别取值约定    电池温度低-1    ${1104告警级别}
        should be equal    ${告警级别设置}    ${1104告警级别}
        ${web实时告警名}    由子工具获取web实时告警名称    电池温度低-1
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    1363    电池_1    ${web实时告警名}
    END
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池温度低    主要
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    #（2）告警恢复
    设置web参数量    电池温度低阈值    ${电池温度低阈值范围}[0]
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池温度低
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池温度低阈值
    ...    AND    设置web参数量    电池温度低    主要

1363_0016_6电池温度高告警
    [Tags]    3
    [Documentation]    与“1363_0004_2电池温度高告警-1”重复
    同时设置三相电压频率    220    50
    打开交流源输出
    连接CSU
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    电池温度高
    #获取告警级别和输出干接点设置值
    ${级别设置值}    获取web参数量    电池温度高
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池温度高    严重
    sleep    3
    ${级别设置值}    获取web参数量    电池温度高
    #（1）告警产生
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    10    1.6    电池_1    电池温度
    #产生电池温度高告警
    ${电池温度高阈值范围}    获取web参数上下限范围    电池温度高阈值
    ${电池温度高阈值可设置范围}    获取web参数可设置范围    电池温度高阈值
    设置web参数量    电池温度高阈值    ${电池温度高阈值可设置范围}[0]
    ${电池温度}    获取web实时数据    电池温度-1
    Wait Until Keyword Succeeds    10    1    判断告警存在    电池温度高
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    电池温度高    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池温度高
        sleep    3
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=44H>>    电池温度告警-1    2    None    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${电池温度高告警级别}    获取web告警属性    电池温度高-1    告警级别
        should be equal    ${告警级别设置}    ${电池温度高告警级别}
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=81H>>    电池温度高告警    None    ${g_ver_1363}
        ${1104告警级别}    获取1104告警级别取值约定    电池温度高-1    ${1104告警级别}
        should be equal    ${电池温度高告警级别}    ${1104告警级别}
        ${web实时告警名}    由子工具获取web实时告警名称    电池温度高-1
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    1363    电池_1    ${web实时告警名}
    END
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池温度高    主要
    #（2）告警恢复
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    设置web参数量    电池温度高阈值    ${电池温度高阈值范围}[0]
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池温度高
    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流段数字量~CID1=40H>>    <<获取交流段数字量~CID2=43H>>    电池温度告警-1    2    None    ${g_ver_1363}
    should not be true    ${1104告警结果}
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池温度高阈值
    ...    AND    设置web参数量    电池温度高    主要

1363_0018_7电池温度无效告警
    [Tags]    3
    [Documentation]    与“1363_0006_3电池温度无效告警”重复
    #电池2不接入传感器
    设置通道无效值/恢复通道原始值    ${plat.batttemps}[0]    1
    实时告警刷新完成
    设置web参数量    电池温度无效    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    电池温度无效
    ${电池温度无效告警}    判断告警存在_带返回值    电池温度无效
    should not be true    ${电池温度无效告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        设置web参数量    电池温度无效    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池温度无效
        sleep    5
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取VRLA段告警量~CID1=46H>>    <<获取VRLA段告警量~CID2=44H>>    电池温度告警-1    80    None    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${电池温度无效告警级别}    获取web告警属性    电池温度无效-1    告警级别
        should be equal    ${告警级别}    ${电池温度无效告警级别}
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取VRLA段告警级别~CID1=46H>>    <<获取VRLA段告警级别~CID2=81H>>    电池温度无效    None    ${g_ver_1363}
        ${1104告警级别}    获取1104告警级别取值约定    电池温度无效    ${1104告警级别}
        should be equal    ${电池温度无效告警级别}    ${1104告警级别}
        ${web实时告警名}    由子工具获取web实时告警名称    电池温度无效
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别}'=='主要'    主动告警上送判断2    1363    电池_1    ${web实时告警名}
    END
    设置web参数量    电池温度无效    主要
    #无告警后查询判断
    Comment    Wait Until Keyword Succeeds    30    1    设置web参数量    电池组容量_2    0
    Comment    wait until keyword succeeds    5m    1    判断告警不存在    电池温度无效
    Comment    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=44H>>    电池温度告警-2    E3    None    ${g_ver_1363}
    Comment    should not be true    ${1104告警结果}
    [Teardown]    Run Keywords    设置web参数量    电池温度无效    主要
    ...           AND     设置通道无效值/恢复通道原始值    ${plat.batttemps}[0]    0

1363_0020_8电池放电告警
    电池管理初始化
    #获取告警级别和输出干接点设置
    ${电池放电级别}    获取web参数量    电池放电
    run keyword if    '${电池放电级别}'=='屏蔽'    设置web参数量    电池放电    严重
    sleep    3
    ${电池放电级别}    获取web参数量    电池放电
    ${放电阈值获取范围}    获取web参数上下限范围    电池放电阈值
    设置web参数量    电池放电阈值    ${放电阈值获取范围}[0]
    关闭交流源输出
    设置负载电压电流    53.5    20
    打开负载输出
    #电池放电
    run keyword and ignore error    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    系统停电
    ${电池电流}    获取web实时数据    电池电流-1
    wait until keyword succeeds    5m    2    判断告警存在    电池放电
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    电池放电    ${告警级别}
        sleep    3
        wait until keyword succeeds    1m    1    查询指定告警信息    电池放电
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=44H>>    电池放电告警-1    E4    None    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${电池测试失败告警级别}    获取web告警属性    电池放电-1    告警级别
        should be equal    ${告警级别}    ${电池测试失败告警级别}
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=81H>>    电池放电告警    None    ${g_ver_1363}
        ${1104告警级别}    获取1104告警级别取值约定    电池放电-1    ${1104告警级别}
        should be equal    ${告警级别}    ${1104告警级别}
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别}'=='主要'    主动告警上送判断2    1363    直流配电    电池放电-1
    END
    设置web参数量    电池放电    主要
    #恢复
    打开交流源输出
    关闭负载输出
    Wait Until Keyword Succeeds    30    1    信号量数据值不为    电池管理状态    系统停电
    wait until keyword succeeds    3m    2    判断告警不存在    电池放电
    #判断1363告警存在与否
    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=44H>>    电池放电告警-1    E4    None    ${g_ver_1363}
    should not be true    ${1104告警结果}
    [Teardown]    Run keywords    设置web设备参数量为默认值    电池放电阈值
    ...    AND    设置web参数量    电池放电    主要
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出

1363_0022_9电池测试失败告警
    电池管理初始化
    #获取告警级别和输出干接点设置
    ${电池测试失败级别}    获取web参数量    电池测试失败
    run keyword if    '${电池测试失败级别}'=='屏蔽'    设置web参数量    电池测试失败    严重
    sleep    3
    ${电池测试失败级别}    获取web参数量    电池测试失败
    ${缺省值}    获取web参数上下限范围_有单位    测试最长时间
    ${可设置范围}    获取web参数可设置范围    测试最长时间
    ${测试最长时间设置值}    run keyword if    '${缺省值}[3]'== 'Hour'    evaluate    ${可设置范围}[0]+1
    ...    ELSE    evaluate    ${可设置范围}[0]+5
    ${测试最长时间转换后}    run keyword if    '${缺省值}[3]'== 'Hour'    evaluate    ${测试最长时间设置值}*60
    ...    ELSE    set variable    ${测试最长时间设置值}
    设置web参数量    测试最长时间    ${测试最长时间设置值}
    #断开电池，以便电池检测异常
    设置负载电压电流    53.5    15
    打开负载输出
    断开电池模拟器
    连接电池模拟器
    关闭电池模拟器输出
    sleep    3
    #启动电池测试
    Wait Until Keyword Succeeds    2m    1    设置web控制量    启动测试
    run keyword and ignore error    Wait Until Keyword Succeeds    10    1    信号量数据值为    电池管理状态    测试
    wait until keyword succeeds    3m    2    判断告警存在    电池测试失败
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    电池测试失败    ${告警级别}
        sleep    3
        wait until keyword succeeds    1m    1    查询指定告警信息    电池测试失败
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=44H>>    电池测试失败-1    E5    None    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${电池测试失败告警级别}    获取web告警属性    电池测试失败-1    告警级别
        should be equal    ${告警级别}    ${电池测试失败告警级别}
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=81H>>    电池测试失败    None    ${g_ver_1363}
        ${1104告警级别}    获取1104告警级别取值约定    电池测试失败-1    ${1104告警级别}
        should be equal    ${告警级别}    ${1104告警级别}
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别}'=='主要'    主动告警上送判断2    1363    电池_1    电池测试失败
    END
    设置web参数量    电池测试失败    主要
    #恢复
    关闭负载输出
    仅电池模拟器供电
    同时设置三相电压频率    220    50
    打开交流源输出
    连接CSU
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    交流供电状态    市电1
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    停电
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    0
    设置web参数量    均充使能    允许
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动测试
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    测试
    ${等待时间}    evaluate    ${测试最长时间转换后}+2
    Wait Until Keyword Succeeds    ${等待时间}m    1    信号量数据值为    电池管理状态    均充
    wait until keyword succeeds    3m    2    判断告警不存在    电池测试失败
    Wait Until Keyword Succeeds    2m    1    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    #判断1363告警存在与否
    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=44H>>    电池测试失败-1    E5    None    ${g_ver_1363}
    should not be true    ${1104告警结果}
    [Teardown]    Run keywords    设置web设备参数量为默认值    测试最长时间
    ...    AND    设置web参数量    电池测试失败    主要
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出
    ...    AND    重置电池模拟器输出

1363_0024_10一次下电告警
    [Documentation]    一次下电告警不能设置为屏蔽级别，因此注释掉设置告警级别为0的语句。
    [Setup]    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息不为    一次下电告警
    关闭交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    一次下电告警
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
        设置web参数量    电池组容量_${i}    0
    END
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    99
    sleep    2
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    30    1    设置web参数量    下电模式    电池电压    #默认1电池电压
    Wait Until Keyword Succeeds    30    1    设置web参数量    下电控制延时    0    #默认0
    Wait Until Keyword Succeeds    30    1    设置web参数量    负载一次下电使能    允许    #默认1允许
    Wait Until Keyword Succeeds    30    1    设置web参数量    负载二次下电使能    禁止    #默认1允许
    显示属性配置    一次下电控制状态    数字量    web_attr=On    gui_attr=On
    ${恢复时间可设置范围}    获取web参数可设置范围    一次下电恢复时间
    run keyword and ignore error    设置web参数量    一次下电恢复时间    ${恢复时间可设置范围}[0]
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${可设置范围}[1]
    ${电压低可设置范围}    获取web参数可设置范围    电池电压低阈值
    设置web参数量    电池电压低阈值    ${电压低可设置范围}[1]
    ${电池下电电压可设置范围}    获取web参数可设置范围    负载一次下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载一次下电电压    ${电池下电电压可设置范围}[1]
    设置负载电压电流    53.5    10
    打开负载输出
    ${一次下电电压设置值}    获取web参数量    负载一次下电电压
    ${一次下电电压}    evaluate    ${一次下电电压设置值}-0.5
    向下调节电池电压    ${一次下电电压}
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    一次下电控制状态    动作
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    一次下电告警    ${告警级别设置}
        sleep    3
        wait until keyword succeeds    1m    1    查询指定告警信息    一次下电告警
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=44H>>    一次下电告警    E7    None    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${一次下电告警级别}    获取web告警属性    一次下电告警    告警级别
        should be equal    ${告警级别设置}    ${一次下电告警级别}
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=81H>>    一次下电告警    None    ${g_ver_1363}
        ${1104告警级别}    获取1104告警级别取值约定    一次下电告警    ${1104告警级别}
        should be equal    ${告警级别设置}    ${1104告警级别}
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    1363    电池组    一次下电告警
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    一次下电告警    严重    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    向上调节电池电压    53.5
    关闭负载输出
    打开交流源输出
    Wait Until Keyword Succeeds    8m    1    信号量数据值为    一次下电控制状态    恢复
    Wait Until Keyword Succeeds    8m    2    查询指定告警信息不为    一次下电告警
    显示属性配置    一次下电控制状态    数字量    web_attr=Off    gui_attr=Off
    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=44H>>    一次下电告警    E7    None    ${g_ver_1363}
    should not be true    ${1104告警结果}
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    负载一次下电电压    测试终止电压    电池电压低阈值    一次下电恢复时间
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出
    ...    AND    重置电池模拟器输出

1363_0026_11二次下电告警
    [Documentation]    二次下电告警不能设置为屏蔽级别，因此注释掉设置告警级别为0的语句。
    [Setup]    重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    5
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息不为    二次下电告警
    关闭交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    二次下电告警
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
        设置web参数量    电池组容量_${i}    0
    END
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    99
    sleep    2
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    100
    wait until keyword succeeds    30    1    设置web参数量    下电模式    电池电压    #默认1电池电压
    wait until keyword succeeds    30    1    设置web参数量    下电控制延时    0    #默认0
    wait until keyword succeeds    30    1    设置web参数量    负载一次下电使能    禁止    #默认1允许
    wait until keyword succeeds    30    1    设置web参数量    负载二次下电使能    允许    #默认1允许
    显示属性配置    二次下电控制状态    数字量    web_attr=On    gui_attr=On
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${可设置范围}[1]
    ${电压低可设置范围}    获取web参数可设置范围    电池电压低阈值
    设置web参数量    电池电压低阈值    ${电压低可设置范围}[1]
    ${恢复时间可设置范围}    获取web参数可设置范围    二次下电恢复时间
    run keyword and ignore error    设置web参数量    二次下电恢复时间    ${恢复时间可设置范围}[0]
    ${二次下电电压可设置范围}    获取web参数可设置范围    负载二次下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电电压    ${二次下电电压可设置范围}[1]
    设置负载电压电流    53.5    10
    打开负载输出
    ${下电电压设置值}    获取web参数量    负载二次下电电压
    ${下电电压}    evaluate    ${下电电压设置值}-0.5
    向下调节电池电压    ${下电电压}
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    二次下电控制状态    动作
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    二次下电告警    ${告警级别设置}
        sleep    3
        wait until keyword succeeds    1m    1    查询指定告警信息    二次下电告警
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=44H>>    二次下电告警    E8    None    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${二次下电告警级别}    获取web告警属性    二次下电告警    告警级别
        should be equal    ${告警级别设置}    ${二次下电告警级别}
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=81H>>    二次下电告警    None    ${g_ver_1363}
        ${1104告警级别}    获取1104告警级别取值约定    二次下电告警    ${1104告警级别}
        should be equal    ${告警级别设置}    ${1104告警级别}
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    1363    电池组    二次下电告警
    END
    wait until keyword succeeds    30    1    设置web参数量    二次下电告警    严重    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    向上调节电池电压    53.5
    关闭负载输出
    打开交流源输出
    Wait Until Keyword Succeeds    8m    1    信号量数据值为    二次下电控制状态    恢复
    Wait Until Keyword Succeeds    8m    2    查询指定告警信息不为    二次下电告警
    显示属性配置    二次下电控制状态    数字量    web_attr=Off    gui_attr=Off
    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=44H>>    二次下电告警    E8    None    ${g_ver_1363}
    should not be true    ${1104告警结果}
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    负载二次下电电压    测试终止电压    电池电压低阈值    负载一次下电使能    二次下电恢复时间
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出
    ...    AND    重置电池模拟器输出

1363_0032_电池电压低告警
    [Setup]    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    纯铅酸
    连接交流源
    连接电池模拟器
    重置电池模拟器输出
    打开电池模拟器输出
    sleep    10
    关闭交流源输出
    实时告警刷新完成
    #获取参数/默认值
    ${级别设置值}    获取web参数量    电池电压低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池电压低    严重
    ${电压低告警}    判断告警不存在_带返回值    电池电压低
    run keyword if    '${电压低告警}' != '[]'    向上调节电池电压    53.5
    ${可设置范围}    获取web参数可设置范围    电池电压低阈值
    设置web参数量    电池电压低阈值    ${可设置范围}[1]
    ${ 电压低阈值}    获取web参数量    电池电压低阈值
    向下调节电池电压    ${ 电压低阈值}-0.5
    wait until keyword succeeds    5m    2    查询指定告警信息    电池电压低
    wait until keyword succeeds    30    1    设置web参数量    电池电压低    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    电池电压低
    ${电压低告警}    判断告警存在_带返回值    电池电压低
    should not be true    ${电压低告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        设置web参数量    电池电压低    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池电压低
        sleep    5
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=44H>>    电池电压告警-1    1    None    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${直流电压低告警级别}    获取web告警属性    电池电压低-1    告警级别
        should be equal    ${告警级别}    ${直流电压低告警级别}
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=81H>>    电池电压低告警    None    ${g_ver_1363}
        ${1104告警级别}    获取1104告警级别取值约定    电池电压低-1    ${1104告警级别}
        should be equal    ${告警级别}    ${1104告警级别}
        ${web实时告警名}    由子工具获取web实时告警名称    电池电压低-1
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别}'=='主要'    主动告警上送判断2    1104    直流配电    ${web实时告警名}
    END
    设置web参数量    电池电压低    主要
    #无告警后查询判断
    向上调节电池电压    ${ 电压低阈值}+1
    wait until keyword succeeds    5m    1    判断告警不存在    电池电压低
    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=44H>>    电池电压告警-1    1    None    ${g_ver_1363}
    should not be true    ${1104告警结果}
    [Teardown]    Run keywords    设置web设备参数量为默认值    电池电压低阈值
    ...    AND    设置web参数量    电池电压低    主要
    ...    AND    打开交流源输出
    ...    AND    重置电池模拟器输出

1363_0034_电池电压过低告警_new
    [Setup]    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    纯铅酸
    连接交流源
    连接电池模拟器
    重置电池模拟器输出
    打开电池模拟器输出
    sleep    10
    关闭交流源输出
    实时告警刷新完成
    #获取参数/默认值
    ${级别设置值}    获取web参数量    电池电压过低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池电压过低    严重
    ${电压低告警}    判断告警不存在_带返回值    电池电压过低
    run keyword if    '${电压低告警}' != '[]'    向上调节电池电压    53.5
    ${可设置范围}    获取web参数可设置范围    电池电压过低阈值
    设置web参数量    电池电压过低阈值    ${可设置范围}[1]
    ${ 电压低阈值}    获取web参数量    电池电压过低阈值
    向下调节电池电压    ${ 电压低阈值}-0.5
    wait until keyword succeeds    5m    2    查询指定告警信息    电池电压过低
    wait until keyword succeeds    30    1    设置web参数量    电池电压过低    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    电池电压过低
    ${电压低告警}    判断告警存在_带返回值    电池电压过低
    should not be true    ${电压低告警}
    设置web参数量    电池电压过低    主要
    wait until keyword succeeds    1m    1    查询指定告警信息    电池电压过低
    sleep    5
    #1363查询告警存在
    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=44H>>    电池电压告警-1    1    None    ${g_ver_1363}
    should be true    ${1104告警结果}
    #1363查询告警级别与web端级别
    ${直流电压低告警级别}    获取web告警属性    电池电压过低-1    告警级别
    should be equal    主要    ${直流电压低告警级别}
    ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=81H>>    直流电压低    None    ${g_ver_1363}
    ${1104告警级别}    获取1104告警级别取值约定    电池电压过低-1    ${1104告警级别}
    should be equal    主要    ${1104告警级别}
    Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client'    主动告警上送判断2    1363    直流配电    电池电压过低-1
    设置web参数量    电池电压过低    主要
    #无告警后查询判断
    向上调节电池电压    ${ 电压低阈值}+1
    wait until keyword succeeds    5m    1    判断告警不存在    电池电压过低
    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=44H>>    电池电压告警-1    1    None    ${g_ver_1363}
    should not be true    ${1104告警结果}
    [Teardown]    Run keywords    设置web设备参数量为默认值    电池电压过低阈值
    ...    AND    设置web参数量    电池电压过低    主要
    ...    AND    打开交流源输出
    ...    AND    重置电池模拟器输出

1363_0036_12电池下电告警
    [Documentation]    电池下电告警不能设置为屏蔽级别，因此注释掉设置告警级别为0的语句。
    [Tags]    no test    T1-1
    [Setup]   重置电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    5
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息不为    电池下电告警
    关闭交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    电池下电告警
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
        设置web参数量    电池组容量_${i}    0
    END
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    99
    sleep    2
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电模式    电池电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电控制延时    0
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载一次下电使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池下电使能    允许
    显示属性配置    电池下电控制状态    数字量    web_attr=On    gui_attr=On
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${可设置范围}[1]
    ${电压低可设置范围}    获取web参数可设置范围    电池电压低阈值
    设置web参数量    电池电压低阈值    ${电压低可设置范围}[1]
    ${下电电压可设置范围}    获取web参数可设置范围    电池下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池下电电压    ${下电电压可设置范围}[1]
    设置负载电压电流    53.5    10
    打开负载输出
    ${下电电压设置值}    获取web参数量    电池下电电压
    ${下电电压}    evaluate    ${下电电压设置值}-0.5
    向下调节电池电压    ${下电电压}
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池下电控制状态    动作
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    电池下电告警    ${告警级别}
        sleep    3
        wait until keyword succeeds    1m    1    查询指定告警信息    电池下电告警
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=44H>>    电池下电告警    EA    None    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${电池下电告警级别}    获取web告警属性    电池下电告警    告警级别
        should be equal    ${告警级别}    ${电池下电告警级别}
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=81H>>    电池下电告警    None    ${g_ver_1363}
        ${1104告警级别}    获取1104告警级别取值约定    电池下电告警    ${1104告警级别}
        should be equal    ${告警级别}    ${1104告警级别}
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别}'=='主要'    主动告警上送判断2    1363    直流配电    电池下电告警
    END
    设置web参数量    电池下电告警    严重
    向上调节电池电压    53.5
    关闭负载输出
    打开交流源输出
    设置web设备参数量为默认值    电池下电电压    测试终止电压    电池电压低阈值    负载一次下电使能    负载二次下电使能
    重置电池模拟器输出
    Comment    Wait Until Keyword Succeeds    8m    1    信号量数据值为    电池下电控制状态    恢复
    显示属性配置    电池下电控制状态    数字量    web_attr=Off    gui_attr=Off
    Wait Until Keyword Succeeds    8m    2    查询指定告警信息不为    电池下电告警
    ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取直流段告警~CID1=42H>>    <<获取直流段告警~CID2=44H>>    电池下电告警    EA    None    ${g_ver_1363}
    should not be true    ${1104告警结果}
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    电池下电电压    测试终止电压    电池电压低阈值    负载一次下电使能    负载二次下电使能
    ...    AND    设置web参数量    电池下电告警干接点    0
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出
    ...    AND    重置电池模拟器输出

1363_0038_一次下电分路断告警测试
    实时告警刷新完成
    ${级别设置值}    获取web参数量    一次下电分路断_1
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    一次下电分路断_1    主要
    wait until keyword succeeds    1m    1    判断告警不存在    一次下电分路断_1
    设置web参数量    一次下电分路断_1    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    一次下电分路断_1
    ${告警}    判断告警存在_带返回值    一次下电分路断
    should not be true    ${告警}
    模拟数字量告警    下电告警    ON
    sleep    10
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    一次下电分路断_1    ${告警级别设置}
        wait until keyword succeeds    3m    1    查询指定告警信息    一次下电分路断_1
        sleep    5
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流段告警量~CID1=42H>>    <<获取交流段告警量~CID2=44H>>    一次下电分路断_1    03    None    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${交流停电告警级别}    获取web告警属性    一次下电分路断_1    告警级别
        should be equal    '${告警级别设置}'    '${交流停电告警级别}'
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取交流告警级别~CID1=42H>>    <<获取交流告警级别~CID2=81H>>    一次下电分路断    None    ${g_ver_1363}
        ${1104告警级别}    获取1104告警级别取值约定    一次下电分路断    ${1104告警级别}
        should be equal    ${告警级别设置}    ${1104告警级别}
        Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    1363    直流配电    一次下电分路断[1]
    END
    设置web参数量    一次下电分路断_1    次要
    [Teardown]    模拟数字量告警    下电告警    OFF

1363_0040_二次下电分路断告警测试
    实时告警刷新完成
    ${级别设置值}    获取web参数量    二次下电分路断_1
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    二次下电分路断_1    主要
    wait until keyword succeeds    1m    1    判断告警不存在    二次下电分路断_1
    设置web参数量    二次下电分路断_1    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    二次下电分路断_1
    ${告警}    判断告警存在_带返回值    二次下电分路断_1
    should not be true    ${告警}
    模拟数字量告警    下电告警    ON
    sleep    10
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    二次下电分路断_1    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    二次下电分路断_1
        sleep    5
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流段告警量~CID1=42H>>    <<获取交流段告警量~CID2=44H>>    二次下电分路断_1    03    None    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${交流停电告警级别}    获取web告警属性    二次下电分路断_1    告警级别
        should be equal    '${告警级别设置}'    '${交流停电告警级别}'
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取交流告警级别~CID1=42H>>    <<获取交流告警级别~CID2=81H>>    二次下电分路断    None    ${g_ver_1363}
        ${1104告警级别}    获取1104告警级别取值约定    二次下电分路断    ${1104告警级别}
        should be equal    ${告警级别设置}    ${1104告警级别}
        Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    1363    直流配电    二次下电分路断[1]
    END
    设置web参数量    二次下电分路断_1    次要
    [Teardown]    模拟数字量告警    下电告警    OFF

1363_0042_一次下电扩展分路断告警测试
    实时告警刷新完成
    ${级别设置值}    获取web参数量    一次下电扩展分路断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    一次下电扩展分路断    主要
    wait until keyword succeeds    1m    1    判断告警不存在    一次下电扩展分路断
    设置web参数量    一次下电扩展分路断    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    一次下电扩展分路断
    ${告警}    判断告警存在_带返回值    一次下电分路断
    should not be true    ${告警}
    模拟数字量告警    下电告警    ON
    sleep    10
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    一次下电扩展分路断    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    一次下电扩展分路断
        sleep    5
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流段告警量~CID1=42H>>    <<获取交流段告警量~CID2=44H>>    一次下电分路E断    03    None    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${交流停电告警级别}    获取web告警属性    一次下电扩展分路断    告警级别
        should be equal    '${告警级别设置}'    '${交流停电告警级别}'
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取交流告警级别~CID1=42H>>    <<获取交流告警级别~CID2=81H>>    一次下电扩展分路断    None    ${g_ver_1363}
        ${1104告警级别}    获取1104告警级别取值约定    一次下电扩展分路断    ${1104告警级别}
        should be equal    ${告警级别设置}    ${1104告警级别}
        ${web实时告警名}    由子工具获取web实时告警名称    一次下电扩展分路断
        Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    1363    直流配电    ${web实时告警名}
    END
    [Teardown]    run keywords    设置web参数量    一次下电扩展分路断    次要
    ...    AND    模拟数字量告警    下电告警    OFF

1363_0044_二次下电扩展分路断告警测试
    实时告警刷新完成
    ${级别设置值}    获取web参数量    二次下电扩展分路断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    二次下电扩展分路断    主要
    wait until keyword succeeds    1m    1    判断告警不存在    二次下电扩展分路断
    设置web参数量    二次下电扩展分路断    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    二次下电扩展分路断
    ${告警}    判断告警存在_带返回值    一次下电分路断
    should not be true    ${告警}
    模拟数字量告警    下电告警    ON
    sleep    10
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    二次下电扩展分路断    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    二次下电扩展分路断
        sleep    5
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流段告警量~CID1=42H>>    <<获取交流段告警量~CID2=44H>>    二次下电分路E断    03    None    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${交流停电告警级别}    获取web告警属性    二次下电扩展分路断    告警级别
        should be equal    '${告警级别设置}'    '${交流停电告警级别}'
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取交流告警级别~CID1=42H>>    <<获取交流告警级别~CID2=81H>>    二次下电扩展分路断    None    ${g_ver_1363}
        ${1104告警级别}    获取1104告警级别取值约定    二次下电扩展分路断    ${1104告警级别}
        should be equal    ${告警级别设置}    ${1104告警级别}
        Comment    设置web参数量    二次下电扩展分路断    次要
        ${web实时告警名}    由子工具获取web实时告警名称    二次下电扩展分路断
        Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    1363    直流配电    ${web实时告警名}
    END
    [Teardown]    run keywords    设置web参数量    二次下电扩展分路断    次要
    ...    AND    模拟数字量告警    下电告警    OFF

1363_0046_直流防雷异常测试
    实时告警刷新完成
    ${级别设置值}    获取web参数量    直流防雷器异常
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    直流防雷器异常    主要
    wait until keyword succeeds    1m    1    判断告警不存在    直流防雷器异常
    设置web参数量    直流防雷器异常    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    直流防雷器异常
    ${告警}    判断告警存在_带返回值    直流防雷器异常
    should not be true    ${告警}
    模拟数字量告警    直流防雷器异常    ON
    sleep    10
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    直流防雷器异常    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    直流防雷器异常
        sleep    5
        ${1104告警结果}    获取1104一条告警值并判断告警存在    ${g_prtcl_1363}    <<获取交流段告警量~CID1=42H>>    <<获取交流段告警量~CID2=44H>>    直流防雷异常    E1    None    ${g_ver_1363}
        should be true    ${1104告警结果}
        ${交流停电告警级别}    获取web告警属性    直流防雷器异常    告警级别
        should be equal    '${告警级别设置}'    '${交流停电告警级别}'
        ${1104告警级别}    获取一个数据的值    ${g_prtcl_1363}    <<获取交流告警级别~CID1=42H>>    <<获取交流告警级别~CID2=81H>>    直流防雷器异常    None    ${g_ver_1363}
        ${1104告警级别}    获取1104告警级别取值约定    直流防雷器异常    ${1104告警级别}
        should be equal    ${告警级别设置}    ${1104告警级别}
        ${web实时告警名}    由子工具获取web实时告警名称    直流防雷器异常
        Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    1363    直流配电    ${web实时告警名}
    END
    Comment    设置web参数量    直流防雷器异常    次要
    Comment    模拟数字量告警    直流防雷器异常    OFF
    [Teardown]    run keywords    设置web参数量    直流防雷器异常    次要
    ...    AND    模拟数字量告警    直流防雷器异常    OFF

1363_0052_一次下电分路断_X告警测试
    Comment    实时告警刷新完成
    FOR    ${参数个数}    IN RANGE    4    1    -1
        实时告警刷新完成
        ${级别设置值}    获取web参数量    一次下电分路断_${参数个数}
        run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    一次下电分路断_${参数个数}    主要
        wait until keyword succeeds    1m    1    判断告警不存在    一次下电分路断_${参数个数}
        设置web参数量    一次下电分路断_${参数个数}    屏蔽
        wait until keyword succeeds    1m    1    判断告警不存在    一次下电分路断_${参数个数}
        ${告警}    判断告警存在_带返回值    一次下电分路断
        should not be true    ${告警}
        模拟数字量告警    下电告警    ON
        sleep    10
        1363FOR循环告警级别测试    一次下电分路断_${参数个数}    一次下电分路断    一次下电分路断[${参数个数}]
        设置web参数量    一次下电分路断_${参数个数}    次要
        模拟数字量告警    下电告警    OFF
        sleep    30
    END
    [Teardown]    模拟数字量告警    下电告警    OFF

1363_0054_二次下电分路断_X告警测试
    Comment    实时告警刷新完成
    FOR    ${参数个数}    IN RANGE    3    1    -1
        实时告警刷新完成
        ${级别设置值}    获取web参数量    二次下电分路断_${参数个数}
        run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    二次下电分路断_${参数个数}    主要
        wait until keyword succeeds    1m    1    判断告警不存在    二次下电分路断_${参数个数}
        设置web参数量    二次下电分路断_${参数个数}    屏蔽
        wait until keyword succeeds    1m    1    判断告警不存在    二次下电分路断_${参数个数}
        ${告警}    判断告警存在_带返回值    二次下电分路断
        should not be true    ${告警}
        模拟数字量告警    下电告警    ON
        sleep    10
        1363FOR循环告警级别测试    二次下电分路断_${参数个数}    二次下电分路断    二次下电分路断[${参数个数}]
        设置web参数量    二次下电分路断_${参数个数}    次要
        模拟数字量告警    下电告警    OFF
        sleep    30
    END
    [Teardown]    模拟数字量告警    下电告警    OFF


