*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
snmp_0002_mains1analogData√
    ${比较结果}    对比数据_V2C    mains1analogData
    should be true    ${比较结果}

snmp_0004_mains1digitalData√
    ${比较结果}    对比数据_V2C    mains1digitalData
    should be true    ${比较结果}

snmp_0006_mains1alarm√
    ${比较结果}    对比告警_V2C    mains1alarm
    should be true    ${比较结果}

snmp_0008_mains1alarm_level√
    ${比较结果}    对比告警级别_V2C    mains1alarm
    should be true    ${比较结果}

snmp_0010_mains1alarm_level_write√
    Comment    ${比较结果}    对比告警_V2C    mains1alarm
    Comment    should be true    ${比较结果}
    ${比较结果}    批量设置四种/五种告警级别    mains1alarm
    should be true    ${比较结果}

snmp_0012_mains1stasticData√
    ${比较结果}    对比数据_V2C    mains1stasticData
    should be true    ${比较结果}

snmp_0014_mains2analogDataX
    [Documentation]    只有一路市电，市电2的SID在web中无，暂时不测此项
    [Tags]    3
    ${比较结果}    对比数据_V2C    mains2analogData
    should be true    ${比较结果}
    Log    只有一路市电，市电2的SID在web中无，暂时不测此项

snmp_0016_mains2digitalData√
    ${比较结果}    对比数据_V2C    mains2digitalData
    should be true    ${比较结果}

snmp_0018_mains2alarmX
    [Documentation]    只有一路市电，市电2的SID在web中无，暂时不测此项
    [Tags]    3
    ${比较结果}    对比告警_V2C    mains2alarm
    should be true    ${比较结果}

snmp_0020_mains2alarm_levelX
    ${比较结果}    对比告警级别_V2C    mains2alarm
    should be true    ${比较结果}

snmp_0022_mains2alarm_level_writeX
    [Documentation]    只有一路市电，市电2的SID在web中无，暂时不测此项
    [Tags]    3
    Comment    ${比较结果}    对比告警_V2C    mains2alarm
    Comment    should be true    ${比较结果}
    ${比较结果}    批量设置四种/五种告警级别    mains2alarm
    should be true    ${比较结果}

snmp_0024_mains2stasticData√
    [Documentation]    错在
    ...
    ...    ${web数据} = [{u'value': u'0', u'short_name': u'\u5e02\u7535\u5de5\u4f5c\u65f6\u95f4', u'valid_tag': u'0', u'full_name': u'\u5e02\u7535\u5de5\u4f5c\u65f6\u95f4', u'sid': u'1407514201686017'}, {u'value': u'0.00', u...
    ...
    ...    web数据为0.00
    ${比较结果}    对比数据_V2C    mains2stasticData
    should be true    ${比较结果}
