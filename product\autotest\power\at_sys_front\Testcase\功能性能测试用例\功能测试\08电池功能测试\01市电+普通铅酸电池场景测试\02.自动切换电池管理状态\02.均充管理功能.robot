*** Settings ***
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
均充时交流停电转停电
    
    电池管理初始化
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动均充
    设置负载电压电流    53.5    10
    打开负载输出
    Wait Until Keyword Succeeds    20    1    信号量数据值为    电池管理状态    均充
    关闭交流源输出
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    系统停电
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    系统停电
    关闭负载输出
    打开交流源输出
    [Teardown]    Run keywords    关闭负载输出
    ...    AND    打开交流源输出

均充时无正常工作整流器转停电
    [Documentation]    0:浮充/Float;1:均充/Equal;2:测试/Test;3:放电/Discharge;4:检测/Detect;5:过渡/Transition;6:充电/Charge
    ...
    ...    交流节能模式：0:安全/Safe;1:节能/Save;2:自由/Free；
    
    电池管理初始化
    Wait Until Keyword Succeeds    3m    2    信号量数据值为    交流供电状态    市电1
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    过渡
    Wait Until Keyword Succeeds    30    1    设置web控制量    启动均充
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    均充
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    均充
    ${均充电压可设置范围}    获取web参数可设置范围    均充电压
    设置web参数量    均充电压    ${均充电压可设置范围}[0]
    ${输出高停机电压可设置范围}    获取web参数可设置范围    整流器输出高停机电压
    设置web参数量    整流器输出高停机电压    ${输出高停机电压可设置范围}[0]
    #使整流器输出过压保证无正常工作的整流器
    ${整流器输出高电压}    获取web参数量    整流器输出高停机电压
    向上调节电池电压    ${整流器输出高电压}+0.5
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    工作整流器数量    0
    ${状态}    获取web实时数据    电池管理状态
    #无整流器放电状态启动浮充，应F
    设置负载电压电流    53.5    10
    打开负载输出
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    系统停电
    #整流器过压恢复，5min应能恢复
    关闭负载输出
    向下调节电池电压    53.5
    ${在线整流器数量}    获取web实时数据    在线整流器数量
    Wait Until Keyword Succeeds    5m10s    1    信号量数据值为    工作整流器数量    ${在线整流器数量}
    [Teardown]    Run keywords    电池管理参数恢复默认值
    ...    AND    重置电池模拟器输出

均充时电池组无效时转浮充
    
    电池管理初始化
    设置web控制量    启动均充
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    均充
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    均充
    sleep    5
    Wait Until Keyword Succeeds    30    1s    设置web参数量    电池组容量_1    0
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    浮充
    [Teardown]    设置web参数量    电池组容量_1    100

均充时直流电压高时转浮充
    
    电池管理初始化
    Wait Until Keyword Succeeds    30    1    设置web控制量    启动均充
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    均充
    #制造直流电压高告警，应转浮充
    ${直流电压高阈值}    获取web参数量    直流电压高阈值
    ${直流电压高阈值范围}    获取web参数上下限范围    直流电压高阈值
    ${直流电压高阈值可设置范围}    获取web参数可设置范围    直流电压高阈值
    设置web参数量    直流电压高阈值    ${直流电压高阈值可设置范围}[0]
    ${是否告警存在}    run keyword and return status    Wait Until Keyword Succeeds    2m    1    判断告警存在    直流电压高
    #若直流电压高没有出现，则调节电池电压超过告警阈值
    run keyword if    '${是否告警存在}'=='False'    向上调节电池电压    ${直流电压高阈值可设置范围}[0]
    run keyword if    '${是否告警存在}'=='False'    Wait Until Keyword Succeeds    2m    1    判断告警存在    直流电压高
    sleep    5
    ${电池状态}    获取web实时数据    电池管理状态
    Should Be Equal    ${电池状态}    浮充
    #恢复
    设置web参数量    直流电压高阈值    ${直流电压高阈值范围}[0]
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    直流电压高
    [Teardown]    设置web参数量    直流电压高阈值    ${直流电压高阈值范围}[0]

均充禁止时转浮充
    
    电池管理初始化    #初始化后为浮充状态，均充允许，只有一组电池100AH
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动均充
    Wait Until Keyword Succeeds    20    1    信号量数据值为    电池管理状态    均充
    ${电池状态}    获取web实时数据    电池管理状态
    Should Be Equal    ${电池状态}    均充
    设置web参数量    均充使能    禁止
    sleep    10
    ${电池状态}    获取web实时数据    电池管理状态
    Should Be Equal    ${电池状态}    浮充
    [Teardown]    设置web参数量    均充使能    允许

均充最长时间到转浮充
    [Documentation]    0:浮充/Float;1:均充/Equal;2:测试/Test;3:放电/Discharge;4:检测/Detect;5:过渡/Transition;6:充电/Charge
    ...    ==20190321-F：启动均充成功，获取电池管理状态仍然为浮充             失败
    
    电池管理初始化
    设置web参数量    电池均充周期    90
    ${均充末期维持时间范围}    获取web参数上下限范围_有单位    均充末期维持时间
    ${均充最短时间范围}    获取web参数上下限范围_有单位    均充最短时间
    ${均充最长时间范围}    获取web参数上下限范围_有单位    均充最长时间
    ${均充末期电流系数范围}    获取web参数上下限范围    均充末期电流系数
    ${均充末期维持时间}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间范围}[1]+1
    ...    ELSE    evaluate    ${均充末期维持时间范围}[1]+2
    ${均充最短时间}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间范围}[1]+1
    ...    ELSE    evaluate    ${均充最短时间范围}[1]+2
    ${均充最长时间}    run keyword if    '${均充最长时间范围}[3]'== 'Hour'    evaluate    ${均充最长时间范围}[1]+2
    ...    ELSE    evaluate    ${均充最长时间范围}[1]+5
    设置web参数量    均充末期维持时间    ${均充末期维持时间}
    设置web参数量    均充最短时间    ${均充最短时间}
    设置web参数量    均充最长时间    ${均充最长时间}
    设置web参数量    均充末期电流系数    ${均充末期电流系数范围}[0]
    ${均充末期维持时间转换后}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间}*60
    ...    ELSE    set variable    ${均充末期维持时间}
    ${均充最短时间转换后}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间}*60
    ...    ELSE    set variable    ${均充最短时间}
    ${均充最长时间转换后}    run keyword if    '${均充最长时间范围}[3]'== 'Hour'    evaluate    ${均充最长时间}*60
    ...    ELSE    set variable    ${均充最长时间}
    ${等待时间}    evaluate    ${均充最长时间转换后}-${均充最短时间转换后}-${均充末期维持时间转换后}
    ${电池均充周期}    获取web参数量    电池均充周期
    sleep    1
    连接CSU
    sleep    2m
    连接CSU
    ${下次均充日期}    获取web实时数据    下次均充时间    #比下次周期均充时间要提前，否则会进入周期均充，而无法判断是否没进预约均充
    ${当前时间}    获取系统时间
    ${调整时间}    add time to date    ${当前时间}    10d    exclude_millis=yes
    sleep    5
    设置系统时间    ${调整时间}
    Wait Until Keyword Succeeds    5m    2    设置web控制量    启动均充
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    均充
    ${电池状态}    获取web实时数据    电池管理状态
    Should Be Equal    ${电池状态}    均充
    sleep    ${均充末期维持时间转换后}m
    ${电池状态}    获取web实时数据    电池管理状态
    Should Be Equal    ${电池状态}    均充
    sleep    10s
    #${均充最短时间转换后}m
    ${电池状态}    获取web实时数据    电池管理状态
    Should Be Equal    ${电池状态}    均充
    sleep    ${等待时间}m
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充    #在时间到后的30s内容是否转浮充
    ${电池状态}    获取web实时数据    电池管理状态
    Should Be Equal    ${电池状态}    浮充
    ${下次均充时间_cal}    add time to date    ${下次均充日期}    10d    exclude_millis=yes
    sleep    5
    ${下次均充时间_new}    获取web实时数据    下次均充时间
    should be equal    ${下次均充时间_new}    ${下次均充时间_cal}
    [Teardown]    Run keywords    设置web设备参数量为默认值    均充最长时间    均充最短时间    均充末期维持时间
    ...    AND    同步系统时间

均充末期时间和最短时间到转浮充
    [Documentation]    失败
    
    电池管理初始化    #初始化后为浮充状态，只有一组电池100AH
    #关闭电池模拟器以屏蔽器电流不稳定的影响
    关闭电池模拟器输出
    #设置合适的维持时间    #下限都是0min
    ${均充末期维持时间范围}    获取web参数上下限范围_有单位    均充末期维持时间
    ${均充最短时间范围}    获取web参数上下限范围_有单位    均充最短时间
    ${均充最长时间范围}    获取web参数上下限范围_有单位    均充最长时间
    ${均充末期电流系数范围}    获取web参数上下限范围    均充末期电流系数
    ${电流通道配置}    获取通道配置    ${plat.battvolt1}
    ${原电池电流零点}    evaluate    ${电流通道配置}[1]
    设置web参数量    均充末期电流系数    ${均充末期电流系数范围}[2]
    ${均充末期维持时间范围下限}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间范围}[1]+1
    ...    ELSE    evaluate    ${均充末期维持时间范围}[1]+3
    ${均充最短时间范围下限}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间范围}[1]+2
    ...    ELSE    evaluate    ${均充最短时间范围}[1]+6
    设置web参数量    均充末期维持时间    ${均充末期维持时间范围下限}
    设置web参数量    均充最短时间    ${均充最短时间范围下限}
    ${均充末期维持时间范围下限转换后}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间范围下限}*60
    ...    ELSE    set variable    ${均充末期维持时间范围下限}
    ${均充最短时间范围下限转换后}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间范围下限}*60
    ...    ELSE    set variable    ${均充最短时间范围下限}
    ${等待时间}    evaluate    ${均充最短时间范围下限转换后}-${均充末期维持时间范围下限转换后}
    log    ===单位换算结束===
    ${均充末期电流系数}    获取web参数量    均充末期电流系数
    ${电池组容量}    获取web参数量    电池组容量_1
    ${进入均充末期电流}    evaluate    ${均充末期电流系数}*${电池组容量}
    sleep    3
    #（1）最短均充时间退出：达到末期电流后，达到均充末期维持时间，未达到最短时间，不退出均充
    log    ===进入均充===
    设置web控制量    启动均充
    Wait Until Keyword Succeeds    20    1    信号量数据值为    电池管理状态    均充
    sleep    10
    #调节电池电流零点使小于末期电流
    ${当前电池电流}    获取web实时数据    电池电流-1
    ${进入末期电流调节值}    evaluate    0-(${原电池电流零点}+${当前电池电流}-1)
    设置通道配置    ${plat.battvolt1}    ${进入末期电流调节值}    1    直流配电    电池分流器电流_1
    sleep    5
    ${电流通道配置}    获取通道配置    ${plat.battvolt1}
    ${设置后的电池电流零点1}    evaluate    ${电流通道配置}[1]
    ${当前电池电流}    获取web实时数据    电池电流-1
    should be true    ${当前电池电流}<${进入均充末期电流}
    #末期维持时间到，最短时间未到，仍然为均充
    Comment    Wait Until Keyword Succeeds    ${均充末期维持时间范围下限转换后}m    1    信号量数据值为    电池管理状态    均充
    sleep    ${均充末期维持时间范围下限转换后}m
    ${电池电流1}    获取web实时数据    电池电流-1
    ${电池状态}    获取web实时数据    电池管理状态
    Should Be Equal    ${电池状态}    均充
    #最短时间到，进入浮充
    sleep    ${等待时间}m
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    ${电池电流1}    获取web实时数据    电池电流-1
    ${电池状态}    获取web实时数据    电池管理状态
    Should Be Equal    ${电池状态}    浮充
    log    ===最短均充时间到退出均充===
    #（2）均充末期维持时间退出：达到末期电流后，达到均充最短时间和均充末期维持时间，退出均充
    Comment    log    ===均充末期维持时间退出===
    设置通道配置    ${plat.battvolt1}    ${原电池电流零点}    1    直流配电    电池分流器电流_1
    ${均充末期维持时间范围下限}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间范围}[1]+1
    ...    ELSE    evaluate    ${均充末期维持时间范围}[1]+3
    ${均充最短时间范围下限}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间范围}[1]+1
    ...    ELSE    evaluate    ${均充最短时间范围}[1]+3
    设置web参数量    均充末期维持时间    ${均充末期维持时间范围下限}
    设置web参数量    均充最短时间    ${均充最短时间范围下限}
    ${均充末期维持时间范围下限转换后}    run keyword if    '${均充末期维持时间范围}[3]'== 'Hour'    evaluate    ${均充末期维持时间范围下限}*60
    ...    ELSE    set variable    ${均充末期维持时间范围下限}
    ${均充最短时间范围下限转换后}    run keyword if    '${均充最短时间范围}[3]'== 'Hour'    evaluate    ${均充最短时间范围下限}*60
    ...    ELSE    set variable    ${均充最短时间范围下限}
    ${等待时间}    evaluate    ${均充最短时间范围下限转换后}+2
    log    ===单位换算结束===
    sleep    3
    设置web控制量    启动均充
    Wait Until Keyword Succeeds    20    1    信号量数据值为    电池管理状态    均充
    sleep    10
    #调节电池电流零点使小于末期电流
    ${进入末期电流调节值}    evaluate    0-(${原电池电流零点}+${当前电池电流}-1)
    设置通道配置    ${plat.battvolt1}    ${进入末期电流调节值}    1    直流配电    电池分流器电流_1
    sleep    5
    ${电流通道配置}    获取通道配置    ${plat.battvolt1}
    ${设置后的电池电流零点2}    evaluate    ${电流通道配置}[1]
    ${当前电池电流}    获取web实时数据    电池电流-1
    should be true    ${当前电池电流}<${进入均充末期电流}
    #最短时间\末期维持时间到，进入浮充
    Comment    log    ===维持时间和最短时间设置一样时，退出均充需要增加1min，否则会失败，为何？===
    sleep    ${等待时间}m    #修改为4min则成功了
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    ${电池电流1}    获取web实时数据    电池电流-1
    ${电池状态}    获取web实时数据    电池管理状态
    Should Be Equal    ${电池状态}    浮充
    log    ===均充维持时间到退出均充===
    [Teardown]    Run keywords    设置web设备参数量为默认值    均充最长时间    均充最短时间    均充末期维持时间    均充末期电流系数
    ...    AND    设置通道配置    ${plat.battvolt1}    0    1    直流配电    电池分流器电流_1

均充时电池下电时转浮充
    [Documentation]    下电模式：0:禁止；1:电池电压；2:停电时间；3:电池剩余容量
    
    电池管理初始化
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    交流供电状态    市电1
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    过渡
    设置web设备参数量为默认值    均充最长时间    均充最短时间    均充末期维持时间
    设置web参数量    负载一次下电使能    禁止
    设置web参数量    负载二次下电使能    禁止
    设置web参数量    电池下电使能    禁止    #默认0：禁止
    wait until keyword succeeds    10    1    设置web参数量    电池高温下电使能    允许
    ${告警级别}    获取web参数量    电池高温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    电池高温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    电池高温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    sleep    3
    ${可设范围}    获取web参数可设置范围    电池高温下电温度
    设置web参数量    电池高温下电温度    ${可设范围}[0]
    run keyword and ignore error    设置web参数量    下电控制延时    0
    显示属性配置    电池高温下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    3
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动均充
    Wait Until Keyword Succeeds    20    1    信号量数据值为    电池管理状态    均充
    ${直流电压}    获取web实时数据    直流电压
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    10    2    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    设置负载电压电流    53.5    10
    打开负载输出
    wait until keyword succeeds    5m    1    判断告警存在    电池高温下电
    Wait Until Keyword Succeeds    3m    2    信号量数据值为    电池高温下电控制状态    动作
    Wait Until Keyword Succeeds    20    1    信号量数据值为    电池管理状态    浮充
    #恢复
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    -10    1.6    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    电池高温下电控制状态    恢复
    wait until keyword succeeds    4m    2    判断告警不存在    电池高温下电
    显示属性配置    电池高温下电控制状态    数字量    web_attr=Off    gui_attr=Off
    关闭负载输出
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池高温下电温度    电池高温下电使能    负载一次下电使能    负载二次下电使能    电池下电使能
    ...    AND    关闭负载输出

系统复位继续均充
    [Documentation]    0:浮充/Float;1:均充/Equal;2:测试/Test;3:放电/Discharge;4:检测/Detect;5:过渡/Transition;6:充电/Charge   复位失败
    ...    ==20190321-F：启动均充成功，获取电池管理状态仍然为浮充
    
    电池管理初始化
    Wait Until Keyword Succeeds    10    2    设置web控制量    启动均充
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    均充
    系统复位
    连接CSU
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    均充
    ${电池状态}    获取web实时数据    电池管理状态
    Should Be Equal    ${电池状态}    均充
    [Teardown]

系统停电来电继续均充
    [Documentation]    0:浮充/Float;1:均充/Equal;2:测试/Test;3:放电/Discharge;4:检测/Detect;5:过渡/Transition;6:充电/Charge
    ...    ==20190321-F：启动均充成功，获取电池管理状态仍然为浮充
    
    电池管理初始化
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动均充
    设置负载电压电流    53.5    10
    打开负载输出
    Wait Until Keyword Succeeds    20    1    信号量数据值为    电池管理状态    均充
    关闭交流源输出
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    系统停电
    ${Bat_State}    获取web实时数据    电池管理状态
    Should Be Equal    ${Bat_State}    系统停电
    关闭负载输出
    打开交流源输出
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    均充
    [Teardown]    Run keywords    关闭负载输出
    ...    AND    打开交流源输出

均充时电池温度高转浮充
    [Documentation]    下电模式：0:禁止；1:电池电压；2:停电时间；3:电池剩余容量      无电池温度高告警产生
    
    电池管理初始化
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    交流供电状态    市电1
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    过渡
    wait until keyword succeeds    10    1    设置web参数量    电池高温下电使能    禁止
    wait until keyword succeeds    2m    1    判断告警不存在    电池高温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    ${温度阈值}    获取web参数量    电池温度高阈值
    sleep    3
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动均充
    Wait Until Keyword Succeeds    20    1    信号量数据值为    电池管理状态    均充
    ${直流电压}    获取web实时数据    直流电压
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    10    2    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    设置负载电压电流    53.5    10
    打开负载输出
    wait until keyword succeeds    3m    1    判断告警存在    电池温度高
    Wait Until Keyword Succeeds    3m    1    信号量数据值为    电池管理状态    浮充
    #恢复
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    -10    1.6    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    wait until keyword succeeds    4m    2    判断告警不存在    电池温度高
    关闭负载输出
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemp1}    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池温度高阈值    电池高温下电使能
    ...    AND    关闭负载输出

均充时环境温度高转浮充

    [Documentation]    下电模式：0:禁止；1:电池电压；2:停电时间；3:电池剩余容量    无环境温度高告警产生
    
    电池管理初始化
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    交流供电状态    市电1
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    过渡
    wait until keyword succeeds    10    1    设置web参数量    电池高温下电使能    禁止
    wait until keyword succeeds    2m    1    判断告警不存在    电池高温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    ${默认值}    获取web参数上下限范围    环境温度高阈值
    ${可设范围}    获取web参数可设置范围    环境温度高阈值
    ${默认值1}    获取web参数上下限范围    环境温度过高阈值
    ${可设范围1}    获取web参数可设置范围    环境温度过高阈值
    设置web参数量    环境温度过高阈值    ${可设范围1}[1]
    设置web参数量    环境温度高阈值    ${默认值}[0]
    ${温度阈值}    获取web参数量    环境温度高阈值
    sleep    3
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动均充
    Wait Until Keyword Succeeds    20    1    信号量数据值为    电池管理状态    均充
    ${直流电压}    获取web实时数据    直流电压
    #设置环境温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    25    1    系统运行环境    环境温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    ${环境温度}    获取web实时数据    环境温度
    设置负载电压电流    53.5    10
    打开负载输出
    wait until keyword succeeds    3m    1    判断告警存在    环境温度高
    Wait Until Keyword Succeeds    3m    2    信号量数据值为    电池管理状态    浮充
    #恢复
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    0    1    系统运行环境    环境温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    ${环境温度}    获取web实时数据    环境温度
    wait until keyword succeeds    4m    2    判断告警不存在    环境温度高
    关闭负载输出
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    0    1    系统运行环境    环境温度
    ...    AND    设置web设备参数量为默认值    环境温度过高阈值    电池高温下电使能
    ...    AND    关闭负载输出
