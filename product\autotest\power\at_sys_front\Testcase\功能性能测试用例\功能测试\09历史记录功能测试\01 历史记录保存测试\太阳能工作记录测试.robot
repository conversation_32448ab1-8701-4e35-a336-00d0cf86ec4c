*** Settings ***
Suite Setup       PU测试前置条件    #PU测试前置条件
Suite Teardown    PU测试结束条件    #PU测试结束条件
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
太阳能工作记录保存内容
    连接CSU
    ${开始数量}    获取web事件记录数量    太阳能工作记录
    run keyword if    ${开始数量}>995    删除历史记录    太阳能工作记录
    sleep    30
    ${开始数量}    获取web事件记录数量    太阳能工作记录
    设置子工具值    pu    all    模拟量    PU输出电流    10
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-4    10
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-10    10
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-20    10
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-30    10
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-40    10
    ${起始时间}    获取系统时间
    ${起始电量}    获取web实时数据    累计光伏发电量
    ${起始工作时间}    获取web实时数据    光伏工作时间
    sleep    6m
    设置子工具值    pu    all    模拟量    PU输出电流    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-4    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-10    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-20    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-30    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-40    1
    sleep    6m
    ${结束时间}    获取系统时间
    ${结束电量}    获取web实时数据    累计光伏发电量
    ${结束工作时间}    获取web实时数据    光伏工作时间
    ${工作时间}    evaluate    ${结束工作时间}-${起始工作时间}
    ${结束数量}    获取web事件记录数量    太阳能工作记录
    @{记录内容}    获取web事件记录最新一条    太阳能工作记录
    should be true    ${记录内容}[0] == ${结束数量}
    should be true    ${结束数量} == ${开始数量} + 1
    ${起始时间差}    subtract date from date    ${记录内容}[1]    ${起始时间}
    should be true    -3000<=${起始时间差}<=3000
    ${结束时间差}    subtract date from date    ${记录内容}[2]    ${结束时间}
    should be true    -3000<=${结束时间差}<=3000
    should be true    ${记录内容}[3]>=${工作时间}-1
    should be true    ${起始电量}<=${记录内容}[4]<=${起始电量}+1
    should be true    ${结束电量}<=${记录内容}[5]<=${结束电量}+1

太阳能工作记录保存内容(不满足保存条件）
    连接CSU
    ${开始数量}    获取web事件记录数量    太阳能工作记录
    run keyword if    ${开始数量}>995    删除历史记录    太阳能工作记录
    sleep    30
    ${开始数量}    获取web事件记录数量    太阳能工作记录
    @{记录内容1}    获取web事件记录最新一条    太阳能工作记录
    设置子工具值    pu    all    模拟量    PU输出电流    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-4    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-10    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-20    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-30    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-40    1
    ${起始时间}    获取系统时间
    ${起始电量}    获取web实时数据    累计光伏发电量
    ${起始工作时间}    获取web实时数据    光伏工作时间
    sleep    6m
    设置子工具值    pu    all    模拟量    PU输出电流    2
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-4    2
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-10    2
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-20    2
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-30    2
    Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU输出电流-40    2
    sleep    6m
    ${结束时间}    获取系统时间
    ${结束电量}    获取web实时数据    累计光伏发电量
    ${结束工作时间}    获取web实时数据    光伏工作时间
    ${工作时间}    evaluate    ${结束工作时间}-${起始工作时间}
    ${结束数量}    获取web事件记录数量    太阳能工作记录
    @{记录内容}    获取web事件记录最新一条    太阳能工作记录
    should be true    ${记录内容}[0] == ${结束数量}
    should be true    ${结束数量} == ${开始数量}
    ${time}    Subtract Date From Date    ${记录内容1}[1]    ${记录内容}[1]
    should be equal    ${time}    ${0}
    ${time}    Subtract Date From Date    ${记录内容1}[2]    ${记录内容}[2]
    should be equal    ${time}    ${0}
    should be true    ${记录内容1}[3]==${记录内容}[3]
    should be true    ${记录内容1}[4]==${记录内容}[4]
    should be true    ${记录内容1}[5]==${记录内容}[5]

系统断电太阳能工作记录保存测试
    连接CSU
    ${记录数量}    获取web事件记录数量    太阳能工作记录
    测试台上下电操作    ${测试台编号}    OFF
    sleep    15
    测试台上下电操作    ${测试台编号}    ON
    实时告警刷新完成
    sleep    5
    ${记录数量1}    获取web事件记录数量    太阳能工作记录
    should be true    ${记录数量1}==${记录数量}

