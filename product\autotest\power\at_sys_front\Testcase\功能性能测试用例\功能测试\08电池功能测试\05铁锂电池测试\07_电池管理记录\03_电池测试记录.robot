*** Settings ***
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
铁锂电池测试记录保存内容（手动产生手动退出）
    
    铁锂电池管理初始化
    ${缺省值}    获取web参数上下限范围_有单位    测试最长时间
    ${可设置范围}    获取web参数可设置范围    测试最长时间
    ${测试最长时间设置值}    run keyword if    '${缺省值}[3]'== 'Hour'    evaluate    ${可设置范围}[0]+1
    ...    ELSE    evaluate    ${可设置范围}[0]+5
    ${测试最长时间转换后}    run keyword if    '${缺省值}[3]'== 'Hour'    evaluate    ${测试最长时间设置值}*60
    ...    ELSE    set variable    ${测试最长时间设置值}
    设置web参数量    测试最长时间    ${测试最长时间设置值}
    sleep    5
    ${开始数量}    获取web事件记录数量    电池测试记录
    run keyword if    ${开始数量}>97    删除历史记录    电池测试记录
    sleep    30
    ${开始数量}    获取web事件记录数量    电池测试记录
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动测试
    ${起始时间}    获取系统时间
    ${起始SOC}    获取web实时数据    电池组当前容量比率-1
    ${起始电池电压}    获取web实时数据    电池电压-1
    ${起始电量}    获取web实时数据    电池放电电量-1
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    测试
    ${等待时间}    evaluate    ${测试最长时间转换后}-2
    sleep    ${等待时间}m
    Wait Until Keyword Succeeds    1m    1    设置web控制量    启动充电
    ${持续时间}    获取web实时数据    电池状态持续时间
    ${结束时间}    获取系统时间
    ${结束SOC}    获取web实时数据    电池组当前容量比率-1
    ${结束电池电压}    获取web实时数据    电池电压-1
    ${结束电量}    获取web实时数据    电池放电电量-1
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    充电
    sleep    30
    ${结束数量}    获取web事件记录数量    电池测试记录
    @{记录内容}    获取web事件记录最新一条    电池测试记录
    should be true    ${记录内容}[0] == ${结束数量}
    should be true    ${结束数量} == ${开始数量} + 1
    ${起始时间差}    subtract date from date    ${记录内容}[1]    ${起始时间}
    should be true    -60<=${起始时间差}<=60
    ${结束时间差}    subtract date from date    ${记录内容}[2]    ${结束时间}
    should be true    -60<=${结束时间差}<=60
    should be true    ${持续时间} - 1 <= ${记录内容}[3]  <= ${持续时间} + 1
    should be equal    '${记录内容}[4]'    '手动'
    should be equal    '${记录内容}[5]'    '手动'
    should be true    ${记录内容}[6] == ${起始SOC}
    should be true    ${记录内容}[7] == ${结束SOC}
    should be true    ${起始电池电压}-0.6<= ${记录内容}[8] <= ${起始电池电压}+0.6
    should be true    ${结束电池电压}-1 <= ${记录内容}[9] <= ${结束电池电压}+1
    should be true    ${起始电量}-0.2 <= ${记录内容}[10] <= ${起始电量} +0.2
    should be true    ${结束电量}-0.2<= ${记录内容}[11] <= ${结束电量}+0.2
    [Teardown]    设置web参数量    测试最长时间    ${缺省值}[0]

铁锂电池测试记录保存内容（手动进入测试电压退出）
    
    铁锂电池管理初始化
    #设置测试终止电压
    ${测试终止电压缺省值}    获取web参数上下限范围    测试终止电压
    ${测试终止电压可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${测试终止电压可设置范围}[1]
    #设置测试最大时间
    ${测试最长时间缺省值}    获取web参数上下限范围    测试最长时间
    ${测试最长时间可设置范围}    获取web参数可设置范围    测试最长时间
    设置web参数量    测试最长时间    ${测试最长时间可设置范围}[1]
    #设置测试终止容量
    ${测试终止容量缺省值}    获取web参数上下限范围_有单位    测试终止SOC阈值
    ${测试终止容量可设置范围}    获取web参数可设置范围    测试终止SOC阈值
    设置web参数量    测试终止SOC阈值    ${测试终止容量可设置范围}[0]
    ${测试失败容量阈值缺省值}    获取web参数上下限范围_有单位    测试失败SOC阈值
    ${测试失败容量阈值可设置范围}    获取web参数可设置范围    测试失败SOC阈值
    设置web参数量    测试失败SOC阈值    ${测试失败容量阈值可设置范围}[1]
    sleep    5
    ${开始数量}    获取web事件记录数量    电池测试记录
    run keyword if    ${开始数量}>97    删除历史记录    电池测试记录
    sleep    30
    ${开始数量}    获取web事件记录数量    电池测试记录
    #设置均充相关
    设置负载电压电流    53.5    30
    打开负载输出
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动测试
    ${起始时间}    获取系统时间
    ${起始SOC}    获取web实时数据    电池组当前容量比率-1
    ${起始电池电压}    获取web实时数据    电池电压-1
    ${起始电量}    获取web实时数据    电池放电电量-1
    Wait Until Keyword Succeeds    20    1    信号量数据值为    电池管理状态    测试
    ${测试终止SOC设置值}    获取web参数量    测试终止SOC阈值
    ${测试失败SOC设置值}    获取web参数量    测试失败SOC阈值
    ${测试终止容量转换}    run keyword if    '${测试终止容量缺省值}[3]'== 'C10'    evaluate    ${测试终止SOC设置值}*100
    ...    ELSE    evaluate    ${测试终止SOC设置值}
    ${测试失败SOC转换}    run keyword if    '${测试失败容量阈值缺省值}[3]'== 'C10'    evaluate    ${测试失败SOC设置值}*100
    ...    ELSE    evaluate    ${测试失败SOC设置值}
    ${测试终止电压}    获取web参数量    测试终止电压
    ${测试最长时间}    获取web参数量    测试最长时间
    sleep  30
    向下调节电池电压    ${测试终止电压}
    ${持续时间}    获取web实时数据    电池状态持续时间
    should be true    ${测试终止容量转换}<${起始SOC}<=${测试失败SOC转换}
    should be true    ${持续时间}<${测试最长时间}
    ${结束时间}    获取系统时间
    ${结束SOC}    获取web实时数据    电池组当前容量比率-1
    ${结束电池电压}    获取web实时数据    电池电压-1
    ${结束电量}    获取web实时数据    电池放电电量-1
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    充电
    关闭负载输出
    sleep    30
    ${结束数量}    获取web事件记录数量    电池测试记录
    @{记录内容}    获取web事件记录最新一条    电池测试记录
    should be true    ${记录内容}[0] == ${结束数量}
    should be true    ${结束数量} == ${开始数量} + 1
    ${起始时间差}    subtract date from date    ${记录内容}[1]    ${起始时间}
    should be true    -60<=${起始时间差}<=60
    ${结束时间差}    subtract date from date    ${记录内容}[2]    ${结束时间}
    should be true    -60<=${结束时间差}<=60
    should be true    ${持续时间} - 1 <= ${记录内容}[3]  <= ${持续时间} + 1
    should be equal    '${记录内容}[4]'    '手动'
    should be equal    '${记录内容}[5]'    '测试电压'
    should be true    ${记录内容}[6] == ${起始SOC}
    should be true    ${记录内容}[7] == ${结束SOC}
    should be true    ${起始电池电压}-0.3 <= ${记录内容}[8] <= ${起始电池电压}+0.3
    should be true    ${结束电池电压}-1 <= ${记录内容}[9] <= ${结束电池电压}+1
    should be true    ${起始电量}-0.2 <= ${记录内容}[10] <= ${起始电量} +0.2
    should be true    ${结束电量}-0.2<= ${记录内容}[11] <= ${结束电量}+0.2
    向上调节电池电压    53.5
    [Teardown]    Run keywords    设置web设备参数量为默认值    测试终止电压    测试最长时间    测试终止SOC阈值    测试失败SOC阈值
    ...    AND    重置电池模拟器输出
    ...    AND    关闭负载输出

铁锂电池测试记录保存内容（手动进入测试最大时间退出）
    
    铁锂电池管理初始化
    ${缺省值}    获取web参数上下限范围_有单位    测试最长时间
    ${可设置范围}    获取web参数可设置范围    测试最长时间
    ${测试最长时间设置值}    run keyword if    '${缺省值}[3]'== 'Hour'    evaluate    ${可设置范围}[0]+1
    ...    ELSE    evaluate    ${可设置范围}[0]+5
    ${测试最长时间转换后}    run keyword if    '${缺省值}[3]'== 'Hour'    evaluate    ${测试最长时间设置值}*60
    ...    ELSE    set variable    ${测试最长时间设置值}
    设置web参数量    测试最长时间    ${测试最长时间设置值}
    sleep    5
    ${开始数量}    获取web事件记录数量    电池测试记录
    run keyword if    ${开始数量}>97    删除历史记录    电池测试记录
    sleep    30
    ${开始数量}    获取web事件记录数量    电池测试记录
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动测试
    ${起始时间}    获取系统时间
    ${起始SOC}    获取web实时数据    电池组当前容量比率-1
    ${起始电池电压}    获取web实时数据    电池电压-1
    ${起始电量}    获取web实时数据    电池放电电量-1
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    测试
    ${等待时间}    evaluate    ${测试最长时间转换后}+2
    Wait Until Keyword Succeeds    ${等待时间}m    1    信号量数据值为    电池管理状态    充电
    ${持续时间}    获取web实时数据    电池状态持续时间
    ${结束时间}    获取系统时间
    ${结束SOC}    获取web实时数据    电池组当前容量比率-1
    ${结束电池电压}    获取web实时数据    电池电压-1
    ${结束电量}    获取web实时数据    电池放电电量-1
    sleep    30
    ${结束数量}    获取web事件记录数量    电池测试记录
    @{记录内容}    获取web事件记录最新一条    电池测试记录
    should be true    ${记录内容}[0] == ${结束数量}
    should be true    ${结束数量} == ${开始数量} + 1
    ${起始时间差}    subtract date from date    ${记录内容}[1]    ${起始时间}
    should be true    -60<=${起始时间差}<=60
    ${结束时间差}    subtract date from date    ${记录内容}[2]    ${结束时间}
    should be true    -60<=${结束时间差}<=60
    should be true    ${持续时间} - 1 <= ${记录内容}[3]  <= ${持续时间} + 1
    should be equal    '${记录内容}[4]'    '手动'
    should be equal    '${记录内容}[5]'    '测试最大时间'
    should be true    ${记录内容}[6] == ${起始SOC}
    should be true    ${记录内容}[7] == ${结束SOC}
    should be true    ${起始电池电压}-0.3 <= ${记录内容}[8] <= ${起始电池电压}+0.3
    should be true    ${结束电池电压}-1 <= ${记录内容}[9] <= ${结束电池电压}+1
    should be true    ${起始电量}-0.2 <= ${记录内容}[10] <= ${起始电量} +0.2
    should be true    ${结束电量}-0.2<= ${记录内容}[11] <= ${结束电量}+0.2
    [Teardown]    设置web参数量    测试最长时间    ${缺省值}[0]

铁锂电池测试记录保存内容（手动进入测试容量退出）
    
    铁锂电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    20
    #设置测试终止电压
    ${测试终止电压缺省值}    获取web参数上下限范围    测试终止电压
    ${测试终止电压可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${测试终止电压可设置范围}[0]
    #设置测试最大时间
    ${测试最长时间缺省值}    获取web参数上下限范围    测试最长时间
    ${测试最长时间可设置范围}    获取web参数可设置范围    测试最长时间
    设置web参数量    测试最长时间    ${测试最长时间可设置范围}[1]
    #设置测试终止容量
    ${测试终止容量缺省值}    获取web参数上下限范围_有单位    测试终止SOC阈值
    ${测试终止容量可设置范围}    获取web参数可设置范围    测试终止SOC阈值
    设置web参数量    测试终止SOC阈值    ${测试终止容量可设置范围}[1]
    ${测试失败容量阈值缺省值}    获取web参数上下限范围_有单位    测试失败SOC阈值
    ${测试失败容量阈值可设置范围}    获取web参数可设置范围    测试失败SOC阈值
    设置web参数量    测试失败SOC阈值    ${测试失败容量阈值可设置范围}[1]
    ${开始数量}    获取web事件记录数量    电池测试记录
    run keyword if    ${开始数量}>97    删除历史记录    电池测试记录
    sleep    30
    ${开始数量}    获取web事件记录数量    电池测试记录
    sleep    5
    缓慢设置负载电压电流    53.5    50
    打开负载输出
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动测试
    ${起始时间}    获取系统时间
    ${起始SOC}    获取web实时数据    电池组当前容量比率-1
    ${起始电池电压}    获取web实时数据    电池电压-1
    ${起始电量}    获取web实时数据    电池放电电量-1
    Wait Until Keyword Succeeds    20    1    信号量数据值为    电池管理状态    测试
    ${测试终止SOC设置值}    获取web参数量    测试终止SOC阈值
    ${测试失败SOC设置值}    获取web参数量    测试失败SOC阈值
    ${测试终止容量转换}    run keyword if    '${测试终止容量缺省值}[3]'== 'C10'    evaluate    ${测试终止SOC设置值}*100
    ...    ELSE    evaluate    ${测试终止SOC设置值}
    ${测试失败SOC转换}    run keyword if    '${测试失败容量阈值缺省值}[3]'== 'C10'    evaluate    ${测试失败SOC设置值}*100
    ...    ELSE    evaluate    ${测试失败SOC设置值}
    ${测试终止电压}    获取web参数量    测试终止电压
    ${测试最长时间}    获取web参数量    测试最长时间
    Wait Until Keyword Succeeds    20m    1    信号量数据值为    电池组当前容量比率-1    ${测试终止容量转换}
    ${持续时间}    获取web实时数据    电池状态持续时间
    should be true    ${起始电池电压}>${测试终止电压}
    should be true    ${持续时间}<${测试最长时间}
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    充电
    ${结束时间}    获取系统时间
    ${结束SOC}    获取web实时数据    电池组当前容量比率-1
    ${结束电池电压}    获取web实时数据    电池电压-1
    ${结束电量}    获取web实时数据    电池放电电量-1
    sleep    30
    ${结束数量}    获取web事件记录数量    电池测试记录
    @{记录内容}    获取web事件记录最新一条    电池测试记录
    should be true    ${记录内容}[0] == ${结束数量}
    should be true    ${结束数量} == ${开始数量} + 1
    ${起始时间差}    subtract date from date    ${记录内容}[1]    ${起始时间}
    should be true    -60<=${起始时间差}<=60
    ${结束时间差}    subtract date from date    ${记录内容}[2]    ${结束时间}
    should be true    -60<=${结束时间差}<=60
    should be true    ${持续时间} - 1 <= ${记录内容}[3]  <= ${持续时间} + 1
    should be equal    '${记录内容}[4]'    '手动'
    should be equal    '${记录内容}[5]'    '测试容量'
    should be true    ${记录内容}[6] == ${起始SOC}
    should be true    ${记录内容}[7] == ${结束SOC}
    should be true    ${起始电池电压}-0.5 <= ${记录内容}[8] <= ${起始电池电压}+0.5
    should be true    ${结束电池电压}-1 <= ${记录内容}[9] <= ${结束电池电压}+1
    should be true    ${起始电量}-0.2 <= ${记录内容}[10] <= ${起始电量} +0.2
    should be true    ${结束电量}-0.2<= ${记录内容}[11] <= ${结束电量}+0.2
    [Teardown]    Run keywords    设置web设备参数量为默认值    测试终止电压    测试最长时间    测试终止SOC阈值    测试失败SOC阈值
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    100
    ...    AND    关闭负载输出

铁锂电池测试记录保存内容（手动进入交流停电退出）
    
    铁锂电池管理初始化
    sleep    5
    ${开始数量}    获取web事件记录数量    电池测试记录
    run keyword if    ${开始数量}>97    删除历史记录    电池测试记录
    sleep    30
    ${开始数量}    获取web事件记录数量    电池测试记录
    Wait Until Keyword Succeeds    1m    2    设置web控制量    启动测试
    ${起始时间}    获取系统时间
    ${起始SOC}    获取web实时数据    电池组当前容量比率-1
    ${起始电池电压}    获取web实时数据    电池电压-1
    ${起始电量}    获取web实时数据    电池放电电量-1
    Wait Until Keyword Succeeds    20    1    信号量数据值为    电池管理状态    测试
    sleep    30
    关闭交流源输出
    ${持续时间}    获取web实时数据    电池状态持续时间
    ${结束时间}    获取系统时间
    ${结束SOC}    获取web实时数据    电池组当前容量比率-1
    ${结束电池电压}    获取web实时数据    电池电压-1
    ${结束电量}    获取web实时数据    电池放电电量-1
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    系统停电
    sleep    30
    ${结束数量}    获取web事件记录数量    电池测试记录
    @{记录内容}    获取web事件记录最新一条    电池测试记录
    should be true    ${记录内容}[0] == ${结束数量}
    should be true    ${结束数量} == ${开始数量} + 1
    ${起始时间差}    subtract date from date    ${记录内容}[1]    ${起始时间}
    should be true    -60<=${起始时间差}<=60
    ${结束时间差}    subtract date from date    ${记录内容}[2]    ${结束时间}
    should be true    -60<=${结束时间差}<=60
    should be true    ${持续时间} - 1 <= ${记录内容}[3]  <= ${持续时间} + 1
    should be equal    '${记录内容}[4]'    '手动'
    should be equal    '${记录内容}[5]'    '系统停电'
    should be true    ${记录内容}[6] == ${起始SOC}
    should be true    ${记录内容}[7] == ${结束SOC}
    should be true    ${起始电池电压}-0.3 <= ${记录内容}[8] <= ${起始电池电压}+0.3
    should be true    ${结束电池电压}-1 <= ${记录内容}[9] <= ${结束电池电压}+1
    should be true    ${起始电量}-0.2 <= ${记录内容}[10] <= ${起始电量} +0.2
    should be true    ${结束电量}-0.2<= ${记录内容}[11] <= ${结束电量}+0.2
    [Teardown]    打开交流源输出

铁锂电池测试记录保存内容（手动进入电池组无效退出）
    
    铁锂电池管理初始化
    sleep    5
    ${开始数量}    获取web事件记录数量    电池测试记录
    run keyword if    ${开始数量}>97    删除历史记录    电池测试记录
    sleep    30
    ${开始数量}    获取web事件记录数量    电池测试记录
    Wait Until Keyword Succeeds    1m    2    设置web控制量    启动测试
    ${起始时间}    获取系统时间
    ${起始SOC}    获取web实时数据    电池组当前容量比率-1
    ${起始电池电压}    获取web实时数据    电池电压-1
    ${起始电量}    获取web实时数据    电池放电电量-1
    Wait Until Keyword Succeeds    20    1    信号量数据值为    电池管理状态    测试
    sleep    5
    ${持续时间}    获取web实时数据    电池状态持续时间
    ${结束时间}    获取系统时间
    ${结束SOC}    获取web实时数据    电池组当前容量比率-1
    ${结束电池电压}    获取web实时数据    电池电压-1
    ${结束电量}    获取web实时数据    电池充电电量-1
    Wait Until Keyword Succeeds    30    1s    设置web参数量    电池组容量_1    0
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    充电
    sleep    30
    ${结束数量}    获取web事件记录数量    电池测试记录
    @{记录内容}    获取web事件记录最新一条    电池测试记录
    should be true    ${记录内容}[0] == ${结束数量}
    should be true    ${结束数量} == ${开始数量} + 1
    ${起始时间差}    subtract date from date    ${记录内容}[1]    ${起始时间}
    should be true    -60<=${起始时间差}<=60
    ${结束时间差}    subtract date from date    ${记录内容}[2]    ${结束时间}
    should be true    -60<=${结束时间差}<=60
    should be true    ${持续时间} - 1 <= ${记录内容}[3]  <= ${持续时间} + 1
    should be equal    '${记录内容}[4]'    '手动'
    should be equal    '${记录内容}[5]'    '电池组异常'
    [Teardown]    设置web参数量    电池组容量_1    100

铁锂电池测试记录保存内容（手动进入电池下电退出）
    
    铁锂电池管理初始化
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    交流供电状态    市电1
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    过渡
    # 设置web设备参数量为默认值    均充最长时间    均充最短时间    均充末期维持时间
    设置web参数量    负载一次下电使能    禁止
    设置web参数量    负载二次下电使能    禁止
    设置web参数量    电池下电使能    禁止    #默认0：禁止
    wait until keyword succeeds    10    1    设置web参数量    电池低温下电使能    允许
    ${告警级别}    获取web参数量    电池低温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    电池低温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    电池低温下电温度
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    sleep    3
    ${可设范围}    获取web参数可设置范围    电池低温下电温度
    设置web参数量    电池低温下电温度    ${可设范围}[1]
    run keyword and ignore error    设置web参数量    下电控制延时    0
    显示属性配置    电池低温下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    5
    ${开始数量}    获取web事件记录数量    电池测试记录
    run keyword if    ${开始数量}>97    删除历史记录    电池测试记录
    sleep    30
    ${开始数量}    获取web事件记录数量    电池测试记录
    Wait Until Keyword Succeeds    1m    2    设置web控制量    启动测试
    ${起始时间}    获取系统时间
    ${起始SOC}    获取web实时数据    电池组当前容量比率-1
    ${起始电池电压}    获取web实时数据    电池电压-1
    ${起始电量}    获取web实时数据    电池放电电量-1
    Wait Until Keyword Succeeds    20    1    信号量数据值为    电池管理状态    测试
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    -10    0.001    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    电池低温下电控制状态    动作
    wait until keyword succeeds    5m    1    判断告警存在    电池低温下电
    ${结束时间}    获取系统时间
    ${结束SOC}    获取web实时数据    电池组当前容量比率-1
    ${结束电池电压}    获取web实时数据    电池电压-1
    ${结束电量}    获取web实时数据    电池放电电量-1
    ${结束数量}    获取web事件记录数量    电池测试记录
    ${持续时间}    获取web实时数据    电池状态持续时间
    Wait Until Keyword Succeeds    20    1    信号量数据值为    电池管理状态    充电
    @{记录内容}    获取web事件记录最新一条    电池测试记录
    sleep    30
    should be true    ${记录内容}[0] == ${结束数量}
    should be true    ${结束数量} == ${开始数量} + 1
    ${起始时间差}    subtract date from date    ${记录内容}[1]    ${起始时间}
    should be true    -60<=${起始时间差}<=60
    ${结束时间差}    subtract date from date    ${记录内容}[2]    ${结束时间}
    should be true    -60<=${结束时间差}<=60
    should be true    ${持续时间} - 1 <= ${记录内容}[3]  <= ${持续时间} + 1
    should be equal    '${记录内容}[4]'    '手动'
    should be equal    '${记录内容}[5]'    '电池下电'
    should be true    ${记录内容}[6] == ${起始SOC}
    should be true    ${记录内容}[7] == ${结束SOC}
    should be true    ${起始电池电压}-0.3 <= ${记录内容}[8] <= ${起始电池电压}+0.3
    should be true    ${结束电池电压}-1 <= ${记录内容}[9] <= ${结束电池电压}+1
    should be true    ${起始电量}-0.2 <= ${记录内容}[10] <= ${起始电量} +0.2
    should be true    ${结束电量}-0.2<= ${记录内容}[11] <= ${结束电量}+0.2
    #恢复
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    电池低温下电控制状态    恢复
    wait until keyword succeeds    4m    2    判断告警不存在    电池低温下电
    显示属性配置    电池低温下电控制状态    数字量    web_attr=Off    gui_attr=Off
    关闭负载输出
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池低温下电温度    电池低温下电使能    负载一次下电使能    负载二次下电使能    电池下电使能

铁锂电池测试记录保存内容（周期进入最大时间退出）
    
    铁锂电池管理初始化
    设置web参数量    电池测试周期    60    #默认0天
    设置web参数量    电池检测周期    0    #默认30
    sleep    5
    ${缺省值}    获取web参数上下限范围_有单位    测试最长时间
    ${可设置范围}    获取web参数可设置范围    测试最长时间
    ${测试最长时间设置值}    run keyword if    '${缺省值}[3]'== 'Hour'    evaluate    ${可设置范围}[0]+1
    ...    ELSE    evaluate    ${可设置范围}[0]+5
    ${测试最长时间转换后}    run keyword if    '${缺省值}[3]'== 'Hour'    evaluate    ${测试最长时间设置值}*60
    ...    ELSE    set variable    ${测试最长时间设置值}
    设置web参数量    测试最长时间    ${测试最长时间设置值}
    sleep    5
    ${开始数量}    获取web事件记录数量    电池测试记录
    run keyword if    ${开始数量}>97    删除历史记录    电池测试记录
    sleep    30
    ${开始数量}    获取web事件记录数量    电池测试记录
    ${电池测试周期}    获取web参数量    电池测试周期
    ${下次测试时间}    获取web实时数据    下次测试时间
    ${下次测试时间0}    Subtract Time From Date    ${下次测试时间}    5s    #比10:00:00差5s
    设置系统时间    ${下次测试时间0}
    ${起始电池电压}    获取web实时数据    电池电压-1
    sleep    8
    ${修改时间后的系统时间}    获取系统时间
    ${起始时间}    获取系统时间
    ${起始SOC}    获取web实时数据    电池组当前容量比率-1
    ${起始电量}    获取web实时数据    电池放电电量-1
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    测试
    ${等待时间}    evaluate    ${测试最长时间转换后}+2
    Wait Until Keyword Succeeds    ${等待时间}m    1    信号量数据值为    电池管理状态    充电
    ${持续时间}    获取web实时数据    电池状态持续时间
    ${结束时间}    获取系统时间
    ${结束SOC}    获取web实时数据    电池组当前容量比率-1
    ${结束电池电压}    获取web实时数据    电池电压-1
    ${结束电量}    获取web实时数据    电池放电电量-1
    sleep    30
    ${结束数量}    获取web事件记录数量    电池测试记录
    @{记录内容}    获取web事件记录最新一条    电池测试记录
    should be true    ${记录内容}[0] == ${结束数量}
    should be true    ${结束数量} == ${开始数量} + 1
    ${起始时间差}    subtract date from date    ${记录内容}[1]    ${起始时间}
    should be true    -60<=${起始时间差}<=60
    ${结束时间差}    subtract date from date    ${记录内容}[2]    ${结束时间}
    should be true    -60<=${结束时间差}<=60
    should be true    ${持续时间} - 1 <= ${记录内容}[3]  <= ${持续时间} + 1
    should be equal    '${记录内容}[4]'    '定期'
    should be equal    '${记录内容}[5]'    '测试最大时间'
    should be true    ${记录内容}[6] == ${起始SOC}
    should be true    ${记录内容}[7] == ${结束SOC}
    should be true    ${起始电池电压}-0.3 <= ${记录内容}[8] <= ${起始电池电压}+0.3
    should be true    ${结束电池电压}-1 <= ${记录内容}[9] <= ${结束电池电压}+1
    should be true    ${起始电量}-0.2 <= ${记录内容}[10] <= ${起始电量} +0.2
    should be true    ${结束电量}-0.2<= ${记录内容}[11] <= ${结束电量}+0.2
    [Teardown]    Run keywords    同步系统时间
    ...    AND    设置web设备参数量为默认值    电池测试周期    电池检测周期
    ...    AND    设置web参数量    测试最长时间    ${缺省值}[0]

铁锂电池测试记录掉电保存
    
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    ${记录数量}    获取web事件记录数量    电池测试记录
    run keyword if    ${记录数量}>990    删除历史记录    电池测试记录
    测试台上下电操作    ${测试台编号}    OFF
    sleep    15
    测试台上下电操作    ${测试台编号}    ON
    实时告警刷新完成
    sleep    5
    ${记录数量1}    获取web事件记录数量    电池测试记录
    should be true    ${记录数量}==${记录数量1}

铁锂电池测试记录最大条数测试
    [Tags]    notest
    
    铁锂电池管理初始化
    sleep    20
    ${起始时间}    获取系统时间
    #产生测试记录
    FOR    ${k}    IN RANGE    101
        Wait Until Keyword Succeeds    10    1    设置web控制量    启动测试
        Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    测试
        sleep    10
        Wait Until Keyword Succeeds    10    1    设置web控制量    启动充电
        Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    充电
        sleep    10
        ${记录数量}    获取web事件记录数量    电池测试记录
        exit for loop if    ${记录数量}>=102
    END
    sleep    2m
    ${记录数量}    获取web事件记录数量    电池测试记录
    should be true    ${记录数量} == 100
    Wait Until Keyword Succeeds    10    1    设置web控制量    启动测试
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    测试
    sleep    10
    Wait Until Keyword Succeeds    10    1    设置web控制量    启动充电
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    充电
    sleep    2m
    ${记录数量}    获取web事件记录数量    电池测试记录
    should be true    ${记录数量} == 100
