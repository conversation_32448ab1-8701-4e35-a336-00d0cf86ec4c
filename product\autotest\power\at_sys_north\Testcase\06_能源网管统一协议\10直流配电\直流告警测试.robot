*** Settings ***
Suite Setup       Run Keywords    主动告警测试前置条件    ${CSU_role}
...   AND  测试用例前置条件
Suite Teardown    Run Keywords    设置web设备参数量为默认值    CSU主动告警使能
...   AND  测试用例后置条件
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Default Tags      T1-1
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0002_直流电压高
    [Setup]    Run keywords     重置电池模拟器输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    关闭交流源输出
    实时告警刷新完成
    ${级别设置值}    获取web参数量    直流电压高
    run keyword if    '${级别设置值}'=='屏蔽'    设置web设备参数量为默认值    直流电压高
    ${直流电压高告警}    判断告警不存在_带返回值    直流电压高
    run keyword if    '${直流电压高告警}' != '[]'    向下调节电池电压    52
    ${可设置范围}    获取web参数可设置范围    直流电压高阈值
    设置web参数量    直流电压高阈值    ${可设置范围}[0]
    ${ 直流电压高阈值}    获取web参数量    直流电压高阈值
    向上调节电池电压    ${ 直流电压高阈值}+0.5
    sleep    3m
    ${电压1}    获取web实时数据    直流电压
    ${ 直流电压高阈值}    获取web参数量    直流电压高阈值
    wait until keyword succeeds    30    2    查询指定告警信息    直流电压高
    wait until keyword succeeds    30    1    设置web参数量    直流电压高    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    直流电压高
    ${直流电压高告警}    判断告警存在_带返回值    直流电压高
    should not be true    ${直流电压高告警}
    ${告警级别取值约定dict}    获取web参数的取值约定    直流电压高
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    直流电压高    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    直流电压高
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别}'=='主要'    主动告警上送判断2    power_sm    直流配电    直流电压高
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    直流电压高    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别}
    END
    wait until keyword succeeds    30    1    设置web参数量    直流电压高    主要
    向下调节电池电压    ${ 直流电压高阈值}-0.5
    wait until keyword succeeds    5m    1    判断告警不存在    直流电压高
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    直流电压高    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    Run keywords    直流电压参数恢复默认值
    ...    AND    设置web参数量    直流电压高    主要
    ...    AND    重置电池模拟器输出
    ...    AND    打开交流源输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0003_直流电压低
    [Setup]    Run keywords    重置电池模拟器输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    关闭交流源输出
    实时告警刷新完成
    ${级别设置值}    获取web参数量    直流电压低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web设备参数量为默认值    直流电压低
    ${直流电压低告警}    判断告警不存在_带返回值    直流电压低
    run keyword if    '${直流电压低告警}' != '[]'    向上调节电池电压    53.5
    ${可设置范围}    获取web参数可设置范围    直流电压低阈值
    设置web参数量    直流电压低阈值    ${可设置范围}[1]
    ${ 直流电压低阈值}    获取web参数量    直流电压低阈值
    向下调节电池电压    ${ 直流电压低阈值}-0.5
    wait until keyword succeeds    5m    2    查询指定告警信息    直流电压低
    wait until keyword succeeds    30    1    设置web参数量    直流电压低    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    直流电压低
    ${直流电压低告警}    判断告警存在_带返回值    直流电压低
    should not be true    ${直流电压低告警}
    ${告警级别取值约定dict}    获取web参数的取值约定    直流电压低
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    直流电压低    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    直流电压低
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别}'=='主要'    主动告警上送判断2    power_sm    直流配电    直流电压低
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    直流电压低    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别}
    END
    设置web参数量    直流电压低    主要
    向上调节电池电压    ${直流电压低阈值}+1
    wait until keyword succeeds    5m    1    判断告警不存在    直流电压低
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    直流电压低    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    Run keywords    直流电压参数恢复默认值
    ...    AND    设置web参数量    直流电压低    主要
    ...    AND    重置电池模拟器输出
    ...    AND    打开交流源输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0004_直流电压过高告警
    [Setup]    Run keywords    重置电池模拟器输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    关闭交流源输出
    实时告警刷新完成
    ${级别设置值}    获取web参数量    直流电压过高
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    直流电压过高    严重
    ${直流电压过高告警}    判断告警不存在_带返回值    直流电压过高
    run keyword if    '${直流电压过高告警}' != '[]'    向下调节电池电压    52
    ${可设置范围}    获取web参数可设置范围    直流电压高阈值
    设置web参数量    直流电压高阈值    ${可设置范围}[0]
    ${可设置范围}    获取web参数可设置范围    直流电压过高阈值
    设置web参数量    直流电压过高阈值    ${可设置范围}[0]
    ${ 直流电压过高阈值}    获取web参数量    直流电压过高阈值
    向上调节电池电压    ${ 直流电压过高阈值}+0.5
    wait until keyword succeeds    5m    2    查询指定告警信息    直流电压过高
    wait until keyword succeeds    30    1    设置web参数量    直流电压过高    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    直流电压过高
    ${直流电压过高告警}    判断告警存在_带返回值    直流电压过高
    should not be true    ${直流电压过高告警}
    ${告警级别取值约定dict}    获取web参数的取值约定    直流电压过高
    FOR    ${告警级别}    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    直流电压过高    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    直流电压过高
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别}'=='主要'    主动告警上送判断2    power_sm    直流配电    直流电压过高
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    直流电压过高    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别}
    END
    设置web参数量    直流电压过高    主要
    向下调节电池电压    ${ 直流电压过高阈值}-0.5
    wait until keyword succeeds    5m    1    判断告警不存在    直流电压过高
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    直流电压过高    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    Run keywords    直流电压参数恢复默认值
    ...    AND    设置web参数量    直流电压过高    主要
    ...    AND    重置电池模拟器输出
    ...    AND    打开交流源输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0005_直流电压过低告警
    [Setup]    Run keywords    重置电池模拟器输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    关闭交流源输出
    实时告警刷新完成
    #获取告警级别和输出干接点设置值
    ${级别设置值}    获取web参数量    直流电压过低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    直流电压过低    严重
    ${直流电压过高告警}    判断告警不存在_带返回值    直流电压过低
    run keyword if    '${直流电压过高告警}' != '[]'    向上调节电池电压    53.5
    ${可设置范围}    获取web参数可设置范围    直流电压低阈值
    设置web参数量    直流电压低阈值    ${可设置范围}[1]
    ${可设置范围}    获取web参数可设置范围    直流电压过低阈值
    设置web参数量    直流电压过低阈值    ${可设置范围}[1]
    ${ 直流电压过低阈值}    获取web参数量    直流电压过低阈值
    向下调节电池电压    ${ 直流电压过低阈值}-0.5
    wait until keyword succeeds    5m    2    查询指定告警信息    直流电压过低
    wait until keyword succeeds    30    1    设置web参数量    直流电压过低    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    直流电压过低
    ${直流电压过低告警}    判断告警存在_带返回值    直流电压过低
    should not be true    ${直流电压过低告警}
    ${告警级别取值约定dict}    获取web参数的取值约定    直流电压过低
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    直流电压过低    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    直流电压过低
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别}'=='主要'    主动告警上送判断2    power_sm    直流配电    直流电压过低
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    直流电压过低    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别}
    END
    wait until keyword succeeds    30    1    设置web参数量    直流电压过低    主要
    向上调节电池电压    ${ 直流电压过低阈值}+1
    wait until keyword succeeds    1m    1    判断告警不存在    直流电压过低
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    直流电压过低    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    Run keywords    直流电压参数恢复默认值
    ...    AND    设置web参数量    直流电压过低    主要
    ...    AND    重置电池模拟器输出
    ...    AND    打开交流源输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0005_一次下电分路断
    [Setup]
    实时告警刷新完成
    ${级别设置值}    获取web参数量    一次下电分路断_1
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    一次下电分路断_1    主要
    wait until keyword succeeds    1m    1    判断告警不存在    一次下电分路断
    模拟数字量告警    下电告警    ON
    ${告警级别取值约定dict}    获取web参数的取值约定    一次下电分路断
    ${级别设置值}    获取web参数量    一次下电分路断_1
    FOR    ${分路序号}    IN RANGE    4    1    -1
        wait until keyword succeeds    15m    1    判断告警存在    一次下电分路断_${分路序号}
        sleep    5
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    一次下电分路断_${分路序号}    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${级别设置值}
    END
    模拟数字量告警    下电告警    OFF
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    一次下电分路断
    FOR    ${分路序号}    IN RANGE    4    1    -1
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    一次下电分路断_${分路序号}    ${SSH}
        should not be true    ${power_sm告警存在}[0]
    END
    [Teardown]    run keywords    设置web设备参数量为默认值    一次下电分路断_1
    ...    AND    模拟数字量告警    下电告警    OFF
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0005_二次下电分路断
    [Setup]
    实时告警刷新完成
    ${级别设置值}    获取web参数量    二次下电分路断_1
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    二次下电分路断_1    主要
    wait until keyword succeeds    1m    1    判断告警不存在    二次下电分路断
    模拟数字量告警    下电告警    ON
    ${告警级别取值约定dict}    获取web参数的取值约定    一次下电分路断
    ${级别设置值}    获取web参数量    二次下电分路断_1
    FOR    ${分路序号}    IN RANGE    4    1    -1
        wait until keyword succeeds    15m    1    判断告警存在    二次下电分路断_${分路序号}
        sleep    5
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    二次下电分路断_${分路序号}    ${SSH}
        should be true    ${power_sm告警存在}[0]
    END
    模拟数字量告警    下电告警    OFF
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    二次下电分路断
    FOR    ${分路序号}    IN RANGE    4    1    -1
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    二次下电分路断_${分路序号}    ${SSH}
        should not be true    ${power_sm告警存在}[0]
    END
    [Teardown]    run keywords    设置web设备参数量为默认值    二次下电分路断_1
    ...    AND    模拟数字量告警    下电告警    OFF
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0005_电池下电分路断
    [Tags]    notest
    [Setup]
    实时告警刷新完成
    ${级别设置值}    获取web参数量    电池下电分路断_1
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池下电分路断_1    主要
    wait until keyword succeeds    15m    1    判断告警不存在    电池下电分路断
    模拟数字量告警    下电告警    ON
    ${告警级别取值约定dict}    获取web参数的取值约定    电池下电分路断
    ${级别设置值}    获取web参数量    电池下电分路断_1
    FOR    ${分路序号}    IN RANGE    4    1    -1
        wait until keyword succeeds    15m    1    判断告警存在    电池下电分路断_${分路序号}
        sleep    5
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    电池下电分路断_${分路序号}    ${SSH}
        should be true    ${power_sm告警存在}[0]
    END
    模拟数字量告警    下电告警    OFF
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池下电分路断
    FOR    ${分路序号}    IN RANGE    4    1    -1
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    电池下电分路断_${分路序号}    ${SSH}
        should not be true    ${power_sm告警存在}[0]
    END
    [Teardown]    run keywords    设置web设备参数量为默认值    电池下电分路断_1
    ...    AND    模拟数字量告警    下电告警    OFF
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0005_一次下电扩展分路断
    [Setup]
    实时告警刷新完成
    ${级别设置值}    获取web参数量    一次下电扩展分路断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    一次下电扩展分路断    主要
    wait until keyword succeeds    1m    1    判断告警不存在    一次下电扩展分路断
    设置web参数量    一次下电扩展分路断    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    一次下电扩展分路断
    ${告警}    判断告警存在_带返回值    一次下电扩展分路断
    should not be true    ${告警}
    模拟数字量告警    下电告警    ON
    ${告警级别取值约定dict}    获取web参数的取值约定    一次下电扩展分路断
    FOR    ${告警级别设置}    IN    主要    次要    警告    严重
        Wait Until Keyword Succeeds    30    1    设置web参数量    一次下电扩展分路断    ${告警级别设置}
        sleep   5
        wait until keyword succeeds    15m    1    查询指定告警信息    一次下电扩展分路断
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    直流配电    一次下电扩展分路断
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    一次下电扩展分路断    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
        ${sm告警级别}    获取web告警属性    一次下电扩展分路断    告警级别
        should be equal    ${告警级别设置}    ${sm告警级别}
    END
    模拟数字量告警    下电告警    OFF
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    一次下电扩展分路断
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    一次下电扩展分路断    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    设置web设备参数量为默认值    一次下电扩展分路断
    ...    AND    模拟数字量告警    下电告警    OFF
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0005_二次下电扩展分路断
    [Setup]
    实时告警刷新完成
    ${级别设置值}    获取web参数量    二次下电扩展分路断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    二次下电扩展分路断    主要
    wait until keyword succeeds    1m    1    判断告警不存在    二次下电扩展分路断
    设置web参数量    二次下电扩展分路断    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    二次下电扩展分路断
    ${告警}    判断告警存在_带返回值    二次下电扩展分路断
    should not be true    ${告警}
    模拟数字量告警    下电告警    ON
    ${告警级别取值约定dict}    获取web参数的取值约定    二次下电扩展分路断
    FOR    ${告警级别设置}    IN    主要    次要    警告    严重
        Wait Until Keyword Succeeds    30    1    设置web参数量    二次下电扩展分路断    ${告警级别设置}
        sleep   5
        wait until keyword succeeds    15m    1    查询指定告警信息    二次下电扩展分路断
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    直流配电    二次下电扩展分路断
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    二次下电扩展分路断    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
        ${sm告警级别}    获取web告警属性    二次下电扩展分路断    告警级别
        should be equal    ${告警级别设置}    ${sm告警级别}
    END
    模拟数字量告警    下电告警    OFF
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    二次下电扩展分路断
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    二次下电扩展分路断    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    设置web设备参数量为默认值    二次下电扩展分路断
    ...    AND    模拟数字量告警    下电告警    OFF
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0005_电池下电扩展分路断
    [Tags]    notest
    [Setup]
    实时告警刷新完成
    ${级别设置值}    获取web参数量    电池下电扩展分路断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池下电扩展分路断    主要
    wait until keyword succeeds    1m    1    判断告警不存在    电池下电扩展分路断
    设置web参数量    电池下电扩展分路断    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    电池下电扩展分路断
    ${告警}    判断告警存在_带返回值    电池下电扩展分路断
    should not be true    ${告警}
    模拟数字量告警    下电告警    ON
    ${告警级别取值约定dict}    获取web参数的取值约定    电池下电扩展分路断
    FOR    ${告警级别设置}    IN    主要    次要    警告    严重
        Wait Until Keyword Succeeds    5m    1    设置web参数量    电池下电扩展分路断    ${告警级别设置}
        sleep   5
        wait until keyword succeeds    15m    1    查询指定告警信息    电池下电扩展分路断
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    直流配电    电池下电扩展分路断
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    电池下电扩展分路断    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
        ${sm告警级别}    获取web告警属性    电池下电扩展分路断    告警级别
        should be equal    ${告警级别设置}    ${sm告警级别}
    END
    模拟数字量告警    下电告警    OFF
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池下电扩展分路断
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    电池下电扩展分路断    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    设置web设备参数量为默认值    电池下电扩展分路断
    ...    AND    模拟数字量告警    下电告警    OFF
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0005_直流防雷器异常
    [Setup]    run keywords    模拟数字量告警    直流防雷器异常    OFF
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    实时告警刷新完成
    ${级别设置值}    获取web参数量    直流防雷器异常
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    直流防雷器异常    主要
    wait until keyword succeeds    1m    1    判断告警不存在    直流防雷器异常
    设置web参数量    直流防雷器异常    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    直流防雷器异常
    ${告警}    判断告警存在_带返回值    直流防雷器异常
    should not be true    ${告警}
    模拟数字量告警    直流防雷器异常    ON
    ${告警级别取值约定dict}    获取web参数的取值约定    直流防雷器异常
    FOR    ${告警级别设置}    IN    主要    次要    警告    严重
        Wait Until Keyword Succeeds    30    1    设置web参数量    直流防雷器异常    ${告警级别设置}
        sleep   5
        wait until keyword succeeds    15m    1    查询指定告警信息    直流防雷器异常
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    直流配电    直流防雷器异常
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    直流防雷器异常    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
        ${sm告警级别}    获取web告警属性    直流防雷器异常    告警级别
        should be equal    ${告警级别设置}    ${sm告警级别}
    END
    模拟数字量告警    直流防雷器异常    OFF
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    直流防雷器异常
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    直流防雷器异常    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    设置web设备参数量为默认值    直流防雷器异常
    ...    AND    模拟数字量告警    直流防雷器异常    OFF
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
