*** Settings ***
Documentation     还有八个一次下电分路断、八个二次下电分路断、八个电池下电分路断、一个一次下电扩展分路断、一个二次下电扩展分路断、一个电池下电扩展分路断
Test Setup        Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接
Default Tags      T1-1
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
uniform_0004_直流电压低√
    [Documentation]    直流电压高范围：53-59，默认58.5；>=直流电压低+1，<=直流电压过高。。。
    ...    直流电压低范围：44-55，默认46.5；<=直流电压高-1，>=直流电压过低。。。
    ...    直流电压过高：53-59，默认59。。。
    ...    直流电压过低：44-55，默认46.。。
    [Setup]    Run keywords    测试用例前置条件
    ...    AND    重置电池模拟器输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    关闭交流源输出
    实时告警刷新完成
    #获取参数/默认值
    #获取告警级别
    ${级别设置值}    获取web参数量    直流电压低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    直流电压低    主要
    ${直流电压低告警}    判断告警不存在_带返回值    直流电压低
    run keyword if    '${直流电压低告警}' != '[]'    向上调节电池电压    53.5
    ${可设置范围}    获取web参数可设置范围    直流电压低阈值
    设置web参数量    直流电压低阈值    ${可设置范围}[1]
    ${ 直流电压低阈值}    获取web参数量    直流电压低阈值
    向下调节电池电压    ${ 直流电压低阈值}-1
    wait until keyword succeeds    5m    2    查询指定告警信息    直流电压低
    wait until keyword succeeds    30    1    设置web参数量    直流电压低    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    直流电压低
    ${直流电压低告警}    判断告警存在_带返回值    直流电压低
    should not be true    ${直流电压低告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        设置web参数量    直流电压低    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    直流电压低
        sleep    5
    #统一监控协议查询告警存在
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    直流电压低
        should be true    ${统一监控协议告警结果}
    #查询web端级别
        ${直流电压低告警级别}    获取web告警属性    直流电压低    告警级别
        should be equal    ${告警级别}    ${直流电压低告警级别}
    END
    设置web参数量    直流电压低    主要
    #无告警后查询判断
    向上调节电池电压    ${ 直流电压低阈值}+1
    wait until keyword succeeds    5m    1    判断告警不存在    直流电压低
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    直流电压低
    should not be true    ${统一监控协议告警结果}
    [Teardown]    Run keywords    直流电压参数恢复默认值
    ...    AND    设置web参数量    直流电压低    主要
    ...    AND    重置电池模拟器输出
    ...    AND    打开交流源输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0006_直流电压过低
    [Documentation]    直流电压高范围：53-59，默认58.5；>=直流电压低+1，<=直流电压过高。。。
    ...    直流电压低范围：44-55，默认46.5；<=直流电压高-1，>=直流电压过低。。。
    ...    直流电压过高：53-59，默认59。。。
    ...    直流电压过低：44-55，默认46.。。
    [Setup]    Run keywords    测试用例前置条件
    ...    AND    重置电池模拟器输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    关闭交流源输出
    实时告警刷新完成
    #获取告警级别
    ${级别设置值}    获取web参数量    直流电压过低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    直流电压过低    严重
    Comment    wait until keyword succeeds    5m    1    查询指定告警信息不为    直流电压高
    ${直流电压过高告警}    判断告警不存在_带返回值    直流电压过低
    run keyword if    '${直流电压过高告警}' != '[]'    向上调节电池电压    53.5
    ${可设置范围}    获取web参数可设置范围    直流电压低阈值
    设置web参数量    直流电压低阈值    ${可设置范围}[1]
    ${可设置范围}    获取web参数可设置范围    直流电压过低阈值
    设置web参数量    直流电压过低阈值    ${可设置范围}[1]
    ${ 直流电压过低阈值}    获取web参数量    直流电压过低阈值
    向下调节电池电压    ${ 直流电压过低阈值}-1
    wait until keyword succeeds    5m    2    查询指定告警信息    直流电压过低
    wait until keyword succeeds    30    1    设置web参数量    直流电压过低    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    直流电压过低
    ${直流电压过低告警}    判断告警存在_带返回值    直流电压过低
    should not be true    ${直流电压过低告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    直流电压过低    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    直流电压过低
        sleep    3
    #统一监控协议查询告警存在
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    直流电压低
        should be true    ${统一监控协议告警结果}
    #查询web端级别
        ${直流电压过低告警级别}    获取web告警属性    直流电压过低    告警级别
        should be equal    '${告警级别}'    '${直流电压过低告警级别}'
    END
    wait until keyword succeeds    30    1    设置web参数量    直流电压过低    主要
    #无告警后查询判断
    向上调节电池电压    ${ 直流电压过低阈值}+1
    wait until keyword succeeds    5m    1    判断告警不存在    直流电压过低
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    直流电压低
    should not be true    ${统一监控协议告警结果}
    [Teardown]    Run keywords    直流电压参数恢复默认值
    ...    AND    设置web参数量    直流电压过低    主要
    ...    AND    重置电池模拟器输出
    ...    AND    打开交流源输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0008_直流电压高√
    [Setup]    Run keywords    测试用例前置条件
    ...    AND    重置电池模拟器输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    关闭交流源输出
    实时告警刷新完成
    #获取参数/默认值
    #获取告警级别
    ${级别设置值}    获取web参数量    直流电压高
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    直流电压高    严重
    ${直流电压高告警}    判断告警不存在_带返回值    直流电压高
    run keyword if    '${直流电压高告警}' != '[]'    向下调节电池电压    52
    ${可设置范围}    获取web参数可设置范围    直流电压高阈值
    设置web参数量    直流电压高阈值    ${可设置范围}[0]
    ${ 直流电压高阈值}    获取web参数量    直流电压高阈值
    向上调节电池电压    ${ 直流电压高阈值}+1
    wait until keyword succeeds    5m    2    查询指定告警信息    直流电压高
    wait until keyword succeeds    30    1    设置web参数量    直流电压高    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    直流电压高
    ${直流电压高告警}    判断告警存在_带返回值    直流电压高
    should not be true    ${直流电压高告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    直流电压高    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    直流电压高
        sleep    5
        #    统一监控协议查询告警存在
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    直流电压高
        should be true    ${统一监控协议告警结果}
        #    查询web端级别
        ${直流电压高告警级别}    获取web告警属性    直流电压高    告警级别
        should be equal    '${告警级别}'    '${直流电压高告警级别}'
    END
    wait until keyword succeeds    30    1    设置web参数量    直流电压高    主要
    #无告警后查询判断
    向下调节电池电压    ${ 直流电压高阈值}-1
    wait until keyword succeeds    5m    1    判断告警不存在    直流电压高
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    直流电压高
    should not be true    ${统一监控协议告警结果}
    [Teardown]    Run keywords    直流电压参数恢复默认值
    ...    AND    设置web参数量    直流电压高    主要
    ...    AND    重置电池模拟器输出
    ...    AND    打开交流源输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0010_直流电压过高X
    [Documentation]    干接点实际是否动作需要将输出干接点连接到输入干接点，输出干接点接UIB_X3_DI1；请在测试前连接好
    [Setup]    Run keywords    测试用例前置条件
    ...    AND    重置电池模拟器输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    关闭交流源输出
    实时告警刷新完成
    #获取参数/默认值
    #获取告警级别
    ${级别设置值}    获取web参数量    直流电压过高
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    直流电压过高    严重
    ${直流电压过高告警}    判断告警不存在_带返回值    直流电压过高
    run keyword if    '${直流电压过高告警}' != '[]'    向下调节电池电压    52
    ${可设置范围}    获取web参数可设置范围    直流电压高阈值
    设置web参数量    直流电压高阈值    ${可设置范围}[0]
    ${可设置范围}    获取web参数可设置范围    直流电压过高阈值
    设置web参数量    直流电压过高阈值    ${可设置范围}[0]
    ${ 直流电压过高阈值}    获取web参数量    直流电压过高阈值
    向上调节电池电压    ${ 直流电压过高阈值}+1
    wait until keyword succeeds    5m    2    查询指定告警信息    直流电压过高
    wait until keyword succeeds    30    1    设置web参数量    直流电压过高    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    直流电压过高
    ${直流电压过高告警}    判断告警存在_带返回值    直流电压过高
    should not be true    ${直流电压过高告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    直流电压过高    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    直流电压过高
        sleep    3
        Comment    统一监控协议查询告警存在
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    直流电压高
        should be true    ${统一监控协议告警结果}
        Comment    查询web端级别    #查询web端级别
        ${直流电压过高告警级别}    获取web告警属性    直流电压过高    告警级别
        should be equal    ${告警级别}    ${直流电压过高告警级别}
    END
    #无告警后查询判断
    向下调节电池电压    ${ 直流电压过高阈值}-1
    wait until keyword succeeds    5m    1    判断告警不存在    直流电压过高
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    直流电压高
    should not be true    ${统一监控协议告警结果}
    [Teardown]    Run keywords    直流电压参数恢复默认值
    ...    AND    设置web参数量    直流电压过高    主要
    ...    AND    重置电池模拟器输出
    ...    AND    打开交流源输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0012_直流防雷器异常
    实时告警刷新完成
    ${级别设置值}    获取web参数量    直流防雷器异常
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    直流防雷器异常    主要
    wait until keyword succeeds    1m    1    判断告警不存在    直流防雷器异常
    设置web参数量    直流防雷器异常    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    直流防雷器异常
    ${告警}    判断告警存在_带返回值    直流防雷器异常
    should not be true    ${告警}
    模拟数字量告警    直流防雷器异常    ON
    FOR    ${告警级别设置}    IN    主要    次要    警告    严重
        Wait Until Keyword Succeeds    30    1    设置web参数量    直流防雷器异常    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    直流防雷器异常
        sleep    5
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    直流防雷器异常
        should be true    ${统一监控协议告警结果}
        ${sm告警级别}    获取web告警属性    直流防雷器异常    告警级别
        should be equal    ${告警级别设置}    ${sm告警级别}
    END
    模拟数字量告警    直流防雷器异常    OFF
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    直流防雷器异常
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    直流防雷器异常
    should not be true    ${统一监控协议告警结果}
    [Teardown]    run keywords    设置web设备参数量为默认值    直流防雷器异常
    ...    AND    模拟数字量告警    直流防雷器异常    OFF
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0014_电池高温下电√
    [Setup]    Run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    电池管理初始化
    #获取参数/默认值
    wait until keyword succeeds    10    1    设置web参数量    电池高温下电使能    允许
    ${告警级别}    获取web参数量    电池高温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    电池高温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    电池高温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    ${电池电压}    获取web实时数据    电池电压-1
    sleep    3
    ${可设范围}    获取web参数可设置范围    电池高温下电温度
    设置web参数量    电池高温下电温度    ${可设范围}[0]
    run keyword and ignore error    设置web参数量    下电控制延时    0
    显示属性配置    电池高温下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    3
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    10    2    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    设置负载电压电流    53.5    10
    打开负载输出
    wait until keyword succeeds    15m    1    判断告警存在    电池高温下电
    Wait Until Keyword Succeeds    3m    2    信号量数据值为    电池高温下电控制状态    动作
    #统一监控协议查询告警存在
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    电池高温下电
    should be true    ${统一监控协议告警结果}
    #恢复
    设置web参数量    电池高温下电使能    禁止
    Comment    Wait Until Keyword Succeeds    7m    2    信号量数据值为    电池高温下电控制状态    恢复
    wait until keyword succeeds    3m    2    判断告警不存在    电池高温下电
    #无告警后查询判断
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    电池高温下电
    should not be true    ${统一监控协议告警结果}
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    设置web参数量    电池高温下电使能    允许
    Wait Until Keyword Succeeds    5    1    显示属性配置    电池高温下电控制状态    数字量    web_attr=Off    gui_attr=Off
    关闭负载输出
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池高温下电温度    电池高温下电使能
    ...    AND    关闭负载输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0016_电池低温下电√
    [Setup]    Run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    电池管理初始化
    #获取参数/默认值
    wait until keyword succeeds    10    1    设置web参数量    电池低温下电使能    允许
    ${告警级别}    获取web参数量    电池低温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    电池低温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    电池低温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    sleep    3
    ${可设范围}    获取web参数可设置范围    电池低温下电温度
    设置web参数量    电池低温下电温度    ${可设范围}[1]
    run keyword and ignore error    设置web参数量    下电控制延时    0
    显示属性配置    电池低温下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    3
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    -10    0.001    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    设置负载电压电流    53.5    10
    打开负载输出
    wait until keyword succeeds    15m    1    判断告警存在    电池低温下电
    Wait Until Keyword Succeeds    6m    2    信号量数据值为    电池低温下电控制状态    动作
    #统一监控协议查询告警存在
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    电池低温下电
    should be true    ${统一监控协议告警结果}
    #恢复
    设置web参数量    电池低温下电使能    禁止
    wait until keyword succeeds    4m    2    判断告警不存在    电池低温下电
    #无告警后查询判断
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    电池低温下电
    should not be true    ${统一监控协议告警结果}
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    设置web参数量    电池低温下电使能    允许
    Wait Until Keyword Succeeds    5    1    显示属性配置    电池低温下电控制状态    数字量    web_attr=Off    gui_attr=Off
    关闭负载输出
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池低温下电温度    电池低温下电使能
    ...    AND    关闭负载输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0018_电池下电告警√
    [Documentation]    电池下电告警不能设置为屏蔽级别，因此注释掉设置告警级别为0的语句。
    [Tags]    no test    T1-1
    [Setup]    Run keywords    测试用例前置条件
    ...    AND    重置电池模拟器输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    5
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息不为    电池下电告警
    关闭交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    电池下电告警
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
        设置web参数量    电池组容量_${i}    0
    END
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    99
    sleep    2
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电模式    电池电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电控制延时    0
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载一次下电使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池下电使能    允许
    ${可设置范围}    获取web参数可设置范围    电池下电恢复回差
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池下电恢复回差    ${可设置范围}[0]
    显示属性配置    电池下电控制状态    数字量    web_attr=On    gui_attr=On
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${可设置范围}[1]
    ${电压低可设置范围}    获取web参数可设置范围    电池电压低阈值
    设置web参数量    电池电压低阈值    ${电压低可设置范围}[1]
    ${下电电压可设置范围}    获取web参数可设置范围    电池下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池下电电压    ${下电电压可设置范围}[1]
    设置负载电压电流    53.5    10
    打开负载输出
    ${下电电压设置值}    获取web参数量    电池下电电压
    ${下电电压}    evaluate    ${下电电压设置值}-1
    向下调节电池电压    ${下电电压}
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池下电控制状态    动作
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    电池下电告警    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池下电告警
        sleep    5
    #统一监控协议查询告警存在
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    电池下电告警
        should be true    ${统一监控协议告警结果}
    #查询web端级别
        ${电池下电告警级别}    获取web告警属性    电池下电告警    告警级别
        should be equal    ${告警级别}    ${电池下电告警级别}
    END
    设置web参数量    电池下电告警    严重
    向上调节电池电压    53.5
    关闭负载输出
    打开交流源输出
    Wait Until Keyword Succeeds    8m    1    信号量数据值为    电池下电控制状态    恢复
    Wait Until Keyword Succeeds    8m    2    查询指定告警信息不为    电池下电告警
    #无告警后查询判断
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    电池下电告警
    should not be true    ${统一监控协议告警结果}
    显示属性配置    电池下电控制状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    电池下电电压    测试终止电压    电池电压低阈值    负载一次下电使能    负载二次下电使能
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出
    ...    AND    重置电池模拟器输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0020_二次下电告警√
    [Documentation]    二次下电告警不能设置为屏蔽级别，因此注释掉设置告警级别为0的语句。
    [Setup]    Run keywords    测试用例前置条件
    ...    AND    重置电池模拟器输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    同时设置三相电压频率    220    50
    打开交流源输出
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息不为    二次下电告警
    关闭交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    二次下电告警
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
        设置web参数量    电池组容量_${i}    0
    END
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    99
    sleep    2
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    100
    wait until keyword succeeds    30    1    设置web参数量    下电模式    电池电压    #默认1电池电压
    wait until keyword succeeds    30    1    设置web参数量    下电控制延时    0    #默认0
    wait until keyword succeeds    30    1    设置web参数量    负载一次下电使能    禁止    #默认1允许
    wait until keyword succeeds    30    1    设置web参数量    负载二次下电使能    允许    #默认1允许
    ${可设置范围}    获取web参数可设置范围    二次下电恢复回差
    Wait Until Keyword Succeeds    10    1    设置web参数量    二次下电恢复回差    ${可设置范围}[0]
    显示属性配置    二次下电控制状态    数字量    web_attr=On    gui_attr=On
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${可设置范围}[1]
    ${电压低可设置范围}    获取web参数可设置范围    电池电压低阈值
    设置web参数量    电池电压低阈值    ${电压低可设置范围}[1]
    ${恢复时间可设置范围}    获取web参数可设置范围    二次下电恢复时间
    run keyword and ignore error    设置web参数量    二次下电恢复时间    ${恢复时间可设置范围}[0]
    ${二次下电电压可设置范围}    获取web参数可设置范围    负载二次下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电电压    ${二次下电电压可设置范围}[1]
    设置负载电压电流    53.5    10
    打开负载输出
    ${下电电压设置值}    获取web参数量    负载二次下电电压
    ${下电电压}    evaluate    ${下电电压设置值}-1
    向下调节电池电压    ${下电电压}
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    二次下电控制状态    动作
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    二次下电告警    ${告警级别设置}
        sleep    3
        wait until keyword succeeds    1m    1    查询指定告警信息    二次下电
    #统一监控协议查询告警存在
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    二次下电告警
        should be true    ${统一监控协议告警结果}
    #查询web端级别
        ${二次下电告警级别}    获取web告警属性    二次下电告警    告警级别
        should be equal    '${告警级别设置}'    '${二次下电告警级别}'
    END
    wait until keyword succeeds    30    1    设置web参数量    二次下电告警    严重    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    向上调节电池电压    53.5
    关闭负载输出
    打开交流源输出
    Wait Until Keyword Succeeds    8m    1    信号量数据值为    二次下电控制状态    恢复
    Wait Until Keyword Succeeds    8m    2    查询指定告警信息不为    二次下电告警
    #无告警后查询判断
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    二次下电告警
    should not be true    ${统一监控协议告警结果}
    显示属性配置    二次下电控制状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    负载二次下电电压    测试终止电压    电池电压低阈值    负载一次下电使能    二次下电恢复时间
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出
    ...    AND    重置电池模拟器输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0022_一次下电告警√
    [Documentation]    一次下电告警不能设置为屏蔽级别，因此注释掉设置告警级别为0的语句。
    [Setup]    Run keywords    测试用例前置条件
    ...    AND    重置电池模拟器输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    同时设置三相电压频率    220    50
    打开交流源输出
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息不为    一次下电告警
    关闭交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    一次下电告警
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
        设置web参数量    电池组容量_${i}    0
    END
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    99
    sleep    2
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    30    1    设置web参数量    下电模式    电池电压    #默认1电池电压
    Wait Until Keyword Succeeds    30    1    设置web参数量    下电控制延时    0    #默认0
    Wait Until Keyword Succeeds    30    1    设置web参数量    负载一次下电使能    允许    #默认1允许
    Wait Until Keyword Succeeds    30    1    设置web参数量    负载二次下电使能    禁止    #默认1允许
    ${可设置范围}    获取web参数可设置范围    一次下电恢复回差
    Wait Until Keyword Succeeds    10    1    设置web参数量    一次下电恢复回差    ${可设置范围}[0]
    显示属性配置    一次下电控制状态    数字量    web_attr=On    gui_attr=On
    ${恢复时间可设置范围}    获取web参数可设置范围    一次下电恢复时间
    run keyword and ignore error    设置web参数量    一次下电恢复时间    ${恢复时间可设置范围}[0]
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${可设置范围}[1]
    ${电压低可设置范围}    获取web参数可设置范围    电池电压低阈值
    设置web参数量    电池电压低阈值    ${电压低可设置范围}[1]
    ${电池下电电压可设置范围}    获取web参数可设置范围    负载一次下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载一次下电电压    ${电池下电电压可设置范围}[1]
    设置负载电压电流    53.5    10
    打开负载输出
    ${一次下电电压设置值}    获取web参数量    负载一次下电电压
    ${一次下电电压}    evaluate    ${一次下电电压设置值}-1
    向下调节电池电压    ${一次下电电压}
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    一次下电控制状态    动作
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    一次下电告警    ${告警级别设置}
        sleep    3
        wait until keyword succeeds    1m    1    查询指定告警信息    一次下电告警
    #统一监控协议查询告警存在
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    一次下电告警
        should be true    ${统一监控协议告警结果}
    #查询web端级别
        ${一次下电告警级别}    获取web告警属性    一次下电告警    告警级别
        should be equal    '${告警级别设置}'    '${一次下电告警级别}'
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    一次下电告警    严重    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    向上调节电池电压    53.5
    关闭负载输出
    打开交流源输出
    Wait Until Keyword Succeeds    8m    1    信号量数据值为    一次下电控制状态    恢复
    Wait Until Keyword Succeeds    8m    2    查询指定告警信息不为    一次下电告警
    #无告警后查询判断
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    一次下电告警
    should not be true    ${统一监控协议告警结果}
    显示属性配置    一次下电控制状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    负载一次下电电压    测试终止电压    电池电压低阈值    一次下电恢复时间
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出
    ...    AND    重置电池模拟器输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0024_电池电压过低X
    [Setup]    Run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    连接交流源
    连接电池模拟器
    重置电池模拟器输出
    打开电池模拟器输出
    sleep    10
    关闭交流源输出
    实时告警刷新完成
    #获取参数/默认值
    ${级别设置值}    获取web参数量    电池电压过低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池电压过低    严重
    ${电压低告警}    判断告警不存在_带返回值    电池电压过低
    run keyword if    '${电压低告警}' != '[]'    向上调节电池电压    53.5
    ${可设置范围}    获取web参数可设置范围    电池电压过低阈值
    设置web参数量    电池电压过低阈值    ${可设置范围}[1]
    ${ 电压低阈值}    获取web参数量    电池电压过低阈值
    向下调节电池电压    ${ 电压低阈值}-1
    wait until keyword succeeds    5m    2    查询指定告警信息    电池电压过低
    wait until keyword succeeds    30    1    设置web参数量    电池电压过低    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    电池电压过低
    ${电压低告警}    判断告警存在_带返回值    电池电压过低
    should not be true    ${电压低告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    电池电压过低    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池电压过低
        sleep    5
    #统一监控协议查询告警存在
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    电池电压过低-1
        should be true    ${统一监控协议告警结果}
    #查询web端级别
        ${直流电压低告警级别}    获取web告警属性    电池电压过低-1    告警级别
        should be equal    ${告警级别}    ${直流电压低告警级别}
    END
    向上调节电池电压    ${ 电压低阈值}+1
    wait until keyword succeeds    5m    1    判断告警不存在    电池电压过低
    #无告警后查询判断
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    电池电压过低-1
    should not be true    ${统一监控协议告警结果}
    [Teardown]    Run keywords    设置web设备参数量为默认值    电池电压过低阈值
    ...    AND    设置web参数量    电池电压过低    主要
    ...    AND    打开交流源输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

电池电压过低告警
    [Setup]    Run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    关闭交流源输出
    实时告警刷新完成
    #获取参数/默认值
    ${级别设置值}    获取web参数量    电池电压过低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池电压过低    严重
    ${电压低告警}    判断告警不存在_带返回值    电池电压过低
    run keyword if    '${电压低告警}' != '[]'    向上调节电池电压    53.5
    ${可设置范围}    获取web参数可设置范围    电池电压过低阈值
    设置web参数量    电池电压过低阈值    ${可设置范围}[1]
    ${ 电压低阈值}    获取web参数量    电池电压过低阈值
    向下调节电池电压    ${ 电压低阈值}-1
    wait until keyword succeeds    5m    2    查询指定告警信息    电池电压过低
    wait until keyword succeeds    30    1    设置web参数量    电池电压过低    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    电池电压过低
    ${电压低告警}    判断告警存在_带返回值    电池电压过低
    should not be true    ${电压低告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    电池电压过低    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池电压过低
        sleep    5
        ${直流电压低告警级别}    获取web告警属性    电池电压过低-1    告警级别
        should be equal    ${告警级别}    ${直流电压低告警级别}
    END
    设置web参数量    电池电压过低    主要
    向上调节电池电压    ${ 电压低阈值}+1
    wait until keyword succeeds    5m    1    判断告警不存在    电池电压过低
    [Teardown]    Run keywords    设置web设备参数量为默认值    电池电压过低阈值
    ...    AND    设置web参数量    电池电压过低    主要
    ...    AND    打开交流源输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0026_电池放电
    [Setup]    Run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    电池管理初始化
    #获取告警级别和输出干接点设置
    ${电池放电级别}    获取web参数量    电池放电
    run keyword if    '${电池放电级别}'=='屏蔽'    设置web参数量    电池放电    严重
    sleep    3
    ${电池放电级别}    获取web参数量    电池放电
    ${放电阈值获取范围}    获取web参数上下限范围    电池放电阈值
    设置web参数量    电池放电阈值    ${放电阈值获取范围}[0]
    关闭交流源输出
    设置负载电压电流    53.5    20
    打开负载输出
    #电池放电
    run keyword and ignore error    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    系统停电
    ${电池电流}    获取web实时数据    电池电流-1
    wait until keyword succeeds    3m    2    判断告警存在    电池放电
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    电池放电    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池放电
        sleep    5
    #统一监控协议查询告警存在
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    电池放电-1
        should be true    ${统一监控协议告警结果}
    #查询web端级别
        ${获取告警级别}    获取web告警属性    电池放电-1    告警级别
        should be equal    ${告警级别}    ${获取告警级别}
    END
    #恢复
    打开交流源输出
    关闭负载输出
    Wait Until Keyword Succeeds    30    1    信号量数据值不为    电池管理状态    系统停电
    wait until keyword succeeds    3m    2    判断告警不存在    电池放电
    #无告警后查询判断
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    电池放电-1
    should not be true    ${统一监控协议告警结果}
    [Teardown]    Run keywords    设置web设备参数量为默认值    电池放电阈值
    ...    AND    设置web参数量    电池放电    主要
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0028_电池测试失败
    [Setup]    Run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    #2021/5/6加
    连接交流源
    连接电池模拟器
    重置电池模拟器输出
    #
    电池管理初始化
    #获取告警级别和输出干接点设置
    ${电池测试失败级别}    获取web参数量    电池测试失败
    Comment    ${电池测试失败干接点}    获取web参数量    电池测试失败干接点
    run keyword if    '${电池测试失败级别}'=='屏蔽'    设置web参数量    电池测试失败    严重
    Comment    run keyword if    '${电池测试失败干接点}'=='无干接点'    设置web参数量    电池测试失败干接点    A1
    sleep    3
    ${电池测试失败级别}    获取web参数量    电池测试失败
    Comment    ${电池测试失败干接点}    获取web参数量    电池测试失败干接点
    Comment    ${干接点序号}    strip string    ${电池测试失败干接点}    characters=A
    ${缺省值}    获取web参数上下限范围_有单位    测试最长时间
    ${可设置范围}    获取web参数可设置范围    测试最长时间
    ${测试最长时间设置值}    run keyword if    '${缺省值}[3]'== 'Hour'    evaluate    ${可设置范围}[0]+1
    ...    ELSE    evaluate    ${可设置范围}[0]+5
    ${测试最长时间转换后}    run keyword if    '${缺省值}[3]'== 'Hour'    evaluate    ${测试最长时间设置值}*60
    ...    ELSE    set variable    ${测试最长时间设置值}
    设置web参数量    测试最长时间    ${测试最长时间设置值}
    #断开电池，以便电池检测异常
    设置负载电压电流    53.5    15
    打开负载输出
    断开电池模拟器
    连接电池模拟器
    关闭电池模拟器输出
    sleep    3
    #启动电池测试
    Wait Until Keyword Succeeds    2m    1    设置web控制量    启动测试
    run keyword and ignore error    Wait Until Keyword Succeeds    10    1    信号量数据值为    电池管理状态    测试
    wait until keyword succeeds    3m    2    判断告警存在    电池测试失败
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    10    1    设置web参数量    电池测试失败    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池测试失败
        sleep    5
    #统一监控协议查询告警存在
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    电池测试失败-1
        should be true    ${统一监控协议告警结果}
    #查询web端级别
        ${电池下电告警级别}    获取web告警属性    电池测试失败-1    告警级别
        should be equal    ${告警级别}    ${电池下电告警级别}
    END
    #恢复
    关闭负载输出
    仅电池模拟器供电
    同时设置三相电压频率    220    50
    打开交流源输出
    连接CSU
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    交流供电状态    市电1
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    停电
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    0
    设置web参数量    均充使能    允许
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动测试
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    测试
    ${等待时间}    evaluate    ${测试最长时间转换后}+2
    Wait Until Keyword Succeeds    ${等待时间}m    1    信号量数据值为    电池管理状态    均充
    wait until keyword succeeds    3m    2    判断告警不存在    电池测试失败
    Wait Until Keyword Succeeds    2m    1    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    #无告警后查询判断
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    电池测试失败-1
    should not be true    ${统一监控协议告警结果}
    [Teardown]    Run keywords    设置web设备参数量为默认值    测试最长时间
    ...    AND    设置web参数量    电池测试失败    主要
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出
    ...    AND    重置电池模拟器输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0030_电池温度低√
    [Setup]    Run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    同时设置三相电压频率    220    50
    打开交流源输出
    连接CSU
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    电池温度低
    #获取告警级别和输出干接点设置值
    ${级别设置值}    获取web参数量    电池温度低
    ${干接点设置值}    获取web参数量    电池温度低干接点
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池温度低    严重
    run keyword if    '${干接点设置值}'=='无干接点'    设置web参数量    电池温度低干接点    A1
    sleep    3
    ${级别设置值}    获取web参数量    电池温度低
    ${干接点设置值}    获取web参数量    电池温度低干接点
    ${干接点序号}    strip string    ${干接点设置值}    characters=A
    #（1）告警产生
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    -10    0.001    电池_1    电池温度
    #产生电池温度低告警
    ${电池温度低阈值范围}    获取web参数上下限范围    电池温度低阈值
    ${电池温度低阈值可设置范围}    获取web参数可设置范围    电池温度低阈值
    设置web参数量    电池温度低阈值    ${电池温度低阈值可设置范围}[1]
    ${电池温度}    获取web实时数据    电池温度-1
    Wait Until Keyword Succeeds    10    1    判断告警存在    电池温度低
    sleep    3
    #只是通过调节温度零点来改变温度，如果室温较高时，无法达到告警点，视为正常
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    电池温度低    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池温度低
        sleep    3
    #统一监控协议查询告警存在
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    电池温度过低-1
        should be true    ${统一监控协议告警结果}
    #查询web端级别
        ${告警级别}    获取web告警属性    电池温度低-1    告警级别
        should be equal    ${告警级别设置}    ${告警级别}
    END
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]     0    1    电池_1    电池温度
    #（2）告警恢复
    设置web参数量    电池温度低阈值    ${电池温度低阈值范围}[0]
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池温度低
    #无告警后查询判断
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    电池温度过低-1
    should not be true    ${统一监控协议告警结果}
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[1]     0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池温度低阈值
    ...    AND    设置web参数量    电池温度低    主要
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0032_电池温度高√
    [Setup]    Run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    同时设置三相电压频率    220    50
    打开交流源输出
    连接CSU
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    电池温度高
    #获取告警级别和输出干接点设置值
    ${级别设置值}    获取web参数量    电池温度高
    ${干接点设置值}    获取web参数量    电池温度高干接点
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池温度高    严重
    run keyword if    '${干接点设置值}'=='无干接点'    设置web参数量    电池温度高干接点    A1
    sleep    3
    ${级别设置值}    获取web参数量    电池温度高
    ${干接点设置值}    获取web参数量    电池温度高干接点
    ${干接点序号}    strip string    ${干接点设置值}    characters=A
    #（1）告警产生
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]     10    1.6    电池_1    电池温度
    #产生电池温度高告警
    ${电池温度高阈值范围}    获取web参数上下限范围    电池温度高阈值
    ${电池温度高阈值可设置范围}    获取web参数可设置范围    电池温度高阈值
    设置web参数量    电池温度高阈值    ${电池温度高阈值可设置范围}[0]
    ${电池温度}    获取web实时数据    电池温度-1
    Wait Until Keyword Succeeds    10    1    判断告警存在    电池温度高
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    电池温度高    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池温度高
        sleep    3
    #统一监控协议查询告警存在
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    电池温度过高-1
        should be true    ${统一监控协议告警结果}
    #查询web端级别
        ${告警级别}    获取web告警属性    电池温度高-1    告警级别
        should be equal    ${告警级别设置}    ${告警级别}
    END
    #（2）告警恢复
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]     0    1    电池_1    电池温度
    设置web参数量    电池温度高阈值    ${电池温度高阈值范围}[0]
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池温度高
    #无告警后查询判断
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    电池温度过高-1
    should not be true    ${统一监控协议告警结果}
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[1]     0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池温度高阈值
    ...    AND    设置web参数量    电池温度高    主要
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0034_电池温度无效√
    [Setup]    Run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    #电池2不接入传感器
    设置通道无效值/恢复通道原始值     ${plat.batttemps}[0]     1
    设置通道无效值/恢复通道原始值     ${plat.batttemps}[1]     1
    设置通道无效值/恢复通道原始值     ${plat.batttemps}[2]     1
    实时告警刷新完成
    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池组容量_2    100
    设置web参数量    电池温度无效    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    电池温度无效
    ${电池温度无效告警}    判断告警存在_带返回值    电池温度无效
    should not be true    ${电池温度无效告警}
    设置通道配置    ${plat.batttemps}[1]    0    1    电池_2    电池温度
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    10    1    设置web参数量    电池温度无效    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池温度无效-2
        sleep    5
    #统一监控协议查询告警存在
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    电池温度无效-2
        should be true    ${统一监控协议告警结果}
    #查询web端级别
        ${告警级别}    获取web告警属性    电池温度无效-2    告警级别
        should be equal    ${告警级别}    ${告警级别}
    END
    设置通道无效值/恢复通道原始值     ${plat.batttemps}[0]     0
    设置通道无效值/恢复通道原始值     ${plat.batttemps}[1]     0
    设置通道无效值/恢复通道原始值     ${plat.batttemps}[2]     0
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置web参数量    电池组容量_2    0
    ...    AND    设置web参数量    电池温度无效    主要
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接
    ...    AND    设置通道无效值/恢复通道原始值     ${plat.batttemps}[0]     0
    ...    AND    设置通道无效值/恢复通道原始值     ${plat.batttemps}[1]     0
    ...    AND    设置通道无效值/恢复通道原始值     ${plat.batttemps}[2]     0

uniform_0036_电池回路断
    连接CSU
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
        设置web参数量    电池组容量_${i}    0
    END
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    99
    sleep    2
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    100
    #获取告警级别和输出干接点设置
    ${电池回路断级别}    获取web参数量    电池回路断
    run keyword if    '${电池回路断级别}'=='屏蔽'    设置web参数量    电池回路断    严重
    Wait Until Keyword Succeeds    3m    1    判断告警不存在    电池回路断
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_3    100
    wait until keyword succeeds    5m    2    查询指定告警信息    电池回路断
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    电池回路断    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池回路断
        sleep    5
        ${获取告警级别}    获取web告警属性    电池回路断-3    告警级别
        should be equal    ${告警级别}    ${获取告警级别}
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    电池回路断-3
        should be true    ${统一监控协议告警结果}
    END
    #恢复
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_3    0
    wait until keyword succeeds    3m    2    判断告警不存在    电池回路断
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    电池回路断-3
    should not be true    ${统一监控协议告警结果}
    [Teardown]    run keywords    设置web设备参数量为默认值    电池回路断
    ...    AND    设置web参数量    电池组容量_3    0
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0038_电池丢失√
    [Setup]    Run keywords    测试用例前置条件
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_创建加密链路连接
    连接CSU
    #获取参数/默认值
    ${干接点上下限}    获取web参数量    电池丢失干接点
    ${告警干接点默认值}    run keyword if    '${干接点上下限}]'=='0'    set variable    无干接点
    ...    ELSE    set variable    A1
    #获取告警级别和输出干接点设置值
    ${级别设置值}    获取web参数量    电池丢失
    ${干接点设置值}    获取web参数量    电池丢失干接点
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池丢失    严重
    ${DI2通道配置}    获取通道配置    ${plat.Inrelay2Status}
    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay2Status}    电池_1    电池丢失    断开
    Comment    Wait Until Keyword Succeeds    5m    1    判断告警不存在    电池丢失
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    电池丢失    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池丢失
        sleep    3
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    电池丢失-1
        should be true    ${统一监控协议告警结果}
        ${告警级别}    获取web告警属性    电池丢失-1    告警级别
        should be equal    '${告警级别设置}'    '${告警级别}'
    END
    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay2Status}    ${DI2通道配置}[1]    ${DI2通道配置}[2]    ${DI2通道配置}[3]
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池丢失
    #无告警后查询判断
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    电池丢失-1
    should not be true    ${统一监控协议告警结果}
    [Teardown]    Run keywords    设置web参数量    电池丢失    主要
    ...    AND    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay2Status}    ${DI2通道配置}[1]    ${DI2通道配置}[2]    ${DI2通道配置}[3]
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0012_一次下电分路断
    [Documentation]    现有硬件环境只支持四组分路断


    
    实时告警刷新完成
    ${级别设置值}    获取web参数量    一次下电分路断_1
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    一次下电分路断_1    主要
    wait until keyword succeeds    1m    1    判断告警不存在    一次下电分路断
    模拟数字量告警    下电告警    ON
    FOR    ${分路序号}    IN RANGE    4    1    -1
        wait until keyword succeeds    1m    1    判断告警存在    一次下电分路断_${分路序号}
        sleep    5
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    一次下电分路断_${分路序号}
        should be true    ${统一监控协议告警结果}
    END
    模拟数字量告警    下电告警    OFF
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    一次下电分路断
    FOR    ${分路序号}    IN RANGE    4    1    -1
        wait until keyword succeeds    1m    1    判断告警不存在    一次下电分路断_${分路序号}
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    一次下电分路断_${分路序号}
        should not be true    ${统一监控协议告警结果}
    END
    [Teardown]    run keywords    设置web设备参数量为默认值    一次下电分路断_1
    ...    AND    模拟数字量告警    下电告警    OFF
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0012_二次下电分路断
    [Documentation]    现有硬件环境只支持四组分路断
    实时告警刷新完成
    ${级别设置值}    获取web参数量    二次下电分路断_1
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    二次下电分路断_1    主要
    wait until keyword succeeds    1m    1    判断告警不存在    二次下电分路断
    模拟数字量告警    下电告警    ON
    FOR    ${分路序号}    IN RANGE    4    1    -1
        wait until keyword succeeds    1m    1    判断告警存在    二次下电分路断_${分路序号}
        sleep    5
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    二次下电分路断_${分路序号}
        should be true    ${统一监控协议告警结果}
    END
    模拟数字量告警    下电告警    OFF
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    二次下电分路断
    FOR    ${分路序号}    IN RANGE    4    1    -1
        wait until keyword succeeds    1m    1    判断告警不存在    二次下电分路断_${分路序号}
        sleep    5
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    二次下电分路断_${分路序号}
        should not be true    ${统一监控协议告警结果}
    END
    [Teardown]    run keywords    设置web设备参数量为默认值    二次下电分路断_1
    ...    AND    模拟数字量告警    下电告警    OFF
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0012_一次下电扩展分路断
    [Documentation]    现有硬件环境只支持四组分路断
    实时告警刷新完成
    ${级别设置值}    获取web参数量    一次下电扩展分路断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    一次下电扩展分路断    主要
    wait until keyword succeeds    1m    1    判断告警不存在    一次下电扩展分路断
    设置web参数量    一次下电扩展分路断    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    一次下电扩展分路断
    ${告警}    判断告警存在_带返回值    一次下电扩展分路断
    should not be true    ${告警}
    模拟数字量告警    下电告警    ON
    FOR    ${告警级别设置}    IN    主要    次要    警告    严重
        Wait Until Keyword Succeeds    30    1    设置web参数量    一次下电扩展分路断    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    一次下电扩展分路断
        sleep    5
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    一次下电扩展分路状态
        should be true    ${统一监控协议告警结果}
        ${sm告警级别}    获取web告警属性    一次下电扩展分路断    告警级别
        should be equal    ${告警级别设置}    ${sm告警级别}
    END
    模拟数字量告警    下电告警    OFF
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    一次下电扩展分路断
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    一次下电扩展分路状态
    should not be true    ${统一监控协议告警结果}
    [Teardown]    run keywords    设置web设备参数量为默认值    一次下电扩展分路断
    ...    AND    模拟数字量告警    下电告警    OFF
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0012_二次下电扩展分路断
    [Documentation]    现有硬件环境只支持四组分路断
    实时告警刷新完成
    ${级别设置值}    获取web参数量    二次下电扩展分路断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    二次下电扩展分路断    主要
    wait until keyword succeeds    1m    1    判断告警不存在    二次下电扩展分路断
    设置web参数量    二次下电扩展分路断    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    二次下电扩展分路断
    ${告警}    判断告警存在_带返回值    二次下电扩展分路断
    should not be true    ${告警}
    模拟数字量告警    下电告警    ON
    FOR    ${告警级别设置}    IN    主要    次要    警告    严重
        Wait Until Keyword Succeeds    30    1    设置web参数量    二次下电扩展分路断    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    二次下电扩展分路断
        sleep    5
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    二次下电扩展分路状态
        should be true    ${统一监控协议告警结果}
        ${sm告警级别}    获取web告警属性    二次下电扩展分路断    告警级别
        should be equal    ${告警级别设置}    ${sm告警级别}
    END
    模拟数字量告警    下电告警    OFF
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    二次下电扩展分路断
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    二次下电扩展分路状态
    should not be true    ${统一监控协议告警结果}
    [Teardown]    run keywords    设置web设备参数量为默认值    二次下电扩展分路断
    ...    AND    模拟数字量告警    下电告警    OFF
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

uniform_0012_电池下电分路断
    [Documentation]    现有硬件环境只支持四组分路断


    连接CSU
    ${原始用户参数文件}    导出参数文件
    压缩文件夹    ${原始用户参数文件}
    sleep    5
    ${用户参数文件}    导出参数文件
    &{dict1}    create dictionary    id=pdt.power_subrack    para_name=F01 Det Channel App Conf    para_val=1
    &{dict2}    create dictionary    id=pdt.power_subrack    para_name=F02 Det Channel App Conf    para_val=2
    &{dict3}    create dictionary    id=pdt.power_subrack    para_name=F03 Det Channel App Conf    para_val=3
    &{dict4}    create dictionary    id=pdt.power_subrack    para_name=F04 Det Channel App Conf    para_val=4
    &{dict5}    create dictionary    id=pdt.power_subrack    para_name=F05 Det Channel App Conf    para_val=9
    &{dict6}    create dictionary    id=pdt.power_subrack    para_name=F06 Det Channel App Conf    para_val=10
    &{dict7}    create dictionary    id=pdt.power_subrack    para_name=F07 Det Channel App Conf    para_val=11
    &{dict8}    create dictionary    id=pdt.power_subrack    para_name=F08 Det Channel App Conf    para_val=12
    &{dict9}    create dictionary    id=pdt.power_subrack    para_name=F09 Det Channel App Conf    para_val=1
    &{dict10}    create dictionary    id=pdt.power_subrack    para_name=F10 Det Channel App Conf    para_val=1
    &{dict11}    create dictionary    id=pdt.power_subrack    para_name=F11 Det Channel App Conf    para_val=2
    &{dict12}    create dictionary    id=pdt.power_subrack    para_name=F12 Det Channel App Conf    para_val=3
    @{配置列表}    create list    ${dict1}    ${dict2}    ${dict3}    ${dict4}    ${dict5}    ${dict6}    ${dict7}    ${dict8}    ${dict9}    ${dict10}    ${dict11}    ${dict12}
    修改导出配置文件    ${配置列表}    ${用户参数文件}/config.xml
    压缩文件夹    ${用户参数文件}
    ${导入结果}    导入参数文件    ${用户参数文件}.zip
    sleep    4m
    连接CSU


    实时告警刷新完成
    ${级别设置值}    获取web参数量    电池下电分路断_1
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池下电分路断_1    主要
    wait until keyword succeeds    1m    1    判断告警不存在    电池下电分路断
    模拟数字量告警    下电告警    ON
    FOR    ${分路序号}    IN RANGE    4    1    -1
        wait until keyword succeeds    1m    1    判断告警存在    电池下电分路断_${分路序号}
        sleep    5
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    电池下电分路断_${分路序号}
        should be true    ${统一监控协议告警结果}
    END
    模拟数字量告警    下电告警    OFF
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池下电分路断
    FOR    ${分路序号}    IN RANGE    4    1    -1
        wait until keyword succeeds    1m    1    判断告警不存在    电池下电分路断_${分路序号}
        sleep    5
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    电池下电分路断_${分路序号}
        should not be true    ${统一监控协议告警结果}
    END
    [Teardown]    run keywords    设置web设备参数量为默认值    电池下电分路断_1
    ...    AND    模拟数字量告警    下电告警    OFF
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

    ...    AND    导入参数文件    ${原始用户参数文件}.zip
    ...    AND    sleep    4m
    ...    AND    系统复位
    ...    AND    sleep    30
    ...    AND    连接CSU
    
uniform_0012_电池下电扩展分路断
    [Documentation]    现有硬件环境只支持四组分路断


    连接CSU
    ${原始用户参数文件}    导出参数文件
    压缩文件夹    ${原始用户参数文件}
    sleep    5
    ${用户参数文件}    导出参数文件
    &{dict1}    create dictionary    id=pdt.power_subrack    para_name=F01 Det Channel App Conf    para_val=1
    &{dict2}    create dictionary    id=pdt.power_subrack    para_name=F02 Det Channel App Conf    para_val=2
    &{dict3}    create dictionary    id=pdt.power_subrack    para_name=F03 Det Channel App Conf    para_val=3
    &{dict4}    create dictionary    id=pdt.power_subrack    para_name=F04 Det Channel App Conf    para_val=4
    &{dict5}    create dictionary    id=pdt.power_subrack    para_name=F05 Det Channel App Conf    para_val=9
    &{dict6}    create dictionary    id=pdt.power_subrack    para_name=F06 Det Channel App Conf    para_val=10
    &{dict7}    create dictionary    id=pdt.power_subrack    para_name=F07 Det Channel App Conf    para_val=11
    &{dict8}    create dictionary    id=pdt.power_subrack    para_name=F08 Det Channel App Conf    para_val=12
    &{dict9}    create dictionary    id=pdt.power_subrack    para_name=F09 Det Channel App Conf    para_val=1
    &{dict10}    create dictionary    id=pdt.power_subrack    para_name=F10 Det Channel App Conf    para_val=1
    &{dict11}    create dictionary    id=pdt.power_subrack    para_name=F11 Det Channel App Conf    para_val=2
    &{dict12}    create dictionary    id=pdt.power_subrack    para_name=F12 Det Channel App Conf    para_val=3
    @{配置列表}    create list    ${dict1}    ${dict2}    ${dict3}    ${dict4}    ${dict5}    ${dict6}    ${dict7}    ${dict8}    ${dict9}    ${dict10}    ${dict11}    ${dict12}
    修改导出配置文件    ${配置列表}    ${用户参数文件}/config.xml
    压缩文件夹    ${用户参数文件}
    ${导入结果}    导入参数文件    ${用户参数文件}.zip
    sleep    4m
    连接CSU



    实时告警刷新完成
    ${级别设置值}    获取web参数量    电池下电扩展分路断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池下电扩展分路断    主要
    wait until keyword succeeds    1m    1    判断告警不存在    电池下电扩展分路断
    设置web参数量    电池下电扩展分路断    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    电池下电扩展分路断
    ${告警}    判断告警存在_带返回值    电池下电扩展分路断
    should not be true    ${告警}
    模拟数字量告警    下电告警    ON
    FOR    ${告警级别设置}    IN    主要    次要    警告    严重
        Wait Until Keyword Succeeds    30    1    设置web参数量    电池下电扩展分路断    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池下电扩展分路断
        sleep    5
        ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    电池下电扩展分路状态
        should be true    ${统一监控协议告警结果}
        ${sm告警级别}    获取web告警属性    电池下电扩展分路断    告警级别
        should be equal    ${告警级别设置}    ${sm告警级别}
    END
    模拟数字量告警    下电告警    OFF
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池下电扩展分路断
    ${统一监控协议告警结果}    获取统一监控协议一条告警值并判断告警存在    直流告警量    电池下电扩展分路状态
    should not be true    ${统一监控协议告警结果}
    [Teardown]    run keywords    设置web设备参数量为默认值    电池下电扩展分路断
    ...    AND    模拟数字量告警    下电告警    OFF
    ...    AND    Run Keyword if    '${SSH}'=='True'    统一监控协议_断开加密链路连接

    ...    AND    导入参数文件    ${原始用户参数文件}.zip
    ...    AND    sleep    4m
    ...    AND    系统复位
    ...    AND    sleep    30
    ...    AND    连接CSU