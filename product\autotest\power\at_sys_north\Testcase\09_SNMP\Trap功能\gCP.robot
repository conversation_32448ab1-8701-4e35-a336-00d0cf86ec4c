*** Settings ***
Suite Setup       油机通讯测试前置条件
Suite Teardown    油机通讯测试结束条件
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
snmp_0002_油机控制屏通讯中断1
    连接CSU
    ${级别设置值}    获取web参数量    油机控制屏通讯中断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机控制屏通讯中断    主要
    #判断无告警
    ${告警不存在}    判断告警存在_带返回值    油机控制屏通讯中断
    should not be true    ${告警不存在}    #告警不存在返回FALSE
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    控制子工具运行停止    oileng    关闭
    wait until keyword succeeds    6m    2    查询指定告警信息    油机控制屏通讯中断
    #snmp产生
    ${snmp英文名}    获取snmp单个告警英文名    油机控制屏通讯中断_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    控制子工具运行停止    oileng    开启
    wait until keyword succeeds    6m    2    查询指定告警信息不为    油机控制屏通讯中断
    ${告警消失}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警消失}
    [Teardown]    控制子工具运行停止    oileng    开启

snmp_0004_市电分闸失败3
    连接CSU
    ${级别设置值}    获取web参数量    市电分闸失败
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    市电分闸失败    主要
    #判断无告警
    ${告警不存在}    判断告警存在_带返回值    市电分闸失败
    should not be true    ${告警不存在}    #告警不存在返回FALSE
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    设置子工具值    oileng    all    告警    预报警代码    30
    设置子工具值    oileng    all    告警    报警代码    30
    sleep    1m
    wait until keyword succeeds    5m    2    查询指定告警信息    市电分闸失败
    #snmp产生
    ${snmp英文名}    获取snmp单个告警英文名    市电分闸失败_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    设置子工具值    oileng    all    告警    预报警代码    0
    设置子工具值    oileng    all    告警    报警代码    0
    sleep    1m
    wait until keyword succeeds    5m    2    查询指定告警信息不为    市电分闸失败
    ${告警消失}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警消失}
    [Teardown]    run keywords    设置子工具值    oileng    all    告警    预报警代码    0
    ...    AND    设置子工具值    oileng    all    告警    报警代码    0

snmp_0006_市电合闸失败4
    连接CSU
    ${级别设置值}    获取web参数量    市电合闸失败
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    市电合闸失败    主要
    #判断无告警
    ${告警不存在}    判断告警存在_带返回值    市电合闸失败
    should not be true    ${告警不存在}    #告警不存在返回FALSE
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    设置子工具值    oileng    all    告警    备用9    1
    wait until keyword succeeds    5m    2    查询指定告警信息    市电合闸失败
    #snmp产生
    ${snmp英文名}    获取snmp单个告警英文名    市电合闸失败_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    设置子工具值    oileng    all    告警    备用9    0
    sleep    1m
    wait until keyword succeeds    5m    2    查询指定告警信息不为    市电合闸失败
    ${告警消失}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警消失}
    [Teardown]    设置子工具值    oileng    all    告警    备用9    0

snmp_0008_油机合闸失败1
    连接CSU
    ${级别设置值}    获取web参数量    油机合闸失败
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机合闸失败    主要
    #判断无告警
    ${告警不存在}    判断告警存在_带返回值    油机合闸失败
    should not be true    ${告警不存在}    #告警不存在返回FALSE
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    设置子工具值    oileng    all    告警    备用9    2
    sleep    1m
    wait until keyword succeeds    5m    2    查询指定告警信息    油机合闸失败
    #snmp产生
    ${snmp英文名}    获取snmp单个告警英文名    油机合闸失败_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    设置子工具值    oileng    all    告警    备用9    0
    sleep    1m
    wait until keyword succeeds    5m    2    查询指定告警信息不为    油机合闸失败
    ${告警消失}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警消失}
    连接CSU
    设置子工具值    oileng    all    告警    备用9    2
    sleep    20s
    ${级别设置值}    获取web参数量    油机合闸失败
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机合闸失败    主要
    wait until keyword succeeds    5m    5    查询指定告警信息    油机合闸失败
    设置子工具值    oileng    all    告警    备用9    0
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机合闸失败
    [Teardown]    设置子工具值    oileng    all    告警    备用9    0

snmp_00010_油机分闸失败2
    连接CSU
    ${级别设置值}    获取web参数量    油机分闸失败
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机分闸失败    主要
    #判断无告警
    ${告警不存在}    判断告警存在_带返回值    油机分闸失败
    should not be true    ${告警不存在}    #告警不存在返回FALSE
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    设置子工具值    oileng    all    告警    预报警代码    28
    设置子工具值    oileng    all    告警    报警代码    28
    sleep    1m
    wait until keyword succeeds    5m    2    查询指定告警信息    油机分闸失败
    #snmp产生
    ${snmp英文名}    获取snmp单个告警英文名    油机分闸失败_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    设置子工具值    oileng    all    告警    预报警代码    0
    设置子工具值    oileng    all    告警    报警代码    0
    sleep    1m
    wait until keyword succeeds    5m    2    查询指定告警信息不为    油机分闸失败
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    run keywords    设置子工具值    oileng    all    告警    预报警代码    0
    ...    AND    设置子工具值    oileng    all    告警    报警代码    0
