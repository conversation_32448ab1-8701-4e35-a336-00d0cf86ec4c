#!/usr/bin/env python2
# -*- coding: utf-8 -*-
#####################################################################################
# File Name:    WriteTestReport.py
# Discription:  使用docx库生成常用的测试报告
# Author:   Pengy；
# DevEnv:   winXP/7x86+python2.7
# Notes:               
# Log:      20180915Pengy 
################################################################################
##import docx
import os,datetime
import time
import sys,re
##import shutil#复制文件用
##from docx.shared import Inches


class WriteTestReport: #数据保存类
    def __init__(self):
        os.chdir(os.path.split(os.path.realpath(__file__))[0])#回到当前脚本目录
##        plt=matplotlib.pyplot()                              

#保存运行日志=====================================
    def SaveLog(self,strattime,stoptime,runtime):
        """
        入参为列表，包括以下内容：开始运行时间,停止运行时间,本次运行时长
        """
        str1=''
        timelist=[]
        runtimelist=[]
        Totaltime=0
        runtimestr=self.itv2time(runtime)
        
        os.chdir(os.path.split(os.path.realpath(__file__))[0])#回到当前脚本目录
        logname = os.getcwd().split('\\')[-3]#获取文件夹名称用于表示测试项目
##        print logname

        # 写之前，先检验文件是否存在
        if os.path.exists("C:\\Python27\\RFRunLog.csv"):
            try:
                f=open('C:\\Python27\\RFRunLog.csv','r')
                csvlist = []
                csvlist = f.readlines()
                f.close()
            except:
                print u'打开文件出错！'
                f.close()
            for i in csvlist[1:]:
                timelist=i.split(',')
                Totaltime+=self.time2itv(timelist[4].strip('\n'))
            Totaltime=Totaltime+int(runtime)
            Totaltime=self.itv2time(Totaltime)
            print u'总运行时长：',Totaltime

            str1 =logname+','+str(strattime)+','+str(stoptime)+','+str(runtimestr)+','+str(Totaltime)

            #保存文档
            try:
                savef=open('C:\\Python27\\RFRunLog.csv','a')
                print>>savef,time.strftime("%Y-%m-%d %H:%M:%S")+','+str1.encode('gbk')
                savef.close()
            except:
                print u'保存文件出错1！'
##                savef.close()   
        else:
            print u'新建RFRunLog.csv日志！'
            str0=u'日志时间,测试项目,开始运行时间,停止运行时间,本次运行时长,总运行时长'
            str1 =logname+','+str(strattime)+','+str(stoptime)+','+str(runtimestr)+','+str(runtimestr)
            try:
                savef=open('C:\\Python27\\RFRunLog.csv','a')
                print>>savef,str0.encode('gbk')
                print>>savef,time.strftime("%Y-%m-%d %H:%M:%S")+','+str1.encode('gbk')
                savef.close()
            except:
                print u'保存文件出错2！'
##                savef.close()

    # 将计时器"时:分:秒"字符串转换为秒数间隔
    def time2itv(self,sTime):
        p="^([0-9]+):([0-5][0-9]):([0-5][0-9])$"
        cp=re.compile(p)
        try:
            mTime=cp.match(sTime)
        except TypeError:
            return "[InModuleError]:time2itv(sTime) invalid argument type"
     
        if mTime:
            t=map(int,mTime.group(1,2,3))
            return 3600*t[0]+60*t[1]+t[2]
        else:
            return "[InModuleError]:time2itv(sTime) invalid argument value"
     
     
    # 将秒数间隔转换为计时器"时:分:秒"字符串
    def itv2time(self,iItv):
        iItv=int(iItv)
        if type(iItv)==type(1):
            m, s = divmod(iItv, 60)
            h, m = divmod(m, 60)
            Totaltimestr="%d:%02d:%02d" % (h, m, s)#转换为时长
##            print Totaltimestr
            return Totaltimestr
        else:
            return "[InModuleError]:itv2time(iItv) invalid argument type"


if __name__=="__main__":
    ds=WriteTestReport()
    ds.SaveLog('2006-03-29 15:06:21','2006-03-29 15:16:21',11.0)
##    ds.WriteHead(u'测试记录',1)
##    ds.WriteTestTable()
##    ds.FindNewFile()
##    ds.WriteTestResult()
##    ds.CreateWord()
##    ds.WriteParagraph(u'你说的啥子哦！！')

