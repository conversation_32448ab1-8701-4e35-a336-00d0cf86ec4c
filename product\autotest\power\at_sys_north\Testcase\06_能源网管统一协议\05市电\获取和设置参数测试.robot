*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_获取市电模拟量
    [Tags]    notest
    ${能源网管数据}    能源网管协议_获取数据    获取市电模拟量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取市电模拟量
    Should Be True    ${数据对比结果}
    ${校验结果}    能源网管协议_校验数据上送完整性    ${能源网管数据}    市电    analog data
    Comment   Should Be True    ${校验结果}

power_sm_0002_获取市电数字量
    ${能源网管数据}    能源网管协议_获取数据    获取市电数字量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取市电数字量
    Should Be True    ${数据对比结果}
    ${校验结果}    能源网管协议_校验数据上送完整性    ${能源网管数据}    市电    digital data
    Comment      Should Be True    ${校验结果}

power_sm_0003_获取市电统计量
    ${能源网管数据}    能源网管协议_获取数据    获取市电统计量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取市电统计量
    Should Be True    ${数据对比结果}
    ${校验结果}    能源网管协议_校验数据上送完整性    ${能源网管数据}    市电    stastic data
    Comment      Should Be True    ${校验结果}
