
#-*-coding:utf-8 -*-
#####################################################################################
# File Name:    PM3000_GPIB.py
# Discription:  实现与PM3000的连接和数据读写
# Author:   Pengy；
# DevEnv:   winXP/7x86+python2.7
# Notes:    使用GPIB与PM3000连接，读取和控制PM3000
#           建议使用19200bps的波特率，以提高通讯速率 
# Log:      20180110Pengy 
################################################################################
import sys,os
import time

class PM3000_GPIB():
    def __init__(self):
        os.chdir(os.path.split(os.path.realpath(__file__))[0])#回到当前脚本目录

    #############定位到TestRecord文件夹#######################        
    def Rename_AllTestCase(self,path,index='power_sm'):
        for root,dirs,files in os.walk(path):
##            for dir in dirs: #列出目录下所有目录名称
##                print os.path.join(root,dir).decode('gbk')
            for file in files: #列出目录下所有文件名称
                #filePath = os.path.join(root,file).decode('gbk')
                filePath = os.path.join(root,file)
##                print filePath
                if filePath.split('\\')[-1]!='__init__.robot' and ('.robot' in filePath.split('\\')[-1]):
##                    print filePath.split('\\')[-1]
                  self.Rename_OneTestCase(filePath,index)

    def Rename_OneTestCase(self, filepath,index='power_sm'):
        outStr=''
        # f = open( filepath, 'r')
        csvlist = []
        # csvlist = f.readlines()
        TestCaseNameLst=[]
        NewTestCaseNameLst=[]
        # f.close()

        try:
            f = open( filepath, 'r')
            csvlist = f.readlines()
            f.close()
        except:
            f = open(filepath,'r',encoding = 'utf-8')
            csvlist = f.readlines()
            f.close()


        cnt=0
        
        for i in range(len(csvlist)):
            if (len(csvlist[i])>1
                and csvlist[i].strip('\n')[0]!=' '
                and csvlist[i].strip('\n')[0]!='*'
                and csvlist[i].strip('\n')[0:8]!='Resource'
                and csvlist[i].strip('\n')[0:7]!='Library'
                and csvlist[i].strip('\n')[0:11]!='Suite Setup'
                and csvlist[i].strip('\n')[0:10]!='Force Tags'
                ):
                cnt+=1
##                print type('%04d' % cnt)
                csvlist[i]=index+'_'+('%04d' % cnt)+'_'+csvlist[i]
##                print csvlist[i]

        outStr=''.join(csvlist)
##        print outStr
        try:
            savef=open(filepath,'w+',encoding = 'utf-8')
            savef.read()
##            print outStr
            savef.write(outStr)
            savef.close()
        except:
            print (u'打开源效应测试文件出错！')
        print (u'转换测试用例名称完成....')

#======================================================================================================    
if __name__ == "__main__":
    myapp = PM3000_GPIB()
##    mya￥pp.Rename_TestCase(u'1363测试用例_网口.txt')
    myapp.Rename_AllTestCase('.')

