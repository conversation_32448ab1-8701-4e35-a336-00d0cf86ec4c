*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
批量获取数字量测试
    写入CSV文档    智能空开数字量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=digital data    INDEX_DEVICE_CH=智能空开
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SSW排除数字量信号}    ${排除列表}    1
    ${缺省值列表}   获取缺省值列表  ${列表1}    1
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    DMU_IntelAirSwit          呼叫     ${缺省值列表}    1    智能空开数字量获取测试
    ${缺省值列表}   获取缺省值列表  ${列表1}    2
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    DMU_IntelAirSwit          呼叫     ${缺省值列表}    1    智能空开数字量获取测试
    ${缺省值列表}   获取缺省值列表  ${列表1}    0
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    DMU_IntelAirSwit          呼叫     ${缺省值列表}    1    智能空开数字量获取测试

智能空开在位状态测试
    连接CSU
    显示属性配置    智能空开在位状态    数字量    web_attr=On    gui_attr=On
    ${名称1}    获取web实时数据    智能空开在位状态-1
    ${名称2}    获取web实时数据    智能空开在位状态-2
    ${名称3}    获取web实时数据    智能空开在位状态-3
    should be equal    '${名称1}'    '在位'
    should be equal    '${名称2}'    '在位'
    should be equal    '${名称3}'    '在位'
    设置子工具个数    DMU_IntelAirSwit    37
    sleep    4m
    ${名称1}    获取web实时数据    智能空开在位状态-38
    ${名称2}    获取web实时数据    智能空开在位状态-39
    ${名称3}    获取web实时数据    智能空开在位状态-40
    should be equal    '${名称1}'    '在位'
    should be equal    '${名称2}'    '在位'
    should be equal    '${名称3}'    '在位'
    控制子工具运行停止    DMU_IntelAirSwit    关闭
    sleep    4m
    显示属性配置    智能空开在位状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    控制子工具运行停止    DMU_IntelAirSwit    开启

智能空开通讯状态测试
    连接CSU
    显示属性配置    智能空开通讯状态    数字量    web_attr=On    gui_attr=On
    设置子工具个数    DMU_IntelAirSwit    3
    控制子工具运行停止    DMU_IntelAirSwit    启动
    sleep    3m
    ${名称1}    获取web实时数据    智能空开通讯状态-1
    ${名称2}    获取web实时数据    智能空开通讯状态-2
    ${名称3}    获取web实时数据    智能空开通讯状态-3
    should be equal    '${名称1}'    '正常'
    should be equal    '${名称2}'    '正常'
    should be equal    '${名称3}'    '正常'
    设置子工具个数    DMU_IntelAirSwit    2
    sleep    4m
    ${名称1}    获取web实时数据    智能空开通讯状态-1
    ${名称2}    获取web实时数据    智能空开通讯状态-2
    ${名称3}    获取web实时数据    智能空开通讯状态-3
    should be equal    '${名称1}'    '正常'
    should be equal    '${名称2}'    '正常'
    should be equal    '${名称3}'    '异常'
    显示属性配置    智能空开通讯状态    数字量    web_attr=Off    gui_attr=Off
    控制子工具运行停止    DMU_IntelAirSwit    开启
    sleep    2m
    [Teardown]    控制子工具运行停止    DMU_IntelAirSwit    开启

智能空开工作状态测试
    连接CSU
    显示属性配置    智能空开工作状态    数字量    web_attr=On    gui_attr=On
    设置子工具个数    DMU_IntelAirSwit    3
    sleep    3m
    ${名称1}    获取web实时数据    智能空开工作状态-1
    ${名称2}    获取web实时数据    智能空开工作状态-2
    ${名称3}    获取web实时数据    智能空开工作状态-3
    should be equal    '${名称1}'    '正常'
    should be equal    '${名称2}'    '正常'
    should be equal    '${名称3}'    '正常'
    设置子工具个数    DMU_IntelAirSwit    2
    sleep    3m
    ${名称1}    获取web实时数据    智能空开工作状态-1
    ${名称2}    获取web实时数据    智能空开工作状态-2
    ${名称3}    获取web实时数据    智能空开工作状态-3
    should be equal    '${名称1}'    '正常'
    should be equal    '${名称2}'    '正常'
    should be equal    '${名称3}'    '通讯断'
    控制子工具运行停止    DMU_IntelAirSwit    关闭
    sleep    3m
    ${名称1}    获取web实时数据    智能空开工作状态-1
    ${名称2}    获取web实时数据    智能空开工作状态-2
    ${名称3}    获取web实时数据    智能空开工作状态-3
    should be equal    '${名称1}'    '通讯断'
    should be equal    '${名称2}'    '通讯断'
    should be equal    '${名称3}'    '通讯断'
    显示属性配置    智能空开工作状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    控制子工具运行停止    DMU_IntelAirSwit    开启
