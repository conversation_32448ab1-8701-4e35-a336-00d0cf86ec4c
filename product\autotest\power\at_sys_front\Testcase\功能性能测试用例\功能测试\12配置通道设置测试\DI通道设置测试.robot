*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
DI1通道设置测试
    连接CSU
    ${干接点设置值}    获取web参数量    电池丢失干接点
    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay1Status}    电池_1    电池丢失    断开
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池丢失     屏蔽
    Wait Until Keyword Succeeds    3m    1    判断告警不存在    电池丢失
    Wait Until Keyword Succeeds    5m    2    设置web参数量    电池丢失    严重
    wait until keyword succeeds    1m    1    查询指定告警信息    电池丢失
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    电池丢失    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池丢失
        sleep    3
        ${告警级别获取}    获取web告警属性    电池丢失-1    告警级别
        should be equal    '${告警级别设置}'    '${告警级别获取}'
    END
    wait until keyword succeeds    30    1    设置web参数量    电池丢失    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    Comment    FOR    ${告警干接点设置}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
    Comment    \    ${干接点设置为无}    evaluate    0
    Comment    \    ${告警干接点获取}    获取web告警属性    扩展柜烟雾告警    告警干接点
    Comment    \    run keyword if    ${告警干接点获取}!=0    wait until keyword succeeds    30    1    设置web参数量    扩展柜烟雾告警干接点    ${干接点设置为无}
    Comment    \    sleep    5
    Comment    \    ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
    Comment    \    should be true    ${干接点实际是否动作状态1}==1
    Comment    \    wait until keyword succeeds    30    1    设置web参数量    扩展柜烟雾告警干接点    ${告警干接点设置}
    Comment    \    sleep    5
    Comment    \    ${告警干接点获取}    获取web告警属性    扩展柜烟雾告警    告警干接点
    Comment    \    should be true    ${告警干接点设置}==${告警干接点获取}
    Comment    \    ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
    Comment    \    should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
    Comment    END
    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay1Status}    电池_1    电池丢失    闭合
    Wait Until Keyword Succeeds    3m    1    判断告警不存在    电池丢失
    [Teardown]    Run keywords    设置web参数量    电池丢失干接点    0
    ...    AND    设置web参数量    电池丢失    次要
    ...    AND    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay1Status}    无    无    闭合

DI3通道设置测试
    连接CSU
    ${干接点设置值}    获取web参数量    扩展柜烟雾告警干接点
    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay3Status}    系统运行环境    扩展柜烟雾告警    断开
    Wait Until Keyword Succeeds    10    2    设置web参数量    扩展柜烟雾告警    屏蔽
    Wait Until Keyword Succeeds    3m    1    判断告警不存在    扩展柜烟雾告警
    Wait Until Keyword Succeeds    5m    2    设置web参数量    扩展柜烟雾告警    严重
    wait until keyword succeeds    1m    1    查询指定告警信息    扩展柜烟雾告警
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    扩展柜烟雾告警    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    扩展柜烟雾告警
        sleep    3
        ${告警级别获取}    获取web告警属性    扩展柜烟雾告警    告警级别
        should be equal    '${告警级别设置}'    '${告警级别获取}'
    END
    wait until keyword succeeds    30    1    设置web参数量    扩展柜烟雾告警    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    Comment    FOR    ${告警干接点设置}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
    Comment    \    ${干接点设置为无}    evaluate    0
    Comment    \    ${告警干接点获取}    获取web告警属性    扩展柜烟雾告警    告警干接点
    Comment    \    run keyword if    ${告警干接点获取}!=0    wait until keyword succeeds    30    1    设置web参数量    扩展柜烟雾告警干接点    ${干接点设置为无}
    Comment    \    sleep    5
    Comment    \    ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
    Comment    \    should be true    ${干接点实际是否动作状态1}==1
    Comment    \    wait until keyword succeeds    30    1    设置web参数量    扩展柜烟雾告警干接点    ${告警干接点设置}
    Comment    \    sleep    5
    Comment    \    ${告警干接点获取}    获取web告警属性    扩展柜烟雾告警    告警干接点
    Comment    \    should be true    ${告警干接点设置}==${告警干接点获取}
    Comment    \    ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
    Comment    \    should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
    Comment    END
    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay3Status}    系统运行环境    扩展柜烟雾告警    闭合
    Wait Until Keyword Succeeds    3m    1    判断告警不存在    扩展柜烟雾告警
    [Teardown]    Run keywords    设置web参数量    扩展柜烟雾告警干接点    0
    ...    AND    设置web参数量    扩展柜烟雾告警    次要
    ...    AND    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay3Status}    系统运行环境    扩展柜烟雾告警    闭合

DI5通道设置测试
    连接CSU
    ${干接点设置值}    获取web参数量    电池丢失干接点
    wait until keyword succeeds    30    1    设置通道配置    ${plat.battmidvolt1}    电池_1    电池丢失    断开
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池丢失     屏蔽
    Wait Until Keyword Succeeds    3m    1    判断告警不存在    电池丢失
    Wait Until Keyword Succeeds    5m    2    设置web参数量    电池丢失    严重
    wait until keyword succeeds    1m    1    查询指定告警信息    电池丢失
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    电池丢失    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池丢失
        sleep    3
        ${告警级别获取}    获取web告警属性    电池丢失-1    告警级别
        should be equal    '${告警级别设置}'    '${告警级别获取}'
    END
    wait until keyword succeeds    30    1    设置web参数量    电池丢失    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    Comment    FOR    ${告警干接点设置}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
    Comment    \    ${干接点设置为无}    evaluate    0
    Comment    \    ${告警干接点获取}    获取web告警属性    扩展柜烟雾告警    告警干接点
    Comment    \    run keyword if    ${告警干接点获取}!=0    wait until keyword succeeds    30    1    设置web参数量    扩展柜烟雾告警干接点    ${干接点设置为无}
    Comment    \    sleep    5
    Comment    \    ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
    Comment    \    should be true    ${干接点实际是否动作状态1}==1
    Comment    \    wait until keyword succeeds    30    1    设置web参数量    扩展柜烟雾告警干接点    ${告警干接点设置}
    Comment    \    sleep    5
    Comment    \    ${告警干接点获取}    获取web告警属性    扩展柜烟雾告警    告警干接点
    Comment    \    should be true    ${告警干接点设置}==${告警干接点获取}
    Comment    \    ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
    Comment    \    should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
    Comment    END
    wait until keyword succeeds    30    1    设置通道配置    ${plat.battmidvolt1}    电池_1    电池丢失    闭合
    Wait Until Keyword Succeeds    3m    1    判断告警不存在    电池丢失
    [Teardown]    Run keywords    设置web参数量    电池丢失干接点    0
    ...    AND    设置web参数量    电池丢失    次要
    ...    AND    wait until keyword succeeds    30    1    设置通道配置    ${plat.battmidvolt1}    无    无    闭合

DI6通道设置测试
    连接CSU
    ${干接点设置值}    获取web参数量    加热器异常干接点
    wait until keyword succeeds    30    1    设置通道配置    ${plat.battmidvolt2}    系统运行环境    加热器异常    断开
    Wait Until Keyword Succeeds    10    2    设置web参数量    加热器异常    屏蔽
    Wait Until Keyword Succeeds    3m    1    判断告警不存在    加热器异常
    Wait Until Keyword Succeeds    5m    2    设置web参数量    加热器异常    严重
    wait until keyword succeeds    1m    1    查询指定告警信息    加热器异常
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    加热器异常    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    加热器异常
        sleep    3
        ${告警级别获取}    获取web告警属性    加热器异常    告警级别
        should be equal    '${告警级别设置}'    '${告警级别获取}'
    END
    wait until keyword succeeds    30    1    设置web参数量    加热器异常    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    Comment    FOR    ${告警干接点设置}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
    Comment    \    ${干接点设置为无}    evaluate    0
    Comment    \    ${告警干接点获取}    获取web告警属性    扩展柜烟雾告警    告警干接点
    Comment    \    run keyword if    ${告警干接点获取}!=0    wait until keyword succeeds    30    1    设置web参数量    扩展柜烟雾告警干接点    ${干接点设置为无}
    Comment    \    sleep    5
    Comment    \    ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
    Comment    \    should be true    ${干接点实际是否动作状态1}==1
    Comment    \    wait until keyword succeeds    30    1    设置web参数量    扩展柜烟雾告警干接点    ${告警干接点设置}
    Comment    \    sleep    5
    Comment    \    ${告警干接点获取}    获取web告警属性    扩展柜烟雾告警    告警干接点
    Comment    \    should be true    ${告警干接点设置}==${告警干接点获取}
    Comment    \    ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
    Comment    \    should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
    Comment    END
    wait until keyword succeeds    30    1    设置通道配置    ${plat.battmidvolt2}    系统运行环境    加热器异常    闭合
    Wait Until Keyword Succeeds    3m    1    判断告警不存在    加热器异常
    [Teardown]    Run keywords    设置web参数量    加热器异常干接点    0
    ...    AND    设置web参数量    加热器异常    次要
    ...    AND    wait until keyword succeeds    30    1    设置通道配置    ${plat.battmidvolt2}    系统运行环境    加热器异常    闭合

DI7通道设置测试
    连接CSU
    ${干接点设置值}    获取web参数量    直流空调异常干接点
    wait until keyword succeeds    30    1    设置通道配置    ${plat.battmidvolt3}    系统运行环境    直流空调异常    断开
    Wait Until Keyword Succeeds    10    2    设置web参数量    直流空调异常    屏蔽
    Wait Until Keyword Succeeds    3m    1    判断告警不存在    直流空调异常
    Wait Until Keyword Succeeds    5m    2    设置web参数量    直流空调异常    严重
    wait until keyword succeeds    1m    1    查询指定告警信息    直流空调异常
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    直流空调异常    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    直流空调异常
        sleep    3
        ${告警级别获取}    获取web告警属性    直流空调异常    告警级别
        should be equal    '${告警级别设置}'    '${告警级别获取}'
    END
    wait until keyword succeeds    30    1    设置web参数量    直流空调异常    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    Comment    FOR    ${告警干接点设置}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
    Comment    \    ${干接点设置为无}    evaluate    0
    Comment    \    ${告警干接点获取}    获取web告警属性    扩展柜烟雾告警    告警干接点
    Comment    \    run keyword if    ${告警干接点获取}!=0    wait until keyword succeeds    30    1    设置web参数量    扩展柜烟雾告警干接点    ${干接点设置为无}
    Comment    \    sleep    5
    Comment    \    ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
    Comment    \    should be true    ${干接点实际是否动作状态1}==1
    Comment    \    wait until keyword succeeds    30    1    设置web参数量    扩展柜烟雾告警干接点    ${告警干接点设置}
    Comment    \    sleep    5
    Comment    \    ${告警干接点获取}    获取web告警属性    扩展柜烟雾告警    告警干接点
    Comment    \    should be true    ${告警干接点设置}==${告警干接点获取}
    Comment    \    ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
    Comment    \    should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
    Comment    END
    wait until keyword succeeds    30    1    设置通道配置    ${plat.battmidvolt3}    系统运行环境    直流空调异常    闭合
    Wait Until Keyword Succeeds    3m    1    判断告警不存在    直流空调异常
    [Teardown]    Run keywords    设置web参数量    直流空调异常干接点    0
    ...    AND    设置web参数量    直流空调异常    次要
    ...    AND    wait until keyword succeeds    30    1    设置通道配置    ${plat.battmidvolt3}    系统运行环境    直流空调异常    闭合

DI8通道设置测试
    连接CSU
    ${干接点设置值}    获取web参数量    逆变器告警干接点
    wait until keyword succeeds    30    1    设置通道配置    ${plat.battmidvolt4}    能源系统    逆变器告警    断开
    Wait Until Keyword Succeeds    10    2    设置web参数量    逆变器告警    屏蔽
    Wait Until Keyword Succeeds    3m    1    判断告警不存在    逆变器告警
    Wait Until Keyword Succeeds    5m    2    设置web参数量    逆变器告警    严重
    wait until keyword succeeds    1m    1    查询指定告警信息    逆变器告警
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    逆变器告警    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    逆变器告警
        sleep    3
        ${告警级别获取}    获取web告警属性    逆变器告警    告警级别
        should be equal    '${告警级别设置}'    '${告警级别获取}'
    END
    wait until keyword succeeds    30    1    设置web参数量    逆变器告警    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    Comment    FOR    ${告警干接点设置}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
    Comment    \    ${干接点设置为无}    evaluate    0
    Comment    \    ${告警干接点获取}    获取web告警属性    扩展柜烟雾告警    告警干接点
    Comment    \    run keyword if    ${告警干接点获取}!=0    wait until keyword succeeds    30    1    设置web参数量    扩展柜烟雾告警干接点    ${干接点设置为无}
    Comment    \    sleep    5
    Comment    \    ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
    Comment    \    should be true    ${干接点实际是否动作状态1}==1
    Comment    \    wait until keyword succeeds    30    1    设置web参数量    扩展柜烟雾告警干接点    ${告警干接点设置}
    Comment    \    sleep    5
    Comment    \    ${告警干接点获取}    获取web告警属性    扩展柜烟雾告警    告警干接点
    Comment    \    should be true    ${告警干接点设置}==${告警干接点获取}
    Comment    \    ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
    Comment    \    should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
    Comment    END
    wait until keyword succeeds    30    1    设置通道配置    ${plat.battmidvolt4}    能源系统    逆变器告警    闭合
    Wait Until Keyword Succeeds    3m    1    判断告警不存在    逆变器告警
    [Teardown]    Run keywords    设置web参数量    逆变器告警干接点    0
    ...    AND    设置web参数量    逆变器告警    次要
    ...    AND    wait until keyword succeeds    30    1    设置通道配置    ${plat.battmidvolt4}    能源系统    逆变器告警    闭合

