*** Settings ***
Suite Setup       主动告警测试前置条件    ${CSU_role}
Suite Teardown    设置web设备参数量为默认值    CSU主动告警使能
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0002_批量获取状态量和PU告警测试
    [Documentation]    power_sm字典中只显示SMR告警/SMR故障/SMR通讯中断
    ...    所以此处无需从power_sm中再查找数据，以data_dict得到的为主
    写入CSV文档    PU数字量和告警量获取测试    信号名称    信号值    结果
    连接CSU
    ${级别设置值}    获取web参数量    PU告警
    run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    PU告警    严重
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=太阳能模块
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除告警量信号}    ${排除列表}    1    ${模拟PU起始地址}    1
    Comment    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    整流器    alarm    ${PU排除告警量信号}
    Comment    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    ${待测数据长度}    Get Length    ${列表1}
    #power_sm字典中只显示SMR告警/SMR故障/SMR通讯中断
    #所以此处无需从power_sm中查找数据，以data_dict得到的为主
    ${待测}    Evaluate    ${待测数据长度}-1
    ${待测索引}    Evaluate    random.randint(0,${待测})    random
    FOR    ${i}    IN    ${列表1}[${待测索引}]
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${实时告警中的设备名称}    主动告警_组合实时告警中的设备名称    太阳能模块    ${信号名称}
        Run Keyword And Continue On Failure    整流器或PU告警量产生封装判断结果    power_sm    pu    ${信号名称}    数字量    ${告警产生}    ${模拟PU起始地址}    PU告警    PU数字量和告警量获取测试    ${实时告警中的设备名称}    null    null    null    null    null
        ...    null
        Run Keyword And Continue On Failure    整流器或PU告警量恢复封装判断结果    power_sm    pu    ${信号名称}    数字量    ${告警恢复}    ${模拟PU起始地址}    PU告警    PU数字量和告警量获取测试    太阳能模块    null    null    null    null    null
        ...    null
    END

power_sm_0003_批量获取状态量和PU故障测试
    [Documentation]    power_sm字典中只显示SMR告警/SMR故障/SMR通讯中断
    ...    所以此处无需从power_sm中再查找数据，以data_dict得到的为主
    写入CSV文档    PU数字量和故障量获取测试    信号名称    信号值    结果
    连接CSU
    ${级别设置值}    获取web参数量    PU故障
    run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    PU故障    严重
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=太阳能模块
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除故障量信号}    ${排除列表}    1    ${模拟PU起始地址}    1
    Comment    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    整流器    alarm    ${PU排除故障量信号}
    Comment    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    #power_sm字典中只显示SMR告警/SMR故障/SMR通讯中断
    #所以此处无需从power_sm中查找数据，以data_dict得到的为主
    ${待测数据长度}    Get Length    ${列表1}
    ${待测}    Evaluate    ${待测数据长度}-1
    ${待测索引}    Evaluate    random.randint(0,${待测})    random
    FOR    ${i}    IN    ${列表1}[${待测索引}]
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${实时告警中的设备名称}    主动告警_组合实时告警中的设备名称    太阳能模块    ${信号名称}
        Run Keyword And Continue On Failure    整流器或PU故障量产生封装判断结果    power_sm    pu    ${信号名称}    数字量    ${告警产生}    ${模拟PU起始地址}    PU故障    PU数字量和故障量获取测试    ${实时告警中的设备名称}    null    null    null    null    null
        ...    null
        Run Keyword And Continue On Failure    整流器或PU故障量恢复封装判断结果    power_sm    pu    ${信号名称}    数字量    ${告警恢复}    ${模拟PU起始地址}    PU故障    PU数字量和故障量获取测试    null    null    null    null    null    null
        ...    null
    END

power_sm_0004_PU通讯中断
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    显示属性配置    PU通讯状态    数字量    web_attr=On    gui_attr=On
    ${级别设置值}    获取web参数量    PU通讯中断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    PU通讯中断    严重
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    PU通讯中断_1
    设置子工具个数    pu    1
    FOR    ${PU序号}    IN RANGE    2    ${北向协议PU最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU通讯状态-${PU序号}    异常
        wait until keyword succeeds    5m    1    判断告警存在    PU通讯中断-${PU序号}
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    PU通讯中断-${PU序号}    ${SSH}
        should be true    ${power_sm告警存在}[0]
    END
    ${类型}    evaluate    type(${北向协议PU最大数})
    设置子工具个数    pu    ${北向协议PU最大数}
    FOR    ${PU序号}    IN RANGE    1    ${北向协议PU最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    PU通讯状态-${PU序号}    正常
        wait until keyword succeeds    5m    1    判断告警不存在    PU通讯中断-${PU序号}
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    PU通讯中断-${PU序号}    ${SSH}
        should not be true    ${power_sm告警存在}[0]
    END
    wait until keyword succeeds    1m    2    查询指定告警信息不为    PU通讯中断
    显示属性配置    PU通讯状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    run keywords    设置子工具个数    pu    ${北向协议PU最大数}
    ...    AND    sleep    3m
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

批量获取PU内部告警
    写入CSV文档    太阳能内部告警获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=太阳能模块
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${太阳能模块内部告警排除告警量}    ${排除列表}    1    ${模拟PU起始地址}    1
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    太阳能模块    alarm    ${太阳能模块内部告警排除告警量}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    ${待测数据长度}    Get Length    ${power_sm待测}
    ${number}    Set Variable    1
    FOR    ${i}    IN    @{power_sm待测}
        Log    正在验证第${number}个数据，一共${待测数据长度}个数据
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${实时告警中的设备名称}    主动告警_组合实时告警中的设备名称    太阳能模块    ${信号名称}
        Run Keyword And Continue On Failure    整流器或PU内部告警产生封装判断结果    power_sm    pu    ${信号名称}    数字量    ${告警产生}    ${模拟PU起始地址}    太阳能内部告警获取测试    ${实时告警中的设备名称}    null    null    null    null    null
        Run Keyword And Continue On Failure    整流器或PU内部告警恢复封装判断结果    power_sm    pu    ${信号名称}    数字量    ${告警恢复}    ${模拟PU起始地址}    太阳能内部告警获取测试    ${实时告警中的设备名称}    null    null    null    null    null
        Log    正在验证第${number}个数据，一共${待测数据长度}个数据
    END
