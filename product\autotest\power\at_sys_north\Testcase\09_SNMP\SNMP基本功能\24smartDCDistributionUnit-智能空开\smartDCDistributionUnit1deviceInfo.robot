*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
snmp批量获取SDDU设备信息测试
    写入CSV文档    SDDU2设备信息获取测试    信号名称    信号值    结果    备注
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string32    INDEX_DEVICE_CH=智能直流配电单元
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SDDU2排除系统名称}    ${排除列表}
    ${设备名称}    Set Variable    smartDCDistributionUnit
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    ${设备名称}    deviceInfo
    @{SDDU2厂家信息数据}    create list
    FOR    ${i}    IN    @{snmp待测}
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        ${信号序号}    BuiltIn.Convert To Integer    ${信号序号}
        Run Keyword IF    ${信号序号}==1    Append to List    ${SDDU2厂家信息数据}    ${i}
        Run Keyword IF    ${信号序号}==5    Append to List    ${SDDU2厂家信息数据}    ${i}
        Run Keyword IF    ${信号序号}==9    Append to List    ${SDDU2厂家信息数据}    ${i}
        Run Keyword IF    ${信号序号}==13    Append to List    ${SDDU2厂家信息数据}    ${i}
    END
    FOR    ${i}    IN    @{SDDU2厂家信息数据}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    snmp_name
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        ${信号序号1}    BuiltIn.Convert To Integer    ${信号序号}
        ${呼叫命令}    Evaluate    (${信号序号1}-1)//4+1
        ${命令名称}    Set Variable    空开厂家${呼叫命令}
        Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    IntelAirSwitExtend    ${信号名称}    ${命令名称}    V99.23    SDDU2设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
        Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    IntelAirSwitExtend    ${信号名称}    ${命令名称}    V10.10    SDDU2设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
        Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    IntelAirSwitExtend    ${信号名称}    ${命令名称}    V12345    SDDU2设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
    END
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string32    INDEX_DEVICE_CH=智能直流配电单元
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SDDU2排除软件版本}    ${排除列表}
    ${设备名称}    Set Variable    smartDCDistributionUnit
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    ${设备名称}    deviceInfo
    @{SDDU2厂家信息数据1}    create list
    FOR    ${i}    IN    @{snmp待测}
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        ${信号序号}    BuiltIn.Convert To Integer    ${信号序号}
        Run Keyword IF    ${信号序号}==1    Append to List    ${SDDU2厂家信息数据1}    ${i}
        Run Keyword IF    ${信号序号}==5    Append to List    ${SDDU2厂家信息数据1}    ${i}
        Run Keyword IF    ${信号序号}==9    Append to List    ${SDDU2厂家信息数据1}    ${i}
        Run Keyword IF    ${信号序号}==13    Append to List    ${SDDU2厂家信息数据1}    ${i}
    END
    FOR    ${i}    IN    @{SDDU2厂家信息数据1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    snmp_name
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        ${信号序号1}    BuiltIn.Convert To Integer    ${信号序号}
        ${呼叫命令}    Evaluate    (${信号序号1}-1)//4+1
        ${命令名称}    Set Variable    空开厂家${呼叫命令}
        Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    IntelAirSwitExtend    ${信号名称}    ${命令名称}    VZXDU48 FB100B3    SDDU2设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
        Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    IntelAirSwitExtend    ${信号名称}    ${命令名称}    ZTE-smartli    SDDU2设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
        Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    IntelAirSwitExtend    ${信号名称}    ${命令名称}    zte    SDDU2设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
    END
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=date    INDEX_DEVICE_CH=智能直流配电单元
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    @{SDDU2排除软件发布日期}    ${排除列表}
    ${设备名称}    Set Variable    smartDCDistributionUnit
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    ${设备名称}    deviceInfo
    @{SDDU2厂家信息数据2}    create list
    FOR    ${i}    IN    @{snmp待测}
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        ${信号序号}    BuiltIn.Convert To Integer    ${信号序号}
        Run Keyword IF    ${信号序号}==1    Append to List    ${SDDU2厂家信息数据2}    ${i}
        Run Keyword IF    ${信号序号}==5    Append to List    ${SDDU2厂家信息数据2}    ${i}
        Run Keyword IF    ${信号序号}==9    Append to List    ${SDDU2厂家信息数据2}    ${i}
        Run Keyword IF    ${信号序号}==13    Append to List    ${SDDU2厂家信息数据2}    ${i}
    END
    FOR    ${i}    IN    @{SDDU2厂家信息数据2}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    snmp_name
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        ${信号序号1}    BuiltIn.Convert To Integer    ${信号序号}
        ${呼叫命令}    Evaluate    (${信号序号1}-1)//4+1
        ${命令名称}    Set Variable    空开厂家${呼叫命令}
        Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    IntelAirSwitExtend    ${信号名称}    ${命令名称}    2018-11-15    SDDU2设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
        Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    IntelAirSwitExtend    ${信号名称}    ${命令名称}    2021-08-23    SDDU2设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
        Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    IntelAirSwitExtend    ${信号名称}    ${命令名称}    2022-02-22    SDDU2设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
    END
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=float    INDEX_DEVICE_CH=智能直流配电单元
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${排除列表}    ${排除列表}
    ${设备名称}    Set Variable    smartDCDistributionUnit
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    ${设备名称}    deviceInfo
    @{SDDU2厂家信息数据3}    create list
    FOR    ${i}    IN    @{snmp待测}
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        ${信号序号}    BuiltIn.Convert To Integer    ${信号序号}
        Run Keyword IF    ${信号序号}==1    Append to List    ${SDDU2厂家信息数据3}    ${i}
        Run Keyword IF    ${信号序号}==5    Append to List    ${SDDU2厂家信息数据3}    ${i}
        Run Keyword IF    ${信号序号}==9    Append to List    ${SDDU2厂家信息数据3}    ${i}
        Run Keyword IF    ${信号序号}==13    Append to List    ${SDDU2厂家信息数据3}    ${i}
    END
    FOR    ${i}    IN    @{SDDU2厂家信息数据3}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    snmp_name
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        ${信号序号1}    BuiltIn.Convert To Integer    ${信号序号}
        ${呼叫命令}    Evaluate    (${信号序号1}-1)//4+1
        ${命令名称}    Set Variable    空开厂家${呼叫命令}
        ${缺省值}    获取web参数上下限范围    ${信号名称}
        Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    IntelAirSwitExtend    ${信号名称}    ${命令名称}    ${缺省值}[1]    SDDU2设备信息获取测试    数值    null    ${节点名称}    ${信号序号}
        Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    IntelAirSwitExtend    ${信号名称}    ${命令名称}    ${缺省值}[2]    SDDU2设备信息获取测试    数值    null    ${节点名称}    ${信号序号}
    END
