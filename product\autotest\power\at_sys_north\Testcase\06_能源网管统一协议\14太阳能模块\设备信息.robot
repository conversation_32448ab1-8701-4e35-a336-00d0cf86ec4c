*** Settings ***
Suite Setup       #Run keywords    测试用例前置条件    # AND    仅有市电条件上电
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_批量获取太阳能模块设备信息
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    写入CSV文档    太阳能模块设备信息获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string64    INDEX_DEVICE_CH=太阳能模块
    ${排除列表}    create list
	@{信号名称列表1}  create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除设备版本信息}    ${排除列表}    1    ${模拟PU起始地址}
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    太阳能模块    device info    ${PU排除设备版本信息}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    FOR    ${i}    IN    @{power_sm待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
		${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
		Append To List       ${信号名称列表1}    ${dict}
	END
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    pu    ${信号名称列表1}    版本    V99.23    PU设备信息获取测试    字符    获取太阳能模块设备信息   
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    pu    ${信号名称列表1}    版本    V10.10    PU设备信息获取测试    字符    获取太阳能模块设备信息    
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    pu    ${信号名称列表1}    版本    V1.81    PU设备信息获取测试    字符    获取太阳能模块设备信息    
    
	
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string32    INDEX_DEVICE_CH=太阳能模块
    ${排除列表}    create list
	@{信号名称列表2}  create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除设备名称信息}    ${排除列表}    1    ${模拟PU起始地址}
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    太阳能模块    device info    ${PU排除设备名称信息}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    FOR    ${i}    IN    @{power_sm待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
		${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
		Append To List       ${信号名称列表2}    ${dict}
	END
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    pu    ${信号名称列表2}    厂家信息    VZXDU48 FB100B3    PU设备信息获取测试    字符    获取太阳能模块设备信息    
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    pu    ${信号名称列表2}    厂家信息    ZTE-smartli        PU设备信息获取测试    字符    获取太阳能模块设备信息    
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    pu    ${信号名称列表2}    厂家信息    VZXDU48 FB100C2    PU设备信息获取测试    字符    获取太阳能模块设备信息    
    
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=float    INDEX_DEVICE_CH=太阳能模块
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    2    ${模拟PU起始地址}
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    太阳能模块    device info    null
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    ${缺省值列表}     获取缺省值列表  ${power_sm待测}    1        power_sm
    Run Keyword And Continue On Failure    南向子设备厂家信息列表封装判断结果    power_sm    pu    ${缺省值列表}    版本        PU设备信息获取测试    数值    获取太阳能模块设备信息   
	${缺省值列表}     获取缺省值列表  ${power_sm待测}    2     power_sm
	Run Keyword And Continue On Failure    南向子设备厂家信息列表封装判断结果    power_sm    pu    ${缺省值列表}    版本        PU设备信息获取测试    数值    获取太阳能模块设备信息    
    ${缺省值列表}     获取缺省值列表  ${power_sm待测}    0         power_sm
	Run Keyword And Continue On Failure    南向子设备厂家信息列表封装判断结果    power_sm    pu    ${缺省值列表}    版本        PU设备信息获取测试    数值    获取太阳能模块设备信息    
    
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=int    INDEX_DEVICE_CH=太阳能模块
    ${排除列表}    create list
	@{信号名称列表3}  create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    2    ${模拟PU起始地址}
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    太阳能模块    device info    null
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    FOR    ${i}    IN    @{power_sm待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
		Comment    ${缺省值}    获取web参数上下限范围    ${信号名称}
		${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
		Append To List       ${信号名称列表3}    ${dict}
	END 
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    pu    ${信号名称列表3}    资产管理信息    0    PU设备信息获取测试    数值    获取太阳能模块设备信息   
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    pu    ${信号名称列表3}    资产管理信息    255    PU设备信息获取测试    数值    获取太阳能模块设备信息    
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    pu    ${信号名称列表3}    资产管理信息    0    PU设备信息获取测试    数值    获取太阳能模块设备信息    
    
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=date    INDEX_DEVICE_CH=太阳能模块
    ${排除列表}    create list
	@{信号名称列表4}  create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${PU排除设备日期信息}    ${排除列表}    1    ${模拟PU起始地址}
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    太阳能模块    device info    ${PU排除设备日期信息}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    FOR    ${i}    IN    @{power_sm待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
		${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
		Append To List       ${信号名称列表4}    ${dict}
	END
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    pu    ${信号名称列表4}    版本    2018-11-15    PU设备信息获取测试    字符    获取太阳能模块设备信息    
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    pu    ${信号名称列表4}    版本    2021-08-23    PU设备信息获取测试    字符    获取太阳能模块设备信息    
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    pu    ${信号名称列表4}    版本    2018-07-28    PU设备信息获取测试    字符    获取太阳能模块设备信息    
    [Teardown]    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0002_PU条码8
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    设置子工具值    PU    all    资产管理信息    PU条码3    12522
    设置子工具值    PU    all    资产管理信息    PU条码4    50416
    设置子工具值    PU    all    资产管理信息    PU条码5g    178
    sleep    120
    FOR    ${PU序号}    IN RANGE    1    ${北向协议PU最大数}+1
        ${web获取}    获取web实时数据    <<PU条码-${PU序号}~0x10001080080001>>
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取太阳能模块设备信息    PU条码-${PU序号}    ${SSH}
        should be equal as numbers    ${web获取}    210097205426
        should be equal as numbers    ${power_sm获取值1}    210097205426
    END
    [Teardown]    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0003_生产日期9
    [Tags]    4
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    设置子工具值    PU    all    资产管理信息    生产日期年5d6g    2020
    设置子工具值    PU    all    资产管理信息    生产日期月6d    11
    设置子工具值    PU    all    资产管理信息    生产日期日7g    19
    sleep    5m
    FOR    ${PU序号}    IN RANGE    1    ${北向协议PU最大数}+1
        ${web获取}    获取web实时数据    <<生产日期-${PU序号}~0x10001080090001>>
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取太阳能模块设备信息    生产日期-${PU序号}    ${SSH}
        should be equal as strings    ${web获取}    2020-11-19
        should be equal as strings    ${power_sm获取值1}    2020-11-19
    END
    [Teardown]    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

pm_sm_0004_太阳能序列号
    连接CSU
    FOR    ${PU序号}    IN RANGE    1    ${北向协议PU最大数}+1
        ${PU序列号}    Set Variable    4294967295
        ${PU地址}    Evaluate    ${PU序号}-${模拟PU起始地址}+1
        ${PU地址}    Convert To String    ${PU地址}
        ${PU序列号}    Evaluate    ${PU序列号}-${PU序号}
        ${PU序列号}    Convert To String    ${PU序列号}
        设置子工具值    PU    ${PU地址}    版本    PU序列号    ${PU序列号}
        设置子工具值    PU    ${PU地址}    厂家信息    PU序列号    ${PU序列号}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<PU序列号-${PU序号}~0x10001080010001>>    ${PU序列号}
        sleep    10
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取太阳能模块设备信息    PU序列号-${PU序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    ${PU序列号}
        ${PU序列号}    Set Variable    2147483647
        设置子工具值    PU    ${PU地址}    版本    PU序列号    ${PU序列号}
        设置子工具值    PU    ${PU地址}    厂家信息    PU序列号    ${PU序列号}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<PU序列号-${PU序号}~0x10001080010001>>    ${PU序列号}
        sleep    10
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取太阳能模块设备信息    PU序列号-${PU序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    ${PU序列号}
        ${PU序列号}    Set Variable    2415919103
        设置子工具值    PU    ${PU地址}    版本    PU序列号    ${PU序列号}
        设置子工具值    PU    ${PU地址}    厂家信息    PU序列号    ${PU序列号}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<PU序列号-${PU序号}~0x10001080010001>>    ${PU序列号}
        sleep    10
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取太阳能模块设备信息    PU序列号-${PU序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    ${PU序列号}
        ${PU序列号}    Set Variable    3803448746
        设置子工具值    PU    ${PU地址}    版本    PU序列号    ${PU序列号}
        设置子工具值    PU    ${PU地址}    厂家信息    PU序列号    ${PU序列号}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<PU序列号-${PU序号}~0x10001080010001>>    ${PU序列号}
        sleep    10
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取太阳能模块设备信息    PU序列号-${PU序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    ${PU序列号}
        ${PU序列号}    Set Variable    3803448753
        设置子工具值    PU    ${PU地址}    版本    PU序列号    ${PU序列号}
        设置子工具值    PU    ${PU地址}    厂家信息    PU序列号    ${PU序列号}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<PU序列号-${PU序号}~0x10001080010001>>    ${PU序列号}
        sleep    10
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取太阳能模块设备信息    PU序列号-${PU序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    ${PU序列号}
        ${PU序列号}    Set Variable    ${PU地址}
        设置子工具值    PU    ${PU地址}    版本    PU序列号    ${PU序列号}
        设置子工具值    PU    ${PU地址}    厂家信息    PU序列号    ${PU序列号}
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    <<PU序列号-${PU序号}~0x10001080010001>>    ${PU序列号}
        sleep    10
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取太阳能模块设备信息    PU序列号-${PU序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    ${PU序列号}
    END
