*** Settings ***
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
批量获取设备信息
    写入CSV文档    FB100B3设备信息获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string64    INDEX_DEVICE_CH=FBBMS
    ${排除列表}    create list
	@{信号名称列表1}   create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除设备版本信息}    ${排除列表}    1
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${dict1}    Create Dictionary
        Set To Dictionary    ${dict1}     name     ${信号名称}
		Append To List       ${信号名称列表1}    ${dict1}
    END
	Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    smartli    ${信号名称列表1}    只读    V99.23    FB100B3设备信息获取测试
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    smartli    ${信号名称列表1}    只读    V10.10    FB100B3设备信息获取测试
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    smartli    ${信号名称列表1}    只读    V1.81    FB100B3设备信息获取测试
	
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string64    INDEX_DEVICE_CH=FBBMS
    ${排除列表}    create list
	@{信号名称列表2}    create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除设备名称信息}    ${排除列表}    1
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${dict2}    Create Dictionary
        Set To Dictionary    ${dict2}     name     ${信号名称}
		Append To List       ${信号名称列表2}    ${dict2}
    END
	Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    smartli    ${信号名称列表2}    只读    VZXDU48 FB100B3    FB100B3设备信息获取测试
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    smartli    ${信号名称列表2}    只读    ZTE-smartli    FB100B3设备信息获取测试
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    smartli    ${信号名称列表2}    只读    VZXDU48 FB100C2    FB100B3设备信息获取测试
	
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string64    INDEX_DEVICE_CH=FBBMS
    ${排除列表}    create list
	@{信号名称列表3}   create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除设备序列号信息}    ${排除列表}    1
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${dict3}    Create Dictionary
        Set To Dictionary    ${dict3}     name     ${信号名称}
		Append To List       ${信号名称列表3}    ${dict3}
    END
	Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    smartli    ${信号名称列表3}    读写    210097205489    FB100B3设备信息获取测试
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    smartli    ${信号名称列表3}    读写    210097205673    FB100B3设备信息获取测试
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    smartli    ${信号名称列表3}    读写    210097205688    FB100B3设备信息获取测试
	
	
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=float    INDEX_DEVICE_CH=FBBMS
    ${排除列表}    create list   
    Append To List    ${排除列表}    INDEX_SID=0x170010800d0001
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    1
    ${缺省值列表}   获取缺省值列表  ${列表1}    1
	Run Keyword And Continue On Failure    南向子设备数值型厂家信息列表封装判断结果    smartli    ${缺省值列表}    只读        FB100B3设备信息获取测试
    ${缺省值列表}   获取缺省值列表  ${列表1}    2
	Run Keyword And Continue On Failure    南向子设备数值型厂家信息列表封装判断结果    smartli    ${缺省值列表}    只读        FB100B3设备信息获取测试
	${缺省值列表}   获取缺省值列表  ${列表1}    0
    Run Keyword And Continue On Failure    南向子设备数值型厂家信息列表封装判断结果    smartli    ${缺省值列表}    只读        FB100B3设备信息获取测试
	
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=int    INDEX_DEVICE_CH=FBBMS
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    1
    ${缺省值列表}   获取缺省值列表  ${列表1}    1
	Run Keyword And Continue On Failure    南向子设备数值型厂家信息列表封装判断结果    smartli    ${缺省值列表}    只读    FB100B3设备信息获取测试
    ${缺省值列表}   获取缺省值列表  ${列表1}    2
	Run Keyword And Continue On Failure    南向子设备数值型厂家信息列表封装判断结果    smartli    ${缺省值列表}    只读    FB100B3设备信息获取测试
	${缺省值列表}   获取缺省值列表  ${列表1}    0
	Run Keyword And Continue On Failure    南向子设备数值型厂家信息列表封装判断结果    smartli    ${缺省值列表}    只读    FB100B3设备信息获取测试

    Comment    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string32    INDEX_DEVICE_CH=FBBMS
    Comment    ${排除列表}    create list
    Comment    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除str32设备信息}    ${排除列表}    1
    Comment    FOR    ${i}    IN    @{列表1}
    Comment    \    ${信号名称}    Get From Dictionary    ${i}    signal_name
    Comment    \    Run Keyword And Continue On Failure    南向子设备字符型厂家信息封装判断结果    smartli    ${信号名称}    读写    2021/12/26    FB100B3设备信息获取测试
    Comment    \    Run Keyword And Continue On Failure    南向子设备字符型厂家信息封装判断结果    smartli    ${信号名称}    读写    2021/4/19    FB100B3设备信息获取测试
    Comment    \    Run Keyword And Continue On Failure    南向子设备字符型厂家信息封装判断结果    smartli    ${信号名称}    读写    2022/1/20    FB100B3设备信息获取测试
    Comment    END
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string32    INDEX_DEVICE_CH=FBBMS
    ${排除列表}    create list
	@{信号名称列表4}   create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除str32 BOOT信息}    ${排除列表}    1
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${dict4}    Create Dictionary
        Set To Dictionary    ${dict4}     name     ${信号名称}
		Append To List       ${信号名称列表4}    ${dict4}
    END
	Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    smartli    ${信号名称列表4}    只读    SmartLi100    FB100B3设备信息获取测试
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    smartli    ${信号名称列表4}    只读    ZTE-Li    FB100B3设备信息获取测试
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    smartli    ${信号名称列表4}    只读    ZTE-SS    FB100B3设备信息获取测试
  
电池容量测试
    连接CSU
    设置子工具值    smartli    all    读写    电池容量    0
    Wait Until Keyword Succeeds    20m    5    信号量数据值为(强制获取)    电池容量-1    0
    Wait Until Keyword Succeeds    20m    5    信号量数据值为(强制获取)    电池容量-8    0
    Wait Until Keyword Succeeds    20m    5    信号量数据值为(强制获取)    电池容量-${铁锂电池组数}    0
    设置子工具值    smartli    all    读写    电池容量    300
    Wait Until Keyword Succeeds    20m    5    信号量数据值为(强制获取)    电池容量-1    300
    Wait Until Keyword Succeeds    20m    5    信号量数据值为(强制获取)    电池容量-8    300
    Wait Until Keyword Succeeds    20m    5    信号量数据值为(强制获取)    电池容量-${铁锂电池组数}    300
    设置子工具值    smartli    all    读写    电池容量    100
    Wait Until Keyword Succeeds    20m    5    信号量数据值为(强制获取)    电池容量-1    100
    Wait Until Keyword Succeeds    20m    5    信号量数据值为(强制获取)    电池容量-8    100
    Wait Until Keyword Succeeds    20m    5    信号量数据值为(强制获取)    电池容量-${铁锂电池组数}    100


BMS软件发布日期获取测试
    连接CSU
    设置子工具值    smartli    all    只读    BMS软件发布日期    2020
    设置子工具值    smartli    all    只读    BMS软件发布日期mouth    12
    设置子工具值    smartli    all    只读    BMS软件发布日期day    5
    Wait Until Keyword Succeeds    20m  5    设备信息获取值    BMS软件发布日期-1    2020-12-05
    Wait Until Keyword Succeeds    20m  5    设备信息获取值    BMS软件发布日期-8    2020-12-05
    Wait Until Keyword Succeeds    20m  5    设备信息获取值    BMS软件发布日期-${铁锂电池组数}    2020-12-05 
    设置子工具值    smartli    all    只读    BMS软件发布日期    2020
    设置子工具值    smartli    all    只读    BMS软件发布日期mouth    12
    设置子工具值    smartli    all    只读    BMS软件发布日期day    4
    Wait Until Keyword Succeeds    20m  5    设备信息获取值    BMS软件发布日期-1    2020-12-04
    Wait Until Keyword Succeeds    20m  5    设备信息获取值    BMS软件发布日期-8    2020-12-04
    Wait Until Keyword Succeeds    20m  5    设备信息获取值    BMS软件发布日期-${铁锂电池组数}    2020-12-04
    # 测试台上下电操作    ${测试台编号}    OFF
    # sleep    5
    # 测试台上下电操作    ${测试台编号}    ON
    # 连接CSU
    # 设置子工具值    smartli    all    只读    BMS软件发布日期    2020
    # 设置子工具值    smartli    all    只读    BMS软件发布日期mouth    12
    # 设置子工具值    smartli    all    只读    BMS软件发布日期day    5
    # Wait Until Keyword Succeeds    20m  5    设备信息获取值    BMS软件发布日期-1    2020-12-05
    # Wait Until Keyword Succeeds    20m  5    设备信息获取值    BMS软件发布日期-8    2020-12-05
    # Wait Until Keyword Succeeds    20m  5    设备信息获取值    BMS软件发布日期-${铁锂电池组数}    2020-12-05
    # 设置子工具值    smartli    all    只读    BMS软件发布日期    2020
    # 设置子工具值    smartli    all    只读    BMS软件发布日期mouth    12
    # 设置子工具值    smartli    all    只读    BMS软件发布日期day    4
    # Wait Until Keyword Succeeds    20m  5    设备信息获取值    BMS软件发布日期-1    2020-12-04
    # Wait Until Keyword Succeeds    20m  5    设备信息获取值    BMS软件发布日期-8    2020-12-04
    # Wait Until Keyword Succeeds    20m  5    设备信息获取值    BMS软件发布日期-${铁锂电池组数}    2020-12-04

BDCU软件发布日期获取测试
    连接CSU
    设置子工具值    smartli    all    只读    BDU版本日期    2020
    设置子工具值    smartli    all    只读    BDU版本日期mouth    12
    设置子工具值    smartli    all    只读    BDU版本日期day    12
    Wait Until Keyword Succeeds     20m   5    设备信息获取值     BDCU软件发布日期-1    2020-12-12
    Wait Until Keyword Succeeds     20m   5    设备信息获取值     BDCU软件发布日期-8    2020-12-12
    Wait Until Keyword Succeeds     20m   5    设备信息获取值     BDCU软件发布日期-${铁锂电池组数}    2020-12-12
    设置子工具值    smartli    all    只读    BDU版本日期    2020
    设置子工具值    smartli    all    只读    BDU版本日期mouth    11
    设置子工具值    smartli    all    只读    BDU版本日期day    11
    Wait Until Keyword Succeeds    20m   5    设备信息获取值    BDCU软件发布日期-1    2020-11-11
    Wait Until Keyword Succeeds    20m   5    设备信息获取值    BDCU软件发布日期-8    2020-11-11
    Wait Until Keyword Succeeds    20m   5    设备信息获取值    BDCU软件发布日期-${铁锂电池组数}      2020-11-11
    # 测试台上下电操作    ${测试台编号}    OFF
    # sleep    5
    # 测试台上下电操作    ${测试台编号}    ON
    # 连接CSU
    # 设置子工具值    smartli    all    只读    BDU版本日期    2020
    # 设置子工具值    smartli    all    只读    BDU版本日期mouth    12
    # 设置子工具值    smartli    all    只读    BDU版本日期day    12
    # Wait Until Keyword Succeeds    20m   5    设备信息获取值    BDCU软件发布日期-1    2020-12-12
    # Wait Until Keyword Succeeds    20m   5    设备信息获取值    BDCU软件发布日期-8    2020-12-12
    # Wait Until Keyword Succeeds    20m   5    设备信息获取值    BDCU软件发布日期-${铁锂电池组数}      2020-12-12
    # 设置子工具值    smartli    all    只读    BDU版本日期    2020
    # 设置子工具值    smartli    all    只读    BDU版本日期mouth    11
    # 设置子工具值    smartli    all    只读    BDU版本日期day    11
    # Wait Until Keyword Succeeds    20m   5    设备信息获取值    BDCU软件发布日期-1    2020-11-11
    # Wait Until Keyword Succeeds    20m   5    设备信息获取值    BDCU软件发布日期-8    2020-11-11
    # Wait Until Keyword Succeeds    20m   5    设备信息获取值    BDCU软件发布日期-${铁锂电池组数}      2020-11-11
    
电池启用日期获取测试
    连接CSU
    设置子工具值    smartli    all    只读    电芯启用日期    2018
    设置子工具值    smartli    all    只读    电芯启用日期mouth    07
    设置子工具值    smartli    all    只读    电芯启用日期day    28
    Wait Until Keyword Succeeds    20m  5    设备信息获取值     <<电池启用日期-1~0x170010800f0001>>     2018-07-28
    Wait Until Keyword Succeeds    20m  5    设备信息获取值  <<电池启用日期-8~0x170010800f0001>>        2018-07-28
    Wait Until Keyword Succeeds    20m  5    设备信息获取值     <<电池启用日期-${铁锂电池组数}~0x170010800f0001>>       2018-07-28
    设置子工具值    smartli    all    只读    电芯启用日期    2020
    设置子工具值    smartli    all    只读    电芯启用日期mouth    10
    设置子工具值    smartli    all    只读    电芯启用日期day    11
    Wait Until Keyword Succeeds    20m  5    设备信息获取值     <<电池启用日期-1~0x170010800f0001>>     2020-10-11
    Wait Until Keyword Succeeds    20m  5    设备信息获取值     <<电池启用日期-8~0x170010800f0001>>     2020-10-11
    Wait Until Keyword Succeeds    20m  5    设备信息获取值     <<电池启用日期-${铁锂电池组数}~0x170010800f0001>>     2020-10-11
    # 测试台上下电操作    ${测试台编号}    OFF
    # sleep    5
    # 测试台上下电操作    ${测试台编号}    ON
    # 连接CSU
    # 设置子工具值    smartli    all    只读    电芯启用日期    2018
    # 设置子工具值    smartli    all    只读    电芯启用日期mouth    07
    # 设置子工具值    smartli    all    只读    电芯启用日期day    28
    # Wait Until Keyword Succeeds    20m  5    设备信息获取值     <<电池启用日期-1~0x170010800f0001>>     2018-07-28
    # Wait Until Keyword Succeeds    20m  5    设备信息获取值     <<电池启用日期-8~0x170010800f0001>>     2018-07-28
    # Wait Until Keyword Succeeds    20m  5    设备信息获取值     <<电池启用日期-${铁锂电池组数}~0x170010800f0001>>     2018-07-28
    # 设置子工具值    smartli    all    只读    电芯启用日期    2020
    # 设置子工具值    smartli    all    只读    电芯启用日期mouth    10
    # 设置子工具值    smartli    all    只读    电芯启用日期day    11
    # Wait Until Keyword Succeeds    20m  5    设备信息获取值     <<电池启用日期-1~0x170010800f0001>>     2020-10-11
    # Wait Until Keyword Succeeds    20m  5    设备信息获取值     <<电池启用日期-8~0x170010800f0001>>     2020-10-11
    # Wait Until Keyword Succeeds    20m  5    设备信息获取值     <<电池启用日期-${铁锂电池组数}~0x170010800f0001>>     2020-10-11

电池生产日期获取测试
    连接CSU
    设置子工具值    smartli    all    只读    电芯出厂日期    2020
    设置子工具值    smartli    all    只读    电芯出厂日期mouth    12
    设置子工具值    smartli    all    只读    电芯出厂日期day    12
    Wait Until Keyword Succeeds    20m   5    设备信息获取值    电池生产日期-1     2020-12-12
    Wait Until Keyword Succeeds    20m   5    设备信息获取值    电池生产日期-8     2020-12-12
    Wait Until Keyword Succeeds    20m   5    设备信息获取值     电池生产日期-${铁锂电池组数}      2020-12-12
    设置子工具值    smartli    all    只读    电芯出厂日期    2020
    设置子工具值    smartli    all    只读    电芯出厂日期mouth    11
    设置子工具值    smartli    all    只读    电芯出厂日期day    11
    Wait Until Keyword Succeeds    20m   5    设备信息获取值    电池生产日期-1       2020-11-11
    Wait Until Keyword Succeeds    20m   5    设备信息获取值     电池生产日期-8      2020-11-11
    Wait Until Keyword Succeeds    20m   5    设备信息获取值     电池生产日期-${铁锂电池组数}     2020-11-11
    # 测试台上下电操作    ${测试台编号}    OFF
    # sleep    5
    # 测试台上下电操作    ${测试台编号}    ON
    # 连接CSU
    # sleep    20m
    # 设置子工具值    smartli    all    只读    电芯出厂日期    2020
    # 设置子工具值    smartli    all    只读    电芯出厂日期mouth    12
    # 设置子工具值    smartli    all    只读    电芯出厂日期day    12
    # Wait Until Keyword Succeeds    20m   5    设备信息获取值    电池生产日期-1       2020-12-12
    # Wait Until Keyword Succeeds    20m   5    设备信息获取值     电池生产日期-8      2020-12-12
    # Wait Until Keyword Succeeds    20m   5    设备信息获取值     电池生产日期-${铁锂电池组数}     2020-12-12
    # 设置子工具值    smartli    all    只读    电芯出厂日期    2020
    # 设置子工具值    smartli    all    只读    电芯出厂日期mouth    11
    # 设置子工具值    smartli    all    只读    电芯出厂日期day    11
    # Wait Until Keyword Succeeds    20m   5    设备信息获取值    电池生产日期-1       2020-11-11
    # Wait Until Keyword Succeeds    20m   5    设备信息获取值     电池生产日期-8      2020-11-11
    # Wait Until Keyword Succeeds    20m   5    设备信息获取值     电池生产日期-${铁锂电池组数}     2020-11-11

BOOT日期获取测试
    连接CSU
    设置子工具值    smartli    all    只读    BMS软件版本    V1.00.03.00
    Wait Until Keyword Succeeds   20m   5    设备信息获取值    <<BMS软件版本-1~0x17001080020001>>     V1.00.03.00
    Wait Until Keyword Succeeds   20m   5    设备信息获取值     <<BMS软件版本-8~0x17001080020001>>    V1.00.03.00
    Wait Until Keyword Succeeds   20m   5    设备信息获取值     <<BMS软件版本-${铁锂电池组数}~0x17001080020001>>     V1.00.03.00
    设置子工具值    smartli    all    只读    BMS系统名称    ZXDC48 FB100B3
    Wait Until Keyword Succeeds    20m   5    设备信息获取值    <<BMS系统名称-1~0x17001080010001>>     ZXDC48 FB100B3
    Wait Until Keyword Succeeds    20m   5    设备信息获取值    <<BMS系统名称-8~0x17001080010001>>     ZXDC48 FB100B3
    Wait Until Keyword Succeeds    20m   5    设备信息获取值     <<BMS系统名称-${铁锂电池组数}~0x17001080010001>>    ZXDC48 FB100B3
    设置子工具值    smartli    all    读写    BOOT日期    2020/12/12
    Wait Until Keyword Succeeds    20m   5    设备信息获取值    BOOT版本标识-1        2020/12/12
    Wait Until Keyword Succeeds    20m   5    设备信息获取值    BOOT版本标识-8        2020/12/12
    Wait Until Keyword Succeeds    20m   5    设备信息获取值    BOOT版本标识-${铁锂电池组数}    2020/12/12
    测试台上下电操作    ${测试台编号}    OFF
    sleep    5
    测试台上下电操作    ${测试台编号}    ON
    连接CSU
    sleep    5
    设置子工具值    smartli    all    读写    BOOT日期    2018/7/28
    Wait Until Keyword Succeeds    20m   5    设备信息获取值    BOOT版本标识-1        2018/7/28
    Wait Until Keyword Succeeds    20m   5    设备信息获取值    BOOT版本标识-8        2018/7/28
    Wait Until Keyword Succeeds    20m   5    设备信息获取值    BOOT版本标识-${铁锂电池组数}    2018/7/28
    设置子工具值    smartli    all    读写    BOOT日期    2022/1/25
    Wait Until Keyword Succeeds    20m   5    设备信息获取值    BOOT版本标识-1        2022/1/25
    Wait Until Keyword Succeeds    20m   5    设备信息获取值    BOOT版本标识-8        2022/1/25
    Wait Until Keyword Succeeds    20m   5    设备信息获取值    BOOT版本标识-${铁锂电池组数}    2022/1/25
    
