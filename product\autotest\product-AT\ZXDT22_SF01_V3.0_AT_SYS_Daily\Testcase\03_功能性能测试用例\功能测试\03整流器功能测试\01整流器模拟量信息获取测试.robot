*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
整流器1输出电压获取测试
    [Documentation]    调整输出电压范围按照正负1%。
    [Tags]
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    10    2    设置web参数量    整流器输出高停机电压    61
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    10    2    设置web参数量    均充电压    58
    Wait Until Keyword Succeeds    10    2    设置web参数量    浮充电压    58
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    整流器输出电压-1    57.8
    ${整流器1输出电压}    获取web实时数据    整流器输出电压-1
    log    ${整流器1输出电压}
    should be true    57.42<=${整流器1输出电压}<=58.58
    Wait Until Keyword Succeeds    10    2    设置web参数量    浮充电压    48
    sleep    2
    Wait Until Keyword Succeeds    10    2    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    5m    2    信号量数据值小于    整流器输出电压-1    48.2
    ${整流器1输出电压}    获取web实时数据    整流器输出电压-1
    log    ${整流器1输出电压}
    should be true    47.52<=${整流器1输出电压}<=48.48
    [Teardown]

整流器2输出电压获取测试
    [Documentation]    调整输出电压范围按照正负1%。
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    10    2    设置web参数量    整流器输出高停机电压    61
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    10    2    设置web参数量    均充电压    58
    Wait Until Keyword Succeeds    10    2    设置web参数量    浮充电压    58
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    整流器输出电压-2    57.8
    ${整流器2输出电压}    获取web实时数据    整流器输出电压-2
    log    ${整流器2输出电压}
    should be true    57.42<=${整流器2输出电压}<=58.58
    Wait Until Keyword Succeeds    10    2    设置web参数量    浮充电压    48
    sleep    2
    Wait Until Keyword Succeeds    10    2    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    5m    2    信号量数据值小于    整流器输出电压-2    48.2
    ${整流器2输出电压}    获取web实时数据    整流器输出电压-2
    log    ${整流器2输出电压}
    should be true    47.52<=${整流器2输出电压}<=48.48
    [Teardown]

整流器1输出电流获取测试
    [Tags]
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    2m    2    设置web参数量    浮充电压    53.5
    sleep    2
    Comment    设置web控制量    电池组    启动浮充
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1
    ${工作整流器数量}    获取web实时数据    工作整流器数量
    log    ${工作整流器数量}
    Wait Until Keyword Succeeds    2m    2    设置web参数量    电池组容量_1    0
    ${设置负载电流}    evaluate    ${工作整流器数量}*10
    设置负载电压电流    53.5    ${设置负载电流}
    打开负载输出
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    整流器输出电压-1    53.3
    Wait Until Keyword Succeeds    2m    2    信号量数据值大于    整流器输出电流-1    8
    ${整流器1输出电流}    获取web实时数据    整流器输出电流-1
    should be true    8<=${整流器1输出电流}<=12
    ${整流器最大输出电流}    获取web实时数据    整流器最大输出电流-1
    ${设置负载电流}    evaluate    ${工作整流器数量}*${整流器最大输出电流}*0.87
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    ${设置负载电流}    5
    sleep    20
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    整流器输出电压-1    53.3
    Wait Until Keyword Succeeds    2m    2    信号量数据值大于    整流器输出电流-1    48
    ${整流器1输出电流}    Wait Until Keyword Succeeds    2m    2    获取web实时数据    整流器输出电流-1
    should be true    47<=${整流器1输出电流}<=52
    [Teardown]    run keywords    关闭负载输出
    ...    AND    设置web参数量    电池组容量_1    100

整流器2输出电流获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    2m    2    设置web参数量    浮充电压    53.5
    sleep    2
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1
    ${工作整流器数量}    获取web实时数据    工作整流器数量
    log    ${工作整流器数量}
    Wait Until Keyword Succeeds    2m    2    设置web参数量    电池组容量_1    0
    ${设置负载电流}    evaluate    ${工作整流器数量}*10
    设置负载电压电流    53.5    ${设置负载电流}
    打开负载输出
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    整流器输出电压-2    53.3
    Wait Until Keyword Succeeds    2m    2    信号量数据值大于    整流器输出电流-2    8
    ${整流器2输出电流}    获取web实时数据    整流器输出电流-2
    should be true    8<=${整流器2输出电流}<=12
    ${整流器最大输出电流}    获取web实时数据    整流器最大输出电流-2
    ${设置负载电流}    evaluate    ${工作整流器数量}*${整流器最大输出电流}*0.87
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    ${设置负载电流}    5
    sleep    20
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    整流器输出电压-2    53.3
    Wait Until Keyword Succeeds    2m    2    信号量数据值大于    整流器输出电流-2    48
    ${整流器2输出电流}    Wait Until Keyword Succeeds    2m    2    获取web实时数据    整流器输出电流-2
    should be true    47<=${整流器2输出电流}<=52
    [Teardown]    run keywords    关闭负载输出
    ...    AND    设置web参数量    电池组容量_1    100

整流器机内温度获取测试
    [Tags]
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    在线整流器数量    1
    ${整流器1机内温度}    获取web实时数据    整流器机内温度-1
    log    ${整流器1机内温度}
    ${整流器2机内温度}    获取web实时数据    整流器机内温度-2
    log    ${整流器2机内温度}
    ${整流器3机内温度}    获取web实时数据    整流器机内温度-3
    log    ${整流器3机内温度}
    should be true    (${整流器1机内温度}-3)<=${整流器2机内温度}<=(${整流器1机内温度}+3)
    should be true    (${整流器1机内温度}-3)<=${整流器3机内温度}<=(${整流器1机内温度}+3)
    should be true    (${整流器2机内温度}-3)<=${整流器3机内温度}<=(${整流器2机内温度}+3)
    [Teardown]

整流器1最大输出电流获取测试
    [Tags]
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    5m    1    设置web参数量    浮充电压    48
    sleep    2
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动浮充
    sleep    5
    Wait Until Keyword Succeeds    5m    1    信号量数据值大于    整流器输出电压-1    47.8
    Wait Until Keyword Succeeds    5m    1    信号量数据值小于    整流器输出电压-1    48.2
    ${整流器1最大输出电流}    获取web实时数据    整流器最大输出电流-1
    log    ${整流器1最大输出电流}
    should be true    62<=${整流器1最大输出电流}<=65
    设置web参数量    浮充电压    58
    Wait Until Keyword Succeeds    5m    1    信号量数据值大于    整流器输出电压-1    57.8
    ${整流器1最大输出电流}    获取web实时数据    整流器最大输出电流-1
    log    ${整流器1最大输出电流}
    should be true    51<=${整流器1最大输出电流}<=54
    [Teardown]

整流器2最大输出电流获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    设置web参数量    电池组容量_1    100
    设置web参数量    浮充电压    48
    sleep    2
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动浮充
    sleep    5
    Wait Until Keyword Succeeds    5m    1    信号量数据值大于    整流器输出电压-2    47.8
    Wait Until Keyword Succeeds    5m    1    信号量数据值小于    整流器输出电压-2    48.2
    ${整流器2最大输出电流}    获取web实时数据    整流器最大输出电流-2
    log    ${整流器2最大输出电流}
    should be true    62<=${整流器2最大输出电流}<=65
    设置web参数量    浮充电压    58
    Wait Until Keyword Succeeds    5m    1    信号量数据值大于    整流器输出电压-2    57.8
    ${整流器2最大输出电流}    获取web实时数据    整流器最大输出电流-2
    log    ${整流器2最大输出电流}
    should be true    51<=${整流器2最大输出电流}<=54
    [Teardown]

整流器1输入电压获取测试
    [Tags]
    [Setup]    测试用例前置条件
    关闭交流源输出
    设置电池模拟器模式    铅酸电池    24    5    50
    电池模拟器接触器分合    ON
    打开电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    连接CSU
    sleep    20
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1
    ${整流器1输入电压}    Wait Until Keyword Succeeds    2m    2    获取web实时数据    整流器输入电压-1
    log    ${整流器1输入电压}
    should be true    218<=${整流器1输入电压}<=222
    同时设置三相电压频率    102    50
    sleep    20
    ${整流器1输入电压}    获取web实时数据    整流器输入电压-1
    log    ${整流器1输入电压}
    should be true    100<=${整流器1输入电压}<=104
    关闭电池模拟器输出
    [Teardown]    同时设置三相电压频率    220    50

整流器2输入电压获取测试
    [Setup]    测试用例前置条件
    关闭交流源输出
    设置电池模拟器模式    铅酸电池    24    5    50
    电池模拟器接触器分合    ON
    打开电池模拟器输出
    同时设置三相电压频率    220    50
    打开交流源输出
    连接CSU
    sleep    20
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1
    ${整流器2输入电压}    Wait Until Keyword Succeeds    2m    2    获取web实时数据    整流器输入电压-2
    log    ${整流器2输入电压}
    should be true    218<=${整流器2输入电压}<=222
    同时设置三相电压频率    102    50
    sleep    20
    ${整流器2输入电压}    获取web实时数据    整流器输入电压-2
    log    ${整流器2输入电压}
    should be true    100<=${整流器2输入电压}<=104
    关闭电池模拟器输出
    [Teardown]    同时设置三相电压频率    220    50

整流器1输入电流获取测试
    [Documentation]    测试不通过：实际输入电流12.6A，显示输入电流1.27A，差距10倍；修改电池组容量为100
    [Tags]
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    2m    2    设置web参数量    浮充电压    53.5
    sleep    2
    设置web控制量    启动浮充
    sleep    5
    ${工作整流器数量}    获取web实时数据    工作整流器数量
    ${整流器最大输出电流}    获取web实时数据    整流器最大输出电流-1
    ${设置负载电流}    evaluate    ${工作整流器数量}*${整流器最大输出电流}*0.8
    ${设置负载电流}    evaluate    str(${设置负载电流})
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    ${设置负载电流}    5
    打开负载输出
    sleep    5
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1
    Wait Until Keyword Succeeds    5m    1    信号量数据值大于    电池电压-1    53.2
    Wait Until Keyword Succeeds    5m    1    信号量数据值大于    整流器输入电流-1    11
    ${整流器1输入电流}    获取web实时数据    整流器输入电流-1
    should be true    10.5<=${整流器1输入电流}<=13.5
    [Teardown]    关闭负载输出

整流器2输入电流获取测试
    [Documentation]    测试不通过：实际输入电流12.6A，显示输入电流1.27A，差距10倍；
    ...    修改电池组容量为100
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    2m    2    设置web参数量    浮充电压    53.5
    sleep    2
    设置web控制量    启动浮充
    sleep    5
    ${工作整流器数量}    获取web实时数据    工作整流器数量
    ${整流器最大输出电流}    获取web实时数据    整流器最大输出电流-2
    ${设置负载电流}    evaluate    ${工作整流器数量}*${整流器最大输出电流}*0.8
    ${设置负载电流}    evaluate    str(${设置负载电流})
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    ${设置负载电流}    5
    打开负载输出
    sleep    5
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1
    Wait Until Keyword Succeeds    5m    1    信号量数据值大于    电池电压-1    53.2
    Wait Until Keyword Succeeds    5m    1    信号量数据值大于    整流器输入电流-2    11
    ${整流器2输入电流}    获取web实时数据    整流器输入电流-2
    should be true    10.5<=${整流器2输入电流}<=13.5
    [Teardown]    关闭负载输出

整流器1输入频率获取测试
    [Tags]
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    同时设置三相电压频率    220    45
    Wait Until Keyword Succeeds    5m    2    信号量数据值小于    整流器输入频率-1    46    #为保证整流器数据获取正常
    ${整流器1输入频率}    Wait Until Keyword Succeeds    2m    2    获取web实时数据    整流器输入频率-1
    log    ${整流器1输入频率}
    should be true    44<=${整流器1输入频率}<=46
    同时设置三相电压频率    220    65
    Wait Until Keyword Succeeds    1m    2    信号量数据值大于    整流器输入频率-1    64    #为保证整流器数据获取正常
    ${整流器1输入频率}    获取web实时数据    整流器输入频率-1
    log    ${整流器1输入频率}
    should be true    64<=${整流器1输入频率}<=66
    [Teardown]    同时设置三相电压频率    220    50

整流器2输入频率获取测试
    [Tags]
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    同时设置三相电压频率    220    45
    Wait Until Keyword Succeeds    5m    2    信号量数据值小于    整流器输入频率-2    46    #为保证整流器数据获取正常
    ${整流器2输入频率}    Wait Until Keyword Succeeds    2m    2    获取web实时数据    整流器输入频率-2
    log    ${整流器2输入频率}
    should be true    44<=${整流器2输入频率}<=46
    同时设置三相电压频率    220    65
    Wait Until Keyword Succeeds    1m    2    信号量数据值大于    整流器输入频率-2    64    #为保证整流器数据获取正常
    ${整流器2输入频率}    获取web实时数据    整流器输入频率-2
    log    ${整流器2输入频率}
    should be true    64<=${整流器2输入频率}<=66
    [Teardown]    同时设置三相电压频率    220    50

整流器总输出电流获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    10    2    设置web参数量    浮充电压    53.5
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池组容量_1    1000
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1
    设置负载电压电流    53.5    10
    打开负载输出
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    整流器总输出电流    9.2    #为保证整流器数据获取正常
    ${整流器总输出电流}    Wait Until Keyword Succeeds    2m    2    获取web实时数据    整流器总输出电流
    should be true    9.2<=${整流器总输出电流}<=10.8
    ${工作整流器数量}    获取web实时数据    工作整流器数量
    ${整流器额定输出电流}    获取web实时数据    整流器最大输出电流-1
    ${电压}    获取web实时数据    直流电压
    ${设置负载电流}    evaluate    ${工作整流器数量}*${整流器额定输出电流}*0.6
    ${设置负载电流1}    evaluate    ${设置负载电流}+5    #负载有压降，大电流不准，因此补偿5A
    缓慢设置负载电压电流    ${电压}    ${设置负载电流1}    5
    ${整流器总输出电流判断点}    evaluate    ${设置负载电流}-3
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    整流器总输出电流    ${整流器总输出电流判断点}    #为保证整流器数据获取正常
    ${整流器总输出电流}    获取web实时数据    整流器总输出电流
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池组容量_1    100
    should be true    (${设置负载电流}-4)<=${整流器总输出电流}<=(${设置负载电流}+4)
    [Teardown]    关闭负载输出

整流器当前输出设定电压获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    10    2    设置web参数量    浮充电压    58
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1
    Wait Until Keyword Succeeds    5m    1    信号量数据值大于    整流器当前输出设定电压    57.8
    ${整流器当前输出设定电压}    获取web实时数据    整流器当前输出设定电压
    should be true    57.8<=${整流器当前输出设定电压}<=58.2
    Wait Until Keyword Succeeds    10    2    设置web参数量    浮充电压    48
    Wait Until Keyword Succeeds    5m    1    信号量数据值小于    整流器当前输出设定电压    48.2
    ${整流器当前输出设定电压}    获取web实时数据    整流器当前输出设定电压
    should be true    47.8<=${整流器当前输出设定电压}<=48.2
    [Teardown]

整流器设定限流点比率获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池组容量_1    500
    ${工作整流器数量}    获取web实时数据    工作整流器数量
    ${整流器设定限流点比率}    获取web实时数据    整流器设定限流点比率
    should be true    970<=${整流器设定限流点比率}<=1000
    [Teardown]

工作整流器数量获取测试
    [Tags]    view
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1
    sleep    5
    ${在线整流器数量}    获取web实时数据    在线整流器数量
    log    ${在线整流器数量}
    ${工作整流器数量}    获取web实时数据    工作整流器数量
    log    ${工作整流器数量}
    should be true    2<=${工作整流器数量}<=${在线整流器数量}
    [Teardown]

在线整流器数量获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1
    sleep    5
    ${在线整流器数量}    获取web实时数据    在线整流器数量
    log    ${在线整流器数量}
    ${工作整流器数量}    获取web实时数据    工作整流器数量
    log    ${工作整流器数量}
    should be true    2<=${工作整流器数量}<=${在线整流器数量}
    [Teardown]

交流节能状态获取测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Comment    Wait Until Keyword Succeeds    10m    2    设置web控制量    监控单元复位
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    1
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-2    否    #为保证整流器数据获取正常
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流节能模式    节能
    Wait Until Keyword Succeeds    10    2    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    2    设置web控制量    暂时非节能控制
    sleep    5
    ${交流节能状态}    获取web实时数据    交流节能状态
    log    ${交流节能状态}
    should be equal    '${交流节能状态}'    '暂时非节能'
    Wait Until Keyword Succeeds    10    2    设置web控制量    人工维护检测
    sleep    5
    ${交流节能状态}    获取web实时数据    交流节能状态
    log    ${交流节能状态}
    should be equal    '${交流节能状态}'    '人工维护检测'
    Wait Until Keyword Succeeds    10    2    设置web控制量    永久非节能控制
    sleep    5
    ${交流节能状态}    获取web实时数据    交流节能状态
    log    ${交流节能状态}
    should be equal    '${交流节能状态}'    '永久非节能'
    Wait Until Keyword Succeeds    10    2    设置web控制量    自动节能控制
    sleep    5
    ${交流节能状态}    获取web实时数据    交流节能状态
    log    ${交流节能状态}
    should be equal    '${交流节能状态}'    '自动非节能'
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    10    2    设置web参数量    整流器最小开机数量    3
    [Teardown]
