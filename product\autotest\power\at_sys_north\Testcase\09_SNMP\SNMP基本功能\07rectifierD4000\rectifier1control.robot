*** Settings ***
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp_test关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/Web关键字_V30.robot
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
snmp_0002_rectifier1control_sMRSleep1√
    [Documentation]    交流节能模式 默认为 安全
    ...
    ...    0:安全/Safe;1:节能/Save;2:自由/Free
    连接CSU
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    10    2    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流节能模式    自由
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    #只休眠n-1个整流器，全部休眠会掉电，需要手动恢复
    ${测试环境真实整流器地址}    Create List    1    2    3
    ${工作整流器地址}    Set Variable    ${测试环境真实整流器地址}
    FOR    ${整流器序号}    IN    @{工作整流器地址[:-1]}
        Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-${整流器序号}    否    #为保证整流器数据获取正常
        sleep    5
        ${设置控制量}    snmp控制量循环体    sMRSleep${整流器序号}value    # totalAlarmDisablevalue
        should be true    ${设置控制量}
        sleep    5
        ${整流器1休眠状态}    获取web实时数据    整流器休眠状态-${整流器序号}
        should be equal    '${整流器1休眠状态}'    '是'
        ${snmp该参数值}    获取SNMP数据_单个    sMRSleepStatus${整流器序号}value
        should be equal    ${snmp该参数值}    1
        Comment    增加判断准确性
        ${存在结果}    判断web历史记录存在snmp控制内容    整流器    整流器休眠    300    ${整流器序号}
        should be true    ${存在结果}
        sleep    5
        ${设置控制量}    snmp控制量循环体    sMRWaken${整流器序号}value    # totalAlarmDisablevalue
        should be true    ${设置控制量}
        sleep    5
        ${整流器1休眠状态}    获取web实时数据    整流器休眠状态-${整流器序号}
        should be equal    '${整流器1休眠状态}'    '否'
        ${snmp该参数值}    获取SNMP数据_单个    sMRSleepStatus${整流器序号}value
        should be equal    ${snmp该参数值}    0
        Comment    增加判断准确性
        ${存在结果}    判断web历史记录存在snmp控制内容    整流器    整流器唤醒    300    ${整流器序号}
        should be true    ${存在结果}
    END
    断开连接SNMP
    [Teardown]    run keywords    连接CSU
    ...    AND    Wait Until Keyword Succeeds    30    1    设置web参数量    交流节能模式    安全

snmp_0004_rectifier1control_sMRWaken1√
    连接CSU
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流节能模式    安全
    Wait Until Keyword Succeeds    10    2    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流节能模式    自由
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ##标准自动化测试环境应该是接入三个实际整流器
    ${测试环境真实整流器地址}    Create List    1    2    3
    ${工作整流器地址}    Set Variable    ${测试环境真实整流器地址}
    FOR    ${整流器序号}    IN    @{工作整流器地址}
        Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-${整流器序号}    否    #为保证整流器数据获取正常
        sleep    5
        ${设置控制量}    snmp控制量循环体    sMRWaken${整流器序号}value    # totalAlarmDisablevalue
        should be true    ${设置控制量}
        sleep    5
        ${整流器1休眠状态}    获取web实时数据    整流器休眠状态-${整流器序号}
        should be equal    '${整流器1休眠状态}'    '否'
        ${snmp该参数值}    获取SNMP数据_单个    sMRSleepStatus${整流器序号}value
        should be equal    ${snmp该参数值}    0
        Comment    增加判断准确性
        ${存在结果}    判断web历史记录存在snmp控制内容    整流器    整流器唤醒    300    ${整流器序号}
        should be true    ${存在结果}
    END
    断开连接SNMP

snmp_0006_rectifier1control_sMRFanControlDisable1√
    [Documentation]    0:自动/Auto;1:全速/Full Speed
    连接CSU
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流节能模式    自由
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${测试环境真实整流器地址}    Create List    1    2    3
    FOR    ${整流器序号}    IN    @{测试环境真实整流器地址}
        Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-${整流器序号}    否    #为保证整流器数据获取正常
        sleep    5
        ${设置控制量}    snmp控制量循环体    sMRFanControlDisable${整流器序号}value    # totalAlarmDisablevalue
        should be true    ${设置控制量}
        Wait Until Keyword Succeeds    2m    2    信号量数据值为    整流器风扇控制状态-1    全速
        Sleep    30
        ${snmp该参数值}    获取SNMP数据_单个    sMRFanControlState${整流器序号}value
        should be equal    ${snmp该参数值}    1
        ${存在结果}    判断web历史记录存在snmp控制内容    整流器    整流器风扇调速禁止    300    ${整流器序号}
        should be true    ${存在结果}
    END
    断开连接SNMP

snmp_0008_rectifier1control_sMRFanControlEnable1√
    连接CSU
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流节能模式    自由
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${测试环境真实整流器地址}    Create List    1    2    3
    FOR    ${整流器序号}    IN    @{测试环境真实整流器地址}
        Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-${整流器序号}    否    #为保证整流器数据获取正常
        sleep    5
        ${设置控制量}    snmp控制量循环体    sMRFanControlEnable${整流器序号}value    # totalAlarmDisablevalue
        should be true    ${设置控制量}
        Wait Until Keyword Succeeds    2m    2    信号量数据值为    整流器风扇控制状态-1    自动
        Sleep    30
        ${snmp该参数值}    获取SNMP数据_单个    sMRFanControlState${整流器序号}value
        should be equal    ${snmp该参数值}    0
        ${存在结果}    判断web历史记录存在snmp控制内容    整流器    整流器风扇调速允许    300    ${整流器序号}
        should be true    ${存在结果}
    END
    断开连接SNMP

snmp_0010_rectifier1control_sMRRestart1X
    [Documentation]    整流器复位 在web中默认没有显示，需要先进行显示属性配置
    ...
    ...    整流器暂时无法自动复位，不测
    [Tags]    3
    连接CSU
    Comment    连接SNMP_V2C    ${g_community}
    显示属性配置    整流器复位    控制量    web_attr=On    gui_attr=On
    进行SNMP_V2/V3连接    ${snmp连接方式}
    FOR    ${整流器序号}    IN    @{测试环境真实整流器地址}
        ${设置控制量}    snmp控制量循环体    sMRRestart${整流器序号}value
        should be true    ${设置控制量}
        sleep    1m
        ${存在结果}    判断web历史记录存在snmp控制内容    整流器    整流器复位    360    ${整流器序号}
        should be true    ${存在结果}
    END
    断开连接SNMP
    显示属性配置    整流器复位    控制量    web_attr=Off    gui_attr=Off

snmp_0012_rectifier1control_sMRCommunicationFailAlarmClear1√
    连接CSU
    Comment    Wait Until Keyword Succeeds    10    2    设置web参数量    交流节能模式    自由
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${测试环境真实整流器地址}    Create List    1    2    3
    FOR    ${整流器序号}    IN    @{测试环境真实整流器地址}
        ${设置控制量}    snmp控制量循环体    sMRCommunicationFailAlarmClear${整流器序号}value
        should be true    ${设置控制量}
        sleep    5
        ${存在结果}    判断web历史记录存在snmp控制内容    整流器    整流器通讯中断告警清除    300    ${整流器序号}
        should be true    ${存在结果}
    END
    断开连接SNMP

