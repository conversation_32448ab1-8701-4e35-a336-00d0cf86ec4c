*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
1363批量获取SSW数字量
    [Documentation]    ${协议类型} | ${子设备名称} | ${信号名称} | ${命令名称} | ${参数值} | ${文档名称} | ${power_sm命令名称} | ${节点名} | ${节点序号} | ${查询SID序号}=2 | ${1104/1363表单名称}=None | ${1104/1363命令名称}=None | ${1104/1363数据名称}=None | ${屏号}=None
    写入CSV文档    SSW数字量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=digital data    INDEX_DEVICE_CH=智能空开
    ${排除列表}    create list
    P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SSW排除数字量信号}    ${排除列表}    1
    ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取智能空开开关量~CID1=D3H>>    <<获取智能空开开关量~CID2=43H>>    None    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${1104待测}    1104_获取1104/1363指定设备指定信号量    ${协议数据}    ${web数据}    ${列表1}
    ${缺省值列表1}    获取缺省值列表    ${1104待测}    1    1363
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    1363    DMU_IntelAirSwit    呼叫    ${缺省值列表1}    SSW数字量获取测试    null    <<获取智能空开开关量~CID1=D3H>>    <<获取智能空开开关量~CID2=43H>>    None
    ${缺省值列表2}    获取缺省值列表    ${1104待测}    2    1363
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    1363    DMU_IntelAirSwit    呼叫    ${缺省值列表2}    SSW数字量获取测试    null    <<获取智能空开开关量~CID1=D3H>>    <<获取智能空开开关量~CID2=43H>>    None
    ${缺省值列表0}    获取缺省值列表    ${1104待测}    0    1363
    # ${缺省值列表0}    1104检查缺省值列表    ${缺省值列表0}    ${缺省值列表1}    ${缺省值列表2} 
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    1363    DMU_IntelAirSwit    呼叫    ${缺省值列表0}    SSW数字量获取测试    null    <<获取智能空开开关量~CID1=D3H>>    <<获取智能空开开关量~CID2=43H>>    None
