*** Settings ***
Suite Teardown    run keywords    设置web设备参数量为默认值    烟雾告警
...               AND    模拟数字量告警    烟雾告警    OFF
Default Tags      T1-1
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
snmp_0002_烟雾告警√
    [Tags]    T1-1
    [Setup]
    实时告警刷新完成
    ${级别设置值}    获取web参数量    烟雾告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    烟雾告警    主要
    wait until keyword succeeds    10m    1    判断告警不存在    烟雾告警
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    模拟数字量告警    烟雾告警    ON
    sleep    10
    ${烟雾状态}    获取干接点状态    ${plat.smoke}
    should be true    ${烟雾状态}==1    \    #0为正常（闭合），1为断开（断开）
    wait until keyword succeeds    10m    1    查询指定告警信息    烟雾告警
    ${snmp英文名}    获取snmp单个告警英文名    烟雾告警
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    模拟数字量告警    烟雾告警    OFF
    sleep    10
    Wait Until Keyword Succeeds    12m    1    判断告警不存在    烟雾告警
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    run keywords    设置web设备参数量为默认值    烟雾告警
    ...    AND    模拟数字量告警    烟雾告警    OFF

snmp_0004_水淹告警√
    [Tags]    T1-1
    [Setup]
    实时告警刷新完成
    ${级别设置值}    获取web参数量    水淹告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    水淹告警    主要
    wait until keyword succeeds    1m    1    判断告警不存在    水淹告警
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    模拟数字量告警    水淹告警    ON
    sleep    10
    ${烟雾状态}    获取干接点状态    ${plat.Water}
    should be true    ${烟雾状态}==1    \    #0为正常（闭合），1为断开（断开）
    wait until keyword succeeds    1m    1    查询指定告警信息    水淹告警
    ${snmp英文名}    获取snmp单个告警英文名    水淹告警
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    模拟数字量告警    水淹告警    OFF
    sleep    10
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    水淹告警
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    run keywords    设置web设备参数量为默认值    水淹告警
    ...    AND    模拟数字量告警    水淹告警    OFF

snmp_0006_门磁告警√
    [Tags]    T1-1
    [Setup]
    实时告警刷新完成
    ${级别设置值}    获取web参数量    门磁告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    门磁告警    主要
    wait until keyword succeeds    1m    1    判断告警不存在    门磁告警
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    模拟数字量告警    门磁告警    ON
    sleep    10
    ${烟雾状态}    获取干接点状态    ${plat.MagneticDoor}
    should be true    ${烟雾状态}==1    \    #0为正常（闭合），1为断开（断开）
    wait until keyword succeeds    1m    1    查询指定告警信息    门磁告警
    ${snmp英文名}    获取snmp单个告警英文名    门磁告警
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    模拟数字量告警    门磁告警    OFF
    sleep    10
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    门磁告警
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    run keywords    设置web设备参数量为默认值    门磁告警
    ...    AND    模拟数字量告警    门磁告警    OFF

snmp_0008_门禁告警_无
    [Tags]    3
    Log    不支持模拟此告警信息
    Log    暂不自动化

snmp_0010_环境温度无效_无
    [Tags]    3
    Log    不支持模拟此告警信息
    Log    暂不自动化

snmp_0012_环境温度高√
    [Documentation]    高阈值范围：30-60，默认55；过高阈值范围：33-63，默认58。高比过高低3°
    [Setup]    测试用例前置条件
    实时告警刷新完成
    #获取告警级别
    ${级别设置值}    获取web参数量    环境温度高
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    环境温度高    次要
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    环境温度高
    Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度过高阈值    58    #默认58，min33
    Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度高阈值    30    #默认55，min30
    ${环境温度}    Wait Until Keyword Succeeds    5m    1    获取web实时数据    环境温度
    FOR    ${调节步长}    IN RANGE    2    40    2
        ${零点}    evaluate    str(${调节步长})
        Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    ${零点}    1    系统运行环境    环境温度
        Comment    Wait Until Keyword Succeeds    30    1    通道配置_AI    ${plat.envtemp}    ${零点}    1    系统运行环境    环境温度
        sleep    3
        ${环境温度获取}    获取web实时数据    环境温度
        ${环境温度设置值}    获取web参数量    环境温度高阈值
        exit for loop if    ${环境温度获取}>${ 环境温度设置值}
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度高    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    环境温度高
    ${环境温度高告警}    判断告警存在_带返回值    环境温度高
    should not be true    ${环境温度高告警}
    ###以上告警已产生，先屏蔽
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    wait until keyword succeeds    30    1    设置web参数量    环境温度高    次要
    wait until keyword succeeds    3m    2    判断告警存在    环境温度高
    #产生
    ${snmp英文名}    获取snmp单个告警英文名    环境温度高
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    #无告警后查询判断
    环境温度参数恢复默认值
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    环境温度高
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    Run keywords    环境温度参数恢复默认值
    ...    AND    设置web参数量    环境温度高    次要
    ...    AND    设置通道配置    ${plat.envtemp}    0    1    系统运行环境    环境温度

snmp_0014_环境温度低√
    [Documentation]    低阈值范围：-27~23，默认-5；过低阈值范围：-30~20，默认-8
    [Setup]    测试用例前置条件
    实时告警刷新完成
    #获取告警级别
    ${级别设置值}    获取web参数量    环境温度低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    环境温度低    次要
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    环境温度低
    ${可设置范围}    获取web参数可设置范围    环境温度低阈值
    设置web参数量    环境温度低阈值    ${可设置范围}[1]
    ${环境温度}    Wait Until Keyword Succeeds    5m    1    获取web实时数据    环境温度
    FOR    ${调节步长}    IN RANGE    -5    -50    -5
        ${零点}    evaluate    str(${调节步长})
        Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    ${零点}    1    系统运行环境    环境温度
        sleep    3
        ${环境温度获取}    获取web实时数据    环境温度
        ${环境温度设置值}    获取web参数量    环境温度低阈值
        exit for loop if    ${环境温度获取}<${ 环境温度设置值}
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度低    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    环境温度低
    ${环境温度低告警}    判断告警存在_带返回值    环境温度低
    should not be true    ${环境温度低告警}
    ###以上告警已产生，先屏蔽
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    wait until keyword succeeds    30    1    设置web参数量    环境温度低    次要
    wait until keyword succeeds    3m    2    判断告警存在    环境温度低
    #产生
    ${snmp英文名}    获取snmp单个告警英文名    环境温度低
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    #无告警后查询判断
    环境温度参数恢复默认值
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    环境温度低
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    Run keywords    环境温度参数恢复默认值
    ...    AND    设置web参数量    环境温度低    次要
    ...    AND    设置通道配置    ${plat.envtemp}    0    1    系统运行环境    环境温度

snmp_0016_环境湿度无效√
    [Documentation]    默认级别 屏蔽
    ...
    ...    系统一直存在此告警，需要先屏蔽后设置为非屏蔽级别
    ...    以产生告警信息变化
    [Setup]    测试用例前置条件
    #不接入传感器
    设置通道无效值/恢复通道原始值    ${plat.humity}    1
    sleep    10
    实时告警刷新完成
    #系统7应该存在此告警
    Comment    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    环境湿度无效
    #屏蔽
    设置web参数量    环境湿度无效    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    环境湿度无效
    ${环境湿度无效告警}    判断告警存在_带返回值    环境湿度无效
    should not be true    ${环境湿度无效告警}
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    #产生告警，并判断
    Wait Until Keyword Succeeds    30    1    设置web参数量    环境湿度无效    次要
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    环境湿度无效
    sleep    30
    #产生
    ${snmp英文名}    获取snmp单个告警英文名    环境湿度无效
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    Wait Until Keyword Succeeds    5m    2    设置web参数量    环境湿度无效    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    环境湿度无效
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    设置通道无效值/恢复通道原始值    ${plat.humity}    0
    [Teardown]    Wait Until Keyword Succeeds    30    1    设置web参数量    环境湿度无效    屏蔽

snmp_0018_环境湿度高_无
    [Tags]    3
    Log    不支持模拟此告警信息
    Log    暂不自动化

snmp_0020_环境湿度低_无
    [Tags]    3
    Log    不支持模拟此告警信息
    Log    暂不自动化

snmp_0022_环境温度过高√
    [Documentation]    高阈值范围：30-60，默认55；过高阈值范围：33-63，默认58。高比过高低3°
    [Setup]    测试用例前置条件
    实时告警刷新完成
    #获取告警级别
    ${级别设置值}    获取web参数量    环境温度过高
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    环境温度过高    次要
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    环境温度过高
    ${可设置范围}    获取web参数可设置范围    环境温度高阈值
    设置web参数量    环境温度高阈值    ${可设置范围}[0]
    ${可设置范围}    获取web参数可设置范围    环境温度过高阈值
    设置web参数量    环境温度过高阈值    ${可设置范围}[0]
    ${环境温度}    Wait Until Keyword Succeeds    10    1    获取web实时数据    环境温度
    FOR    ${调节步长}    IN RANGE    2    40    2
        ${零点}    evaluate    str(${调节步长})
        Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    ${零点}    1    系统运行环境    环境温度
        Comment    Wait Until Keyword Succeeds    30    1    通道配置_AI    ${plat.envtemp}    ${零点}    1    系统运行环境    环境温度
        sleep    3
        ${环境温度获取}    获取web实时数据    环境温度
        ${环境温度设置值}    获取web参数量    环境温度过高阈值
        exit for loop if    ${环境温度获取}>${ 环境温度设置值}
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度过高    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    环境温度过高
    ${环境温度高告警}    判断告警存在_带返回值    环境温度过高
    should not be true    ${环境温度高告警}
    ###以上告警已产生，先屏蔽
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    wait until keyword succeeds    30    1    设置web参数量    环境温度过高    次要
    wait until keyword succeeds    3m    2    判断告警存在    环境温度过高
    #产生
    ${snmp英文名}    获取snmp单个告警英文名    环境温度过高
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    #无告警后查询判断
    环境温度参数恢复默认值
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    环境温度过高
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    Run keywords    环境温度参数恢复默认值
    ...    AND    设置通道配置    ${plat.envtemp}    0    1    系统运行环境    环境温度

snmp_0024_环境温度过低√
    [Documentation]    低阈值范围：-27~23，默认-5；过低阈值范围：-30~20，默认-8
    [Setup]    测试用例前置条件
    实时告警刷新完成
    #获取告警级别
    ${级别设置值}    获取web参数量    环境温度过低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    环境温度过低    次要
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    环境温度过低
    ${可设置范围}    获取web参数可设置范围    环境温度低阈值
    设置web参数量    环境温度低阈值    ${可设置范围}[1]
    ${可设置范围}    获取web参数可设置范围    环境温度过低阈值
    设置web参数量    环境温度过低阈值    ${可设置范围}[1]
    ${环境温度}    Wait Until Keyword Succeeds    5m    1    获取web实时数据    环境温度
    FOR    ${调节步长}    IN RANGE    -2    -50    -2
        ${零点}    evaluate    str(${调节步长})
        Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    ${零点}    1    系统运行环境    环境温度
        sleep    3
        ${环境温度获取}    获取web实时数据    环境温度
        ${环境温度设置值}    获取web参数量    环境温度过低阈值
        exit for loop if    ${环境温度获取}<${ 环境温度设置值}
    END
    设置web参数量    环境温度过低    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    环境温度过低
    ${环境温度过低告警}    判断告警存在_带返回值    环境温度过低
    should not be true    ${环境温度过低告警}
    ###以上告警已产生，先屏蔽
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    wait until keyword succeeds    30    1    设置web参数量    环境温度过低    次要
    wait until keyword succeeds    3m    2    判断告警存在    环境温度过低
    #产生
    ${snmp英文名}    获取snmp单个告警英文名    环境温度过低
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    #无告警后查询判断
    环境温度参数恢复默认值
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    环境温度过低
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    Run keywords    环境温度参数恢复默认值
    ...    AND    设置通道配置    ${plat.envtemp}    0    1    系统运行环境    环境温度

snmp_0026_温控单元异常
    连接CSU
    ${级别设置值}    获取web参数量    温控单元异常
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    温控单元异常    主要
    Wait Until Keyword Succeeds    1m    1    判断告警不存在    温控单元异常
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay4Status}    系统运行环境    温控单元异常    断开
    wait until keyword succeeds    1m    1    查询指定告警信息    温控单元异常
    ${snmp英文名}    获取snmp单个告警英文名    温控单元异常
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay4Status}    系统运行环境    温控单元异常    闭合
    Wait Until Keyword Succeeds    3m    1    判断告警不存在    温控单元异常
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    Run keywords    设置web设备参数量为默认值    温控单元异常
    ...    AND    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay4Status}    系统运行环境    温控单元异常    闭合

snmp_0028_直流空调异常_无
    [Tags]    3
    Log    不支持模拟此告警信息
    Log    暂不自动化

snmp_0030_加热器异常_无
    [Tags]    3
    Log    不支持模拟此告警信息
    Log    暂不自动化
