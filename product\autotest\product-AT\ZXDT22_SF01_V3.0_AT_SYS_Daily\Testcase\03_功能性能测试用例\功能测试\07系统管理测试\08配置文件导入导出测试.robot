*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
配置文件导入导出测试
    [Setup]    测试用例前置条件
    连接CSU
    导入参数文件    all_para.zip
    sleep    90
    连接CSU
    ${配置参数文件}    导出参数文件
    sleep    5
    ${channel_config}    通道配置_AI    UIB_X4_T1    0.0000    3.0000    单组电池_1    电池温度
    should be true    ${channel_config} == True
    sleep    10
    ${电池温度}    获取web模拟量    单组电池_1    电池温度
    should be true    ${电池温度}>40.0
    导入参数文件    ${配置参数文件}
    sleep    90
    连接CSU
    sleep    10
    ${电池温度}    获取web模拟量    单组电池_1    电池温度
    should be true    ${电池温度}<40.0
    Wait Until Keyword Succeeds    1m    2    设置web参数量    交流配电    交流防雷检测    值=0
    sleep    2
    ${交流防雷检测获取}    获取web参数量    交流配电    交流防雷检测
    should be true    ${交流防雷检测获取}==0
    设置web参数量    系统运行环境    温度传感器类型    值=0
    ${传感器设置类型}    获取web参数量    系统运行环境    温度传感器类型
    should be true    ${传感器设置类型}==0
    Wait Until Keyword Succeeds    5m    1    设置web参数量    直流配电    负载一次下电配置    值=0
    ${一次下电配置}    获取web参数量    直流配电    负载一次下电配置
    should be true    ${一次下电配置}==0
    Wait Until Keyword Succeeds    1m    2    设置web参数量    单组电池_1    分流器检测配置[1]    值=0
    sleep    2
    ${分流器检测配置[1]获取}    获取web参数量    单组电池_1    分流器检测配置[1]
    should be true    ${分流器检测配置[1]获取}==0
    导入参数文件    ${出厂参数文件}
    sleep    90
    连接CSU
    ${能源系统名称获取值}    获取web参数量    能源系统    能源系统名称
    should be true    '${能源系统名称获取值}'=='ZXDT22 SF01(V3.0) DC POWER'
    ${交流防雷检测获取}    获取web参数量    交流配电    交流防雷检测
    should be true    ${交流防雷检测获取}==1
    ${传感器设置类型}    获取web参数量    系统运行环境    温度传感器类型
    should be true    ${传感器设置类型}==1
    ${一次下电配置}    获取web参数量    直流配电    负载一次下电配置
    should be true    ${一次下电配置}==1
    ${分流器检测配置[1]获取}    获取web参数量    单组电池_1    分流器检测配置[1]
    should be true    ${分流器检测配置[1]获取}==1
    设置web参数量    能源系统    能源系统名称    值=ZXDT22 SF01(V3.0) DC POWER1
    ${能源系统名称获取值}    获取web参数量    能源系统    能源系统名称
    should be true    '${能源系统名称获取值}'=='ZXDT22 SF01(V3.0) DC POWER1'
