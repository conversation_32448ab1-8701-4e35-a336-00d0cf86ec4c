*** Settings ***
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp_test关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/Web关键字_V30.robot
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
snmp_0002_liquidLevelSensor1alarm_level√
    FOR    ${液位传感器序号}    IN RANGE    1    2
        ${比较结果}    对比告警级别_V2C    liquidLevelSensor${液位传感器序号}alarm
        should be true    ${比较结果}
    END

snmp_0004_liquidLevelSensor1alarm_level_write√
    FOR    ${液位传感器序号}    IN RANGE    1    2
        ${比较结果}    批量设置四种/五种告警级别    liquidLevelSensor${液位传感器序号}alarm
        should be true    ${比较结果}
        Comment    ${比较结果}    批量设置四种/五种告警级别    liquidLevelSensor1alarm
        Comment    should be true    ${比较结果}
    END

snmp_0006_liquidLevelSensor1alarm_relayl√
    FOR    ${液位传感器序号}    IN RANGE    1    2
        ${比较结果}    对比告警干接点_V2C    liquidLevelSensor${液位传感器序号}alarm
        should be true    ${比较结果}
    END

snmp_0008_liquidLevelSensor1alarm_relay_writel√
    FOR    ${液位传感器序号}    IN RANGE    1    2
        ${比较结果}    批量修改告警干接点_V2C    liquidLevelSensor${液位传感器序号}alarm
        should be true    ${比较结果}
    END
