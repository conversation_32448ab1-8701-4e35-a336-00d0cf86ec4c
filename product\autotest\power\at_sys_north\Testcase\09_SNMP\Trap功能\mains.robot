*** Settings ***
Suite Setup       测试用例前置条件
Suite Teardown    测试用例后置条件
Resource          ../../../测试用例关键字.robot


*** Test Cases ***
snmp_0002_市电频率高_无
    [Tags]    3

snmp_0004_市电相电压不平衡_无
    [Tags]    3

snmp_0006_市电停电
    [Documentation]    纯市电场景，交流停电后，CSU告警市电停电，不会告警交流停电
    [Tags]    T1-1
    重置电池模拟器输出
    #1
    打开负载输出

    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    #2
    设置直流源模式      53.5      1       53.5
    设置负载电压电流        53.5      1 

    ${级别设置值}    获取web参数量    市电停电
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    市电停电    主要
    Wait Until Keyword Succeeds    10m    1    查询指定告警信息不为    停电
    Wait Until Keyword Succeeds    10m    1    信号量数据值为    交流供电状态    市电1
    关闭交流源输出
    #3
    设置直流源模式      48      1       48
    设置负载电压电流        48      1 

    Wait Until Keyword Succeeds    10m    2    设置web参数量    市电停电    屏蔽    #默认级别：2
    sleep    3
    Wait Until Keyword Succeeds    10m    1    判断告警不存在    市电停电
    ###以上告警已产生，先屏蔽
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    Wait Until Keyword Succeeds    30    1    设置web参数量    市电停电    主要
    Wait Until Keyword Succeeds    10m    2    查询指定告警信息    市电停电
    #产生
    ${snmp英文名}    获取snmp单个告警英文名    市电停电_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    打开交流源输出
    Wait Until Keyword Succeeds    10m    1    判断告警不存在    市电停电
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    打开交流源输出

snmp_0008_市电电压过压_无
    [Tags]    3

snmp_0010_市电电压欠压_无
    [Tags]    3

snmp_0012_市电相电流高_无
    [Tags]    3

snmp_0014_市电频率低_无
    [Tags]    3
