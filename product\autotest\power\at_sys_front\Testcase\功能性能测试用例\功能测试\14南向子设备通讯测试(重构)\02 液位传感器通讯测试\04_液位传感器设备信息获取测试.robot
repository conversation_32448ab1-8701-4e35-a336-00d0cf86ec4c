*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
液位设备信息获取测试
    连接CSU
    设置子工具值    llSensor    all    只读    厂家信息    0000000KAN1100000000V1.00
    Wait Until Keyword Succeeds    5m    1    设备信息获取值    液位传感器系统名称-1     KAN110
    Wait Until Keyword Succeeds    5m    1    设备信息获取值    液位传感器软件版本-1     V1.00
    # sleep    40
    # ${名称1}    获取web实时数据    液位传感器系统名称-1
    # Comment    ${名称2}    获取web实时数据    液位传感器系统名称-2
    # ${名称3}    获取web实时数据    液位传感器软件版本-1
    # Comment    ${名称4}    获取web实时数据    液位传感器软件版本-2
    # should be equal    '${名称1}'    'KAN110'
    # Comment    should be equal    '${名称2}'    'KAN110'
    # should be equal    '${名称3}'    'V1.00'
    # Comment    should be equal    '${名称4}'    'V1.00'
    设置子工具值    llSensor    all    只读    厂家信息    0000000ZHANGH0000000V7.28
    Wait Until Keyword Succeeds    5m    1    设备信息获取值    液位传感器系统名称-1     ZHANGH
    Wait Until Keyword Succeeds    5m    1    设备信息获取值    液位传感器软件版本-1     V7.28
    # sleep    40
    # ${名称1}    获取web实时数据    液位传感器系统名称-1
    # Comment    ${名称2}    获取web实时数据    液位传感器系统名称-2
    # ${名称3}    获取web实时数据    液位传感器软件版本-1
    # Comment    ${名称4}    获取web实时数据    液位传感器软件版本-2
    # should be equal    '${名称1}'    'ZHANGH'
    # Comment    should be equal    '${名称2}'    'ZHANGH'
    # should be equal    '${名称3}'    'V7.28'
    # Comment    should be equal    '${名称4}'    'V7.28'
    设置子工具值    llSensor    all    只读    厂家信息    0000000KANGYU0000000V1.01
    Wait Until Keyword Succeeds    5m    1    设备信息获取值    液位传感器系统名称-1     KANGYU
    Wait Until Keyword Succeeds    5m    1    设备信息获取值    液位传感器软件版本-1     V1.01
    # sleep    40
    # ${名称1}    获取web实时数据    液位传感器系统名称-1
    # Comment    ${名称2}    获取web实时数据    液位传感器系统名称-2
    # ${名称3}    获取web实时数据    液位传感器软件版本-1
    # Comment    ${名称4}    获取web实时数据    液位传感器软件版本-2
    # should be equal    '${名称1}'    'KANGYU'
    # Comment    should be equal    '${名称2}'    'KANGYU'
    # should be equal    '${名称3}'    'V1.01'
    # Comment    should be equal    '${名称4}'    'V1.01'
