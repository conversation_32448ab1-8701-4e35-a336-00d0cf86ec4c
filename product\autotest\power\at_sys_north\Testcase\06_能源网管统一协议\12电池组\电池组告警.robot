*** Settings ***
Suite Setup       Run Keywords    主动告警测试前置条件    ${CSU_role}
...   AND  测试用例前置条件
Suite Teardown    Run Keywords    设置web设备参数量为默认值    CSU主动告警使能
...   AND  测试用例后置条件
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0002_一次下电告警
    [Documentation]    一次下电告警不能设置为屏蔽级别，因此注释掉设置告警级别为0的语句。
    [Tags]    T1-1
    [Setup]    Run keywords    重置电池模拟器输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    同时设置三相电压频率    220    50
    打开交流源输出
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息不为    一次下电告警
    关闭交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    一次下电告警
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
        设置web参数量    电池组容量_${i}    0
    END
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    99
    sleep    2
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    30    1    设置web参数量    下电模式    电池电压    #默认1电池电压
    Wait Until Keyword Succeeds    30    1    设置web参数量    下电控制延时    0    #默认0
    Wait Until Keyword Succeeds    30    1    设置web参数量    负载一次下电使能    允许    #默认1允许
    Wait Until Keyword Succeeds    30    1    设置web参数量    负载二次下电使能    禁止    #默认1允许
    显示属性配置    一次下电控制状态    数字量    web_attr=On    gui_attr=On
    ${恢复时间可设置范围}    获取web参数可设置范围    一次下电恢复时间
    run keyword and ignore error    设置web参数量    一次下电恢复时间    ${恢复时间可设置范围}[0]
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${可设置范围}[1]
    ${电压低可设置范围}    获取web参数可设置范围    电池电压低阈值
    设置web参数量    电池电压低阈值    ${电压低可设置范围}[1]
    ${电池下电电压可设置范围}    获取web参数可设置范围    负载一次下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载一次下电电压    ${电池下电电压可设置范围}[1]
    设置负载电压电流    53.5    10
    打开负载输出
    ${一次下电电压设置值}    获取web参数量    负载一次下电电压
    ${一次下电电压}    evaluate    ${一次下电电压设置值}-0.5
    向下调节电池电压    ${一次下电电压}
    Wait Until Keyword Succeeds    8m    1    信号量数据值为    一次下电控制状态    动作
    ${告警级别取值约定dict}    获取web参数的取值约定    一次下电告警
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    一次下电告警    ${告警级别设置}
        sleep   5
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    电池组    一次下电告警
        wait until keyword succeeds    1m    1    查询指定告警信息    一次下电告警
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    一次下电告警    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    一次下电告警    严重    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    向上调节电池电压    53.5
    关闭负载输出
    打开交流源输出
    Wait Until Keyword Succeeds    15m    1    信号量数据值为    一次下电控制状态    恢复
    Wait Until Keyword Succeeds    8m    2    查询指定告警信息不为    一次下电告警
    显示属性配置    一次下电控制状态    数字量    web_attr=Off    gui_attr=Off
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    一次下电告警    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    一次下电告警    负载一次下电电压    测试终止电压    电池电压低阈值    一次下电恢复时间
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出
    ...    AND    重置电池模拟器输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0003_二次下电告警
    [Documentation]    二次下电告警不能设置为屏蔽级别，因此注释掉设置告警级别为0的语句。
    [Tags]    T1-1
    [Setup]    Run keywords    重置电池模拟器输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    5
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息不为    二次下电告警
    关闭交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    二次下电告警
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
        设置web参数量    电池组容量_${i}    0
    END
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    99
    sleep    2
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    100
    wait until keyword succeeds    30    1    设置web参数量    下电模式    电池电压    #默认1电池电压
    wait until keyword succeeds    30    1    设置web参数量    下电控制延时    0    #默认0
    wait until keyword succeeds    30    1    设置web参数量    负载一次下电使能    禁止    #默认1允许
    wait until keyword succeeds    30    1    设置web参数量    负载二次下电使能    允许    #默认1允许
    显示属性配置    二次下电控制状态    数字量    web_attr=On    gui_attr=On
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${可设置范围}[1]
    ${电压低可设置范围}    获取web参数可设置范围    电池电压低阈值
    设置web参数量    电池电压低阈值    ${电压低可设置范围}[1]
    ${恢复时间可设置范围}    获取web参数可设置范围    二次下电恢复时间
    run keyword and ignore error    设置web参数量    二次下电恢复时间    ${恢复时间可设置范围}[0]
    ${二次下电电压可设置范围}    获取web参数可设置范围    负载二次下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电电压    ${二次下电电压可设置范围}[1]
    设置负载电压电流    53.5    10
    打开负载输出
    ${下电电压设置值}    获取web参数量    负载二次下电电压
    ${下电电压}    evaluate    ${下电电压设置值}-0.5
    向下调节电池电压    ${下电电压}
    Wait Until Keyword Succeeds    8m    1    信号量数据值为    二次下电控制状态    动作
    ${告警级别取值约定dict}    获取web参数的取值约定    二次下电告警
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    二次下电告警    ${告警级别设置}
        sleep   5
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    电池组    二次下电告警
        wait until keyword succeeds    1m    1    查询指定告警信息    二次下电告警
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    二次下电告警    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    wait until keyword succeeds    30    1    设置web参数量    二次下电告警    严重    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    向上调节电池电压    53.5
    关闭负载输出
    打开交流源输出
    Wait Until Keyword Succeeds    20m    1    信号量数据值为    二次下电控制状态    恢复
    Wait Until Keyword Succeeds    8m    2    查询指定告警信息不为    二次下电告警
    显示属性配置    二次下电控制状态    数字量    web_attr=Off    gui_attr=Off
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    二次下电告警    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    二次下电告警    负载二次下电电压    测试终止电压    电池电压低阈值    负载一次下电使能    二次下电恢复时间
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出
    ...    AND    重置电池模拟器输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0004_电池下电告警
    [Tags]    T1-1
    [Setup]    Run keywords    重置电池模拟器输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    5
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息不为    电池下电告警
    关闭交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    电池下电告警
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
        设置web参数量    电池组容量_${i}    0
    END
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    99
    sleep    2
    Wait Until Keyword Succeeds    5m    1    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电模式    电池电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    下电控制延时    0
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载一次下电使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    负载二次下电使能    禁止
    Wait Until Keyword Succeeds    10m    1    设置web参数量    电池下电使能    允许
    显示属性配置    电池下电控制状态    数字量    web_attr=On    gui_attr=On
    ${可设置范围}    获取web参数可设置范围    测试终止电压
    设置web参数量    测试终止电压    ${可设置范围}[1]
    ${电压低可设置范围}    获取web参数可设置范围    电池电压低阈值
    设置web参数量    电池电压低阈值    ${电压低可设置范围}[1]
    ${下电电压可设置范围}    获取web参数可设置范围    电池下电电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池下电电压    ${下电电压可设置范围}[1]
    设置负载电压电流    53.5    10
    打开负载输出
    ${下电电压设置值}    获取web参数量    电池下电电压
    ${下电电压}    evaluate    ${下电电压设置值}-0.5
    向下调节电池电压    ${下电电压}
    Wait Until Keyword Succeeds    10m    1    信号量数据值为    电池下电控制状态    动作
    ${告警级别取值约定dict}    获取web参数的取值约定    电池下电告警
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    电池下电告警    ${告警级别设置}
        sleep   5
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    电池组    电池下电告警
        wait until keyword succeeds    1m    1    查询指定告警信息    电池下电告警
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    电池下电告警    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    设置web参数量    电池下电告警    严重
    向上调节电池电压    53.5
    关闭负载输出
    打开交流源输出
    设置web设备参数量为默认值    电池下电电压    测试终止电压    电池电压低阈值    负载一次下电使能    负载二次下电使能
    重置电池模拟器输出
    Comment    Wait Until Keyword Succeeds    8m    1    信号量数据值为    电池下电控制状态    恢复
    显示属性配置    电池下电控制状态    数字量    web_attr=Off    gui_attr=Off
    Wait Until Keyword Succeeds    8m    2    查询指定告警信息不为    电池下电告警
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    电池下电告警    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    电池下电电压    测试终止电压    电池电压低阈值    负载一次下电使能    负载二次下电使能
    ...    AND    设置web参数量    电池下电告警干接点    0
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出
    ...    AND    重置电池模拟器输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0005_电池检测异常
    [Tags]    T1-1
    [Setup]    Run keywords    重置电池模拟器输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    电池管理初始化
    Wait Until Keyword Succeeds    10    2s    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    检测
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池检测异常    次要
    关闭电池模拟器输出
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    电池检测异常
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池检测异常    屏蔽
    sleep    5
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    电池检测异常
    ${告警级别取值约定dict}    获取web参数的取值约定    电池检测异常
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    电池检测异常    ${告警级别设置}
        sleep   5
        wait until keyword succeeds    1m    1    查询指定告警信息    电池检测异常
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    电池组    电池检测异常
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    电池检测异常    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    重置电池模拟器输出
    Wait Until Keyword Succeeds    5m    2s    设置web控制量    启动电池检测
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    电池检测异常
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    电池检测异常
    Wait Until Keyword Succeeds    2m    2    设置web控制量    启动浮充
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    电池检测异常    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    Run keywords    设置web设备参数量为默认值    电池检测异常
    ...    AND    Wait Until Keyword Succeeds    2m    2    设置web控制量    启动电池检测
    ...    AND    Wait Until Keyword Succeeds    2m    2    设置web控制量    启动浮充
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0006_电池测试
    [Tags]    T1-1
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    电池管理初始化
    Wait Until Keyword Succeeds    5m    2    设置web参数量    电池测试    严重
    Wait Until Keyword Succeeds    5m    2s    设置web控制量    启动测试
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    测试
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    电池测试
    Wait Until Keyword Succeeds    5m    2    设置web参数量    电池测试    屏蔽
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    电池测试
    ${告警级别取值约定dict}    获取web参数的取值约定    电池测试
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    电池测试    ${告警级别设置}
        sleep   5
        wait until keyword succeeds    1m    1    查询指定告警信息    电池测试
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    电池组    电池测试
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    电池测试    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    wait until keyword succeeds    30    1    设置web参数量    电池测试    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    Wait Until Keyword Succeeds    2m    2    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池测试
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    电池测试    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    Run keywords    设置web设备参数量为默认值    电池测试
    ...    AND    Wait Until Keyword Succeeds    1m    2    设置web控制量    启动浮充
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0007_电池均充
    [Tags]    T1-1
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    电池管理初始化
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池均充    次要
    Wait Until Keyword Succeeds    10    1    设置web控制量    启动均充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    均充
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息    电池均充
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池均充    屏蔽
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    电池均充
    ${告警级别取值约定dict}    获取web参数的取值约定    电池均充
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    电池均充    ${告警级别设置}
        sleep   5
        wait until keyword succeeds    1m    1    查询指定告警信息    电池均充
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    电池组    电池均充
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    电池均充    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    Wait Until Keyword Succeeds    10    2    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池均充
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    电池均充    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    Run keywords    设置web设备参数量为默认值    电池均充
    ...    AND    Wait Until Keyword Succeeds    1m    2    设置web控制量    启动浮充
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0008_电池高温下电
    [Tags]    T1-1
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    电池管理初始化
    #获取参数/默认值
    wait until keyword succeeds    10    1    设置web参数量    电池高温下电使能    允许
    ${告警级别}    获取web参数量    电池高温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web设备参数量为默认值    电池高温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池高温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    ${电池电压}    获取web实时数据    电池电压-1
    ${可设范围}    获取web参数可设置范围    电池高温下电温度
    设置web参数量    电池高温下电温度    ${可设范围}[0]
    run keyword and ignore error    设置web参数量    下电控制延时    0
    显示属性配置    电池高温下电控制状态    数字量    web_attr=On    gui_attr=On
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    10    2    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    设置负载电压电流    53.5    10
    打开负载输出
    ${告警级别取值约定dict}    获取web参数的取值约定    电池高温下电
    ${告警级别}    获取web参数量    电池高温下电
    wait until keyword succeeds    15m    1    判断告警存在    电池高温下电
    Wait Until Keyword Succeeds    3m    2    信号量数据值为    电池高温下电控制状态    动作
    Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client'    主动告警上送判断2    power_sm    电池组    电池高温下电
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    电池高温下电    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${告警级别}
    #恢复
    设置web参数量    电池高温下电使能    禁止
    Comment    Wait Until Keyword Succeeds    7m    2    信号量数据值为    电池高温下电控制状态    恢复
    wait until keyword succeeds    3m    2    判断告警不存在    电池高温下电
    #无告警后查询判断
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    电池高温下电    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    设置web参数量    电池高温下电使能    允许
    Wait Until Keyword Succeeds    5    1    显示属性配置    电池高温下电控制状态    数字量    web_attr=Off    gui_attr=Off
    关闭负载输出
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池高温下电温度    电池高温下电使能
    ...    AND    关闭负载输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0009_电池低温下电
    [Tags]    T1-1
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    电池管理初始化
    #获取参数/默认值
    wait until keyword succeeds    10    1    设置web参数量    电池低温下电使能    允许
    ${告警级别}    获取web参数量    电池低温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    电池低温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    电池低温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    sleep    3
    ${可设范围}    获取web参数可设置范围    电池低温下电温度
    设置web参数量    电池低温下电温度    ${可设范围}[1]
    run keyword and ignore error    设置web参数量    下电控制延时    0
    显示属性配置    电池低温下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    3
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    -10    0.001    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    设置负载电压电流    53.5    10
    打开负载输出
    wait until keyword succeeds    15m    1    判断告警存在    电池低温下电
    Wait Until Keyword Succeeds    6m    2    信号量数据值为    电池低温下电控制状态    动作
    Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client'    主动告警上送判断2    power_sm    电池组    电池低温下电
    ${告警级别取值约定dict}    获取web参数的取值约定    电池低温下电
    ${告警级别}    获取web参数量    电池低温下电
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    电池低温下电    ${SSH}
    should be true    ${power_sm告警存在}[0]
    ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
    should be equal    ${power_sm告警级别}    ${告警级别}
    #恢复
    设置web参数量    电池低温下电使能    禁止
    wait until keyword succeeds    4m    2    判断告警不存在    电池低温下电
    #无告警后查询判断
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    电池低温下电    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    设置web参数量    电池低温下电使能    允许
    Wait Until Keyword Succeeds    5    1    显示属性配置    电池低温下电控制状态    数字量    web_attr=Off    gui_attr=Off
    关闭负载输出
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池低温下电温度    电池低温下电使能
    ...    AND    关闭负载输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0010_电池组丢失
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接    
    连接CSU
    ${级别设置值}    获取web参数量    电池组丢失
    run keyword if    '${级别设置值}'=='屏蔽'    设置web设备参数量为默认值    电池组丢失
    ${DI2通道配置}    获取通道配置    ${plat.Inrelay2Status}
    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay2Status}    电池组    电池组丢失    断开
    ${告警级别取值约定dict}    获取web参数的取值约定    电池组丢失
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    电池组丢失    ${告警级别设置}
        sleep   5
        wait until keyword succeeds    1m    1    查询指定告警信息    电池组丢失
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    电池组    电池组丢失
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    电池组丢失    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay2Status}    ${DI2通道配置}[1]    ${DI2通道配置}[2]    ${DI2通道配置}[3]
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池组丢失
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    电池组丢失    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    Run keywords    设置web设备参数量为默认值    电池组丢失
    ...    AND    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay2Status}    ${DI2通道配置}[1]    ${DI2通道配置}[2]    ${DI2通道配置}[3]
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
