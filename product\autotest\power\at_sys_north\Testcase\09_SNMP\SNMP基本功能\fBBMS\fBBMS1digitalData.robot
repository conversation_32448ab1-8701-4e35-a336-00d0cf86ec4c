*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
# snmp批量获取FBBMS数字量
#     [Documentation]    21min
#     写入CSV文档    FB100B3数字量获取测试    信号名称    信号值    结果    备注
#     连接CSU
#     进行SNMP_V2/V3连接    ${snmp连接方式}
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=digital data    INDEX_DEVICE_CH=FBBMS
#     ${排除列表}    create list
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除数字量信号}    ${排除列表}    2    1    0    2
#     ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    fBBMS    digitalData
#     FOR    ${i}    IN    @{snmp待测}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${节点名称}    Get From Dictionary    ${i}    snmp_name
#         ${信号序号}    Get From Dictionary    ${i}    Vindex
#         ${缺省值}    获取web参数上下限范围    ${信号名称}
#         Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    snmp    smartli    ${信号名称}    只读    ${缺省值}[1]    FB100B3数字量获取测试    null    ${节点名称}    ${信号序号}    null    null    null    null
#         Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    snmp    smartli    ${信号名称}    只读    ${缺省值}[2]    FB100B3数字量获取测试    null    ${节点名称}    ${信号序号}    null    null    null    null
#         Run keyword IF    ${缺省值}[1]!=${缺省值}[0] and ${缺省值}[2]!=${缺省值}[0]    Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    snmp    smartli    ${信号名称}    只读    ${缺省值}[0]    FB100B3数字量获取测试    null    ${节点名称}    ${信号序号}    null    null    null
#         ...    null
#     END
#     断开连接SNMP

snmp批量获取FBBMS数字量
    [Documentation]    21min
    写入CSV文档    FB100B3数字量获取测试    信号名称    信号值    结果    备注
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=digital data    INDEX_DEVICE_CH=FBBMS
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除数字量信号}    ${排除列表}    2    1    0    2
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    fBBMS    digitalData
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    1    snmp
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    snmp    smartli    只读    ${缺省值列表}    FB100B3数字量获取测试    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    2    snmp
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    snmp    smartli    只读    ${缺省值列表}    FB100B3数字量获取测试    null
    ${缺省值列表}   获取缺省值列表  ${snmp待测}    0    snmp
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    snmp    smartli    只读    ${缺省值列表}    FB100B3数字量获取测试    null
    断开连接SNMP
