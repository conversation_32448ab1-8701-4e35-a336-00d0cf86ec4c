*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
snmp_0002_fBBMS1contro_bmsReset1X
    [Documentation]    关键字设置失败，web实际显示设置成功，MIB Browser手动设置可成功
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    Comment    : FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
    Comment    @{锂电单体随机list}    从1-15个数中随机选n个不重复的单体    ${北向协议铁锂最大数}    6
    FOR    ${i}    IN RANGE    0    4
        ${设置控制量}    设置SNMP控制量    bmsReset1value
        ${存在结果}    判断web历史记录存在snmp控制内容    FBBMS    BMS复位    60    1
        Exit For Loop if    '${存在结果}'=='True'
        sleep    10
    END
    FOR    ${i}    IN RANGE    0    4
        ${设置控制量}    设置SNMP控制量    bmsReset${北向协议铁锂最大数}value
        ${存在结果}    判断web历史记录存在snmp控制内容    FBBMS    BMS复位    60    ${北向协议铁锂最大数}
        Exit For Loop if    '${存在结果}'=='True'
        sleep    10
    END
    断开连接SNMP

snmp_0004_fBBMS1control_bMSCommunicationFailAlarmClear1√
    [Documentation]    ${设备名称_不带index} | ${SNMP控制量在web中的名称} | ${interval} | ${index}=0
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    Comment    : FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
    FOR    ${i}    IN RANGE    0    4
        ${设置控制量}    设置SNMP控制量    bMSCommunicationFailAlarmClear$1value
        ${存在结果}    判断web历史记录存在snmp控制内容    FBBMS    BMS通讯中断告警清除    60    1
        Exit For Loop if    '${存在结果}'=='True'
        sleep    10
    END
    FOR    ${i}    IN RANGE    0    4
        ${设置控制量}    设置SNMP控制量    bMSCommunicationFailAlarmClear${北向协议铁锂最大数}value
        ${存在结果}    判断web历史记录存在snmp控制内容    FBBMS    BMS通讯中断告警清除    60    ${北向协议铁锂最大数}
        Exit For Loop if    '${存在结果}'=='True'
        sleep    10
    END
    断开连接SNMP
