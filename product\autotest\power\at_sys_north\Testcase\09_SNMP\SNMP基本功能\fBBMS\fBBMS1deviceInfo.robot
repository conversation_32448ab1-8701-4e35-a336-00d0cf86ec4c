*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
# snmp批量获取FBBMS设备信息
#     写入CSV文档    FB100B3设备信息获取测试    信号名称    信号值    结果    备注
#     连接CSU
#     进行SNMP_V2/V3连接    ${snmp连接方式}
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string64    INDEX_DEVICE_CH=FBBMS
#     ${排除列表}    create list
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除设备版本信息}    ${排除列表}    2    1    0    2
#     ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    fBBMS    deviceInfo
#     FOR    ${i}    IN    @{snmp待测}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${节点名称}    Get From Dictionary    ${i}    snmp_name
#         ${信号序号}    Get From Dictionary    ${i}    Vindex
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    smartli    ${信号名称}    只读    V99.23    FB100B3设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    smartli    ${信号名称}    只读    V10.10    FB100B3设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    smartli    ${信号名称}    只读    V1.81    FB100B3设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
#     END
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string64    INDEX_DEVICE_CH=FBBMS
#     ${排除列表}    create list
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除设备名称信息}    ${排除列表}    2    1    0    2
#     ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    fBBMS    deviceInfo
#     FOR    ${i}    IN    @{snmp待测}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${节点名称}    Get From Dictionary    ${i}    snmp_name
#         ${信号序号}    Get From Dictionary    ${i}    Vindex
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    smartli    ${信号名称}    只读    VZXDU48 FB100B3    FB100B3设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    smartli    ${信号名称}    只读    ZTE-smartli    FB100B3设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    smartli    ${信号名称}    只读    VZXDU48 FB100C2    FB100B3设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
#     END
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=float    INDEX_DEVICE_CH=FBBMS
#     ${排除列表}    create list
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    2    1    0    2
#     ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    fBBMS    deviceInfo
#     FOR    ${i}    IN    @{snmp待测}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${节点名称}    Get From Dictionary    ${i}    snmp_name
#         ${信号序号}    Get From Dictionary    ${i}    Vindex
#         ${缺省值}    获取web参数上下限范围    ${信号名称}
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    smartli    ${信号名称}    只读    ${缺省值}[1]    FB100B3设备信息获取测试    数值    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    smartli    ${信号名称}    只读    ${缺省值}[2]    FB100B3设备信息获取测试    数值    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    smartli    ${信号名称}    只读    ${缺省值}[0]    FB100B3设备信息获取测试    数值    null    ${节点名称}    ${信号序号}
#     END
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=int    INDEX_DEVICE_CH=FBBMS
#     ${排除列表}    create list
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    2    1    0    2
#     ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    fBBMS    deviceInfo
#     FOR    ${i}    IN    @{snmp待测}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${节点名称}    Get From Dictionary    ${i}    snmp_name
#         ${信号序号}    Get From Dictionary    ${i}    Vindex
#         ${缺省值}    获取web参数上下限范围    ${信号名称}
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    smartli    ${信号名称}    只读    ${缺省值}[1]    FB100B3设备信息获取测试    数值    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    smartli    ${信号名称}    只读    ${缺省值}[2]    FB100B3设备信息获取测试    数值    null    ${节点名称}    ${信号序号}
#         Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    smartli    ${信号名称}    只读    ${缺省值}[0]    FB100B3设备信息获取测试    数值    null    ${节点名称}    ${信号序号}
#     END
#     断开连接SNMP

snmp批量获取FBBMS设备信息
    写入CSV文档    FB100B3设备信息获取测试    信号名称    信号值    结果    备注
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string64    INDEX_DEVICE_CH=FBBMS
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除设备版本信息}    ${排除列表}    2    1    0    2
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    fBBMS    deviceInfo
    FOR    ${i}    IN    @{snmp待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    snmp_name
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    smartli    ${信号名称}    只读    V99.23    FB100B3设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
        Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    smartli    ${信号名称}    只读    V10.10    FB100B3设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
        Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    smartli    ${信号名称}    只读    V1.81    FB100B3设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
    END
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string64    INDEX_DEVICE_CH=FBBMS
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除设备名称信息}    ${排除列表}    2    1    0    2
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    fBBMS    deviceInfo
    FOR    ${i}    IN    @{snmp待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    snmp_name
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    smartli    ${信号名称}    只读    VZXDU48 FB100B3    FB100B3设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
        Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    smartli    ${信号名称}    只读    ZTE-smartli    FB100B3设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
        Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    smartli    ${信号名称}    只读    VZXDU48 FB100C2    FB100B3设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
    END
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string64    INDEX_DEVICE_CH=FBBMS
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除设备序列号信息}    ${排除列表}    2    1    0    2
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    fBBMS    deviceInfo
    FOR    ${i}    IN    @{snmp待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    snmp_name
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    smartli    ${信号名称}    读写    210097205489    FB100B3设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
        Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    smartli    ${信号名称}    读写    210097205673    FB100B3设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
        Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    smartli    ${信号名称}    读写    210097205688    FB100B3设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
    END
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=float    INDEX_DEVICE_CH=FBBMS
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    2    1    0    2
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    fBBMS    deviceInfo
    FOR    ${i}    IN    @{snmp待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    snmp_name
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        ${缺省值}    获取web参数上下限范围    ${信号名称}
        Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    smartli    ${信号名称}    读写    ${缺省值}[1]    FB100B3设备信息获取测试    数值    null    ${节点名称}    ${信号序号}
        Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    smartli    ${信号名称}    读写    ${缺省值}[2]    FB100B3设备信息获取测试    数值    null    ${节点名称}    ${信号序号}
        Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    smartli    ${信号名称}    读写    ${缺省值}[0]    FB100B3设备信息获取测试    数值    null    ${节点名称}    ${信号序号}
    END
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=int    INDEX_DEVICE_CH=FBBMS
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    2    1    0    2
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    fBBMS    deviceInfo
    FOR    ${i}    IN    @{snmp待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    snmp_name
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        ${缺省值}    获取web参数上下限范围    ${信号名称}
        Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    smartli    ${信号名称}    读写    ${缺省值}[1]    FB100B3设备信息获取测试    数值    null    ${节点名称}    ${信号序号}
        Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    smartli    ${信号名称}    读写    ${缺省值}[2]    FB100B3设备信息获取测试    数值    null    ${节点名称}    ${信号序号}
        Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    snmp    smartli    ${信号名称}    读写    ${缺省值}[0]    FB100B3设备信息获取测试    数值    null    ${节点名称}    ${信号序号}
    END
    断开连接SNMP

snmp_0006_BMS软件发布日期3
    [Setup]
    连接CSU
    设置子工具值    smartli    all    只读    BMS软件发布日期    2020
    设置子工具值    smartli    all    只读    BMS软件发布日期mouth    12
    设置子工具值    smartli    all    只读    BMS软件发布日期day    5
    sleep    15m
    进行SNMP_V2/V3连接    ${snmp连接方式}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        ${日期16}    获取web实时数据    BMS软件发布日期-${锂电序号}
        ${snmp获取值16}    获取SNMP数据_单个    bMSSoftwareReleaseDate${锂电序号}value
        should be equal    '${日期16}'    '2020-12-05'
        should be equal    '${snmp获取值16}'    '2020-12-05'
    END
    设置子工具值    smartli    all    只读    BMS软件发布日期    2020
    设置子工具值    smartli    all    只读    BMS软件发布日期mouth    12
    设置子工具值    smartli    all    只读    BMS软件发布日期day    4
    sleep    15m
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        ${日期16}    获取web实时数据    BMS软件发布日期-${锂电序号}
        ${snmp获取值16}    获取SNMP数据_单个    bMSSoftwareReleaseDate${锂电序号}value
        should be equal    '${日期16}'    '2020-12-04'
        should be equal    '${snmp获取值16}'    '2020-12-04'
    END
    断开连接SNMP

snmp_0016_BDCU软件发布日期8
    [Setup]
    连接CSU
    设置子工具值    smartli    all    只读    BDU版本日期    2020
    设置子工具值    smartli    all    只读    BDU版本日期mouth    12
    设置子工具值    smartli    all    只读    BDU版本日期day    12
    sleep    15m
    进行SNMP_V2/V3连接    ${snmp连接方式}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        ${日期16}    获取web实时数据    BDCU软件发布日期-${锂电序号}
        ${snmp获取值16}    获取SNMP数据_单个    bDCUSoftwareReleaseDate${锂电序号}value
        should be equal    '${日期16}'    '2020-12-12'
        should be equal    '${snmp获取值16}'    '2020-12-12'
    END
    设置子工具值    smartli    all    只读    BDU版本日期    2020
    设置子工具值    smartli    all    只读    BDU版本日期mouth    11
    设置子工具值    smartli    all    只读    BDU版本日期day    11
    sleep    15m
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        ${日期16}    获取web实时数据    BDCU软件发布日期-${锂电序号}
        ${snmp获取值16}    获取SNMP数据_单个    bDCUSoftwareReleaseDate${锂电序号}value
        should be equal    '${日期16}'    '2020-11-11'
        should be equal    '${snmp获取值16}'    '2020-11-11'
    END
    断开连接SNMP

snmp_0026_电池启用日期13
    [Setup]
    连接CSU
    设置子工具值    smartli    all    只读    电芯启用日期    2018
    设置子工具值    smartli    all    只读    电芯启用日期mouth    07
    设置子工具值    smartli    all    只读    电芯启用日期day    28
    sleep    15m
    进行SNMP_V2/V3连接    ${snmp连接方式}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        ${日期16}    获取web实时数据    <<电池启用日期-${锂电序号}~0x170010800f0001>>
        ${snmp获取值16}    获取SNMP数据_单个    packDateOfActive${锂电序号}value
        should be equal    '${日期16}'    '2018-07-28'
        should be equal    '${snmp获取值16}'    '2018-07-28'
    END
    设置子工具值    smartli    all    只读    电芯启用日期    2020
    设置子工具值    smartli    all    只读    电芯启用日期mouth    10
    设置子工具值    smartli    all    只读    电芯启用日期day    11
    sleep    15m
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        ${日期16}    获取web实时数据    <<电池启用日期-${锂电序号}~0x170010800f0001>>
        ${snmp获取值16}    获取SNMP数据_单个    packDateOfActive${锂电序号}value
        should be equal    '${日期16}'    '2020-10-11'
        should be equal    '${snmp获取值16}'    '2020-10-11'
    END
    断开连接SNMP

snmp_0020_电池生产日期
    连接CSU
    设置子工具值    smartli    all    只读    电芯出厂日期    2018
    设置子工具值    smartli    all    只读    电芯出厂日期mouth    07
    设置子工具值    smartli    all    只读    电芯出厂日期day    28
    sleep    18m
    进行SNMP_V2/V3连接    ${snmp连接方式}
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        ${日期16}    获取web实时数据    电池生产日期-${锂电序号}
        ${snmp获取值16}    获取SNMP数据_单个    batteryManufactureDate${锂电序号}value
        should be equal    '${日期16}'    '2018-07-28'
        should be equal    '${snmp获取值16}'    '2018-07-28'
    END
    设置子工具值    smartli    all    只读    电芯出厂日期    2020
    设置子工具值    smartli    all    只读    电芯出厂日期mouth    10
    设置子工具值    smartli    all    只读    电芯出厂日期day    11
    sleep    18m
    FOR    ${锂电序号}    IN RANGE    1    ${北向协议铁锂最大数}+1
        ${日期16}    获取web实时数据    电池生产日期-${锂电序号}
        ${snmp获取值16}    获取SNMP数据_单个    batteryManufactureDate${锂电序号}value
        should be equal    '${日期16}'    '2020-10-11'
        should be equal    '${snmp获取值16}'    '2020-10-11'
    END
    断开连接SNMP
