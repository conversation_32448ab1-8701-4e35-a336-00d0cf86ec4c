*** Settings ***
Force Tags
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp_test关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/Web关键字_V30.robot
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
snmp_0002_交流电表通讯状态√
    [Documentation]    0:正常/Normal;1:异常/Abnormal
    连接CSU
    显示属性配置    交流电表通讯状态    数字量    web_attr=On    gui_attr=On
    设置子工具个数    ACmeter    9
    控制子工具运行停止    ACmeter    启动
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    交流电表通讯状态-1    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    交流电表通讯状态-2    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    交流电表通讯状态-3    正常
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    0    aCEMCommunicationState1value    0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    0    aCEMCommunicationState2value    0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    0    aCEMCommunicationState3value    0
    # ${snmp获取值1}    获取SNMP数据_单个    aCEMCommunicationState1value
    # ${snmp获取值2}    获取SNMP数据_单个    aCEMCommunicationState2value
    # ${snmp获取值3}    获取SNMP数据_单个    aCEMCommunicationState3value
    # should be equal    ${snmp获取值1}    0
    # should be equal    ${snmp获取值2}    0
    # should be equal    ${snmp获取值3}    0
    设置子工具个数    ACmeter    6
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    交流电表通讯状态-1    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    交流电表通讯状态-2    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    交流电表通讯状态-3    异常
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    0    aCEMCommunicationState1value    0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    0    aCEMCommunicationState2value    0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    1    aCEMCommunicationState3value    0
    # sleep    1m
    # ${名称1}    获取web实时数据    交流电表通讯状态-1
    # ${名称2}    获取web实时数据    交流电表通讯状态-2
    # ${名称3}    获取web实时数据    交流电表通讯状态-3
    # ${snmp获取值1}    获取SNMP数据_单个    aCEMCommunicationState1value
    # ${snmp获取值2}    获取SNMP数据_单个    aCEMCommunicationState2value
    # ${snmp获取值3}    获取SNMP数据_单个    aCEMCommunicationState3value
    # should be equal    '${名称1}'    '正常'
    # should be equal    '${名称2}'    '正常'
    # should be equal    '${名称3}'    '异常'
    # should be equal    ${snmp获取值1}    0
    # should be equal    ${snmp获取值2}    0
    # should be equal    ${snmp获取值3}    1
    设置子工具个数    ACmeter    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    交流电表通讯状态-1    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    交流电表通讯状态-2    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    交流电表通讯状态-3    异常
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    0    aCEMCommunicationState1value    0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    1    aCEMCommunicationState2value    0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    1    aCEMCommunicationState3value    0
    # sleep    1m
    # ${名称1}    获取web实时数据    交流电表通讯状态-1
    # ${名称2}    获取web实时数据    交流电表通讯状态-2
    # ${名称3}    获取web实时数据    交流电表通讯状态-3
    # ${snmp获取值1}    获取SNMP数据_单个    aCEMCommunicationState1value
    # ${snmp获取值2}    获取SNMP数据_单个    aCEMCommunicationState2value
    # ${snmp获取值3}    获取SNMP数据_单个    aCEMCommunicationState3value
    # should be equal    '${名称1}'    '正常'
    # should be equal    '${名称2}'    '异常'
    # should be equal    '${名称3}'    '异常'
    # should be equal    ${snmp获取值1}    0
    # should be equal    ${snmp获取值2}    1
    # should be equal    ${snmp获取值3}    1
    控制子工具运行停止    ACmeter    关闭
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    交流电表通讯状态-1    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    交流电表通讯状态-2    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    交流电表通讯状态-3    异常
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    1    aCEMCommunicationState1value    0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    1    aCEMCommunicationState2value    0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    1    aCEMCommunicationState3value    0
    # sleep    1m
    # ${名称1}    获取web实时数据    交流电表通讯状态-1
    # ${名称2}    获取web实时数据    交流电表通讯状态-2
    # ${名称3}    获取web实时数据    交流电表通讯状态-3
    # ${snmp获取值1}    获取SNMP数据_单个    aCEMCommunicationState1value
    # ${snmp获取值2}    获取SNMP数据_单个    aCEMCommunicationState2value
    # ${snmp获取值3}    获取SNMP数据_单个    aCEMCommunicationState3value
    # should be equal    '${名称1}'    '异常'
    # should be equal    '${名称2}'    '异常'
    # should be equal    '${名称3}'    '异常'
    # should be equal    ${snmp获取值1}    1
    # should be equal    ${snmp获取值2}    1
    # should be equal    ${snmp获取值3}    1
    断开连接SNMP
    显示属性配置    交流电表通讯状态    数字量    web_attr=Off    gui_attr=Off
    控制子工具运行停止    ACmeter    开启
    sleep    1m
    [Teardown]    控制子工具运行停止    ACmeter    开启

snmp_0004_交流电表在位状态√
    [Documentation]    0:不在位/Not Exist;1:在位/Is Exist
    连接CSU
    显示属性配置    交流电表在位状态    数字量    web_attr=On    gui_attr=On
    设置子工具个数    ACmeter    9
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    交流电表在位状态-1    在位
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    交流电表在位状态-2    在位
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    交流电表在位状态-3    在位
    进行SNMP_V2/V3连接    ${snmp连接方式}
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    1    aCEMExistState1value    0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    1    aCEMExistState2value    0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    1    aCEMExistState3value    0
    设置子工具个数    ACmeter    5
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    交流电表在位状态-1    在位
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    交流电表在位状态-2    在位
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    交流电表在位状态-3    在位
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    1    aCEMExistState1value    0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    1    aCEMExistState2value    0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    1    aCEMExistState3value    0
    设置子工具个数    ACmeter    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    交流电表在位状态-1    在位
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    交流电表在位状态-2    在位
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    交流电表在位状态-3    在位
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    1    aCEMExistState1value    0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    1    aCEMExistState2value    0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    1    aCEMExistState3value    0
    控制子工具运行停止    llSensor    关闭
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    交流电表在位状态-1    在位
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    交流电表在位状态-2    在位
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    交流电表在位状态-3    在位
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    1    aCEMExistState1value    0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    1    aCEMExistState2value    0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    1    aCEMExistState3value    0
    断开连接SNMP
    显示属性配置    交流电表在位状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    控制子工具运行停止    ACmeter    开启

snmp_0006_交流电表工作状态X
    [Documentation]    0:无/Null;1:正常/Normal;2:告警/Alarm;3:通讯断/CommFail;
    连接CSU
    显示属性配置    交流电表工作状态    数字量    web_attr=On    gui_attr=On
    设置子工具个数    ACmeter    9
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    交流电表工作状态-1    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    交流电表工作状态-2    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    交流电表工作状态-3    正常
    进行SNMP_V2/V3连接    ${snmp连接方式}
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    1    aCEMWorkState1value    0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    1    aCEMWorkState2value    0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    1    aCEMWorkState3value    0
    设置子工具个数    ACmeter    5
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    交流电表工作状态-1    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    交流电表工作状态-2    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    交流电表工作状态-3    通讯断
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    1    aCEMWorkState1value    0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    1    aCEMWorkState2value    0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    3    aCEMWorkState3value    0
    设置子工具个数    ACmeter    1
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    交流电表工作状态-1    正常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    交流电表工作状态-2    通讯断
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    交流电表工作状态-3    通讯断
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    1    aCEMWorkState1value    0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    3    aCEMWorkState2value    0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    3    aCEMWorkState3value    0
    控制子工具运行停止    ACmeter    关闭
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    交流电表工作状态-1    通讯断
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    交流电表工作状态-2    通讯断
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    交流电表工作状态-3    通讯断
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    3    aCEMWorkState1value    0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    3    aCEMWorkState2value    0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备模拟量/数字量获取    3    aCEMWorkState3value    0
    断开连接SNMP
    显示属性配置    交流电表工作状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    控制子工具运行停止    ACmeter    开启
