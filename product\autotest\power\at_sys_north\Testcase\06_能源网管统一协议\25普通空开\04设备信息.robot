*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
批量获取SDDU设备信息
    写入CSV文档    SDU2设备信息获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string32    INDEX_DEVICE_CH=智能直流配电单元
    ${排除列表}    create list
    @{信号名称列表1}    create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SDU2排除系统名称}    ${排除列表}    2
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    智能直流配电单元    device info    ${SDU2排除系统名称}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    FOR    ${i}    IN    @{power_sm待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
		Append To List       ${信号名称列表1}    ${dict}
    END
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    SDU2   ${信号名称列表1}   建链    V99.23    SDDU设备信息获取测试    字符    获取SDDU设备信息    
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    SDU2   ${信号名称列表1}   建链    V10.10    SDDU设备信息获取测试    字符    获取SDDU设备信息    
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    SDU2   ${信号名称列表1}   建链    V1.81     SDDU设备信息获取测试    字符    获取SDDU设备信息
   
    



    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string32    INDEX_DEVICE_CH=智能直流配电单元
    ${排除列表}    create list
    @{信号名称列表2}    create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SDU2排除软件版本}    ${排除列表}    2
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    智能直流配电单元    device info    ${SDU2排除软件版本}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    FOR    ${i}    IN    @{power_sm待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
		Append To List       ${信号名称列表2}    ${dict}
    END
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    SDU2   ${信号名称列表2}   建链    VZXDU48 FB100B3    SDDU设备信息获取测试    字符    获取SDDU设备信息    
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    SDU2   ${信号名称列表2}   建链    ZTE-smartli        SDDU设备信息获取测试    字符    获取SDDU设备信息    
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    SDU2   ${信号名称列表2}   建链    VZXDU48 FB100C2     SDDU设备信息获取测试    字符    获取SDDU设备信息
    
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=date    INDEX_DEVICE_CH=智能直流配电单元
    ${排除列表}    create list
    @{信号名称列表3}    create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${排除列表}    ${排除列表}    2
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    智能直流配电单元    device info    ${排除列表}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    FOR    ${i}    IN    @{power_sm待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
		Append To List       ${信号名称列表3}    ${dict}
    END
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    SDU2   ${信号名称列表3}   建链    2018-11-15    SDDU设备信息获取测试    字符    获取SDDU设备信息    
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    SDU2   ${信号名称列表3}   建链    2021-08-23    SDDU设备信息获取测试    字符    获取SDDU设备信息    
    Run Keyword And Continue On Failure    南向子设备厂家数据信息列表封装判断结果    power_sm    SDU2   ${信号名称列表3}   建链    2018-10-31    SDDU设备信息获取测试    字符    获取SDDU设备信息
    
    
