*** Settings ***
Suite Setup       主动告警测试前置条件    ${CSU_role}
Suite Teardown    设置web设备参数量为默认值    CSU主动告警使能
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_BMS通信断告警
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    #子设备工具模拟
    控制子工具运行停止    fb100c2    关闭
    ${级别设置值}    获取web参数量    <<BMS通信断告警~0x21001030210001>>
    run keyword if    '${级别设置值}'=='屏蔽'    wait until keyword succeeds    10    2    设置web参数量    <<BMS通信断告警~0x21001030210001>>    严重
    ${告警级别取值约定dict}    获取web参数的取值约定    <<BMS通信断告警~0x21001030210001>>
    ${级别设置值}    获取web参数量    <<BMS通信断告警~0x21001030210001>>
    @{锂电序号随机list}    从1-15个数中随机选n个不重复的单体    ${北向协议NFBBMS最大数}    4
    FOR    ${锂电序号}    IN    @{锂电序号随机list}
        Wait Until Keyword Succeeds    10m    5    信号量数据值为    <<BMS通信断状态~0x210010202c0001>>    异常
        wait until keyword succeeds    5m    2    查询指定告警信息    BMS通信断告警-${锂电序号}
        ${power_sm告警存在}    Wait Until Keyword Succeeds    1m   5   power_sm存在单个实时告警  BMS通信断告警-${锂电序号}    ${SSH}
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${级别设置值}
    END
    wait until keyword succeeds    5m    2    查询指定告警信息    BMS通信断告警
    控制子工具运行停止    fb100c2    开启
    FOR    ${锂电序号}    IN    @{锂电序号随机list}
        Wait Until Keyword Succeeds    10m    5    信号量数据值为    <<BMS通信断状态~0x210010202c0001>>    正常
        wait until keyword succeeds    5m    1    判断告警不存在    BMS通信断告警-${锂电序号}
        Wait Until Keyword Succeeds    1m   5   power_sm不存在单个实时告警  BMS通信断告警-${锂电序号}    ${SSH}
    END
    wait until keyword succeeds    5m    2    查询指定告警信息不为    BMS通信断告警
    [Teardown]    run keywords    控制子工具运行停止    fb100c2    开启
    ...    AND    sleep    5m
    ...    AND    设置web设备参数量为默认值    <<BMS通信断告警~0x21001030210001>>
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0002_批量获取NFBBMS告警量
    写入CSV文档    NFBBMS数字量和告警量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=NFBBMS
    ${排除列表}    create list
    @{信号名称列表1}  create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100C2排除告警量信号}    ${排除列表}    1    1    0    2
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    NFBBMS    alarm    ${FB100C2排除告警量信号}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    ${待测数据长度}    Get Length    ${power_sm待测}
    ${number}    Set Variable    1
    FOR    ${i}    IN    @{power_sm待测}
        Log    正在验证第${number}个数据，一共${待测数据长度}个数据
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${实时告警中的设备名称}    主动告警_组合实时告警中的设备名称    NFBBMS    ${信号名称}
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
        Set To Dictionary    ${dict}     device_name     ${实时告警中的设备名称}
		Append To List       ${信号名称列表1}    ${dict}
        ${number}    Evaluate    ${number}+1
	END
    Run Keyword And Continue On Failure    南向RS485子设备告警量列表产生封装判断结果   power_sm    fb100c2    ${信号名称列表1}    
    ...    获取告警量    ${告警产生}    FB100C2数字量和告警量获取测试    电池    NFBBMS    
    ...    null    null    null    null    null    null
    Run Keyword And Continue On Failure    南向RS485子设备告警量列表恢复封装判断结果    power_sm    fb100c2    ${信号名称列表1}    
    ...    获取告警量    ${告警恢复}    FB100C2数字量和告警量获取测试    电池    NFBBMS    
    ...    null    null    null    null    null    null
      

power_sm_0003_批量获取NFBBMS合并的实时告警
    写入CSV文档    NFBBMS数字量和告警量获取测试    信号名称    信号值    结果
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=NFBBMS
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100C2排除欠压低温的告警量信号}    ${排除列表}    1    1    0    2
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    NFBBMS    alarm    ${FB100C2排除欠压低温的告警量信号}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    FOR    ${i}    IN    @{power_sm待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${实时告警中的设备名称}    主动告警_组合实时告警中的设备名称    NFBBMS    ${信号名称}
        Run Keyword And Continue On Failure    南向RS485子设备告警量产生封装判断结果    power_sm    fb100c2    ${信号名称}    获取告警量    ${告警产生}    FB100C2数字量和告警量获取测试    ${实时告警中的设备名称}    null    null    null    null    null    null
        Run Keyword And Continue On Failure    南向RS485子设备告警量恢复封装判断结果    power_sm    fb100c2    ${信号名称}    获取告警量    ${告警恢复}    FB100C2数字量和告警量获取测试    null    null    null    null    null    null    null
    END
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=NFBBMS
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100C2排除过压高温的告警量信号}    ${排除列表}    1    1    0    2
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    NFBBMS    alarm    ${FB100C2排除过压高温的告警量信号}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    FOR    ${i}    IN    @{power_sm待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${实时告警中的设备名称}    主动告警_组合实时告警中的设备名称    NFBBMS    ${信号名称}
        Run Keyword And Continue On Failure    南向RS485子设备告警量产生封装判断结果    power_sm    fb100c2    ${信号名称}    获取告警量    2    FB100C2数字量和告警量获取测试    ${实时告警中的设备名称}    null    null    null    null    null    null
        Run Keyword And Continue On Failure    南向RS485子设备告警量恢复封装判断结果    power_sm    fb100c2    ${信号名称}    获取告警量    ${告警恢复}    FB100C2数字量和告警量获取测试    null    null    null    null    null    null    null
    END
