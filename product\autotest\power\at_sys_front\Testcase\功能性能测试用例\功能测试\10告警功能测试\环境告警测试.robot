*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
环境温度高
    [Documentation]    高阈值范围：30-60，默认55；过高阈值范围：33-63，默认58。高比过高低3°
    
    实时告警刷新完成
    Comment    Wait Until Keyword Succeeds    5m    1    设置web设备参数量    系统运行环境    温度传感器类型    值=1    #默认1
    Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度过高阈值    58    #默认58，min33
    Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度高阈值    30    #默认55，min30
    ${环境温度}    Wait Until Keyword Succeeds    5m    1    获取web实时数据    环境温度
    FOR    ${调节步长}    IN RANGE    20    40    2
        ${零点}    evaluate    str(${调节步长})
        Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    ${零点}    1    系统运行环境    环境温度
        Comment    Wait Until Keyword Succeeds    30    1    通道配置_AI    ${plat.envtemp}    ${零点}    1    系统运行环境    环境温度
        sleep    3
        ${环境温度获取}    获取web实时数据    环境温度
        ${环境温度设置值}    获取web参数量    环境温度高阈值
        exit for loop if    ${环境温度获取}>${ 环境温度设置值}
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度高    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    环境温度高
    ${环境温度高告警}    判断告警存在_带返回值    环境温度高
    should not be true    ${环境温度高告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度高    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    环境温度高
        sleep    5
        ${环境温度高告警级别}    获取web告警属性    环境温度高    告警级别
        should be equal    '${告警级别}'    '${环境温度高告警级别}'
    END
    
    Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度高    次要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度高干接点    0
        ${告警干接点获取}    获取web告警属性    环境温度高    告警干接点
        should be true    ${告警干接点获取}==0
        sleep    8
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度高干接点    ${告警干接点设置}
        sleep    8
        ${告警干接点获取}    获取web告警属性    环境温度高    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0
    END
    [Teardown]    Run keywords    环境温度参数恢复默认值
    ...    AND    设置通道配置    ${plat.envtemp}    0    1    系统运行环境    环境温度
    ...    AND    设置web参数量    环境温度高干接点    0
    

环境温度过高
    [Documentation]    高阈值范围：30-60，默认55；过高阈值范围：33-63，默认58。高比过高低3°
    
    实时告警刷新完成
    ${可设置范围}    获取web参数可设置范围    环境温度高阈值
    设置web参数量    环境温度高阈值    ${可设置范围}[0]
    ${可设置范围}    获取web参数可设置范围    环境温度过高阈值
    设置web参数量    环境温度过高阈值    ${可设置范围}[0]
    ${环境温度}    Wait Until Keyword Succeeds    10    1    获取web实时数据    环境温度
    FOR    ${调节步长}    IN RANGE    20    40    2
        ${零点}    evaluate    str(${调节步长})
        Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    ${零点}    1    系统运行环境    环境温度
        Comment    Wait Until Keyword Succeeds    30    1    通道配置_AI    ${plat.envtemp}    ${零点}    1    系统运行环境    环境温度
        sleep    3
        ${环境温度获取}    获取web实时数据    环境温度
        ${环境温度设置值}    获取web参数量    环境温度过高阈值
        exit for loop if    ${环境温度获取}>${ 环境温度设置值}
    END
    Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度过高    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    环境温度过高
    ${环境温度高告警}    判断告警存在_带返回值    环境温度过高
    should not be true    ${环境温度高告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        Comment    ${告警级别设置}    evaluate    str(${告警级别})
        Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度过高    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    环境温度过高
        sleep    5
        ${环境温度高告警级别}    获取web告警属性    环境温度过高    告警级别
        should be equal    '${告警级别}'    '${环境温度高告警级别}'
    END
    
    Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度过高    次要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度过高干接点    0
        ${告警干接点获取}    获取web告警属性    环境温度过高    告警干接点
        should be true    ${告警干接点获取}==0
        sleep    8
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        Wait Until Keyword Succeeds    30    1    设置web参数量    环境温度过高干接点    ${告警干接点设置}
        sleep    8
        ${告警干接点获取}    获取web告警属性    环境温度过高    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0
    END
    [Teardown]    Run keywords    环境温度参数恢复默认值
    ...    AND    设置通道配置    ${plat.envtemp}    0    1    系统运行环境    环境温度
    ...    AND    设置web参数量    环境温度过高干接点    0
    

环境温度低
    [Documentation]    低阈值范围：-27~23，默认-5；过低阈值范围：-30~20，默认-8
    
    实时告警刷新完成
    ${可设置范围}    获取web参数可设置范围    环境温度低阈值
    设置web参数量    环境温度低阈值    ${可设置范围}[1]
    ${环境温度}    Wait Until Keyword Succeeds    5m    1    获取web实时数据    环境温度
    FOR    ${调节步长}    IN RANGE    -5    -50    -5
        ${零点}    evaluate    str(${调节步长})
        Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    ${零点}    1    系统运行环境    环境温度
        sleep    3
        ${环境温度获取}    获取web实时数据    环境温度
        ${环境温度设置值}    获取web参数量    环境温度低阈值
        exit for loop if    ${环境温度获取}<${ 环境温度设置值}
    END
    设置web参数量    环境温度低    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    环境温度低
    ${环境温度低告警}    判断告警存在_带返回值    环境温度低
    should not be true    ${环境温度低告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    10    1    设置web参数量    环境温度低    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    环境温度低
        sleep    5
        ${环境温度低告警级别}    获取web告警属性    环境温度低    告警级别
        should be equal    ${告警级别}    ${环境温度低告警级别}
    END
    
    设置web参数量    环境温度低    次要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    环境温度低    告警干接点
        run keyword if    ${告警干接点获取}!=0    设置web参数量    环境温度低干接点    ${干接点设置为无}
        sleep    8
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        设置web参数量    环境温度低干接点    ${告警干接点设置}
        sleep    8
        ${告警干接点获取}    获取web告警属性    环境温度低    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0
    END
    [Teardown]    Run keywords    环境温度参数恢复默认值
    ...    AND    设置通道配置    ${plat.envtemp}    0    1    系统运行环境    环境温度
    ...    AND    设置web参数量    环境温度低干接点    0
    

环境温度过低
    [Documentation]    低阈值范围：-27~23，默认-5；过低阈值范围：-30~20，默认-8
    
    实时告警刷新完成
    ${可设置范围}    获取web参数可设置范围    环境温度低阈值
    设置web参数量    环境温度低阈值    ${可设置范围}[1]
    ${可设置范围}    获取web参数可设置范围    环境温度过低阈值
    设置web参数量    环境温度过低阈值    ${可设置范围}[1]
    ${环境温度}    Wait Until Keyword Succeeds    5m    1    获取web实时数据    环境温度
    FOR    ${调节步长}    IN RANGE    -2    -50    -2
        ${零点}    evaluate    str(${调节步长})
        Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.envtemp}    ${零点}    1    系统运行环境    环境温度
        sleep    3
        ${环境温度获取}    获取web实时数据    环境温度
        ${环境温度设置值}    获取web参数量    环境温度过低阈值
        exit for loop if    ${环境温度获取}<${ 环境温度设置值}
    END
    设置web参数量    环境温度过低    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    环境温度过低
    ${环境温度过低告警}    判断告警存在_带返回值    环境温度过低
    should not be true    ${环境温度过低告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        设置web参数量    环境温度过低    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    环境温度过低
        sleep    5
        ${环境温度过低告警级别}    获取web告警属性    环境温度过低    告警级别
        should be equal    ${告警级别}    ${环境温度过低告警级别}
    END
    
    设置web参数量    环境温度过低    次要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    环境温度过低    告警干接点
        run keyword if    ${告警干接点获取}!=0    设置web参数量    环境温度过低干接点    ${干接点设置为无}
        sleep    8
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        设置web参数量    环境温度过低干接点    ${告警干接点设置}
        sleep    8
        ${告警干接点获取}    获取web告警属性    环境温度过低    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0
    END
    [Teardown]    Run keywords    环境温度参数恢复默认值
    ...    AND    设置通道配置    ${plat.envtemp}    0    1    系统运行环境    环境温度
    ...    AND    设置web参数量    环境温度过低干接点    0
    

环境湿度无效
    
    设置通道无效值/恢复通道原始值    ${plat.humity}    1
    #不接入传感器
    实时告警刷新完成
    设置web参数量    环境湿度无效    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    环境湿度无效
    ${环境湿度无效告警}    判断告警存在_带返回值    环境湿度无效
    should not be true    ${环境湿度无效告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    10    1    设置web参数量    环境湿度无效    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    环境湿度无效
        sleep    5
        ${环境湿度告警级别}    获取web告警属性    环境湿度无效    告警级别
        should be equal    ${告警级别}    ${环境湿度告警级别}
    END
    设置web参数量    环境湿度无效    次要
    
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    环境湿度无效    告警干接点
        run keyword if    ${告警干接点获取}!=0    设置web参数量    环境湿度无效干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        设置web参数量    环境湿度无效干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    环境湿度无效    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0
    END
    设置通道无效值/恢复通道原始值    ${plat.humity}    0
    [Teardown]    Run keywords    设置web参数量    环境湿度无效干接点    0
    ...    AND    设置通道无效值/恢复通道原始值    ${plat.humity}    0
    
环境温度无效
    
    设置通道无效值/恢复通道原始值    ${plat.envtemp}    1
    #不接入传感器
    实时告警刷新完成
    设置web参数量    环境温度无效    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    环境温度无效
    ${环境温度无效告警}    判断告警存在_带返回值    环境温度无效
    should not be true    ${环境温度无效告警}
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    10    1    设置web参数量    环境温度无效    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    环境温度无效
        sleep    5
        ${环境温度告警级别}    获取web告警属性    环境温度无效    告警级别
        should be equal    ${告警级别}    ${环境温度告警级别}
    END
    设置web参数量    环境温度无效    次要
    
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    环境温度无效    告警干接点
        run keyword if    ${告警干接点获取}!=0    设置web参数量    环境温度无效干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        设置web参数量    环境温度无效干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    环境温度无效    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0
    END
    设置通道无效值/恢复通道原始值    ${plat.envtemp}    0
    [Teardown]    设置web参数量    环境温度无效干接点    0
    

烟雾告警
    
    实时告警刷新完成
    设置web参数量    烟雾告警    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    烟雾告警
    ${烟雾告警}    判断告警存在_带返回值    烟雾告警
    should not be true    ${烟雾告警}
    模拟数字量告警    烟雾告警    ON
    sleep    10
    ${烟雾状态}    获取干接点状态    ${plat.smoke}
    should be true    ${烟雾状态}==1    \    #0为正常（闭合），1为断开（断开）
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        wait until keyword succeeds    10    1    设置web参数量    烟雾告警    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    烟雾告警
        sleep    3
        ${烟雾告警级别}    获取web告警属性    烟雾告警    告警级别
        should be equal    ${告警级别}    ${烟雾告警级别}
    END
    
    设置web参数量    烟雾告警    次要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    烟雾告警    告警干接点
        run keyword if    ${告警干接点获取}!=0    设置web参数量    烟雾告警干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        设置web参数量    烟雾告警干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    烟雾告警    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0
    END
    模拟数字量告警    烟雾告警    OFF
    sleep    10
    [Teardown]    run keywords    设置web参数量    烟雾告警干接点    0
    ...    AND    模拟数字量告警    烟雾告警    OFF
    

水淹告警
    [Documentation]    将水淹（X9）测试前先短接
    ...    ==20190327-F：水淹状态与数据字典相反，数据字典要求为0为正常，1为异常；实际水淹告警时，获取的状态为0
    ...    xhf0627:水淹状态应从配置文件中读取，默认状态是0，短接后状态为1.
    
    实时告警刷新完成
    Wait Until Keyword Succeeds    30    1    设置web参数量    水淹告警    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    水淹告警
    ${水淹告警}    判断告警存在_带返回值    水淹告警
    should not be true    ${水淹告警}
    模拟数字量告警    水淹告警    ON
    sleep    10
    ${水淹状态}    获取干接点状态    ${plat.Water}
    should be true    ${水淹状态}==1    #0为正常，1为异常
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    水淹告警    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    水淹告警
        sleep    3
        ${水淹告警级别}    获取web告警属性    水淹告警    告警级别
        should be equal    '${告警级别}'    '${水淹告警级别}'
    END
    
    Wait Until Keyword Succeeds    30    1    设置web参数量    水淹告警    主要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    水淹告警    告警干接点
        run keyword if    ${告警干接点获取}!=0    Wait Until Keyword Succeeds    30    1    设置web参数量    水淹告警干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        Wait Until Keyword Succeeds    30    1    设置web参数量    水淹告警干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    水淹告警    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0
    END
    模拟数字量告警    水淹告警    OFF
    sleep    10
    [Teardown]    run keywords    设置web参数量    水淹告警干接点    0
    ...    AND    模拟数字量告警    水淹告警    OFF
    

门磁告警
    
    实时告警刷新完成
    设置web参数量    门磁告警    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    门磁告警
    ${门磁告警}    判断告警存在_带返回值    门磁告警
    should not be true    ${门磁告警}
    模拟数字量告警    门磁告警    ON
    sleep    10
    ${门磁状态}    获取干接点状态    ${plat.MagneticDoor}
    should be true    ${门磁状态}==1    \    #0为正常（门磁闭合），1为断开（门磁断开）
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        设置web参数量    门磁告警    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    门磁告警
        sleep    3
        ${门磁告警级别}    获取web告警属性    门磁告警    告警级别
        should be equal    ${告警级别}    ${门磁告警级别}
    END
    
    设置web参数量    门磁告警    次要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    门磁告警    告警干接点
        run keyword if    ${告警干接点获取}!=0    设置web参数量    门磁告警干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        设置web参数量    门磁告警干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    门磁告警    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0
    END
    模拟数字量告警    门磁告警    OFF
    sleep    10
    [Teardown]    run keywords    设置web参数量    门磁告警干接点    0
    ...    AND    模拟数字量告警    门磁告警    OFF
    

温控单元异常告警测试
    [Setup]
    连接CSU
    ${干接点设置值}    获取web参数量    温控单元异常干接点
    #设置${plat.Inrelay4Status}，对应的CSU:温控单元异常告警
    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay4Status}    系统运行环境    温控单元异常    断开
    Wait Until Keyword Succeeds    10    2    设置web参数量    温控单元异常    屏蔽
    Wait Until Keyword Succeeds    3m    1    判断告警不存在    温控单元异常
    Wait Until Keyword Succeeds    5m    2    设置web参数量    温控单元异常    严重
    wait until keyword succeeds    1m    1    查询指定告警信息    温控单元异常
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    温控单元异常    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    温控单元异常
        sleep    3
        ${告警级别获取}    获取web告警属性    温控单元异常    告警级别
        should be equal    '${告警级别设置}'    '${告警级别获取}'
    END
    
    wait until keyword succeeds    30    1    设置web参数量    温控单元异常    主要    #告警默认值，默认如果屏蔽，也要设置个主要或严重。
    FOR    ${告警干接点设置}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${干接点设置为无}    evaluate    0
        ${告警干接点获取}    获取web告警属性    温控单元异常    告警干接点
        run keyword if    ${告警干接点获取}!=0    wait until keyword succeeds    30    1    设置web参数量    温控单元异常干接点    ${干接点设置为无}
        sleep    5
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==1
        wait until keyword succeeds    30    1    设置web参数量    温控单元异常干接点    ${告警干接点设置}
        sleep    5
        ${告警干接点获取}    获取web告警属性    温控单元异常    告警干接点
        should be true    ${告警干接点设置}==${告警干接点获取}
        ${干接点实际是否动作状态1}    获取干接点状态    ${输出干接点状态}
        should be true    ${干接点实际是否动作状态1}==0    #干接点闭合是0.
    END
    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay4Status}    系统运行环境    温控单元异常    闭合
    Wait Until Keyword Succeeds    3m    1    判断告警不存在    温控单元异常
    [Teardown]    Run keywords    设置web参数量    温控单元异常干接点    0
    ...    AND    设置web参数量    温控单元异常    次要
    ...    AND    wait until keyword succeeds    30    1    设置通道配置    ${plat.Inrelay4Status}    系统运行环境    温控单元异常    闭合
    
