*** Settings ***
Suite Setup       #run keywords | 连接CSU | # AND | 系统复位 | # AND | sleep | 5m
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
1104_0002_V3.0_1104_通用命令_获取协议版本号1-4_1
    [Tags]    1
    ${1104版本}    通用命令_获取协议版本号    ${g_prtcl_1104}    <<获取协议版本号1-4~CID1=40H>>    ${g_ver_1104}
    ${对比结果}    对比协议版本信息    ${1104版本}    ${g_ver_1104}    1104_协议版本_1
    should be true    ${对比结果}

1104_0004_V3.0_1104_通用命令_获取协议版本号1-4_2
    [Tags]    1
    ${1104版本}    通用命令_获取协议版本号    ${g_prtcl_1104}    <<获取协议版本号1-4~CID1=41H>>    ${g_ver_1104}
    ${对比结果}    对比协议版本信息    ${1104版本}    ${g_ver_1104}    1104_协议版本_2
    should be true    ${对比结果}

1104_0006_V3.0_1104_通用命令_获取协议版本号1-4_3
    [Tags]    1
    ${1104版本}    通用命令_获取协议版本号    ${g_prtcl_1104}    <<获取协议版本号1-4~CID1=42H>>    ${g_ver_1104}
    ${对比结果}    对比协议版本信息    ${1104版本}    ${g_ver_1104}    1104_协议版本_3
    should be true    ${对比结果}

1104_0008_V3.0_1104_通用命令_获取协议版本号1-4_4
    [Tags]    1
    ${1104版本}    通用命令_获取协议版本号    ${g_prtcl_1104}    <<获取协议版本号1-4~CID1=80H>>    ${g_ver_1104}
    ${对比结果}    对比协议版本信息    ${1104版本}    ${g_ver_1104}    1104_协议版本_4
    should be true    ${对比结果}
