*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
交流电表通讯状态获取测试
    连接CSU
    显示属性配置    交流电表通讯状态    数字量    web_attr=On    gui_attr=On
    设置子工具个数    ACmeter    9
    控制子工具运行停止    ACmeter    启动
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表通讯状态-1     正常
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表通讯状态-2     正常
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表通讯状态-3     正常
    设置子工具个数    ACmeter    5
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表通讯状态-1     正常
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表通讯状态-2     正常
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表通讯状态-3     异常
    设置子工具个数    ACmeter    1
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表通讯状态-1     正常
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表通讯状态-2     异常
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表通讯状态-3     异常
    控制子工具运行停止    ACmeter    关闭
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表通讯状态-1     异常
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表通讯状态-2     异常
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表通讯状态-3     异常
    显示属性配置    交流电表通讯状态    数字量    web_attr=Off    gui_attr=Off
    控制子工具运行停止    ACmeter    开启
    sleep    2m
    [Teardown]    控制子工具运行停止    ACmeter    开启

交流电表在位状态获取测试
    连接CSU
    显示属性配置    交流电表在位状态    数字量    web_attr=On    gui_attr=On
    设置子工具个数    ACmeter    9
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表在位状态-1     在位
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表在位状态-2     在位
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表在位状态-3     在位
    设置子工具个数    ACmeter    5
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表在位状态-1     在位
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表在位状态-2     在位
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表在位状态-3     在位
    设置子工具个数    ACmeter    1
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表在位状态-1     在位
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表在位状态-2     在位
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表在位状态-3     在位
    控制子工具运行停止    llSensor    关闭
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表在位状态-1     在位
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表在位状态-2     在位
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表在位状态-3     在位
    显示属性配置    交流电表在位状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    控制子工具运行停止    ACmeter    开启

交流电表工作状态获取测试
    连接CSU
    显示属性配置    交流电表工作状态    数字量    web_attr=On    gui_attr=On
    设置子工具个数    ACmeter    9
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表工作状态-1     正常
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表工作状态-2     正常
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表工作状态-3     正常
    设置子工具个数    ACmeter    5
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表工作状态-1     正常
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表工作状态-2     正常
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表工作状态-3     通讯断
    设置子工具个数    ACmeter    1
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表工作状态-1     正常
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表工作状态-2     通讯断
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表工作状态-3     通讯断
    控制子工具运行停止    ACmeter    关闭
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表工作状态-1     通讯断
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表工作状态-2     通讯断
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    交流电表工作状态-3     通讯断
    显示属性配置    交流电表工作状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    控制子工具运行停止    ACmeter    开启
