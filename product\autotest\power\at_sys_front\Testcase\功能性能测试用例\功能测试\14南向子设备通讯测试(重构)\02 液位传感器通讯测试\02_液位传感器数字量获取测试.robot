*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
液位传感器在位状态获取测试
    连接CSU
    显示属性配置    液位传感器在位状态    数字量    web_attr=On    gui_attr=On
    设置子工具个数    llSensor    1
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    液位传感器在位状态-1     在位
    # sleep    2m
    # ${名称1}    获取web实时数据    液位传感器在位状态-1
    # Comment    ${名称2}    获取web实时数据    液位传感器在位状态-2
    # should be equal    '${名称1}'    '在位'
    # Comment    should be equal    '${名称2}'    '在位'
    设置子工具个数    llSensor    1
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    液位传感器在位状态-1     在位
    sleep    2m
    # ${名称1}    获取web实时数据    液位传感器在位状态-1
    # Comment    ${名称2}    获取web实时数据    液位传感器在位状态-2
    # should be equal    '${名称1}'    '在位'
    # Comment    should be equal    '${名称2}'    '在位'
    控制子工具运行停止    llSensor    关闭
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    液位传感器在位状态-1     在位
    # sleep    2m
    # ${名称1}    获取web实时数据    液位传感器在位状态-1
    # Comment    ${名称2}    获取web实时数据    液位传感器在位状态-2
    # should be equal    '${名称1}'    '在位'
    # Comment    should be equal    '${名称2}'    '在位'
    显示属性配置    液位传感器通讯状态    数字量    web_attr=Off    gui_attr=Off
    控制子工具运行停止    llSensor    开启
    sleep    2m
    [Teardown]    控制子工具运行停止    llSensor    开启

液位传感器工作状态获取测试
    连接CSU
    显示属性配置    液位传感器工作状态    数字量    web_attr=On    gui_attr=On
    设置子工具个数    llSensor    1
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    液位传感器工作状态-1     正常
    # sleep    2m
    # ${名称1}    获取web实时数据    液位传感器工作状态-1
    # Comment    ${名称2}    获取web实时数据    液位传感器工作状态-2
    # should be equal    '${名称1}'    '正常'
    # Comment    should be equal    '${名称2}'    '正常'
    设置子工具个数    llSensor    1
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    液位传感器工作状态-1     正常
    # sleep    2m
    # ${名称1}    获取web实时数据    液位传感器工作状态-1
    # Comment    ${名称2}    获取web实时数据    液位传感器工作状态-2
    # should be equal    '${名称1}'    '正常'
    # Comment    should be equal    '${名称2}'    '通讯断'
    控制子工具运行停止    llSensor    关闭
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    液位传感器工作状态-1     通讯断
    # sleep    2m
    # ${名称1}    获取web实时数据    液位传感器工作状态-1
    # Comment    ${名称2}    获取web实时数据    液位传感器工作状态-2
    # should be equal    '${名称1}'    '通讯断'
    # Comment    should be equal    '${名称2}'    '通讯断'
    显示属性配置    液位传感器通讯状态    数字量    web_attr=Off    gui_attr=Off
    控制子工具运行停止    llSensor    开启
    sleep    2m
    [Teardown]    控制子工具运行停止    llSensor    开启

液位传感器通讯状态获取测试
    连接CSU
    显示属性配置    液位传感器通讯状态    数字量    web_attr=On    gui_attr=On
    设置子工具个数    llSensor    1
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    液位传感器通讯状态-1     正常
    # sleep    2m
    # ${名称1}    获取web实时数据    液位传感器通讯状态-1
    # Comment    ${名称2}    获取web实时数据    液位传感器通讯状态-2
    # should be equal    '${名称1}'    '正常'
    # Comment    should be equal    '${名称2}'    '正常'
    设置子工具个数    llSensor    1
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    液位传感器通讯状态-1     正常
    # sleep    2m
    # ${名称1}    获取web实时数据    液位传感器通讯状态-1
    # Comment    ${名称2}    获取web实时数据    液位传感器通讯状态-2
    # should be equal    '${名称1}'    '正常'
    # Comment    should be equal    '${名称2}'    '异常'
    控制子工具运行停止    llSensor    关闭
    Wait Until Keyword Succeeds    5m    1    信号量数据值为(强制获取)    液位传感器通讯状态-1     异常
    # sleep    2m
    # ${名称1}    获取web实时数据    液位传感器通讯状态-1
    # Comment    ${名称2}    获取web实时数据    液位传感器通讯状态-2
    # should be equal    '${名称1}'    '异常'
    # Comment    should be equal    '${名称2}'    '异常'
    显示属性配置    液位传感器通讯状态    数字量    web_attr=Off    gui_attr=Off
    控制子工具运行停止    llSensor    开启
    sleep    2m
    [Teardown]    控制子工具运行停止    llSensor    开启
