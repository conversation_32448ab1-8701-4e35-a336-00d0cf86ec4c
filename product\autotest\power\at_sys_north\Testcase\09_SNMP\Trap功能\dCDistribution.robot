*** Settings ***
Suite Setup        测试用例前置条件
Suite Teardown     测试用例后置条件
Default Tags      T1-1
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
snmp_0002_直流电压高√
    [Setup]    Run keywords    测试用例前置条件
    ...    AND    重置电池模拟器输出
    关闭交流源输出
    实时告警刷新完成
    #获取告警级别
    ${级别设置值}    获取web参数量    直流电压高
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    直流电压高    主要
    ${直流电压高告警}    判断告警不存在_带返回值    直流电压高
    run keyword if    '${直流电压高告警}' != '[]'    向下调节电池电压    52
    ${可设置范围}    获取web参数可设置范围    直流电压高阈值
    设置web参数量    直流电压高阈值    ${可设置范围}[0]
    ${ 直流电压高阈值}    获取web参数量    直流电压高阈值
    向上调节电池电压    ${ 直流电压高阈值}+0.5
    wait until keyword succeeds    5m    2    查询指定告警信息    直流电压高
    ###以上告警已产生
    wait until keyword succeeds    30    1    设置web参数量    直流电压高    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    直流电压高
    ${直流电压高告警}    判断告警存在_带返回值    直流电压高
    should not be true    ${直流电压高告警}
    #已产生，先屏蔽
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    wait until keyword succeeds    30    1    设置web参数量    直流电压高    主要
    wait until keyword succeeds    1m    1    判断告警存在    直流电压高
    #产生
    ${snmp英文名}    获取snmp单个告警英文名    直流电压高
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    #无告警后查询判断
    向下调节电池电压    ${ 直流电压高阈值}-0.5
    wait until keyword succeeds    5m    1    判断告警不存在    直流电压高
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    Run keywords    直流电压参数恢复默认值
    ...    AND    设置web参数量    直流电压高    主要
    ...    AND    重置电池模拟器输出
    ...    AND    打开交流源输出

snmp_0004_直流电压过高√
    [Documentation]    干接点实际是否动作需要将输出干接点连接到输入干接点，输出干接点接UIB_X3_DI1；请在测试前连接好
    [Setup]    重置电池模拟器输出
    关闭交流源输出
    实时告警刷新完成
    #获取告警级别
    ${级别设置值}    获取web参数量    直流电压过高
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    直流电压过高    主要
    ${直流电压过高告警}    判断告警不存在_带返回值    直流电压过高
    run keyword if    '${直流电压过高告警}' != '[]'    向下调节电池电压    52
    ${可设置范围}    获取web参数可设置范围    直流电压高阈值
    设置web参数量    直流电压高阈值    ${可设置范围}[0]
    ${可设置范围}    获取web参数可设置范围    直流电压过高阈值
    设置web参数量    直流电压过高阈值    ${可设置范围}[0]
    ${ 直流电压过高阈值}    获取web参数量    直流电压过高阈值
    向上调节电池电压    ${ 直流电压过高阈值}+0.5
    wait until keyword succeeds    5m    2    查询指定告警信息    直流电压过高
    ###以上告警已产生
    wait until keyword succeeds    30    1    设置web参数量    直流电压过高    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    直流电压过高
    ${直流电压过高告警}    判断告警存在_带返回值    直流电压过高
    should not be true    ${直流电压过高告警}
    #已产生，先屏蔽
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    wait until keyword succeeds    30    1    设置web参数量    直流电压过高    主要
    wait until keyword succeeds    1m    1    判断告警存在    直流电压过高
    #产生
    ${snmp英文名}    获取snmp单个告警英文名    直流电压过高
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    #无告警后查询判断
    向下调节电池电压    ${ 直流电压过高阈值}-0.5
    wait until keyword succeeds    5m    1    判断告警不存在    直流电压过高
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    Run keywords    直流电压参数恢复默认值
    ...    AND    设置web参数量    直流电压过高    主要
    ...    AND    重置电池模拟器输出
    ...    AND    打开交流源输出

snmp_0006_直流电压低√
    [Documentation]    直流电压高范围：53-59，默认58.5；>=直流电压低+1，<=直流电压过高。。。
    ...    直流电压低范围：44-55，默认46.5；<=直流电压高-1，>=直流电压过低。。。
    ...    直流电压过高：53-59，默认59。。。
    ...    直流电压过低：44-55，默认46.。。
    [Setup]    重置电池模拟器输出
    关闭交流源输出
    实时告警刷新完成
    #获取参数/默认值
    ${级别设置值}    获取web参数量    直流电压低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    直流电压低    主要
    ${直流电压低告警}    判断告警不存在_带返回值    直流电压低
    run keyword if    '${直流电压低告警}' != '[]'    向上调节电池电压    53.5
    ${可设置范围}    获取web参数可设置范围    直流电压低阈值
    设置web参数量    直流电压低阈值    ${可设置范围}[1]
    ${ 直流电压低阈值}    获取web参数量    直流电压低阈值
    向下调节电池电压    ${ 直流电压低阈值}-0.5
    wait until keyword succeeds    5m    2    查询指定告警信息    直流电压低
    ###以上告警已产生
    wait until keyword succeeds    30    1    设置web参数量    直流电压低    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    直流电压低
    ${直流电压低告警}    判断告警存在_带返回值    直流电压低
    should not be true    ${直流电压低告警}
    #已产生，先屏蔽
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    wait until keyword succeeds    30    1    设置web参数量    直流电压低    主要
    wait until keyword succeeds    1m    1    查询指定告警信息    直流电压低
    打印web实时告警信息名称
    #产生
    ${snmp英文名}    获取snmp单个告警英文名    直流电压低
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    #无告警后查询判断
    向上调节电池电压    ${ 直流电压低阈值}+1
    wait until keyword succeeds    5m    1    判断告警不存在    直流电压低
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    Run keywords    直流电压参数恢复默认值
    ...    AND    设置web参数量    直流电压低    主要
    ...    AND    重置电池模拟器输出
    ...    AND    打开交流源输出

snmp_0008_直流电压过低√
    [Documentation]    直流电压高范围：53-59，默认58.5；>=直流电压低+1，<=直流电压过高。。。
    ...    直流电压低范围：44-55，默认46.5；<=直流电压高-1，>=直流电压过低。。。
    ...    直流电压过高：53-59，默认59。。。
    ...    直流电压过低：44-55，默认46.。。
    [Setup]    重置电池模拟器输出
    关闭交流源输出
    实时告警刷新完成
    #获取告警级别
    ${级别设置值}    获取web参数量    直流电压过低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    直流电压过低    主要
    Comment    wait until keyword succeeds    5m    1    查询指定告警信息不为    直流电压高
    ${直流电压过高告警}    判断告警不存在_带返回值    直流电压过低
    run keyword if    '${直流电压过高告警}' != '[]'    向上调节电池电压    53.5
    ${可设置范围}    获取web参数可设置范围    直流电压低阈值
    设置web参数量    直流电压低阈值    ${可设置范围}[1]
    ${可设置范围}    获取web参数可设置范围    直流电压过低阈值
    设置web参数量    直流电压过低阈值    ${可设置范围}[1]
    ${ 直流电压过低阈值}    获取web参数量    直流电压过低阈值
    向下调节电池电压    ${ 直流电压过低阈值}-0.5
    wait until keyword succeeds    5m    2    查询指定告警信息    直流电压过低
    ###以上告警已产生
    wait until keyword succeeds    30    1    设置web参数量    直流电压过低    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    直流电压过低
    ${直流电压过低告警}    判断告警存在_带返回值    直流电压过低
    should not be true    ${直流电压过低告警}
    #已产生，先屏蔽
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    wait until keyword succeeds    30    1    设置web参数量    直流电压过低    主要
    wait until keyword succeeds    1m    1    查询指定告警信息    直流电压过低
    获取系统时间
    #产生
    ${snmp英文名}    获取snmp单个告警英文名    直流电压过低
    Comment    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    Comment    should be true    ${告警产生}
    FOR    ${i}    IN RANGE    0    4
        ${告警产生}    判断Trap告警产生    ${snmp英文名}
        Exit For Loop if    '${告警产生}'=='True'
        sleep    10
    END
    #消失
    获取系统时间
    #无告警后查询判断
    向上调节电池电压    ${ 直流电压过低阈值}+1
    wait until keyword succeeds    5m    1    判断告警不存在    直流电压过低
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    Run keywords    直流电压参数恢复默认值
    ...    AND    设置web参数量    直流电压过低    主要
    ...    AND    重置电池模拟器输出
    ...    AND    打开交流源输出

snmp_0008_一次下电分路断
    [Setup]
    实时告警刷新完成
    ${级别设置值}    获取web参数量    一次下电分路断_1
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    一次下电分路断_1    主要
    wait until keyword succeeds    1m    1    判断告警不存在    一次下电分路断
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    模拟数字量告警    下电告警    ON
    FOR    ${分路序号}    IN RANGE    4    1    -1
        wait until keyword succeeds    10m    1    判断告警存在    一次下电分路断_${分路序号}
        sleep    10
        ${snmp英文名}    获取snmp重构告警英文名    一次下电分路断_${分路序号}    直流配电
        ${告警产生}    判断Trap告警产生    ${snmp英文名}
        should be true    ${告警产生}
    END
    模拟数字量告警    下电告警    OFF
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    一次下电分路断
    FOR    ${分路序号}    IN RANGE    4    1    -1
        ${snmp英文名}    获取snmp重构告警英文名    一次下电分路断_${分路序号}    直流配电
        ${告警产生}    判断Trap告警消失    ${snmp英文名}
        should be true    ${告警产生}
    END
    [Teardown]    run keywords    设置web设备参数量为默认值    一次下电分路断_1
    ...    AND    模拟数字量告警    下电告警    OFF

snmp_0008_二次下电分路断
    [Setup]
    实时告警刷新完成
    ${级别设置值}    获取web参数量    二次下电分路断_1
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    二次下电分路断_1    主要
    wait until keyword succeeds    1m    1    判断告警不存在    二次下电分路断
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    模拟数字量告警    下电告警    ON
    FOR    ${分路序号}    IN RANGE    4    1    -1
        wait until keyword succeeds    1m    1    判断告警存在    二次下电分路断_${分路序号}
        sleep    5
        打印web实时告警信息名称
        ${snmp英文名}    获取snmp重构告警英文名    二次下电分路断_${分路序号}    直流配电
        ${告警产生}    判断Trap告警产生    ${snmp英文名}
        should be true    ${告警产生}
    END
    模拟数字量告警    下电告警    OFF
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    二次下电分路断
    FOR    ${分路序号}    IN RANGE    4    1    -1
        ${snmp英文名}    获取snmp重构告警英文名    二次下电分路断_${分路序号}    直流配电
        ${告警产生}    判断Trap告警消失    ${snmp英文名}
        should be true    ${告警产生}
    END
    [Teardown]    run keywords    设置web设备参数量为默认值    二次下电分路断_1
    ...    AND    模拟数字量告警    下电告警    OFF

snmp_0008_一次下电扩展分路断
    [Setup]
    实时告警刷新完成
    ${级别设置值}    获取web参数量    一次下电扩展分路断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    一次下电扩展分路断    主要
    wait until keyword succeeds    1m    1    判断告警不存在    一次下电扩展分路断
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    模拟数字量告警    下电告警    ON
    wait until keyword succeeds    1m    1    判断告警存在    一次下电扩展分路断
    sleep    5
    ${snmp英文名}    获取snmp单个告警英文名    一次下电扩展分路断
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    模拟数字量告警    下电告警    OFF
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    一次下电扩展分路断
    ${snmp英文名}    获取snmp单个告警英文名    一次下电扩展分路断
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    run keywords    设置web设备参数量为默认值    一次下电扩展分路断
    ...    AND    模拟数字量告警    下电告警    OFF

snmp_0008_二次下电扩展分路断
    [Setup]
    实时告警刷新完成
    ${级别设置值}    获取web参数量    二次下电扩展分路断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    二次下电扩展分路断    主要
    wait until keyword succeeds    1m    1    判断告警不存在    二次下电扩展分路断
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    模拟数字量告警    下电告警    ON
    wait until keyword succeeds    1m    1    判断告警存在    二次下电扩展分路断
    sleep    5
    ${snmp英文名}    获取snmp单个告警英文名    二次下电扩展分路断
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    模拟数字量告警    下电告警    OFF
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    二次下电扩展分路断
    ${snmp英文名}    获取snmp单个告警英文名    二次下电扩展分路断
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    run keywords    设置web设备参数量为默认值    二次下电扩展分路断
    ...    AND    模拟数字量告警    下电告警    OFF

# snmp_0008_电池下电分路断
#     [Setup]
#     实时告警刷新完成
#     ${级别设置值}    获取web参数量    电池下电分路断_1
#     run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池下电分路断_1    主要
#     wait until keyword succeeds    1m    1    判断告警不存在    电池下电分路断
#     ${清零Trap缓存}    清零Trap缓存
#     should be true    ${清零Trap缓存}
#     模拟数字量告警    下电告警    ON
#     FOR    ${分路序号}    IN RANGE    4    1    -1
#         wait until keyword succeeds    10m    1    判断告警存在    电池下电分路断_${分路序号}
#         sleep    5
#         ${snmp英文名}    获取snmp重构告警英文名    电池下电分路断_${分路序号}    直流配电
#         ${告警产生}    判断Trap告警产生    ${snmp英文名}
#         should be true    ${告警产生}
#     END
#     模拟数字量告警    下电告警    OFF
#     Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池下电分路断
#     FOR    ${分路序号}    IN RANGE    4    1    -1
#         ${snmp英文名}    获取snmp重构告警英文名    电池下电分路断_${分路序号}    直流配电
#         ${告警产生}    判断Trap告警消失    ${snmp英文名}
#         should be true    ${告警产生}
#     END
#     [Teardown]    run keywords    设置web设备参数量为默认值    电池下电分路断_1
#     ...    AND    模拟数字量告警    下电告警    OFF

# snmp_0008_电池下电扩展分路断
#     [Setup]
#     实时告警刷新完成
#     ${级别设置值}    获取web参数量    电池下电扩展分路断
#     run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池下电扩展分路断    主要
#     wait until keyword succeeds    1m    1    判断告警不存在    电池下电扩展分路断
#     ${清零Trap缓存}    清零Trap缓存
#     should be true    ${清零Trap缓存}
#     模拟数字量告警    下电告警    ON
#     wait until keyword succeeds    1m    1    判断告警存在    电池下电扩展分路断
#     sleep    5
#     ${snmp英文名}    获取snmp单个告警英文名    电池下电扩展分路断
#     ${告警产生}    判断Trap告警产生    ${snmp英文名}
#     should be true    ${告警产生}
#     模拟数字量告警    下电告警    OFF
#     Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池下电扩展分路断
#     ${snmp英文名}    获取snmp单个告警英文名    电池下电扩展分路断
#     ${告警产生}    判断Trap告警消失    ${snmp英文名}
#     should be true    ${告警产生}
#     [Teardown]    run keywords    设置web设备参数量为默认值    电池下电扩展分路断
#     ...    AND    模拟数字量告警    下电告警    OFF

snmp_0008_直流防雷器异常
    [Setup]
    实时告警刷新完成
    ${级别设置值}    获取web参数量    直流防雷器异常
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    直流防雷器异常    主要
    wait until keyword succeeds    1m    1    判断告警不存在    直流防雷器异常
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    模拟数字量告警    直流防雷器异常    ON
    wait until keyword succeeds    1m    1    判断告警存在    直流防雷器异常
    sleep    5
    ${snmp英文名}    获取snmp单个告警英文名    直流防雷器异常
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    模拟数字量告警    直流防雷器异常    OFF
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    直流防雷器异常
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    run keywords    设置web设备参数量为默认值    直流防雷器异常
    ...    AND    模拟数字量告警    直流防雷器异常    OFF
