*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
1363批量获取交流电表模拟量FF
    [Documentation]    @{交流电表排除模拟量信号} \ \ \ 交流电表回路1总有功功率~0x1e001010030001 \ \ \ 交流电表回路1电量~0x1e001010070001 \ \ \ 交流电表回路2总有功功率~0x1e0010100a0001 \ \ \ 交流电表回路2电量~0x1e0010100e0001 \ \ \ 交流电表回路3电压~0x1e0010100f0001 \ \ \ 交流电表回路3电流~0x1e001010100001 \ \ \ 交流电表回路3总有功功率~0x1e001010110001 \ \ \ 交流电表回路3总功率因数~0x1e001010120001 \ \ \ 交流电表回路3相功率因数~0x1e001010130001 \ \ \ 交流电表回路3频率~0x1e001010140001 \ \ \ 交流电表回路3电量~0x1e001010150001 \ \ \ 交流电表回路4电压~0x1e001010160001 \ \ \ 交流电表回路4电流~0x1e001010170001 \ \ \ 交流电表回路4总有功功率~0x1e001010180001 \ \ \ 交流电表回路4总功率因数~0x1e001010190001 \ \ \ 交流电表回路4相功率因数~0x1e0010101a0001 \ \ \ 交流电表回路4频率~0x1e0010101b0001
    ...    ... \ \ \ \ \ \ \ \ \ \ \ \ \ \ 交流电表回路4电量~0x1e0010101c0001
    写入CSV文档    交流电表模拟量获取测试    信号名称    信号值    结果    备注
    连接CSU
    P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=交流电表
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${交流电表排除模拟量信号}    ${排除列表}    1
    ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取交流电表模拟量~CID1=D2H>>    <<获取交流电表模拟量~CID2=42H>>    FF    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${1104待测}    1104_获取1104/1363指定设备指定信号量    ${协议数据}    ${web数据}    ${列表1}    FF
    ${缺省值列表1}    获取缺省值列表    ${1104待测}    1    1363
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    1363    pu    模拟量    ${缺省值列表1}    PU模拟量获取测试    null    <<获取交流电表模拟量~CID1=D2H>>    <<获取交流电表模拟量~CID2=42H>>    None
    ${缺省值列表2}    获取缺省值列表    ${1104待测}    2    1363
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    1363    pu    模拟量    ${缺省值列表2}    PU模拟量获取测试    null    <<获取交流电表模拟量~CID1=D2H>>    <<获取交流电表模拟量~CID2=42H>>    None
    ${缺省值列表0}    获取缺省值列表    ${1104待测}    0    1363
    # ${缺省值列表0}    1104检查缺省值列表    ${缺省值列表0}    ${缺省值列表1}    ${缺省值列表2} 
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    1363    pu    模拟量    ${缺省值列表0}    PU模拟量获取测试    null    <<获取交流电表模拟量~CID1=D2H>>    <<获取交流电表模拟量~CID2=42H>>    None


1363批量获取交流电表模拟量01
    [Documentation]    @{交流电表排除模拟量信号} \ \ \ 交流电表回路1总有功功率~0x1e001010030001 \ \ \ 交流电表回路1电量~0x1e001010070001 \ \ \ 交流电表回路2总有功功率~0x1e0010100a0001 \ \ \ 交流电表回路2电量~0x1e0010100e0001 \ \ \ 交流电表回路3电压~0x1e0010100f0001 \ \ \ 交流电表回路3电流~0x1e001010100001 \ \ \ 交流电表回路3总有功功率~0x1e001010110001 \ \ \ 交流电表回路3总功率因数~0x1e001010120001 \ \ \ 交流电表回路3相功率因数~0x1e001010130001 \ \ \ 交流电表回路3频率~0x1e001010140001 \ \ \ 交流电表回路3电量~0x1e001010150001 \ \ \ 交流电表回路4电压~0x1e001010160001 \ \ \ 交流电表回路4电流~0x1e001010170001 \ \ \ 交流电表回路4总有功功率~0x1e001010180001 \ \ \ 交流电表回路4总功率因数~0x1e001010190001 \ \ \ 交流电表回路4相功率因数~0x1e0010101a0001 \ \ \ 交流电表回路4频率~0x1e0010101b0001
    ...    ... \ \ \ \ \ \ \ \ \ \ \ \ \ \ 交流电表回路4电量~0x1e0010101c0001
    写入CSV文档    交流电表模拟量获取测试    信号名称    信号值    结果    备注
    连接CSU
    P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=交流电表
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${交流电表排除模拟量信号}    ${排除列表}    1
    ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取交流电表模拟量~CID1=D2H>>    <<获取交流电表模拟量~CID2=42H>>    01    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${1104待测}    1104_获取1104/1363指定设备指定信号量    ${协议数据}    ${web数据}    ${列表1}
    ${缺省值列表1}    获取缺省值列表    ${1104待测}    1    1363
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    1363    pu    模拟量    ${缺省值列表1}    PU模拟量获取测试    null    <<获取交流电表模拟量~CID1=D2H>>    <<获取交流电表模拟量~CID2=42H>>    None
    ${缺省值列表2}    获取缺省值列表    ${1104待测}    2    1363
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    1363    pu    模拟量    ${缺省值列表2}    PU模拟量获取测试    null    <<获取交流电表模拟量~CID1=D2H>>    <<获取交流电表模拟量~CID2=42H>>    None
    ${缺省值列表0}    获取缺省值列表    ${1104待测}    0    1363
    # ${缺省值列表0}    1104检查缺省值列表    ${缺省值列表0}    ${缺省值列表1}    ${缺省值列表2} 
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    1363    pu    模拟量    ${缺省值列表0}    PU模拟量获取测试    null    <<获取交流电表模拟量~CID1=D2H>>    <<获取交流电表模拟量~CID2=42H>>    None


获取交流电表回路1电量
    连接CSU
    设置子工具值    ACmeter    all    只读    1路当前组合有功电能    655.34
    FOR    ${交流电表序号}    IN RANGE    1    4
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表回路1电量-${交流电表序号}    655.34
        1104/1363_南向子设备模拟量/数字量获取    655.34    1363    <<获取交流电表模拟量~CID1=D2H>>    <<获取交流电表模拟量~CID2=42H>>    回路1电量-${交流电表序号}    FF
    END
    1104/1363_南向子设备模拟量/数字量获取    655.34    1363    <<获取交流电表模拟量~CID1=D2H>>    <<获取交流电表模拟量~CID2=42H>>    回路1电量-1    01
    设置子工具值    ACmeter    all    只读    1路当前组合有功电能    0
    FOR    ${交流电表序号}    IN RANGE    1    4
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表回路1电量-${交流电表序号}    0
        1104/1363_南向子设备模拟量/数字量获取    0    1363    <<获取交流电表模拟量~CID1=D2H>>    <<获取交流电表模拟量~CID2=42H>>    回路1电量-${交流电表序号}    FF
    END
    1104/1363_南向子设备模拟量/数字量获取    0    1363    <<获取交流电表模拟量~CID1=D2H>>    <<获取交流电表模拟量~CID2=42H>>    回路1电量-1    01

获取交流电表回路2电量
    连接CSU
    设置子工具值    ACmeter    all    只读    2路当前组合有功电能    655.34
    FOR    ${交流电表序号}    IN RANGE    1    4
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表回路2电量-${交流电表序号}    655.34
        1104/1363_南向子设备模拟量/数字量获取    655.34    1363    <<获取交流电表模拟量~CID1=D2H>>    <<获取交流电表模拟量~CID2=42H>>    回路2电量-${交流电表序号}    FF
    END
    1104/1363_南向子设备模拟量/数字量获取    655.34    1363    <<获取交流电表模拟量~CID1=D2H>>    <<获取交流电表模拟量~CID2=42H>>    回路2电量-1    01
    设置子工具值    ACmeter    all    只读    2路当前组合有功电能    0
    FOR    ${交流电表序号}    IN RANGE    1    4
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表回路2电量-${交流电表序号}    0
        1104/1363_南向子设备模拟量/数字量获取    0    1363    <<获取交流电表模拟量~CID1=D2H>>    <<获取交流电表模拟量~CID2=42H>>    回路2电量-${交流电表序号}    FF
    END
    1104/1363_南向子设备模拟量/数字量获取    0    1363    <<获取交流电表模拟量~CID1=D2H>>    <<获取交流电表模拟量~CID2=42H>>    回路2电量-1    01

获取交流电表回路1总有功功率
    连接CSU
    设置子工具值    ACmeter    all    只读    1路总有功功率    600
    sleep    30
    FOR    ${交流电表序号}    IN RANGE    1    4
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表回路1总有功功率-${交流电表序号}    600
        1104/1363_南向子设备模拟量/数字量获取    600    1363    <<获取交流电表模拟量~CID1=D2H>>    <<获取交流电表模拟量~CID2=42H>>    回路1总有功-${交流电表序号}    FF
    END
    1104/1363_南向子设备模拟量/数字量获取    600    1363    <<获取交流电表模拟量~CID1=D2H>>    <<获取交流电表模拟量~CID2=42H>>    回路1总有功-1    01
    设置子工具值    ACmeter    all    只读    1路总有功功率    220
    sleep    30
    FOR    ${交流电表序号}    IN RANGE    1    4
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表回路1总有功功率-${交流电表序号}    220
        1104/1363_南向子设备模拟量/数字量获取    220    1363    <<获取交流电表模拟量~CID1=D2H>>    <<获取交流电表模拟量~CID2=42H>>    回路1总有功-${交流电表序号}    FF
    END
    1104/1363_南向子设备模拟量/数字量获取    220    1363    <<获取交流电表模拟量~CID1=D2H>>    <<获取交流电表模拟量~CID2=42H>>    回路1总有功-1    01
    设置子工具值    ACmeter    all    只读    1路总有功功率    0.1
    FOR    ${交流电表序号}    IN RANGE    1    4
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表回路1总有功功率-${交流电表序号}    0.1
        1104/1363_南向子设备模拟量/数字量获取    0.1    1363    <<获取交流电表模拟量~CID1=D2H>>    <<获取交流电表模拟量~CID2=42H>>    回路1总有功-${交流电表序号}    FF
    END
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表回路1总有功功率-1    0.1
    1104/1363_南向子设备模拟量/数字量获取    0.1    1363    <<获取交流电表模拟量~CID1=D2H>>    <<获取交流电表模拟量~CID2=42H>>    回路1总有功-1    01

获取交流电表回路2总有功功率
    连接CSU
    设置子工具值    ACmeter    all    只读    2路总有功功率    600
    FOR    ${交流电表序号}    IN RANGE    1    4
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表回路2总有功功率-${交流电表序号}    600
        1104/1363_南向子设备模拟量/数字量获取    600    1363    <<获取交流电表模拟量~CID1=D2H>>    <<获取交流电表模拟量~CID2=42H>>    回路2总有功-${交流电表序号}    FF
    END
    1104/1363_南向子设备模拟量/数字量获取    600    1363    <<获取交流电表模拟量~CID1=D2H>>    <<获取交流电表模拟量~CID2=42H>>    回路2总有功-1    01
    设置子工具值    ACmeter    all    只读    2路总有功功率    220
    FOR    ${交流电表序号}    IN RANGE    1    4
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表回路2总有功功率-${交流电表序号}    220
        1104/1363_南向子设备模拟量/数字量获取    220    1363    <<获取交流电表模拟量~CID1=D2H>>    <<获取交流电表模拟量~CID2=42H>>    回路2总有功-${交流电表序号}    FF
    END
    1104/1363_南向子设备模拟量/数字量获取    220    1363    <<获取交流电表模拟量~CID1=D2H>>    <<获取交流电表模拟量~CID2=42H>>    回路2总有功-1    01
    设置子工具值    ACmeter    all    只读    2路总有功功率    0.1
    FOR    ${交流电表序号}    IN RANGE    1    4
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    交流电表回路2总有功功率-${交流电表序号}    0.1
        1104/1363_南向子设备模拟量/数字量获取    0.1    1363    <<获取交流电表模拟量~CID1=D2H>>    <<获取交流电表模拟量~CID2=42H>>    回路2总有功-${交流电表序号}    FF
    END
    1104/1363_南向子设备模拟量/数字量获取    0.1    1363    <<获取交流电表模拟量~CID1=D2H>>    <<获取交流电表模拟量~CID2=42H>>    回路2总有功-1    01
