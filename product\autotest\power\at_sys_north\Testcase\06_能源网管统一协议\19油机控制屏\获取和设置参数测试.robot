*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_获取油机控制屏所有模拟量
    ${能源网管数据}    能源网管协议_获取数据    获取油机控制屏模拟量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取油机控制屏模拟量
    Should Be True    ${数据对比结果}
    ${校验结果}    能源网管协议_校验数据上送完整性    ${能源网管数据}    油机控制屏    analog data
    Comment   Should Be True    ${校验结果}

power_sm_0002_获取油机控制屏所有参数量
    [Tags]    4
    ${能源网管数据}    能源网管协议_获取数据    获取油机控制屏参数量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取油机控制屏参数量
    Should Be True    ${数据对比结果}
    ${校验结果}    能源网管协议_校验数据上送完整性    ${能源网管数据}    油机控制屏    parameter
    Comment    Should Be True    ${校验结果}

power_sm_0003_设置油机控制屏参数量
    [Tags]    4
    ${sheet_name}    Set Variable    设置油机控制屏参数
    ${能源网管数据}    能源网管协议_设置单个参数    ${sheet_name}    ${BOARD_IP}    ${SSH}
    ${对比结果}    能源网管协议_批量对比参数设置    ${能源网管数据}    设置油机控制屏参数
    Should Be True    ${对比结果}

power_sm_0004_设置油机控制屏控制量
    sleep    3m
    ${能源网管数据}    能源网管协议_设置控制量    设置油机控制屏控制量    ${BOARD_IP}    ${SSH}
    ${对比结果}    能源网管协议_批量对比参数设置    ${能源网管数据}    设置油机控制屏控制量
    Should Be True    ${对比结果}

power_sm_0005_获取油机控制屏设备信息
    ${能源网管数据}    能源网管协议_获取数据    获取油机控制屏设备信息    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取油机控制屏设备信息
    Should Be True    ${数据对比结果}
    ${校验结果}    能源网管协议_校验数据上送完整性    ${能源网管数据}    油机控制屏    device info
    Comment    Should Be True    ${校验结果}
