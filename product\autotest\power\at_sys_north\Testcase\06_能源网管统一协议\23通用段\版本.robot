*** Settings ***
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_获取协议版本
    [Tags]    pass
    ${能源网管数据}    能源网管协议_获取数据    获取协议版本    ${SSH}
    ${对比结果}    能源网管协议_对比协议版本    ${能源网管数据}    V1.23.04.1    获取协议版本
    Should Be True    ${对比结果}
