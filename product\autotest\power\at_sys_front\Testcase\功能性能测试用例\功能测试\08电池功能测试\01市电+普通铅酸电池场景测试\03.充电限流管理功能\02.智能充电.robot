*** Settings ***
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
不满足智能充电条件下的充电限流-F
    [Tags]    notest
    
    关闭负载输出
    关闭交流源输出
    关闭电池模拟器输出
    sleep    10
    设置电池模拟器模式    铅酸电池    25    5    50
    电池模拟器接触器分合    ON
    打开电池模拟器输出
    连接CSU
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    停电
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    系统交流输入    交流供电状态    value=4
    Wait Until Keyword Succeeds    5m    1s    设置web参数量    电池组    电池组容量    1    值=100
    设置web参数量    电池组    电池组容量    2    值=0
    设置web参数量    电池组    电池组容量    3    值=0
    设置web参数量    电池组    电池组容量    4    值=0
    Comment    设置web参数量    电池组    电池充电模式    值=1    #默认0,普通充电
    设置web参数量    电池组    电池充电电流系数    值=0.15    #默认0.15
    设置web参数量    电池组    均充使能    值=1
    sleep    3
    ${电池组容量设置}    获取web参数量    电池组    电池组容量    signal_index=1
    ${充电电流系数设置}    获取web参数量    电池组    电池充电电流系数
    ${充电电流设置}    evaluate    ${电池组容量设置}*${充电电流系数设置}
    Wait Until Keyword Succeeds    2m    1    设置web控制量    电池组    启动均充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池组    电池管理状态    value=1
    ${当前电池电流_1}    获取web模拟量    单组电池_1    电池电流
    should be true    ${当前电池电流_1}<=1.1*${充电电流设置}
    Comment    设置web参数量    电池组    电池充电模式    值=0    #默认0,普通充电
    [Teardown]    电池管理参数恢复默认值

满足智能充电条件下的充电限流
    [Tags]    notest
    
    关闭负载输出
    关闭交流源输出
    关闭电池模拟器输出
    sleep    10
    设置电池模拟器模式    铅酸电池    25    5    50
    电池模拟器接触器分合    ON
    打开电池模拟器输出
    连接CSU
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    停电
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    系统交流输入    交流供电状态    value=4
    Wait Until Keyword Succeeds    5m    1s    设置web参数量    电池组    电池组容量    1    值=100
    设置web参数量    电池组    电池组容量    2    值=0
    设置web参数量    电池组    电池组容量    3    值=0
    设置web参数量    电池组    电池组容量    4    值=0
    Comment    设置web参数量    电池组    电池充电模式    值=1    #默认0,普通充电
    设置web参数量    电池组    电池充电电流系数    值=0.15    #默认0.15
    设置web参数量    电池组    均充使能    值=1
    sleep    3
    #制造9次交流停电次数,满足智能充电条件
    FOR    ${停电次数}    IN RANGE    9
        关闭交流源输出
        Wait Until Keyword Succeeds    5m    1    信号量数据值为    系统交流输入    交流供电状态    value=0
        打开交流源输出
        Wait Until Keyword Succeeds    5m    1    信号量数据值为    系统交流输入    交流供电状态    value=4
        sleep    3
    END
    ${电池组容量设置}    获取web参数量    电池组    电池组容量    signal_index=1
    ${充电电流系数设置}    获取web参数量    电池组    电池充电电流系数
    ${充电电流设置}    evaluate    ${电池组容量设置}*${充电电流系数设置}
    Wait Until Keyword Succeeds    2m    1    设置web控制量    电池组    启动均充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池组    电池管理状态    value=1
    ${当前电池电流_1}    获取web模拟量    单组电池_1    电池电流
    should be true    ${当前电池电流_1}<=1.1*0.2*${电池组容量设置}
    Comment    设置web参数量    电池组    电池充电模式    值=0    #默认0,普通充电
    [Teardown]    电池管理参数恢复默认值

智能充电条件下的容量变化-F
    [Tags]    notest
    
    关闭负载输出
    关闭交流源输出
    关闭电池模拟器输出
    sleep    10
    设置电池模拟器模式    铅酸电池    24    5    50
    电池模拟器接触器分合    ON
    打开电池模拟器输出
    连接CSU
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    停电
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    系统交流输入    交流供电状态    value=4
    Wait Until Keyword Succeeds    5m    1s    设置web参数量    电池组    电池组容量    1    值=200
    设置web参数量    电池组    电池组容量    2    值=0
    设置web参数量    电池组    电池组容量    3    值=0
    设置web参数量    电池组    电池组容量    4    值=0
    Comment    设置web参数量    电池组    电池充电模式    值=1    #默认0,普通充电
    设置web参数量    电池组    电池充电电流系数    值=0.15    #默认0.15
    设置web参数量    电池组    均充使能    值=1
    sleep    3
    #制造9次交流停电次数,满足智能充电条件
    FOR    ${停电次数}    IN RANGE    9
        关闭交流源输出
        Wait Until Keyword Succeeds    5m    1    信号量数据值为    系统交流输入    交流供电状态    value=0
        打开交流源输出
        Wait Until Keyword Succeeds    5m    1    信号量数据值为    系统交流输入    交流供电状态    value=4
        sleep    3
    END
    ${电池组容量设置}    获取web参数量    电池组    电池组容量    signal_index=1
    ${充电电流系数设置}    获取web参数量    电池组    电池充电电流系数
    ${充电电流设置}    evaluate    ${电池组容量设置}*${充电电流系数设置}
    Wait Until Keyword Succeeds    2m    1    设置web控制量    电池组    启动均充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池组    电池管理状态    value=1
    ${当前电池电流_1}    获取web模拟量    单组电池_1    电池电流
    should be true    ${当前电池电流_1}<=1.1*0.2*${电池组容量设置}
    设置web参数量    电池组    电池组容量    1    值=100
    ${电池组容量设置}    获取web参数量    电池组    电池组容量    signal_index=1
    ${充电电流设置}    evaluate    ${电池组容量设置}*${充电电流系数设置}
    ${当前电池电流_1}    获取web模拟量    单组电池_1    电池电流
    should be true    ${当前电池电流_1}<=1.1*0.2*${电池组容量设置}
    Comment    设置web参数量    电池组    电池充电模式    值=0    #默认0,普通充电
    [Teardown]    电池管理参数恢复默认值

智能充电条件下的充电系数变化
    [Tags]    notest
    
    关闭负载输出
    关闭交流源输出
    关闭电池模拟器输出
    sleep    10
    设置电池模拟器模式    铅酸电池    25    5    50
    电池模拟器接触器分合    ON
    打开电池模拟器输出
    连接CSU
    同时设置三相电压频率    220    50
    打开交流源输出
    实时告警刷新完成
    Wait Until Keyword Succeeds    5m    1    查询指定告警信息不为    停电
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    系统交流输入    交流供电状态    value=4
    Wait Until Keyword Succeeds    5m    1s    设置web参数量    电池组    电池组容量    1    值=100
    设置web参数量    电池组    电池组容量    2    值=0
    设置web参数量    电池组    电池组容量    3    值=0
    设置web参数量    电池组    电池组容量    4    值=0
    Comment    设置web参数量    电池组    电池充电模式    值=1    #默认0,普通充电
    设置web参数量    电池组    电池充电电流系数    值=0.15    #默认0.15
    设置web参数量    电池组    均充使能    值=1
    sleep    3
    #制造9次交流停电次数,满足智能充电条件
    FOR    ${停电次数}    IN RANGE    9
        关闭交流源输出
        Wait Until Keyword Succeeds    5m    1    信号量数据值为    系统交流输入    交流供电状态    value=0
        打开交流源输出
        Wait Until Keyword Succeeds    5m    1    信号量数据值为    系统交流输入    交流供电状态    value=4
        sleep    3
    END
    ${电池组容量设置}    获取web参数量    电池组    电池组容量    signal_index=1
    ${充电电流系数设置}    获取web参数量    电池组    电池充电电流系数
    ${充电电流设置}    evaluate    ${电池组容量设置}*${充电电流系数设置}
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池组    电池管理状态    value=5
    Wait Until Keyword Succeeds    2m    1    设置web控制量    电池组    启动均充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池组    电池管理状态    value=1
    ${当前电池电流_1}    获取web模拟量    单组电池_1    电池电流
    should be true    ${当前电池电流_1}<=1.1*0.2*${电池组容量设置}
    设置web参数量    电池组    电池充电电流系数    值=0.1
    ${充电电流系数设置}    获取web参数量    电池组    电池充电电流系数
    ${充电电流设置}    evaluate    ${电池组容量设置}*${充电电流系数设置}
    ${当前电池电流_1}    获取web模拟量    单组电池_1    电池电流
    should be true    ${当前电池电流_1}<=1.1*0.2*${电池组容量设置}
    Comment    设置web参数量    电池组    电池充电模式    值=0    #默认0,普通充电
    [Teardown]    电池管理参数恢复默认值
