*** Settings ***
Suite Setup       Run Keywords   主动告警测试前置条件    ${CSU_role}
...   AND   测试用例前置条件
Suite Teardown    Run Keywords   设置web设备参数量为默认值    CSU主动告警使能
...   AND   测试用例后置条件
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Default Tags      T1-1
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0002_电池温度高告警
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接   
    同时设置三相电压频率    220    50
    打开交流源输出
    连接CSU
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    电池温度高
    #获取告警级别和输出干接点设置值
    ${级别设置值}    获取web参数量    电池温度高
    run keyword if    '${级别设置值}'=='屏蔽'    设置web设备参数量为默认值    电池温度高
    sleep    3
    ${级别设置值}    获取web参数量    电池温度高
    #（1）告警产生
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    10    1.6    电池_1    电池温度
    #产生电池温度高告警
    ${电池温度高阈值范围}    获取web参数上下限范围    电池温度高阈值
    ${电池温度高阈值可设置范围}    获取web参数可设置范围    电池温度高阈值
    设置web参数量    电池温度高阈值    ${电池温度高阈值可设置范围}[0]
    ${电池温度}    获取web实时数据    电池温度-1
    Wait Until Keyword Succeeds    10m    1    判断告警存在    电池温度高
    ${告警级别取值约定dict}    获取web参数的取值约定    电池温度高
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    电池温度高    ${告警级别设置}
        sleep   5
        wait until keyword succeeds    1m    1    查询指定告警信息    电池温度高
        ${web实时告警名}    由子工具获取web实时告警名称    电池温度高-1
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    电池_1    ${web实时告警名}
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    电池温度高-1    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池温度高    主要
    #（2）告警恢复
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    设置web参数量    电池温度高阈值    ${电池温度高阈值范围}[0]
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池温度高
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    电池温度高-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池温度高阈值
    ...    AND    设置web参数量    电池温度高    主要
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0003_电池温度无效告警
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    #电池2不接入传感器
    设置通道无效值/恢复通道原始值     ${plat.batttemps}[0]     1
    设置通道无效值/恢复通道原始值     ${plat.batttemps}[1]     1
    设置通道无效值/恢复通道原始值     ${plat.batttemps}[2]     1
    sleep    60
    实时告警刷新完成
    Wait Until Keyword Succeeds    1m    1    设置web参数量    电池组容量_2    100
    设置web参数量    电池温度无效    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    电池温度无效
    ${电池温度无效告警}    判断告警存在_带返回值    电池温度无效
    should not be true    ${电池温度无效告警}
    ${告警级别取值约定dict}    获取web参数的取值约定    电池温度无效
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        设置web参数量    电池温度无效-2    ${告警级别设置}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池温度无效
        sleep    20
        ${web实时告警名}    由子工具获取web实时告警名称    电池温度无效-2
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    电池_2    ${web实时告警名}
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    电池温度无效-2    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    #无告警后查询判断
    Wait Until Keyword Succeeds    30    1    设置web参数量    电池组容量_2    0
    设置通道无效值/恢复通道原始值     ${plat.batttemps}[0]     0
    设置通道无效值/恢复通道原始值     ${plat.batttemps}[1]     0
    设置通道无效值/恢复通道原始值     ${plat.batttemps}[2]     0
    wait until keyword succeeds    5m    1    判断告警不存在    电池温度无效
    sleep    5
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    电池温度无效-2    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置web参数量    电池组容量_2    0
    ...    AND    设置web设备参数量为默认值    电池温度无效
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
    ...    AND    设置通道无效值/恢复通道原始值     ${plat.batttemps}[0]     0
    ...    AND    设置通道无效值/恢复通道原始值     ${plat.batttemps}[1]     0
    ...    AND    设置通道无效值/恢复通道原始值     ${plat.batttemps}[2]     0

power_sm_0004_电池温度低告警
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    同时设置三相电压频率    220    50
    打开交流源输出
    连接CSU
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    电池温度低
    ${级别设置值}    获取web参数量    电池温度低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web设备参数量为默认值    电池温度低
    ${级别设置值}    获取web参数量    电池温度低
    #（1）告警产生
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    -10    0.001    电池_1    电池温度
    #产生电池温度低告警
    ${电池温度低阈值范围}    获取web参数上下限范围    电池温度低阈值
    ${电池温度低阈值可设置范围}    获取web参数可设置范围    电池温度低阈值
    设置web参数量    电池温度低阈值    ${电池温度低阈值可设置范围}[1]
    ${电池温度}    获取web实时数据    电池温度-1
    Wait Until Keyword Succeeds    10    1    判断告警存在    电池温度低
    ${告警级别取值约定dict}    获取web参数的取值约定    电池温度低
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        wait until keyword succeeds    30    1    设置web参数量    电池温度低    ${告警级别设置}
        sleep   5
        wait until keyword succeeds    1m    1    查询指定告警信息    电池温度低
        ${web实时告警名}    由子工具获取web实时告警名称    电池温度低-1
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    电池_1    ${web实时告警名}
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    电池温度低-1    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    Wait Until Keyword Succeeds    10    2    设置web参数量    电池温度低    主要
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    #（2）告警恢复
    设置web参数量    电池温度低阈值    ${电池温度低阈值范围}[0]
    Wait Until Keyword Succeeds    2m    1    判断告警不存在    电池温度低
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    电池温度低-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池温度低阈值
    ...    AND    设置web设备参数量为默认值    电池温度低
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0005_电池测试失败告警
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    电池管理初始化
    #获取告警级别和输出干接点设置
    ${电池测试失败级别}    获取web参数量    电池测试失败
    run keyword if    '${电池测试失败级别}'=='屏蔽'    设置web设备参数量为默认值    电池测试失败
    ${电池测试失败级别}    获取web参数量    电池测试失败
    ${缺省值}    获取web参数上下限范围_有单位    测试最长时间
    ${可设置范围}    获取web参数可设置范围    测试最长时间
    ${测试最长时间设置值}    run keyword if    '${缺省值}[3]'== 'Hour'    evaluate    ${可设置范围}[0]+1
    ...    ELSE    evaluate    ${可设置范围}[0]+5
    ${测试最长时间转换后}    run keyword if    '${缺省值}[3]'== 'Hour'    evaluate    ${测试最长时间设置值}*60
    ...    ELSE    set variable    ${测试最长时间设置值}
    设置web参数量    测试最长时间    ${测试最长时间设置值}
    #断开电池，以便电池检测异常
    设置负载电压电流    53.5    15
    打开负载输出
    断开电池模拟器
    连接电池模拟器
    关闭电池模拟器输出
    #启动电池测试
    Wait Until Keyword Succeeds    2m    1    设置web控制量    启动测试
    run keyword and ignore error    Wait Until Keyword Succeeds    10    1    信号量数据值为    电池管理状态    测试
    wait until keyword succeeds    15m    2    判断告警存在    电池测试失败
    ${告警级别取值约定dict}    获取web参数的取值约定    电池测试失败
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    电池测试失败    ${告警级别设置}
        sleep    3
        wait until keyword succeeds    1m    1    查询指定告警信息    电池测试失败
        ${web实时告警名}    由子工具获取web实时告警名称    电池测试失败-1
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    电池_1    ${web实时告警名}
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    电池测试失败-1    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    设置web参数量    电池测试失败    主要
    #恢复
    关闭负载输出
    仅电池模拟器供电
    同时设置三相电压频率    220    50
    打开交流源输出
    连接CSU
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    交流供电状态    市电1
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    停电
    Wait Until Keyword Succeeds    5m    2    信号量数据值大于    工作整流器数量    0
    设置web参数量    均充使能    允许
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动测试
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    测试
    ${等待时间}    evaluate    ${测试最长时间转换后}+5
    Wait Until Keyword Succeeds    ${等待时间}m    1    信号量数据值为    电池管理状态    均充
    wait until keyword succeeds    3m    2    判断告警不存在    电池测试失败
    Wait Until Keyword Succeeds    2m    1    设置web控制量    启动浮充
    Wait Until Keyword Succeeds    2m    1    信号量数据值为    电池管理状态    浮充
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    电池测试失败-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    Run keywords    设置web设备参数量为默认值    测试最长时间    电池测试失败
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出
    ...    AND    重置电池模拟器输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0006_电池电压低
    [Setup]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    纯铅酸
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接交流源
    连接电池模拟器
    重置电池模拟器输出
    打开电池模拟器输出
    sleep    10
    关闭交流源输出
    实时告警刷新完成
    #获取参数/默认值
    ${级别设置值}    获取web参数量    电池电压低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web设备参数量为默认值    电池电压低
    ${电压低告警}    判断告警不存在_带返回值    电池电压低
    run keyword if    '${电压低告警}' != '[]'    向上调节电池电压    53.5
    ${可设置范围}    获取web参数可设置范围    电池电压低阈值
    设置web参数量    电池电压低阈值    ${可设置范围}[1]
    ${ 电压低阈值}    获取web参数量    电池电压低阈值
    向下调节电池电压    ${ 电压低阈值}-0.5
    wait until keyword succeeds    5m    2    查询指定告警信息    电池电压低
    wait until keyword succeeds    30    1    设置web参数量    电池电压低    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    电池电压低
    ${电压低告警}    判断告警存在_带返回值    电池电压低
    should not be true    ${电压低告警}
    ${告警级别取值约定dict}    获取web参数的取值约定    电池电压低
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        设置web参数量    电池电压低    ${告警级别设置}
        sleep   5
        wait until keyword succeeds    1m    1    查询指定告警信息    电池电压低
        ${web实时告警名}    由子工具获取web实时告警名称    电池电压低-1
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    电池_1    ${web实时告警名}
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    电池电压低-1    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    设置web参数量    电池电压低    主要
    #无告警后查询判断
    向上调节电池电压    ${ 电压低阈值}+1
    wait until keyword succeeds    3m    1    判断告警不存在    电池电压低-1
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    电池电压低-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    Run keywords    设置web设备参数量为默认值    电池电压低阈值    电池电压低
    ...    AND    打开交流源输出
    ...    AND    重置电池模拟器输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0007_电池电压过低告警
    [Setup]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web参数量    电池配置    纯铅酸
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接交流源
    连接电池模拟器
    重置电池模拟器输出
    打开电池模拟器输出
    sleep    10
    关闭交流源输出
    实时告警刷新完成
    #获取参数/默认值
    ${级别设置值}    获取web参数量    电池电压过低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    电池电压过低    严重
    ${电压低告警}    判断告警不存在_带返回值    电池电压过低
    run keyword if    '${电压低告警}' != '[]'    向上调节电池电压    53.5
    ${可设置范围}    获取web参数可设置范围    电池电压过低阈值
    设置web参数量    电池电压过低阈值    ${可设置范围}[1]
    ${ 电压低阈值}    获取web参数量    电池电压过低阈值
    向下调节电池电压    ${ 电压低阈值}-0.5
    wait until keyword succeeds    5m    2    查询指定告警信息    电池电压过低
    wait until keyword succeeds    30    1    设置web参数量    电池电压过低    屏蔽
    wait until keyword succeeds    1m    1    判断告警不存在    电池电压过低
    ${电压低告警}    判断告警存在_带返回值    电池电压过低
    should not be true    ${电压低告警}
    ${告警级别取值约定dict}    获取web参数的取值约定    电池电压过低
    FOR    ${告警级别设置}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    电池电压过低    ${告警级别设置}
        sleep   5
        wait until keyword succeeds    1m    1    查询指定告警信息    电池电压过低
        ${web实时告警名}    由子工具获取web实时告警名称    电池电压过低-1
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别设置}'=='主要'    主动告警上送判断2    power_sm    电池_1    ${web实时告警名}
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    电池电压过低-1    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别设置}
    END
    向上调节电池电压    ${ 电压低阈值}+1
    wait until keyword succeeds    5m    1    判断告警不存在    电池电压过低
    #无告警后查询判断
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    电池电压过低-1    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    Run keywords    设置web设备参数量为默认值    电池电压过低阈值
    ...    AND    设置web参数量    电池电压过低    主要
    ...    AND    打开交流源输出
    ...    AND    重置电池模拟器输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0008_电池放电告警
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    电池管理初始化
    ${电池放电级别}    获取web参数量    电池放电
    run keyword if    '${电池放电级别}'=='屏蔽'    设置web参数量    电池放电    严重
    sleep    3
    ${电池放电级别}    获取web参数量    电池放电
    ${放电阈值获取范围}    获取web参数上下限范围    电池放电阈值
    设置web参数量    电池放电阈值    ${放电阈值获取范围}[0]
    关闭交流源输出
    设置负载电压电流    53.5    20
    打开负载输出
    #电池放电
    run keyword and ignore error    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    系统停电
    ${电池电流}    获取web实时数据    电池电流-1
    wait until keyword succeeds    3m    2    判断告警存在    电池放电
    ${告警级别取值约定dict}    获取web参数的取值约定    电池放电
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    电池放电    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池放电
        ${web实时告警名}    由子工具获取web实时告警名称    电池放电-1
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别}'=='主要'    主动告警上送判断2    power_sm    电池_1    ${web实时告警名}
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    电池放电-1    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别}
    END
    设置web参数量    电池放电    主要
    #恢复
    打开交流源输出
    关闭负载输出
    Wait Until Keyword Succeeds    30    1    信号量数据值不为    电池管理状态    系统停电
    wait until keyword succeeds    3m    2    判断告警不存在    电池放电
    ${power_sm告警存在}    判断power_sm是否存在单个实时告警    电池放电    ${SSH}
    should not be true    ${power_sm告警存在}[0]
    [Teardown]    Run keywords    设置web设备参数量为默认值    电池放电阈值
    ...    AND    设置web设备参数量为默认值    电池放电
    ...    AND    关闭负载输出
    ...    AND    打开交流源输出
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0008_电池回路断
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    #设置一组电池组容量
    ${电池组数量}    获取web参数的数量    电池组容量
    FOR    ${i}    IN RANGE    ${电池组数量}    1    -1
        设置web参数量    电池组容量_${i}    0
    END
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    99
    sleep    2
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    100
    ${电池回路断级别}    获取web参数量    电池回路断
    run keyword if    '${电池回路断级别}'=='屏蔽'    设置web参数量    电池回路断    严重
    Wait Until Keyword Succeeds    3m    1    判断告警不存在    电池回路断
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_3    100
    wait until keyword succeeds    5m    2    查询指定告警信息    电池回路断
    ${告警级别取值约定dict}    获取web参数的取值约定    电池回路断
    FOR    ${告警级别}    IN    严重    主要    次要    警告
        Wait Until Keyword Succeeds    30    1    设置web参数量    电池回路断    ${告警级别}
        wait until keyword succeeds    1m    1    查询指定告警信息    电池回路断
        Run Keyword And Continue On Failure    Run Keyword if    '${CSU_role}'=='Client' and '${告警级别}'=='主要'    主动告警上送判断2    power_sm    电池_3    电池回路断
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    电池回路断-3    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${告警级别}
        ${sm告警级别}    获取web告警属性    电池回路断-3    告警级别
        should be equal    ${告警级别}    ${sm告警级别}
    END
    #恢复
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_3    0
    wait until keyword succeeds    3m    2    判断告警不存在    电池回路断
    wait until keyword succeeds    1m    5    power_sm不存在单个实时告警    电池回路断-3    ${SSH}
    [Teardown]    run keywords    设置web设备参数量为默认值    电池回路断
    ...    AND    设置web参数量    电池组容量_3    0
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
