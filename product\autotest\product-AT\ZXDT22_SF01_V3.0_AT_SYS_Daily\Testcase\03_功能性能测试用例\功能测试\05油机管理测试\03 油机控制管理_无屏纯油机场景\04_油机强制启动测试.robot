*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
电池组无效强制启动油机测试
    [Setup]
    油机管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    0
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动电压使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动SOC使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动时间使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动系统异常使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机定时启动使能    禁止
    Wait Until Keyword Succeeds    4m    1    获取干接点状态直到变化为    ${油机干接点状态}    0    #干接点动作为1（断开），恢复为0（闭合）
    ${最短运行时间设置值}    获取web参数量    油机最短运行时间
    ${默认值}    获取web参数上下限范围    油机最短运行时间
    run keyword if    ${最短运行时间设置值} != @{默认值}[0]    Wait Until Keyword Succeeds    10    1    设置web参数量    油机最短运行时间    @{默认值}[0]
    ${最短运行时间设置值}    获取web参数量    油机最短运行时间
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    运行
    ${状态}    Run Keyword And Return Status    Wait Until Keyword Succeeds    10m    2    信号量数据值为    油机状态    停止
    should not be true    ${状态}
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    100
    ${电池电压}    获取web实时数据    电池电压-1
    Wait Until Keyword Succeeds    2m    1    设置web控制量    油机关闭
    Wait Until Keyword Succeeds    4m    2    信号量数据值为    油机状态    停止
    Wait Until Keyword Succeeds    4m    1    获取干接点状态直到变化为    ${油机干接点状态}    1    #干接点动作为1（断开），恢复为0（闭合）
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    油机启动时间使能    油机启动电压使能
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动系统异常使能    允许
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    100
    ...    AND    关闭交流源输出

电池空开断启动油机测试
    [Setup]
    油机管理初始化
    同时设置三相电压频率    220    50
    打开交流源输出
    ${可设置范围}    获取web参数可设置范围    电池回路断阈值电压
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池回路断阈值电压    @{可设置范围}[0]
    ${设置值}    获取web参数量    电池回路断阈值电压
    ${最短运行时间设置值}    获取web参数量    油机最短运行时间
    ${默认值}    获取web参数上下限范围    油机最短运行时间
    run keyword if    ${最短运行时间设置值} != @{默认值}[0]    Wait Until Keyword Succeeds    10    1    设置web参数量    油机最短运行时间    @{默认值}[0]
    ${最短运行时间设置值}    获取web参数量    油机最短运行时间
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    0
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_2    100
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动电压使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动SOC使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动时间使能    禁止
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动系统异常使能    允许
    Wait Until Keyword Succeeds    10    1    设置web参数量    油机定时启动使能    禁止
    Wait Until Keyword Succeeds    5m    1    判断告警存在    电池回路断-2
    关闭交流源输出
    Wait Until Keyword Succeeds    4m    1    获取干接点状态直到变化为    ${油机干接点状态}    0    #干接点动作为1（断开），恢复为0（闭合）
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    运行
    ${状态}    Run Keyword And Return Status    Wait Until Keyword Succeeds    10m    2    信号量数据值为    油机状态    停止
    should not be true    ${状态}
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    100
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_2    0
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    电池回路断
    ${电池电压}    获取web实时数据    电池电压-1
    Comment    ${设置值}    获取web参数量    油机启动电压
    Comment    run keyword if    ${电池电压} < ${设置值}    向上调节电池电压    ${设置值}+1
    Wait Until Keyword Succeeds    2m    1    设置web控制量    油机关闭
    Wait Until Keyword Succeeds    4m    2    信号量数据值为    油机状态    停止
    Wait Until Keyword Succeeds    4m    1    获取干接点状态直到变化为    ${油机干接点状态}    1    #干接点动作为1（断开），恢复为0（闭合）
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    油机启动时间使能    油机启动电压使能    电池回路断阈值电压
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    油机启动系统异常使能    允许
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    100
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_2    0
    ...    AND    关闭交流源输出
