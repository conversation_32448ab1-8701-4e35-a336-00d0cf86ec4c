*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
导出用户参数测试
    [Tags]    PMSA-NTest
    连接CSU
    ${导出路径}    导出参数文件    ${datapassword}
    ${WEB与导出文件对比}    ${导出文件与WEB对比}    比对导出参数    ${导出路径}//parameter.csv
    should be true    ${WEB与导出文件对比}
    should be true    ${导出文件与WEB对比}
    ${CPU占用率高阈值获取}    获取web参数量    CPU利用率高阈值
    ${CPU占用率高阈值获取new}    evaluate    ${CPU占用率高阈值获取}+1
    Wait Until Keyword Succeeds    10    2    设置web参数量    CPU利用率高阈值    ${CPU占用率高阈值获取new}
    ${系统过载告警阈值获取}    获取web参数量    系统过载告警阈值
    ${系统过载告警阈值获取new}    evaluate    ${系统过载告警阈值获取}+1
    Wait Until Keyword Succeeds    10    2    设置web参数量    系统过载告警阈值    ${系统过载告警阈值获取new}
    ${WEB与导出文件对比}    ${导出文件与WEB对比}    比对导出参数    ${导出路径}//parameter.csv
    should not be true    ${WEB与导出文件对比}
    should not be true    ${导出文件与WEB对比}
    Wait Until Keyword Succeeds    10    2    设置web参数量    CPU利用率高阈值    ${CPU占用率高阈值获取}
    Wait Until Keyword Succeeds    10    2    设置web参数量    系统过载告警阈值    ${系统过载告警阈值获取}
    ${获取值}    获取web参数量    交流输入场景
    run keyword if    '${获取值}' != '油电'    Wait Until Keyword Succeeds    10    2    设置web参数量    交流输入场景    油电
    run keyword if    '${获取值}' == '油电'    Wait Until Keyword Succeeds    10    2    设置web参数量    市电额定有功功率    2
    ${WEB与导出文件对比}    ${导出文件与WEB对比}    比对导出参数    ${导出路径}//parameter.csv
    should not be true    ${WEB与导出文件对比}
    should not be true    ${导出文件与WEB对比}
    删除文件夹以及文件夹下所有文件    download//${导出路径}
    删除文件    download//${导出路径}.zip


导出告警参数测试
    [Tags]    PMSA-NTest
    连接CSU
    ${导出路径}    导出参数文件    ${datapassword}
    ${WEB与导出文件对比}    ${导出文件与WEB对比}    比对导出参数    ${导出路径}//alarm_attr_para.csv
    should be true    ${WEB与导出文件对比}
    should be true    ${导出文件与WEB对比}
    删除文件夹以及文件夹下所有文件    download//${导出路径}
    删除文件    download//${导出路径}.zip

导出显示属性参数测试
    [Tags]    PMSA-NTest
    连接CSU
    ${导出路径}    导出参数文件    ${datapassword}
    ${WEB与导出文件对比}    ${导出文件与WEB对比}    比对导出参数    ${导出路径}//show_attr_para.csv
    should be true    ${WEB与导出文件对比}
    should be true    ${导出文件与WEB对比}
    删除文件夹以及文件夹下所有文件    download//${导出路径}
    删除文件    download//${导出路径}.zip

加密导出参数正确性测试
    连接CSU
    #加密导出，无密码解压失败
    ${导出参数文件}    WebKeyword_V30.download_para    ${datapassword}
    ${路径}    Split String    ${导出参数文件}    .
    ${解压文件}    WebKeyword_V30.un_zip_dir_doc    ${导出参数文件}    ${路径}[0]
    should not be true    ${解压文件}
    #加密导出，有密码解压成功
    ${导出参数文件}    WebKeyword_V30.download_para    ${datapassword}
    ${路径}    Split String    ${导出参数文件}    .
    ${解压文件}    WebKeyword_V30.un_zip_dir_doc    ${导出参数文件}    ${路径}[0]    ${datapassword}
    should be true    ${解压文件}
    删除文件夹以及文件夹下所有文件    download//${导出参数文件}
    删除文件    download//${导出参数文件}.zip
