#-*-coding:utf-8 -*-
#####################################################################################
# File Name:    FLUKE_8808A.py
# Discription:  实现与FLUKE_8808A的连接和数据读写
# Author:   Pengy；
# DevEnv:   winXP/7x86+python2.7
# Notes:    使用RS232与FLUKE_8808A连接，读取和控制FLUKE_8808A
#           
# Log:      20180110Pengy 
################################################################################
import socket	#for sockets
import sys
import time
import serial


####读取文件地址，使用RIDE时，改为ADD[1]；使用本地编译时改为ADD[0]####
ADD={0:'..\.',
     1:''
    }
add=ADD[0]
########################################
#设置一个时间的格式
ISOTIME = '%Y-%m-%d %X'
########################################
class FLUKE_8808A():
    def __init__(self):     
        self.bSerialIsOpen = False
        self.ser = serial.Serial()
     
##打开串口====================================================================================================================
    def ConnectCom_FLUKE_8808A(self,destPort,destBaud):
        if self.bSerialIsOpen: #在线切换串口,关闭已经打开的串口
            self.bSerialIsOpen = False
            self.ser.close()
            print u'串口已打开'
        else:
            #初始化串口        
            try:
                self.ser = serial.Serial(port=destPort-1, baudrate=destBaud)
##                print u'地址及波特率为：',destPort,destBaud
                self.bSerialIsOpen = True
                
            except serial.SerialException:
                print u'打开串口失败！'
                self.bSerialIsOpen = False
##关闭串口====================================================================================================================
    def CloseCom_FLUKE_8808A(self):
        self.bSerialIsOpen = False
        self.ser.close()
##        print u'关闭串口！'
        
##设置电压模式=============================================
    def SetVolMode(self,Mod):
        """""""""""""""""""""""""""""""""""""""
        Mod:选择读交流电压还是直流电压，可输入：VDC、VAC
        返回一个浮点数的电压值
        """""""""""""""""""""""""""""""""""""""
        self.sendBySerial(Mod+'\r','no')
        self.receiveData(0.5,'no')
        time.sleep(3)
##读电压数据=============================================
    def ReadVol(self,Mod):
        """""""""""""""""""""""""""""""""""""""
        Mod:选择读交流电压还是直流电压，可输入：VDC、VAC
        返回一个浮点数的电压值
        """""""""""""""""""""""""""""""""""""""
        stopflag=1
        while(stopflag):
            self.sendBySerial('VAL?'+'\r','no')
            recVol=self.receiveData(0.5,'no')
            if recVol!='':
                stopflag=0
                if recVol[-3:]=='VDC' or recVol[-3:]=='VAC' :
                    recVol=recVol[:-4]            
    ##            if Mod=='VDC':
    ##                print u'直流电压为：',float(recVol),'V'
    ##            elif Mod=='VAC':
    ##                print u'交流电压为：',float(recVol),'V'
                return str(recVol)
            else:
                stopflag+=1
                print u'没收到FLUKE数据！'
            if stopflag>4:
                print u'3次没收到FLUKE数据！请检查连接！'
                return False
            time.sleep(1)
                


##查询命令=============================================
    def QueryCmd(self, sComment,sleeptime=1,printflag=''):     
        f = open( r'PM3000_command_list.csv', 'r')
        csvlist = []
        csvlist = f.readlines()
        f.close()
        receiveData=''
        returnList=[]
        tmplist=[]
        for i in range(len(csvlist)):
            tmplist = csvlist[i].split(',')
            if tmplist[0] == sComment.encode('gbk'):
                sCmd = tmplist[1].strip('\n')   #命令
                print u'=======',tmplist[0],'========='
                self.sendBySerial(sCmd+'\r',printflag)   #发送的命令要加回车
                returnList=self.receiveData(sleeptime,printflag)
        return returnList

##处理发送数据：===============================================================================================================
    def sendBySerial(self, lstSendData,printflag=''):
        wSendLen = len( lstSendData )
        if wSendLen == 0:
            return
        tempStr = ''

        try:
            self.ser.write( lstSendData )
            if printflag=='':
                print 'send:',lstSendData
        except:
            print u'串口发送数据失败'                
            return
                    
##处理接收数据：===================================================================================================        
    def receiveData(self, interval=1,printflag=''):     
        time.sleep(interval)
        listByteData=''
        recList=[]
        returnList=[]

##        ##接收串口数据
        if self.bSerialIsOpen:
            i = self.ser.inWaiting()
##            print i
            if i != 0: #有数据
                listByteData = self.ser.read(i)
                if printflag=='':
                    print 'rec:',listByteData

        recList=listByteData.split('\n')
##        print 't1',recList
        returnList=recList[0].strip('\r')
        return returnList

#======================================================================================================    
if __name__ == "__main__":
    myapp = FLUKE_8808A()
    myapp.ConnectCom_FLUKE_8808A(1,9600)

##    myapp.sendBySerial(':FNC:CH1:VLT?\r')
####    myapp.sendBySerial('\n')
##    myapp.QueryCmd(u'选择CH1')

##    myapp.QueryCmd(u'CH1的数据')
    myapp.ReadVol('VDC')
    myapp.CloseCom_FLUKE_8808A()

##    myapp.receiveData()
