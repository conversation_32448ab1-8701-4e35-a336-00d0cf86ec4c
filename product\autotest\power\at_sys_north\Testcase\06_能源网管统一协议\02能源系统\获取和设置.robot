*** Settings ***
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_获取能源系统模拟量
    [Tags]    4
    ${能源网管数据}    能源网管协议_获取数据    获取能源系统模拟量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取能源系统模拟量
    Should Be True    ${数据对比结果}

power_sm_0002_获取能源系统数字量
    ${能源网管数据}    能源网管协议_获取数据    获取能源系统数字量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取能源系统数字量
    Should Be True    ${数据对比结果}

power_sm_0003_获取能源系统参数量
    [Tags]    4
    ${能源网管数据}    能源网管协议_获取数据    获取能源系统参数量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取能源系统参数量
    Should Be True    ${数据对比结果}

power_sm_0004_设置能源系统参数量
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    #铅酸&锂电混用
    设置web参数量    电池配置    铅酸&锂电混用
    设置web参数量    交流输入场景    市电
    设置web参数量    电池应用场景    循环场景
    ${能源网管数据}    能源网管协议_设置单个参数    设置能源系统参数    ${BOARD_IP}    ${SSH}
    ${对比结果1}    能源网管协议_批量对比参数设置    ${能源网管数据}    设置能源系统参数
    #常规锂电池
    设置web参数量    电池配置    常规锂电池
    ${能源网管数据}    能源网管协议_设置单个参数    设置能源系统参数    ${BOARD_IP}    ${SSH}
    ${对比结果2}    能源网管协议_批量对比参数设置    ${能源网管数据}    设置能源系统参数
    设置web参数量    电池配置    纯铅酸
    Should Be True    ${对比结果1}
    Should Be True    ${对比结果2}
    [Teardown]    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

power_sm_0005_获取能源系统设备信息
    ${能源网管数据}    能源网管协议_获取数据    获取能源系统设备信息    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取能源系统设备信息
    Should Be True    ${数据对比结果}

power_sm_0006_获取能源系统统计量
    [Tags]  notest
    ${能源网管数据}    能源网管协议_获取数据    获取能源系统统计量    ${SSH}
    ${web数据}    能源网管协议_获取web对应数据    ${能源网管数据}
    ${数据对比结果}    批量对比数据_能源网管协议_WEB    ${能源网管数据}    ${web数据}    获取能源系统统计量
    Should Be True    ${数据对比结果}
