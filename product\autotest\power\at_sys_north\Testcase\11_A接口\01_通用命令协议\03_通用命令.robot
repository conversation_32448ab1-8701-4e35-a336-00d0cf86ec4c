*** Settings ***
Resource          ../A接口关键字_V30.robot

*** Test Cases ***
获取交流配电时间
    ${协议数据}    A接口通用命令_获取时间信息1-6    <<获取交流配电时间~CID1=40H>>    <<获取交流配电时间~CID2=4DH>>
    Log    ${协议数据}
    ${系统时间}    A接口通用命令_获取系统时间
    ${对比结果}    A接口对比时间信息    ${协议数据}    ${系统时间}    交流配电时间
    should be true    ${对比结果}

获取整流配电时间
    ${协议数据}    A接口通用命令_获取时间信息1-6    <<获取整流配电时间~CID1=41H>>    <<获取整流配电时间~CID2=4DH>>
    Log    ${协议数据}
    ${系统时间}    A接口通用命令_获取系统时间
    ${对比结果}    A接口对比时间信息    ${协议数据}    ${系统时间}    整流配电时间
    should be true    ${对比结果}

获取直流配电时间
    ${协议数据}    A接口通用命令_获取时间信息1-6    <<获取直流配电时间~CID1=42H>>    <<获取直流配电时间~CID2=4DH>>
    Log    ${协议数据}
    ${系统时间}    A接口通用命令_获取系统时间
    ${对比结果}    A接口对比时间信息    ${协议数据}    ${系统时间}    直流配电时间
    should be true    ${对比结果}

获取系统防盗随机码
    @{模块CID1}    Create List    <<获取防盗随机码~CID1=40H>>    <<获取防盗随机码~CID1=41H>>    <<获取防盗随机码~CID1=42H>>
    FOR    ${var1}    IN    @{模块CID1}
        ${协议数据}    A接口获取数据    ${var1}    <<设备地址~CID2=E2H>>    None
        判断返回异常结果    ${协议数据}    7
    END

获取交流模块厂家信息
    ${协议数据}    A接口通用命令_获取时间信息1-6    <<获取交流配电厂家信息~CID1=40H>>    <<获取交流配电厂家信息~CID2=51H>>
    Log    ${协议数据}
    ${web数据}    通过A接口数据获取web数据    ${协议数据}
    ${对比结果}    批量对比数据_A接口_WEB    ${协议数据}    ${web数据}    A接口_交流模块厂家信息
    should be true    ${对比结果}

获取设备地址
    @{CID1}    Create List    <<获取交流配电协议版本~CID1=40H>>    <<获取整流器协议版本~CID1=41H>>    <<获取直流配电协议版本~CID1=42H>>
    FOR    ${var1}    IN    @{CID1}
        ${协议数据}    A接口通用命令_获取设备地址    ${var1}    <<设备地址~CID2=50H>>
        Log    ${协议数据}
        ${对比结果}    A接口对比设备地址信息    ${协议数据}    01    交流配电设备地址
        should be true    ${对比结果}
    END

获取通讯版本号
    @{CID1}    Create List    <<获取交流配电协议版本~CID1=40H>>    <<获取整流器协议版本~CID1=41H>>    <<获取直流配电协议版本~CID1=42H>>
    FOR    ${var1}    IN    @{CID1}
        ${协议数据}    A接口通用命令_获取协议版本号    ${var1}    <<获取交流配电协议版本~CID2=4FH>>
        Log    ${协议数据}
        ${对比结果}    A接口对比协议版本信息    ${协议数据}    18    交流配电协议版本
        should be true    ${对比结果}
    END

获取整流器厂家信息
    ${协议数据}    A接口通用命令_获取时间信息1-6    <<获取整流器厂家信息~CID1=41H>>    <<获取整流器厂家信息~CID2=51H>>
    Log    ${协议数据}
    ${web数据}    通过A接口数据获取web数据    ${协议数据}
    ${对比结果}    批量对比数据_A接口_WEB    ${协议数据}    ${web数据}    A接口_整流器厂家信息
    should be true    ${对比结果}

获取直流模块厂家信息
    ${协议数据}    A接口通用命令_获取时间信息1-6    <<获取直流配电厂家信息~CID1=42H>>    <<获取直流配电厂家信息~CID2=51H>>
    Log    ${协议数据}
    ${web数据}    通过A接口数据获取web数据    ${协议数据}
    ${对比结果}    批量对比数据_A接口_WEB    ${协议数据}    ${web数据}    A接口_直流模块厂家信息
    should be true    ${对比结果}

设定正常时间信息
    @{CID1}    Create List    <<设置交流配电时间~CID1=40H>>    <<设置整流器时间~CID1=41H>>    <<设置直流配电时间~CID1=42H>>
    FOR    ${var1}    IN    @{CID1}
        ${对比结果}    A接口设置正常时间    ${var1}    <<设置时间~CID2=4EH>>    <<获取时间~CID2=4DH>>    time
    END
    ${date}    A接口获取本地时间
    ${new_time}    时间格式化转换    ${date}
    ${设置时间}    A接口通用命令_设置时间信息1-6    ${var1}    <<设置时间~CID2=4EH>>    ${date}
    ${系统时间}    A接口通用命令_获取系统时间
    ${对比结果}    A接口对比时间信息    ${系统时间}    ${new_time}    最新时间

设定异常时间信息
    @{通用CID1}    Create List    <<设置交流配电时间~CID1=40H>>    <<设置整流器时间~CID1=41H>>    <<设置直流配电时间~CID1=42H>>
    FOR    ${var1}    IN    @{通用CID1}
        ${对比结果}    A接口设置异常时间    ${var1}    <<设置时间~CID2=4EH>>    <<获取时间~CID2=4DH>>    time
    END
    ${date}    A接口获取本地时间
    ${new_time}    时间格式化转换    ${date}
    ${设置时间}    A接口通用命令_设置时间信息1-6    ${var1}    <<设置时间~CID2=4EH>>    ${date}
    ${系统时间}    A接口通用命令_获取系统时间
    ${对比结果}    A接口对比时间信息    ${系统时间}    ${new_time}    最新时间
