*** Settings ***
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
整流器休眠和唤醒控制测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-1    否    #为保证整流器数据获取正常
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流节能模式    自由
    sleep    5
    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器休眠-1
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-1    是
    ${整流器1休眠状态}    获取web实时数据    整流器休眠状态-1
    log    ${整流器1休眠状态}
    should be equal    '${整流器1休眠状态}'    '是'
    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器唤醒-1
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-1    否
    ${整流器1休眠状态}    获取web实时数据    整流器休眠状态-1
    log    ${整流器1休眠状态}
    should be equal    '${整流器1休眠状态}'    '否'
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流节能模式    安全

整流器风扇调速允许和禁止控制测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-2    否    #为保证整流器数据获取正常
    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器风扇调速禁止-1
    sleep    5
    ${整流器1风扇控制状态}    获取web实时数据    整流器风扇控制状态-1
    log    ${整流器1风扇控制状态}
    should be equal    '${整流器1风扇控制状态}'    '全速'
    Wait Until Keyword Succeeds    10X    2    设置web控制量    整流器风扇调速允许-1
    sleep    5
    ${整流器1风扇控制状态}    获取web实时数据    整流器风扇控制状态-1
    log    ${整流器1风扇控制状态}
    should be equal    '${整流器1风扇控制状态}'    '自动'

整流器自动节能和其他非节能控制测试
    [Documentation]    0:安全/Safe;1:自由/Free Mode;2:自动非节能/Auto NonSave;3:自动节能/Auto Save;4:暂时非节能/Temp NonSave;5:永久非节能/Perm NonSave;6:人工维护检测/Manual Detect
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-2    否    #为保证整流器数据获取正常
    Wait Until Keyword Succeeds    10X    2    设置web参数量    交流节能模式    节能
    设置web参数量    整流器最小开机数量    1
    Wait Until Keyword Succeeds    10X    2    设置web控制量    暂时非节能控制
    sleep    5
    ${交流节能状态}    获取web实时数据    交流节能状态
    log    ${交流节能状态}
    should be equal    ${交流节能状态}    暂时非节能
    Wait Until Keyword Succeeds    10X    2    设置web控制量    人工维护检测
    sleep    5
    ${交流节能状态}    获取web实时数据    交流节能状态
    log    ${交流节能状态}
    should be equal    ${交流节能状态}    人工维护检测
    Wait Until Keyword Succeeds    10X    2    设置web控制量    永久非节能控制
    sleep    5
    ${交流节能状态}    获取web实时数据    交流节能状态
    log    ${交流节能状态}
    should be equal    ${交流节能状态}    永久非节能
    Wait Until Keyword Succeeds    10X    2    设置web控制量    自动节能控制
    sleep    5
    ${交流节能状态}    获取web实时数据    交流节能状态
    log    ${交流节能状态}
    should be equal    ${交流节能状态}    自动非节能
    [Teardown]    Run keywords    设置web参数量    交流节能模式    安全
    ...    AND    设置web参数量    整流器最小开机数量    3

整流器设备统计控制测试
    [Setup]    测试用例前置条件
    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-1    否    #为保证整流器数据获取正常
    Wait Until Keyword Succeeds    10    2    设置web控制量    SMR设备统计
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-1    否    #为保证整流器数据获取正常
    ${整流器1通讯状态}    Wait Until Keyword Succeeds    5m    2    获取web实时数据    整流器通讯状态-1
    log    ${整流器1通讯状态}
    should be equal    '${整流器1通讯状态}'    '正常'
    ${整流器2通讯状态}    获取web实时数据    整流器通讯状态-2
    log    ${整流器2通讯状态}
    should be equal    '${整流器2通讯状态}'    '正常'
