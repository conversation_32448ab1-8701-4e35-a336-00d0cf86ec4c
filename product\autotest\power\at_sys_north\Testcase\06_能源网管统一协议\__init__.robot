*** Settings ***
Suite Setup       run keywords    power_sm加密/不加密链路配置前置条件
...               AND    能源网管协议_初始化
...               AND    客户端/服务端协议配置    power_sm    10.233.211.92
Suite Teardown    客户端测试后置条件    power_sm    # run keywords | Run Keyword if | '${SSH}'=='True' | power_sm_断开加密连接 | AND | 客户端测试后置条件 | power_sm
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../测试用例关键字.robot
