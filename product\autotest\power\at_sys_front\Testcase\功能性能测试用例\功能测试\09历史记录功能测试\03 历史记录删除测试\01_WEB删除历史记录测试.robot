*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
删除历史记录功能测试
    [Documentation]    其它记录产生需要模拟子工具，分散到子工具通讯测试中进行测试。
    连接CSU
    #历史告警
    ${开始数量}    获取web历史告警数量    ${empty}    ${empty}
    删除历史记录    历史告警
    sleep    30
    ${结束数量}    获取web历史告警数量    ${empty}    ${empty}
    should be true    ${结束数量} == 0
    #历史数据
    ${开始数量}    获取web历史数据数量    ${empty}    ${empty}
    删除历史记录    历史数据
    sleep    30
    ${结束数量}    获取web历史数据数量    ${empty}    ${empty}
    should be true    ${结束数量} < ${开始数量}
    #操作记录
    ${开始数量}    获取web历史事件数量    ${empty}    ${empty}
    删除历史记录    操作记录
    sleep    30
    ${结束数量}    获取web历史事件数量    ${empty}    ${empty}
    should be true    ${结束数量} == 1
    #电池充电记录
    ${开始数量}    获取web事件记录数量    电池充电记录
    删除历史记录    电池充电记录
    sleep    30
    ${结束数量}    获取web事件记录数量    电池充电记录
    should be true    ${结束数量} == 0
    #电池放电记录
    ${开始数量}    获取web事件记录数量    电池放电记录
    删除历史记录    电池放电记录
    sleep    30
    ${结束数量}    获取web事件记录数量    电池放电记录
    should be true    ${结束数量} == 0
    #电池测试记录
    ${开始数量}    获取web事件记录数量    电池测试记录
    删除历史记录    电池测试记录
    sleep    30
    ${结束数量}    获取web事件记录数量    电池测试记录
    should be true    ${结束数量} == 0
    #电池均充记录
    ${开始数量}    获取web事件记录数量    电池均充记录
    删除历史记录    电池均充记录
    sleep    30
    ${结束数量}    获取web事件记录数量    电池均充记录
    should be true    ${结束数量} == 0
