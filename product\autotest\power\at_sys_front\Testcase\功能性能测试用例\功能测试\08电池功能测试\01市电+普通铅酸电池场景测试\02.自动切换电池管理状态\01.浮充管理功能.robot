*** Settings ***
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
浮充时交流停电转停电
    [Documentation]    0:浮充/Float;1:均充/Equal;2:测试/Test;3:交流停电（以前叫放电）/Discharge;4:检测/Detect;5:过渡/Transition;6:充电/Charge
    [Tags]    view
    
    电池管理初始化
    sleep    5
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动浮充
    设置负载电压电流    53.5    10
    打开负载输出
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    浮充
    关闭交流源输出
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    系统停电
    关闭负载输出
    打开交流源输出
    [Teardown]    Run keywords    关闭负载输出
    ...    AND    打开交流源输出

浮充时无正常工作整流器转停电
    
    电池管理初始化
    Wait Until Keyword Succeeds    3m    2    信号量数据值为    交流供电状态    市电1
    Wait Until Keyword Succeeds    5m    1    信号量数据值不为    电池管理状态    过渡
    ${状态}    获取web实时数据    电池管理状态
    run keyword if    '${状态}' != '浮充'    设置web控制量    启动浮充
    ${均充电压可设置范围}    获取web参数可设置范围    均充电压
    设置web参数量    均充电压    ${均充电压可设置范围}[0]
    ${输出高停机电压可设置范围}    获取web参数可设置范围    整流器输出高停机电压
    设置web参数量    整流器输出高停机电压    ${输出高停机电压可设置范围}[0]
    #使整流器输出过压保证无正常工作的整流器
    ${整流器输出高电压}    获取web参数量    整流器输出高停机电压
    向上调节电池电压    ${整流器输出高电压}+0.5
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    工作整流器数量    0
    ${状态}    获取web实时数据    电池管理状态
    #无整流器放电状态启动浮充，应F
    设置负载电压电流    53.5    10
    打开负载输出
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    系统停电
    #整流器过压恢复，5min应能恢复
    关闭负载输出
    向下调节电池电压    53.5
    ${在线整流器数量}    获取web实时数据    在线整流器数量
    Wait Until Keyword Succeeds    5m10s    1    信号量数据值为    工作整流器数量    ${在线整流器数量}
    [Teardown]    Run keywords    电池管理参数恢复默认值
    ...    AND    重置电池模拟器输出

浮充时大电流充电转均充
    
    电池管理初始化    #初始化后为浮充状态，只有一组电池100AH
    #确定转均充的阈值电流值
    ${电池组容量}    获取web参数量    电池组容量_1
    ${均充阈值电流系数}    获取web参数量    均充阈值电流
    ${均充阈值电流值}    evaluate    ${电池组容量}*${均充阈值电流系数}
    #通过修改电池电流零点，使充电电流大于均充阈值电流值
    ${原电池电流}    获取web实时数据    电池电流-1
    ${电流通道配置}    获取通道配置    ${plat.battcurr1}
    ${原电池电流零点数据}    evaluate    ${电流通道配置}[1]
    ${转均充充电电流值}    evaluate    ${原电池电流零点数据}+${均充阈值电流值}+5    #使充电电流大于均充阈值电流5A
    设置通道配置    ${plat.battcurr1}    ${转均充充电电流值}    1    直流配电    电池分流器电流_1
    Wait Until Keyword Succeeds    20    1    信号量数据值大于    电池电流-1    ${均充阈值电流值}
    #浮充大电流充电，30min后应自动转均充
    Wait Until Keyword Succeeds    32m    10    信号量数据值不为    电池管理状态    浮充
    Wait Until Keyword Succeeds    1m    1    信号量数据值为    电池管理状态    均充
    Comment    sleep    10m
    Comment    ${电池电流1}    获取web实时数据    电池电流_1
    Comment    ${电池状态}    获取web实时数据    电池管理状态
    Comment    Should Be Equal    ${电池状态}    浮充
    Comment    sleep    10m
    Comment    ${电池电流1}    获取web实时数据    电池电流_1
    Comment    ${电池状态}    获取web实时数据    电池管理状态
    Comment    Should Be Equal    ${电池状态}    浮充
    Comment    sleep    12m
    Comment    ${电池电流1}    获取web实时数据    电池电流_1
    Comment    ${电池状态}    获取web实时数据    电池管理状态
    Comment    Should Be Equal    ${电池状态}    均充
    #恢复
    设置通道配置    ${plat.battcurr1}    0    1    直流配电    电池分流器电流_1
    设置web控制量    启动浮充
    [Teardown]    Run keywords    电池管理参数恢复默认值
    ...    AND    设置通道配置    ${plat.battcurr1}    0    1    直流配电
    ...    电池分流器电流_1
