*** Settings ***
Documentation     As：用户
...               I want to：本监控单元支持扩展温度监控业务，要求包括：
...               1、扩展温度至少支持3路，根据温度AI通道、南向通信采样通道（基于南向协议通信采集的温度）的配置要求如下：
...               1）无温度采样通道配置，相应温传状态为未配置；
...               2）有温度采样通道配置，相应温传状态要求如下：
...               a、对于AI通道：模拟量仅有效显示，有效范围-40~100℃，则温传状态有效；超出有效范围，则温传状态无效。
...               b、对于南向通信采样通道：通信正常，模拟量仅有效显示，有效范围-40~100℃，则温传状态有效；超出有效范围，则温传状态无效。通信异常，则温传状态无效。（仅适用南向支持温湿度传感器设备场景）
...               2、扩展温度告警
...               1）温度无效告警：
...               相应温传状态为无效，且满足实时告警产生条件时，则产生无效实时告警；
...               相应温传状态为有效或未配置，且满足实时告警恢复条件时，则恢复无效实时告警，或不产生无效实时告警。
...               2）温度高、低告警：
...               a、相应温传状态有效时：
...               相应温度满足温度高或低告警阈值且满足实时告警产生条件时，则产生相应实时告警；
...               相应温度满足温度高或低告警阈值回差且满足实时告警恢复条件时，则恢复相应实时告警。
...               b、相应温传状态无效或未配置，则恢复相应实时告警，或不产生实时告警。
...               So that：满足扩展温度监控要求。
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
扩展温度传感器状态测试(AI通道配置)
    [Documentation]    对于AI通道：模拟量仅有效显示，有效范围-40~100℃，则温传状态有效；超出有效范围，则温传状态无效。
    ...    可将温度传感器接入AI通道中的SPB_J4_T1、SPB_J4_T2、SPB_J13_T3、SPB_J12_T4
    ...
    ...    需将温湿度传感器通道配置成无，排除干扰
    连接CSU
    ${i}    Set Variable
    ${j}    Set Variable
    ${扩展温度}    Set Variable
    ${扩展温度传感器状态}    Set Variable
    #将温度传感器通道设置成无
    设置温湿度传感器通道    温度传感器    系统运行环境    无
    @{扩展温度AI通道}    Create List    SPB_J4_T1    SPB_J4_T2    SPB_J13_T3    SPB_J12_T4
    FOR    ${j}    IN RANGE    0    4
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[${j}]    0    1    无    无
    END
    FOR    ${j}    IN RANGE    0    4
        AI通道遍历扩展温度    ${扩展温度}    ${扩展温度传感器状态}    ${扩展温度AI通道}[${j}]
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[${j}]    0    1    无    无
        ${起始时间}    ${结束时间}    获取起始结束时间段    5
        ${获取操作记录数量1}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    操作日志    AI通道配置 [plat.batt1temp] 关联信号 扩展温度[8] \ -> \ 无
        ${获取操作记录数量2}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    操作日志    AI通道配置 [plat.batt2temp] 关联信号 扩展温度[8] \ -> \ 无
        ${获取操作记录数量3}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    操作日志    AI通道配置 [plat.batt3temp] 关联信号 扩展温度[8] \ -> \ 无
        ${获取操作记录数量4}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    操作日志    AI通道配置 [plat.envtemp] 关联信号 扩展温度[8] \ -> \ 无
        Run Keyword If    '${j}'=='0'    Should Be True    ${获取操作记录数量1}==1
        Run Keyword If    '${j}'=='1'    Should Be True    ${获取操作记录数量2}==1
        Run Keyword If    '${j}'=='2'    Should Be True    ${获取操作记录数量3}==1
        Run Keyword If    '${j}'=='3'    Should Be True    ${获取操作记录数量4}==1
    END

扩展温度操作记录测试（AI通道配置）
    [Documentation]    需将温湿度传感器通道配置成无，排除干扰
    连接CSU
    #将温度传感器通道设置成无
    设置温湿度传感器通道    温度传感器    系统运行环境    无
    @{扩展温度AI通道}    Create List    SPB_J4_T1    SPB_J4_T2    SPB_J13_T3    SPB_J12_T4
    ${j}    Set Variable
    FOR    ${j}    IN RANGE    0    4
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[${j}]    0    1    无    无
    END
    FOR    ${j}    IN RANGE    0    4
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[${j}]    3    5    系统运行环境    扩展温度_1
        ${起始时间}    ${结束时间}    获取起始结束时间段    5
        ${获取关联信号操作记录数量1}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    操作日志    AI通道配置 [plat.batt1temp] 关联信号 无 \ -> \ 扩展温度[1]
        ${获取关联信号操作记录数量2}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    操作日志    AI通道配置 [plat.batt2temp] 关联信号 无 \ -> \ 扩展温度[1]
        ${获取关联信号操作记录数量3}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    操作日志    AI通道配置 [plat.batt3temp] 关联信号 无 \ -> \ 扩展温度[1]
        ${获取关联信号操作记录数量4}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    操作日志    AI通道配置 [plat.envtemp] 关联信号 无 \ -> \ 扩展温度[1]
        ${获取斜率操作记录数量1}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    操作日志    AI通道配置 [plat.batt1temp] 斜率 1.000000 \ -> \ 5.000000
        ${获取斜率操作记录数量2}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    操作日志    AI通道配置 [plat.batt2temp] 斜率 1.000000 \ -> \ 5.000000
        ${获取斜率操作记录数量3}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    操作日志    AI通道配置 [plat.batt3temp] 斜率 1.000000 \ -> \ 5.000000
        ${获取斜率操作记录数量4}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    操作日志    AI通道配置 [plat.envtemp] 斜率 1.000000 \ -> \ 5.000000
        ${获取零点操作记录数量1}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    操作日志    AI通道配置 [plat.batt1temp] 零点 0.000000 \ -> \ 3.000000
        ${获取零点操作记录数量2}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    操作日志    AI通道配置 [plat.batt2temp] 零点 0.000000 \ -> \ 3.000000
        ${获取零点操作记录数量3}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    操作日志    AI通道配置 [plat.batt3temp] 零点 0.000000 \ -> \ 3.000000
        ${获取零点操作记录数量4}    获取web历史事件数量_指定时间段和事件信息    ${起始时间}    ${结束时间}    操作日志    AI通道配置 [plat.envtemp] 零点 0.000000 \ -> \ 3.000000
        Run Keyword If    '${j}'=='0'    Should Be True    ${获取关联信号操作记录数量1}==1
        Run Keyword If    '${j}'=='0'    Should Be True    ${获取斜率操作记录数量1}==1
        Run Keyword If    '${j}'=='0'    Should Be True    ${获取零点操作记录数量1}==1
        Run Keyword If    '${j}'=='1'    Should Be True    ${获取关联信号操作记录数量2}==1
        Run Keyword If    '${j}'=='1'    Should Be True    ${获取斜率操作记录数量2}==1
        Run Keyword If    '${j}'=='1'    Should Be True    ${获取零点操作记录数量2}==1
        Run Keyword If    '${j}'=='2'    Should Be True    ${获取关联信号操作记录数量3}==1
        Run Keyword If    '${j}'=='2'    Should Be True    ${获取斜率操作记录数量3}==1
        Run Keyword If    '${j}'=='2'    Should Be True    ${获取零点操作记录数量3}==1
        Run Keyword If    '${j}'=='3'    Should Be True    ${获取关联信号操作记录数量4}==1
        Run Keyword If    '${j}'=='3'    Should Be True    ${获取斜率操作记录数量4}==1
        Run Keyword If    '${j}'=='3'    Should Be True    ${获取零点操作记录数量4}==1
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[${j}]    0    1    无    无
    END

扩展温度传感器状态测试（未配置）
    [Documentation]    扩展温度未配置时，不显示扩展温度相关信息
    ...    需将温湿度传感器通道配置成无，排除干扰
    连接CSU
    #将温度传感器通道设置成无
    设置温湿度传感器通道    温度传感器    系统运行环境    无
    @{扩展温度AI通道}    Create List    SPB_J4_T1    SPB_J4_T2    SPB_J13_T3    SPB_J12_T4
    FOR    ${j}    IN RANGE    0    4
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[${j}]    0    1    无    无
    END
    FOR    ${j}    IN RANGE    0    4
        扩展温度未配置
    END

扩展温度无效告警测试（AI通道配置）
    连接CSU
    ${扩展温度}    Set Variable
    #将温度传感器通道设置成无
    设置温湿度传感器通道    温度传感器    系统运行环境    无
    ${级别设置值}    Set Variable
    @{扩展温度AI通道}    Create List    SPB_J4_T1    SPB_J4_T2    SPB_J13_T3    SPB_J12_T4
    FOR    ${j}    IN RANGE    0    4
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[${j}]    0    1    无    无
    END
    FOR    ${j}    IN RANGE    0    4
    #未配置不显示告警
        扩展温度未配置
    #遍历所有扩展温度
        扩展温度无效告警    ${扩展温度}    ${扩展温度AI通道}[${j}]
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[${j}]    0    1    无    无
    END

扩展温度低告警测试（AI通道配置）
    连接CSU
    ${扩展温度}    Set Variable
    ${级别设置值}    Set Variable
    #将温度传感器通道设置成无
    设置温湿度传感器通道    温度传感器    系统运行环境    无
    @{扩展温度AI通道}    Create List    SPB_J4_T1    SPB_J4_T2    SPB_J13_T3    SPB_J12_T4
    FOR    ${j}    IN RANGE    0    4
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[${j}]    0    1    无    无
    END
    FOR    ${j}    IN RANGE    0    4
        扩展温度未配置
    #遍历所有扩展温度
        扩展温度低告警    ${扩展温度}    ${扩展温度AI通道}[${j}]
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[${j}]    0    1    无    无
    END

扩展温度高告警测试（AI通道配置）
    连接CSU
    ${扩展温度}    Set Variable
    ${级别设置值}    Set Variable
    #将温度传感器通道设置成无
    设置温湿度传感器通道    温度传感器    系统运行环境    无
    @{扩展温度AI通道}    Create List    SPB_J4_T1    SPB_J4_T2    SPB_J13_T3    SPB_J12_T4
    FOR    ${j}    IN RANGE    0    4
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[${j}]    0    1    无    无
    END
    FOR    ${j}    IN RANGE    0    4
        扩展温度未配置
    #遍历所有扩展温度
        扩展温度高告警    ${扩展温度}    ${扩展温度AI通道}[${j}]
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[${j}]    0    1    无    无
    END

扩展温度传感器状态测试（温湿度传感器通道配置）
    [Documentation]    既有AI通道又有温湿度传感器通道，此时AI通道未关联扩展温度
    [Setup]    温湿度传感器通讯测试前置条件
    ${温湿度传感器设备}    Run Keyword If    '${温湿度传感器型号}'=='0'    Set Variable    DMU_WS312M1
    ${温湿度传感器设备}    Run Keyword If    '${温湿度传感器型号}'=='1'    Set Variable    DMU_YD8779Y
    #先将AI通道配置成无
    @{扩展温度AI通道}    Create List    SPB_J4_T1    SPB_J4_T2    SPB_J13_T3    SPB_J12_T4
    FOR    ${j}    IN RANGE    0    4
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[${j}]    0    1    无    无
    END
    #设置温湿度传感器通道
    设置子工具值    ${温湿度传感器设备}    all    只读    温度    26
    FOR    ${i}    IN RANGE    1    9
        设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_${i}
        Wait Until Keyword Succeeds    10m    5    信号量数据值为(强制获取)    扩展温度_${i}    26
        Wait Until Keyword Succeeds    10m    5    信号量数据值为(强制获取)    扩展温度传感器状态_${i}    正常
    END
    #使温度超出有效范围
    设置子工具值    ${温湿度传感器设备}    all    只读    温度    -41
    FOR    ${i}    IN RANGE    1    9
        设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_${i}
        ${扩展温度}    Wait Until Keyword Succeeds    10m    5    获取web实时数据    扩展温度_${i}
        Should Be True    '${扩展温度}'=='val_invalid'
        Wait Until Keyword Succeeds    10m    5    信号量数据值为(强制获取)    扩展温度传感器状态_${i}    异常
    END
    设置子工具值    ${温湿度传感器设备}    all    只读    温度    101
    FOR    ${i}    IN RANGE    1    9
        设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_${i}
        ${扩展温度}    Wait Until Keyword Succeeds    10m    5    获取web实时数据    扩展温度_${i}
        Should Be True    '${扩展温度}'=='val_invalid'
        Wait Until Keyword Succeeds    10m    5    信号量数据值为(强制获取)    扩展温度传感器状态_${i}    异常
    END
    #断开温湿度传感器通讯
    控制子工具运行停止    ${温湿度传感器设备}    启动
    sleep    3m
    设置子工具值    ${温湿度传感器设备}    all    只读    温度    30
    FOR    ${i}    IN RANGE    1    9
        设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_${i}
        Wait Until Keyword Succeeds    10m    5    信号量数据值为(强制获取)    扩展温度_${i}    30
        Wait Until Keyword Succeeds    10m    5    信号量数据值为(强制获取)    扩展温度传感器状态_${i}    正常
    END
    控制子工具运行停止    ${温湿度传感器设备}    关闭
    sleep    3m
    FOR    ${i}    IN RANGE    1    9
        设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_${i}
        ${扩展温度}    Wait Until Keyword Succeeds    10m    5    获取web实时数据    扩展温度_${i}
        Should Be True    '${扩展温度}'=='val_invalid'
        Wait Until Keyword Succeeds    10m    5    信号量数据值为(强制获取)    扩展温度传感器状态_${i}    异常
    END
    设置温湿度传感器通道    温度传感器    系统运行环境    无
    [Teardown]    温湿度传感器通讯测试结束条件

扩展温度无效告警测试（温湿度传感器通道配置）
    [Documentation]    既有AI通道又有温湿度传感器通道，此时AI通道未关联扩展温度
    [Setup]    温湿度传感器通讯测试前置条件
    ${温湿度传感器设备}    Run Keyword If    '${温湿度传感器型号}'=='0'    Set Variable    DMU_WS312M1
    ${温湿度传感器设备}    Run Keyword If    '${温湿度传感器型号}'=='1'    Set Variable    DMU_YD8779Y
    ${扩展温度}    set Variable
    ${级别设置值}    set Variable
    #先将AI通道配置成无
    @{扩展温度AI通道}    Create List    SPB_J4_T1    SPB_J4_T2    SPB_J13_T3    SPB_J12_T4
    FOR    ${j}    IN RANGE    0    4
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[${j}]    0    1    无    无
    END
    #设置温湿度传感器通道
    #使温度超出有效范围
    设置子工具值    ${温湿度传感器设备}    all    只读    温度    -41
    FOR    ${i}    IN RANGE    1    9
        设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_${i}
        ${扩展温度}    Wait Until Keyword Succeeds    10m    5    获取web实时数据    扩展温度_${i}
        Should Be True    '${扩展温度}'=='val_invalid'
        Wait Until Keyword Succeeds    10m    5    信号量数据值为(强制获取)    扩展温度传感器状态_${i}    异常
        ${级别设置值}    获取web参数量    扩展温度无效_${i}
        run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    扩展温度无效_${i}    主要
        ${扩展温度}    获取web实时数据    扩展温度_${i}
        Wait Until Keyword Succeeds    2m    2    Run Keyword If    '${扩展温度}'=='val_invalid'    判断告警存在    扩展温度无效_${i}
        Wait Until Keyword Succeeds    10    2    Run Keyword If    '${扩展温度}'=='val_invalid'    设置web参数量    扩展温度无效_${i}    屏蔽
        Wait Until Keyword Succeeds    10    2    Run Keyword If    '${扩展温度}'=='val_invalid'    判断告警不存在    扩展温度无效_${i}
        Wait Until Keyword Succeeds    10    2    Run Keyword If    '${扩展温度}'=='val_invalid'    设置web参数量    扩展温度无效_${i}    严重
        Wait Until Keyword Succeeds    10    2    Run Keyword If    '${扩展温度}'=='val_invalid'    判断告警存在    扩展温度无效_${i}
    #未配置不显示告警
        设置温湿度传感器通道    温度传感器    系统运行环境    无
        Wait Until Keyword Succeeds    2m    2    判断告警不存在    扩展温度无效_${i}
    END
    设置子工具值    ${温湿度传感器设备}    all    只读    温度    101
    FOR    ${i}    IN RANGE    1    9
        设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_${i}
        ${扩展温度}    Wait Until Keyword Succeeds    10m    5    获取web实时数据    扩展温度_${i}
        Should Be True    '${扩展温度}'=='val_invalid'
        Wait Until Keyword Succeeds    10m    5    信号量数据值为(强制获取)    扩展温度传感器状态_${i}    异常
        ${级别设置值}    获取web参数量    扩展温度无效_${i}
        run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    扩展温度无效_${i}    主要
        ${扩展温度}    获取web实时数据    扩展温度_${i}
        Wait Until Keyword Succeeds    2m    2    Run Keyword If    '${扩展温度}'=='val_invalid'    判断告警存在    扩展温度无效_${i}
        Wait Until Keyword Succeeds    10    2    Run Keyword If    '${扩展温度}'=='val_invalid'    设置web参数量    扩展温度无效_${i}    屏蔽
        Wait Until Keyword Succeeds    10    2    Run Keyword If    '${扩展温度}'=='val_invalid'    判断告警不存在    扩展温度无效_${i}
        Wait Until Keyword Succeeds    10    2    Run Keyword If    '${扩展温度}'=='val_invalid'    设置web参数量    扩展温度无效_${i}    严重
        Wait Until Keyword Succeeds    10    2    Run Keyword If    '${扩展温度}'=='val_invalid'    判断告警存在    扩展温度无效_${i}
    #未配置不显示告警
        设置温湿度传感器通道    温度传感器    系统运行环境    无
        Wait Until Keyword Succeeds    2m    2    判断告警不存在    扩展温度无效_${i}
    END
    [Teardown]    温湿度传感器通讯测试结束条件

扩展温度高告警测试（温湿度传感器通道配置）
    [Documentation]    既有AI通道又有温湿度传感器通道，此时AI通道未关联扩展温度
    [Setup]    温湿度传感器通讯测试前置条件
    ${温湿度传感器设备}    Run Keyword If    '${温湿度传感器型号}'=='0'    Set Variable    DMU_WS312M1
    ${温湿度传感器设备}    Run Keyword If    '${温湿度传感器型号}'=='1'    Set Variable    DMU_YD8779Y
    ${级别设置值}    set Variable
    ${告警阈值}    set Variable
    ${扩展温度}    set Variable
    #先将AI通道配置成无
    @{扩展温度AI通道}    Create List    SPB_J4_T1    SPB_J4_T2    SPB_J13_T3    SPB_J12_T4
    FOR    ${j}    IN RANGE    0    4
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[${j}]    0    1    无    无
    END
    FOR    ${i}    IN RANGE    1    9
    #扩展温度高告警阈值最大为60
        设置子工具值    ${温湿度传感器设备}    all    只读    温度    61
        设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_${i}
        Wait Until Keyword Succeeds    2m    5    设置web参数量    扩展温度高阈值_${i}    60
        ${级别设置值}    获取web参数量    扩展温度高_${i}
        ${告警阈值}    获取web参数量    扩展温度高阈值_${i}
        ${扩展温度}    获取web实时数据    扩展温度_${i}
    #如果扩展温度无效，则跳过校验
        Continue For Loop If    '${扩展温度}'=='val_invalid'
    #如果扩展温度有效且满足告警产生条件，校验告警
        run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    扩展温度高_${i}    主要
        Wait Until Keyword Succeeds    2m    2    Run Keyword If    ${扩展温度}>${告警阈值}    判断告警存在    扩展温度高_${i}
    #告警屏蔽，告警消失
        Wait Until Keyword Succeeds    10    2    Run Keyword If    ${扩展温度}>${告警阈值}    设置web参数量    扩展温度高_${i}    屏蔽
        Wait Until Keyword Succeeds    10    2    Run Keyword If    ${扩展温度}>${告警阈值}    判断告警不存在    扩展温度高_${i}
        Wait Until Keyword Succeeds    10    2    Run Keyword If    ${扩展温度}>${告警阈值}    设置web参数量    扩展温度高_${i}    严重
        Wait Until Keyword Succeeds    10    2    Run Keyword If    ${扩展温度}>${告警阈值}    判断告警存在    扩展温度高_${i}
    #设置成未配置，告警消失
        设置温湿度传感器通道    温度传感器    系统运行环境    无
        Wait Until Keyword Succeeds    2m    2    判断告警不存在    扩展温度高_${i}
        设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_${i}
        sleep    30
    #如果扩展温度为30，告警阈值为30，将告警阈值修改为34，则告警消失
        Wait Until Keyword Succeeds    2m    5    设置web参数量    扩展温度高阈值_${i}    30
        设置子工具值    ${温湿度传感器设备}    all    只读    温度    30
        sleep    5s
        ${扩展温度}    获取web实时数据    扩展温度_${i}
        Wait Until Keyword Succeeds    10    2    Run Keyword If    ${扩展温度}==30    设置web参数量    扩展温度高阈值_${i}    34
        Wait Until Keyword Succeeds    2m    2    Run Keyword If    ${扩展温度}==30    判断告警不存在    扩展温度高_${i}
    #如果30<扩展温度<=60,将告警阈值修改成30，则告警存在
        设置子工具值    ${温湿度传感器设备}    all    只读    温度    35
        ${扩展温度}    获取web实时数据    扩展温度_${i}
        Wait Until Keyword Succeeds    10    2    Run Keyword If    30<${扩展温度}<=60    设置web参数量    扩展温度高阈值_${i}    30
        Wait Until Keyword Succeeds    2m    2    Run Keyword If    30<${扩展温度}<=60    判断告警存在    扩展温度高_${i}
        设置温湿度传感器通道    温度传感器    系统运行环境    无
    END
    [Teardown]    温湿度传感器通讯测试结束条件

扩展温度低告警测试（温湿度传感器通道配置）
    [Documentation]    既有AI通道又有温湿度传感器通道，此时AI通道未关联扩展温度
    [Setup]    温湿度传感器通讯测试前置条件
    ${温湿度传感器设备}    Run Keyword If    '${温湿度传感器型号}'=='0'    Set Variable    DMU_WS312M1
    ${温湿度传感器设备}    Run Keyword If    '${温湿度传感器型号}'=='1'    Set Variable    DMU_YD8779Y
    ${级别设置值}    set Variable
    ${告警阈值}    set Variable
    ${扩展温度}    set Variable
    #先将AI通道配置成无
    @{扩展温度AI通道}    Create List    SPB_J4_T1    SPB_J4_T2    SPB_J13_T3    SPB_J12_T4
    FOR    ${j}    IN RANGE    0    4
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[${j}]    0    1    无    无
    END
    FOR    ${i}    IN RANGE    1    9
    #若满足-40<=扩展温度<-30,则告警阈值取值-30，告警都存在
        设置子工具值    ${温湿度传感器设备}    all    只读    温度    -35
        设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_${i}
        Wait Until Keyword Succeeds    2m    5    设置web参数量    扩展温度低阈值_${i}    -30
        ${级别设置值}    获取web参数量    扩展温度低_${i}
        ${告警阈值}    获取web参数量    扩展温度低阈值_${i}
        ${扩展温度}    获取web实时数据    扩展温度_${i}
    #如果扩展温度无效，则跳过校验
        Continue For Loop If    '${扩展温度}'=='val_invalid'
    #如果扩展温度有效且满足告警产生条件，校验告警
        run keyword if    '${级别设置值}'=='屏蔽'    Wait Until Keyword Succeeds    10    2    设置web参数量    扩展温度低_${i}    主要
        Wait Until Keyword Succeeds    2m    2    Run Keyword If    ${扩展温度}<${告警阈值}    判断告警存在    扩展温度低_${i}
    #告警屏蔽，告警消失
        Wait Until Keyword Succeeds    10    2    Run Keyword If    ${扩展温度}<${告警阈值}    设置web参数量    扩展温度低_${i}    屏蔽
        Wait Until Keyword Succeeds    10    2    Run Keyword If    ${扩展温度}<${告警阈值}    判断告警不存在    扩展温度低_${i}
        Wait Until Keyword Succeeds    10    2    Run Keyword If    ${扩展温度}<${告警阈值}    设置web参数量    扩展温度低_${i}    严重
        Wait Until Keyword Succeeds    10    2    Run Keyword If    ${扩展温度}<${告警阈值}    判断告警存在    扩展温度低_${i}
    #设置成未配置，告警消失
        设置温湿度传感器通道    温度传感器    系统运行环境    无
        Wait Until Keyword Succeeds    2m    2    判断告警不存在    扩展温度低_${i}
        设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_${i}
        sleep    30
    #如果扩展温度为20，告警阈值为20，将告警阈值修改为16，则告警消失
        Wait Until Keyword Succeeds    2m    5    设置web参数量    扩展温度低阈值_${i}    20
        设置子工具值    ${温湿度传感器设备}    all    只读    温度    20
        sleep    5s
        ${扩展温度}    获取web实时数据    扩展温度_${i}
        Wait Until Keyword Succeeds    10    2    Run Keyword If    ${扩展温度}==20    设置web参数量    扩展温度低阈值_${i}    16
        Wait Until Keyword Succeeds    2m    2    Run Keyword If    ${扩展温度}==20    判断告警不存在    扩展温度低_${i}
    #如果-30<扩展温度<20,将告警阈值修改成20，则告警存在
        Wait Until Keyword Succeeds    10    2    设置web参数量    扩展温度低阈值_${i}    20
        设置子工具值    ${温湿度传感器设备}    all    只读    温度    15
        sleep    5s
        ${扩展温度}    获取web实时数据    扩展温度_${i}
        Wait Until Keyword Succeeds    2m    2    Run Keyword If    -30<${扩展温度}<20    判断告警存在    扩展温度低_${i}
        设置温湿度传感器通道    温度传感器    系统运行环境    无
    END
    [Teardown]    温湿度传感器通讯测试结束条件

扩展温度无效告警级别默认值测试
    连接CSU
    强制恢复默认值并重新登录
    #采用AI通道设置
    ${j}    Set Variable
    ${i}    Set Variable
    #将温度传感器通道设置成无
    设置温湿度传感器通道    温度传感器    系统运行环境    无
    @{扩展温度AI通道}    Create List    SPB_J4_T1    SPB_J4_T2    SPB_J13_T3    SPB_J12_T4
    FOR    ${j}    IN RANGE    0    4
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[${j}]    0    1    无    无
    END
    #选取通道SPB_J4_T1
    FOR    ${i}    IN RANGE    1    9
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[0]    0    1    系统运行环境    扩展温度_${i}
        Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    扩展温度无效_${i}    屏蔽
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[0]    0    1    无    无
    END
    #采用温湿度传感器通道
    FOR    ${i}    IN RANGE    1    9
        wait until keyword succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_${i}
        Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    扩展温度无效_${i}    屏蔽
        wait until keyword succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    无
    END
    #选取通道SPB_J4_T2
    FOR    ${i}    IN RANGE    1    9
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[1]    0    1    系统运行环境    扩展温度_${i}
        Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    扩展温度无效_${i}    屏蔽
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[1]    0    1    无    无
    END
    #选取通道SPB_J13_T3
    FOR    ${i}    IN RANGE    1    9
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[2]    0    1    系统运行环境    扩展温度_${i}
        Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    扩展温度无效_${i}    屏蔽
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[2]    0    1    无    无
    END
    #SPB_J12_T4
    FOR    ${i}    IN RANGE    1    9
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[3]    0    1    系统运行环境    扩展温度_${i}
        Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    扩展温度无效_${i}    屏蔽
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[3]    0    1    无    无
    END

扩展温度高低告警级别默认值测试
    连接CSU
    强制恢复默认值并重新登录
    #采用AI通道设置
    ${j}    Set Variable
    ${i}    Set Variable
    #将温度传感器通道设置成无
    设置温湿度传感器通道    温度传感器    系统运行环境    无
    @{扩展温度AI通道}    Create List    SPB_J4_T1    SPB_J4_T2    SPB_J13_T3    SPB_J12_T4
    FOR    ${j}    IN RANGE    0    4
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[${j}]    0    1    无    无
    END
    #选取通道SPB_J4_T1
    FOR    ${i}    IN RANGE    1    9
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[0]    0    1    系统运行环境    扩展温度_${i}
        Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    扩展温度高_${i}    次要
        Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    扩展温度低_${i}    次要
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[0]    0    1    无    无
    END
    #采用温湿度传感器通道
    FOR    ${i}    IN RANGE    1    9
        wait until keyword succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_${i}
        Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    扩展温度高_${i}    次要
        Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    扩展温度低_${i}    次要
        wait until keyword succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    无
    END
    #选取通道SPB_J4_T2
    FOR    ${i}    IN RANGE    1    9
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[1]    0    1    系统运行环境    扩展温度_${i}
        Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    扩展温度高_${i}    次要
        Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    扩展温度低_${i}    次要
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[1]    0    1    无    无
    END
    #选取通道SPB_J13_T3
    FOR    ${i}    IN RANGE    1    9
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[2]    0    1    系统运行环境    扩展温度_${i}
        Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    扩展温度高_${i}    次要
        Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    扩展温度低_${i}    次要
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[2]    0    1    无    无
    END
    #SPB_J12_T4
    FOR    ${i}    IN RANGE    1    9
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[3]    0    1    系统运行环境    扩展温度_${i}
        Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    扩展温度高_${i}    次要
        Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    扩展温度低_${i}    次要
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[3]    0    1    无    无
    END

扩展温度高低告警阈值默认值测试
    连接CSU
    强制恢复默认值并重新登录
    #采用AI通道设置
    ${j}    Set Variable
    ${i}    Set Variable
    #将温度传感器通道设置成无
    设置温湿度传感器通道    温度传感器    系统运行环境    无
    @{扩展温度AI通道}    Create List    SPB_J4_T1    SPB_J4_T2    SPB_J13_T3    SPB_J12_T4
    FOR    ${j}    IN RANGE    0    4
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[${j}]    0    1    无    无
    END
    #选取通道SPB_J4_T1
    FOR    ${i}    IN RANGE    1    9
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[0]    0    1    系统运行环境    扩展温度_${i}
        Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    扩展温度高阈值_${i}    45
        Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    扩展温度低阈值_${i}    -5
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[0]    0    1    无    无
    END
    #采用温湿度传感器通道
    FOR    ${i}    IN RANGE    1    9
        wait until keyword succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_${i}
        Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    扩展温度高阈值_${i}    45
        Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    扩展温度低阈值_${i}    -5
        wait until keyword succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    无
    END
    #选取通道SPB_J4_T2
    FOR    ${i}    IN RANGE    1    9
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[1]    0    1    系统运行环境    扩展温度_${i}
        Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    扩展温度高阈值_${i}    45
        Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    扩展温度低阈值_${i}    -5
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[1]    0    1    无    无
    END
    #选取通道SPB_J13_T3
    FOR    ${i}    IN RANGE    1    9
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[2]    0    1    系统运行环境    扩展温度_${i}
        Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    扩展温度高阈值_${i}    45
        Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    扩展温度低阈值_${i}    -5
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[2]    0    1    无    无
    END
    #SPB_J12_T4
    FOR    ${i}    IN RANGE    1    9
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[3]    0    1    系统运行环境    扩展温度_${i}
        Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    扩展温度高阈值_${i}    45
        Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    扩展温度低阈值_${i}    -5
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[3]    0    1    无    无
    END

扩展温度高告警阈值设置测试
    连接CSU
    强制恢复默认值并重新登录
    #采用AI通道设置
    ${j}    Set Variable
    ${i}    Set Variable
    #将温度传感器通道设置成无
    设置温湿度传感器通道    温度传感器    系统运行环境    无
    @{扩展温度AI通道}    Create List    SPB_J4_T1    SPB_J4_T2    SPB_J13_T3    SPB_J12_T4
    FOR    ${j}    IN RANGE    0    4
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[${j}]    0    1    无    无
    END
    #选取通道SPB_J4_T1
    FOR    ${i}    IN RANGE    1    9
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[0]    0    1    系统运行环境    扩展温度_${i}
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度高阈值_${i}    60
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度高阈值_${i}    30
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度高阈值_${i}    61
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度高阈值_${i}    29
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[0]    0    1    无    无
    END
    #采用温湿度传感器通道
    FOR    ${i}    IN RANGE    1    9
        wait until keyword succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_${i}
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度高阈值_${i}    60
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度高阈值_${i}    30
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度高阈值_${i}    61
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度高阈值_${i}    29
        wait until keyword succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    无
    END
    #选取通道SPB_J4_T2
    FOR    ${i}    IN RANGE    1    9
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[1]    0    1    系统运行环境    扩展温度_${i}
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度高阈值_${i}    60
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度高阈值_${i}    30
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度高阈值_${i}    61
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度高阈值_${i}    29       
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[1]    0    1    无    无
    END
    #选取通道SPB_J13_T3
    FOR    ${i}    IN RANGE    1    9
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[2]    0    1    系统运行环境    扩展温度_${i}
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度高阈值_${i}    60
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度高阈值_${i}    30
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度高阈值_${i}    61
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度高阈值_${i}    29
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[2]    0    1    无    无
    END
    #SPB_J12_T4
    FOR    ${i}    IN RANGE    1    9
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[3]    0    1    系统运行环境    扩展温度_${i}
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度高阈值_${i}    60
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度高阈值_${i}    30
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度高阈值_${i}    61
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度高阈值_${i}    29
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[3]    0    1    无    无
    END

扩展温度低告警阈值设置测试
    连接CSU
    强制恢复默认值并重新登录
    #采用AI通道设置
    ${j}    Set Variable
    ${i}    Set Variable
    #将温度传感器通道设置成无
    设置温湿度传感器通道    温度传感器    系统运行环境    无
    @{扩展温度AI通道}    Create List    SPB_J4_T1    SPB_J4_T2    SPB_J13_T3    SPB_J12_T4
    FOR    ${j}    IN RANGE    0    4
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[${j}]    0    1    无    无
    END
    #选取通道SPB_J4_T1
    FOR    ${i}    IN RANGE    1    9
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[0]    0    1    系统运行环境    扩展温度_${i}
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度低阈值_${i}    20
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度低阈值_${i}    -30
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度低阈值_${i}    21
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度低阈值_${i}    -31
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[0]    0    1    无    无
    END
    #采用温湿度传感器通道
    FOR    ${i}    IN RANGE    1    9
        wait until keyword succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_${i}
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度低阈值_${i}    20
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度低阈值_${i}    -30
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度低阈值_${i}    21
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度低阈值_${i}    -31        
        wait until keyword succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    无
    END
    #选取通道SPB_J4_T2
    FOR    ${i}    IN RANGE    1    9
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[1]    0    1    系统运行环境    扩展温度_${i}
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度低阈值_${i}    20
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度低阈值_${i}    -30
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度低阈值_${i}    21
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度低阈值_${i}    -31
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[1]    0    1    无    无
    END
    #选取通道SPB_J13_T3
    FOR    ${i}    IN RANGE    1    9
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[2]    0    1    系统运行环境    扩展温度_${i}
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度低阈值_${i}    20
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度低阈值_${i}    -30
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度低阈值_${i}    21
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度低阈值_${i}    -31
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[2]    0    1    无    无
    END
    #SPB_J12_T4
    FOR    ${i}    IN RANGE    1    9
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[3]    0    1    系统运行环境    扩展温度_${i}
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度低阈值_${i}    20
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度低阈值_${i}    -30
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度低阈值_${i}    21
        Wait Until Keyword Succeeds    5m    5    设置web参数量_带返回值    扩展温度低阈值_${i}    -31
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[3]    0    1    无    无
    END

扩展温度采样通道默认配置测试
    [Documentation]    扩展温度默认配置为扩展温度4#
    连接CSU
    强制恢复默认值并重新登录
    Wait Until Keyword Succeeds    10m    5    信号量数据值为(强制获取)    扩展温度传感器状态_4    异常
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    扩展温度高阈值_4    45
    Wait Until Keyword Succeeds    5m    5    信号量数据值为(强制获取)    扩展温度低阈值_4    -5
    #同一扩展温度只支持一个采样通道采集
    @{扩展温度AI通道}    Create List    SPB_J4_T1    SPB_J4_T2    SPB_J13_T3    SPB_J12_T4
    FOR    ${i}    IN RANGE    0    4
        ${设置结果}    wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[${i}]    0    1    系统运行环境    扩展温度_4
        Should Not Be True    ${设置结果}
    END
    ${i}    Set Variable
    FOR    ${i}    IN RANGE    1    4
        Wait Until Keyword Succeeds    10m    5    信号量数据值为(强制获取)    扩展温度传感器状态_${i}    未配置
    END
    ${i}    Set Variable
    FOR    ${i}    IN RANGE    5    9
        Wait Until Keyword Succeeds    10m    5    信号量数据值为(强制获取)    扩展温度传感器状态_${i}    未配置
    END
强制恢复默认值操作后扩展温度无效告警测试（AI通道配置）
    连接CSU
    强制恢复默认值并重新登录
    连接CSU
    ${扩展温度}    Set Variable
    #将温度传感器通道设置成无
    设置温湿度传感器通道    温度传感器    系统运行环境    无
    ${级别设置值}    Set Variable
    @{扩展温度AI通道}    Create List    SPB_J4_T1    SPB_J4_T2    SPB_J13_T3    SPB_J12_T4
    FOR    ${j}    IN RANGE    0    4
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[${j}]    0    1    无    无
    END
    FOR    ${j}    IN RANGE    0    4
    #未配置不显示告警
        扩展温度未配置
    #遍历所有扩展温度
        扩展温度无效告警    ${扩展温度}    ${扩展温度AI通道}[${j}]
        wait until keyword succeeds    30    1    设置通道配置    ${扩展温度AI通道}[${j}]    0    1    无    无
    END