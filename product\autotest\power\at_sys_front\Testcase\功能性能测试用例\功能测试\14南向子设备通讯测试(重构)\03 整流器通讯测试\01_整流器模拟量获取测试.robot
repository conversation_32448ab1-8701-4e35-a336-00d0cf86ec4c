*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
设置整流器序列号
    连接CSU
    FOR    ${地址}    IN RANGE    1    49
        ${序列号}    evaluate     2147483647+${地址}
        ${序列号}    evaluate     str(${序列号})
        ${地址}    evaluate     str(${地址})
        设置子工具值    SMR    ${地址}    版本    SMR序列号    ${序列号}
    END

批量获取整流器模拟量测试
    [Tags]    PMSA-NTest
    写入CSV文档    整流器模拟量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=整流器
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除模拟量信号}    ${排除列表}    1    ${模拟整流器地址}
    ${缺省值列表}   获取缺省值列表  ${列表1}    1
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    SMR    模拟量    ${缺省值列表}    2    整流器模拟量获取测试
    ${缺省值列表}   获取缺省值列表  ${列表1}    2
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    SMR    模拟量    ${缺省值列表}    2    整流器模拟量获取测试
    ${缺省值列表}   获取缺省值列表  ${列表1}    0
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    SMR    模拟量    ${缺省值列表}    2    整流器模拟量获取测试

整流器输入电压测试
    连接CSU
    设置子工具值    smr    all    模拟量    整流器输入电压    0
    Wait Until Keyword Succeeds    10m    1    信号量数据值为(强制获取)    整流器输入电压-1     0
    Wait Until Keyword Succeeds    10m    1    信号量数据值为(强制获取)    整流器输入电压-8     0
    Wait Until Keyword Succeeds    10m    1    信号量数据值为(强制获取)    整流器输入电压-48    0
    设置子工具值    smr    all    模拟量    整流器输入电压    520
    Wait Until Keyword Succeeds    10m    1    信号量数据值为(强制获取)    整流器输入电压-1     520
    Wait Until Keyword Succeeds    10m    1    信号量数据值为(强制获取)    整流器输入电压-8     520
    Wait Until Keyword Succeeds    10m    1    信号量数据值为(强制获取)    整流器输入电压-48    520
    设置子工具值    smr    all    模拟量    整流器输入电压    220
    Wait Until Keyword Succeeds    10m    1    信号量数据值为(强制获取)    整流器输入电压-1     220
    Wait Until Keyword Succeeds    10m    1    信号量数据值为(强制获取)    整流器输入电压-8     220
    Wait Until Keyword Succeeds    10m    1    信号量数据值为(强制获取)    整流器输入电压-48    220
    [Teardown]  设置子工具值    smr    all    模拟量    整流器输入电压    220
