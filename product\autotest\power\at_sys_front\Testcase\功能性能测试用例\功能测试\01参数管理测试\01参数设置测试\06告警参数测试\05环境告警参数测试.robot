*** Settings ***
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
01 环境温度高测试
    [Documentation]    高阈值范围：30-60，默认55；过高阈值范围：33-63，默认58。高比过高低3°
    [Tags]    view    PMSA-NTest
    [Setup]    判断web参数是否存在    环境温度高
    FOR    ${告警级别}    IN    严重    主要    次要    警告    屏蔽
        wait until keyword succeeds    10    1    设置web参数量    环境温度高    ${告警级别}
        sleep    5
        ${环境温度高告警级别}    wait until keyword succeeds    10    1    获取web参数量    环境温度高
        should be equal    ${环境温度高告警级别}    ${告警级别}
    END
    设置web参数量    环境温度高    次要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        ${告警干接点获取}    获取web参数量    环境温度高干接点
        run keyword if    ${告警干接点获取}!=${告警干接点设置}    设置web参数量    环境温度高干接点    ${告警干接点设置}
        sleep    3
        ${告警干接点获取}    获取web参数量    环境温度高干接点
        should be true    ${告警干接点获取}==${告警干接点设置}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    环境温度高干接点    0
    ...    AND    设置web参数量    环境温度高    次要

02 环境温度过高测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    环境温度过高
    FOR    ${告警级别}    IN    严重    主要    次要    警告    屏蔽
        wait until keyword succeeds    10    1    设置web参数量    环境温度过高    ${告警级别}
        sleep    5
        ${环境温度过高告警级别}    wait until keyword succeeds    10    1    获取web参数量    环境温度过高
        should be equal    ${环境温度过高告警级别}    ${告警级别}
    END
    设置web参数量    环境温度过高    次要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        ${告警干接点获取}    获取web参数量    环境温度过高干接点
        run keyword if    ${告警干接点获取}!=${告警干接点设置}    设置web参数量    环境温度过高干接点    ${告警干接点设置}
        sleep    3
        ${告警干接点获取}    获取web参数量    环境温度过高干接点
        should be true    ${告警干接点获取}==${告警干接点设置}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    环境温度过高干接点    0
    ...    AND    设置web参数量    环境温度过高    次要

03 环境温度低测试
    [Documentation]    低阈值范围：-27~23，默认-5；过低阈值范围：-30~20，默认-8
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    环境温度低
    FOR    ${告警级别}    IN    严重    主要    次要    警告    屏蔽
        wait until keyword succeeds    10    1    设置web参数量    环境温度低    ${告警级别}
        sleep    5
        ${环境温度低告警级别}    wait until keyword succeeds    10    1    获取web参数量    环境温度低
        should be equal    ${环境温度低告警级别}    ${告警级别}
    END
    设置web参数量    环境温度低    次要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        ${告警干接点获取}    获取web参数量    环境温度低干接点
        run keyword if    ${告警干接点获取}!=${告警干接点设置}    设置web参数量    环境温度低干接点    ${告警干接点设置}
        sleep    3
        ${告警干接点获取}    获取web参数量    环境温度低干接点
        should be true    ${告警干接点获取}==${告警干接点设置}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    环境温度低干接点    0
    ...    AND    设置web参数量    环境温度低    次要

04 环境温度过低测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    环境温度过低
    FOR    ${告警级别}    IN    严重    主要    次要    警告    屏蔽
        wait until keyword succeeds    10    1    设置web参数量    环境温度过低    ${告警级别}
        sleep    5
        ${环境温度过低告警级别}    wait until keyword succeeds    10    1    获取web参数量    环境温度过低
        should be equal    ${环境温度过低告警级别}    ${告警级别}
    END
    设置web参数量    环境温度过低    次要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        ${告警干接点获取}    获取web参数量    环境温度过低干接点
        run keyword if    ${告警干接点获取}!=${告警干接点设置}    设置web参数量    环境温度过低干接点    ${告警干接点设置}
        sleep    3
        ${告警干接点获取}    获取web参数量    环境温度过低干接点
        should be true    ${告警干接点获取}==${告警干接点设置}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    环境温度过低干接点    0
    ...    AND    设置web参数量    环境温度过低    次要

05 环境温度无效测试
    [Documentation]    温度传感器类型：0:AD590/AD590;1:NTC/NTC....不接温度传感器与测试温度的相矛盾，需要测试温度无效则无法测试其他环境温度相关的用例
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    环境温度无效
    FOR    ${告警级别}    IN    严重    主要    次要    警告    屏蔽
        wait until keyword succeeds    10    1    设置web参数量    环境温度无效    ${告警级别}
        sleep    5
        ${环境温度无效告警级别}    wait until keyword succeeds    10    1    获取web参数量    环境温度无效
        should be equal    ${环境温度无效告警级别}    ${告警级别}
    END
    设置web参数量    环境温度无效    次要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        ${告警干接点获取}    获取web参数量    环境温度无效干接点
        run keyword if    ${告警干接点获取}!=${告警干接点设置}    设置web参数量    环境温度无效干接点    ${告警干接点设置}
        sleep    3
        ${告警干接点获取}    获取web参数量    环境温度无效干接点
        should be true    ${告警干接点获取}==${告警干接点设置}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    环境温度无效干接点    0
    ...    AND    设置web参数量    环境温度无效    次要

06 环境湿度高测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    环境湿度高
    FOR    ${告警级别}    IN    严重    主要    次要    警告    屏蔽
        wait until keyword succeeds    10    1    设置web参数量    环境湿度高    ${告警级别}
        sleep    5
        ${环境湿度高告警级别}    wait until keyword succeeds    10    1    获取web参数量    环境湿度高
        should be equal    ${环境湿度高告警级别}    ${告警级别}
    END
    设置web参数量    环境湿度高    次要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        ${告警干接点获取}    获取web参数量    环境湿度高干接点
        run keyword if    ${告警干接点获取}!=${告警干接点设置}    设置web参数量    环境湿度高干接点    ${告警干接点设置}
        sleep    3
        ${告警干接点获取}    获取web参数量    环境湿度高干接点
        should be true    ${告警干接点获取}==${告警干接点设置}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    环境湿度高干接点    0
    ...    AND    设置web参数量    环境湿度高    次要

07 环境湿度低测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    环境湿度低
    FOR    ${告警级别}    IN    严重    主要    次要    警告    屏蔽
        wait until keyword succeeds    10    1    设置web参数量    环境湿度低    ${告警级别}
        sleep    5
        ${环境湿度低告警级别}    wait until keyword succeeds    10    1    获取web参数量    环境湿度低
        should be equal    ${环境湿度低告警级别}    ${告警级别}
    END
    设置web参数量    环境湿度低    次要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        ${告警干接点获取}    获取web参数量    环境湿度低干接点
        run keyword if    ${告警干接点获取}!=${告警干接点设置}    设置web参数量    环境湿度低干接点    ${告警干接点设置}
        sleep    3
        ${告警干接点获取}    获取web参数量    环境湿度低干接点
        should be true    ${告警干接点获取}==${告警干接点设置}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    环境湿度低干接点    0
    ...    AND    设置web参数量    环境湿度低    次要

08 环境湿度无效测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    环境湿度无效
    FOR    ${告警级别}    IN    严重    主要    次要    警告    屏蔽
        wait until keyword succeeds    10    1    设置web参数量    环境湿度无效    ${告警级别}
        sleep    5
        ${环境湿度无效告警级别}    wait until keyword succeeds    10    1    获取web参数量    环境湿度无效
        should be equal    ${环境湿度无效告警级别}    ${告警级别}
    END
    设置web参数量    环境湿度无效    次要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        ${告警干接点获取}    获取web参数量    环境湿度无效干接点
        run keyword if    ${告警干接点获取}!=${告警干接点设置}    设置web参数量    环境湿度无效干接点    ${告警干接点设置}
        sleep    3
        ${告警干接点获取}    获取web参数量    环境湿度无效干接点
        should be true    ${告警干接点获取}==${告警干接点设置}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    环境湿度无效干接点    0
    ...    AND    设置web参数量    环境湿度无效    次要

09 烟雾告警测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    烟雾告警
    FOR    ${告警级别}    IN    严重    主要    次要    警告    屏蔽
        wait until keyword succeeds    10    1    设置web参数量    烟雾告警    ${告警级别}
        sleep    5
        ${烟雾告警级别}    wait until keyword succeeds    10    1    获取web参数量    烟雾告警
        should be equal    ${烟雾告警级别}    ${告警级别}
    END
    设置web参数量    烟雾告警    次要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        ${告警干接点获取}    获取web参数量    烟雾告警干接点
        run keyword if    ${告警干接点获取}!=${告警干接点设置}    设置web参数量    烟雾告警干接点    ${告警干接点设置}
        sleep    3
        ${告警干接点获取}    获取web参数量    烟雾告警干接点
        should be true    ${告警干接点获取}==${告警干接点设置}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    烟雾告警干接点    0
    ...    AND    设置web参数量    烟雾告警    次要

10 水淹告警测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    水淹告警
    FOR    ${告警级别}    IN    严重    主要    次要    警告    屏蔽
        wait until keyword succeeds    10    1    设置web参数量    水淹告警    ${告警级别}
        sleep    5
        ${水淹告警级别}    wait until keyword succeeds    10    1    获取web参数量    水淹告警
        should be equal    ${水淹告警级别}    ${告警级别}
    END
    设置web参数量    水淹告警    次要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        ${告警干接点获取}    获取web参数量    水淹告警干接点
        run keyword if    ${告警干接点获取}!=${告警干接点设置}    设置web参数量    水淹告警干接点    ${告警干接点设置}
        sleep    3
        ${告警干接点获取}    获取web参数量    水淹告警干接点
        should be true    ${告警干接点获取}==${告警干接点设置}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    水淹告警干接点    0
    ...    AND    设置web参数量    水淹告警    次要

11 门磁告警测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    门磁告警
    FOR    ${告警级别}    IN    严重    主要    次要    警告    屏蔽
        wait until keyword succeeds    10    1    设置web参数量    门磁告警    ${告警级别}
        sleep    5
        ${门磁告警级别}    wait until keyword succeeds    10    1    获取web参数量    门磁告警
        should be equal    ${门磁告警级别}    ${告警级别}
    END
    设置web参数量    门磁告警    次要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        ${告警干接点获取}    获取web参数量    门磁告警干接点
        run keyword if    ${告警干接点获取}!=${告警干接点设置}    设置web参数量    门磁告警干接点    ${告警干接点设置}
        sleep    3
        ${告警干接点获取}    获取web参数量    门磁告警干接点
        should be true    ${告警干接点获取}==${告警干接点设置}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    门磁告警干接点    0
    ...    AND    设置web参数量    门磁告警    次要

12 门禁告警测试
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    门禁告警
    FOR    ${告警级别}    IN    严重    主要    次要    警告    屏蔽
        wait until keyword succeeds    10    1    设置web参数量    门禁告警    ${告警级别}
        sleep    5
        ${门禁告警级别}    wait until keyword succeeds    10    1    获取web参数量    门禁告警
        should be equal    ${门禁告警级别}    ${告警级别}
    END
    设置web参数量    门禁告警    次要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        ${告警干接点获取}    获取web参数量    门禁告警干接点
        run keyword if    ${告警干接点获取}!=${告警干接点设置}    设置web参数量    门禁告警干接点    ${告警干接点设置}
        sleep    3
        ${告警干接点获取}    获取web参数量    门禁告警干接点
        should be true    ${告警干接点获取}==${告警干接点设置}
    END
    sleep    5
    [Teardown]    run keywords    设置web参数量    门禁告警干接点    0
    ...    AND    设置web参数量    门禁告警    次要

13 温控单元异常
    [Tags]    PMSA-NTest
    [Setup]    判断web参数是否存在    温控单元异常
    FOR    ${告警级别}    IN    严重    主要    次要    警告    屏蔽
        wait until keyword succeeds    10    1    设置web参数量    温控单元异常    ${告警级别}
        sleep    5
        ${门禁告警级别}    wait until keyword succeeds    10    1    获取web参数量    温控单元异常
        should be equal    ${告警级别}    ${门禁告警级别}
    END
    设置web参数量    温控单元异常    次要
    FOR    ${告警干接点}    IN RANGE    ${g_干接点结束编号}    ${g_干接点起始编号}    -1
        ${告警干接点设置}    evaluate    str(${告警干接点})
        ${告警干接点获取}    获取web参数量    温控单元异常干接点
        run keyword if    ${告警干接点获取}!=${告警干接点设置}    设置web参数量    温控单元异常干接点    ${告警干接点设置}
        sleep    3
        ${告警干接点获取}    获取web参数量    温控单元异常干接点
        should be equal    ${告警干接点设置}    ${告警干接点获取}
    END
    [Teardown]    run keywords    设置web参数量    温控单元异常干接点    0
    ...    AND    设置web参数量    温控单元异常    次要
