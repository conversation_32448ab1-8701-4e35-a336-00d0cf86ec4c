*** Settings ***
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***
批量获取设备信息
    写入CSV文档    FB100B1设备信息获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string64    INDEX_DEVICE_CH=FBBMS
    ${排除列表}    create list
    @{信号名称列表1}    create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除设备版本信息}    ${排除列表}    1
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${dict1}    Create Dictionary
        Set To Dictionary    ${dict1}     name     ${信号名称}
		Append To List       ${信号名称列表1}    ${dict1} 
    END
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    smartli_fb100b1    ${信号名称列表1}    只读    V99.23    FB100B1设备信息获取测试
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    smartli_fb100b1    ${信号名称列表1}    只读    V10.10    FB100B1设备信息获取测试
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    smartli_fb100b1    ${信号名称列表1}    只读    V1.81    FB100B1设备信息获取测试
	
	
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string64    INDEX_DEVICE_CH=FBBMS
    ${排除列表}    create list
    @{信号名称列表2}     create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除设备名称信息}    ${排除列表}    1
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${dict2}    Create Dictionary
        Set To Dictionary    ${dict2}     name     ${信号名称}
		Append To List       ${信号名称列表2}    ${dict2}
    END
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    smartli_fb100b1    ${信号名称列表2}    只读    VZXDU48 FB100B3    FB100B1设备信息获取测试
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    smartli_fb100b1    ${信号名称列表2}    只读    ZTE-smartli    FB100B1设备信息获取测试
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    smartli_fb100b1    ${信号名称列表2}    只读    VZXDU48 FB100C2    FB100B1设备信息获取测试
 
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string64    INDEX_DEVICE_CH=FBBMS
    ${排除列表}    create list
    @{信号名称列表3}   create_list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${FB100B3排除设备序列号信息}    ${排除列表}    1
    FOR    ${i}    IN    @{列表1}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${dict3}    Create Dictionary
        Set To Dictionary    ${dict3}     name     ${信号名称}
		Append To List       ${信号名称列表3}    ${dict3}
	END
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    smartli_fb100b1    ${信号名称列表3}    读写    210097205489    FB100B1设备信息获取测试
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    smartli_fb100b1    ${信号名称列表3}    读写    210097205673    FB100B1设备信息获取测试
    Run Keyword And Continue On Failure    南向子设备字符型厂家信息列表封装判断结果    smartli_fb100b1    ${信号名称列表3}    读写    210097205688    FB100B1设备信息获取测试
 
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=float    INDEX_DEVICE_CH=FBBMS
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    1
    ${缺省值列表}   获取缺省值列表  ${列表1}    1
    Run Keyword And Continue On Failure    南向子设备数值型厂家信息列表封装判断结果    smartli_fb100b1    ${缺省值列表}    只读       FB100B1设备信息获取测试
    ${缺省值列表}   获取缺省值列表  ${列表1}    2
	Run Keyword And Continue On Failure    南向子设备数值型厂家信息列表封装判断结果    smartli_fb100b1    ${缺省值列表}    只读       FB100B1设备信息获取测试
    ${缺省值列表}   获取缺省值列表  ${列表1}    0
	Run Keyword And Continue On Failure    南向子设备数值型厂家信息列表封装判断结果    smartli_fb100b1    ${缺省值列表}    只读       FB100B1设备信息获取测试

    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=int    INDEX_DEVICE_CH=FBBMS
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}    1
    ${缺省值列表}   获取缺省值列表  ${列表1}    1
	Run Keyword And Continue On Failure    南向子设备数值型厂家信息列表封装判断结果    smartli_fb100b1    ${缺省值列表}    只读      FB100B1设备信息获取测试
    ${缺省值列表}   获取缺省值列表  ${列表1}    2   
    Run Keyword And Continue On Failure    南向子设备数值型厂家信息列表封装判断结果    smartli_fb100b1    ${缺省值列表}    只读      FB100B1设备信息获取测试
    ${缺省值列表}   获取缺省值列表  ${列表1}    0
	Run Keyword And Continue On Failure    南向子设备数值型厂家信息列表封装判断结果    smartli_fb100b1    ${缺省值列表}    只读      FB100B1设备信息获取测试
   

BMS软件发布日期获取测试
    连接CSU
    设置子工具值    smartli_fb100b1    all    只读    BMS软件发布日期    2020
    设置子工具值    smartli_fb100b1    all    只读    BMS软件发布日期mouth    12
    设置子工具值    smartli_fb100b1    all    只读    BMS软件发布日期day    5
    Wait Until Keyword Succeeds    20m   5    设备信息获取值    BMS软件发布日期-1          2020-12-05
    Wait Until Keyword Succeeds    20m   5    设备信息获取值   BMS软件发布日期-8           2020-12-05
    Wait Until Keyword Succeeds    20m   5    设备信息获取值   BMS软件发布日期-${铁锂电池组数}      2020-12-05
    设置子工具值    smartli_fb100b1    all    只读    BMS软件发布日期    2020
    设置子工具值    smartli_fb100b1    all    只读    BMS软件发布日期mouth    12
    设置子工具值    smartli_fb100b1    all    只读    BMS软件发布日期day    4
    Wait Until Keyword Succeeds    20m   5    设备信息获取值   BMS软件发布日期-1           2020-12-04
    Wait Until Keyword Succeeds    20m   5    设备信息获取值   BMS软件发布日期-8            2020-12-04     
    Wait Until Keyword Succeeds    20m   5    设备信息获取值   BMS软件发布日期-${铁锂电池组数}     2020-12-04
    # 测试台上下电操作    ${测试台编号}    OFF
    # sleep    5
    # 测试台上下电操作    ${测试台编号}    ON
    # 连接CSU
    # 设置子工具值    smartli_fb100b1    all    只读    BMS软件发布日期    2020
    # 设置子工具值    smartli_fb100b1    all    只读    BMS软件发布日期mouth    12
    # 设置子工具值    smartli_fb100b1    all    只读    BMS软件发布日期day    5
    # Wait Until Keyword Succeeds    20m   5    设备信息获取值   BMS软件发布日期-1           2020-12-05
    # Wait Until Keyword Succeeds    20m   5    设备信息获取值   BMS软件发布日期-8            2020-12-05     
    # Wait Until Keyword Succeeds    20m   5    设备信息获取值   BMS软件发布日期-${铁锂电池组数}     2020-12-05
    # 设置子工具值    smartli_fb100b1    all    只读    BMS软件发布日期    2020
    # 设置子工具值    smartli_fb100b1    all    只读    BMS软件发布日期mouth    12
    # 设置子工具值    smartli_fb100b1    all    只读    BMS软件发布日期day    4
    # Wait Until Keyword Succeeds    20m   5    设备信息获取值   BMS软件发布日期-1           2020-12-04
    # Wait Until Keyword Succeeds    20m   5    设备信息获取值   BMS软件发布日期-8            2020-12-04     
    # Wait Until Keyword Succeeds    20m   5    设备信息获取值   BMS软件发布日期-${铁锂电池组数}     2020-12-04

BDCU软件发布日期获取测试
    连接CSU
    设置子工具值    smartli_fb100b1    all    只读    BDU版本日期    2020
    设置子工具值    smartli_fb100b1    all    只读    BDU版本日期mouth    12
    设置子工具值    smartli_fb100b1    all    只读    BDU版本日期day    12
    Wait Until Keyword Succeeds    20m   5    设备信息获取值    BDCU软件发布日期-1      2020-12-12
    Wait Until Keyword Succeeds    20m   5    设备信息获取值   BDCU软件发布日期-8       2020-12-12
    Wait Until Keyword Succeeds    20m   5    设备信息获取值   BDCU软件发布日期-${铁锂电池组数}       2020-12-12
    设置子工具值    smartli_fb100b1    all    只读    BDU版本日期    2020
    设置子工具值    smartli_fb100b1    all    只读    BDU版本日期mouth    11
    设置子工具值    smartli_fb100b1    all    只读    BDU版本日期day    11
    Wait Until Keyword Succeeds    20m   5    设备信息获取值   BDCU软件发布日期-1      2020-11-11
    Wait Until Keyword Succeeds    20m   5    设备信息获取值   BDCU软件发布日期-8      2020-11-11
    Wait Until Keyword Succeeds    20m   5    设备信息获取值   BDCU软件发布日期-${铁锂电池组数}    2020-11-11
    测试台上下电操作    ${测试台编号}    OFF
    sleep    5
    测试台上下电操作    ${测试台编号}    ON
    连接CSU
    设置子工具值    smartli_fb100b1    all    只读    BDU版本日期    2020
    设置子工具值    smartli_fb100b1    all    只读    BDU版本日期mouth    12
    设置子工具值    smartli_fb100b1    all    只读    BDU版本日期day    12
    Wait Until Keyword Succeeds    20m   5    设备信息获取值   BDCU软件发布日期-1      2020-12-12
    Wait Until Keyword Succeeds    20m   5    设备信息获取值   BDCU软件发布日期-8      2020-12-12
    Wait Until Keyword Succeeds    20m   5    设备信息获取值   BDCU软件发布日期-${铁锂电池组数}    2020-12-12
    设置子工具值    smartli_fb100b1    all    只读    BDU版本日期    2020
    设置子工具值    smartli_fb100b1    all    只读    BDU版本日期mouth    11
    设置子工具值    smartli_fb100b1    all    只读    BDU版本日期day    11
    Wait Until Keyword Succeeds    20m   5    设备信息获取值   BDCU软件发布日期-1      2020-11-11
    Wait Until Keyword Succeeds    20m   5    设备信息获取值   BDCU软件发布日期-8      2020-11-11
    Wait Until Keyword Succeeds    20m   5    设备信息获取值   BDCU软件发布日期-${铁锂电池组数}    2020-11-11

电池生产日期获取测试
    [Tags]    no test
    连接CSU
    设置子工具值    smartli_fb100b1    all    只读    电芯出厂日期    2020
    设置子工具值    smartli_fb100b1    all    只读    电芯出厂日期mouth    12
    设置子工具值    smartli_fb100b1    all    只读    电芯出厂日期day    12
    Wait Until Keyword Succeeds    20m   5    设备信息获取值    电池生产日期-1        2020-12-12
    Wait Until Keyword Succeeds    20m   5    设备信息获取值    电池生产日期-8        2020-12-12
    Wait Until Keyword Succeeds    20m   5    设备信息获取值    电池生产日期-${铁锂电池组数}      2020-12-12
    设置子工具值    smartli_fb100b1    all    只读    电芯出厂日期    2020
    设置子工具值    smartli_fb100b1    all    只读    电芯出厂日期mouth    11
    设置子工具值    smartli_fb100b1    all    只读    电芯出厂日期day    11
    Wait Until Keyword Succeeds    20m   5    设备信息获取值   电池生产日期-1        2020-11-11
    Wait Until Keyword Succeeds    20m   5    设备信息获取值    电池生产日期-8       2020-11-11
    Wait Until Keyword Succeeds    20m   5    设备信息获取值    电池生产日期-${铁锂电池组数}      2020-11-11
    测试台上下电操作    ${测试台编号}    OFF
    sleep    5
    测试台上下电操作    ${测试台编号}    ON
    连接CSU
    设置子工具值    smartli_fb100b1    all    只读    电芯出厂日期    2020
    设置子工具值    smartli_fb100b1    all    只读    电芯出厂日期mouth    12
    设置子工具值    smartli_fb100b1    all    只读    电芯出厂日期day    12
    Wait Until Keyword Succeeds    20m   5    设备信息获取值   电池生产日期-1        2020-12-12
    Wait Until Keyword Succeeds    20m   5    设备信息获取值    电池生产日期-8       2020-12-12
    Wait Until Keyword Succeeds    20m   5    设备信息获取值    电池生产日期-${铁锂电池组数}      2020-12-12
    设置子工具值    smartli_fb100b1    all    只读    电芯出厂日期    2020
    设置子工具值    smartli_fb100b1    all    只读    电芯出厂日期mouth    11
    设置子工具值    smartli_fb100b1    all    只读    电芯出厂日期day    11
    Wait Until Keyword Succeeds    20m   5    设备信息获取值   电池生产日期-1        2020-11-11
    Wait Until Keyword Succeeds    20m   5    设备信息获取值    电池生产日期-8       2020-11-11
    Wait Until Keyword Succeeds    20m   5    设备信息获取值    电池生产日期-${铁锂电池组数}      2020-11-11

