*** Settings ***
Test Setup        Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
Test Teardown     Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_交流电表系统名称
    连接CSU
    设置子工具值    ACmeter    all    只读    型号    DTSD36
    FOR    ${交流电表序号}    IN RANGE    1    4
        Wait Until Keyword Succeeds    5m    2    对比WEB和能源网管单个数据  获取交流电表设备信息  交流电表系统名称-${交流电表序号}
        ...   DTSD36
    END
    设置子工具值    ACmeter    all    只读    型号    DTSD35M2123456
    FOR    ${交流电表序号}    IN RANGE    1    4
        Wait Until Keyword Succeeds    5m    2    对比WEB和能源网管单个数据  获取交流电表设备信息  交流电表系统名称-${交流电表序号}
        ...   DTSD35M2123456
    END
    设置子工具值    ACmeter    all    只读    型号    DTSD36M
    FOR    ${交流电表序号}    IN RANGE    1    4
        Wait Until Keyword Succeeds    5m    2    对比WEB和能源网管单个数据  获取交流电表设备信息  交流电表系统名称-${交流电表序号}
        ...   DTSD36M
    END

power_sm_0002_交流电表软件版本
    连接CSU
    设置子工具值    ACmeter    all    只读    版本    255
    FOR    ${交流电表序号}    IN RANGE    1    4
        Wait Until Keyword Succeeds    5m    2    对比WEB和能源网管单个数据  获取交流电表设备信息  交流电表软件版本-${交流电表序号}
        ...   V2.55
    END
    设置子工具值    ACmeter    all    只读    版本    0
    FOR    ${交流电表序号}    IN RANGE    1    4
        Wait Until Keyword Succeeds    5m    2    对比WEB和能源网管单个数据  获取交流电表设备信息  交流电表软件版本-${交流电表序号}
        ...   V0.0
    END
    设置子工具值    ACmeter    all    只读    版本    211
    FOR    ${交流电表序号}    IN RANGE    1    4
        Wait Until Keyword Succeeds    5m    2    对比WEB和能源网管单个数据  获取交流电表设备信息  交流电表软件版本-${交流电表序号}
        ...   V2.11
    END
