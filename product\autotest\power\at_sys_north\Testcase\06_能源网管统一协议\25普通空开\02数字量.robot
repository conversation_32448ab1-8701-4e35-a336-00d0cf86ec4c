*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
批量获取SDDU数字量
    [Documentation]    配电单元次要负载下电状态~0x22001020040001
    写入CSV文档    SDU2数字量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=digital data    INDEX_DEVICE_CH=智能直流配电单元
    @{排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SDU2排除数字量信号}    ${排除列表}    1
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    智能直流配电单元    digital data    ${SDU2排除数字量信号}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    1    power_sm
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    power_sm    SDU2     呼叫    ${缺省值列表}    SDDU数字量获取测试    获取SDDU数字量   
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    2    power_sm
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    power_sm    SDU2     呼叫    ${缺省值列表}    SDDU数字量获取测试    获取SDDU数字量    
    ${缺省值列表}   获取缺省值列表  ${power_sm待测}    0    power_sm
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    power_sm    SDU2     呼叫    ${缺省值列表}     SDDU数字量获取测试    获取SDDU数字量   


   
 
获取SDDU工作状态
    [Documentation]    0:无/Null;1:正常/Normal;2:告警/Alarm;3:通讯断/CommFail;4:升级/Update;
    连接CSU
    #正常
    设置子工具值    SDU2    all    呼叫    SDU负载分路状态1    1
    设置子工具值    SDU2    all    呼叫    SDU负载分路状态2    1
    设置子工具值    SDU2    all    呼叫    SDU负载分路状态3    1
    设置子工具值    SDU2    all    呼叫    SDU负载分路状态4    1
    FOR    ${直流配电单元序号}    IN RANGE    1    ${智能直流配电单元数量}+1
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    SDDU工作状态-${直流配电单元序号}    正常
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取SDDU数字量    SDDU工作状态-${直流配电单元序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    1
    END
    设置子工具值    SDU2    all    呼叫    SDU负载分路状态1    0
    设置子工具值    SDU2    all    呼叫    SDU负载分路状态2    0
    设置子工具值    SDU2    all    呼叫    SDU负载分路状态3    0
    设置子工具值    SDU2    all    呼叫    SDU负载分路状态4    0
    FOR    ${直流配电单元序号}    IN RANGE    1    ${智能直流配电单元数量}+1
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    SDDU工作状态-${直流配电单元序号}    告警
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取SDDU数字量    SDDU工作状态-${直流配电单元序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    2
    END
    设置子工具值    SDU2    all    呼叫    SDU负载分路状态1    1
    设置子工具值    SDU2    all    呼叫    SDU负载分路状态2    1
    设置子工具值    SDU2    all    呼叫    SDU负载分路状态3    1
    设置子工具值    SDU2    all    呼叫    SDU负载分路状态4    1
    Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    SDDU工作状态-${直流配电单元序号}    正常
    控制子工具运行停止    SDU2    关闭
    FOR    ${直流配电单元序号}    IN RANGE    1    ${智能直流配电单元数量}+1
        Wait Until Keyword Succeeds    5m    5    查询web信息直到值为    SDDU工作状态-${直流配电单元序号}    通讯断
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取SDDU数字量    SDDU工作状态-${直流配电单元序号}    ${SSH}
        should be equal as numbers    ${power_sm获取值1}    3
    END
    [Teardown]    Run Keywords    控制子工具运行停止    SDU2    启动
    ...    AND    sleep    3min
