*** Settings ***
Resource          ../../../../../测试用例关键字.robot

*** Test Cases ***
PU休眠和唤醒测试
    [Tags]    PMSA-NTest
    连接CSU
    #子设备工具模拟PU
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU休眠-1
    Wait Until Keyword Succeeds    10m     2    信号量数据值为    PU休眠状态-1    是
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU休眠-10
    Wait Until Keyword Succeeds    10m     2    信号量数据值为    PU休眠状态-10    是
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU休眠-20
    Wait Until Keyword Succeeds    10m     2    信号量数据值为    PU休眠状态-20    是
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU休眠-30
    Wait Until Keyword Succeeds    10m     2    信号量数据值为    PU休眠状态-30    是
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU休眠-40
    Wait Until Keyword Succeeds    10m     2    信号量数据值为    PU休眠状态-40    是
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU唤醒-1
    Wait Until Keyword Succeeds    10m     2    信号量数据值为    PU休眠状态-1    否
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU唤醒-10
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    PU休眠状态-10    否
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU唤醒-20
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    PU休眠状态-20    否
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU唤醒-30
    Wait Until Keyword Succeeds    10m     2    信号量数据值为    PU休眠状态-30    否
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU唤醒-40
    Wait Until Keyword Succeeds    10m     2    信号量数据值为    PU休眠状态-40    否

PU风扇调速允许和禁止测试
    [Tags]    PMSA-NTest
    连接CSU
    #子设备工具模拟PU
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU风扇调速允许-1
    Wait Until Keyword Succeeds    2m     2    信号量数据值为    PU风扇控制状态-1    自动
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU风扇调速允许-10
    Wait Until Keyword Succeeds    2m     2    信号量数据值为    PU风扇控制状态-10    自动
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU风扇调速允许-20
    Wait Until Keyword Succeeds    2m     2    信号量数据值为    PU风扇控制状态-20    自动
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU风扇调速允许-30
    Wait Until Keyword Succeeds    2m     2    信号量数据值为    PU风扇控制状态-30    自动
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU风扇调速允许-40
    Wait Until Keyword Succeeds    2m     2    信号量数据值为    PU风扇控制状态-40    自动
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU风扇调速禁止-1
    Wait Until Keyword Succeeds    2m    2    信号量数据值为    PU风扇控制状态-1    全速
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU风扇调速禁止-10
    Wait Until Keyword Succeeds    2m     2    信号量数据值为    PU风扇控制状态-10    全速
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU风扇调速禁止-20
    Wait Until Keyword Succeeds    2m     2    信号量数据值为    PU风扇控制状态-20    全速
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU风扇调速禁止-30
    Wait Until Keyword Succeeds    2m     2    信号量数据值为    PU风扇控制状态-30    全速
    Wait Until Keyword Succeeds    10X    2    设置web控制量    PU风扇调速禁止-40
    Wait Until Keyword Succeeds    2m     2    信号量数据值为    PU风扇控制状态-40    全速

PU通讯中断告警清除测试
    [Tags]    PMSA-NTest
    连接CSU
    设置子工具个数    pu    1
    sleep    3m
    wait until keyword succeeds    10m    2    查询指定告警信息    PU通讯中断
    wait until keyword succeeds    30m    60    获取指定告警数量大于    PU通讯中断    37
    Wait Until Keyword Succeeds    20X    2    设置web控制量    PU通讯中断告警清除-4
    wait until keyword succeeds    30m    2    精准判断告警不存在    PU通讯中断-4
    wait until keyword succeeds    30m    60    获取指定告警数量大于    PU通讯中断    36
    Wait Until Keyword Succeeds    20X    2    设置web控制量    PU通讯中断告警清除-10
    wait until keyword succeeds    30m    2    精准判断告警不存在    PU通讯中断-10
    wait until keyword succeeds    30m    60    获取指定告警数量大于    PU通讯中断    35
    Wait Until Keyword Succeeds    20X    2    设置web控制量    PU通讯中断告警清除-20
    wait until keyword succeeds    30m    2    精准判断告警不存在    PU通讯中断-20
    wait until keyword succeeds    30m    60    获取指定告警数量大于    PU通讯中断    34
    Wait Until Keyword Succeeds    20X    2    设置web控制量    PU通讯中断告警清除-30
    wait until keyword succeeds    30m    2    精准判断告警不存在    PU通讯中断-30
    wait until keyword succeeds    30m    60    获取指定告警数量大于    PU通讯中断    33
    Wait Until Keyword Succeeds    20X    2    设置web控制量    PU通讯中断告警清除-40
    wait until keyword succeeds    30m    2    精准判断告警不存在    PU通讯中断-40
    wait until keyword succeeds    30m    60    获取指定告警数量大于    PU通讯中断    32
    [Teardown]    Run keywords    设置子工具个数    pu    48
    ...    AND    等待所有PU工作正常

PU设备统计测试
    [Tags]    PMSA-NTest
    连接CSU
    设置子工具个数    pu    1
    sleep    3m
    ${级别设置值}    获取web参数量    PU通讯中断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    PU通讯中断    严重
    wait until keyword succeeds    10m    2    查询指定告警信息    PU通讯中断
    wait until keyword succeeds    30m    60    获取指定告警数量大于    PU通讯中断    46
    wait until keyword succeeds    1m     2    设置web控制量    PU设备统计
    wait until keyword succeeds    23m    2    查询指定告警信息不为    PU通讯中断
    [Teardown]    Run keywords    设置子工具个数    pu    48
    ...    AND    sleep    3m

CAN设备统计测试
    [Tags]    PMSA-NTest
    连接CSU
    设置子工具个数    SMR    48
    控制子工具运行停止    SMR    启动
    sleep    1m
    Wait Until Keyword Succeeds    10    2    设置web控制量    CAN总线设备统计
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    在线整流器数量    48
    等待所有PU工作正常
    设置子工具个数    pu    1
    sleep    3m
    ${级别设置值}    获取web参数量    PU通讯中断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    PU通讯中断    严重
    ${级别设置值}    获取web参数量    整流器通讯中断
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    整流器通讯中断    严重
    设置子工具个数    SMR    3
    wait until keyword succeeds    10m    2    查询指定告警信息    整流器通讯中断
    wait until keyword succeeds    30m    60    获取指定告警数量大于    整流器通讯中断    44
    wait until keyword succeeds    10m    2    查询指定告警信息    PU通讯中断
    wait until keyword succeeds    30m    60    获取指定告警数量大于    PU通讯中断    46
    wait until keyword succeeds    1m     2    设置web控制量    CAN总线设备统计
    wait until keyword succeeds    23m    2    查询指定告警信息不为    PU通讯中断
    wait until keyword succeeds    23m    2    查询指定告警信息不为    整流器通讯中断
    [Teardown]    Run keywords    设置子工具个数    pu    48
    ...    AND    控制子工具运行停止    SMR    关闭
    ...    AND    sleep    3m
