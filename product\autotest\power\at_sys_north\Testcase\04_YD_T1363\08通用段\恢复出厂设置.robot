*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
恢复出厂设置_1
    ${设置协议数据}    设置1363通用控制数据    ${g_prtcl_1363}    <<恢复出厂设置1-6 (6)~CID1=40H>>    <<恢复出厂设置1-6~CID2=90>>    CSU恢复出厂设置    00    ${g_ver_1363}
    should be true    ${设置协议数据}
    sleep    1min
    ${是否复位}    Set Variable    False
    FOR    ${page}    IN RANGE  1    4
        @{历史事件内容}    获取web历史事件内容    ${empty}    ${empty}    所有    ${page}    100
        FOR    ${i}    IN   @{历史事件内容}
            ${是否复位}    Evaluate    "系统复位 恢复出厂设置" in "${i}"
            ${操作记录}    Set Variable    ${i}
            Exit For Loop If    ${是否复位}==True
        END
        Exit For Loop If    ${是否复位}==True
    END
    Should Contain    ${操作记录}    north 1363
    Should Be True    ${是否复位}

恢复出厂设置_2
    ${设置协议数据}    设置1363通用控制数据    ${g_prtcl_1363}    <<恢复出厂设置1-6 (6)~CID1=41H>>    <<恢复出厂设置1-6~CID2=90>>    CSU恢复出厂设置    00    ${g_ver_1363}
    should be true    ${设置协议数据}
    sleep    1min
    ${是否复位}    Set Variable    False
    FOR    ${page}    IN RANGE    1    4
        @{历史事件内容}    获取web历史事件内容    ${empty}    ${empty}    所有    ${page}    100
        FOR    ${i}    IN    @{历史事件内容}
            ${是否复位}    Evaluate    "系统复位 恢复出厂设置" in "${i}"
            ${操作记录}    Set Variable    ${i}
            Exit For Loop If    ${是否复位}==True
        END
        Exit For Loop If    ${是否复位}==True
    END
    Should Contain    ${操作记录}    north 1363
    Should Be True    ${是否复位}

恢复出厂设置_3
    ${设置协议数据}    设置1363通用控制数据    ${g_prtcl_1363}    <<恢复出厂设置1-6 (6)~CID1=42H>>    <<恢复出厂设置1-6~CID2=90>>    CSU恢复出厂设置    00    ${g_ver_1363}
    should be true    ${设置协议数据}
    sleep   1min
    ${是否复位}    Set Variable    False
    FOR    ${page}    IN RANGE    1    4
        @{历史事件内容}    获取web历史事件内容    ${empty}    ${empty}    所有    ${page}    100
        FOR    ${i}    IN    @{历史事件内容}
            ${是否复位}    Evaluate    "系统复位 恢复出厂设置" in "${i}"
            ${操作记录}    Set Variable    ${i}
            Exit For Loop If    ${是否复位}==True
        END
        Exit For Loop If    ${是否复位}==True
    END
    Should Contain    ${操作记录}    north 1363
    Should Be True    ${是否复位}

恢复出厂设置_4
    ${设置协议数据}    设置1363通用控制数据    ${g_prtcl_1363}    <<恢复出厂设置1-6 (6)~CID1=91H>>    <<恢复出厂设置1-6~CID2=90>>    CSU恢复出厂设置    00    ${g_ver_1363}
    should be true    ${设置协议数据}
    sleep    1min
    ${是否复位}    Set Variable    False
    FOR    ${page}    IN RANGE    1    4
        @{历史事件内容}    获取web历史事件内容    ${empty}    ${empty}    所有    ${page}    100
        FOR    ${i}    IN    @{历史事件内容}
            ${是否复位}    Evaluate    "系统复位 恢复出厂设置" in "${i}"
            ${操作记录}    Set Variable    ${i}
            Exit For Loop If    ${是否复位}==True
        END
        Exit For Loop If    ${是否复位}==True
    END
    Should Contain    ${操作记录}    north 1363
    Should Be True    ${是否复位}

恢复出厂设置_5
    ${设置协议数据}    设置1363通用控制数据    ${g_prtcl_1363}    <<恢复出厂设置1-6 (6)~CID1=D0H>>    <<恢复出厂设置1-6~CID2=90>>    CSU恢复出厂设置    00    ${g_ver_1363}
    should be true    ${设置协议数据}
    sleep    1min
    ${是否复位}    Set Variable    False
    FOR    ${page}    IN RANGE    1    4
        @{历史事件内容}    获取web历史事件内容    ${empty}    ${empty}    所有    ${page}    100
        FOR    ${i}    IN    @{历史事件内容}
            ${是否复位}    Evaluate    "系统复位 恢复出厂设置" in "${i}"
            ${操作记录}    Set Variable    ${i}
            Exit For Loop If    ${是否复位}==True
        END
        Exit For Loop If    ${是否复位}==True
    END
    Should Contain    ${操作记录}    north 1363
    Should Be True    ${是否复位}

恢复出厂设置_6
    ${设置协议数据}    设置1363通用控制数据    ${g_prtcl_1363}    <<恢复出厂设置1-6 (6)~CID1=46H>>    <<恢复出厂设置1-6~CID2=90>>    CSU恢复出厂设置    00    ${g_ver_1363}
    should be true    ${设置协议数据}
    sleep    1min
    ${是否复位}    Set Variable    False
    FOR    ${page}    IN RANGE    1    4
        @{历史事件内容}    获取web历史事件内容    ${empty}    ${empty}    所有    ${page}    100
        FOR    ${i}    IN    @{历史事件内容}
            ${是否复位}    Evaluate    "系统复位 恢复出厂设置" in "${i}"
            ${操作记录}    Set Variable    ${i}
            Exit For Loop If    ${是否复位}==True
        END
        Exit For Loop If    ${是否复位}==True
    END
    Should Contain    ${操作记录}    north 1363
    Should Be True    ${是否复位}
