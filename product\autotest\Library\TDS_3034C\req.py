#coding=utf-8
import requests
import HTMLParser
import os
import re

def download_pic(ip,add='.\\',name='Image'):
    url ='http://'+ip+'/Image.png'
    response = requests.get(url)
# 获取的文本实际上是图片的二进制文本
    img = response.content
# 将他拷贝到本地文件 w 写  b 二进制  wb代表写入二进制文本
    with open( add+name+'.png','wb' ) as f:
        f.write(img)
    print u'获取图片成功！'



def reqtext(ip,cmd):
##    payload = {'COMMAND': 'ETHERnet:IPADDress?'}
    "使用requests方法获取HTML原码"
    data=''
    yk = HTMLP()
    payload = {'COMMAND': cmd}
    try:
        r = requests.post('http://'+ip+"/Comm.html",data=payload)
        data=r.text
        yk.feed(data)   #使用HTMLParser库的feed方法解析HTML，得到想要的文本
    except:
        print 'Error:与示波器连接失败，请检查IP设置！'
    return yk.ReturnValue


class HTMLP(HTMLParser.HTMLParser):
    "使用HTMLParser库解析HTML脚本"
    a_text = False
    ReturnValue = ''
   
    def __init__(self):   
        HTMLParser.HTMLParser.__init__(self)
        
    def handle_starttag(self,tag,attr):
        if tag == 'textarea':  #如果开始的标签为'textarea'
            self.a_text = True
              
    def handle_endtag(self,tag):
        if tag == 'textarea':   #如果结束的标签为'textarea'
            self.a_text = False
              
    def handle_data(self,data):
        if self.a_text:  
##            print data
            self.ReturnValue = data


if __name__ == '__main__':
##    req('***********','ID?')
    tt=reqtext('***********','ID?')
    print tt
 
