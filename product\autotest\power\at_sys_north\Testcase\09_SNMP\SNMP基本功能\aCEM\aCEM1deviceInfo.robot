*** Settings ***
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp_test关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/Web关键字_V30.robot
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
snmp批量获取ACEM设备信息
    写入CSV文档    交流电表设备信息获取测试    信号名称    信号值    结果
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string32    INDEX_DEVICE_CH=交流电表
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${电表排除设备名称信息}    ${排除列表}
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    aCEM    deviceInfo
    FOR    ${i}    IN    @{snmp待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    snmp_name
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        Run Keyword And Continue On Failure    北向协议_南向厂家信息封装判断结果    snmp    ACmeter    ${信号名称}    只读    ZXDU48-FB100B3    交流电表设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
        Run Keyword And Continue On Failure    北向协议_南向厂家信息封装判断结果    snmp    ACmeter    ${信号名称}    只读    ZTE-smartli    交流电表设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
        Run Keyword And Continue On Failure    北向协议_南向厂家信息封装判断结果    snmp    ACmeter    ${信号名称}    只读    ZXDU48 FB100C2    交流电表设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
    END
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=float    INDEX_DEVICE_CH=交流电表
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    aCEM    deviceInfo
    FOR    ${i}    IN    @{snmp待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    snmp_name
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        ${缺省值}    获取web参数上下限范围    ${信号名称}
        Run Keyword And Continue On Failure    北向协议_南向厂家信息封装判断结果    snmp    ACmeter    ${信号名称}    只读    ${缺省值}[1]    交流电表设备信息获取测试    数值    null    ${节点名称}    ${信号序号}
        Run Keyword And Continue On Failure    北向协议_南向厂家信息封装判断结果    snmp    ACmeter    ${信号名称}    只读    ${缺省值}[2]    交流电表设备信息获取测试    数值    null    ${节点名称}    ${信号序号}
        Run Keyword And Continue On Failure    北向协议_南向厂家信息封装判断结果    snmp    ACmeter    ${信号名称}    只读    ${缺省值}[0]    交流电表设备信息获取测试    数值    null    ${节点名称}    ${信号序号}
    END
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=int    INDEX_DEVICE_CH=交流电表
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${电表排除设备名称信息}    ${排除列表}
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    aCEM    deviceInfo
    FOR    ${i}    IN    @{snmp待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    snmp_name
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        ${缺省值}    获取web参数上下限范围    ${信号名称}
        Run Keyword And Continue On Failure    北向协议_南向厂家信息封装判断结果    snmp    ACmeter    ${信号名称}    只读    ${缺省值}[1]    交流电表设备信息获取测试    数值    null    ${节点名称}    ${信号序号}
        Run Keyword And Continue On Failure    北向协议_南向厂家信息封装判断结果    snmp    ACmeter    ${信号名称}    只读    ${缺省值}[2]    交流电表设备信息获取测试    数值    null    ${节点名称}    ${信号序号}
        Run Keyword And Continue On Failure    北向协议_南向厂家信息封装判断结果    snmp    ACmeter    ${信号名称}    只读    ${缺省值}[0]    交流电表设备信息获取测试    数值    null    ${节点名称}    ${信号序号}
    END
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=date    INDEX_DEVICE_CH=交流电表
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    null    ${排除列表}
    ${snmp待测}    snmp_获取snmp指定内容的待测列表    ${列表1}    aCEM    deviceInfo
    FOR    ${i}    IN    @{snmp待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${节点名称}    Get From Dictionary    ${i}    snmp_name
        ${信号序号}    Get From Dictionary    ${i}    Vindex
        Run Keyword And Continue On Failure    北向协议_南向厂家信息封装判断结果    snmp    ACmeter    ${信号名称}    只读    2018-11-15    交流电表设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
        Run Keyword And Continue On Failure    北向协议_南向厂家信息封装判断结果    snmp    ACmeter    ${信号名称}    只读    2021-08-23    交流电表设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
        Run Keyword And Continue On Failure    北向协议_南向厂家信息封装判断结果    snmp    ACmeter    ${信号名称}    只读    2018-07-28    交流电表设备信息获取测试    字符    null    ${节点名称}    ${信号序号}
    END
    断开连接SNMP

snmp_0004_交流电表软件版本X
    连接CSU
    设置子工具值    ACmeter    all    只读    版本    255
    Wait Until Keyword Succeeds    5m    5    厂家信号量数据值为(强制获取)    交流电表软件版本-1    V2.55
    Wait Until Keyword Succeeds    5m    5    厂家信号量数据值为(强制获取)    交流电表软件版本-2    V2.55
    Wait Until Keyword Succeeds    5m    5    厂家信号量数据值为(强制获取)    交流电表软件版本-3    V2.55
    进行SNMP_V2/V3连接    ${snmp连接方式}
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备字符型/数值型厂家信息    字符    V2.55    aCEMSoftwareVersion1value    0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备字符型/数值型厂家信息    字符    V2.55    aCEMSoftwareVersion2value    0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备字符型/数值型厂家信息    字符    V2.55    aCEMSoftwareVersion3value    0
    设置子工具值    ACmeter    all    只读    版本    0
    Wait Until Keyword Succeeds    5m    5    厂家信号量数据值为(强制获取)    交流电表软件版本-1    V0.0
    Wait Until Keyword Succeeds    5m    5    厂家信号量数据值为(强制获取)    交流电表软件版本-2    V0.0
    Wait Until Keyword Succeeds    5m    5    厂家信号量数据值为(强制获取)    交流电表软件版本-3    V0.0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备字符型/数值型厂家信息    字符    V0.0    aCEMSoftwareVersion1value    0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备字符型/数值型厂家信息    字符    V0.0    aCEMSoftwareVersion2value    0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备字符型/数值型厂家信息    字符    V0.0    aCEMSoftwareVersion3value    0
    设置子工具值    ACmeter    all    只读    版本    211
    Wait Until Keyword Succeeds    5m    5    厂家信号量数据值为(强制获取)    交流电表软件版本-1    V2.11
    Wait Until Keyword Succeeds    5m    5    厂家信号量数据值为(强制获取)    交流电表软件版本-2    V2.11
    Wait Until Keyword Succeeds    5m    5    厂家信号量数据值为(强制获取)    交流电表软件版本-3    V2.11
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备字符型/数值型厂家信息    字符    V2.11   aCEMSoftwareVersion1value    0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备字符型/数值型厂家信息    字符    V2.11    aCEMSoftwareVersion2value    0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备字符型/数值型厂家信息    字符    V2.11    aCEMSoftwareVersion3value    0
    断开连接SNMP

snmp_0002_交流电表系统名称X
    连接CSU
    设置子工具值    ACmeter    all    只读    型号    DTSD36
    Wait Until Keyword Succeeds    5m    5    厂家信号量数据值为(强制获取)    交流电表系统名称-1    DTSD36
    Wait Until Keyword Succeeds    5m    5    厂家信号量数据值为(强制获取)    交流电表系统名称-2    DTSD36
    Wait Until Keyword Succeeds    5m    5    厂家信号量数据值为(强制获取)    交流电表系统名称-3    DTSD36
    进行SNMP_V2/V3连接    ${snmp连接方式}
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备字符型/数值型厂家信息    字符    DTSD36    aCEMSystemName1value    0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备字符型/数值型厂家信息    字符    DTSD36    aCEMSystemName2value    0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备字符型/数值型厂家信息    字符    DTSD36    aCEMSystemName3value    0
    设置子工具值    ACmeter    all    只读    型号    DTSD35M2123456
    Wait Until Keyword Succeeds    5m    5    厂家信号量数据值为(强制获取)    交流电表系统名称-1    DTSD35M2123456
    Wait Until Keyword Succeeds    5m    5    厂家信号量数据值为(强制获取)    交流电表系统名称-2    DTSD35M2123456
    Wait Until Keyword Succeeds    5m    5    厂家信号量数据值为(强制获取)    交流电表系统名称-3    DTSD35M2123456
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备字符型/数值型厂家信息    字符    DTSD35M2123456    aCEMSystemName1value    0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备字符型/数值型厂家信息    字符    DTSD35M2123456    aCEMSystemName2value    0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备字符型/数值型厂家信息    字符    DTSD35M2123456    aCEMSystemName3value    0
    设置子工具值    ACmeter    all    只读    型号    DTSD36M
    Wait Until Keyword Succeeds    5m    5    厂家信号量数据值为(强制获取)    交流电表系统名称-1    DTSD36M
    Wait Until Keyword Succeeds    5m    5    厂家信号量数据值为(强制获取)    交流电表系统名称-2    DTSD36M
    Wait Until Keyword Succeeds    5m    5    厂家信号量数据值为(强制获取)    交流电表系统名称-3    DTSD36M
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备字符型/数值型厂家信息    字符    DTSD36M    aCEMSystemName1value    0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备字符型/数值型厂家信息    字符    DTSD36M    aCEMSystemName2value    0
    Wait Until Keyword Succeeds    3m    5    snmp_南向子设备字符型/数值型厂家信息    字符    DTSD36M    aCEMSystemName3value    0
    断开连接SNMP
