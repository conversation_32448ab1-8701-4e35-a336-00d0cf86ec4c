*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
power_sm_0001_CSU控制量
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    ${sheet_name}    Set Variable    设置CSU控制量
    ${能源网管数据}    能源网管协议_设置控制量    ${sheet_name}    ${BOARD_IP}    ${SSH}
    ${对比结果}    能源网管协议_批量对比参数设置    ${能源网管数据}    设置CSU控制量
    Should Be True    ${对比结果}
    [Teardown]    Run keywords    连接CSU
    ...    AND    Wait Until Keyword Succeeds    10    2    设置web控制量    允许所有告警
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
