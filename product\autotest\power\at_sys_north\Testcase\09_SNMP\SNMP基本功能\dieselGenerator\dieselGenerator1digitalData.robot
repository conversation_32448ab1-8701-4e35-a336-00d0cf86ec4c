*** Settings ***
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp_test关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/snmp_test关键字_V30.robot
Resource          ../../../../../../ztepwrlibrary/Resource/V30CSU关键字/Web关键字_V30.robot
Resource          ../../../../测试用例关键字.robot

*** Test Cases ***
snmp_0001_dieselGenerator1digitalData
    ${比较结果}    对比数据_V2C    dieselGenerator1digitalData
    should be true    ${比较结果}

snmp_0002_油机控制器工作模式√
    [Documentation]    0:自动/Auto;1:手动/Manual;2:其他模式/Other Mode
    ...
    ...    默认为2
    连接CSU
    设置子工具值    oileng    all    只读    灯亮为    4
    sleep    1m
    ${获取值1}    获取web实时数据    油机控制器工作模式
    #
    Comment    连接SNMP_V2C    ${g_community}
    进行SNMP_V2/V3连接    ${snmp连接方式}
    ${snmp获取值1}    获取SNMP数据_单个    dGControlerMode1value
    should be true    '${获取值1}'=='手动'
    should be true    ${snmp获取值1}==1
    设置子工具值    oileng    all    只读    灯亮为    8
    sleep    1m
    ${获取值1}    获取web实时数据    油机控制器工作模式
    ${snmp获取值1}    获取SNMP数据_单个    dGControlerMode1value
    should be true    '${获取值1}'=='自动'
    should be true    ${snmp获取值1}==0
    设置子工具值    oileng    all    只读    灯亮为    0
    sleep    1m
    ${获取值1}    获取web实时数据    油机控制器工作模式
    ${snmp获取值1}    获取SNMP数据_单个    dGControlerMode1value
    断开连接SNMP
    should be true    '${获取值1}'=='其他模式'
    should be true    ${snmp获取值1}==2
    [Teardown]    设置子工具值    oileng    all    只读    灯亮为    8

snmp_0004_油机控制状态_无
    [Tags]    3

snmp_0006_油机状态
    [Documentation]    0:自动/Auto;1:手动/Manual;2:其他模式/Other Mode
    ...
    ...
    ...    0:未配置/Not Config;1:停止/OFF;2:运行/ON;3:异常/Abnormal;4:过渡/Transition
    [Setup]    测试用例关键字.测试用例前置条件
    关闭交流源输出
    仅电池模拟器供电
    连接CSU
    进行SNMP_V2/V3连接    ${snmp连接方式}
    Wait Until Keyword Succeeds    10m    1    设置web参数量    交流输入场景    纯油机
    ${获取值1}    获取web实时数据    油机控制器工作模式
    run keyword if    '${获取值1}' != '自动'    Wait Until Keyword Succeeds    5m    1    设置web控制量    油机进入自动模式
    设置子工具值    oileng    all    只读    灯亮为    8
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    油机控制器工作模式    自动
    ${snmp获取值1}    获取SNMP数据_单个    dGControlerMode1value
    should be equal as numbers    ${snmp获取值1}    0
    Wait Until Keyword Succeeds    10m    1    设置web控制量    油机开启
    设置子工具值    oileng    all    只读    发电 A 相电压    299
    设置子工具值    oileng    all    只读    发电 B 相电压    300
    设置子工具值    oileng    all    只读    发电 C 相电压    298
    设置子工具值    oileng    all    只读    转速    1500
    Wait Until Keyword Succeeds    5m    5     信号量数据值为(强制获取)    油机相电压_1    298
    Wait Until Keyword Succeeds    5m    5     信号量数据值为(强制获取)    油机相电压_2    300
    Wait Until Keyword Succeeds    5m    5     信号量数据值为(强制获取)    油机相电压_3    299
    Wait Until Keyword Succeeds    5m    5     信号量数据值为(强制获取)    油机转速        1500
    Wait Until Keyword Succeeds    20m    1    信号量数据值为    油机状态    异常
    ${snmp获取值1}    获取SNMP数据_单个    dGStatus1value
    should be equal as numbers    ${snmp获取值1}    3
    Wait Until Keyword Succeeds    20m    1    设置web控制量    油机开启
    同时设置三相电压频率    220    50
    打开交流源输出
    设置子工具值    oileng    all    只读    发电 A 相电压    299
    设置子工具值    oileng    all    只读    发电 B 相电压    300
    设置子工具值    oileng    all    只读    发电 C 相电压    298
    设置子工具值    oileng    all    只读    转速    1500
    Wait Until Keyword Succeeds    5m    5     信号量数据值为(强制获取)    油机相电压_1    298
    Wait Until Keyword Succeeds    5m    5     信号量数据值为(强制获取)    油机相电压_2    300
    Wait Until Keyword Succeeds    5m    5     信号量数据值为(强制获取)    油机相电压_3    299
    Wait Until Keyword Succeeds    5m    5     信号量数据值为(强制获取)    油机转速        1500
    Wait Until Keyword Succeeds    13m    1    信号量数据值为    油机状态    过渡
    ${snmp获取值1}    获取SNMP数据_单个    dGStatus1value
    should be equal as numbers    ${snmp获取值1}    4
    Wait Until Keyword Succeeds    13m    1    信号量数据值为    油机状态    运行
    ${snmp获取值1}    获取SNMP数据_单个    dGStatus1value
    should be equal as numbers    ${snmp获取值1}    2
    Wait Until Keyword Succeeds    15m    1    设置web控制量    油机关闭
    关闭交流源输出
    设置子工具值    oileng    all    只读    发电 A 相电压    0
    设置子工具值    oileng    all    只读    发电 B 相电压    0
    设置子工具值    oileng    all    只读    发电 C 相电压    0
    设置子工具值    oileng    all    只读    转速    0
    Wait Until Keyword Succeeds    5m    5     信号量数据值为(强制获取)    油机相电压_1    0
    Wait Until Keyword Succeeds    5m    5     信号量数据值为(强制获取)    油机相电压_2    0
    Wait Until Keyword Succeeds    5m    5     信号量数据值为(强制获取)    油机相电压_3    0
    Wait Until Keyword Succeeds    5m    5     信号量数据值为(强制获取)    油机转速        0
    Wait Until Keyword Succeeds    13m    1    信号量数据值为    油机状态    停止
    ${snmp获取值1}    获取SNMP数据_单个    dGStatus1value
    should be equal as numbers    ${snmp获取值1}    1
    断开连接SNMP
    [Teardown]    测试用例关键字.测试用例后置条件
