*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
批量获取SDDU设备信息
    [Tags]    3
    写入CSV文档    SDDU2设备信息获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string32    INDEX_DEVICE_CH=智能直流配电单元
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SDDU2排除系统名称}    ${排除列表}    2
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    智能直流配电单元    device info    ${SDDU2排除系统名称}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    ${信号1}    Set Variable    0
    ${信号2}    Set Variable    0
    ${信号3}    Set Variable    0
    ${信号4}    Set Variable    0
    FOR    ${i}    IN    @{power_sm待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${交流电表设备}    split string    ${信号名称}    ~
        ${交流电表设备1}    split string    ${交流电表设备}[0]    -
        Run keyword if    ${交流电表设备1}[1]!=1    continue for loop
        Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    power_sm    IntelAirSwitExtend    ${信号名称}    空开厂家1    V99.23    SDDU设备信息获取测试    字符    获取SDDU设备信息    null    null
        Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    power_sm    IntelAirSwitExtend    ${信号名称}    空开厂家1    V10.10    SDDU设备信息获取测试    字符    获取SDDU设备信息    null    null
        Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    power_sm    IntelAirSwitExtend    ${信号名称}    空开厂家1    V1.81    SDDU设备信息获取测试    字符    获取SDDU设备信息    null    null
    END
    Comment    ${信号1}    Set Variable    0
    Comment    ${信号2}    Set Variable    0
    Comment    ${信号3}    Set Variable    0
    Comment    ${信号4}    Set Variable    0
    Comment    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=string32    INDEX_DEVICE_CH=智能直流配电单元
    Comment    ${排除列表}    create list
    Comment    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${SDDU2排除软件版本}    ${排除列表}    2
    Comment    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    智能直流配电单元    device info    ${SDDU2排除软件版本}
    Comment    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    Comment    FOR    ${i}    IN    @{power_sm待测}
    Comment    \    ${信号名称}    Get From Dictionary    ${i}    signal_name
    Comment    \    @{信号名称和序号}    split string    ${信号名称}    _
    Comment    \    @{信号序号和设备序号}    split string    ${信号名称和序号}[1]    -
    Comment    \    ${信号序号}    Set Variable    ${信号序号和设备序号}[0]
    Comment    \    ${信号序号}    BuiltIn.Convert To Integer    ${信号序号}
    Comment    \    ${呼叫命令}    Evaluate    (${信号序号}-1)//4+1
    Comment    \    ${信号序号}    Run Keyword IF    ${呼叫命令}==1    Set Variable    1
    ...    ELSE IF    ${呼叫命令}==2    Set Variable    5
    ...    ELSE IF    ${呼叫命令}==3    Set Variable    9
    ...    ELSE    Set Variable    13
    Comment    \    ${信号1}    Run Keyword IF    ${信号序号}==1    Evaluate    ${信号1}+1
    ...    ELSE    Set Variable    ${信号1}
    Comment    \    ${信号2}    Run Keyword IF    ${信号序号}==5    Evaluate    ${信号2}+1
    ...    ELSE    Set Variable    ${信号2}
    Comment    \    ${信号3}    Run Keyword IF    ${信号序号}==9    Evaluate    ${信号3}+1
    ...    ELSE    Set Variable    ${信号3}
    Comment    \    ${信号4}    Run Keyword IF    ${信号序号}==13    Evaluate    ${信号4}+1
    ...    ELSE    Set Variable    ${信号4}
    Comment    \    Run Keyword IF    ${信号序号}==1    Run keyword if    ${信号1}>1    continue for loop
    ...    ELSE IF    ${信号序号}==5    Run keyword if    ${信号2}>1    continue for loop
    ...    ELSE IF    ${信号序号}==9    Run keyword if    ${信号3}>1    continue for loop
    ...    ELSE    Run keyword if    ${信号4}>1    continue for loop
    Comment    \    ${信号名称}    Set Variable    ${信号名称和序号}[0]_${信号序号}-${信号序号和设备序号}[1]
    Comment    \    ${工具命令}    Set Variable    空开厂家${呼叫命令}
    Comment    \    Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    power_sm    SDDU    ${信号名称}    ${工具命令}    VZXDU48 FB100B3    SDDU设备信息获取测试    字符    获取SDDU设备信息    null    null
    Comment    \    Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    power_sm    SDDU    ${信号名称}    ${工具命令}    ZTE-smartli    SDDU设备信息获取测试    字符    获取SDDU设备信息    null    null
    Comment    \    Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    power_sm    SDDU    ${信号名称}    ${工具命令}    VZXDU48 FB100C2    SDDU设备信息获取测试    字符    获取SDDU设备信息    null    null
    Comment    END
    Comment    @{参数列表}    create list    INDEX_SIGNAL_TYPE=device info    INDEX_DATA_TYPE=date    INDEX_DEVICE_CH=智能直流配电单元
    Comment    ${排除列表}    create list
    Comment    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${排除列表}    ${排除列表}    2
    Comment    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    智能直流配电单元    device info    ${排除列表}
    Comment    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    Comment    FOR    ${i}    IN    @{power_sm待测}
    Comment    \    ${信号名称}    Get From Dictionary    ${i}    signal_name
    Comment    \    Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    power_sm    SDDU    ${信号名称}    建链    2018-11-15    SDDU设备信息获取测试    字符    获取SDDU设备信息    null    null
    Comment    \    Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    power_sm    SDDU    ${信号名称}    建链    2021-08-23    SDDU设备信息获取测试    字符    获取SDDU设备信息    null    null
    Comment    \    Run Keyword And Continue On Failure    南向子设备厂家信息封装判断结果    power_sm    SDDU    ${信号名称}    建链    2018-10-31    SDDU设备信息获取测试    字符    获取SDDU设备信息    null    null
    
