*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
批量获取直流空调告警
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    写入CSV文档    直流空调数字量和告警量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=alarm    INDEX_DEVICE_CH=直流空调
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${直流空调排除告警量信号}    ${排除列表}    2
    ${power_sm1}    power_sm_获取网管字典指定设备指定信号量    直流空调    alarm    ${直流空调排除告警量信号}
    ${power_sm待测}    power_sm_获取网管字典指定内容的待测列表    ${power_sm1}    ${列表1}
    ${待测数据长度}    Get Length    ${power_sm待测}
    ${信号名称列表1}   create list
     FOR    ${i}    IN    @{power_sm待测}
        ${信号名称}    Get From Dictionary    ${i}    signal_name
        ${实时告警中的设备名称}    主动告警_组合实时告警中的设备名称    直流空调    ${信号名称}
        ${dict}    Create Dictionary
        Set To Dictionary    ${dict}     signal_name     ${信号名称}
        Set To Dictionary    ${dict}     device_name     ${实时告警中的设备名称}
		Append To List       ${信号名称列表1}    ${dict}
	END
    Run Keyword And Continue On Failure    南向RS485子设备告警量列表产生封装判断结果   power_sm    DMU_DCAirCondition    ${信号名称列表1}    
    ...    告警    ${告警产生}    直流空调数字量和告警量获取测试    环境    直流空调    null    null    null    null    null    null
    Run Keyword And Continue On Failure    南向RS485子设备告警量列表恢复封装判断结果   power_sm    DMU_DCAirCondition    ${信号名称列表1}    
    ...    告警    ${告警恢复}    直流空调数字量和告警量获取测试    环境    直流空调    null    null    null    null    null    null
    [Teardown]    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

直流空调通讯断告警&工作状态
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    ${级别设置值}    获取web参数量    <<直流空调通讯断~0x25001030010001>>
    run keyword if    '${级别设置值}'=='屏蔽'    wait until keyword succeeds    10    2    设置web参数量    <<直流空调通讯断~0x25001030010001>>    严重
    #子设备工具模拟
    控制子工具运行停止    DMU_DCAirCondition    关闭
    ${告警级别取值约定dict}    获取web参数的取值约定    <<直流空调通讯断~0x25001030010001>>
    ${级别设置值}    获取web参数量    <<直流空调通讯断~0x25001030010001>>
    FOR    ${直流空调序号}    IN RANGE    1    ${直流空调最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    直流空调工作状态-${直流空调序号}    通讯断
        wait until keyword succeeds    5m    2    查询指定告警信息    直流空调通讯断-${直流空调序号}
        ${power_sm获取值}    power_sm_获取一个数据的值    获取直流空调数字量    直流空调工作状态-${直流空调序号}    ${SSH}
        should be equal as strings    ${power_sm获取值}    3
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    直流空调通讯断-${直流空调序号}    ${SSH}
        should be true    ${power_sm告警存在}[0]
        ${power_sm告警级别}    get from dictionary    ${告警级别取值约定dict}    ${power_sm告警存在}[1]
        should be equal    ${power_sm告警级别}    ${级别设置值}
    END
    控制子工具运行停止    DMU_DCAirCondition    开启
    FOR    ${直流空调序号}    IN RANGE    1    ${直流空调最大数}+1
        Wait Until Keyword Succeeds    5m    5    信号量数据值为    直流空调工作状态-${直流空调序号}    正常
        wait until keyword succeeds    5m    1    判断告警不存在    直流空调通讯断-${直流空调序号}
        ${power_sm获取值}    power_sm_获取一个数据的值    获取直流空调数字量    直流空调工作状态-${直流空调序号}    ${SSH}
        should be equal as strings    ${power_sm获取值}    1
        ${power_sm告警存在}    判断power_sm是否存在单个实时告警    直流空调通讯断-${直流空调序号}    ${SSH}
        should not be true    ${power_sm告警存在}[0]
    END
    wait until keyword succeeds    5m    2    查询指定告警信息不为    直流空调通讯断
    [Teardown]    run keywords    控制子工具运行停止    DMU_DCAirCondition    开启
    ...    AND    sleep    5m
    ...    AND    设置web设备参数量为默认值    <<直流空调通讯断~0x25001030010001>>
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接
