*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
# 1363批量获取SMR模拟量
#     [Documentation]    21min
#     [Setup]
#     写入CSV文档    整流器模拟量获取测试    信号名称    信号值    结果    备注
#     连接CSU
#     P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=整流器
#     ${排除列表}    create list
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除模拟量信号}    ${排除列表}    2    ${模拟整流器地址}
#     ${列表1}    处理1104/1363SMR或PU设备序号大于40的部分    ${列表1}    ${模拟整流器地址}
#     ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取整流段模拟量~CID1=41H>>    <<获取整流段模拟量~CID2=42H>>    None    ${g_ver_1363}
#     ${web数据}    通过1104_data获取web数据    ${协议数据}
#     ${1104待测}    1104_获取1104/1363指定设备指定信号量    ${协议数据}    ${web数据}    ${列表1}
#     Comment    ${指定数据1}    Create Dictionary    signal_name    <<整流器输出电流-10~0x7001010020001>>    convention    False    device_name    整流器    data_type    float    unit    A    1104_name    模块输出电流-10
#     Comment    @{1104待测}    Create List    ${指定数据1}
#     FOR    ${i}    IN    @{1104待测}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${1104协议名称}    Get From Dictionary    ${i}    1104_name
#         ${缺省值}    获取web参数上下限范围    ${信号名称}
#         Run Keyword And Continue On Failure    南向子设备模拟量获取值封装判断结果    1363    SMR    ${信号名称}    模拟量    ${缺省值}[1]    整流器模拟量获取测试    null    null    null    <<获取整流段模拟量~CID1=41H>>    <<获取整流段模拟量~CID2=42H>>    ${1104协议名称}    None
#         Run Keyword And Continue On Failure    南向子设备模拟量获取值封装判断结果    1363    SMR    ${信号名称}    模拟量    ${缺省值}[2]    整流器模拟量获取测试    null    null    null    <<获取整流段模拟量~CID1=41H>>    <<获取整流段模拟量~CID2=42H>>    ${1104协议名称}    None
#         Run Keyword IF    ${缺省值}[1]!=${缺省值}[0] and ${缺省值}[2]!=${缺省值}[0]    Run Keyword And Continue On Failure    南向子设备模拟量获取值封装判断结果    1363    SMR    ${信号名称}    模拟量    ${缺省值}[0]    整流器模拟量获取测试    null    null    null    <<获取整流段模拟量~CID1=41H>>    <<获取整流段模拟量~CID2=42H>>    ${1104协议名称}
#         ...    None
#     END

1363批量获取SMR模拟量
    [Documentation]    21min
    [Setup]
    写入CSV文档    整流器模拟量获取测试    信号名称    信号值    结果    备注
    连接CSU
    P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=analog data    INDEX_DEVICE_CH=整流器
    ${排除列表}    create list
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除模拟量信号}    ${排除列表}    2    ${模拟整流器地址}
    ${列表1}    处理1104/1363SMR或PU设备序号大于40的部分    ${列表1}    ${模拟整流器地址}
    ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取整流段模拟量~CID1=41H>>    <<获取整流段模拟量~CID2=42H>>    None    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${1104待测}    1104_获取1104/1363指定设备指定信号量    ${协议数据}    ${web数据}    ${列表1}
    Comment    ${指定数据1}    Create Dictionary    signal_name    <<整流器输出电流-10~0x7001010020001>>    convention    False    device_name    整流器    data_type    float    unit    A    1104_name    模块输出电流-10
    Comment    @{1104待测}    Create List    ${指定数据1}
    # ${缺省值列表1}   获取缺省值列表  ${1104待测}    1
    # Run Keyword And Continue On Failure    1104南向子设备模拟量获取值封装判断结果    1104    SMR    模拟量    ${缺省值列表1}    整流器模拟量获取测试    null    null    null    <<获取整流段模拟量~CID1=41H>>    <<获取整流段模拟量~CID2=42H>>    None
    # ${缺省值列表2}   获取缺省值列表  ${1104待测}    2
    # Run Keyword And Continue On Failure    1104南向子设备模拟量获取值封装判断结果    1104    SMR    模拟量    ${缺省值列表2}    整流器模拟量获取测试    null    null    null    <<获取整流段模拟量~CID1=41H>>    <<获取整流段模拟量~CID2=42H>>    None
    # ${缺省值列表0}    获取缺省值列表    ${1104待测}    0
    # ${缺省值列表0}    1104检查缺省值列表    ${缺省值列表0}    ${缺省值列表1}    ${缺省值列表2} 
    # Run Keyword And Continue On Failure    1104南向子设备模拟量获取值封装判断结果    1104    SMR    数字量    ${缺省值列表0}    整流器数字量获取测试    null    null    null    <<获取整流段数字量~CID1=41H>>    <<获取整流段数字量~CID2=42H>>    None

    ${缺省值列表1}    获取缺省值列表    ${1104待测}    1    1363
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    1363    SMR    模拟量    ${缺省值列表1}    整流器模拟量获取测试    null    <<获取整流段模拟量~CID1=41H>>    <<获取整流段模拟量~CID2=42H>>    None
    ${缺省值列表2}    获取缺省值列表    ${1104待测}    2    1363
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    1363    SMR    模拟量    ${缺省值列表2}    整流器模拟量获取测试    null    <<获取整流段模拟量~CID1=41H>>    <<获取整流段模拟量~CID2=42H>>    None
    ${缺省值列表0}    获取缺省值列表    ${1104待测}    0    1363
    # ${缺省值列表0}    1104检查缺省值列表    ${缺省值列表0}    ${缺省值列表1}    ${缺省值列表2} 
    Run Keyword And Continue On Failure    南向子设备模拟量获取值列表封装判断结果    1363    SMR    模拟量    ${缺省值列表0}    整流器数字量获取测试    null    <<获取整流段数字量~CID1=41H>>    <<获取整流段数字量~CID2=42H>>    None



# 1363批量获取SMR数字量
#     [Documentation]    21min
#     [Setup]
#     写入CSV文档    整流器数字量获取测试    信号名称    信号值    结果    备注
#     连接CSU
#     @{参数列表}    create list    INDEX_SIGNAL_TYPE=digital data    INDEX_DEVICE_CH=整流器
#     ${排除列表}    create list
#     P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
#     ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除数字量信号}    ${排除列表}    2    ${模拟整流器地址}
#     ${列表1}    处理1104/1363SMR或PU设备序号大于40的部分    ${列表1}    ${模拟整流器地址}
#     ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取整流段数字量~CID1=41H>>    <<获取整流段数字量~CID2=43H>>    None    ${g_ver_1363}
#     ${web数据}    通过1104_data获取web数据    ${协议数据}
#     ${1104待测}    1104_获取1104/1363指定设备指定信号量    ${协议数据}    ${web数据}    ${列表1}
#     FOR    ${i}    IN    @{1104待测}
#         ${信号名称}    Get From Dictionary    ${i}    signal_name
#         ${1104协议名称}    Get From Dictionary    ${i}    1104_name
#         ${缺省值}    获取web参数上下限范围    ${信号名称}
#         Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    1363    SMR    ${信号名称}    数字量    ${缺省值}[1]    整流器数字量获取测试    null    null    null    <<获取整流段数字量~CID1=41H>>    <<获取整流段数字量~CID2=43H>>    ${1104协议名称}    None
#         Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    1363    SMR    ${信号名称}    数字量    ${缺省值}[2]    整流器数字量获取测试    null    null    null    <<获取整流段数字量~CID1=41H>>    <<获取整流段数字量~CID2=43H>>    ${1104协议名称}    None
#         Run Keyword IF    ${缺省值}[1]!=${缺省值}[0] and ${缺省值}[2]!=${缺省值}[0]    Run Keyword And Continue On Failure    南向子设备数字量获取值封装判断结果    1363    SMR    ${信号名称}    数字量    ${缺省值}[0]    整流器数字量获取测试    null    null    null    <<获取整流段数字量~CID1=41H>>    <<获取整流段数字量~CID2=43H>>    ${1104协议名称}
#         ...    None
#     END

1363批量获取SMR数字量
    [Documentation]    21min
    [Setup]    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    自由
    写入CSV文档    整流器数字量获取测试    信号名称    信号值    结果    备注
    连接CSU
    @{参数列表}    create list    INDEX_SIGNAL_TYPE=digital data    INDEX_DEVICE_CH=整流器
    ${排除列表}    create list
    P1104Keyword.init_dest_TestCasePath2    ${测试用例地址}
    ${列表1}    获取数据字典指定查询的信号量列表    ${参数列表}    null    ${整流器排除数字量信号}    ${排除列表}    2    ${模拟整流器地址}
    ${列表1}    处理1104/1363SMR或PU设备序号大于40的部分    ${列表1}    ${模拟整流器地址}
    ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取整流段数字量~CID1=41H>>    <<获取整流段数字量~CID2=43H>>    None    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${1104待测}    1104_获取1104/1363指定设备指定信号量    ${协议数据}    ${web数据}    ${列表1}
    # ${缺省值列表1}    获取缺省值列表    ${1104待测}    1    
    # Run Keyword And Continue On Failure    1104南向子设备数字量列表获取值封装判断结果    1363    SMR    数字量    ${缺省值列表1}    整流器数字量获取测试    null    null    null    <<获取整流段数字量~CID1=41H>>    <<获取整流段数字量~CID2=43H>>    None
    # ${缺省值列表2}    获取缺省值列表    ${1104待测}    2
    # Run Keyword And Continue On Failure    1104南向子设备数字量列表获取值封装判断结果    1363    SMR    数字量    ${缺省值列表2}    整流器数字量获取测试    null    null    null    <<获取整流段数字量~CID1=41H>>    <<获取整流段数字量~CID2=43H>>    None
    # ${缺省值列表0}    获取缺省值列表    ${1104待测}    0
    # ${缺省值列表0}    1104检查缺省值列表    ${缺省值列表0}    ${缺省值列表1}    ${缺省值列表2} 
    # Run Keyword And Continue On Failure    1104南向子设备数字量列表获取值封装判断结果    1363    SMR    数字量    ${缺省值列表0}    整流器数字量获取测试    null    null    null    <<获取整流段数字量~CID1=41H>>    <<获取整流段数字量~CID2=43H>>    None

    ${缺省值列表1}    获取缺省值列表    ${1104待测}    1    1363    
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    1363    SMR    数字量    ${缺省值列表1}    整流器数字量获取测试    null    <<获取整流段数字量~CID1=41H>>    <<获取整流段数字量~CID2=43H>>    None
    ${缺省值列表2}    获取缺省值列表    ${1104待测}    2    1363
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    1363    SMR    数字量    ${缺省值列表2}    整流器数字量获取测试    null    <<获取整流段数字量~CID1=41H>>    <<获取整流段数字量~CID2=43H>>    None
    ${缺省值列表0}    获取缺省值列表    ${1104待测}    0    1363
    # ${缺省值列表0}    1104检查缺省值列表    ${缺省值列表0}    ${缺省值列表1}    ${缺省值列表2} 
    Run Keyword And Continue On Failure    南向子设备数字量获取值列表封装判断结果    1363    SMR    数字量    ${缺省值列表0}    整流器数字量获取测试    null    <<获取整流段数字量~CID1=41H>>    <<获取整流段数字量~CID2=43H>>    None
    [Teardown]    Wait Until Keyword Succeeds    10    1    设置web参数量    交流节能模式    安全

1363_0006_V_3.0_1363_整流段_遥控
    [Tags]    T1-1
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-1    否    #为保证整流器数据获取正常
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流节能模式    自由
    sleep    5
    ${设置控制量1}    设置1104整流遥控数据    ${g_prtcl_1363}    整流模块均充    255    ${g_ver_1363}
    ${设置控制量2}    设置1104整流遥控数据    ${g_prtcl_1363}    整流模块浮充    255    ${g_ver_1363}
    ${设置控制量3}    设置1104整流遥控数据    ${g_prtcl_1363}    整流模块测试    255    ${g_ver_1363}
    ${设置控制量4}    设置1104整流遥控数据    ${g_prtcl_1363}    整流模块唤醒    1    ${g_ver_1363}
    ${设置控制量5}    设置1104整流遥控数据    ${g_prtcl_1363}    整流模块休眠    1    ${g_ver_1363}
    should be true    ${设置控制量1}
    should be true    ${设置控制量2}
    should be true    ${设置控制量3}
    should be true    ${设置控制量4}
    should be true    ${设置控制量5}
    [Teardown]    run keywords    连接CSU
    ...    AND    Wait Until Keyword Succeeds    30    1    设置web参数量    交流节能模式    安全

1363_0010_V_3.0_1363_整流段_休眠唤醒1
    [Documentation]    ${设置协议数据} P1104Keyword.set_data ${协议数据} <<整流段遥控~CID1=41H>> <<整流段遥控~CID2=45H>> ${参数名称} val=${值} ver=${g_ver} addr=${g_addr} port=${SERIAL_PORT}
    ...
    ...    ${协议数据} | ${参数名称} | ${值} | ${g_ver}
    Comment    仅有市电条件上电
    连接CSU
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器输入限功率-1    否    #为保证整流器数据获取正常
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流节能模式    自由
    sleep    5
    @{整流器序号随机list}    从1-15个数中随机选n个不重复的单体    ${模拟SMR个数}    4
    FOR    ${i}    IN    @{整流器序号随机list} 
        ${整流器地址}    evaluate    str(${i})
        Wait Until Keyword Succeeds    10X    2    设置1104整流遥控数据    ${g_prtcl_1363}    整流模块休眠    ${整流器地址}    ${g_ver_1363}
        Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-${i}    是
        Comment    sleep    1m
        ${整流器1休眠状态}    获取web实时数据    整流器休眠状态-${i}
        should be equal    '${整流器1休眠状态}'    '是'
        Wait Until Keyword Succeeds    10X    2    设置1104整流遥控数据    ${g_prtcl_1363}    整流模块唤醒    ${整流器地址}    ${g_ver_1363}
        Wait Until Keyword Succeeds    5m    2    信号量数据值为    整流器休眠状态-${i}    否
        Comment    sleep    1m
        ${整流器1休眠状态}    获取web实时数据    整流器休眠状态-${i}
        should be equal    '${整流器1休眠状态}'    '否'
    END
    Wait Until Keyword Succeeds    10    2    设置web参数量    交流节能模式    安全
    [Teardown]    run keywords    连接CSU
    ...    AND    Wait Until Keyword Succeeds    30    1    设置web参数量    交流节能模式    安全

1363_0012_V_3.0_1363_整流器软件版本(1-20)
    [Tags]    2
    ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取整流器软件版本(1-20)~CID1=41H>>    <<获取整流器软件版本(1-20)~CID2=85H>>    None    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${对比结果}    批量对比数据_1104_WEB    ${协议数据}    ${web数据}    1363_整流器软件版本(1-20)
    should be true    ${对比结果}

1363_0014_V_3.0_1363_整流器软件版本(21-40)
    [Tags]    2
    ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取整流器软件版本(21-40)~CID1=41H>>    <<获取整流器软件版本(21-40)~CID2=86H>>    None    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${对比结果}    批量对比数据_1104_WEB    ${协议数据}    ${web数据}    1363_整流器软件版本(21-40)
    should be true    ${对比结果}

1363_0016_V_3.0_1363_整流器软件版本(41-60)
    [Tags]    2
    ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取整流器软件版本(41-60)~CID1=41H>>    <<获取整流器软件版本(41-60)~CID2=87H>>    None    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${对比结果}    批量对比数据_1104_WEB    ${协议数据}    ${web数据}    1363_整流器软件版本(41-60)
    should be true    ${对比结果}

1363_0018_V_3.0_1363_整流器系统名称(1-20)
    [Tags]    2
    ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取整流器系统名称(1-20)~CID1=41H>>    <<获取整流器系统名称(1-20)~CID2=88H>>    None    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${对比结果}    批量对比数据_1104_WEB    ${协议数据}    ${web数据}    1363_整流器系统名称(1-20)
    should be true    ${对比结果}

1363_0020_V_3.0_1363_整流器系统名称(21-40)
    [Tags]    2
    ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取整流器系统名称(21-40)~CID1=41H>>    <<获取整流器系统名称(21-40)~CID2=89H>>    None    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${对比结果}    批量对比数据_1104_WEB    ${协议数据}    ${web数据}    1363_整流器系统名称(21-40)
    should be true    ${对比结果}

1363_0022_V_3.0_1363_整流器系统名称(41-60)
    [Tags]    2
    ${协议数据}    1104获取数据    ${g_prtcl_1363}    <<获取整流器系统名称(41-60)~CID1=41H>>    <<获取整流器系统名称(41-60)~CID2=8AH>>    None    ${g_ver_1363}
    ${web数据}    通过1104_data获取web数据    ${协议数据}
    ${对比结果}    批量对比数据_1104_WEB    ${协议数据}    ${web数据}    1363_整流器系统名称(41-60)
    should be true    ${对比结果}
