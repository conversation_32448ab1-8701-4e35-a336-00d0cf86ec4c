*** Settings ***
Resource          ../../../../../../测试用例关键字.robot

*** Test Cases ***

系统停电时设置测试
    
    铁锂电池管理初始化
    关闭交流源输出
    设置负载电压电流    53.5    10
    打开负载输出
    Wait Until Keyword Succeeds    5m    1    信号量数据值为    电池管理状态    系统停电
    Wait Until Keyword Succeeds    5m    1    设置失败的Web控制量    启动测试
    [Teardown]    Run keywords    关闭负载输出
    ...    AND    打开交流源输出

电池组无效时设置测试
    
    铁锂电池管理初始化
    Wait Until Keyword Succeeds    5m    1s    设置web参数量    电池组容量_1    0
    sleep    3
    Wait Until Keyword Succeeds    5m    1    设置失败的web控制量    启动测试
    [Teardown]    设置web参数量    电池组容量_1    100

直流电压低时设置测试
    
    铁锂电池管理初始化
    ${直流电压高范围}    获取web参数上下限范围    直流电压高阈值
    设置web参数量    直流电压高阈值    ${直流电压高范围}[0]
    ${直流电压过低范围}    获取web参数上下限范围    直流电压过低阈值
    设置web参数量    直流电压过低阈值    ${直流电压过低范围}[0]
    ${直流电压低范围}    获取web参数上下限范围    直流电压低阈值
    ${直流电压低可设范围}    获取web参数可设置范围    直流电压低阈值
    设置web参数量    直流电压低阈值    ${直流电压低可设范围}[1]
    ${直流电压低阈值}    获取web参数量    直流电压低阈值
    ${直流电压目标}    evaluate    ${直流电压低阈值}-0.5
    设置web参数量    充电电压    ${直流电压目标}
    sleep    2m
    向下调节电池电压    ${直流电压目标}
    sleep    5
    Wait Until Keyword Succeeds    10m    2    判断告警存在    直流电压低
    Wait Until Keyword Succeeds    5m    2s    设置失败的web控制量    启动测试
    ${告警恢复值}    evaluate    ${直流电压低阈值}+1
    设置web参数量    充电电压    ${告警恢复值}
    向上调节电池电压    ${告警恢复值}
    Wait Until Keyword Succeeds    5m    2    判断告警不存在    直流电压低
    [Teardown]    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    直流电压过高阈值    直流电压过低阈值
    ...    直流电压高阈值    直流电压低阈值    充电电压

电池下电时设置测试
    
    铁锂电池管理初始化
    #获取参数/默认值
    wait until keyword succeeds    10    1    设置web参数量    电池高温下电使能    允许
    ${告警级别}    获取web参数量    电池高温下电
    run keyword if    '${告警级别}'=='屏蔽'    设置web参数量    电池高温下电    严重
    wait until keyword succeeds    2m    1    判断告警不存在    电池高温下电
    wait until keyword succeeds    2m    1    判断告警不存在    电池回路断
    run keyword and ignore error    设置web参数量    三次下电类型    下电池
    sleep    3
    ${可设范围}    获取web参数可设置范围    电池高温下电温度
    设置web参数量    电池高温下电温度    ${可设范围}[0]
    run keyword and ignore error    设置web参数量    下电控制延时    0
    显示属性配置    电池高温下电控制状态    数字量    web_attr=On    gui_attr=On
    sleep    3
    #设置电池温度
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    10    2
    ...    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    设置负载电压电流    53.5    10
    打开负载输出
    Wait Until Keyword Succeeds    5m    2    信号量数据值为    电池高温下电控制状态    动作
    wait until keyword succeeds    10m    1    判断告警存在    电池高温下电
    Wait Until Keyword Succeeds    5m    1    设置失败的web控制量    启动测试
    #恢复
    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]    0    1
    ...    电池_1    电池温度
    sleep    10
    ${电池温度}    获取web实时数据    电池温度-1
    Wait Until Keyword Succeeds    10m    2    信号量数据值为    电池高温下电控制状态    恢复
    wait until keyword succeeds    4m    2    判断告警不存在    电池高温下电
    显示属性配置    电池高温下电控制状态    数字量    web_attr=Off    gui_attr=Off
    关闭负载输出
    [Teardown]    run keywords    Wait Until Keyword Succeeds    30    1    设置通道配置    ${plat.batttemps}[0]
    ...    0    1    电池_1    电池温度
    ...    AND    设置web设备参数量为默认值    电池高温下电温度    电池高温下电使能
    ...    AND    关闭负载输出

电池容量小于测试终止容量时设置测试
    
    铁锂电池管理初始化
    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1    20
    设置web参数量    负载一次下电使能    允许
    设置web参数量    负载二次下电使能    允许
    设置web参数量    下电模式    电池剩余容量
    sleep    5
    ${一次下电可设置范围}    获取web参数上下限范围    负载一次下电SOC阈值
    设置web参数量    负载一次下电SOC阈值    ${一次下电可设置范围}[0]
    ${二次下电可设置范围}    获取web参数上下限范围    负载二次下电SOC阈值
    设置web参数量    负载二次下电SOC阈值    ${二次下电可设置范围}[0]
    ${测试失败可设置范围}    获取web参数可设置范围    测试失败SOC阈值
    设置web参数量    测试失败SOC阈值    ${测试失败可设置范围}[1]
    ${测试终止可设置范围}    获取web参数可设置范围    测试终止SOC阈值
    ${测试终止设置值}    evaluate    ${测试终止可设置范围}[1]-3
    设置web参数量    测试终止SOC阈值    ${测试终止设置值}
    ${测试终止容量}    获取web参数量    测试终止SOC阈值
    Wait Until Keyword Succeeds    10    1    设置web参数量    市电额定有功功率    2.1
    Wait Until Keyword Succeeds    10    1    设置web 参数量    市电降额系数    85
    ${电压}    获取web实时数据    直流电压
    缓慢设置负载电压电流    ${电压}    120
    打开负载输出
    Wait Until Keyword Succeeds    32m    1    信号量数据值小于    电池组当前容量比率-1    ${测试终止容量}
    ${电池当前容量}    获取web实时数据    电池组当前容量比率-1
    Wait Until Keyword Succeeds    5m    1    设置失败的web控制量    启动测试
    关闭负载输出
    [Teardown]    Run keywords    Wait Until Keyword Succeeds    10    1    设置web设备参数量为默认值    负载一次下电SOC阈值
    ...    负载二次下电SOC阈值    电池下电SOC阈值    测试终止SOC阈值    测试失败SOC阈值
    ...    AND    设置web设备参数量为默认值    市电降额系数    市电额定有功功率
    ...    AND    Wait Until Keyword Succeeds    10    1    设置web参数量    电池组容量_1
    ...    100
    ...    AND    关闭负载输出

充电保护时设置测试
    
    铁锂电池管理初始化
    显示属性配置    电池充电保护    数字量    web_attr=On    gui_attr=On
    设置子工具值    smartli    all    只读    电池充电保护    1
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    电池充电保护-1     是
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    电池充电保护-2     是
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    电池充电保护-3    是
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动测试
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    测试
    显示属性配置    电池充电保护    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    设置子工具值    smartli    all    只读    电池充电保护    0

放电保护时设置测试
    
    铁锂电池管理初始化
    显示属性配置    电池放电保护    数字量    web_attr=On    gui_attr=On
    设置子工具值    smartli    all    只读    电池放电保护    1
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    电池放电保护-1     是
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    电池放电保护-2     是
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    电池放电保护-3    是
    Wait Until Keyword Succeeds    5m    1    设置web控制量    启动测试
    Wait Until Keyword Succeeds    30    1    信号量数据值为    电池管理状态    测试
    显示属性配置    电池充电保护    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    设置子工具值    smartli    all    只读    电池放电保护    0

BMS通讯断时设置测试
    
    铁锂电池管理初始化
    显示属性配置    BMS通信断状态    数字量    web_attr=On    gui_attr=On
    Comment    关闭交流源输出
    #子设备工具模拟
    控制子工具运行停止    smartli    关闭
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    BMS通信断状态-1     异常
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    BMS通信断状态-2     异常
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    BMS通信断状态-3    异常
    ${级别设置值}    获取web参数量    BMS通信断告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    BMS通信断告警    严重
    wait until keyword succeeds    5m    2    查询指定告警信息    BMS通信断告警
    Wait Until Keyword Succeeds    5m    1    设置失败的web控制量    启动测试
    重新启动FB100B3    3
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    BMS通信断状态-1     正常
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    BMS通信断状态-2     正常
    Wait Until Keyword Succeeds    10m    5    信号量数据值为    BMS通信断状态-3    正常
    wait until keyword succeeds    5m    2    查询指定告警信息不为    BMS通信断告警
    显示属性配置    BMS通信断状态    数字量    web_attr=Off    gui_attr=Off
    [Teardown]    重新启动FB100B3    3
