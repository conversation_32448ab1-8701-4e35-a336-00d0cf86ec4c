*** Settings ***
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
获取和设置扩展温湿度参数量(扩展温度低阈值)
    [Setup]    Run Keywords    连接CSU
    ...    AND    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    FOR    ${i}    IN RANGE    1    9
        Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_${i}
        sleep    10
        ${缺省值}    获取WEB参数上下限范围    扩展温度低阈值_${i}
        ${可设置范围}    获取WEB参数可设置范围    扩展温度低阈值_${i}
        ${超下限}    Evaluate    ${可设置范围}[0]-${缺省值}[4]
        ${超上限}    Evaluate    ${可设置范围}[1]+${缺省值}[4]
        ${初始值}    获取WEB参数量    扩展温度低阈值_${i}
        ${设置结果1}    能源网管协议_设置数据    系统运行环境 扩展温度低阈值[${i}]    ${超下限}    None    ${g_energySC_addr}    ${SSH}
        ${设置结果1}    Run Keyword IF    '${设置结果1}[result]'=='ok'    Set Variable    True
        ...    ELSE    Set Variable    False
        sleep    10
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取系统运行环境参数量    系统运行环境 扩展温度低阈值[${i}]    ${SSH}
        ${web获取结果1}    获取WEB参数量    扩展温度低阈值_${i}
        should not be true    ${设置结果1}
        should be equal as numbers    ${power_sm获取值1}    ${初始值}
        should be equal as numbers    ${web获取结果1}    ${初始值}
        ${设置结果2}    能源网管协议_设置数据    系统运行环境 扩展温度低阈值[${i}]    ${超上限}    None    ${g_energySC_addr}    ${SSH}
        ${设置结果2}    Run Keyword IF    '${设置结果2}[result]'=='ok'    Set Variable    True
        ...    ELSE    Set Variable    False
        sleep    10
        ${power_sm获取值2}    power_sm_获取一个数据的值    获取系统运行环境参数量    系统运行环境 扩展温度低阈值[${i}]    ${SSH}
        ${web获取结果2}    获取WEB参数量    扩展温度低阈值_${i}
        should not be true    ${设置结果2}
        should be equal as numbers    ${power_sm获取值2}    ${初始值}
        should be equal as numbers    ${web获取结果2}    ${初始值}
        ${设置结果3}    能源网管协议_设置数据    系统运行环境 扩展温度低阈值[${i}]    ${可设置范围}[0]    None    ${g_energySC_addr}    ${SSH}
        ${设置结果3}    Run Keyword IF    '${设置结果3}[result]'=='ok'    Set Variable    True
        ...    ELSE    Set Variable    False
        sleep    10
        ${power_sm获取值3}    power_sm_获取一个数据的值    获取系统运行环境参数量    系统运行环境 扩展温度低阈值[${i}]    ${SSH}
        ${web获取结果3}    获取WEB参数量    扩展温度低阈值_${i}
        should be true    ${设置结果3}
        should be equal as numbers    ${power_sm获取值3}    ${可设置范围}[0]
        should be equal as numbers    ${web获取结果3}    ${可设置范围}[0]
        ${设置结果4}    能源网管协议_设置数据    系统运行环境 扩展温度低阈值[${i}]    ${可设置范围}[1]    None    ${g_energySC_addr}    ${SSH}
        ${设置结果4}    Run Keyword IF    '${设置结果4}[result]'=='ok'    Set Variable    True
        ...    ELSE    Set Variable    False
        sleep    10
        ${power_sm获取值4}    power_sm_获取一个数据的值    获取系统运行环境参数量    系统运行环境 扩展温度低阈值[${i}]    ${SSH}
        ${web获取结果4}    获取WEB参数量    扩展温度低阈值_${i}
        should be true    ${设置结果4}
        should be equal as numbers    ${power_sm获取值4}    ${可设置范围}[1]
        should be equal as numbers    ${web获取结果4}    ${可设置范围}[1]
        ${设置结果4}    能源网管协议_设置数据    系统运行环境 扩展温度低阈值[${i}]    ${初始值}    None    ${g_energySC_addr}    ${SSH}
        ${设置结果4}    Run Keyword IF    '${设置结果4}[result]'=='ok'    Set Variable    True
        ...    ELSE    Set Variable    False
        sleep    10
        ${power_sm获取值4}    power_sm_获取一个数据的值    获取系统运行环境参数量    系统运行环境 扩展温度低阈值[${i}]    ${SSH}
        ${web获取结果4}    获取WEB参数量    扩展温度低阈值_${i}
        should be true    ${设置结果4}
        should be equal as numbers    ${power_sm获取值4}    ${初始值}
        should be equal as numbers    ${web获取结果4}    ${初始值}
    END
    [Teardown]    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

获取和设置扩展温湿度参数量(扩展温度高阈值)
    [Setup]    Run Keyword if    '${SSH}'=='True'    power_sm_创建加密连接
    连接CSU
    FOR    ${i}    IN RANGE    1    9
        Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    温度传感器    系统运行环境    扩展温度_${i}
        sleep    10
        ${缺省值}    获取WEB参数上下限范围    扩展温度高阈值_${i}
        ${可设置范围}    获取WEB参数可设置范围    扩展温度高阈值_${i}
        ${超下限}    Evaluate    ${可设置范围}[0]-${缺省值}[4]
        ${超上限}    Evaluate    ${可设置范围}[1]+${缺省值}[4]
        ${初始值}    获取WEB参数量    扩展温度高阈值_${i}
        ${设置结果1}    能源网管协议_设置数据    系统运行环境 扩展温度高阈值[${i}]    ${超下限}    None    ${g_energySC_addr}    ${SSH}
        ${设置结果1}    Run Keyword IF    '${设置结果1}[result]'=='ok'    Set Variable    True
        ...    ELSE    Set Variable    False
        sleep    10
        ${power_sm获取值1}    power_sm_获取一个数据的值    获取系统运行环境参数量    系统运行环境 扩展温度高阈值[${i}]    ${SSH}
        ${web获取结果1}    获取WEB参数量    扩展温度高阈值_${i}
        should not be true    ${设置结果1}
        should be equal as numbers    ${power_sm获取值1}    ${初始值}
        should be equal as numbers    ${web获取结果1}    ${初始值}
        ${设置结果2}    能源网管协议_设置数据    系统运行环境 扩展温度高阈值[${i}]    ${超上限}    None    ${g_energySC_addr}    ${SSH}
        ${设置结果2}    Run Keyword IF    '${设置结果2}[result]'=='ok'    Set Variable    True
        ...    ELSE    Set Variable    False
        sleep    10
        ${power_sm获取值2}    power_sm_获取一个数据的值    获取系统运行环境参数量    系统运行环境 扩展温度高阈值[${i}]    ${SSH}
        ${web获取结果2}    获取WEB参数量    扩展温度高阈值_${i}
        should not be true    ${设置结果2}
        should be equal as numbers    ${power_sm获取值2}    ${初始值}
        should be equal as numbers    ${web获取结果2}    ${初始值}
        ${设置结果3}    能源网管协议_设置数据    系统运行环境 扩展温度高阈值[${i}]    ${可设置范围}[0]    None    ${g_energySC_addr}    ${SSH}
        ${设置结果3}    Run Keyword IF    '${设置结果3}[result]'=='ok'    Set Variable    True
        ...    ELSE    Set Variable    False
        sleep    10
        ${power_sm获取值3}    power_sm_获取一个数据的值    获取系统运行环境参数量    系统运行环境 扩展温度高阈值[${i}]    ${SSH}
        ${web获取结果3}    获取WEB参数量    扩展温度高阈值_${i}
        should be true    ${设置结果3}
        should be equal as numbers    ${power_sm获取值3}    ${可设置范围}[0]
        should be equal as numbers    ${web获取结果3}    ${可设置范围}[0]
        ${设置结果4}    能源网管协议_设置数据    系统运行环境 扩展温度高阈值[${i}]    ${可设置范围}[1]    None    ${g_energySC_addr}    ${SSH}
        ${设置结果4}    Run Keyword IF    '${设置结果4}[result]'=='ok'    Set Variable    True
        ...    ELSE    Set Variable    False
        sleep    10
        ${power_sm获取值4}    power_sm_获取一个数据的值    获取系统运行环境参数量    系统运行环境 扩展温度高阈值[${i}]    ${SSH}
        ${web获取结果4}    获取WEB参数量    扩展温度高阈值_${i}
        should be true    ${设置结果4}
        should be equal as numbers    ${power_sm获取值4}    ${可设置范围}[1]
        should be equal as numbers    ${web获取结果4}    ${可设置范围}[1]
        ${设置结果4}    能源网管协议_设置数据    系统运行环境 扩展温度高阈值[${i}]    ${初始值}    None    ${g_energySC_addr}    ${SSH}
        ${设置结果4}    Run Keyword IF    '${设置结果4}[result]'=='ok'    Set Variable    True
        ...    ELSE    Set Variable    False
        sleep    10
        ${power_sm获取值4}    power_sm_获取一个数据的值    获取系统运行环境参数量    系统运行环境 扩展温度高阈值[${i}]    ${SSH}
        ${web获取结果4}    获取WEB参数量    扩展温度高阈值_${i}
        should be true    ${设置结果4}
        should be equal as numbers    ${power_sm获取值4}    ${初始值}
        should be equal as numbers    ${web获取结果4}    ${初始值}
    END
    [Teardown]    Run Keyword if    '${SSH}'=='True'    power_sm_断开加密连接

获取和设置扩展温湿度参数量(扩展湿度高阈值)
    [Setup]
    连接CSU
    Comment    Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    湿度传感器    系统运行环境    扩展湿度
    Comment    sleep    10
    ${缺省值}    获取WEB参数上下限范围    扩展湿度高阈值
    ${可设置范围}    获取WEB参数可设置范围    扩展湿度高阈值
    ${超下限}    Evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    Evaluate    ${可设置范围}[1]+${缺省值}[4]
    ${初始值}    获取WEB参数量    扩展湿度高阈值
    ${设置结果1}    能源网管协议_设置数据    系统运行环境 扩展湿度高阈值    ${超下限}    None    ${g_energySC_addr}    ${SSH}
    ${设置结果1}    Run Keyword IF    '${设置结果1}[result]'=='ok'    Set Variable    True
    ...    ELSE    Set Variable    False
    sleep    10
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取系统运行环境参数量    系统运行环境 扩展湿度高阈值    ${SSH}
    ${web获取结果1}    获取WEB参数量    扩展湿度高阈值
    should not be true    ${设置结果1}
    should be equal as numbers    ${power_sm获取值1}    ${初始值}
    should be equal as numbers    ${web获取结果1}    ${初始值}
    ${设置结果2}    能源网管协议_设置数据    系统运行环境 扩展湿度高阈值    ${超上限}    None    ${g_energySC_addr}    ${SSH}
    ${设置结果2}    Run Keyword IF    '${设置结果2}[result]'=='ok'    Set Variable    True
    ...    ELSE    Set Variable    False
    sleep    10
    ${power_sm获取值2}    power_sm_获取一个数据的值    获取系统运行环境参数量    系统运行环境 扩展湿度高阈值    ${SSH}
    ${web获取结果2}    获取WEB参数量    扩展湿度高阈值
    should not be true    ${设置结果2}
    should be equal as numbers    ${power_sm获取值2}    ${初始值}
    should be equal as numbers    ${web获取结果2}    ${初始值}
    ${设置结果3}    能源网管协议_设置数据    系统运行环境 扩展湿度高阈值    ${可设置范围}[0]    None    ${g_energySC_addr}    ${SSH}
    ${设置结果3}    Run Keyword IF    '${设置结果3}[result]'=='ok'    Set Variable    True
    ...    ELSE    Set Variable    False
    sleep    10
    ${power_sm获取值3}    power_sm_获取一个数据的值    获取系统运行环境参数量    系统运行环境 扩展湿度高阈值    ${SSH}
    ${web获取结果3}    获取WEB参数量    扩展湿度高阈值
    should be true    ${设置结果3}
    should be equal as numbers    ${power_sm获取值3}    ${可设置范围}[0]
    should be equal as numbers    ${web获取结果3}    ${可设置范围}[0]
    ${设置结果4}    能源网管协议_设置数据    系统运行环境 扩展湿度高阈值    ${可设置范围}[1]    None    ${g_energySC_addr}    ${SSH}
    ${设置结果4}    Run Keyword IF    '${设置结果4}[result]'=='ok'    Set Variable    True
    ...    ELSE    Set Variable    False
    sleep    10
    ${power_sm获取值4}    power_sm_获取一个数据的值    获取系统运行环境参数量    系统运行环境 扩展湿度高阈值    ${SSH}
    ${web获取结果4}    获取WEB参数量    扩展湿度高阈值
    should be true    ${设置结果4}
    should be equal as numbers    ${power_sm获取值4}    ${可设置范围}[1]
    should be equal as numbers    ${web获取结果4}    ${可设置范围}[1]
    ${设置结果4}    能源网管协议_设置数据    系统运行环境 扩展湿度高阈值    ${初始值}    None    ${g_energySC_addr}    ${SSH}
    ${设置结果4}    Run Keyword IF    '${设置结果4}[result]'=='ok'    Set Variable    True
    ...    ELSE    Set Variable    False
    sleep    10
    ${power_sm获取值4}    power_sm_获取一个数据的值    获取系统运行环境参数量    系统运行环境 扩展湿度高阈值    ${SSH}
    ${web获取结果4}    获取WEB参数量    扩展湿度高阈值
    should be true    ${设置结果4}
    should be equal as numbers    ${power_sm获取值4}    ${初始值}
    should be equal as numbers    ${web获取结果4}    ${初始值}
    [Teardown]

获取和设置扩展温湿度参数量(扩展湿度低阈值)
    [Setup]
    连接CSU
    Comment    Wait Until Keyword Succeeds    30    1    设置温湿度传感器通道    湿度传感器    系统运行环境    扩展湿度
    Comment    sleep    10
    ${缺省值}    获取WEB参数上下限范围    扩展湿度低阈值
    ${可设置范围}    获取WEB参数可设置范围    扩展湿度低阈值
    ${超下限}    Evaluate    ${可设置范围}[0]-${缺省值}[4]
    ${超上限}    Evaluate    ${可设置范围}[1]+${缺省值}[4]
    ${初始值}    获取WEB参数量    扩展湿度低阈值
    ${设置结果1}    能源网管协议_设置数据    系统运行环境 扩展湿度低阈值    ${超下限}    None    ${g_energySC_addr}    ${SSH}
    ${设置结果1}    Run Keyword IF    '${设置结果1}[result]'=='ok'    Set Variable    True
    ...    ELSE    Set Variable    False
    sleep    10
    ${power_sm获取值1}    power_sm_获取一个数据的值    获取系统运行环境参数量    系统运行环境 扩展湿度低阈值    ${SSH}
    ${web获取结果1}    获取WEB参数量    扩展湿度低阈值
    should not be true    ${设置结果1}
    should be equal as numbers    ${power_sm获取值1}    ${初始值}
    should be equal as numbers    ${web获取结果1}    ${初始值}
    ${设置结果2}    能源网管协议_设置数据    系统运行环境 扩展湿度低阈值    ${超上限}    None    ${g_energySC_addr}    ${SSH}
    ${设置结果2}    Run Keyword IF    '${设置结果2}[result]'=='ok'    Set Variable    True
    ...    ELSE    Set Variable    False
    sleep    10
    ${power_sm获取值2}    power_sm_获取一个数据的值    获取系统运行环境参数量    系统运行环境 扩展湿度低阈值    ${SSH}
    ${web获取结果2}    获取WEB参数量    扩展湿度低阈值
    should not be true    ${设置结果2}
    should be equal as numbers    ${power_sm获取值2}    ${初始值}
    should be equal as numbers    ${web获取结果2}    ${初始值}
    ${设置结果3}    能源网管协议_设置数据    系统运行环境 扩展湿度低阈值    ${可设置范围}[0]    None    ${g_energySC_addr}    ${SSH}
    ${设置结果3}    Run Keyword IF    '${设置结果3}[result]'=='ok'    Set Variable    True
    ...    ELSE    Set Variable    False
    sleep    10
    ${power_sm获取值3}    power_sm_获取一个数据的值    获取系统运行环境参数量    系统运行环境 扩展湿度低阈值    ${SSH}
    ${web获取结果3}    获取WEB参数量    扩展湿度低阈值
    should be true    ${设置结果3}
    should be equal as numbers    ${power_sm获取值3}    ${可设置范围}[0]
    should be equal as numbers    ${web获取结果3}    ${可设置范围}[0]
    ${设置结果4}    能源网管协议_设置数据    系统运行环境 扩展湿度低阈值    ${可设置范围}[1]    None    ${g_energySC_addr}    ${SSH}
    ${设置结果4}    Run Keyword IF    '${设置结果4}[result]'=='ok'    Set Variable    True
    ...    ELSE    Set Variable    False
    sleep    10
    ${power_sm获取值4}    power_sm_获取一个数据的值    获取系统运行环境参数量    系统运行环境 扩展湿度低阈值    ${SSH}
    ${web获取结果4}    获取WEB参数量    扩展湿度低阈值
    should be true    ${设置结果4}
    should be equal as numbers    ${power_sm获取值4}    ${可设置范围}[1]
    should be equal as numbers    ${web获取结果4}    ${可设置范围}[1]
    ${设置结果4}    能源网管协议_设置数据    系统运行环境 扩展湿度低阈值    ${初始值}    None    ${g_energySC_addr}    ${SSH}
    ${设置结果4}    Run Keyword IF    '${设置结果4}[result]'=='ok'    Set Variable    True
    ...    ELSE    Set Variable    False
    sleep    10
    ${power_sm获取值4}    power_sm_获取一个数据的值    获取系统运行环境参数量    系统运行环境 扩展湿度低阈值    ${SSH}
    ${web获取结果4}    获取WEB参数量    扩展湿度低阈值
    should be true    ${设置结果4}
    should be equal as numbers    ${power_sm获取值4}    ${初始值}
    should be equal as numbers    ${web获取结果4}    ${初始值}
    [Teardown]
