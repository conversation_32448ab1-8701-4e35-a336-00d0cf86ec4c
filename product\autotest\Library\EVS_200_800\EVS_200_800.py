#-*-coding:utf-8 -*-
import socket	#for sockets
import sys
from struct import *
import time
import serial
import os
import pickle #用来 保存/读取 列表的内容(腌制数据)
import Func
import re

import parseFrame
import binascii

####读取文件地址，使用RIDE时，改为ADD[1]；使用本地编译时改为ADD[0]####
ADD={0:'..\.',
     1:''
    }
add=ADD[0]
#####################################################

ERR_CODE = {1:'1:非法的功能码',
            2:'2:非法的数据地址',
            3:'3:非法的数据值',
            4:'4:服务/从机故障'
            }
########################################
#设置一个时间的格式
ISOTIME = '%Y-%m-%d %X'
########################################
class EVS_200_800():
##    #声明类成员
    bNetState = False
    frame = None
    cntFramesSend = 0
    cntFramesRec  = 0
    cntBytesSend  = 0
    cntBytesRec   = 0

    listOutCsv = []
    bIsWritingFile = False
    bSendNotRec = False #解决通讯断,只发送,没有接收时的保存问题
    sock = None # telnet通讯用的变量

#=======================================================================================   
    def __init__(self, parent = None):
############################################
#全局变量
############################################
        os.chdir(os.path.split(os.path.realpath(__file__))[0])#回到当前脚本目录
        self.bSerialIsOpen = False
        self.receiveFrame = []
        self.frame = parseFrame.ParseFrame(add+r'.\table\modbusDB')
        self.ser = serial.Serial()
        self.bInitSocket = False #socket初始化完成标志
##延时============================================================
    def timesleep(self,time_delay):
        time.sleep(time_delay)
        print 'time delay:',time_delay
##打开串口============================================================================================= 
    def Connect_EVS_200_800_Com(self,destPort='1',destBaud='19200'):
        destPort=int(destPort)
        destBaud=int(destBaud)
        if self.bSerialIsOpen: #在线切换串口,关闭已经打开的串口
##            self.bSerialIsOpen = False
##            self.ser.close()
            print u'EVS_200_800串口已打开'
            return
        else:
            #初始化串口        
            try:
                self.ser = serial.Serial(port=destPort-1, baudrate=destBaud)
                self.bSerialIsOpen = True
                print u'打开EVS_200_800串口成功！'                
            except serial.SerialException:
                print u'打开EVS_200_800串口失败！'
                self.bSerialIsOpen = False

##关闭串口============================================================================================= 
    def Close_EVS_200_800_Com(self):
##        self.QueryCmd('02 06 00 02 00 00 28 39','no')#本地
        self.bSerialIsOpen = False
        self.ser.close()
        print u'关闭EVS_200_800串口！'
#读模拟量===================================================================
    def QueryAnalog(self):
        """
        读模拟量等数据;
        
        返回列表：输出电压、输出电流、输出功率、当前SOC、当前工作模式
        """
        returnStr=''
        print u'查询EVS_200_800的模拟量:'
        
        sendChars='01 03 03 E9 00 03'#查询模拟量数据
        sendChars=sendChars+Func.getCRC16(sendChars)
        recList=self.QueryCmd(sendChars,'no')
        recList=self.ResolutionProtocol(recList)     #解析16进制模拟量数据
        #查询输出功率
        sendChars='01 03 04 12 00 01'#查询输出功率
        sendChars=sendChars+Func.getCRC16(sendChars)
        recList1=self.QueryCmd(sendChars,'no')
        recList.append(recList1[0]/100.0)#加入功率查询
        #查询当前SOC
        sendChars='01 03 03 F5 00 01'#查询当前SOC
        sendChars=sendChars+Func.getCRC16(sendChars)
        recList1=self.QueryCmd(sendChars,'no')
        recList.append(recList1[0]/10.0)#加入当前SOC查询
        #查询当前工作模式
        sendChars='01 03 03 F7 00 01'#查询当前工作模式
        sendChars=sendChars+Func.getCRC16(sendChars)
        recList1=self.QueryCmd(sendChars,'no')
        if recList1[0]==11:
            recList1[0]=u'放电模式'.encode('gbk')
        elif recList1[0]==10:
            recList1[0]=u'充电模式'.encode('gbk')
        else:
            recList1[0]='Null'
        recList.append(recList1[0])#加入当前工作模式查询
        for i in recList:
            i=str(i).decode('gbk')
            returnStr=returnStr+i+','
        print returnStr
        recList=returnStr.split(',')
        return recList 

#查询电池模拟器模式参数===================================================================
    def QueryBatModPara(self):
        """
        查询电池模拟器模式的参数设置
        
        返回列表：电池类型、电池串联数、电池并联数、电池初始Soc
        """
        returnStr=''
        recList1=[]
        recList=[]
        print u'查询EVS_200_800电池模拟器模式的参数:'
        
        sendChars='01 03 08 0A 00 04'#查询电池模拟器模式的参数
        sendChars=sendChars+Func.getCRC16(sendChars)
        recList=self.QueryCmd(sendChars,'no')

        if recList[0]==0:
            recList[0]=u'锰酸锂'.encode('gbk')
        elif recList[0]==1:
            recList[0]=u'钴酸锂'.encode('gbk')
        elif recList[0]==2:
            recList[0]=u'铅酸电池'.encode('gbk')
            recList[1]=recList[1]-3
        elif recList[0]==3:
            recList[0]=u'磷酸铁锂'.encode('gbk')
        elif recList[0]==4:
            recList[0]=u'镍氢电池'.encode('gbk')
        elif recList[0]==5:
            recList[0]=u'自定义类型'.encode('gbk')
        else:
            recList[0]='Null'
        recList[3]=recList[3]/10
        for i in recList:
            i=str(i).decode('gbk')
            returnStr=returnStr+i+','
        print returnStr
        recList=returnStr[:-1].split(',')
        return recList 

#接触器分合###########################################################
    def Contactor_ON_OFF(self,state):
        """
        控制设备接触器分合，可选参数如下：
        ON：闭合接触器
        OFF：断开接触器
        """
        if state!='ON' and state!='OFF' :
            print u'输入命令错误，请检查！'
            return False
        if state=='ON':
            sendChars='01 05 07 D1 FF 00'
            sendChars=sendChars+Func.getCRC16(sendChars)
            self.QueryCmd(sendChars,'no')
            time.sleep(10)#必须等接触器吸合后才能做其他操作
            print u'电池模拟器接触器闭合'

        elif state=='OFF':
            self.OutPut_ON_OFF('OFF')
            time.sleep(2)
            sendChars='01 05 07 D1 00 00'
            sendChars=sendChars+Func.getCRC16(sendChars)
            self.QueryCmd(sendChars,'no')
            print u'电池模拟器接触器断开'


#系统开关机###########################################################
    def OutPut_ON_OFF(self,state):
        """
        控制设备开关机输出，可选参数如下：
        ON：开机运行
        OFF：关机停止
        """

        if state!='ON' and state!='OFF' :
            print u'输入命令错误，请检查！'
            return False
        if state=='ON':
            self.QueryCmd('01 05 07 D0 FF 00 8C B7','no')
            print u'电池模拟器运行'

        elif state=='OFF':
            sendChars='01 05 07 D0 00 00'
            sendChars=sendChars+Func.getCRC16(sendChars)
            self.QueryCmd(sendChars,'no')
            print u'电池模拟器停止'

#设置直流源模式###########################################################
    def SetDCMode(self,Volt='40.0',Cur='11.1',Power='55.0'):
        """
        设置为直流源模式
        volt:设置电压，范围：24.0-799.9V
        Cur:设置电流，范围：1.0-600.0A
        Power：设置功率，范围：1.0-200.0KW
        """

        Volt=round(float(Volt)+6.6,1)
        Cur=round(float(Cur),1)
        Power=round(float(Power),1)
##        if Volt>800.0:
        if Volt>70.0:#为了整流器安全，直流电压不能超过70
            print u'输出电压设置超限！',Volt
            return False
        if Cur>600.0:
            print u'输出电流设置超限！',Cur
            return False
        if Power>=200.0:
            print u'输出功率设置超限！',Power
            return False
        if Volt*Cur>Power*1000:
            print u'输出电压电流设置超限！',Volt,Cur,Volt*Cur,Power*1000
            return False
        
        print u'设置为直流源模式:',Volt,'V/',Cur,'A/',Power,'KW'
        Volt1=int(Volt*10)
        Cur1=int(Cur*10)
        Power1=int(Power*10)
        Setvolt=self.IntToHexStr(Volt1)
        SetCur=self.IntToHexStr(Cur1)
        SetPower=self.IntToHexStr(Power1)
        #设置为直流源模式-直流源通用模式
        sendChars='01 10 08 02 00 02 04 00 00 00 00'
        sendChars=sendChars+Func.getCRC16(sendChars)
        self.QueryCmd(sendChars,'no')
        time.sleep(1)
        #设置电压电流功率
        sendChars='01 10 08 04 00 03 06'
        sendChars=sendChars+' '+Setvolt+' '+SetCur+' '+SetPower
        sendChars=sendChars+Func.getCRC16(sendChars)
        self.QueryCmd(sendChars,'no')
##
        sendChars='01 03 08 02 00 05'#查询设置数据
        sendChars=sendChars+Func.getCRC16(sendChars)
        recData=self.QueryCmd(sendChars,'no')
        print recData
##        if recData[0]!=0 or recData[1]!=0:
##            print u'模式设置失败！'
##            return False
        if recData[2]/10.0!=Volt:
            print u'输出电压设置失败！',recData[2]/10.0,Volt
            return False
        if recData[3]/10.0!=Cur:
            print u'输出电流设置失败！',recData[3]/10.0,Cur
            return False
        if recData[4]/10.0!=Power:
            print u'输出功率设置失败！',recData[4]/10.0,Power
            return False
        print u'设置直流源模式成功！'
        return True
            
#设置电池模式###########################################################
    def SetBatteryMode(self,BatType=u'铅酸电池',BatSerNum='25',BatParaNum='2',BatInitSoc='100.0',dealerr='0'):
        """
        设置为电池模拟器模式模式
        BatType:电池类型:0：锰酸锂；1：钴酸锂；2：铅酸电池；3：磷酸铁锂；4：镍氢电池；5.自定义类型
        BatSerNum(电池串联数):锰酸锂最大为190；钴酸锂最大为216；铅酸电池最大为333；
                            磷酸铁锂最大为219；镍氢电池锂最大为571；        
        BatParaNum（电池并联数):最大为5；
        BatInitSoc:电池初始Soc
        """
        BatSerNum=int(BatSerNum)
        BatParaNum=int(BatParaNum)
        BatInitSoc=round(float(BatInitSoc),1)
        if BatType==u'锰酸锂':
            SetBatType=0
            if BatSerNum>190:
                print u'锰酸锂电池串联数超限！'
                return False
        if BatType==u'钴酸锂':
            SetBatType=1
            if BatSerNum>216:
                print u'钴酸锂电池串联数超限！'
                return False
        if BatType==u'铅酸电池':
            BatSerNum=BatSerNum+3
            SetBatType=2
##            if BatSerNum>333:
            if BatSerNum>31:#2018/11/29Pengy：为了安全，整流器电池电压不能超过28节
                if dealerr!='1':
                    print u'铅酸电池串联数超限！'
                    return False
##                else:#打开电池节数保护
##                    continue
                
        if BatType==u'磷酸铁锂':
            SetBatType=3
            if BatSerNum>219:
                print u'磷酸铁锂电池串联数超限！'
                return False
        if BatType==u'镍氢电池':
            SetBatType=4
            if BatSerNum>571:
                print u'镍氢电池串联数超限！'
                return False             
        if BatParaNum>5:
            print u'电池并联数超限！'
            return False
        SetBatSerNum=self.IntToHexStr(BatSerNum)
        SetBatParaNum=self.IntToHexStr(BatParaNum)
        SetBatInitSoc=self.IntToHexStr(int(BatInitSoc*10))
        SetBatType1=self.IntToHexStr(SetBatType)
        print u'设置为电池模拟器/电池类型:',BatType,SetBatType1,u'/电池串联数:',\
               BatSerNum,u'/电池并联数:',BatParaNum,u'/电池初始Soc:',BatInitSoc,'%'
##        print SetBatSerNum,SetBatParaNum,SetBatInitSoc,SetBatType
        #设置为电池模拟器
        sendChars='01 10 08 02 00 01 02 00 01'
        sendChars=sendChars+Func.getCRC16(sendChars)
        self.QueryCmd(sendChars,'no')
        time.sleep(1)
        #设置电压电流功率
        sendChars='01 10 08 0A 00 04 08'
        sendChars=sendChars+' '+SetBatType1+' '+SetBatSerNum+' '+SetBatParaNum+' '+SetBatInitSoc
        sendChars=sendChars+Func.getCRC16(sendChars)
        self.QueryCmd(sendChars,'no')
####
        sendChars='01 03 08 0A 00 04'#查询设置数据
        sendChars=sendChars+Func.getCRC16(sendChars)
        recData=self.QueryCmd(sendChars,'no')
##        print recData
##        print recData[0],SetBatType
        if int(recData[0])!=SetBatType:
            print u'电池类型设置失败！',recData[0],SetBatType
            return False
        if recData[1]!=BatSerNum:
            print u'电池串联数设置失败！',recData[1],BatSerNum
            return False
        if recData[2]!=BatParaNum:
            print u'电池并联数设置失败！',recData[2],BatParaNum
            return False
        if recData[3]/10.0!=BatInitSoc:
            print u'电池初始Soc设置失败！',recData[3]/10.0,BatInitSoc
            return False

        print u'设置电池模式成功！'
        return True            
#整型数转换为16进制字符串###########################################################
    def IntToHexStr(self,tmp):
        if int(tmp)<0:
            Settmp=str(hex(65536+int(tmp))[2:].upper())
        else:
            Settmp=str(hex(int(tmp))[2:].upper())
        if len(Settmp)==1:
            Settmp='00 0'+Settmp
        elif len(Settmp)==2:
            Settmp='00 '+Settmp
        elif len(Settmp)==3:
            Settmp='0'+Settmp[0:1]+' '+Settmp[1:]
        elif len(Settmp)==4:
            Settmp=Settmp[0:2]+' '+Settmp[2:]
        else:
            print u'设置超出设置范围！'
            return False
        return Settmp
#=====================================================================
#处理发送数据：
#=====================================================================
    def sendBySerial(self, lstSendData):
        wSendLen = len( lstSendData )
        if wSendLen == 0:
            return
        bSendData = b''
        for d in lstSendData:
            bSendData += pack( 'B', d )        
        sCommType = 'Send By Serial:'        
        try:
            self.ser.write( bSendData )                            
        except:
            print u'串口发送数据失败'                
            return
        #发送成功后续处理
        self.cntBytesSend  += wSendLen
        self.cntFramesSend += 1
        return

#============================================================================
    def SendData(self,sendcomm):        
        #处理数据
        self.frame.sendStr = sendcomm
        if self.frame.sendStr == None:            
            return
        self.frame.sendStrToSendData()      
        #根据不同接口进行发送数据
        if self.bSerialIsOpen == True:
            self.sendBySerial( self.frame.sendData )
        else:
            print u'发送数据失败,没有找到通讯通道'
        return       
#============================================================================
# 处理接收数据：
#=============================================================================
    def ResolutionProtocol(self,recList,sendcmd=''):
        ResolutionList=[]
        ResolutionList.append(recList[1]/10.0)#输出电压
        ResolutionList.append(recList[2]/10.0)#输出电流
     
        return ResolutionList

#将16进制数转换为16进制字符串=============================================================================================                
    def dealRecData(self,listByteData,isSerial,sendcmd):
        if len(listByteData) == 0:
            return
        tempStr = ''
        #显示接收到的原始数据(十六进制)  
        for d in listByteData:    
            tempStr += Func.getHex_sXX(d)
##        print 'Receive:',tempStr
        return tempStr              
            
#===================================================================================================        
    def receiveData(self,sleep,sendcmd=''):
##        self.s.settimeout(5)
        receiveList =[]
        listByteData = b''
        gaga=b''
        buffer = []       
        time.sleep(sleep)#延时1s收数据
        ##接收串口数据
        if self.bSerialIsOpen:
            i = self.ser.inWaiting()
            if i != 0: #有数据
                listByteData = self.ser.read(i)
                for d in listByteData:
                    buffer.append(ord(d))
                tempList=self.dealRecData(buffer,True,sendcmd)  #第二个参数表示是否是串口数据
##                print tempList
                listByteData = b''
                buffer=[]
            else:#没有数据
                print u'没有收到数据！'
                tempList=self.dealRecData(buffer,True)
        
        return tempList
#===================================================================================================        
    def QueryCmd(self,sendcmd='',pirntflag=''):
        flag=1
        while(flag!=0):
            try:
                self.SendData(sendcmd)
                returnStr=self.receiveData(0.5,sendcmd)
                if pirntflag=='':
                    print '收到数据：',returnStr
                flag=0
            except :
                flag=flag+1  #出错了重发
                time.sleep(1)
            if flag>4:
                print u'通讯异常，请检查设备连接',sendcmd
                return False
        #将收到的16进制数据转换为10进制整型列表数据
        recStr=returnStr[10:-6].replace(' ','')#去掉帧头帧尾和空格
        recList=re.findall(r'.{4}',recStr)#使用正则表达式将字符串按长度为4分开为列表
        for i in range(len(recList)):
            recList[i]=int(recList[i],16)
            if recList[i]>32767:
                recList[i]=recList[i]-65536
        return recList

#==============================================================================    
if __name__ == "__main__":
    myapp = EVS_200_800()
    myapp.Connect_EVS_200_800_Com('2')
##    myapp.QueryAnalog()
##    myapp.SetBatteryMode()
##    myapp.SetBatteryMode()
##    myapp.Contactor_ON_OFF('ON')
##    myapp.OutPut_ON_OFF('ON')
##    time.sleep(8)
    print myapp.QueryAnalog()
##    myapp.OutPut_ON_OFF('OFF')
##    time.sleep(2)
    print myapp.QueryBatModPara()
    
##    myapp.Contactor_ON_OFF('OFF')
##    myapp.SetLineVoltage('360','52.5')
##    myapp.OutPut_ON_OFF('ON')
##    time.sleep(5)
##    myapp.QueryThreePhaseVoltage()
