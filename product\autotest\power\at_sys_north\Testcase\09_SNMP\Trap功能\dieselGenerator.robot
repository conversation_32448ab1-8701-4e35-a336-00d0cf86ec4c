*** Settings ***
Documentation     最开始
...               设置子工具值 oileng all 告警 备用8 2048
...               经常查到油机2的告警
...
...               现在需改成
...
...               设置子工具值 oileng 1 告警 备用8 2048
...
...               每个油机单独设置
Suite Setup       油机通讯测试前置条件
Suite Teardown    油机通讯测试结束条件
Force Tags
Resource          ../../../测试用例关键字.robot

*** Test Cases ***
snmp_0022_油机启动告警_无
    [Documentation]    lt--模拟工具无
    ...    暂时未自动化
    [Tags]    3
    连接CSU
    设置子工具值    oileng    all    告警    发电频率    75
    sleep    20s
    ${获取值1}    获取web实时数据    油机输出频率
    should be true    ${获取值1}==75
    设置子工具值    oileng    all    告警    发电频率    40
    sleep    20s
    ${获取值1}    获取web实时数据    油机输出频率
    should be true    ${获取值1}==40

snmp_0024_油机异常告警
    [Documentation]    lt--模拟工具无
    ...    暂时未自动化
    [Tags]    T1-1
    [Setup]    Run Keywords    设置DODI短接配置    7    ${油机干接点状态}
    ...        AND    测试用例前置条件
    油机管理初始化
    #获取告警级别
    ${级别设置值}    获取web参数量    油机异常告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机异常告警    严重
    Wait Until Keyword Succeeds    5m    2    查询指定告警信息不为    油机异常告警
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    should be true    ${清零Trap缓存}
    ###产生告警，并判断
    Wait Until Keyword Succeeds    5m    1    设置web控制量    油机开启
    sleep    20
    ${干接点实际是否动作状态1}    获取干接点状态    ${油机干接点状态}
    should be true    ${干接点实际是否动作状态1}==0    #干接点动作为1（断开），恢复为0（闭合）
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    5m    1    判断告警存在    油机异常告警
    # sleep    20
    #产生
    ${snmp英文名}    获取snmp单个告警英文名    油机异常告警_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    Wait Until Keyword Succeeds    5m    1    设置web控制量    油机开启
    sleep    20
    ${干接点实际是否动作状态1}    获取干接点状态    ${油机干接点状态}
    should be true    ${干接点实际是否动作状态1}==0    #干接点动作为1（断开），恢复为0（闭合）
    同时设置三相电压频率    220    50
    打开交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    过渡
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    运行
    Wait Until Keyword Succeeds    5m    1    判断告警不存在    油机异常告警
    Wait Until Keyword Succeeds    5m    1    设置web控制量    油机关闭
    # Wait Until Keyword Succeeds    5m    1    设置web控制量    油机关闭
    Wait Until Keyword Succeeds    4m    1    信号量数据值为    油机状态    停止
    Comment    sleep    20
    ${干接点实际是否动作状态1}    获取干接点状态    ${油机干接点状态}
    should be true    ${干接点实际是否动作状态1}==1    #干接点动作为1（断开），恢复为0（闭合）
    关闭交流源输出
    sleep    30
    ${获取值1}    获取web实时数据    系统交流电压_1
    ${获取值2}    获取web实时数据    系统交流电压_2
    ${获取值3}    获取web实时数据    系统交流电压_3
    #消失
    #无告警后查询判断
    ${告警消失}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警消失}
    [Teardown]    Run Keywords    重置DODI短接配置
    ...    AND    sleep    120
    ...        AND    测试用例后置条件

snmp_0026_油机公共告警_无
    [Documentation]    lt--模拟工具无
    ...    暂时未自动化
    [Tags]    3

snmp_0028_油机门磁告警_无
    [Documentation]    lt--模拟工具无
    ...    暂时未自动化
    [Tags]    3

snmp_0030_油机手动模式告警_无
    [Documentation]    lt--模拟工具无
    ...    暂时未自动化
    [Tags]    3
    连接CSU
    设置子工具值    oileng    all    告警    发电频率    75
    sleep    20s
    ${获取值1}    获取web实时数据    油机输出频率
    should be true    ${获取值1}==75
    设置子工具值    oileng    all    告警    发电频率    40
    sleep    20s
    ${获取值1}    获取web实时数据    油机输出频率
    should be true    ${获取值1}==40

snmp_0032_油机燃油泄漏告警_无
    [Documentation]    lt--模拟工具无
    ...    暂时未自动化
    [Tags]    3

snmp_0034_油机输出欠压7
    连接CSU
    #发电低电压 1
    ${级别设置值}    获取web参数量    油机输出欠压
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机输出欠压    主要
    #判断无告警
    ${告警不存在}    判断告警存在_带返回值    油机输出欠压
    should not be true    ${告警不存在}    #告警不存在返回FALSE    #CPU占用率无法设置10    #关键字设置成功不提示失败
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    设置子工具值    oileng    1    告警    备用8    256
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息    油机输出欠压
    #snmp产生
    ${snmp英文名}    获取snmp单个告警英文名    油机输出欠压_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    设置子工具值    oileng    1    告警    备用8    0
    Comment    设置子工具值    oileng    all    告警    备用8    0
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机输出欠压
    sleep    30
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    #####
    #发电低电压 2
    ${级别设置值}    获取web参数量    油机输出欠压
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机输出欠压    主要
    #判断无告警
    ${告警不存在}    判断告警存在_带返回值    油机输出欠压
    should not be true    ${告警不存在}    #告警不存在返回FALSE    #CPU占用率无法设置10    #关键字设置成功不提示失败
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    设置子工具值    oileng    1    告警    备用8    512
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息    油机输出欠压
    #snmp产生
    ${snmp英文名}    获取snmp单个告警英文名    油机输出欠压_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    设置子工具值    oileng    1    告警    备用8    0
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机输出欠压
    sleep    30
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    Run keywords    设置子工具值    oileng    all    告警    备用8    0
    ...    AND    设置web参数量    油机输出欠压    主要

snmp_0036_油机输出过压8
    连接CSU
    #发电高电压 1
    ${级别设置值}    获取web参数量    油机输出过压
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机输出过压    主要
    #判断无告警
    ${告警不存在}    判断告警存在_带返回值    油机输出过压
    should not be true    ${告警不存在}    #告警不存在返回FALSE    #CPU占用率无法设置10    #关键字设置成功不提示失败
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    设置子工具值    oileng    1    告警    备用8    1024
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息    油机输出过压
    #snmp产生
    ${snmp英文名}    获取snmp单个告警英文名    油机输出过压_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    设置子工具值    oileng    1    告警    备用8    0
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机输出过压
    sleep    30
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    ###
    #发电高电压 2
    ${级别设置值}    获取web参数量    油机输出过压
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机输出过压    主要
    #判断无告警
    ${告警不存在}    判断告警存在_带返回值    油机输出过压
    should not be true    ${告警不存在}    #告警不存在返回FALSE    #CPU占用率无法设置10    #关键字设置成功不提示失败
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    设置子工具值    oileng    1    告警    备用8    2048
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息    油机输出过压
    #snmp产生
    ${snmp英文名}    获取snmp单个告警英文名    油机输出过压_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    设置子工具值    oileng    1    告警    备用8    0
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机输出过压
    sleep    30
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    Run keywords    设置子工具值    oileng    all    告警    备用8    0
    ...    AND    设置web参数量    油机输出过压    主要

snmp_0038_油机输出过流9
    连接CSU
    #发电过电流 1
    ${级别设置值}    获取web参数量    油机输出过流
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机输出过流    主要
    #判断无告警
    ${告警不存在}    判断告警存在_带返回值    油机输出过流
    should not be true    ${告警不存在}    #告警不存在返回FALSE    #CPU占用率无法设置10    #关键字设置成功不提示失败
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    设置子工具值    oileng    1    告警    备用7    1
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息    油机输出过流
    #snmp产生
    ${snmp英文名}    获取snmp单个告警英文名    油机输出过流_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    设置子工具值    oileng    1    告警    备用7    0
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机输出过流
    sleep    30
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    ###
    #发电过电流 2
    ${级别设置值}    获取web参数量    油机输出过流
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机输出过流    主要
    #判断无告警
    ${告警不存在}    判断告警存在_带返回值    油机输出过流
    should not be true    ${告警不存在}    #告警不存在返回FALSE    #CPU占用率无法设置10    #关键字设置成功不提示失败
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    设置子工具值    oileng    1    告警    备用7    2
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息    油机输出过流
    #snmp产生
    ${snmp英文名}    获取snmp单个告警英文名    油机输出过流_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    设置子工具值    oileng    all    告警    备用7    0
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机输出过流
    sleep    30
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    Run keywords    设置子工具值    oileng    all    告警    备用7    0
    ...    AND    设置web参数量    油机输出过流    主要    # Run keywords | 设置子工具值 | oileng | all | 告警 | 备用7 | 0 | AND | 设置web参数量 | 油机输出过流 | 主要

snmp_0040_油机输出过载10
    连接CSU
    #发电低电压 1
    ${级别设置值}    获取web参数量    油机输出过载
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机输出过载    主要
    #判断无告警
    ${告警不存在}    判断告警存在_带返回值    油机输出过载
    should not be true    ${告警不存在}    #告警不存在返回FALSE    #CPU占用率无法设置10    #关键字设置成功不提示失败
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    设置子工具值    oileng    1    告警    备用7    4
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息    油机输出过载
    #snmp产生
    ${snmp英文名}    获取snmp单个告警英文名    油机输出过载_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    设置子工具值    oileng    1    告警    备用7    0
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机输出过载
    sleep    30
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    #####
    #发电低电压 2
    ${级别设置值}    获取web参数量    油机输出过载
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机输出过载    主要
    #判断无告警
    ${告警不存在}    判断告警存在_带返回值    油机输出过载
    should not be true    ${告警不存在}    #告警不存在返回FALSE    #CPU占用率无法设置10    #关键字设置成功不提示失败
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    设置子工具值    oileng    1    告警    备用7    8
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息    油机输出过载
    #snmp产生
    ${snmp英文名}    获取snmp单个告警英文名    油机输出过载_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    设置子工具值    oileng    1    告警    备用7    0
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机输出过载
    sleep    30
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    设置子工具值    oileng    all    告警    备用7    0

snmp_0042_油机输出频率低11
    连接CSU
    #发电低电压 1
    ${级别设置值}    获取web参数量    油机输出频率低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机输出频率低    主要
    #判断无告警
    ${告警不存在}    判断告警存在_带返回值    油机输出频率低
    should not be true    ${告警不存在}    #告警不存在返回FALSE    #CPU占用率无法设置10    #关键字设置成功不提示失败
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    设置子工具值    oileng    1    告警    备用8    4096
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息    油机输出频率低
    #snmp产生
    ${snmp英文名}    获取snmp单个告警英文名    油机输出频率低_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    设置子工具值    oileng    1    告警    备用8    0
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机输出频率低
    sleep    30
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    #####
    #发电低电压 2
    ${级别设置值}    获取web参数量    油机输出频率低
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机输出频率低    主要
    #判断无告警
    ${告警不存在}    判断告警存在_带返回值    油机输出频率低
    should not be true    ${告警不存在}    #告警不存在返回FALSE    #CPU占用率无法设置10    #关键字设置成功不提示失败
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    设置子工具值    oileng    1    告警    备用8    8192
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息    油机输出频率低
    #snmp产生
    ${snmp英文名}    获取snmp单个告警英文名    油机输出频率低_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    设置子工具值    oileng    1    告警    备用8    0
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机输出频率低
    sleep    30
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    设置子工具值    oileng    all    告警    备用8    0

snmp_0044_油机输出频率高12
    连接CSU
    #发电低电压 1
    ${级别设置值}    获取web参数量    油机输出频率高
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机输出频率高    主要
    #判断无告警
    ${告警不存在}    判断告警存在_带返回值    油机输出频率高
    should not be true    ${告警不存在}    #告警不存在返回FALSE    #CPU占用率无法设置10    #关键字设置成功不提示失败
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    设置子工具值    oileng    1    告警    备用8    16384
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息    油机输出频率高
    #snmp产生
    ${snmp英文名}    获取snmp单个告警英文名    油机输出频率高_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    设置子工具值    oileng    1    告警    备用8    0
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机输出频率高
    sleep    30
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    #####
    #发电低电压 2
    ${级别设置值}    获取web参数量    油机输出频率高
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机输出频率高    主要
    #判断无告警
    ${告警不存在}    判断告警存在_带返回值    油机输出频率高
    should not be true    ${告警不存在}    #告警不存在返回FALSE    #CPU占用率无法设置10    #关键字设置成功不提示失败
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    设置子工具值    oileng    1    告警    备用8    32768
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息    油机输出频率高
    #snmp产生
    ${snmp英文名}    获取snmp单个告警英文名    油机输出频率高_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    设置子工具值    oileng    1    告警    备用8    0
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机输出频率高
    sleep    30
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    设置子工具值    oileng    all    告警    备用8    0

snmp_0046_油机低速告警13
    连接CSU
    #发电低电压 1
    ${级别设置值}    获取web参数量    油机低速告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机低速告警    主要
    #判断无告警
    ${告警不存在}    判断告警存在_带返回值    油机低速告警
    should not be true    ${告警不存在}    #告警不存在返回FALSE    #CPU占用率无法设置10    #关键字设置成功不提示失败
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    设置子工具值    oileng    1    告警    备用9    16384
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息    油机低速告警
    #snmp产生
    ${snmp英文名}    获取snmp单个告警英文名    油机低速告警_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    设置子工具值    oileng    1    告警    备用9    0
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机低速告警
    sleep    30
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    #####
    #发电低电压 2
    ${级别设置值}    获取web参数量    油机低速告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机低速告警    主要
    #判断无告警
    ${告警不存在}    判断告警存在_带返回值    油机低速告警
    should not be true    ${告警不存在}    #告警不存在返回FALSE    #CPU占用率无法设置10    #关键字设置成功不提示失败
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    设置子工具值    oileng    1    告警    备用9    32768
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息    油机低速告警
    #snmp产生
    ${snmp英文名}    获取snmp单个告警英文名    油机低速告警_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    设置子工具值    oileng    1    告警    备用9    0
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机低速告警
    sleep    30
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    设置子工具值    oileng    all    告警    备用9    0

snmp_0048_油机超速告警14
    连接CSU
    #发电低电压 1
    ${级别设置值}    获取web参数量    油机超速告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机超速告警    主要
    #判断无告警
    ${告警不存在}    判断告警存在_带返回值    油机超速告警
    should not be true    ${告警不存在}    #告警不存在返回FALSE    #CPU占用率无法设置10    #关键字设置成功不提示失败
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    设置子工具值    oileng    1    告警    备用8    1
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息    油机超速告警
    #snmp产生
    ${snmp英文名}    获取snmp单个告警英文名    油机超速告警_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    设置子工具值    oileng    1    告警    备用8    0
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机超速告警
    sleep    30
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    #####
    #发电低电压 2
    ${级别设置值}    获取web参数量    油机超速告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机超速告警    主要
    #判断无告警
    ${告警不存在}    判断告警存在_带返回值    油机超速告警
    should not be true    ${告警不存在}    #告警不存在返回FALSE    #CPU占用率无法设置10    #关键字设置成功不提示失败
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    设置子工具值    oileng    1    告警    备用8    2
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息    油机超速告警
    #snmp产生
    ${snmp英文名}    获取snmp单个告警英文名    油机超速告警_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    设置子工具值    oileng    1    告警    备用8    0
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机超速告警
    sleep    30
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    设置子工具值    oileng    all    告警    备用8    0

snmp_0050_油机油压低告警15
    连接CSU
    #发电低电压 1
    ${级别设置值}    获取web参数量    油机油压低告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机油压低告警    主要
    #判断无告警
    ${告警不存在}    判断告警存在_带返回值    油机油压低告警
    should not be true    ${告警不存在}    #告警不存在返回FALSE    #CPU占用率无法设置10    #关键字设置成功不提示失败
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    设置子工具值    oileng    1    告警    备用8    4
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息    油机油压低告警
    #snmp产生
    ${snmp英文名}    获取snmp单个告警英文名    油机油压低告警_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    设置子工具值    oileng    1    告警    备用8    0
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机油压低告警
    sleep    30
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    #####
    #发电低电压 2
    ${级别设置值}    获取web参数量    油机油压低告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机油压低告警    主要
    #判断无告警
    ${告警不存在}    判断告警存在_带返回值    油机油压低告警
    should not be true    ${告警不存在}    #告警不存在返回FALSE    #CPU占用率无法设置10    #关键字设置成功不提示失败
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    设置子工具值    oileng    1    告警    备用8    8
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息    油机油压低告警
    #snmp产生
    ${snmp英文名}    获取snmp单个告警英文名    油机油压低告警_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    设置子工具值    oileng    1    告警    备用8    0
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机油压低告警
    sleep    30
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    设置子工具值    oileng    all    告警    备用8    0

snmp_0052_油机水温高告警_无
    [Documentation]    lt--模拟工具无
    ...    暂时未自动化
    [Tags]    3

snmp_0054_油机油温高告警_无
    [Documentation]    lt--模拟工具无
    ...    暂时未自动化
    [Tags]    3

snmp_0056_油机电池欠压告警18
    连接CSU
    #发电高电压 1
    ${级别设置值}    获取web参数量    油机电池欠压告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机电池欠压告警    主要
    #判断无告警
    ${告警不存在}    判断告警存在_带返回值    油机电池欠压告警
    should not be true    ${告警不存在}    #告警不存在返回FALSE    #CPU占用率无法设置10    #关键字设置成功不提示失败
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    设置子工具值    oileng    1    告警    备用9    8192
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息    油机电池欠压告警
    #snmp产生
    ${snmp英文名}    获取snmp单个告警英文名    油机电池欠压告警_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    设置子工具值    oileng    1    告警    备用9    0
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机电池欠压告警
    sleep    30
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    设置子工具值    oileng    all    告警    备用9    0

snmp_0058_油机电池过压告警19
    连接CSU
    #发电高电压 1
    ${级别设置值}    获取web参数量    油机电池过压告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机电池过压告警    主要
    #判断无告警
    ${告警不存在}    判断告警存在_带返回值    油机电池过压告警
    should not be true    ${告警不存在}    #告警不存在返回FALSE    #CPU占用率无法设置10    #关键字设置成功不提示失败
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    设置子工具值    oileng    1    告警    备用9    4096
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息    油机电池过压告警
    #snmp产生
    ${snmp英文名}    获取snmp单个告警英文名    油机电池过压告警_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    设置子工具值    oileng    1    告警    备用9    0
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机电池过压告警
    sleep    30
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    设置子工具值    oileng    all    告警    备用9    0

snmp_0060_油机充电失败告警20
    连接CSU
    #发电高电压 1
    ${级别设置值}    获取web参数量    油机充电失败告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机充电失败告警    主要
    #判断无告警
    ${告警不存在}    判断告警存在_带返回值    油机充电失败告警
    should not be true    ${告警不存在}    #告警不存在返回FALSE    #CPU占用率无法设置10    #关键字设置成功不提示失败
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    设置子工具值    oileng    1    告警    备用9    2048
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息    油机充电失败告警
    #snmp产生
    ${snmp英文名}    获取snmp单个告警英文名    油机充电失败告警_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    设置子工具值    oileng    1    告警    备用9    0
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机充电失败告警
    sleep    30
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    设置子工具值    oileng    all    告警    备用9    0

snmp_0062_油机发动机高温告警21
    连接CSU
    #发电低电压 1
    ${级别设置值}    获取web参数量    油机发动机高温告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机发动机高温告警    主要
    #判断无告警
    ${告警不存在}    判断告警存在_带返回值    油机发动机高温告警
    should not be true    ${告警不存在}    #告警不存在返回FALSE    #CPU占用率无法设置10    #关键字设置成功不提示失败
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    设置子工具值    oileng    1    告警    备用8    16
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息    油机发动机高温告警
    #snmp产生
    ${snmp英文名}    获取snmp单个告警英文名    油机发动机高温告警_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    设置子工具值    oileng    1    告警    备用8    0
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机发动机高温告警
    sleep    30
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    #####
    #发电低电压 2
    ${级别设置值}    获取web参数量    油机发动机高温告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机发动机高温告警    主要
    #判断无告警
    ${告警不存在}    判断告警存在_带返回值    油机发动机高温告警
    should not be true    ${告警不存在}    #告警不存在返回FALSE    #CPU占用率无法设置10    #关键字设置成功不提示失败
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    设置子工具值    oileng    1    告警    备用8    32
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息    油机发动机高温告警
    #snmp产生
    ${snmp英文名}    获取snmp单个告警英文名    油机发动机高温告警_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    设置子工具值    oileng    1    告警    备用8    0
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机发动机高温告警
    sleep    30
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    设置子工具值    oileng    all    告警    备用8    0

snmp_0064_油机发动机低温告警_无
    [Documentation]    lt--模拟工具无
    ...    暂时未自动化
    [Tags]    3

snmp_0066_油机启动失败告警23
    连接CSU
    #发电高电压 1
    ${级别设置值}    获取web参数量    油机启动失败告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机启动失败告警    主要
    #判断无告警
    ${告警不存在}    判断告警存在_带返回值    油机启动失败告警
    should not be true    ${告警不存在}    #告警不存在返回FALSE    #CPU占用率无法设置10    #关键字设置成功不提示失败
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    设置子工具值    oileng    1    告警    备用9    4
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息    油机启动失败告警
    #snmp产生
    ${snmp英文名}    获取snmp单个告警英文名    油机启动失败告警_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    设置子工具值    oileng    1    告警    备用9    0
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机启动失败告警
    sleep    30
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    设置子工具值    oileng    all    告警    备用9    0

snmp_0068_油机停机失败告警24
    连接CSU
    #发电高电压 1
    ${级别设置值}    获取web参数量    油机停机失败告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机停机失败告警    主要
    #判断无告警
    ${告警不存在}    判断告警存在_带返回值    油机停机失败告警
    should not be true    ${告警不存在}    #告警不存在返回FALSE    #CPU占用率无法设置10    #关键字设置成功不提示失败
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    设置子工具值    oileng    1    告警    备用9    8
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息    油机停机失败告警
    #snmp产生
    ${snmp英文名}    获取snmp单个告警英文名    油机停机失败告警_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    设置子工具值    oileng    1    告警    备用9    0
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机停机失败告警
    sleep    30
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    设置子工具值    oileng    all    告警    备用9    0

snmp_0070_油机油位高告警25X
    连接CSU
    #发电低电压 1
    ${级别设置值}    获取web参数量    油机油位高告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机油位高告警    主要
    #判断无告警
    ${告警不存在}    判断告警存在_带返回值    油机油位高告警
    should not be true    ${告警不存在}    #告警不存在返回FALSE    #CPU占用率无法设置10    #关键字设置成功不提示失败
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    设置子工具值    oileng    1    告警    备用3    64
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息    油机油位高告警
    #snmp产生
    ${snmp英文名}    获取snmp单个告警英文名    油机油位高告警_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    设置子工具值    oileng    1    告警    备用3    0
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机油位高告警
    sleep    30
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    #####
    #发电低电压 2
    ${级别设置值}    获取web参数量    油机油位高告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油机油位高告警    主要
    #判断无告警
    ${告警不存在}    判断告警存在_带返回值    油机油位高告警
    should not be true    ${告警不存在}    #告警不存在返回FALSE    #CPU占用率无法设置10    #关键字设置成功不提示失败
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    设置子工具值    oileng    1    告警    备用3    128
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息    油机油位高告警
    #snmp产生
    ${snmp英文名}    获取snmp单个告警英文名    油机油位高告警_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    设置子工具值    oileng    1    告警    备用3    0
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油机油位高告警
    sleep    30
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    设置子工具值    oileng    all    告警    备用3    0

snmp_0072_油压传感器开路告警26
    连接CSU
    #发电高电压 1
    ${级别设置值}    获取web参数量    油压传感器开路告警
    run keyword if    '${级别设置值}'=='屏蔽'    设置web参数量    油压传感器开路告警    主要
    #判断无告警
    ${告警不存在}    判断告警存在_带返回值    油压传感器开路告警
    should not be true    ${告警不存在}    #告警不存在返回FALSE    #CPU占用率无法设置10    #关键字设置成功不提示失败
    #清零Trap缓存
    ${清零Trap缓存}    清零Trap缓存
    #产生告警，并判断
    设置子工具值    oileng    all    告警    备用7    4096
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息    油压传感器开路告警
    #snmp产生
    ${snmp英文名}    获取snmp单个告警英文名    油压传感器开路告警_1
    ${告警产生}    判断Trap告警产生    ${snmp英文名}
    should be true    ${告警产生}
    #消失
    设置子工具值    oileng    all    告警    备用7    0
    sleep    20s
    wait until keyword succeeds    5m    5    查询指定告警信息不为    油压传感器开路告警
    sleep    30
    ${告警产生}    判断Trap告警消失    ${snmp英文名}
    should be true    ${告警产生}
    [Teardown]    设置子工具值    oileng    all    告警    备用7    0

snmp_0074_油机超长运行告警_无
    [Documentation]    lt--模拟工具无
    ...    暂时未自动化
    [Tags]    3
